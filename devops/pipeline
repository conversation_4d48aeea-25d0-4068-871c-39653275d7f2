pipeline{
//指定运行此流水线的节点
agent any

//全局变量
environment {
	git_auth = '34dc51e8-6bfd-405c-8488-99f178b782d3'
}
//tool构建工具
tools {
    nodejs "${NodeJS}"
}

stages{
 stage("飞书通知"){
		when {
                allOf {
                     expression { return rollback=='false' }
                }
            }
        steps{
            script{
			println("${NodeJS}")
			env.start_time =sh(script: " date +%Y年%m月%d日%H时%M分%S秒", returnStdout: true).trim()
                                sh '''curl -X POST -H "Content-Type: application/json" \\
			-d \'{
    "email": "<EMAIL>",
    "msg_type": "post",
    "content": {
        "post": {
            "zh_cn": {
                "title": "项目发版开始报告",
                "content": [
                    [
                        {
                            "tag": "text",
                            "un_escape": true,
                            "text": "项目名称:&nbsp;:\'${JOB_NAME}\'服务"
                        }
                    ],
					[
                        {
                            "tag": "text",
                            "un_escape": true,
                            "text": "构建结果:&nbsp;:"
                        }
                    ],
                    [
                        {
                            "tag": "text",
                            "text": "Tag分支:"
                        },
                        {
                            "tag": "text",
                            "text": "\'${Tag}\'"
                        }
                    ],
					[
                        {
                            "tag": "text",
                            "text": "任务:"
                        },
                        {
                            "tag": "text",
                            "text": "\'${BUILD_ID}\'"
                        }
                    ],
					[
                        {
                            "tag": "text",
                            "text": "构建开始时间:"
                        },
                        {
                            "tag": "text",
                            "text": "\'${start_time}\'"
                        }
                    ]
                ]
            }
        }
    }
}\' \\
			$robot_webhook
			'''
            }
        }
    }
    stage("获取代码"){
		when {
                allOf {
                     expression { return rollback=='false' }
                }
            }
        steps{
            script{
			println("${NodeJS}")
                println("获取代码")
                                  checkout([$class: 'GitSCM', branches: [[name: '${Tag}']],doGenerateSubmoduleConfigurations: false,userRemoteConfigs: [[credentialsId:"${git_auth}", url:"${git_address}"]]])

            }
        }
    }
	 stage("打包"){
		when {
                allOf {
                     expression { return rollback=='false' }
                }
            }
        steps{
            script{
                println("打包")
				def tomcat='/opt/tomcat'
                for (build in  compiler_directive_list.tokenize(',')) {
                 sh"$build"
            }
            }
        }
    }
	stage("发布"){
		when {
                allOf {
                     expression { return rollback=='false' }
                }
            }
        steps{
            script{
               for (SERVER in  publish_server_list.tokenize(',')) {
                println("发布 ${SERVER}")
				sh"ssh root@$SERVER rm -rf  ${project_deploy_workdir}dist_fabu_back.tar"
				bo=sh(script: "ssh root@$SERVER 'if [ -f ${project_deploy_workdir}dist_temp.tar ]; then echo ok;fi'", returnStdout: true).trim()
				if(bo!="ok"){
				sh"ssh root@$SERVER touch ${project_deploy_workdir}dist_temp.tar"
				}
				sh"ssh root@$SERVER mv  ${project_deploy_workdir}dist_temp.tar ${project_deploy_workdir}dist_fabu_back.tar"
				sh"ssh root@$SERVER rm -rf  ${project_deploy_workdir}dist_temp.tar"
				sh"ssh root@$SERVER rm -rf  ${project_deploy_workdir}dist"
				sh"tar -zcf dist_temp.tar dist/"
				sh"scp -r dist_temp.tar root@$SERVER:${project_deploy_workdir}"
				sh"ssh root@$SERVER tar -zxf ${project_deploy_workdir}dist_temp.tar -C ${project_deploy_workdir}"
				local_md5=sh(script: "ssh root@$SERVER md5sum  ${project_deploy_workdir}dist_temp.tar|awk \'{print \$1}\'", returnStdout: true).trim()
				remote_md5=sh(script: "md5sum  dist_temp.tar|awk \'{print \$1}\'", returnStdout: true).trim()
				sh"rm -rf dist_temp.tar"
                println("${local_md5} ===${remote_md5}")
				if(local_md5 == remote_md5){
				println("${SERVER} 发布 成功")
				}else{
				error "${SERVER} 发布 文件传输工程文件损坏 失败！"
				}
              }
            }
        }
    }
	stage("回滚"){
			when {
                allOf {
                     expression { return rollback=='true' }
                }
            }
		 steps{
			script{
				println("回滚到上个版本")
				for (SERVER in  publish_server_list.tokenize(',')) {
				sh"ssh root@$SERVER rm -rf  ${project_deploy_workdir}dist"
				sh"ssh root@$SERVER tar -zxf  ${project_deploy_workdir}dist_fabu_back.tar -C ${project_deploy_workdir}"
				}}}
				}
}
post {


    success{
        script{
            println("流水线成功了")
			env.end_time = sh(script: " date +%Y年%m月%d日%H时%M分%S秒", returnStdout: true).trim()
			 sh '''curl -X POST -H "Content-Type: application/json" \\
			-d \'{
    "email": "<EMAIL>",
    "msg_type": "post",
    "content": {
        "post": {
            "zh_cn": {
                "title": "项目发版完成报告",
                "content": [
                    [
                        {
                            "tag": "text",
                            "un_escape": true,
                            "text": "项目名称:&nbsp;:\'${JOB_NAME}\'服务"
                        }
                    ],
					[
                        {
                            "tag": "text",
                            "un_escape": true,
                            "text": "构建结果:&nbsp;:构建成功 ✅"
                        }
                    ],
                    [
                        {
                            "tag": "text",
                            "text": "Tag分支:"
                        },
                        {
                            "tag": "text",
                            "text": "\'${Tag}\'"
                        }
                    ],
					[
                        {
                            "tag": "text",
                            "text": "任务:"
                        },
                        {
                            "tag": "text",
                            "text": "\'${BUILD_ID}\'"
                        }
                    ],
					[
                        {
                            "tag": "text",
                            "text": "构建结束时间:"
                        },
                        {
                            "tag": "text",
                            "text": "\'${end_time}\'"
                        }
                    ]
                ]
            }
        }
    }
}\' \\
			$robot_webhook
			'''
        }

    }
    failure{
        script{
			env.end_time = sh(script: " date +%Y年%m月%d日%H时%M分%S秒", returnStdout: true).trim()
            println("流水线失败了")
			 sh '''curl -X POST -H "Content-Type: application/json" \\
			-d \'{
    "email": "<EMAIL>",
    "msg_type": "post",
    "content": {
        "post": {
            "zh_cn": {
                "title": "项目发版失败报告",
                "content": [
                    [
                        {
                            "tag": "text",
                            "un_escape": true,
                            "text": "项目名称:&nbsp;:\'${JOB_NAME}\'服务"
                        }
                    ],
					[
                        {
                            "tag": "text",
                            "un_escape": true,
                            "text": "构建结果:&nbsp;:构建失败 ❌"
                        }
                    ],
                    [
                        {
                            "tag": "text",
                            "text": "Tag分支:"
                        },
                        {
                            "tag": "text",
                            "text": "\'${Tag}\'"
                        }
                    ],
					[
                        {
                            "tag": "text",
                            "text": "任务:"
                        },
                        {
                            "tag": "text",
                            "text": "\'${BUILD_ID}\'"
                        }
                    ],
					[
                        {
                            "tag": "text",
                            "text": "构建失败时间:"
                        },
                        {
                            "tag": "text",
                            "text": "\'${end_time}\'"
                        }
                    ]
                ]
            }
        }
    }
}\' \\
			$robot_webhook
			'''
        }
    }
	aborted{
        script{
            println("流水线取消")
						env.end_time = sh(script: " date +%Y年%m月%d日%H时%M分%S秒", returnStdout: true).trim()
			sh '''curl -X POST -H "Content-Type: application/json" \\
			-d \'{
    "email": "<EMAIL>",
    "msg_type": "post",
    "content": {
        "post": {
            "zh_cn": {
                "title": "项目发版失败报告",
                "content": [
                    [
                        {
                            "tag": "text",
                            "un_escape": true,
                            "text": "项目名称:&nbsp;:\'${JOB_NAME}\'服务"
                        }
                    ],
					[
                        {
                            "tag": "text",
                            "un_escape": true,
                            "text": "构建结果:&nbsp;:构建取消 ❌"
                        }
                    ],
                    [
                        {
                            "tag": "text",
                            "text": "Tag分支:"
                        },
                        {
                            "tag": "text",
                            "text": "\'${Tag}\'"
                        }
                    ],
					[
                        {
                            "tag": "text",
                            "text": "任务:"
                        },
                        {
                            "tag": "text",
                            "text": "\'${BUILD_ID}\'"
                        }
                    ],
					[
                        {
                            "tag": "text",
                            "text": "构建取消时间:"
                        },
                        {
                            "tag": "text",
                            "text": "\'${end_time}\'"
                        }
                    ]
                ]
            }
        }
    }
}\' \\
			$robot_webhook
			'''
        }
         
    }
}
}
