// import { login, logout, ssoLogin, loginWithCode } from '@/api/login';
import { setUserInfo, removeUserInfo } from '@/common/localStorage/user';
import ShopInfo from '@/helper/ShopInfo';
// import router from '@/router';
const user = {
  state: {
    userInfo: null,
    staffInfo: null, // 员工信息
    userGroup: '' // 1 直供，2 电商，3 国际，4 平台， 5 技术， 6 客服， 7 财务， 8 其他 ，9 品牌方
  },

  mutations: {
    SET_USER: (state, user) => {
      state.userInfo = user;
    },
    SET_USER_GROUP: (state, groupId) => {
      state.userGroup = groupId;
    },
    SET_STAFF_INFO: (state, staffInfo) => {
      state.staffInfo = staffInfo;
    }
  },

  actions: {
    // 单点登录完成
    uniLogin({ commit }, userVO) {
      const shopStaff = {};
      commit('SET_USER', userVO);
      commit('shop/SAVE_SHOP_INFO', ShopInfo.withSoyoungShop(userVO, shopStaff));
      setUserInfo(userVO);
      window.$plugins.setLStorage('SOYOUNG_ZG_CSRF_TOKEN', userVO.csrfToken);
      window.$plugins.setLStorage('SOYOUNG_ZG_SESSION_ID', userVO.sessionId);
    },
    // 前端登出 适用于登录状态失效
    FedLogOut({ commit }) {
      return new Promise((rs) => {
        removeUserInfo();
        commit('SET_USER', null);
        rs();
      });
    },
    setGroupId({ commit }, groupId) {
      commit('SET_USER_GROUP', groupId);
    },
    // 设置员工信息
    setStaffInfoId({ commit }, staffInfo) {
      commit('SET_STAFF_INFO', staffInfo);
    }
  }
};

export default user;
