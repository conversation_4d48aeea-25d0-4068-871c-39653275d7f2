import {
  setShopInfo as setShopInfoLocal,
  removeShopInfo as removeShopInfoLocal
} from '@/common/localStorage';
import {
  setShopInfo as setShopInfoSession,
  removeShopInfo as removeShopInfoSession
} from '@/common/sessionStorage';
import isEmpty from 'lodash/isEmpty';
import get from 'lodash/get';
const shop = {
  namespaced: true,

  state: {
    shopInfo: {}
  },

  getters: {
    shopInfo: state => state.shopInfo,
    staffId: state => get(state.shopInfo, 'shopStaff.id'),
    menus: state =>
      get(state, 'shopInfo.userVO.menus', []).filter(
        ({ type }) => type === 'router'
      ),
    btns: state =>
      get(state, 'shopInfo.userVO.menus', []).filter(
        ({ type }) => type === 'button'
      ),
    btnSet: (_, getters) => new Set(getters.btns.map(({ code }) => code))
  },

  mutations: {
    SAVE_SHOP_INFO: (state, shop = {}) => {
      state.shopInfo = shop;
      if (isEmpty(shop)) {
        removeShopInfoLocal();
        removeShopInfoSession();
        return;
      }
      setShopInfoLocal(shop);
      setShopInfoSession(shop);
    }
  }
};

export default shop;
