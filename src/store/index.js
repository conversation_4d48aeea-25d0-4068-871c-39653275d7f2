import Vue from 'vue';
import Vuex from 'vuex';
import app from './modules/app';
import user from './modules/user';
import shop from './modules/shop';
import getters from './getters';
import design from './modules/design';
import target from './modules/target'
Vue.use(Vuex);

const store = new Vuex.Store({
  modules: {
    app,
    user,
    shop,
    design,
    target
  },
  getters
});

export default store;
