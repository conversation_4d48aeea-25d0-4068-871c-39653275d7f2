import { withExtTenantIdRequest } from '@/utils/request';

// 发送给客户
export function sendToMember(data) {
  return withExtTenantIdRequest({
    method: 'post',
    url: '/chat-service/api/chatRecord/sendToMember',
    data
  });
}

// 关闭会话
export function closeSession(sessionId) {
  return withExtTenantIdRequest({
    method: 'post',
    url: '/chat-service/api/chatSession/closeSession',
    params: { id: sessionId }
  });
}

/**
 * BUSY: 忙碌
 * OFFLINE: 下线
 * ONLINE: 上线
 */
export function changeStatus(status) {
  const apiMap = {
    BUSY: '/chat-service/api/customerService/change2Busy',
    OFFLINE: '/chat-service/api/customerService/change2Offline',
    ONLINE: '/chat-service/api/customerService/change2Online'
  };
  const url = apiMap[status];
  if (!url) {
    return Promise.reject('no matching status');
  }
  return withExtTenantIdRequest({
    method: 'post',
    url
  });
}

// 获取指定客服当日接待会话总数和在线时长
export function fetchServiceInfo() {
  return withExtTenantIdRequest({
    method: 'get',
    url: '/chat-service/api/chatSession/getTotalSessionNumAndOnlineTime'
  });
}

// 获取接待、排队数量
export function fetchQueue() {
  return withExtTenantIdRequest({
    method: 'get',
    url: '/chat-service/api/chatSession/getReceptionAndQueueNum'
  });
}

// 获取客服对象
export function fetchCustom(id) {
  return withExtTenantIdRequest({
    url: '/chat-service/api/customerService/get',
    method: 'get',
    params: { id }
  });
}

// 获取客服会话列表
export function fetchCsChatList() {
  return withExtTenantIdRequest({
    method: 'get',
    url: '/chat-service/api/chatSession/csMyList'
  });
}

// 批量获取会话列表信息
export function fetchCsChatListInfo(sessionIds) {
  return withExtTenantIdRequest({
    method: 'post',
    url: '/chat-service/api/chatRecord/csSessionListChatInfo',
    data: sessionIds
  });
}

// 查询用户基本信息
export function getUserInfo(buyerId) {
  return withExtTenantIdRequest({
    method: 'post',
    url: '/chat-service/api/extWindow/getUserInfo',
    data: buyerId
  });
}

// 获取客服分组分页列表
export function fetchGroupListPage(data) {
  return withExtTenantIdRequest({
    method: 'post',
    url: '/chat-service/api/customerServiceGroup/listPage',
    data
  });
}

// 获取分组对象
export function fetchChatGroup(id) {
  return withExtTenantIdRequest({
    method: 'get',
    url: '/chat-service/api/customerServiceGroup/get',
    params: { id }
  });
}

// 获取所有客服列表
export function fetchCSListAll(data = {}) {
  return withExtTenantIdRequest({
    method: 'post',
    url: '/chat-service/api/customerService/listAll',
    data
  });
}

// 创建客服分组
export function fetchCreateGroup(data) {
  return withExtTenantIdRequest({
    method: 'post',
    url: '/chat-service/api/customerServiceGroup/createGroup',
    data
  });
}

// 查询订单数据
export function fetchOrder(data) {
  return withExtTenantIdRequest({
    baseURL: process.env.VUE_APP_BASE_URL,
    url: `/chat-service/api/extWindow/listOrderByPage`,
    method: 'post',
    data
  });
}

// 查询退款订单数据
export function fetchRefundOrder(data) {
  return withExtTenantIdRequest({
    baseURL: process.env.VUE_APP_BASE_URL,
    url: `/chat-service/api/extWindow/listRefundOrderByPage`,
    method: 'post',
    data
  });
}
// 更新群分组
export function fetchUpdateGroup(data) {
  return withExtTenantIdRequest({
    method: 'post',
    url: '/chat-service/api/customerServiceGroup/updateGroup',
    data
  });
}

// 删除群分组
export function fetchDeleteGroup(id) {
  return withExtTenantIdRequest({
    method: 'post',
    url: '/chat-service/api/customerServiceGroup/delete',
    params: { id }
  });
}
// 修改订单中的客服备注
export function addServiceNotes(data) {
  return withExtTenantIdRequest({
    url: '/trade-main/api/order/addServiceNotes',
    method: 'post',
    data
  });
}

// 历史消息 滑动分页
export function listPageHistory(data) {
  return withExtTenantIdRequest({
    url: '/chat-service/api/chatRecord/historyListPage',
    method: 'post',
    data
  });
}
// 客服按分组展示接口
export function lisCustomerServiceGroup() {
  return withExtTenantIdRequest({
    url: '/chat-service/api/customerServiceGroup/listCustomerServiceGroupVO',
    method: 'post'
  });
}

// 重发消息给我用户（微信返回失败情况）
export function retrySendToMember(recoreId) {
  console.log('recoreId:', recoreId);
  return withExtTenantIdRequest({
    method: 'post',
    url: '/chat-service/api/chatRecord/retrySendToMember',
    data: {
      recoreId
    }
  });
}

// 修改客服最大接待人数
export function updateCsQuota(data) {
  return withExtTenantIdRequest({
    url: '/chat-service/api/customerService/updateCsQuota',
    method: 'post',
    data
  });
}

// 根据手机号获取memberId
export function getByMobile(mobile) {
  return withExtTenantIdRequest({
    url: '/chat-service/api/proactiveSession/getByMobile',
    method: 'get',
    params: { mobile }
  });
}

// 重新发起会话
export function confirmLaunchSession(memberId) {
  return withExtTenantIdRequest({
    url: '/chat-service/api/proactiveSession/confirmLaunchSession',
    method: 'get',
    params: { memberId }
  });
}

/**
 *
 * @param {用户ID} memberId
 * @param {是否自动提示错误} processError
 */
export function launchSessionCheck(memberId, processError = true) {
  return withExtTenantIdRequest({
    url: '/chat-service/api/proactiveSession/launchSessionCheck',
    method: 'get',
    params: { memberId },
    processError
  });
}

// 已结束会话列表
export function csMyEndList() {
  return withExtTenantIdRequest({
    url: '/chat-service/api/chatSession/csMyEndList',
    method: 'get'
  });
}

// 上传图片
export function uploadChatImage(file) {
  const formData = new FormData();
  formData.append('file', file);
  return withExtTenantIdRequest({
    url: '/file-service/api/image/upload',
    method: 'post',
    data: formData,
    params: 'image/newretail/chat'
  });
}

// 收藏/取消收藏
export function markSession(data) {
  return withExtTenantIdRequest({
    url: '/chat-service/api/chatSession/markOrCancelMark',
    method: 'post',
    data
  });
}

// 收藏会话列表
export function markedSessionList() {
  return withExtTenantIdRequest({
    url: '/chat-service/api/chatSession/csIsMarkMyList',
    method: 'get'
  });
}

// 批量关闭会话
export function closeSessionList(data) {
  return withExtTenantIdRequest({
    url: '/chat-service/api/chatSession/closeSessionList',
    method: 'post',
    data
  });
}

// 发送奖品给用户
export function sendPrize(data) {
  return withExtTenantIdRequest({
    method: 'post',
    url: '/chat-service/api/chatRecord/sendPrizeToMember',
    data
  });
}
