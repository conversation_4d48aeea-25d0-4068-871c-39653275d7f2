import {
  withExtTenantIdRequest
} from '@/utils/request';

// 获取分销商会员等级分页列表
export function list (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorLevel/listPage',
    method: 'post',
    data
  });
}
// 获取分销商会员等级分页列表
export function listAllLevel () {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorLevelConfig/listAll',
    method: 'get'
  });
}
// 导出分销商会员等级
export function exportExcel (data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorLevel/exportExcel`,
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}
// 修改分销商会员等级
export function updateLevel (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorLevel/updateLevel',
    method: 'post',
    data
  });
}
// 分销商会员等级列表统计
export function listStatistics (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorLevel/listStatistics',
    method: 'post',
    data
  });
}
// 获取商品采购明细列表
export function brandCommodityStatistics (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorPurchaseStatistics/brandCommodityStatistics',
    method: 'post',
    data
  });
}
// 导出商品采购明细列表
export function exportExcelBrandCommodityStatistics (data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorPurchaseStatistics/exportExcelBrandCommodityStatistics`,
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}
// 获取品牌采购明细列表
export function brandStatistics (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorPurchaseStatistics/brandStatistics',
    method: 'post',
    data
  });
}
// 导出品牌采购明细列表
export function exportExcelBrandStatistics (data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorPurchaseStatistics/exportExcelBrandStatistics`,
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}
// 获取分销商会员等级变更记录列表
export function listLevelLogs (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorLevelLog/listPage',
    method: 'post',
    data
  });
}
