import axios from '@/api/axios';
import axios2 from 'axios';
import { withExtTenantIdRequest } from '@/utils/request';
function getCookie(name) {
  // 读取COOKIE
  const reg = new RegExp('(^| )' + name + '(?:=([^;]*))?(;|$)');
  const val = document.cookie.match(reg);
  return val ? (val[2] ? unescape(val[2]) : '') : null;
}
export default {
  // wxLiveBrandSubmit(data) {
  //   return axios.post('/commodity/api/wxLiveBrand/submit', data);
  // },
  getTopJson() {
    const prefix = process.env.VUE_APP_ENV_CONFIG === 'prod' ? '' : 'test';
    const url = `https://${prefix}hermes.syounggroup.com/top.json`;
    return axios2.get(url);
  },
  getSession() {
    return axios.get('/user/api/user/createUserSession', null, {
      'X-Ext-Tenant-Id': 'defaultExtTenantId',
      'X-Session-Id': getCookie(`${location.hostname}.mushroom.session.id`)
    });
  },
  userVo(sessionId) {
    return withExtTenantIdRequest({
      url: '/user/api/user/getVO',
      method: 'post',
      data: {},
      headers: {
        'X-Session-Id': sessionId,
        'X-Ext-Tenant-Id': 'defaultExtTenantId'
      }
    });
  },
  toolAppListAll() {
    return axios.post('/tool/api/app/listAll', {
      labelQuery: 'hermes'
    });
  },
  // 获取拥有权限
  getTenantIdsByVirtualTenantId(params) {
    return axios.get('/user/api/user/getTenantIdsByLoginName', { params });
  },
  staffGetInfo() {
    return withExtTenantIdRequest({
      url: '/soyoungzg/api/staff/getInfo'
    });
  }
};
