import { withExtTenantIdRequest } from '@/utils/request';

// 品牌分销商品-查询品牌下的分销商品信息（包含搜索条件）
export function commodityList(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/commodity/list',
    method: 'post',
    data
  });
}
// 品牌分销商品-添加商品
export function commodityCreate(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/commodity/create',
    method: 'post',
    data
  });
}

// 品牌分销商品-保存品牌分销商品信息（贸易类型、供货价、控价等）--单条数据
export function commodityUpdate(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/commodity/update',
    method: 'post',
    data
  });
}

// 品牌分销商品-一键同步支持分销商品
export function commoditySaveSynchronousGoods(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/commodity/saveSynchronousGoods',
    method: 'post',
    data
  });
}

// 品牌分销商品-批量修改字段（供货价、平销价、大促价、备注、包邮包税等）
export function batchUpdateProperty(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/commodity/batchUpdateProperty',
    method: 'post',
    data
  });
}

// 品牌档案-品牌分销商品-批量删除
export function commodityDeleteByIds(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/commodity/deleteByIds',
    method: 'post',
    data
  });
}
// 品牌分销商品-一单个删除
export function commodityDelete(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/commodity/delete?id=${id}`,
    method: 'get'
  });
}
// 品牌档案-品牌分销商品-批量启用
export function commodityBatchEnable(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/commodity/batchEnable',
    method: 'post',
    data
  });
}

// 品牌档案-品牌分销商品-批量禁用
export function commodityBatchbatchForbidden(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/commodity/batchForbidden',
    method: 'post',
    data
  });
}

// 品牌档案-品牌分销商品-获取商品信息
export function commodityGet(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/commodity/get?id=${id}`,
    method: 'get'
  });
}

// 品牌分销商品-产品品类-根据查询条件查询
export function productCategoryListByQuery(data = {}) {
  return withExtTenantIdRequest({
    url: `/data-assets-service/api/commodityCenter/productCategory/listByQuery`,
    method: 'post',
    data
  });
}

// 品牌分销商品-查询主数据商品信息
export function commodityListMast(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/commodity/listMaster',
    method: 'post',
    data
  });
}

// 品牌分销商品-添加商品
export function commodityCreateMastData(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/commodity/createMastData',
    method: 'post',
    data
  });
}

// 品牌分销商品-添加导入商品
export function commodityCreateInBatch(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/commodity/createInBatch',
    method: 'post',
    data
  });
}

// 品牌分销商品-上传素材包
export function commodityUploadMaterial(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/commodity/uploadMaterial',
    method: 'post',
    data
  });
}

// 品牌分销商品-获取品牌下已关联商品数量
export function commodityQueryCountByBrandCode(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/commodity/queryCountByBrandCode',
    method: 'post',
    data
  });
}

// 品牌商品导出
export function commodityExportExcel(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/commodity/exportExcel ',
    method: 'post',
    data,
    responseType: 'arraybuffer',
    timeout: 30 * 1000
  });
}

// 品牌分销商品更新信息-获取更新信息
export function commodityQueryDifferentModuleByCode(params) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/commodity/queryDifferentModuleByCode`,
    method: 'get',
    params
  });
}

// 品牌分销商品更新信息-暂不处理
export function commodityNotUpdateDifferentModule(params) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/commodity/notUpdateDifferentModule`,
    method: 'post',
    params
  });
}

// 品牌分销商品更新信息-更新
export function commodityUpdateDifferentModule(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/commodity/updateDifferentModule`,
    method: 'post',
    data
  });
}

// 品牌分销商品获取商品详情
export function commodityDetailGet(params) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/commodityDetail/get`,
    method: 'get',
    params
  });
}

// 品牌分销商品上传商品详情
export function commodityDetailUpdate(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/commodityDetail/update`,
    method: 'post',
    data
  });
}

// 品牌分销商品删除商品详情
export function commodityDetailDelete(params) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/commodityDetail/delete`,
    method: 'post',
    params
  });
}

// 下载商品
export function commodityExportListExcel(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/commodity/exportListExcel',
    method: 'post',
    data,
    responseType: 'arraybuffer',
    headers: { 'Content-Type': 'application/json; application/octet-stream' }
  });
}

// 同步商品信息到商品管理
export function syncCommodityInfoToCommodityService(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg//api/commodity/syncCommodityInfoToCommodityService`,
    method: 'post',
    data
  });
}

// 获取商品扩展信息
export function commodityGetExtendInfo(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg//api/commodity/getExtendInfo?id=${id}`,
    method: 'get'
  });
}
