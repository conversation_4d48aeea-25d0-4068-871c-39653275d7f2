import { withExtTenantIdRequest } from '@/utils/request';

// 获取品牌产品矩阵列表
export function commodityListProductPosition(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/commodity/listProductPosition',
    method: 'post',
    data
  });
}

// 为品牌矩阵设置商品
export function commodityCreateProductPosition(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/commodity/createProductPosition',
    method: 'post',
    data
  });
}

// 为品牌矩阵取消设置商品
export function commodityDeleteProductPosition(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/commodity/deleteProductPosition',
    method: 'post',
    data
  });
}