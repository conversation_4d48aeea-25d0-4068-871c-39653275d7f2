import { withExtTenantIdRequest } from '@/utils/request';

// 运营端-授权品牌管理-列表
export function list(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandLicense/list',
    method: 'post',
    data
  });
}

// 状态字典
export function fetchStatusOptions(Type) {
  return withExtTenantIdRequest({
    url: `/common/api/dict/listByType?type=${Type}`,
    method: 'get'
  });
}

// 运营端-授权品牌管理-列表-启用
export function enableById(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/brandLicense/enable?id=${id}`,
    method: 'POST'
  });
}

// 运营端-授权品牌管理-列表-停用
export function disableById(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/brandLicense/disable?id=${id}`,
    method: 'POST'
  });
}

// 新增
export function create(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandLicense/create',
    method: 'post',
    data
  });
}

// 编辑
export function update(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandLicense/update',
    method: 'post',
    data
  });
}
// 运营端-授权品牌管理-详情
export function getById(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/brandLicense/getDetail?id=${id}`,
    method: 'get'
  });
}

// 导出
export function exportExcel(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandLicense/exportExcel',
    method: 'post',
    data,
    responseType: 'arraybuffer',
    timeout: 30 * 1000
  });
}

// 获取所有业务分组
export function brandCategoryOptions(data = {}) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brand/listBrandInfo',
    method: 'post',
    data
  });
}

// 获取对应权限品牌 不分页
export function brandListAll(data = {}) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandLicense/listAllBrand',
    method: 'post',
    data
  });
}

// -品牌授权信息设置-查询
export function brandLicenseConfigGet() {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandLicenseConfig/get',
    method: 'get'
  });
}
// 品牌授权信息设置
export function brandLicenseConfigInit(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandLicenseConfig/init',
    method: 'post',
    data
  });
}

// 品牌授权管理-列表(无店)
export function storelessLicenseList(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/storelessLicense/list',
    method: 'post',
    data
  });
}

// 品牌授权管理-导出(无店)
export function storelessLicenseExportExcel(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/storelessLicense/exportExcel',
    method: 'post',
    data,
    responseType: 'arraybuffer',
    timeout: 30 * 1000
  });
}

// 品牌授权管理-停用(无店)
export function storelessLicenseDisable(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/storelessLicense/disable?id=${id}`,
    method: 'POST'
  });
}

// 品牌授权管理-启用(无店)
export function storelessLicenseEnable(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/storelessLicense/enable?id=${id}`,
    method: 'POST'
  });
}

// 品牌授权管理-新增(无店)
export function storelessLicenseCreate(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/storelessLicense/create',
    method: 'post',
    data
  });
}

// 品牌授权管理-查看详情(无店)
export function storelessLicenseGet(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/storelessLicense/get?id=${id}`,
    method: 'get'
  });
}

// 品牌授权管理-修改(无店)
export function storelessLicenseUpdate(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/storelessLicense/update',
    method: 'post',
    data
  });
}

// 根据权限获取所有业务分组
export function brandListBrandInfoByPermission(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brand/listBrandInfoByPermission',
    method: 'post',
    data
  });
}

// 创建线上品牌授权书-可授权品牌-1
export function brandLicenseListAvailableLicenseBrand(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandLicense/listAvailableLicenseBrand',
    method: 'post',
    data
  });
}
// 创建线上品牌授权书-授权书类型-2
export function brandLicenseListAvailableLicenseTradeType(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandLicense/listAvailableLicenseTradeType',
    method: 'post',
    data
  });
}
// 创建线上品牌授权书-店铺类型-3
export function brandLicenseListAvailableLicenseShopType(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandLicense/listAvailableLicenseShopType',
    method: 'post',
    data
  });
}
// 创建线上品牌授权书-渠道-4
export function brandLicenseListAvailableLicenseChannel(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandLicense/listAvailableLicenseChannel',
    method: 'post',
    data
  });
}

// 创建线上品牌授权书-有店-授权门槛信息
export function brandLicenseGetAvailableLicenseBrandThresholdDetail(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandLicense/getAvailableLicenseBrandThresholdDetail',
    method: 'post',
    data
  });
}
// 创建线上品牌授权书-无店-授权门槛信息
export function storelessLicenseGetAvailableLicenseBrandThresholdDetail(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/storelessLicense/getAvailableLicenseBrandThresholdDetail',
    method: 'post',
    data
  });
}

// 分销商合同-有效主体列表
export function distributorContractInfoListByAvailable(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorContractInfo/listByAvailable',
    method: 'post',
    data
  });
}
// 创建线上品牌授权书-确认
export function distributorLicenseCreateOnlineLicense(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorLicense/createOnlineLicense',
    method: 'post',
    data
  });
}

// 品牌授权书-无店-可授权品牌门槛信息-订单列表
export function storelessLicenseGetAvailableLicenseBrandThresholdOrderList(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/storelessLicense/getAvailableLicenseBrandThresholdOrderList',
    method: 'post',
    data
  });
}

// 查看特殊授权的品牌-比如大水滴、大陆大水滴
export function brandLicenseListSpecialBrand() {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandLicense/listSpecialBrand',
    method: 'get'
  });
}
// 开启单品授权门槛的品牌-比如伊菲丹
export function brandLicenseListCommodityThresholdBrand() {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandLicense/listCommodityThresholdBrand',
    method: 'get'
  });
}

// 发起线上授权书-授权场景
export function brandLicenseListAvailableLicenseLicenseScene(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandLicense/listAvailableLicenseLicenseScene',
    method: 'post',
    data
  });
}

// 获取分销商授权的店铺白名单-下拉列表
export function distributorWebsiteWhitelistListBriefAllByDistributor(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorWebsiteWhitelist/listBriefAllByDistributor',
    method: 'post',
    data
  });
}
