import { withExtTenantIdRequest } from '@/utils/request';

// 品牌政策分页列表
export function brandPolicyList(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandPolicy/list',
    method: 'post',
    data
  });
}

// 政策 - 删除
export function brandPolicyDelete(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandPolicy/delete',
    method: 'post',
    data
  });
}

// 政策 - 禁用
export function brandPolicyDisable(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandPolicy/disable',
    method: 'post',
    data
  });
}

// 政策 - 启用
export function brandPolicyEnable(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandPolicy/enable',
    method: 'post',
    data
  });
}

// 政策:满赠 - 创建
export function brandPolicyFullGiftCreate(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandPolicyFullGift/create',
    method: 'post',
    data
  });
}

// 政策:满赠 - 创建
export function brandPolicyFullGiftGet(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandPolicyFullGift/get',
    method: 'post',
    data
  });
}

// 政策:满赠 - 修改
export function brandPolicyFullGiftUpdate(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandPolicyFullGift/update',
    method: 'post',
    data
  });
}

// 政策:返点 - 创建
export function brandPolicyReturnAmountCreate(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandPolicyReturnAmount/create',
    method: 'post',
    data
  });
}

// 政策:返点 - 获取
export function brandPolicyReturnAmountGet(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandPolicyReturnAmount/get',
    method: 'post',
    data
  });
}

// 政策:返点 - 修改
export function brandPolicyReturnAmountUpdate(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandPolicyReturnAmount/update',
    method: 'post',
    data
  });
}

// 政策:返货 - 创建
export function brandPolicyReturnGoodsCreate(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandPolicyReturnGoods/create',
    method: 'post',
    data
  });
}

// 政策:返货 - 获取
export function brandPolicyReturnGoodsGet(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandPolicyReturnGoods/get',
    method: 'post',
    data
  });
}

// 政策:返货 - 修改
export function brandPolicyReturnGoodsUpdate(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandPolicyReturnGoods/update',
    method: 'post',
    data
  });
}

// 政策:满减 - 创建
export function brandPolicyFullReductionCreate(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandPolicyFullReduction/create',
    method: 'post',
    data
  });
}

// 政策:满减 - 获取
export function brandPolicyFullReductionGet(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandPolicyFullReduction/get',
    method: 'post',
    data
  });
}

// 政策:满减 - 修改
export function brandPolicyFullReductionUpdate(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandPolicyFullReduction/update',
    method: 'post',
    data
  });
}

// 政策:满折 - 创建
export function brandPolicyFullDiscountCreate(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandPolicyFullDiscount/create',
    method: 'post',
    data
  });
}

// 政策:满折 - 获取
export function brandPolicyFullDiscountGet(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandPolicyFullDiscount/get',
    method: 'post',
    data
  });
}

// 政策:满折 - 修改
export function brandPolicyFullDiscountUpdate(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandPolicyFullDiscount/update',
    method: 'post',
    data
  });
}

// 政策:折扣 - 创建 
export function brandPolicyDiscountCreate(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandPolicyDiscount/create',
    method: 'post',
    data
  });
}

// 政策:折扣 - 获取
export function brandPolicyDiscountGet(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandPolicyDiscount/get',
    method: 'post',
    data
  });
}

// 政策:折扣 - 修改
export function brandPolicyDiscountUpdate(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandPolicyDiscount/update',
    method: 'post',
    data
  });
}

// 政策:包邮 - 创建
export function brandPolicyFreeShippingCreate(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandPolicyFreeShipping/create',
    method: 'post',
    data
  });
}

// 政策:折扣 - 获取
export function brandPolicyFreeShippingGet(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandPolicyFreeShipping/get',
    method: 'post',
    data
  });
}

// 政策:折扣 - 修改
export function brandPolicyFreeShippingUpdate(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandPolicyFreeShipping/update',
    method: 'post',
    data
  });
}

// 政策:返券 - 创建
export function brandPolicyReturnCouponCreate(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandPolicyReturnCoupon/create',
    method: 'post',
    data
  });
}

// 政策:返券 - 获取
export function brandPolicyReturnCouponGet(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandPolicyReturnCoupon/get',
    method: 'post',
    data
  });
}

// 政策:返券 - 修改
export function brandPolicyReturnCouponUpdate(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandPolicyReturnCoupon/update',
    method: 'post',
    data
  });
}