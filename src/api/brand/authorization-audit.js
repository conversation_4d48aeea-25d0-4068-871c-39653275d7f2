import { withExtTenantIdRequest } from '@/utils/request';

// 运营端-品牌授权-授权申请记录列表
export function list(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/licenseApplyRecord/list',
    method: 'post',
    data
  });
}

// 运营端-品牌授权-授权申请记录列表无店
export function listNo(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/storelessLicenceApplyBatch/list',
    method: 'post',
    data
  });
}

// 导出
export function exportExcel(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/licenseApplyRecord/exportExcel',
    method: 'post',
    data,
    responseType: 'arraybuffer',
    timeout: 30 * 1000
  });
}

// 导出无店
export function exportExcelNo(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/storelessLicenceApplyBatch/exportExcel',
    method: 'post',
    data,
    responseType: 'arraybuffer',
    timeout: 30 * 1000
  });
}

// 运营端-品牌授权-授权申请记录-详情
export function getDetail(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/licenseApplyRecord/get?id=${id}`,
    method: 'get'
  });
}
// 运营端-品牌授权-授权申请记录-查询审核信息
export function listAuditLog(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/licenseApplyRecord/listAuditLog',
    method: 'post',
    data
  });
}

// 运营端-品牌授权-审核
export function audit(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/licenseApplyRecord/audit',
    method: 'post',
    data
  });
}

// 运营端-品牌授权-授权申请记录-修改
// export function update(data) {
//   return withExtTenantIdRequest({
//     url: '/soyoungzg/api/licenseApplyRecord/update',
//     method: 'post',
//     data
//   });
// }

// 运营端-品牌授权-修改-重新提交
export function reSubmit(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/licenseApplyRecord/reSubmit',
    method: 'post',
    data
  });
}

// 运营端-品牌授权-修改-驳回到分销商
export function reject(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/licenseApplyRecord/reject',
    method: 'post',
    data
  });
}

// 运营端-品牌授权-授权书列表
export function distributorLicenseList(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorLicense/list',
    method: 'post',
    data
  });
}

// 运营端-品牌授权-授权书列表-导出
export function distributorLicenseExportExcel(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorLicense/exportExcel',
    method: 'post',
    data,
    responseType: 'arraybuffer',
    timeout: 30 * 1000
  });
}

// 运营端-品牌授权-授权书列表-再次发放
export function issued(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorLicense/issued?id=${id}`,
    method: 'post'
  });
}

// 运营端-品牌授权-授权书列表-续签
export function listEffectiveContract(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorContract/listEffectiveContract',
    method: 'post',
    data
  });
}

// 授权渠道
export function listOtherChannel(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorContract/listOtherChannel',
    method: 'post',
    data
  });
}

// 授权书管理-续签弹出框
export function preResign(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorLicense/preResign?id=${id}`,
    method: 'post'
  });
}

// 运营端-品牌授权-授权书列表-续签
export function resignForPC(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorLicense/resignForPC',
    method: 'post',
    data
  });
}

export function createOfflineLicense(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorLicense/createOfflineLicense',
    method: 'post',
    data
  });
}
// 添加线上授权书
export function createOnlineLicense(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorLicense/createOnlineLicense',
    method: 'post',
    data
  });
}

export function listChannelDict() {
  return withExtTenantIdRequest({
    url: '/common/api/dict/listByType?type=soyoungzg_channel',
    method: 'get'
  });
}

export function listShopTypeDict() {
  return withExtTenantIdRequest({
    url: '/common/api/dict/listByType?type=soyoungzg_shop_type',
    method: 'get'
  });
}
// 生成随机7位数授权书编码
export function distributorLicenseGenerateLicenseCode(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorLicense/generateLicenseCode',
    method: 'post',
    data
  });
}
