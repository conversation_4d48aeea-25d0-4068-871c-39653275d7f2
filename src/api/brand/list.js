import { withExtTenantIdRequest } from '@/utils/request';

// 获取品牌授权申请记录分页列表
export function listPage(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorBrandAuthorizationApply/listPage',
    method: 'post',
    data
  });
}
// 审核授权申请
export function applyAudit(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorBrandAuthorizationApply/audit',
    method: 'post',
    data
  });
}

// 状态字典
export function fetchStatusOptions() {
  return withExtTenantIdRequest({
    url: '/common/api/dict/listByType?type=soyoungzg_audit_status',
    method: 'get'
  });
}
// 批量删除
export function handleDelete(data) {
  return withExtTenantIdRequest({
    url: `/commodity/api/distributorBrandAuthorization/listPage`,
    method: 'post',
    data
  });
}
// 通过id删除品牌
export function deleteById(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/zgBrand/delete?id=${id}`,
    method: 'POST'
  });
}

// 新增
export function create(data) {
  return withExtTenantIdRequest({
    url: '/commodity/api/brand/create',
    method: 'post',
    data
  });
}

// 更新
export function update(data) {
  return withExtTenantIdRequest({
    url: '/commodity/api/brand/update',
    method: 'post',
    data
  });
}
// 通过id获取店铺的信息
export function getById(id) {
  return withExtTenantIdRequest({
    url: `/commodity/api/brand/get?id=${id}`,
    method: 'get'
  });
}

// 获取全部参数信息 不分页
export function listAll(data = {}) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brand/listAllBrandName',
    method: 'post',
    data
  });
}
// 获取渠道授权列表
export function listAuthorization(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorBrandChannelAuthorization/listPage',
    method: 'post',
    data
  });
}

// 业务分组管理-获取所有业务分组
export function getBrandCategoryListAll() {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brand/listBrandInfo',
    method: 'post'
  });
}

export function listBrandPageConfig() {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/activityConfig/listBrandPageConfig',
    method: 'post'
  });
}
export function saveInBatch(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/activityConfig/saveInBatch',
    method: 'post',
    data
  });
}

export function brandGetBrandByCode(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brand/getBrandByCode',
    method: 'post',
    data
  });
}

export function brandBriefListAll(data = {}) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brand/briefListAll',
    method: 'post',
    data
  });
}

// 查询品牌配置
export function brandAutoUpdateConfigGetByCode(code) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/brandAutoUpdateConfig/getByCode?code=${code}`,
    method: 'get'
  });
}

// 创建品牌更新配置
export function brandAutoUpdateConfigCreate(data = {}) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandAutoUpdateConfig/create',
    method: 'post',
    data
  });
}

// 商家端-查询官网首页品牌标签及下面的品牌信息
export function brandLabelListLabelAndBrandInfo() {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandLabel/listLabelAndBrandInfo',
    method: 'post'
  });
}
