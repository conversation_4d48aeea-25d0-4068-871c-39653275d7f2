import { withExtTenantIdRequest } from '@/utils/request';

// 品牌授权配置 - 分页列表
export function brandLicenseShopTypeConfigList(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandLicenseShopTypeConfig/list',
    method: 'post',
    data
  });
}

// 品牌授权配置 - 启用
export function brandLicenseShopTypeConfigEnable(params) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandLicenseShopTypeConfig/enable',
    method: 'post',
    params
  });
}

// 品牌授权配置 - 删除
export function brandLicenseShopTypeConfigDelete(params) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandLicenseShopTypeConfig/delete',
    method: 'post',
    params
  });
}

// 品牌授权配置 - 启用
export function brandLicenseShopTypeConfigDisable(params) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandLicenseShopTypeConfig/disable',
    method: 'post',
    params
  });
}


// 品牌授权 - 授权渠道
export function brandLicenseChannelListAll(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandLicenseChannel/listAll',
    method: 'post',
    data
  });
}

// 品牌授权 - 授权渠道 - 获取店铺类型
export function brandLicenseShopTypeListByChannelId(params) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandLicenseShopType/listByChannelId',
    method: 'get',
    params
  });
}


// 品牌授权 - 授权渠道 - 获取店铺类型
export function brandLicenseShopTypeConfigLSBB(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandLicenseShopTypeConfig/listNotSetShopTypeByBrandIdAndChannelId',
    method: 'post',
    data
  });
}

// 品牌授权 - 创建
export function brandLicenseShopTypeConfigCreate(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandLicenseShopTypeConfig/create',
    method: 'post',
    data
  });
}

// 品牌授权 - 获取
export function brandLicenseShopTypeConfigGet(params) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandLicenseShopTypeConfig/get',
    method: 'get',
    params
  });
}

// 品牌授权 - 修改
export function brandLicenseShopTypeConfigUpdate(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandLicenseShopTypeConfig/update',
    method: 'post',
    data
  });
}
