import { withExtTenantIdRequest } from '@/utils/request';

// 获取品牌授权分页列表
export function listPage(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorBrandAuthorization/listPage',
    method: 'post',
    data
  });
}
// 停止授权
export function stopAuthorization(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorBrandAuthorization/stopAuthorization',
    method: 'post',
    data
  });
}
// 授权延期
export function delayAuthorization(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorBrandAuthorization/delayAuthorization',
    method: 'post',
    data
  });
}

// 状态字典
export function fetchStatusOptions() {
  return withExtTenantIdRequest({
    url:
      '/common/api/dict/listByType?type=soyoungzg_brand_authorization_status',
    method: 'get'
  });
}
// 批量删除
export function handleDelete(data) {
  return withExtTenantIdRequest({
    url: `/commodity/api/distributorBrandAuthorization/listPage`,
    method: 'post',
    data
  });
}
