import { withExtTenantIdRequest } from '@/utils/request';

// 品牌档案-竞品品牌-查找所有
export function brandCompetitorInfoListAll(data = {}) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandCompetitorInfo/listAll',
    method: 'post',
    data
  });
}
// 品牌档案-竞品品牌-新增
export function brandCompetitorInfoCreate(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandCompetitorInfo/create',
    method: 'post',
    data
  });
}
