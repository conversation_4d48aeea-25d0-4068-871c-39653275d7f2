import { withExtTenantIdRequest } from '@/utils/request';

// 获取品牌分类分页列表
export function brandCategoryList(data = {}) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandCategory/list',
    method: 'post',
    data
  });
}

// 获取品牌分类所有列表
export function brandCategoryListAll(data = {}) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandCategory/listAll',
    method: 'post',
    data
  });
}

// 新增品牌分类
export function brandCategoryCreate(data = {}) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandCategory/create',
    method: 'post',
    data
  });
}

// 修改品牌分类
export function brandCategoryUpdate(data = {}) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandCategory/update',
    method: 'post',
    data
  });
}

// 删除品牌分类
export function brandCategoryDelete(data = {}) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandCategory/delete',
    method: 'post',
    data
  });
}
