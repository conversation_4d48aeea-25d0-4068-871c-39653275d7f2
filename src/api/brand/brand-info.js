import { withExtTenantIdRequest } from '@/utils/request';

// 品牌信息列表
export function listPage(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brand/list',
    method: 'post',
    data
  });
}

// 批量删除
export function deleteByIds(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/brand/deleteByIds`,
    method: 'post',
    data
  });
}
// 通过id删除品牌
export function deleteById(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/brand/delete?id=${id}`,
    method: 'POST'
  });
}

// 新增
export function create(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brand/create',
    method: 'post',
    data
  });
}

// 更新
export function update(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brand/update',
    method: 'post',
    data
  });
}

// 通过id获取店铺的信息
export function getById(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/brand/get?id=${id}`,
    method: 'get'
  });
}

// 查看所有品牌中心的品牌
export function listAllBrandCenter() {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/brand/listAllBrandCenter`,
    method: 'post'
  });
}

// 查看品牌名称
export function listAllBrandName() {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/brand/listAllBrandName`,
    method: 'post'
  });
}

// 更新品牌状态
export function updateStatus(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brand/updateStatus',
    method: 'post',
    data
  });
}
// 排序 修改商品排序，顺序指定
export function changeSort(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/brand/updateSort`,
    method: 'post',
    data
  });
}
// 置顶 调整排序,type的类型:置顶-TOTOP/上移-UP/下移-DOWN
export function changeSortTOTOP(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/brand/changeSort`,
    method: 'post',
    data
  });
}

// 品牌更新信息-获取更新信息
export function brandQueryDifferentModuleByCode(params) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/brand/queryDifferentModuleByCode`,
    method: 'get',
    params
  });
}

// 品牌更新信息-暂不处理
export function brandNotUpdateDifferentModule(params) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/brand/notUpdateDifferentModule`,
    method: 'post',
    params
  });
}

// 品牌更新信息-更新
export function brandUpdateDifferentModule(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/brand/updateDifferentModule`,
    method: 'post',
    data
  });
}

// 品牌档案-查询品牌的附属数据(奖项/宣传/热度)
export function brandQueryBrandSquareAttachment(params) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/brand/queryBrandSquareAttachment`,
    method: 'get',
    params
  });
}

// 品牌更新信息商品-获取更新信息
export function brandQueryCommodityDifferentModuleByCode(params) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/brand/queryCommodityDifferentModuleByCode`,
    method: 'get',
    params
  });
}

// 品牌更新信息商品-暂不处理
export function brandNotUpdateCommodityDifferentModule(params) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/brand/notUpdateCommodityDifferentModule`,
    method: 'post',
    params
  });
}

// 品牌更新信息商品-更新
export function brandUpdateCommodityDifferentModule(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/brand/updateCommodityDifferentModule`,
    method: 'post',
    data
  });
}

// 校验品牌是否已存在, 存在返回code:561
export function brandVerifyBrandIsExist(brandCode) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/brand/verifyBrandIsExist?brandCode=${brandCode}`,
    method: 'get',
    processError: false
  });
}

// 不展示给分销商
export function brandNotShowToDistributor(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/brand/notShowToDistributor?id=${id}`,
    method: 'post'
  });
}
// 展示给分销商
export function brandShowToDistributor(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/brand/showToDistributor?id=${id}`,
    method: 'post'
  });
}

// 品牌选择查询
export function brandListByMarketing(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brand/listByMarketing',
    method: 'post',
    data
  });
}

// 品牌档案 - 批量更新授权主体公司
export function brandBatchUpdateBrandAuthorityCompany(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brand/batchUpdateBrandAuthorityCompany',
    method: 'post',
    data
  });
}
