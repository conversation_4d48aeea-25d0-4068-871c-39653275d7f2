import { withExtTenantIdRequest } from '@/utils/request';

// 授权店铺--分页列表
export function brandLicenseChannelList(data = {}) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandLicenseChannel/list',
    method: 'post',
    data
  });
}

// 新增授权店铺
export function brandLicenseChannelCreate(data = {}) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandLicenseChannel/create',
    method: 'post',
    data
  });
}

// 修改授权店铺
export function brandLicenseChannelUpdate(data = {}) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandLicenseChannel/update',
    method: 'post',
    data
  });
}

// 删除授权店铺
export function brandLicenseChannelDelete(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/brandLicenseChannel/delete?id=${id}`,
    method: 'post'
  });
}

// 删除授权店铺类型校验
export function brandLicenseShopTypeDeleteVerify(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/brandLicenseShopType/deleteVerify?id=${id}`,
    method: 'post'
  });
}
