import { withExtTenantIdRequest } from '@/utils/request';
// 运营端-营销活动-热销商品-活动列表查询
export function hotCommodityActivityListPage(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/hotCommodityActivity/listPage',
    method: 'post',
    data
  });
}

// 运营端-营销活动-热销商品-新增
export function hotCommodityActivityCreate(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/hotCommodityActivity/create',
    method: 'post',
    data
  });
}

// 运营端-营销活动-热销商品-编辑
export function hotCommodityActivityUpdate(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/hotCommodityActivity/update',
    method: 'post',
    data
  });
}

// 运营端-营销活动-热销商品-删除
export function hotCommodityActivityDelete(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/hotCommodityActivity/delete?id=${id}`,
    method: 'post'
  });
}

// 运营端-营销活动-热销商品-详情查询
export function hotCommodityActivityGet(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/hotCommodityActivity/get?id=${id}`,
    method: 'get'
  });
}

// 根据排序 - 获取商品列表
export function zgCommodityListPageByActivity(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/zgCommodity/listPageByActivity',
    method: 'post',
    data
  });
}

// 根据排序 - 所有热销商品列表
export function hotCommodityActivityListAll(data) {
  return withExtTenantIdRequest({
    url: '/api/hotCommodityActivity/listAll',
    method: 'post',
    data
  });
}

// 微页面 运营端-营销活动-热销商品-详情商品查询
export function PcGetRankListForEdit(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/feature/pc/getRankListForEdit?id=${id}`,
    method: 'get'
  });
}
