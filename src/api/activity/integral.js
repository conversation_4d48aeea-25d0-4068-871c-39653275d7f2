import { withExtTenantIdRequest } from '@/utils/request';

// 积分商品组件-活动查询
export function creditActivityList(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditActivity/list',
    method: 'post',
    data
  });
}

// 活动状态切换
export function changeStatus(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditActivity/changeStatus',
    method: 'post',
    data
  });
}

// 新增积分活动
export function creditActivityCreate(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditActivity/create',
    method: 'post',
    data
  });
}

// 修改积分活动
export function creditActivityUpdate(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditActivity/update',
    method: 'post',
    data
  });
}

// 获取积分活动详情
export function getForInfor(id) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditActivity/getForPC?id=' + id,
    method: 'get'
  });
}

// 积分商品组件-非水羊直供商家商品查询
export function creditActivityListZgCommodity(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditActivity/listZgCommodity',
    method: 'post',
    data
  });
}
// 积分商品组件-水羊直供上架商品查询
export function creditActivityListTradeCenterCommodity(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditActivity/listTradeCenterCommodity',
    method: 'post',
    data
  });
}

// 积分兑换活动列表
export function creditActivityExchangeList(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditActivityExchange/list',
    method: 'post',
    data
  });
}

// 获取积分兑换活动详情
export function creditActivityExchangeGetForPC(id) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditActivityExchange/getForPC?id=' + id,
    method: 'get'
  });
}

// 修改积分兑换活动
export function creditActivityExchangeUpdate(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditActivityExchange/update',
    method: 'post',
    data
  });
}

// 新增积分兑换活动
export function creditActivityExchangeCreate(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditActivityExchange/create',
    method: 'post',
    data
  });
}

// 积分兑换活动启用状态切换
export function activityExchangeChangeStatus(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditActivityExchange/changeStatus',
    method: 'post',
    data
  });
}

// 删除积分兑换活动
export function activityExchangeDelete(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditActivityExchange/delete',
    method: 'post',
    data
  });
}

// 活动商品全部启用
export function enableAllCommodity(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditActivityExchange/enableAllCommodity',
    method: 'post',
    data
  });
}

// 清除更新标识
export function clearAllCommodityTag(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditActivityExchange/clearAllCommodityTag',
    method: 'post',
    data
  });
}

// 积分兑换活动列表导出
export function creditActivityExchangeExport (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditActivityExchange/export',
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}

// 活动人群数量
export function customerInfoGet(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditActivityExchange/customerInfo/get',
    method: 'post',
    data
  });
}

// 下载导入分销商失败记录
export function publicExportExcel(url, data) {
  return withExtTenantIdRequest({
    url: `${url}`,
    method: 'get',
    responseType: 'arraybuffer',
    params: data,
    timeout: 30 * 1000
  });
}

// 积分兑换记录列表
export function creditOrderList(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditOrder/list',
    method: 'post',
    data
  });
}

// 积分兑换记录退款
export function creditOrderRefund(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditOrder/refund',
    method: 'post',
    data
  });
}

// 积分兑换记录人工核销
export function creditOrderVerification(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditOrder/verification',
    method: 'post',
    data
  });
}

// 积分兑换记录导出
export function creditOrderExport (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditOrder/export',
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}

// 获取全部员工
export function staffListAll (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/staff/listAll',
    method: 'post',
    data,
  });
}

// 运营PC-积分兑换活动列表
export function creditActivityExchangeListAll(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditActivityExchange/listAll',
    method: 'post',
    data
  });
}
