import { withExtTenantIdRequest } from '@/utils/request';
export function list(data) {
  return withExtTenantIdRequest({
    url: '/marketing/api/couponTotal/listPage',
    method: 'post',
    data
  });
}
export function create(data) {
  return withExtTenantIdRequest({
    url: '/marketing/api/coupon/create',
    method: 'post',
    data
  });
}
export function update(data) {
  return withExtTenantIdRequest({
    url: '/marketing/api/coupon/update',
    method: 'post',
    data
  });
}
export function getById(id) {
  return withExtTenantIdRequest({
    url: `/marketing/api/coupon/get?id=${id}`,
    method: 'get'
  });
}
// 已完成订单列表（旧）
// export function listUserCouponPage(data) {
//   return withExtTenantIdRequest({
//     url: '/marketing/api/userCoupon/listUserCouponPage',
//     method: 'post',
//     data
//   });
// }

// 已完成订单列表（新）
export function listUserCouponPage(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/userCoupon/listUserCouponPage',
    method: 'post',
    data
  });
}

// 发放奖品、优惠券
export function issuePrize(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/couponDraw/draw',
    method: 'post',
    data
  });
}

// 获取用户ID查询分销商信息
export function listByMemberIds(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributor/listByMemberIds',
    method: 'post',
    data
  });
}

// 查询优惠券发放记录
export function couponListCouponByDistributor(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/coupon/listCouponByDistributor',
    method: 'post',
    data
  });
}

// 核销用户券
export function verificationUserCoupon(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/userCoupon/verificationUserCoupon',
    method: 'post',
    data
  });
}

export function exportCouponExcel(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/coupon/exportCouponExcel',
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}

export function exportUserCouponExcel(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/userCoupon/exportUserCouponExcel',
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}