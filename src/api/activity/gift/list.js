import { withExtTenantIdRequest } from '@/utils/request';

// 获取全部的订单信息
export function list(data) {
  return withExtTenantIdRequest({
    url: '/marketing/api/gift/list',
    method: 'post',
    data
  });
}

// 新增
export function create(data) {
  return withExtTenantIdRequest({
    url: '/marketing/api/gift/create',
    method: 'post',
    data
  });
}

// 更新
export function update(data) {
  return withExtTenantIdRequest({
    url: '/marketing/api/gift/update',
    method: 'post',
    data
  });
}
// 通过id获取店铺的信息
export function getById(id) {
  return withExtTenantIdRequest({
    url: `/marketing/api/gift/get?id=${id}`,
    method: 'get'
  });
}

// 启用打包赠品
export function disable(giftId) {
  return withExtTenantIdRequest({
    url: `/marketing/api/gift/disable?giftId=${giftId}`,
    method: 'post'
  });
}
// 不启用赠品
export function enable(giftId) {
  return withExtTenantIdRequest({
    url: `/marketing/api/gift/enable?giftId=${giftId}`,
    method: 'post'
  });
}
