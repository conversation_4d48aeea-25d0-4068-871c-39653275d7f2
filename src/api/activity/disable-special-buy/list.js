import { withExtTenantIdRequest } from '@/utils/request';
// 特供采货
// 特供采货活动列表
export function list(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/specialBuyActivity/listPage',
    method: 'post',
    data
  });
}
// 新增特供采货活动
export function create(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/specialBuyActivity/create',
    method: 'post',
    data
  });
}
// 编辑特供采货活动
export function update(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/specialBuyActivity/update',
    method: 'post',
    data
  });
}
// 通过id查询特供采货活动详情信息
export function getById(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/specialBuyActivity/get?id=${id}`,
    method: 'get'
  });
}
// // 通过id停用特供采货活动
export function disableById(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/specialBuyActivity/disable?id=${id}`,
    method: 'post'
  });
}
// // 通过id删除特供采货活动
export function disableByIddelete(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/specialBuyActivity/disable?id=${id}`,
    method: 'post'
  });
}
// 效果数据
export function getActivityStatistic(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/specialBuyActivity/getActivityStatistic?id=${data}`,
    method: 'get'
  });
}
// 获取订单状态
export function fetchOrderStatusOptions() {
  return withExtTenantIdRequest({
    url: '/common/api/dict/listByType?type=marketing_lottery_status',
    method: 'get'
  });
}
