import { withExtTenantIdRequest } from '@/utils/request';

// 获取全部品牌
export function listBrand(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brand/listAllBrandName',
    method: 'post',
    data
  });
}

export function fetchStatusType() {
  return withExtTenantIdRequest({
    url: '/common/api/dict/listByType?type=virtual_credit_activity_status',
    method: 'get'
  });
}

// 返点活动列表
export function list(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/virtualCreditActivity/list',
    method: 'post',
    data
  });
}

// 新增返点活动
export function create(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/virtualCreditActivity/create',
    method: 'post',
    data
  });
}

// 修改返点活动
export function update(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/virtualCreditActivity/update',
    method: 'post',
    data
  });
}

// 详情返点活动
export function detail(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/virtualCreditActivity/get?id=${id}`,
    method: 'get'
  });
}

// 停用返点活动
export function disable(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/virtualCreditActivity/disable?id=${id}`,
    method: 'post'
  });
}

// 单笔返点门槛新增
export function onceCreate(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/activityConfig/create',
    method: 'post',
    data
  });
}
// 单笔返点门槛修改
export function onceUpdate(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/activityConfig/update',
    method: 'post',
    data
  });
}
// 单笔返点门槛查询
export function onceGet(type) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/activityConfig/getByType?type=${type}`,
    method: 'get'
  });
}

// 返点详情&审核
export function listLogPage(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/virtualCreditActivity/listLogPage',
    method: 'post',
    data
  });
}

// 发放返利
export function agreeCreditLog(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/virtualCreditActivity/agreeCreditLog?logId=${id}`,
    method: 'post'
  });
}

// 不发放返利
export function rejectCreditLog(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/virtualCreditActivity/rejectCreditLog?logId=${id}`,
    method: 'post'
  });
}

// 一键发放返利
export function issueCredit(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/virtualCreditActivity/issueCredit?id=${id}`,
    method: 'post'
  });
}

// 导出返点审核列表
export function exportExcelVirtualCreditActivity(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/virtualCreditActivity/exportExcel`,
    method: 'post',
    data,
    responseType: 'arraybuffer',
    timeout: 30 * 1000
  });
}

// 返点活动-返点活动统计
export function getActivityStatistics(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/virtualCreditActivity/getActivityStatistics?id=${id}`,
    method: 'get'
  });
}

// 重新结算
export function reCalculate(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/virtualCreditActivity/reCalculate?activityId=${id}`,
    method: 'post'
  });
}

// 采购明细-活动详情信息
export function getLogDetail(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/virtualCreditActivity/getLogDetail?id=${id}`,
    method: 'get'
  });
}

// 采购明细-列表
export function listPurchase(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/virtualCreditBackOrder/list',
    method: 'post',
    data
  });
}

// 采购明细-查看商品
export function listCommodity(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/virtualCreditBackOrder/listCommodity?id=${id}`,
    method: 'post'
  });
}

// 采购明细-导出
export function exportPurchase(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/virtualCreditBackOrder/exportExcel',
    method: 'post',
    data,
    responseType: 'arraybuffer',
    timeout: 30 * 1000
  });
}
