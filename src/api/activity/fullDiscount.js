import { withExtTenantIdRequest } from '@/utils/request';
export function list(data) {
  return withExtTenantIdRequest({
    url: '/marketing/api/moneyOff/listMoneyOffPage',
    method: 'post',
    data
  });
}
export function create(data) {
  return withExtTenantIdRequest({
    url: '/marketing/api/moneyOff/create',
    method: 'post',
    data
  });
}
export function update(data) {
  return withExtTenantIdRequest({
    url: '/marketing/api/moneyOff/update',
    method: 'post',
    data
  });
}
export function getById(id) {
  return withExtTenantIdRequest({
    url: `/marketing/api/moneyOff/get?id=${id}`,
    method: 'get'
  });
}
// 获取全部的已完成订单信息
export function orderList(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/orderMarketing/listPage',
    method: 'post',
    data
  });
}
// 通过activityId获取已完成订单数
export function fetchOrderNums(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/orderMarketing/listActivityOrder',
    method: 'post',
    data
  });
}

// 启用满减折送活动
export function disable(activityId) {
  return withExtTenantIdRequest({
    url: `/marketing/api/moneyOff/disable?activityId=${activityId}`,
    method: 'post'
  });
}
// 不启用满减折送活动
export function enable(activityId) {
  return withExtTenantIdRequest({
    url: `/marketing/api/moneyOff/enable?activityId=${activityId}`,
    method: 'post'
  });
}
// 获取已完成订单的订单状态
export function fetchOrderStatusOptions() {
  return withExtTenantIdRequest({
    url: '/common/api/dict/listByType?type=order_status',
    method: 'get'
  });
}

// 获取满减折送活动状态
export function fetchStatusOptions() {
  return withExtTenantIdRequest({
    url: '/common/api/dict/listByType?type=marketing_bale_status',
    method: 'get'
  });
}
// 获取满减折送活动类型
export function fetchMoneyTypeOptions() {
  return withExtTenantIdRequest({
    url: '/common/api/dict/listByType?type=marketing_config_type_money_off',
    method: 'get'
  });
}
