import { withExtTenantIdRequest } from '@/utils/request';

// 限时抢购列表
export function list(data) {
  return withExtTenantIdRequest({
    url: '/marketing/api/seckill/list',
    method: 'post',
    data
  });
}
// 获取全部的商品列表
export function listCommodity(data) {
  return withExtTenantIdRequest({
    url: '/commodity/api/commodity/listCommodityBriefPage',
    method: 'post',
    data
  });
}
// 获取根据商品id获取规格
export function listSpec(commodityId) {
  return withExtTenantIdRequest({
    url: `/commodity/api/commodity/getSkuDetailByCommodityId?commodityId=${commodityId}`
  });
}
// 新增限时抢购
export function create(data) {
  return withExtTenantIdRequest({
    url: '/marketing/api/seckill/create',
    method: 'post',
    data
  });
}
// 编辑限时抢购
export function update(data) {
  return withExtTenantIdRequest({
    url: '/marketing/api/seckill/update',
    method: 'post',
    data
  });
}
// 通过id查询限时抢购信息
export function getById(id) {
  return withExtTenantIdRequest({
    url: `/marketing/api/seckill/get?id=${id}`,
    method: 'get'
  });
}
// 通过id查询限时抢购信息-简化版
export function getBrief(id) {
  return withExtTenantIdRequest({
    url: `/marketing/api/seckill/getBrief?id=${id}`,
    method: 'get'
  });
}
// 通过id停用活动
export function disableById(id) {
  return withExtTenantIdRequest({
    url: `/marketing/api/seckill/disable?id=${id}`,
    method: 'post'
  });
}
// 通过id删除活动
export function deleteById(id) {
  return withExtTenantIdRequest({
    url: `/marketing/api/seckill/delete?id=${id}`,
    method: 'post'
  });
}

// 通过id获取商品参与的优惠活动
export function listCommodityTool(data) {
  return withExtTenantIdRequest({
    url: '/marketing/api/marketingToolCommodityEs/listAll',
    method: 'post',
    data
  });
}
// 获取订单营销活动列表 （效果数据）
export function listActivityOrder(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/orderMarketing/listActivityOrder',
    method: 'post',
    data
  });
}

// 追加库存
export function appendStock(data) {
  return withExtTenantIdRequest({
    url: '/marketing/api/seckill/appendStock',
    method: 'post',
    data
  });
}

// 修改限购
export function appendLimitQuantity(data) {
  return withExtTenantIdRequest({
    url: '/marketing/api/seckill/appendLimitQuantity',
    method: 'post',
    data
  });
}
