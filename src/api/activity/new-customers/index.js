import { withExtTenantIdRequest, closeErrorRequest } from '@/utils/request';

// 新人有礼列表
export function list(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/newcustomerActivity/list',
    method: 'post',
    data
  });
}

// 新客有礼-新增
export function create(data) {
  return closeErrorRequest({
    url: '/soyoungzg/api/newcustomerActivity/create',
    method: 'post',
    data
  });
}

// 新客有礼-编辑
export function update(data) {
  return closeErrorRequest({
    url: '/soyoungzg/api/newcustomerActivity/update',
    method: 'post',
    data
  });
}

// 新客有礼-查询详情
export function getNewcustomerActivity(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/newcustomerActivity/get?id=${id}`,
    method: 'get'
  });
}

// 新客有礼-停用
export function disable(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/newcustomerActivity/disable?id=${id}`,
    method: 'post'
  });
}
