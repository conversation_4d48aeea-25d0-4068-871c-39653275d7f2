import { withExtTenantIdRequest } from '@/utils/request';

// 获取全部的订单信息
export function list(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/sampleActivity/list',
    method: 'post',
    data
  });
}

// 新增
export function create(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/sampleActivity/create',
    method: 'post',
    data
  });
}

// 更新
export function update(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/sampleActivity/update',
    method: 'post',
    data
  });
}
// 通过id获取店铺的信息
export function getById(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/sampleActivity/get?id=${id}`,
    method: 'get'
  });
}

// 追加库存
export function appendStock(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/sampleActivity/appendStock`,
    method: 'post',
    data
  });
}

// 下面是小样领用详情
// 获取全部的小样领用申请明细
export function detailList(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/sampleApplyRecord/list',
    method: 'post',
    data
  });
}

// 获取小样审核状态
export function fetchStatusOptions(data) {
  return withExtTenantIdRequest({
    url: '/common/api/dict/listByType?type=sample_apply_record_status',
    method: 'get'
  });
}

// 批量审核
export function auditItems(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/sampleApplyRecord/auditList',
    method: 'post',
    data
  });
}

// 审核
export function audit(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/sampleApplyRecord/audit',
    method: 'post',
    data
  });
}

// 获取审核明细
export function getDetailById(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/sampleApplyRecord/get?id=${id}`,
    method: 'get'
  });
}

// 停用小样
export function disable(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/sampleActivity/disable?id=${id}`,
    method: 'post'
  });
}

// 导出小样申请
export function exportExcel(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/sampleApplyRecord/exportExcel`,
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}

// 小样全局限领数量设置详情
export function getSample() {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/activityConfig/getSample',
    method: 'get'
  });
}

// 修改小样全局限领数量设置
export function saveSample(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/activityConfig/saveSample?limitNum=${data}`,
    method: 'post'
  });
}

// 获取样品来源
export function fetchSourceOptions(data) {
  return withExtTenantIdRequest({
    url: '/common/api/dict/listByType?type=sample_apply_record_source',
    method: 'get'
  });
}