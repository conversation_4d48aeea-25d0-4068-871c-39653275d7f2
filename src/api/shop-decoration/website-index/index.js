import { withExtTenantIdRequest } from '@/utils/request';

// 官网首页列表
export function brandWebsiteExtInfoList(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandWebsiteExtInfo/list',
    method: 'post',
    data
  });
}
// 更新品牌信息
export function updateBrandWebsiteExtInfo(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandWebsiteExtInfo/updateBrandWebsiteExtInfo',
    method: 'post',
    data
  });
}

// 批量设置标签
export function updateBatchBrandLabel(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandWebsiteExtInfo/updateBatchBrandLabel',
    method: 'post',
    data
  });
}

// 获取品牌标签下拉
export function brandLabelListAll(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandLabel/listAll',
    method: 'post',
    data
  });
}

// 获取品牌标签列表
export function brandLabelList(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandLabel/list',
    method: 'post',
    data
  });
}

// 新增品牌标签
export function brandLabelCreate(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandLabel/create',
    method: 'post',
    data
  });
}
// 删除品牌标签
export function brandDelete(id) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandLabel/delete?id=' + id,
    method: 'post'
  });
}
// 更新品牌标签
export function brandLabelUpdate(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandLabel/update',
    method: 'post',
    data
  });
}
