import { withExtTenantIdRequest } from '@/utils/request';
// 微页面列表
export function listAll(data) {
  return withExtTenantIdRequest({
    url: '/commodity/api/commodityKind/listAll',
    method: 'post',
    data
  });
}

export function updateSort(data) {
  return withExtTenantIdRequest({
    url: '/commodity/api/commodityKind/updateSort',
    method: 'post',
    data
  });
}

export function deleteItem(data) {
  return withExtTenantIdRequest({
    url: '/commodity/api/commodityKind/delete',
    method: 'post',
    data
  });
}

export function create(data) {
  return withExtTenantIdRequest({
    url: '/commodity/api/commodityKind/create',
    method: 'post',
    data
  });
}

export function update(data) {
  return withExtTenantIdRequest({
    url: '/commodity/api/commodityKind/update',
    method: 'post',
    data
  });
}

export function getById(id) {
  return withExtTenantIdRequest({
    url: '/commodity/api/commodityKind/get?id=' + id
  });
}
