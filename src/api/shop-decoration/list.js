import { withExtTenantIdRequest } from '@/utils/request';

// 获取微页面
export function fetchMP(data) {
  return withExtTenantIdRequest({
    url: `/marketing/api/feature/getByCategory?category=${data}`,
    method: 'post'
  });
}

// 新增微页面
export function createMP(data) {
  return withExtTenantIdRequest({
    // url: '/marketing/api/feature/create',
    url: '/soyoungzg/api/feature/pc/create',
    method: 'post',
    data
  });
}

// 编辑微页面
export function updateMP(data) {
  return withExtTenantIdRequest({
    // url: `/marketing/api/feature/update`,
    url: `/soyoungzg/api/feature/pc/update`,
    method: 'post',
    data
  });
}

// 获取当前店铺的主题色
export function fetchThemeColor() {
  return withExtTenantIdRequest({
    url: `/newretail/api/shopThemeStyle/getMyThemeStyle`,
    method: 'post'
  });
}
/**
 * PC-生成小程序短链
 * https://apidoc.syounggroup.com/doc.html#/HERMES-%E5%88%86%E9%94%80%E7%AE%A1%E7%90%86/shortUrl/generateWxUrlLinkForPCUsingPOST
 */
export function shortUrlGenerateWxUrlLinkForPC(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/shortUrl/generateWxUrlLinkForPC`,
    method: 'post',
    data
  });
}

/**
 * PC-分页获取已生成的短链
 * https://apidoc.syounggroup.com/doc.html#/HERMES-%E5%88%86%E9%94%80%E7%AE%A1%E7%90%86/shortUrl/listPageUsingPOST_105
 */
export function shortUrlList(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/shortUrl/list`,
    method: 'post',
    data
  });
}
