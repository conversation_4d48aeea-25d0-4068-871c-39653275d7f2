import { withExtTenantIdRequest } from '@/utils/request';
// 微页面列表
export function list(data) {
  return withExtTenantIdRequest({
    url: '/marketing/api/feature/listPage',
    method: 'post',
    data
  });
}

// 微页面分类列表
export function classifyList(data) {
  return withExtTenantIdRequest({
    url: '/marketing/api/featureCategory/listAll',
    method: 'post',
    data
  });
}

// 分类列表---用于微页面列表搜索下拉框
export function categroyList() {
  return withExtTenantIdRequest({
    url: '/marketing/api/featureCategory/listAllBrief',
    method: 'post',
    data: {}
  });
}

// 修改微页面分类
export function modifyCategory(data) {
  return withExtTenantIdRequest({
    url: '/marketing/api/feature/batchModifyFeatureCategory',
    method: 'post',
    data
  });
}
// 设置为主页
export function changeToHome(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/feature/pc/changeToHome',
    method: 'post',
    data
  });
}

// 获取未分类的微页面的总个数
export function countNoneFeatureCategory() {
  return withExtTenantIdRequest({
    url: '/marketing/api/featureCategory/countNoneFeatureCategory',
    method: 'get'
  });
}
// 新增分类
export function classifyCreate(name) {
  return withExtTenantIdRequest({
    url: '/marketing/api/featureCategory/create',
    method: 'post',
    data: {
      name
    }
  });
}
// 修改分类
export function classifyUpdate(data) {
  return withExtTenantIdRequest({
    url: '/marketing/api/featureCategory/update',
    method: 'post',
    data
  });
}

// 删除分类
export function classifyDelete(id) {
  return withExtTenantIdRequest({
    url: `/marketing/api/featureCategory/delete?id=${id}`,
    method: 'post'
  });
}
// 微页面删除
export function handleDelete(id) {
  return withExtTenantIdRequest({
    url: `/marketing/api/feature/delete?id=${id}`,
    method: 'post'
  });
}

// 设置页面为h5首页
export function setH5(id) {
  return withExtTenantIdRequest({
    url: `/marketing/api/feature/setAsHomePage?id=${id}`,
    method: 'post'
  });
}

// 设置页面为小程序首页
export function setMp(id) {
  return withExtTenantIdRequest({
    url: `/marketing/api/feature/setAsWeappHomePage?id=${id}`,
    method: 'post'
  });
}

// 设置页面为字节跳动小程序首页
export function setBdmp(id) {
  return withExtTenantIdRequest({
    url: `/marketing/api/feature/changeToBytedanceHomePage?id=${id}`,
    method: 'post'
  });
}

// 创建店铺
export function create(data) {
  return withExtTenantIdRequest({
    // url: '/marketing/api/feature/create',
    url: '/soyoungzg/api/feature/pc/create',
    method: 'post',
    data
  });
}

// 获取页面详情
export function getDetail(id) {
  return withExtTenantIdRequest({
    // url: `/marketing/api/feature/get?id=${id}`
    url: `/soyoungzg/api/feature/pc/get?id=${id}`
  });
}

// 修改
export function update(data) {
  return withExtTenantIdRequest({
    // url: '/marketing/api/feature/update',
    url: `/soyoungzg/api/feature/pc/update`,
    method: 'post',
    data
  });
}
// 选择限时抢购活动
export function fetchSeckillListPage(data) {
  return withExtTenantIdRequest({
    url: '/marketing/api/seckill/listSeckillForFeaturePC',
    method: 'post',
    data
  });
}

// 获取微页面信息
export function getByCategory(category) {
  return withExtTenantIdRequest({
    // url: `/marketing-service/api/feature/getByCategory?category=${category}`,
    url: `/soyoungzg/api/feature/app/getByCategory?category=${category}`,
    method: 'post'
  });
}

// 获取微页面信息  参数列表：FUNCTION_INTRODUCTION-功能介绍、DISTRIBUTOR_MANAGEMENT-分销商管理规范、SERVICE_AGREEMENT-平台使用协议、BUSINESS_LICENSE-营业执照、HELPER_HAND_BOOK-帮助手册
export function getListByCategory(data) {
  return withExtTenantIdRequest({
    url: `/marketing-service/api/feature/listByCategory`,
    method: 'post',
    data
  });
}

// 图片上传
export function saveImgFromLink(data) {
  return withExtTenantIdRequest({
    url: `/file-service/api/image/saveImageFromLink`,
    method: 'post',
    timeout: 30000,
    data
  });
}

export function fileUpload(data) {
  return withExtTenantIdRequest({
    url: '/file/api/image/upload',
    method: 'post',
    data
  });
}

export function videoUpload(data, callback) {
  return withExtTenantIdRequest({
    url: '/file/api/file/upload',
    method: 'post',
    data,
    onUploadProgress: (progressEvent) => {
      callback(progressEvent);
    }
  });
}

// 微页面发布
export function microPublish(data) {
  return withExtTenantIdRequest({
    url: `/marketing/api/feature/historyPublish`,
    method: 'post',
    data
  });
}

// 微页面发布后清除缓存
export function featurePcCleanCache() {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/feature/pc/cleanCache`,
    method: 'get'
  });
}

// 微页面协议发布通知
export function agreementNoticePublish(data) {
  return withExtTenantIdRequest({
    url: `/marketing/api/agreementNotice/publish`,
    method: 'post',
    data
  });
}

// 发布免责声明
export function agreementNoticePublishForDisclaimer(bizType) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/agreementNotice/publish?bizType=${bizType}`,
    method: 'get'
  });
}

// 创建微页面协议
export function createAgreement(data) {
  return withExtTenantIdRequest({
    url: '/marketing/api/feature/createAgreement',
    method: 'post',
    data
  });
}

// 微页面修改备注
export function operatorRemarksUpdate(data) {
  return withExtTenantIdRequest({
    url: '/marketing/api/feature/operatorRemarksUpdate',
    method: 'post',
    data
  });
}
// 获取featureConfig列表
export function featureConfigListAll() {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/featureConfig/listAll',
    method: 'get'
  });
}
// 微页面配置
export function featureConfigUpdateAll(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/featureConfig/updateAll',
    method: 'post',
    data
  });
}
export function featureConfigGetByType(type) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/featureConfig/getByType?type=' + type,
    method: 'get'
  });
}
