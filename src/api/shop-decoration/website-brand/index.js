import { withExtTenantIdRequest } from '@/utils/request';

// 获取全部品牌
export function listBrand(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brand/listAllBrandName',
    method: 'post',
    data
  });
}

// 获取官网品牌
export function listByBrandType(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/officialBrandRecommend/listByBrandType?brandType=${data}`,
    method: 'post'
  });
}

// 新增官网品牌
export function create(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/officialBrandRecommend/create',
    method: 'post',
    data
  });
}

// 编辑官网品牌
export function update(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/officialBrandRecommend/update`,
    method: 'post',
    data
  });
}
