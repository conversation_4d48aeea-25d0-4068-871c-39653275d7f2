import { withExtTenantIdRequest } from '@/utils/request';
// 获取跳转链接数据字典
export function fetchLinkkType() {
  return withExtTenantIdRequest({
    url: '/common/api/dict/listByType?type=marketing_shop_nav_item_type',
    method: 'get'
  });
}

// 通过该接口判断是否 小程序已授权
export function isVersion(data) {
  return withExtTenantIdRequest({
    url: `/wxopen-proxy-service/api/authorizerInfo/getAuthInfoByType?type=${data}`,
    method: 'get'
  });
}

// 通过该接口判断小程序是否有审核中的版本，获取提交审核时间；
export function getMiniAuditUnderVersionTime() {
  return withExtTenantIdRequest({
    url: `/wxopen-proxy-service/api/shopMiniProgram/getMiniAuditUnderVersionTime`,
    method: 'get'
  });
}

// 小程序提交审核接口
export function updateAndSubmitAudit(data) {
  return withExtTenantIdRequest({
    url: `/wxopen-proxy-service/api/shopMiniProgram/updateAndSubmitAudit`,
    method: 'post',
    timeout: 30000, // 请求超时时间
    data
  });
}

// 新增自定义导航栏
export function create(data) {
  return withExtTenantIdRequest({
    url: `/marketing-service/api/shopNavigation/create`,
    method: 'post',
    data
  });
}
// 新增自定义导航栏
export function update(data) {
  return withExtTenantIdRequest({
    url: `/marketing-service/api/shopNavigation/update`,
    method: 'post',
    data
  });
}
// 获取导航栏对象
export function getShopNav(shopNavType) {
  return withExtTenantIdRequest({
    url: `/marketing-service/api/shopNavigation/get?shopNavType=${shopNavType}`,
    method: 'get'
  });
}
