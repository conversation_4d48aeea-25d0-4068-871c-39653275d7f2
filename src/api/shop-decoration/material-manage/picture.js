import { withExtTenantIdRequest } from '@/utils/request';

// 上传图片并且添加入素材中心
export function fetchMP(groupId) {
  return withExtTenantIdRequest({
    url: `/file/api/image/uploadAndInsert?groupId=${groupId}`,
    method: 'post'
  });
}

// 新增分组
export function groupCreate(data) {
  return withExtTenantIdRequest({
    url: '/file/api/materialGroup/create',
    method: 'post',
    data
  });
}
// 删除分组
export function groupdElete(id) {
  return withExtTenantIdRequest({
    url: `/file/api/materialGroup/delete?id=${id}`,
    method: 'post'
  });
}

// 获取素材分组分页列表
export function groupdList(data) {
  return withExtTenantIdRequest({
    url: `/file/api/materialGroup/listPage`,
    method: 'post',
    data
  });
}

// 修改素材分组
export function updateName(data) {
  return withExtTenantIdRequest({
    url: `/file/api/materialGroup/updateName`,
    method: 'post',
    data
  });
}

// 删除素材图片
export function deletePic(id) {
  return withExtTenantIdRequest({
    url: `/file/api/materialPic/delete?id=${id}`,
    method: 'post'
  });
}
// 批量删除素材图片
export function deletePics(data) {
  return withExtTenantIdRequest({
    url: `/file/api/materialPic/deleteByIds`,
    method: 'post',
    data
  });
}
// 获取素材图片对象
export function getPic(id) {
  return withExtTenantIdRequest({
    url: `/file/api/materialPic/get?id=${id}`,
    method: 'get'
  });
}
// 获取素材图片分页列表
export function listPic(data) {
  return withExtTenantIdRequest({
    url: `/file/api/materialPic/listPage`,
    method: 'post',
    data
  });
}
// 修改素材图片分组
export function updateGroup(data) {
  return withExtTenantIdRequest({
    url: `/file/api/materialPic/updateGroup`,
    method: 'post',
    data
  });
}
// 修改素材图片名称
export function updatePicName(data) {
  return withExtTenantIdRequest({
    url: `/file/api/materialPic/updatePicName`,
    method: 'post',
    data
  });
}
