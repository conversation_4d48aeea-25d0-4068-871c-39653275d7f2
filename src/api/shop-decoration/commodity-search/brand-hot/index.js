// 品牌热度
import { withExtTenantIdRequest } from '@/utils/request';

// 分页查询
export function brandHotShortcutList(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg//api/brandHotShortcut/list',
    method: 'post',
    data
  });
}

// 品牌热度：创建
export function brandHotShortcutCreate(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg//api/brandHotShortcut/create',
    method: 'post',
    data
  });
}

// 品牌热度：删除
export function brandHotShortcutDelete(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg//api/brandHotShortcut/delete',
    method: 'post',
    data
  });
}

// 品牌热度：获取
export function brandHotShortcutGet(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg//api/brandHotShortcut/get',
    method: 'post',
    data
  });
}

// 品牌热度：修改
export function brandHotShortcutUpdate(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg//api/brandHotShortcut/update',
    method: 'post',
    data
  });
}

// 品牌热度：排序
export function brandHotShortcutChangeSort(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg//api/brandHotShortcut/changeSort',
    method: 'post',
    data
  });
}