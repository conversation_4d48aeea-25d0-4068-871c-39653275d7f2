// 搜索直达
import { withExtTenantIdRequest } from '@/utils/request';

// 新增素材模板
export function create(data) {
  return withExtTenantIdRequest({
    url: '/marketing-service/api/searchShortcut/create',
    method: 'post',
    data
  });
}
// 获取搜索直达分页列表
export function listPage(data) {
  return withExtTenantIdRequest({
    url: '/marketing-service/api/searchShortcut/listPage',
    method: 'post',
    data
  });
}
// 修改搜索直达
export function update(data) {
  return withExtTenantIdRequest({
    url: '/marketing-service/api/searchShortcut/update',
    method: 'post',
    data
  });
}
// 删除素材模板
export function listdelete(id) {
  return withExtTenantIdRequest({
    url: `/marketing-service/api/searchShortcut/delete?id=${id}`,
    method: 'post'
  });
}

// 获取搜索直达对象
export function getSearchShortcut(id) {
  return withExtTenantIdRequest({
    url: `/marketing-service/api/searchShortcut/get?id=${id}`,
    method: 'get'
  });
}
