// 热门标签
import { withExtTenantIdRequest } from '@/utils/request';

// 新增
export function create(data) {
  return withExtTenantIdRequest({
    url: '/commodity-service/api/commodityHotTag/create',
    method: 'post',
    data
  });
}
// 列表
export function listPage(data) {
  return withExtTenantIdRequest({
    url: '/commodity-service/api/commodityHotTag/listPage',
    method: 'post',
    data
  });
}
// 修改
export function update(data) {
  return withExtTenantIdRequest({
    url: '/commodity-service/api/commodityHotTag/update',
    method: 'post',
    data
  });
}
// 删除
export function listDelete(id) {
  return withExtTenantIdRequest({
    url: `/commodity-service/api/commodityHotTag/delete?id=${id}`,
    method: 'post'
  });
}

// 查看
export function getcommodityHotTag(id) {
  return withExtTenantIdRequest({
    url: `/commodity-service/api/commodityHotTag/get?id=${id}`,
    method: 'get'
  });
}

// 排序
export function changeSort(id, type) {
  return withExtTenantIdRequest({
    url: `/commodity-service/api/commodityHotTag/changeSort/${id}/${type}`,
    method: 'post'
  });
}
