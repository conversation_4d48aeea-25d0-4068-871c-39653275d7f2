import { withExtTenantIdRequest } from '@/utils/request';

/**
 * PC-进销存-销售数据录入列表
 * https://apidoc.syounggroup.com/doc.html#/HERMES-%E5%88%86%E9%94%80%E7%AE%A1%E7%90%86/SOYOUNGZG_V5.0.2/listPageUsingPOST_68
 * @param data
 */
export function psiSaleEntryStatList(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/psiSaleEntryStat/list',
    method: 'post',
    data
  });
}

/**
 * PC-进销存-销售数据录入列表导出
 * https://apidoc.syounggroup.com/doc.html#/HERMES-%E5%88%86%E9%94%80%E7%AE%A1%E7%90%86/SOYOUNGZG_V5.0.2/exportUsingPOST_4
 * @param data
 */
export function psiSaleEntryStatExport(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/psiSaleEntryStat/export',
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}

/**
 * PC-销售数据录入列表
 * https://apidoc.syounggroup.com/doc.html#/HERMES-%E5%88%86%E9%94%80%E7%AE%A1%E7%90%86/SOYOUNGZG_V5.0.2/listPageUsingPOST_67
 * @param data
 */
export function psiSaleEntryList(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/psiSaleEntry/list',
    method: 'post',
    data
  });
}

/**
 * PC-已录入销售数据选择
 * https://apidoc.syounggroup.com/doc.html#/HERMES-%E5%88%86%E9%94%80%E7%AE%A1%E7%90%86/SOYOUNGZG_V5.0.2/chooseUsingPOST
 * @param data
 */
export function psiSaleEntryChoose(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/psiSaleEntry/choose',
    method: 'post',
    data
  });
}

/**
 * PC-已录入销售数据删除
 * https://apidoc.syounggroup.com/doc.html#/HERMES-%E5%88%86%E9%94%80%E7%AE%A1%E7%90%86/SOYOUNGZG_V5.0.2/deleteUsingPOST_42
 * @param data
 */
export function psiSaleEntryDelete(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/psiSaleEntry/delete',
    method: 'post',
    data
  });
}

/**
 * PC-销售数据录入列表导出
 * https://apidoc.syounggroup.com/doc.html#/HERMES-%E5%88%86%E9%94%80%E7%AE%A1%E7%90%86/SOYOUNGZG_V5.0.2/exportUsingPOST_3
 * @param data
 */
export function psiSaleEntryExport(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/psiSaleEntry/export',
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}

/**
 * PC-分销商存货数据导出
 * https://apidoc.syounggroup.com/doc.html#/HERMES-%E5%88%86%E9%94%80%E7%AE%A1%E7%90%86/PC%E7%AB%AF/exportDistributorCommodityInventoryUsingPOST
 * @param data
 */
export function psiSaleEntryExportDistributorCommodityInventory(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/psiSaleEntry/exportDistributorCommodityInventory',
    method: 'post',
    data,
    responseType: 'arraybuffer',
    timeout: 30 * 1000
  });
}

/**
 * PC-销售数据导入保存
 * https://apidoc.syounggroup.com/doc.html#/HERMES-%E5%88%86%E9%94%80%E7%AE%A1%E7%90%86/PC%E7%AB%AF/saveSaleEntryDetailUsingPOST
 * @param data
 */
export function psiSaleEntrySaveSaleEntryDetaill(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/psiSaleEntry/saveSaleEntryDetail',
    method: 'post',
    data
  });
}

/**
 * PC-获取当前白名单店铺下最晚的销售录入时间
 * https://apidoc.syounggroup.com/doc.html#/HERMES-%E5%88%86%E9%94%80%E7%AE%A1%E7%90%86/PC%E7%AB%AF/getLastSaleEntryByShopIdUsingPOST
 * @param distributorId
 */
export function psiSaleEntryGetLastSaleEntryByShopId(distributorId) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/psiSaleEntry/getLastSaleEntryByShopId?distributorId=' + distributorId,
    method: 'post'
  });
}

// 获取进销存配置信息
export function psiConfigQueryPsiConfigInfo() {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/psiConfig/queryPsiConfigInfo`,
    method: 'get'
  });
}

/**
 * PC-修改进销存配置信息
 * https://apidoc.syounggroup.com/doc.html#/HERMES-%E5%88%86%E9%94%80%E7%AE%A1%E7%90%86/PsiConfig/updatePsiConfigUsingPOST
 */
export function psiConfigUpdatePsiConfig(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/psiConfig/updatePsiConfig',
    method: 'post',
    data
  });
}

/**
 * PC-修改分销商进销存管控
 * https://apidoc.syounggroup.com/doc.html#/HERMES-%E5%88%86%E9%94%80%E7%AE%A1%E7%90%86/PC%E7%AB%AF/updatePsiControlUsingPOST
 */
export function distributorUpdatePsiControl(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributor/updatePsiControl',
    method: 'post',
    data
  });
}

/**
 * 进销存管理-进销存概览-获取报表数据更新时间
 * https://apidoc.syounggroup.com/doc.html#/HERMES-%E5%88%86%E9%94%80%E7%AE%A1%E7%90%86/PsiSaleEntryStat/reportUpdateDateUsingPOST
 */
export function psiSaleEntryStatReportUpdateDate(data = {}) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/psiSaleEntryStat/reportUpdateDate',
    method: 'post',
    data
  });
}
