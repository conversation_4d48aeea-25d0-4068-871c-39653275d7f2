import { withExtTenantIdRequest } from '@/utils/request';
// 获取品牌画像信息
export function brandDistributorPortraitGetByBrand(brandId) {
  return withExtTenantIdRequest({
    url: `/soyoungzg//api/brandDistributorPortrait/getByBrand?brandId=${brandId}`,
    method: 'get'
  });
}

// 品牌广场-匹配客户-导出
export function distributorPortraitMatchCustomerExportExcel(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorPortrait/matchCustomerExportExcel`,
    method: 'post',
    data,
    responseType: 'arraybuffer',
    timeout: 30 * 1000
  });
}

// 品牌广场-匹配客户-品牌产品复推
export function distributorPortraitListMatchCustomer(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorPortrait/listMatchCustomer',
    method: 'post',
    data
  });
}
// 品牌广场-匹配客户-默认配置
export function distributorPortraitMatchCustomerDefaultConfig(data = {}) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorPortrait/matchCustomerDefaultConfig',
    method: 'post',
    data
  });
}
// 获取品牌分销商画像分页列表
export function brandDistributorPortraitList(data = {}) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandDistributorPortrait/list',
    method: 'post',
    data
  });
}
// 品牌广场-列表导出
export function brandDistributorPortraitExport(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/brandDistributorPortrait/export`,
    method: 'post',
    data,
    responseType: 'arraybuffer',
    timeout: 30 * 1000
  });
}
