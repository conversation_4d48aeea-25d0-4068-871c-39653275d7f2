import { withExtTenantIdRequest } from '@/utils/request';

// 获取用户画像分页列表
export function distributorPortraitList(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorPortrait/list',
    method: 'post',
    data
  });
}

// 获取用户画像详情
export function distributorPortraitGet(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorPortrait/get?id=${id}`,
    method: 'get'
  });
}
// 二级品类
export function listBrief(data) {
  return withExtTenantIdRequest({
    url: '/commodity/api/commodityKind/listBrief',
    method: 'post',
    data
  });
}
