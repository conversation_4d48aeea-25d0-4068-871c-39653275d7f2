import { withExtTenantIdRequest } from '@/utils/request';

// 广场列表页-分销商广场列表
export function distributorPortraitList(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorPortrait/list',
    method: 'post',
    data
  });
}

// 用户表头配置-查询
export function customHeaderUserGet(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/customHeaderUser/get',
    method: 'post',
    data
  });
}

// 保存用户表头数据
export function customHeaderUserSave(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/customHeaderUser/save',
    method: 'post',
    data
  });
}

// 获取用户表头显示列
export function customHeaderUserListShowColumn(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/customHeaderUser/listShowColumn',
    method: 'post',
    data
  });
}

// 分销商个人画像 采购概况- 详情
export function getPurchaseOverviewDetail(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorPortrait/getPurchaseOverviewDetail?id=${id}`,
    method: 'get'
  });
}

// 分销商个人画像 采购概况- 采购趋势图
export function getPurchaseOverviewTrend(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorPortrait/getPurchaseOverviewTrend',
    method: 'post',
    data
  });
}

// 分销商个人画像 采购分析-品牌采购榜单
export function listPurchaseAnalyzeBrandTop(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorPortrait/listPurchaseAnalyzeBrandTop',
    method: 'post',
    data
  });
}

// 分销商个人画像 采购分析-商品采购榜单
export function listPurchaseAnalyzeCommodityTop(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorPortrait/listPurchaseAnalyzeCommodityTop',
    method: 'post',
    data
  });
}

// 采购分析-品类榜单
export function listPurchaseAnalyzeCommodityCategoryTop(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorPortrait/listPurchaseAnalyzeCommodityCategoryTop',
    method: 'post',
    data
  });
}

// 采购分析-价格区间榜单
export function listPurchaseAnalyzePriceRangeTop(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorPortrait/listPurchaseAnalyzePriceRangeTop',
    method: 'post',
    data
  });
}

// 标签数据
export function getLabelDetail(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorPortrait/getLabelDetail',
    method: 'post',
    data
  });
}

// 分销商个人画像 分销商画像基本信息
export function distributorPortraitGetInfo(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorPortrait/getInfo`,
    method: 'post',
    data
  });
}

// 分销商广场导出
export function distributorPortraitExportExcel(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorPortrait/exportExcel`,
    method: 'post',
    data,
    responseType: 'arraybuffer',
    timeout: 30 * 1000
  });
}

// 分销商广场高级导出
export function distributorPortraitHighLevelExportExcel(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorPortrait/highLevelExportExcel`,
    method: 'post',
    data,
    responseType: 'arraybuffer',
    timeout: 30 * 1000
  });
}

// 分销商沉寂客户模板导出
export function distributorPortraitExportWithSilentTemplate(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorPortrait/exportWithSilentTemplate`,
    method: 'post',
    data,
    responseType: 'arraybuffer',
    timeout: 30 * 1000
  });
}

// 活动人群-获取客户信息
export function distributorPortraitCustomerInfoGet(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorPortrait/customerInfo/get',
    method: 'post',
    data
  });
}
