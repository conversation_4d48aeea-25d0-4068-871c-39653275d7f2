import { withExtTenantIdRequest } from '@/utils/request';

// 从分销商概览留存跳转TAB 分销商
export function listRepurchaseDetail(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorDataAnalysis/listRepurchaseDetail',
    method: 'post',
    data: data
  });
}

// 留存分销商明细-列表合计 分销商
export function summaryRepurchaseDetail(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorDataAnalysis/summaryRepurchaseDetail',
    method: 'post',
    data: data
  });
}
// 留存分销商明细-导出 分销商
export function exportRepurchaseDetailExcel(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorDataAnalysis/exportRepurchaseDetailExcel',
    method: 'post',
    data,
    responseType: 'arraybuffer',
    timeout: 30 * 1000
  });
}

// 从分销商概览留存跳转TAB 品牌
export function listBrandRepurchaseDetail(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorDataAnalysis/listBrandRepurchaseDetail',
    method: 'post',
    data: data
  });
}

// 留存分销商明细-列表合计 品牌
export function summaryBrandRepurchaseDetail(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorDataAnalysis/summaryBrandRepurchaseDetail',
    method: 'post',
    data: data
  });
}

// 留存分销商明细-导出 品牌
export function exportBrandRepurchaseDetailExcel(data) {
  return withExtTenantIdRequest({
    url:
      '/soyoungzg/api/distributorDataAnalysis/exportBrandRepurchaseDetailExcel',
    method: 'post',
    data,
    responseType: 'arraybuffer',
    timeout: 30 * 1000
  });
}
