import { withExtTenantIdRequest } from '@/utils/request';

// 经营详情数据
export function businessTargetStatisticslistAll(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/businessTargetStatistics/listAll',
    method: 'post',
    data: data
  });
}

// 分组设置
export function businessTargetStatisticslistMyGroups(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/businessTargetStatistics/listMyGroups',
    method: 'post',
    data
  });
}

export function getDetailData(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/businessTargetStatistics/listDetail',
    method: 'post',
    data: data
  });
}

export function getGroupList(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/businessTargetStatistics/listMyGroups',
    method: 'post',
    data: data
  });
}
export function getSingleGoodsDetail(specCode) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/zgCommodity/getBySpecCode?specCode=' + specCode
  });
}

export function listBrandDetailStatistic(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/orderCommodityStatistics/listBrandDetailStatistic',
    method: 'post',
    data: data
  });
}

export function brandDetailExport(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/orderCommodityStatistics/exportBrandDetailStatistic',
    method: 'post',
    data,
    responseType: 'arraybuffer',
    timeout: 30 * 1000
  });
}

// 品牌明细导出 天/月 明细
export function exportBrandDetailStatisticByMonthOrYear(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/orderCommodityStatistics/exportBrandDetailStatisticByMonthOrYear',
    method: 'post',
    data,
    responseType: 'arraybuffer',
    timeout: 30 * 1000
  });
}

export function fetchRetentionData(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorDataAnalysis/listRepurchase',
    method: 'post',
    data: data
  });
}

export function fetchPurchaseTrend(id) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributor/getBasicInfo?id=' + id
  });
}
export function getBrandDetail(id) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/zgBrand/getBrandDetail?id=' + id
  });
}
export function fetchBrandRetentionData(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorDataAnalysis/listBrandRepurchase',
    method: 'post',
    data: data
  });
}
