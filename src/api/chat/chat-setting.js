import { withExtTenantIdRequest } from '@/utils/request';
// 新增会话分配规则
export function create(data) {
  return withExtTenantIdRequest({
    url: '/chat-service/api/chatSessionRule/create',
    method: 'post',
    data
  });
}
// 修改会话分配规则
export function update(data) {
  return withExtTenantIdRequest({
    url: '/chat-service/api/chatSessionRule/update',
    method: 'post',
    data
  });
}
// 获取会话分配规则对象
export function getRule() {
  return withExtTenantIdRequest({
    url: '/chat-service/api/chatSessionRule/get',
    method: 'get'
  });
}

// 分组标签列表-用于下拉框
export function customerlistGroupVO() {
  return withExtTenantIdRequest({
    url: '/chat-service/api/customerServiceGroup/listGroupVO',
    method: 'post'
  });
}

// 新增或修改模块开关 --禁用新零售客服功能
export function SET_MINI_MESSAGE_TRANSFER(data) {
  return withExtTenantIdRequest({
    url: '/ocean/api/shopModuleSwitch/update',
    method: 'post',
    data
  });
}

// 根据模块类型获取开关标识 --获取禁用新零售客服功能开关标识
export function GET_MINI_MESSAGE_TRANSFER() {
  return withExtTenantIdRequest({
    url:
      '/ocean/api/shopModuleSwitch/getSwitchByType?type=MINI_MESSAGE_TRANSFER',
    method: 'get'
  });
}
