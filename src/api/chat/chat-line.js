import { withExtTenantIdRequest } from '@/utils/request';
// 排队列表
export function list(data) {
  return withExtTenantIdRequest({
    url: '/chat-service/api/chatSession/listNoAssignCsPage',
    method: 'post',
    data
  });
}
// 咨询来源
export function fetchChannelOptions() {
  return withExtTenantIdRequest({
    url: '/common/api/dict/listByType?type=chat_channel_type',
    method: 'get'
  });
}

// 接入
export function receptionList(data) {
  return withExtTenantIdRequest({
    url: '/chat-service/api/chatSession/assignByHuman',
    method: 'post',
    data
  });
}

// 转接
export function forwardList(data) {
  return withExtTenantIdRequest({
    url: '/chat-service/api/chatSession/forwardList',
    method: 'post',
    data
  });
}

// 单个指派
export function forward(data) {
  return withExtTenantIdRequest({
    url: '/chat-service/api/chatSession/assignSession',
    method: 'post',
    data
  });
}

// 批量指派
export function forwardBatch(data) {
  return withExtTenantIdRequest({
    url: '/chat-service/api/chatSession/assignSessionBatch',
    method: 'post',
    data
  });
}

// 会话列表会单个话转接
export function forwardDialogue(data) {
  return withExtTenantIdRequest({
    url: '/chat-service/api/chatSession/forward',
    method: 'post',
    data
  });
}
// 主管操作客服交班
export function handOver(fromCsId, toCsId) {
  return withExtTenantIdRequest({
    url: `/chat-service/api/chatSession/handOver?fromCsId=${fromCsId}&toCsId=${toCsId}`,
    method: 'post'
  });
}
