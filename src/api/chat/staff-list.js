import { withExtTenantIdRequest } from '@/utils/request';
// 客服名单列表
export function list(data) {
  return withExtTenantIdRequest({
    url: '/chat-service/api/customerService/list',
    method: 'post',
    data
  });
}

// 新增客服
export function create(data) {
  return withExtTenantIdRequest({
    url: '/chat-service/api/customerService/create',
    method: 'post',
    data
  });
}

// 删除客服
export function deleteById(id) {
  return withExtTenantIdRequest({
    url: `/chat-service/api/customerService/delete?id=${id}`,
    method: 'post'
  });
}

// 修改客服
export function update(data) {
  return withExtTenantIdRequest({
    url: `/chat-service/api/customerService/update`,
    method: 'post',
    data
  });
}

// 获取店铺员工分页列表
export function staffListPage(data) {
  return withExtTenantIdRequest({
    url: '/ocean/api/shopStaff/listPage',
    method: 'post',
    data
  });
}
// 获取客服分组分页列表
export function customerListPage() {
  return withExtTenantIdRequest({
    url: '/chat-service/api/customerServiceGroup/listGroupVO',
    method: 'post'
  });
}

// 在线状态字典
export function fetchStatusOptions() {
  return withExtTenantIdRequest({
    url: '/common/api/dict/listByType?type=customer_service_status_type',
    method: 'get'
  });
}

// 修改客服最大接待人数
export function updateCsQuota(data) {
  return withExtTenantIdRequest({
    url: '/chat-service/api/customerService/updateCsQuota',
    method: 'post',
    data
  });
}

// 切换客服状态
export function changeStatus(status, id) {
  return withExtTenantIdRequest({
    url: `/chat-service/api/customerService/${status}?specifiedCsId=${id}`,
    method: 'post'
  });
}
