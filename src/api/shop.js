import { withExtTenantIdRequest } from '@/utils/request';
export function listHotCommodity(dayType = 'LATEST_7_DAYS') {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/shopStatistic/listHotCommodity',
    method: 'get',
    params: { dayType }
  });
}

export function listPayAmount(postData) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/shopStatistic/listPayAmount',
    method: 'post',
    data: postData
  });
}
export function listNewMemberCount(postData) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/shopStatistic/listNewMemberCount',
    method: 'post',
    data: postData
  });
}

// 库存过少 、已售罄SKU列表(最多支持100条数据)
export function getListLowOrNoneStock(data) {
  return withExtTenantIdRequest({
    url: '/commodity-service/api/commodity/listLowOrNoneStockCommodity',
    method: 'post',
    data
  });
}

// 保存短信活动告警管理
export function smsAlarmConfigSave(data) {
  return withExtTenantIdRequest({
    url: '/sms-service/api/smsAlarmConfig/save',
    method: 'post',
    data
  });
}

// 获取短信活动告警管理列表
export function smslistAll(data) {
  return withExtTenantIdRequest({
    url: '/sms-service/api/smsAlarmConfig/listAll',
    method: 'post',
    data
  });
}

// 运营端

// 分销商经营销售数据统计
export function getHome(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/orderStatistics/home`,
    method: 'get'
  });
}

// 订单数据分析
export function listOrderStatisticPage(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/orderStatistics/listOrderStatisticPage',
    method: 'post',
    data
  });
}

// 获取分销商购买数据分析
export function listDistributorPurchaseStatistic(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/orderStatistics/listDistributorPurchaseStatistic',
    method: 'post',
    data
  });
}

// 获取商品销售数据分析
export function listCommoditySaleStatistic(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/orderCommodityStatistics/listCommoditySaleStatistic',
    method: 'post',
    data
  });
}

// 订单来源
// 注册来源渠道类型数据字典获取
export function fetchChannelType() {
  return withExtTenantIdRequest({
    url: '/common/api/dict/listByType?type=syzg_source_channel_type',
    method: 'get'
  });
}
// 获取订单类型
export function fetchOrderTypeOptions() {
  return withExtTenantIdRequest({
    url: '/common/api/dict/listByType?type=syzg_order_type',
    method: 'get'
  });
}
// 获取创建方式
export function getOrderSaleTypeOptions() {
  return withExtTenantIdRequest({
    url: '/common/api/dict/listByType?type=syzg_order_sale_type',
    method: 'get'
  });
}

// 导出商品销售数据分析
export function exportCommoditySaleStatisticExcel(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/orderStatistics/exportCommoditySaleStatisticExcel`,
    method: 'post',
    data,
    responseType: 'arraybuffer',
    timeout: 30 * 1000
  });
}

// 导出订单数据分析
export function exportOrderStatisticExcel(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/orderStatistics/exportOrderStatisticExcel`,
    method: 'post',
    data,
    responseType: 'arraybuffer',
    timeout: 30 * 1000
  });
}

// 导出分销商数据分析
export function exportDistributorStatisticExcel(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/orderStatistics/exportDistributorStatisticExcel`,
    method: 'post',
    data,
    responseType: 'arraybuffer',
    timeout: 30 * 1000
  });
}

// 首单记录分页列表
export function firstOrderRecordListPage(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/firstOrderRecord/listPage',
    method: 'post',
    data
  });
}

// 导出分销商数据分析
export function firstOrderRecordExportExcel(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/firstOrderRecord/exportExcel`,
    method: 'post',
    data,
    responseType: 'arraybuffer',
    timeout: 30 * 1000
  });
}

// 订单数据分析-增加条件查询下的总计
export function totalStatistic(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/orderStatistics/totalStatistic',
    method: 'post',
    data
  });
}

// 店铺top商品列表
export function listCommodityCodeSaleStatistic(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/orderStatistics/listCommodityCodeSaleStatistic',
    method: 'post',
    data
  });
}
// 导出店铺top商品列表
export function exportCommodityCodeSaleStatisticExcel(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/orderStatistics/exportCommodityCodeSaleStatisticExcel`,
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}

// 店铺概况-分销商采购分析列表按月导出
export function exportDistributorMonthPurchaseStatistic(data) {
  return withExtTenantIdRequest({
    url:
      '/soyoungzg/api/orderStatistics/exportDistributorMonthPurchaseStatistic',
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}

// 店铺概况-品牌销售分析-列表数据
export function listBrandSaleStatistic(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/orderCommodityStatistics/listBrandSaleStatistic',
    method: 'post',
    data
  });
}

// 店铺概况-品牌销售分析-增加条件查询下的总计
export function totalBrandSaleStatistic(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/orderStatistics/sumBrandSaleStatistic',
    method: 'post',
    data
  });
}

// 店铺概况-品牌销售分析-品牌商品采购列表
export function listBrandCommoditySaleStatistic(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/orderStatistics/listBrandCommoditySaleStatistic',
    method: 'post',
    data
  });
}

// 店铺概况-品牌销售分析-品牌商品采购列表-导出
export function exportBrandCommoditySaleStatistic(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/orderStatistics/exportBrandCommoditySaleStatistic`,
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}
// 店铺概况-品牌销售分析-分销商采购列表
export function listBrandDistributorSaleStatistic(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/orderStatistics/listBrandDistributorSaleStatistic',
    method: 'post',
    data
  });
}
// 店铺概况-品牌销售分析-分销商采购列表-导出
export function exportBrandDistributorSaleStatistic(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/orderStatistics/exportBrandDistributorSaleStatistic`,
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}

// 品牌销售分析列表导出
export function exportBrandData(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/orderStatistics/exportBrandSaleStatistic`,
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}

// 品牌销售分析列表 - 按月导出
export function exportBrandDataByMonth(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/orderCommodityStatistics/exportBrandSaleStatisticByMonth`,
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}

// 店铺概况-品牌销售分析-分销商采购排行商品列表
export function listDistributorCommoditySaleStatistic(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/orderStatistics/listDistributorCommoditySaleStatistic',
    method: 'post',
    data
  });
}
// 店铺概况-品牌销售分析-分销商采购排行商品列表-导出
export function exportDistributorCommoditySaleStatistic(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/orderStatistics/exportDistributorCommoditySaleStatistic`,
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}

// 店铺概况-品牌销售分析-商品分销商采购排行
export function listCommodityDistributorSaleStatistic(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/orderStatistics/listCommodityDistributorSaleStatistic',
    method: 'post',
    data
  });
}
// 店铺概况-品牌销售分析-商品分销商采购排行-导出
export function exportCommodityDistributorSaleStatistic(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/orderStatistics/exportCommodityDistributorSaleStatistic`,
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}

// 店铺概况-品牌销售数据分析-返利使用明细
export function listBrandUsedVirtualCreditDetail(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/orderStatistics/listBrandUsedVirtualCreditDetail',
    method: 'post',
    data
  });
}

// 店铺概况-品牌销售数据分析-返利使用明细汇总分析
export function sumBrandUsedVirtualCreditDetail(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/orderStatistics/sumBrandUsedVirtualCreditDetail',
    method: 'post',
    data
  });
}

// 店铺概况-品牌销售数据分析-返利使用明细-导出
export function exportBrandUsedVirtualCreditDetailExcel(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/orderStatistics/exportBrandUsedVirtualCreditDetailExcel`,
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}

// 店铺概况-经营目标
export function businessTargetStatisticsGetCurrentTarget() {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/businessTargetStatistics/getCurrentTarget',
    method: 'get'
  });
}

// 分销商明细 列表
export function listDistributorPurchase(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorDataAnalysis/listDistributorPurchase',
    method: 'post',
    data
  });
}
// 分销商明细 合计
export function summaryDistributorPurchase(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorDataAnalysis/summaryDistributorPurchase',
    method: 'post',
    data
  });
}
// 分销商明细 导出
export function exportDistributorExcelAsync(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorDataAnalysis/exportDistributorExcelAsync`,
    method: 'post',
    data
  });
}
