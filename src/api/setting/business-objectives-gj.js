import { withExtTenantIdRequest } from '@/utils/request';

// 品牌目标--分页
export function pageBrandTarget(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/internationalBusinessTarget/pageBrandTarget',
    method: 'post',
    data
  });
}

// 品牌目标--详情
export function queryBrandTarget(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/internationalBusinessTarget/queryBrandTarget',
    method: 'post',
    data
  });
}
// 品牌目标--创建
export function createBrandTarget(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/internationalBusinessTarget/createBrandTarget',
    method: 'post',
    data
  });
}
// 品牌目标--编辑
export function modifyBrandTarget(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/internationalBusinessTarget/modifyBrandTarget',
    method: 'post',
    data
  });
}

// 品牌目标--删除
export function deleteBrandTarget(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/internationalBusinessTarget/deleteBrandTarget',
    method: 'post',
    data
  });
}

// 经营目标设置-客户拓展/客户成长-列表
export function listTarget(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/internationalBusinessTarget/listTarget',
    method: 'post',
    data
  });
}

// 经营目标设置-客户拓展-获取目标
export function listDetailTarget(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/internationalBusinessTarget/listDetailTarget',
    method: 'post',
    data
  });
}

// 修改拓展-客户成长目标
export function updateTarget(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/internationalBusinessTarget/updateTarget',
    method: 'post',
    data
  });
}

// 新增拓展-客户成长目标
export function createTarget(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/internationalBusinessTarget/createTarget',
    method: 'post',
    data
  });
}

// 修改拓展-删除客户目标
export function deleteTarget(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/internationalBusinessTarget/deleteTarget',
    method: 'post',
    data
  });
}

//
export function internationalBusinessTargetListBrandIdHasSetTargets(year) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/internationalBusinessTarget/listBrandIdHasSetTargets?year=' + year
  });
}
