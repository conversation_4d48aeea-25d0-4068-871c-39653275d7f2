import { withExtTenantIdRequest } from '@/utils/request';

// 注销分销商分页列表
export function distributorlistLogout(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributor/listLogout',
    method: 'post',
    data
  });
}
// 注销审核
export function distributorListDecrypts(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributor/logoutAudit',
    method: 'post',
    data
  });
}
