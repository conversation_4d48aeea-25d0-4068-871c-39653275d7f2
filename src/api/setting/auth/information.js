import { withExtTenantIdRequest } from '@/utils/request';
export function getComponentPreAuth() {
  return withExtTenantIdRequest({
    url: '/wxopen-proxy/api/wxpf/getComponentPreAuth',
    method: 'get',
    timeout: 30000 // 请求超时时间
  });
}
export function authNotify(auth_code, expires_in, authType) {
  return withExtTenantIdRequest({
    url: '/wxopen-proxy/api/wxpf/authNotify',
    method: 'post',
    params: {
      auth_code,
      expires_in,
      authType
    }
  });
}

export function getMiniAuthInfo() {
  return withExtTenantIdRequest({
    url: '/wxopen-proxy/api/shopMiniProgram/getMiniAuthInfo',
    method: 'get'
  });
}
// 公众号授权基本信息
export function getAuthInfoByType(type) {
  return withExtTenantIdRequest({
    url: `/wxopen-proxy/api/authorizerInfo/getAuthInfoByType?type=${type}`,
    method: 'get'
  });
}

export function getAuditFailReason(authId) {
  return withExtTenantIdRequest({
    url: `/wxopen-proxy/api/miniSubmitAuditRecord/getAuditFailReason?id=${authId}`,
    method: 'get'
  });
}

export function create(authId) {
  return withExtTenantIdRequest({
    url: `/wxopen-proxy/api/miniSubmitAuditRecord/create?authId=${authId}`,
    method: 'post',
    // 5分钟超时
    timeout: '5 * 60 * 1000'
  });
}

// 获取小程序&H5授权详细信息
export function getAuthorizerDetailInfo(type) {
  return withExtTenantIdRequest({
    url: `/wxopen-proxy/api/authorizerInfo/getAuthorizerDetailInfo?type=${type}`,
    method: 'get'
  });
}

// 查询店铺小程序  关联的小程序列表
export function getListAssociativeMinis() {
  return withExtTenantIdRequest({
    url: `/wxopen-proxy/api/shopMiniProgram/listAssociativeMinis`,
    method: 'get'
  });
}

// 查询店铺小程序  关联的可用小程序列表
export function listAssociativeMinisEnable() {
  return withExtTenantIdRequest({
    url: `/wxopen-proxy/api/shopMiniProgram/listAssociativeMinisEnable`,
    method: 'get'
  });
}

// 修改小程序关联小程序列表并提审
export function updateAssociativeMinisSubmitAudit(data) {
  return withExtTenantIdRequest({
    url: `/wxopen-proxy/api/shopMiniProgram/updateAssociativeMinisSubmitAudit`,
    method: 'post',
    data,
    timeout: 15 * 1000
  });
}

// 通过该接口判断小程序是否有审核中的版本，获取提交审核时间；
export function getMiniAuditUnderVersionTime() {
  return withExtTenantIdRequest({
    url: `/wxopen-proxy-service/api/shopMiniProgram/getMiniAuditUnderVersionTime`,
    method: 'get'
  });
}
