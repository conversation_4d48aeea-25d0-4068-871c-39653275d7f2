import { withExtTenantIdRequest } from '@/utils/request';
// 获取经营目标列表
export function fetchList(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/businessTarget/listAll',
    method: 'post',
    data
  });
}

// 修改经营月度目标
export function updateMonthTarget(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/businessTarget/updateMonthTarget',
    method: 'post',
    data
  });
}

// 新增年度目标
export function createYearTarget(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/businessTarget/create',
    method: 'post',
    data
  });
}

//  获取品牌目标列表
export function getBrandTargetList(businessTargetId, period) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/brandTarget/get?businessTargetId=${businessTargetId}&period=${period}`,
    method: 'get'
  });
}

// 获取经营目标对象
export function getBusinessTarget(id) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/businessTarget/get?id=' + id,
    method: 'get'
  });
}

// 修改经营目标
export function updateBusinessTarget(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/businessTarget/update',
    method: 'post',
    data
  });
}

//  获取品牌多个月度目标列表
export function getListMonthAll(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandTarget/listMonthAll',
    method: 'post',
    data
  });
}
// 新增品牌目标
export function createBrandTarget(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandTarget/create',
    method: 'post',
    data
  });
}

//   修改品牌目标
export function updateBrandTarget(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandTarget/update',
    method: 'post',
    data
  });
}

// 批量设置品牌目标
export function batchBrandTarget(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandTarget/batchUpdate',
    method: 'post',
    data
  });
}

export function getGroupList(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/businessTargetStatistics/listMyGroups',
    method: 'post',
    data
  });
}
