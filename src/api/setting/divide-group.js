import { withExtTenantIdRequest } from '@/utils/request';

// 分组获取：树形结构
export function organization_tree(data={}) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/organization/tree',
    method: 'post',
    data
  });
}

// 分组获取：平铺结构
export function organization_listAll(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/organization/listAll',
    method: 'post',
    data
  });
}

// 分组创建
export function organization_create(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/organization/create',
    method: 'post',
    data
  });
}

// 分组详情
export function organization_get(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/organization/get?id=${id}`,
    method: 'get'
  });
}

// 分组编辑
export function organization_update(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/organization/update',
    method: 'post',
    data
  });
}

// 分组删除
export function organization_delete(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/organization/delete?id=${id}`,
    method: 'POST'
  });
}

// 分配列表
export function organizationAssign_list(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/organizationAssign/list',
    method: 'post',
    data
  });
}

// 分配列表查询所有
export function organizationAssign_listAll(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/organizationAssign/listAll',
    method: 'post',
    data
  });
}

// 分配创建
export function organizationAssign_create(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/organizationAssign/create',
    method: 'post',
    data
  });
}

// 分配详情
export function organizationAssign_get(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/organizationAssign/get?id=${id}`,
    method: 'get'
  });
}

// 分配删除
export function organizationAssign_delete(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/organizationAssign/delete?id=${id}`,
    method: 'POST'
  });
}

// 分配更新
export function organizationAssign_update(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/organizationAssign/update',
    method: 'post',
    data
  });
}

// 分配 - 查询全部人员
export function staff_listAll(data = {}) {
  return withExtTenantIdRequest({
    url: `/ocean/api/shopStaffApi/listAll`,
    method: 'POST',
    data
  });
}

// 分组获取专属顾问：树形结构
export function organization_customerServiceOrganizationTree(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/organization/customerServiceOrganizationTree',
    method: 'post',
    data
  });
}
