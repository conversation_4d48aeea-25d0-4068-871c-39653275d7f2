import { withExtTenantIdRequest } from '@/utils/request';

// 根据渠道拉取历史订单数据
export function orderCreateThirdOrderByChannels(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/order/createThirdOrderByChannels',
    method: 'post',
    data
  });
}

// 根据渠道拉取历史退款订单数据
export function refundOrderCreateThirdOrderByChannels(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/refundOrder/createThirdRefundOrderByChannels',
    method: 'post',
    data
  });
}

// 运营端 - 渠道分页列表
export function externalChannelPage(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/externalChannel/page',
    method: 'post',
    data
  });
}

// 运营端 - 创建渠道
export function externalChannelCreate(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/externalChannel/create',
    method: 'post',
    data
  });
}

// 运营端 - 手工创建渠道
export function externalChannelCreateManual(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/externalChannel/createManual',
    method: 'post',
    data
  });
}

// 运营端 - 修改渠道
export function externalChannelModify(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/externalChannel/modify',
    method: 'post',
    data
  });
}

// 运营端 - 订单业绩修改开关
export function externalChannelModifyChannelOrderSwitchTag(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/externalChannel/modifyChannelOrderSwitchTag',
    method: 'post',
    data
  });
}

// 运营端 - 渠道启用-停用切换
export function externalChannelModifyChannelStatus(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/externalChannel/modifyChannelStatus',
    method: 'post',
    data
  });
}

// 运营端 - 渠道详情
export function externalChannelQuery(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/externalChannel/query?id=${id}`,
    method: 'get'
  });
}
