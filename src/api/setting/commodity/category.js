import { withExtTenantIdRequest } from '@/utils/request';

export function list(data) {
  return withExtTenantIdRequest({
    url: '/commodity/api/category/list',
    method: 'post',
    data
  });
}

// 删除单个记录
export function handleDelete(id) {
  return withExtTenantIdRequest({
    url: `/commodity/api/category/delete?id=${id}`,
    method: 'post'
  });
}
// 删除多个记录
export function handleDeleteIds(data) {
  return withExtTenantIdRequest({
    url: '/commodity/api/category/deleteByIds',
    method: 'post',
    data
  });
}
// 新增
export function create(data) {
  return withExtTenantIdRequest({
    url: '/commodity/api/category/create',
    method: 'post',
    data
  });
}

// 更新
export function update(data) {
  return withExtTenantIdRequest({
    url: '/commodity/api/category/update',
    method: 'post',
    data
  });
}
// 通过id获取店铺的信息
export function getById(id) {
  return withExtTenantIdRequest({
    url: `/commodity/api/category/get?id=${id}`,
    method: 'get'
  });
}
// 排序
export function changeSort(id, type) {
  return withExtTenantIdRequest({
    url: `/commodity/api/category/changeSort/${id}/${type}`,
    method: 'post'
  });
}

// 改变隐藏状态
export function cancelHiddenCommodity(categoryId, status) {
  return withExtTenantIdRequest({
    url: `/commodity/api/category/changeCategoryHiddenStatus?categoryId=${categoryId}&status=${status}`,
    method: 'get'
  });
}
// 分组弹窗列表
export function listCategoryWithCommodity(data) {
  return withExtTenantIdRequest({
    url: '/commodity/api/category/listCategoryWithCommodity',
    method: 'post',
    data
  });
}
// 改分组-确认
export function batchUpdateCategory(data) {
  return withExtTenantIdRequest({
    url: '/commodity/api/commodity/batchUpdateCategory',
    method: 'post',
    data
  });
}
