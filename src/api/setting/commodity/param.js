import { withExtTenantIdRequest } from '@/utils/request';

// 获取全部的参数信息
export function list(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/commodityParam/list',
    method: 'post',
    data
  });
}

// 删除
export function handleDelete(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/commodityParam/delete?id=${id}`,
    method: 'post'
  });
}
// 新增
export function create(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/commodityParam/create',
    method: 'post',
    data
  });
}

// 更新
export function update(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/commodityParam/update',
    method: 'post',
    data
  });
}
// 通过id获取店铺的信息
export function getById(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/commodityParam/get?id=${id}`,
    method: 'get'
  });
}

// 排序
export function changeSort(id, type) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/commodityParam/changeSort/${id}/${type}`,
    method: 'post'
  });
}

// 获取商品中心字段值列表
export function commodityParamListCenterParam() {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/commodityParam/listCenterField`,
    method: 'post'
  });
}
