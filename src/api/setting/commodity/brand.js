import { withExtTenantIdRequest } from '@/utils/request';

// 获取全部的参数信息
export function listPage(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brand/list',
    method: 'post',
    data
  });
}

// 批量删除
export function handleDelete(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/zgBrand/deleteByIds`,
    method: 'post',
    data
  });
}
// 通过id删除品牌
export function deleteById(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/zgBrand/delete?id=${id}`,
    method: 'POST'
  });
}

// 新增
export function create(data) {
  return withExtTenantIdRequest({
    url: '/commodity/api/brand/create',
    method: 'post',
    data
  });
}

// 更新
export function update(data) {
  return withExtTenantIdRequest({
    url: '/commodity/api/brand/update',
    method: 'post',
    data
  });
}
// 通过id获取店铺的信息
export function getById(id) {
  return withExtTenantIdRequest({
    url: `/commodity/api/brand/get?id=${id}`,
    method: 'get'
  });
}

// 获取全部品牌名称 不分页
export function listAll(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brand/listAllBrandName',
    method: 'post',
    data
  });
}

// 获取所有业务分组
export function brandCategoryOptions(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brand/listBrandInfo',
    method: 'post',
    data
  });
}

// 获取获取业务分组对象
export function brandCategoryGet(id) {
  return withExtTenantIdRequest({
    url: `/commodity/api/brandCategory/get?id=${id}`,
    method: 'get'
  });
}

// 新增业务分组
export function brandCategoryCreate(data) {
  return withExtTenantIdRequest({
    url: '/commodity/api/brandCategory/create',
    method: 'post',
    data
  });
}

// 更新业务分组
export function brandCategoryUpdate(data) {
  return withExtTenantIdRequest({
    url: '/commodity/api/brandCategory/update',
    method: 'post',
    data
  });
}

// 删除业务分组
export function brandCategoryDelete(id) {
  return withExtTenantIdRequest({
    url: `/commodity/api/brandCategory/delete?id=${id}`,
    method: 'post'
  });
}

// 专属顾问列表
export function customerList(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/customerService/listAll',
    method: 'post',
    data
  });
}

// 获取热销品牌列表
export function hotBrand(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/zgBrand/listHotBrand',
    method: 'post',
    data
  });
}

// 删除热销品牌
export function brandHotCategoryDelete(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/zgBrand/deleteHotBrand?id=${id}`,
    method: 'post'
  });
}

// 删除全部热销品牌
export function brandAllHotCategoryDelete(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/zgBrand/deleteHotBrands',
    method: 'post',
    data
  });
}

// 查询全部品牌名称
export function selectAllBrand(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brand/listAllBrandName',
    method: 'post',
    data
  });
}

// 调整热销品牌排序
export function brandHotCategorySort(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/zgBrand/changeSort',
    method: 'post',
    data
  });
}
// 排序
export function changeSort(data) {
  return withExtTenantIdRequest({
    url: `/commodity/api/brand/updateSort`,
    method: 'post',
    data
  });
}
// 热销品牌添加
export function brandHotAdd(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/zgBrand/listUnSelectHotBrand',
    method: 'post',
    data
  });
}
// 置顶
export function changeSortTOTOP(data) {
  return withExtTenantIdRequest({
    url: `/commodity/api/brand/changeSort`,
    method: 'post',
    data
  });
}
// 保存畅销榜单微页面
export function hotBrandConfigSave(data) {
  return withExtTenantIdRequest({
    url: '/commodity/api/commodityAdConfig/save',
    method: 'post',
    data
  });
}

// 查询畅销榜单微页面
export function hotBrandConfigGet() {
  return withExtTenantIdRequest({
    url: `/commodity/api/commodityAdConfig/get?type=COMMODITY_SALE_VOLUME`,
    method: 'get'
  });
}

// 添加热销品牌
export function hotBrandAdd(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/zgBrand/createHotBrand',
    method: 'post',
    data
  });
}

// 获取全部品牌名称 不分页
export function brandListByCondition(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brand/listByCondition',
    method: 'post',
    data
  });
}
