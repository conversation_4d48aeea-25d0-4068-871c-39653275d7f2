import request, { withExtTenantIdRequest } from '@/utils/request';

export function listAllMenus(data = {}) {
  return request({
    url: '/user/api/menu/listAll',
    method: 'post',
    data
  });
}

export function listRoleMenus(roleId) {
  return request({
    url: '/user/api/role/menuAssignList',
    method: 'get',
    params: { roleId }
  });
}

export function create(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/role/create',
    method: 'post',
    data
  });
}

export function listPage(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/role/list',
    method: 'post',
    data
  });
}

export function getById(id) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/role/get',
    method: 'get',
    params: { id }
  });
}

export function update(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/role/update',
    method: 'post',
    data
  });
}
