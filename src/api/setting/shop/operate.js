import { withExtTenantIdRequest } from '@/utils/request';

export function listOperatePage(data) {
  return withExtTenantIdRequest({
    url: '/scrm-service/api/scrmTask/zgList',
    method: 'post',
    data
  });
}

export function getOperate(id) {
  return withExtTenantIdRequest({
    url: '/scrm-service/api/scrmTask/zgGet',
    method: 'get',
    params: { id }
  });
}

export function updateOperateStatus(data) {
  return withExtTenantIdRequest({
    url: '/scrm-service//api/scrmTask/updateStatus',
    method: 'post',
    data
  });
}

export function updateOperate(data) {
  return withExtTenantIdRequest({
    url: '/scrm-service//api/scrmTask/update',
    method: 'post',
    data
  });
}

export function deleteOperate(id) {
  return withExtTenantIdRequest({
    url: '/scrm-service//api/scrmTask/delete',
    method: 'post',
    params: { id }
  });
}

export function createOperate(data) {
  return withExtTenantIdRequest({
    url: '/scrm-service/api/scrmTask/create',
    method: 'post',
    data
  });
}
