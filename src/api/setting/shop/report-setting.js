import { withExtTenantIdRequest } from '@/utils/request';

export function create(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/salesTrendReportConfig/create',
    method: 'post',
    data
  });
}

export function get() {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/salesTrendReportConfig/getReportConfig'
  });
}
export function update(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/salesTrendReportConfig/update',
    method: 'post',
    data
  });
}
// 运营端-报表设置-选择商品-获取平台列表
export function listPlatform(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/platformCommoditySaleStatistics/listPlatform',
    method: 'post',
    data
  });
}
// 运营端-报表设置-选择商品-获取品牌列表
export function listBrand(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/platformCommoditySaleStatistics/listBrand',
    method: 'post',
    data
  });
}
// 运营端-报表设置-选择商品-获取品类列表
export function listCategory(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/platformCommoditySaleStatistics/listCategory',
    method: 'post',
    data
  });
}
// 运营端-报表设置-选择商品-获取平台商品列表
export function listCommodityPage(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/platformCommoditySaleStatistics/listCommodityPage',
    method: 'post',
    data
  });
}
// 运营端-报表设置-预览平台商品列表
export function listCommodityView(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/platformCommoditySaleStatistics/listCommodityView',
    method: 'post',
    data
  });
}
