import { withExtTenantIdRequest } from '@/utils/request';
import store from '@/store';

export function listPage(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/staff/list',
    method: 'post',
    data
  });
}
export function listPageAll(data) {
  return withExtTenantIdRequest({
    url: '/ocean/api/shopStaff/listAll',
    method: 'post',
    data
  });
}

export function deleteById(id) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/staff/delete',
    method: 'post',
    params: { id }
  });
}

export function update(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/staff/update',
    method: 'post',
    data
  });
}

export function create(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/staff/create',
    method: 'post',
    data
  });
}

export function listAllRoles() {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/role/listAll',
    method: 'post',
    data: {
      tenantId: process.env.VUE_APP_TENANT_ID,
      extTenantId: store.getters['shop/shopInfo'].id
    }
  });
}

//  按分组关联-列表
export function getListCsGroup(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/staff/listCsGroup',
    method: 'post',
    data
  });
}
// 按人员关联-列表
export function getListCsPage(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/staff/listCsPage',
    method: 'post',
    data
  });
}

export function getById(id) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/staff/get',
    method: 'get',
    params: { id }
  });
}

// 分组设置
export function getGroupList(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/staff/listGroupPage',
    method: 'post',
    data
  });
}

// 获取分组的专属顾问人数
export function staffGetCsNumber(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/staff/getCsNumber',
    method: 'post',
    data
  });
}

