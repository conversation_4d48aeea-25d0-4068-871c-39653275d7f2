import { withExtTenantIdRequest } from '@/utils/request';

export function list(data) {
  return withExtTenantIdRequest({
    url: '/newretail/api/shopAddress/listPage',
    method: 'post',
    data
  });
}

// 删除单个记录
export function handleDelete(id) {
  return withExtTenantIdRequest({
    url: `/newretail/api/shopAddress/delete?id=${id}`,
    method: 'post'
  });
}
// 删除多个记录
export function handleDeleteIds(data) {
  return withExtTenantIdRequest({
    url: '/newretail/api/shopAddress/deleteByIds',
    method: 'post',
    data
  });
}
// 新增
export function create(data) {
  return withExtTenantIdRequest({
    url: '/newretail/api/shopAddress/create',
    method: 'post',
    data
  });
}

// 更新
export function update(data) {
  return withExtTenantIdRequest({
    url: '/newretail/api/shopAddress/update',
    method: 'post',
    data
  });
}
// 通过id获取店铺的信息
export function getById(id) {
  return withExtTenantIdRequest({
    url: `/newretail/api/shopAddress/get?id=${id}`,
    method: 'get'
  });
}
// 排序
export function changeSort(id, type) {
  return withExtTenantIdRequest({
    url: `/newretail/api/category/changeSort/${id}/${type}`,
    method: 'post'
  });
}
