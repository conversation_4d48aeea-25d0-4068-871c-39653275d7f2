import { withExtTenantIdRequest } from '@/utils/request';

// 加密
export function listEncrypts(content) {
  return withExtTenantIdRequest({
    url: '/soyoungzg//api/zgSecret/listEncrypts?content=' + content
  });
}
// 解密
export function listDecrypts(content) {
  return withExtTenantIdRequest({
    url: '/soyoungzg//api/zgSecret/listDecrypts?content=' + content
  });
}
// 百应加密
export function byaiMobileEncrypt(mobile) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/byai/mobileEncrypt?mobile=' + mobile,
  });
}
// PC-标签同步到企微配置列表
export function sysQywxTagListAll(data={}) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/sysQywxTag/listAll',
    method: 'post',
    data
  });
}
// PC-标签同步到企微配置-切换是否同步
export function sysQywxTagChangeSync(data={}) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/sysQywxTag/changeSync',
    method: 'post',
    data
  });
}
