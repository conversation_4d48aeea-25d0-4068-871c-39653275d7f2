import { withExtTenantIdRequest } from '@/utils/request';

// 申请分页记录开票列表
export function listPage(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/invoiceApplyRecord/listPage',
    method: 'post',
    data
  });
}

// 审批
export function audit(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/invoiceApplyRecord/audit?id=${id}`,
    method: 'get'
  });
}
// 批量审批
export function auditList(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/invoiceApplyRecord/auditList',
    method: 'post',
    data
  });
}

// 驳回
export function reject(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/invoiceApplyRecord/reject',
    method: 'post',
    data
  });
}

// 物流
export function updateDeliveryInfo(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/invoiceApplyRecord/updateDeliveryInfo',
    method: 'post',
    data
  });
}

// 订单详情
export function getOrderDetail(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/invoiceApplyRecord/getOrderDetail?id=${id}`,
    method: 'get'
  });
}

// 导出开票列表
export function exportExcel(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/invoiceApplyRecord/exportExcel',
    method: 'post',
    data,
    responseType: 'arraybuffer',
    timeout: 30 * 1000
  });
}
// 获取开票详情
export function getById(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/invoiceApplyRecord/getForPc?id=${id}`,
    method: 'get'
  });
}
// 渠道名称字典  发票开票状态
export function fetchChannel() {
  return withExtTenantIdRequest({
    url: '/common/api/dict/listByType?type=invoice_apply_record_status',
    method: 'get'
  });
}

// 查看更多发票 分页接口
export function getOrderDetailByPage(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/invoiceApplyRecord/getOrderDetailByPage',
    method: 'post',
    data
  });
}

// 修改开票备注
export function invoiceApplyRecordUpdateRemarks(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/invoiceApplyRecord/updateRemarks',
    method: 'post',
    data
  });
}
