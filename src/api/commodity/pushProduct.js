import { withExtTenantIdRequest } from '@/utils/request';

// 推品管理列表
export function pushProductManageList(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/recommendPlan/list',
    method: 'post',
    data
  });
}

// 推品管理 新增
export function pushProductManageCreate(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/recommendPlan/create',
    method: 'post',
    data
  });
}

// 推品管理 删除
export function pushProductManageDelete(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/recommendPlan/delete',
    method: 'post',
    params: data
  });
}

// 推品管理 修改
export function pushProductManageUpdate(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/recommendPlan/update',
    method: 'post',
    data
  });
}

// 推品管理 获取
export function pushProductManageGet(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/recommendPlan/get',
    method: 'get',
    params: data
  });
}

// 推品管理 清单启用
export function pushProductManageEnable(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/recommendPlan/enable',
    method: 'post',
    params: data
  });
}

// 推品管理 清单禁用
export function pushProductManageDisable(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/recommendPlan/disable',
    method: 'post',
    params: data
  });
}

// 推品管理 清单转为公共
export function pushProductManageToPublic(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/recommendPlan/toPublic',
    method: 'post',
    params: data
  });
}

// 推品管理 清单转为私人
export function pushProductManagePublic(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/recommendPlan/publish',
    method: 'post',
    params: data
  });
}

// 商品管理 sku级别
export function commoditySkuList(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/zgCommodity/listSkuPage',
    method: 'post',
    data
  });
}

// 商品管理 年框
export function commoditySkuListForYearly(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/zgCommodity/listSkuPageYearly',
    method: 'post',
    data
  });
}


// 推品统计列表
export function pushProductCensusList(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/recommendPlan/listStatisticPage',
    method: 'post',
    data
  });
}

// 推品管理 商品启用
export function pushProductManageShow(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/recommendPlan/show',
    method: 'post',
    data
  });
}

// 推品管理 商品禁用
export function pushProductManageHide(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/recommendPlan/hide',
    method: 'post',
    data
  });
}
