import { withExtTenantIdRequest } from '@/utils/request';

// 获取全部的参数信息
export function list(data) {
  return withExtTenantIdRequest({
    url: '/commodity/api/commodityLabel/listAll',
    method: 'post',
    data
  });
}

// 删除
export function handleDelete(id) {
  return withExtTenantIdRequest({
    url: `/commodity/api/commodityLabel/delete?id=${id}`,
    method: 'post'
  });
}
// 新增
export function createCommodityLabel(data) {
  return withExtTenantIdRequest({
    url: '/commodity/api/commodityLabel/create',
    method: 'post',
    data
  });
}

// 更新
export function updateCommodityLabel(data) {
  return withExtTenantIdRequest({
    url: '/commodity/api/commodityLabel/update',
    method: 'post',
    data
  });
}
// 设置为新品
export function enableNewProductLabel(id) {
  return withExtTenantIdRequest({
    url: `/commodity/api/commodityLabel/enableNewProductLabel?id=${id}`,
    method: 'post'
  });
}
// 设置为非新品
export function disableNewProductLabel(id) {
  return withExtTenantIdRequest({
    url: `/commodity/api/commodityLabel/disableNewProductLabel?id=${id}`,
    method: 'post'
  });
}
// 排序
export function changeSort(data) {
  return withExtTenantIdRequest({
    url: `/commodity/api/commodityLabel/changeSort`,
    method: 'post',
    data
  });
}
