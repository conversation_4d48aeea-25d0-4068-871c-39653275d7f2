import { withExtTenantIdRequest } from '@/utils/request';

// 获取商品类型
export function fetchCommodityType() {
  return withExtTenantIdRequest({
    url: '/common/api/dict/listByType?type=commodity_type',
    method: 'get'
  });
}
// 封装交易中心-商品服务-新增商品
export function add(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg//api/tradeCenterCommodity/create', //  '/commodity/api/commodity/create',
    method: 'post',
    data
  });
}

// 封装交易中心-商品服务-查看商品
export function getById(id) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/tradeCenterCommodity/get', // '/commodity/api/commodity/get',
    method: 'get',
    params: {
      id
    }
  });
}

export function cancelHiddenCommodity(id) {
  return withExtTenantIdRequest({
    url: '/commodity-service/api/commodity/cancelHiddenCommodity',
    method: 'get',
    params: {
      id
    }
  });
}

// 批量取消隐藏商品
export function batchCancelHiddenCommodity(data) {
  return withExtTenantIdRequest({
    url: '/commodity/api/commodity/batchCancelHiddenCommodity',
    method: 'post',
    data
  });
}

// 批量隐藏商品
export function batchHiddenCommodity(data) {
  return withExtTenantIdRequest({
    url: '/commodity/api/commodity/batchHiddenCommodity',
    method: 'post',
    data
  });
}
// 封装交易中心-商品服务-修改商品
export function update(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/tradeCenterCommodity/update', // '/commodity/api/commodity/update',
    method: 'post',
    data
  });
}

export function listCategories() {
  return withExtTenantIdRequest({
    url: '/commodity/api/category/listAll',
    method: 'post'
  });
}

export function listCommodityParams() {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/commodityParam/listAll',
    method: 'post',
    data: {}
  });
}
// 根据优惠券id获取优惠券信息
export function getByCouponId(id) {
  return withExtTenantIdRequest({
    url: `/marketing/api/coupon/get?id=${id}`,
    method: 'get'
  });
}
// 自动上架
export function autoOnShelve(data) {
  return withExtTenantIdRequest({
    url: '/commodity-service/api/commodity/autoOnShelve',
    method: 'post',
    data
  });
}

// 自动下架
export function autoOffShelve(data) {
  return withExtTenantIdRequest({
    url: '/commodity-service/api/commodity/autoOffShelve',
    method: 'post',
    data
  });
}
// 取消商品自动下架
export function cancelAutoOffShelve(id) {
  return withExtTenantIdRequest({
    url: '/commodity-service/api/commodity/cancelAutoOffShelve',
    method: 'get',
    params: {
      id
    }
  });
}

// 取消商品自动上架
export function cancelAutoOnShelve(id) {
  return withExtTenantIdRequest({
    url: '/commodity-service/api/commodity/cancelAutoOnShelve',
    method: 'get',
    params: {
      id
    }
  });
}

// 判断商品是否参与营销活动
export function validationByCommodityId(id) {
  return withExtTenantIdRequest({
    url: '/marketing/api/commodityActivityVal/validationByCommodityId',
    params: { commodityId: id }
  });
}
// 追加库存
export function appendStock(data) {
  return withExtTenantIdRequest({
    url: '/commodity/api/stock/updateAddStock',
    method: 'post',
    data
  });
}

// 追加销量
export function updateAddSalesVolume(data) {
  return withExtTenantIdRequest({
    url: '/commodity/api/commodity/updateAddSalesVolume',
    method: 'post',
    data
  });
}

// 修改sku价格
export function updatePrice(data) {
  return withExtTenantIdRequest({
    url: '/commodity/api/commodity/updatePrice',
    method: 'post',
    data
  });
}
// 移到回收站
export function disableCommodity(id) {
  return withExtTenantIdRequest({
    url: `/commodity/api/commodity/disableCommodity?id=${id}`,
    method: 'get'
  });
}
// 批量移到回收站
export function batchDisableCommodity(data) {
  return withExtTenantIdRequest({
    url: `/commodity/api/commodity/batchDisableCommodity`,
    method: 'post',
    data
  });
}
// 从回收站恢复
export function enableCommodity(id) {
  return withExtTenantIdRequest({
    url: `/commodity/api/commodity/enableCommodity?id=${id}`,
    method: 'get'
  });
}
// 批量从回收站恢复
export function batchEnableCommodity(data) {
  return withExtTenantIdRequest({
    url: `/commodity/api/commodity/batchEnableCommodity`,
    method: 'post',
    data
  });
}
// 商品回收站-导出
export function newExportByCondition(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/zgCommodity/exportByCondition',
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}
// 获取商品标签
export function getCommodityLabel() {
  return withExtTenantIdRequest({
    url: `/commodity/api/commodityLabel/listAll`,
    method: 'post',
    data: {}
  });
}

// 商品列表-修改备注
export function fastUpdateCommodity(data) {
  return withExtTenantIdRequest({
    url: `/commodity/api/commodity/fastUpdateCommodity`,
    method: 'post',
    data
  });
}

// 商品批量导入
export function zgCommodityCreateInBatch(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/zgCommodity/createInBatch`,
    method: 'post',
    data
  });
}

// 商品-验证
export function zgCommodityValidInBatch(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/zgCommodity/validInBatch`,
    method: 'post',
    data
  });
}

// 简易商品列表
export function commodityListBrief(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/commodity/listBrief',
    method: 'post',
    data
  });
}

// 品牌档案-分销商品-同步商品信息到商品管理
export function commodityListByCreateCommodity(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/commodity/listByCreateCommodity',
    method: 'post',
    data
  });
}

// 商品管理-商品关联活动信息
export function commodityGetRelationActivityInfo(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/tradeCenterCommodity/getRelationActivityInfo',
    method: 'post',
    data
  });
}

// 商品列表
export function commoditySearchList(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/commodity/searchList',
    method: 'post',
    data
  });
}
