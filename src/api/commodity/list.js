import { withExtTenantIdRequest } from '@/utils/request';

export function goodsType() {
  return withExtTenantIdRequest({
    url: '/commodity/api/category/listAll',
    method: 'post'
  });
}
// 获取全部的参数信息
export function list(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/zgCommodity/listPage',
    method: 'post',
    data
  });
}

export function deleteByIds(data) {
  return withExtTenantIdRequest({
    url: '/commodity/api/commodity/deleteByIds',
    method: 'post',
    data
  });
}

export function updateSaleStatus(type, data) {
  return withExtTenantIdRequest({
    url: `/commodity/api/commodity/updateSaleStatus?oper=${type}`,
    method: 'post',
    data
  });
}
// 导出
export function exportExcel(data) {
  return withExtTenantIdRequest({
    url: `/commodity-service/api/commodity/export?status=${data}`,
    method: 'post',
    responseType: 'arraybuffer',
    timeout: 30 * 1000
  });
}

// 按搜索条件导出
export function exportByCondition(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/zgCommodity/exportByCondition`,
    method: 'post',
    data,
    responseType: 'arraybuffer',
    timeout: 30 * 1000
  });
}
// 排序
export function changeSort(data) {
  return withExtTenantIdRequest({
    url: `/commodity/api/commodity/updateSort`,
    method: 'post',
    data
  });
}
// 置顶
export function changeSortTOTOP(id) {
  return withExtTenantIdRequest({
    url: `/commodity/api/commodity/changeSort/${id}/TOTOP`,
    method: 'post'
  });
}

// 预览  移动商品详情接口
export function commodityDetail(goodsId) {
  return withExtTenantIdRequest({
    url: `/commodity/api/commodity/detail?id=${goodsId}`,
    method: 'get'
  });
}

// 预览  移动商品详情活动接口
export function activityListActivityRuleForCommodity(id) {
  return withExtTenantIdRequest({
    url: `/marketing/api/activity/listActivityRuleForCommodity?commodityId=${id}`,
    method: 'post'
  });
}

// 预览   根据商品ID查询推荐商品
export function commodityListRecommendById(id) {
  return withExtTenantIdRequest({
    url: `/commodity/api/commodity/listRecommendById?id=${id}`,
    method: 'post'
  });
}

// 商品详情页-查询品牌详情
export function zgBrandGetBrandById(brandId) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/zgBrand/getBrandById?id=${brandId}`,
    method: 'get'
  });
}

// 获取全部的参数信息
export function commodityEsList(data) {
  return withExtTenantIdRequest({
    url: '/commodity/api/commodityEs/list',
    method: 'post',
    data
  });
}
