import { withExtTenantIdRequest } from '@/utils/request';

// 获取全部的订单信息
export function list(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/commodityPriceMonitor/listBreakDetail',
    method: 'post',
    data
  });
}

export function fetchStatistic(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/commodityPriceMonitor/listBreakDetailStatistic',
    method: 'post',
    data
  });
}

export function fetchSum(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/commodityPriceMonitor/listBreakDetailSum',
    method: 'post',
    data
  });
}
export function fetchPlatformOptions(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/commodityPriceMonitor/listPlatform',
    method: 'post',
    data
  });
}
export function handleBreak(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/commodityPriceMonitor/handleBreak',
    method: 'post',
    data
  });
}

export function exportData(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/commodityPriceMonitor/exportBreakDetail',
    method: 'post',
    data,
    responseType: 'arraybuffer',
    timeout: 30 * 1000
  });
}
