import { withExtTenantIdRequest } from '@/utils/request';

// 获取全部的订单信息
export function list(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/commodityPriceMonitor/list',
    method: 'post',
    data
  });
}

export function fetchStatistic(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/commodityPriceMonitor/listStatistic',
    method: 'post',
    data
  });
}

export function exportData(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/commodityPriceMonitor/export',
    method: 'post',
    data,
    responseType: 'arraybuffer',
    timeout: 30 * 1000
  });
}
