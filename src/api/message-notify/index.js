import { withExtTenantIdRequest } from '@/utils/request';
/**
 * 消息通知
 * @param {*} data
 */

// 消息管理-消息列表
export function listMessagePage(data) {
  return withExtTenantIdRequest({
    url: '/sms/api/message/listPage',
    method: 'post',
    data
  });
}

// 消息管理-分销商黑名单列表
export function listDistributorBlackList(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/message/listDistributorBlackList',
    method: 'post',
    data
  });
}

// 消息管理-员工推送名单列表
export function listShopStaffWhiteList(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/message/listShopStaffWhiteList',
    method: 'post',
    data
  });
}

// 消息管理-增加分销商黑名单
export function addDistributorBlackList(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/message/addDistributorBlackList',
    method: 'post',
    data
  });
}

// 消息管理-删除分销商黑名单
export function deleteDistributorBlackList(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/message/deleteDistributorBlackList',
    method: 'post',
    data
  });
}

// 消息管理-增加员工推送名单
export function addShopStaffWhiteList(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/message/addShopStaffWhiteList',
    method: 'post',
    data
  });
}

// 消息管理-删除员工推送名单
export function deleteShopStaffWhiteList(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/message/deleteShopStaffWhiteList',
    method: 'post',
    data
  });
}
