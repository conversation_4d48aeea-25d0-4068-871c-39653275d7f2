import request, { withExtTenantIdRequest } from '@/utils/request';

export function create(data) {
  return request({
    url: '/@DEFAULT_SERVICE/api/shop/create',
    method: 'post',
    data
  });
}
export function update(data) {
  return request({
    url: '/@DEFAULT_SERVICE/api/shop/update',
    method: 'post',
    data
  });
}
export function listMyShop(data) {
  return request({
    url: '/@DEFAULT_SERVICE/api/shop/listMyShop',
    method: 'post',
    data
  });
}
export function getById(id) {
  return request({
    url: `/@DEFAULT_SERVICE/api/shop/get?id=${id}`,
    method: 'get'
  });
}
export function getShopBasicInfo(id) {
  return request({
    url: `/newretail/api/shop/getBasicInfo?id=${id}`,
    method: 'get'
  });
}
// 类型数据字典获取
export function fetchShopType() {
  return request({
    url: '/common/api/dict/listByType?type=newretail_shop_type',
    method: 'get'
  });
}
export default {
  // 以下3个自定义列的接口不允许删除，微前端主应用会调用
  // 获取用户自定义列配置
  userPageConfigListByTypeAndPage(type, pageName) {
    return withExtTenantIdRequest({
      url: '/user/api/userPageConfig/listByTypeAndPage',
      method: 'get',
      params: { type, pageName },
    });
  },
  // 创建用户自定义列配置
  userPageConfigCreate(obj) {
    return withExtTenantIdRequest({
      url: '/user/api/userPageConfig/create',
      method: 'post',
      data: obj,
    });
  },
  // 修改用户自定义列配置
  userPageConfigUpdate(obj) {
    return withExtTenantIdRequest({
      url: '/user/api/userPageConfig/update',
      method: 'post',
      data: obj,
    });
  },
}
