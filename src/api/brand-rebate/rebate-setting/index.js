import { withExtTenantIdRequest } from '@/utils/request';
/**
 * 返利活动：设置列表
 * @param {*} data
 */
export function listActivity(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackActivity/list',
    method: 'post',
    data
  });
}

// 获取全部品牌
export function listBrand(data = {}) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackActivity/listAllBrand',
    method: 'post',
    data
  });
}
// 获取全部业务分组
export function listBrandCategory(data = {}) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackActivity/listAllBrandCategory',
    method: 'post',
    data
  });
}

/**
 *获取返利商品
 * @param {} data
 */
export function listActivityCommodity(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackActivityCommodity/list',
    method: 'post',
    data
  });
}

/**
 *获取返利商品
 * @param {} data
 */
export function listActivityPeriod(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackPeriod/list',
    method: 'post',
    data
  });
}

export function createActivityPeriod(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackPeriod/create',
    method: 'post',
    data
  });
}

// 禁用
export function disableActivityPeriod(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/creditBackPeriod/disable?id=${id}`,
    method: 'post'
  });
}

// 停用返点活动时间
export function enableActivityPeriod(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/creditBackPeriod/enable?id=${id}`,
    method: 'post'
  });
}

// 删除返点活动时间
export function deleteActivityPeriod(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/creditBackPeriod/delete?id=${id}`,
    method: 'post'
  });
}

/**
 *获取返利商品
 * @param {} data
 */
export function listRule(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackThresholdLog/list',
    method: 'post',
    data
  });
}

/**
 *获取返利商品
 * @param {} data
 */
export function listActivitySnapshoot(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackActivitySnapshoot/listPage',
    method: 'post',
    data
  });
}

// 新增返利活动规则
export function create(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackActivity/create',
    method: 'post',
    data
  });
}

// 获取返利活动详情
export function update(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackActivity/update',
    method: 'post',
    data
  });
}

// 获取返利活动详情
export function getDetail(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/creditBackActivity/get?id=${id}`,
    method: 'get'
  });
}
