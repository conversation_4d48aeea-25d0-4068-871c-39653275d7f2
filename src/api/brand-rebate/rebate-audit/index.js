import { withExtTenantIdRequest } from '@/utils/request';
/**
 * 返利活动：返利审核
 * @param {*} data
 */

// 返利审核-调整订单-列表上的汇总数据
export function getDistributorRecord(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/creditBackDistributorRecord/getDistributorRecord?id=${data}`,
    method: 'post'
  });
}
// 返利审核-调整订单/采购明细-列表
export function creditBackOrderList(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackOrder/list',
    method: 'post',
    data
  });
}
// 返利审核-调整订单/采购明细-导出
export function creditBackOrderExportExcel(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackOrder/exportExcel',
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}
// 返利审核-调整订单-调整返利-列表
export function creditBackOrderDetailList(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackOrderDetail/list',
    method: 'post',
    data
  });
}
// 返利审核-调整订单-调整返利-最大调整金额
export function calculateMaxAmount(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackOrderDetail/calculateMaxAmount',
    method: 'post',
    data
  });
}
// 返利审核-调整订单-调整返利-调整金额
export function changeAmount(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackOrderDetail/changeAmount',
    method: 'post',
    data
  });
}
// 返利审核-调整订单-调整返利-调整参与返利
export function creditBackOrderDetailEnable(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/creditBackOrderDetail/enable?id=${data}`,
    method: 'post'
  });
}
// 返利审核-调整订单-调整返利-调整不参与返利
export function creditBackOrderDetailDisable(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/creditBackOrderDetail/disable?id=${data}`,
    method: 'post'
  });
}

// 返利审核奖励调整明细
export function listAdjustDetail(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackAdjustDetail/list',
    method: 'post',
    data
  });
}

// 导出返利审核奖励调整明细
export function exportAdjustDetail(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/creditBackAdjustDetail/exportExcel`,
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}

// 返利审核奖金调整统计
export function getSumAdjustDetail(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackAdjustDetail/getOrderSummary',
    method: 'post',
    data
  });
}

// 返利审核奖金调整-调整明细统计
export function getSumAdjustCommodityDetail(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/creditBackAdjustDetail/getOrderDetailSummary?creditBackOrderId=${data}`,
    method: 'GET'
  });
}

// 返利审核奖励调整商品明细
export function listAdjustCommodityDetail(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackAdjustDetail/listAdjustCommodity',
    method: 'post',
    data
  });
}

// 返利商品列表
export function listRebateCommodity(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackActivityCommodity/list',
    method: 'post',
    data
  });
}

// 返利活动-详情简要信息
export function getRebateActivityBrief(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/creditBackActivitySnapshoot/getBrief?id=${id}`,
    method: 'get'
  });
}

export function listActivitySnapshoot(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackActivitySnapshoot/listPage',
    method: 'post',
    data
  });
}

// 禁用
export function disableCommodity(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/creditBackActivityCommodity/disable?id=${id}`,
    method: 'post'
  });
}

// 停用返点活动时间
export function enableCommodity(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/creditBackActivityCommodity/enable?id=${id}`,
    method: 'post'
  });
}

// 审核返利（月度/季度）-列表信息
export function creditBackDistributorRecordListPage(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackDistributorRecord/listPage',
    method: 'post',
    data
  });
}
// 获取全部品牌
export function listBrand(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brand/listAllBrandName',
    method: 'post',
    data
  });
}

// 审核状态字典
export function fetChcreditBackStatusOptions() {
  return withExtTenantIdRequest({
    url: `/common/api/dict/listByType?type=distributor_credit_back_status`,
    method: 'get'
  });
}

// 审核返利（月度/季度）-列表信息-导出
export function exportExcelCreditBackDistributorRecord(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/creditBackDistributorRecord/exportExcel`,
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}

// 返利审核-审核详情
export function getDetailCreditBackActivitySnapshoot(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/creditBackActivitySnapshoot/getDetail?id=${id}`,
    method: 'get'
  });
}

// 返利审核-返利发放-审核详情
export function getIssueDetailCreditBackActivitySnapshoot(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/creditBackActivitySnapshoot/getIssueDetail?id=${id}`,
    method: 'get'
  });
}

// 返利审核-发放/不发放
export function auditCreditBackDistributorRecord(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackDistributorRecord/audit',
    method: 'post',
    data
  });
}
// 获取未完成订单列表
export function listOrderUnfinishStatus(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/creditBackOrder/listOrderUnfinishStatus?recordId=${data}`,
    method: 'post'
  });
}
// 获取调整返利列表汇总信息
export function getSnapshotOrder(orderId, activitySnapshootId) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/creditBackActivitySnapshoot/getSnapshotOrder?orderId=${orderId}&activitySnapshootId=${activitySnapshootId}`,
    method: 'get'
  });
}

// 重新计算
export function reCalculate(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/creditBackDistributorRecord/reCalculate`,
    method: 'post',
    data
  });
}

// 重新计算全部
export function reCalculateAll(activityId) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/creditBackDistributorRecord/reCalculateAll?activityId=${activityId}`,
    method: 'post'
  });
}

// 品牌CP审核-通过-不通过
export function creditBackDistributorRecordCpAudit(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackDistributorRecord/cpAudit',
    method: 'post',
    data
  });
}

// 获取creditBackDistributorRecordAuditLog分页列表
export function creditBackDistributorRecordAuditLogListPage(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackDistributorRecordAuditLog/listPage',
    method: 'post',
    data
  });
}
