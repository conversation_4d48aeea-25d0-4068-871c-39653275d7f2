import { withExtTenantIdRequest } from '@/utils/request';
/**
 * 返利活动：返利数据
 * @param {*} data
 */

// 返利发放明细
export function listRebateIssueDetail(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackStatistics/listIssueDetail',
    method: 'post',
    data
  });
}

// 导出返利发放明细
export function exportRebateIssueDetail(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/creditBackStatistics/exportIssueDetail`,
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}

// 月度/季度返利数据总计
export function getSumRebateDetailData(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackStatistics/getSummaryByPeriod',
    method: 'post',
    data
  });
}

// 返利充值明细
export function listRebateRechageDetail(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackStatistics/listRechargeByMonth',
    method: 'post',
    data
  });
}

// 导出返利充值明细
export function exportRebateRechageDetail(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/creditBackStatistics/exportRechargeByMonth`,
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}

// 返利使用明细
export function listRebateUseDetail(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackStatistics/listUsedByMonth',
    method: 'post',
    data
  });
}

// 导出返利使用明细
export function exportRebateUseDetail(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/creditBackStatistics/exportUsedByMonth`,
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}

// 分销商返利数据统计
export function getSumDistributorRebateData(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackStatistics/getSummaryByDistributor',
    method: 'post',
    data
  });
}

// 分销商返利发放明细
export function listDistributorIssueDetail(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackStatistics/listDetailByDistributor',
    method: 'post',
    data
  });
}

// 分销商返利发放明细汇总
export function getIssueByDistributorSum(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackStatistics/getIssueByDistributorSum',
    method: 'post',
    data
  });
}

// 导出分销商返利发放明细
export function exportDistributorIssueDetail(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/creditBackStatistics/exportDetailByDistributor`,
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}

// 活动类型数据字典
export function fetchChannelOptions() {
  return withExtTenantIdRequest({
    url: '/common/api/dict/listByType?type=credit_back_statistics_activity_type',
    method: 'get'
  });
}

// 分销商返利使用明细
export function listDistributorUseDetail(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackStatistics/listUsedByDistributor',
    method: 'post',
    data
  });
}

// 导出分销商返利使用明细
export function exportDistributorUseDetail(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/creditBackStatistics/exportUsedByDistributor`,
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}

// 分销商返利充值明细
export function listDistributorRechargeDetail(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackStatistics/listRechargeByDistributor',
    method: 'post',
    data
  });
}

// 导出分销商返利充值明细
export function exportDistributorRechargeDetail(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/creditBackStatistics/exportRechargeByDistributor`,
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}

// 品牌返利数据统计
export function getSumBrandRebateData(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackStatistics/getSummaryByBrand',
    method: 'post',
    data
  });
}

// 品牌返利发放明细
export function listBrandIssueDetail(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackStatistics/listIssueByBrand',
    method: 'post',
    data
  });
}

// 导出分销商返利发放明细
export function exportBrandIssueDetail(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/creditBackStatistics/exportIssueByBrand`,
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}

// 品牌返利使用明细
export function listBrandUseDetail(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackStatistics/listUsedByBrand',
    method: 'post',
    data
  });
}

// 导出分销商返利使用明细
export function exportBrandUseDetail(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/creditBackStatistics/exportUsedByBrand`,
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}

// 返利数据-月度返利
export function listByMonth(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackStatistics/listByMonth',
    method: 'post',
    data
  });
}
// 返利数据-月度返利-导出
export function exportByMonth(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackStatistics/exportByMonth',
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}
// 返利数据-季度返利
export function listByQuarter(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackStatistics/listByQuarter',
    method: 'post',
    data
  });
}
// 返利数据-季度返利-导出
export function exportByQuarter(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackStatistics/exportByQuarter',
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}
// 返利数据-分销商返利
export function listByDistributor(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackStatistics/listByDistributor',
    method: 'post',
    data
  });
}
// 返利数据-分销商返利-导出
export function exportByDistributor(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackStatistics/exportByDistributor',
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}
// 返利数据-品牌返利
export function listByBrand(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackStatistics/listByBrand',
    method: 'post',
    data
  });
}
// 返利数据-品牌返利-导出
export function exportByBrand(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackStatistics/exportByBrand',
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}
// 返利数据-列表数据概况
export function getSummary() {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackStatistics/getSummary',
    method: 'post'
  });
}
// 返利数据-单品返利
export function listByCommodity(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackStatistics/listByCommodity',
    method: 'post',
    data
  });
}
// 返利数据-单品返利-导出
export function exportByCommodity(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackStatistics/exportByCommodity',
    method: 'post',
    data,
    responseType: 'arraybuffer',
    timeout: 30 * 1000
  });
}
// 返利数据-单品返利数据汇总
export function getSummaryByCommodity(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackStatistics/getSummaryByCommodity',
    method: 'post',
    data
  });
}
// 返利数据-单品返利-活动返利发放明细
export function listIssueByCommodity(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackStatistics/listIssueByCommodity',
    method: 'post',
    data
  });
}
// 返利数据-单品返利-活动返利发放明细数据汇总
export function getIssueByCommoditySum(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackStatistics/getIssueByCommoditySum',
    method: 'post',
    data
  });
}
// 返利数据-单品返利-活动返利发放明细-导出
export function exportIssueByCommodity(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackStatistics/exportIssueByCommodity',
    method: 'post',
    data,
    responseType: 'arraybuffer',
    timeout: 30 * 1000
  });
}
// 返利数据-单品返利-活动返利发放明细-单品分销商发放明细
export function listIssueByCommodityAndDistributor(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackStatistics/listIssueByCommodityAndDistributor',
    method: 'post',
    data
  });
}
// 返利数据-单品返利-活动返利发放明细-单品分销商发放明细汇总
export function getIssueByCommodityAndDistributorSum(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackStatistics/getIssueByCommodityAndDistributorSum',
    method: 'post',
    data
  });
}
// 返利数据-单品返利-活动返利发放明细-单品分销商发放明细-导出
export function exportIssueByCommodityAndDistributor(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackStatistics/exportIssueByCommodityAndDistributor',
    method: 'post',
    data,
    responseType: 'arraybuffer',
    timeout: 30 * 1000
  });
}
// 返利数据-单品返利-返利使用明细
export function listUsedByCommodity(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackStatistics/listUsedByCommodity',
    method: 'post',
    data
  });
}
// 返利数据-单品返利-返利使用明细汇总
export function listUsedByCommoditySum(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackStatistics/listUsedByCommoditySum',
    method: 'post',
    data
  });
}
// 返利数据-单品返利-返利使用明细-导出
export function exportUsedByCommodity(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackStatistics/exportUsedByCommodity',
    method: 'post',
    data,
    responseType: 'arraybuffer',
    timeout: 30 * 1000
  });
}
// 返利数据-品牌返利-品牌返利发放明细
export function listIssueByBrand(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackStatistics/listIssueByBrand',
    method: 'post',
    data
  });
}
// 返利数据-品牌返利-品牌返利发放明细汇总
export function getIssueByBrandSum(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackStatistics/getIssueByBrandSum',
    method: 'post',
    data
  });
}

// 分销商返利发放明细
export function listByGrandDistributor(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackAccountRecord/listByDistributor',
    method: 'post',
    data
  });
}

// 分销商返利发放明细统计
export function queryStatisticsByDistributor(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackAccountRecord/queryStatisticsByDistributor',
    method: 'post',
    data
  });
}

// 分销商返利余额明细
export function listByDistributorVirtualCredit(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackAccountRecord/listByDistributorVirtualCredit',
    method: 'post',
    data
  });
}

// 分销商返利余额明细-导出
export function exportExcelDistributorVirtualCredit(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackAccountRecord/exportExcelDistributorVirtualCredit',
    method: 'post',
    data,
    responseType: 'arraybuffer',
    timeout: 30 * 1000
  });
}
