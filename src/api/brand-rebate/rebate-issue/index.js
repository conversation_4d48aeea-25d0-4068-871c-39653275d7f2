import { withExtTenantIdRequest } from '@/utils/request';
/**
 * 返利活动：发放列表
 * @param {*} data
 */

// 返利发放列表
export function listRebateIssuePage (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackActivitySnapshoot/listIssuePage',
    method: 'post',
    data
  });
}

// 返利商品列表
export function listRebateCommodity (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackActivityCommodity/list',
    method: 'post',
    data
  });
}

// 返利活动-详情简要信息
export function getRebateActivityBrief (id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/creditBackActivitySnapshoot/getBrief?id=${id}`,
    method: 'get'
  });
}
// 审核发放列表
export function listAuditIssuePage (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackDistributorRecord/listIssuePage',
    method: 'post',
    data
  });
}
// 审核发放-列表汇总信息
export function getIssueDetail (data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/creditBackActivitySnapshoot/getIssueDetail?id=${data}`,
    method: 'get'
  });
}
// 审核发放-全部发放
export function issueAll (data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/creditBackDistributorRecord/issueAll?activityId=${data}`,
    method: 'post'
  });
}
// 审核发放-发放返利/驳回到运营
export function issue (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackDistributorRecord/issue',
    method: 'post',
    data
  });
}
// 审核发放-导出
export function auditIssueExportExcel (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackDistributorRecord/exportIssueExcel',
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}
// 单品返利-返利审核-采购明细-查看订单-调整订单
export function changeAmount (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackOrderDetail/changeAmount',
    method: 'post',
    data
  });
}
