import { withExtTenantIdRequest } from '@/utils/request';
import qs from 'qs';
// 查询关键词回复列表
export default {
  post(url, data, headers) {
    return new Promise((resolve, reject) => {
      withExtTenantIdRequest({
        url: url,
        method: 'post',
        data,
        headers
      }).then(res => {
        if (res.code === '0') {
          resolve(res.data);
        } else if (res.status === '500') {
          window.$vue.$message(res.msg);
          reject(res);
        } else if (res.status === '0') {
          resolve(res.data);
        } else {
          reject(res);
        }
      });
    });
  },
  get(url, data, headers) {
    data = qs.stringify(data);
    url = data ? `${url}?${data}` : url;
    return new Promise((resolve, reject) => {
      withExtTenantIdRequest({
        url,
        method: 'get',
        data,
        headers
      }).then(res => {
        if (res.code === '0') {
          resolve(res.data);
        } else if (res.status === '0') {
          resolve(res.data);
        } else if (res.status === '500') {
          window.$vue.$message(res.msg);
          reject(res);
        } else {
          reject(res);
        }
      });
    });
  }
};
