import request, { withExtTenantIdRequest } from '@/utils/request';

// 获取全部的订单信息
export function list (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/order/listPage',
    method: 'post',
    data
  });
}
// 获取全部的包裹信息
export function getDeliveries (id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/order/getDeliveries?id=${id}`,
    method: 'get'
  });
}
// 获取订单详情
export function getById (id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/order/getOrderDetailVO?id=${id}`,
    method: 'get'
  });
}

// 获取交易快照
export function getSnapshootById (id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/order/getSnapshootByOrderDetailId?id=${id}`,
    method: 'get'
  });
}

// 获取订单类型
export function fetchOrderTypeOptions () {
  return withExtTenantIdRequest({
    url: '/common/api/dict/listByType?type=syzg_order_type',
    method: 'get'
  });
}
// 获取订单状态
export function fetchOrderStatusOptions () {
  return withExtTenantIdRequest({
    url: '/common/api/dict/listByType?type=syzg_order_status',
    method: 'get'
  });
}

// 获取物流公司列表
export function fetchLogisticsOptions (data) {
  return request({
    url: '/soyoungzg/api/orderDelivery/listAllDeliveryCompany',
    method: 'post',
    data
  });
}

// 手工发货
export function deliveryGoodsByHand (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/order/delivered',
    method: 'post',
    data
  });
}

// 退款
export function refund (data) {
  return withExtTenantIdRequest({
    url: '/pay/api/weChatPay/refund',
    method: 'post',
    data
  });
}

export function getMemberById (id) {
  return withExtTenantIdRequest({
    url: '/shopmember/api/memberIdcard/getById',
    method: 'get',
    params: {
      id
    }
  });
}

// 订单添加客服备注
export function addServiceNotes (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/order/addServiceNotes',
    method: 'post',
    data
  });
}

// 订单改价，保存订单详情商品价格信息及修改订单实付总价
export function updateOrderDetailPrice (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/order/updateOrderDetailPrice',
    method: 'post',
    data
  });
}

// 订单列表导出
export function exportTable (data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/order/orderExportAsync`,
    method: 'post',
    data
  });
}
// 获取订单来源
export function fetchOrderSourceOptions () {
  return withExtTenantIdRequest({
    url: '/common/api/dict/listByType?type=syzg_order_source',
    method: 'get'
  });
}

// 获取物流详情
export function getDeliveryDetail (id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/order/listDeliveryItemsByOrderId?id=${id}`,
    method: 'get'
  });
}

// 获取创建方式
export function getOrderSaleTypeOptions () {
  return withExtTenantIdRequest({
    url: '/common/api/dict/listByType?type=syzg_order_sale_type',
    method: 'get'
  });
}


// 获取订单渠道来源类型
export function getOrderChannelType () {
  return withExtTenantIdRequest({
    url: '/common/api/dict/listByType?type=syzg_order_channel_type',
    method: 'get'
  });
}

// 获取团队下拉数据
export function getStaffGroupSelect(data = {}) {
  return withExtTenantIdRequest({
    method: 'post',
    url: '/ocean/api/shopStaffGroup/listAll',
    data
  });
}

// 查询自营渠道（湖南水羊、长沙水羊）
export function externalChannelListSelfChannelInfo() {
  return withExtTenantIdRequest({
    method: 'get',
    url: '/soyoungzg/api/externalChannel/listSelfChannelInfo',
  });
}

// 获取订单改价日志分页列表
export function businessRecordLogListByBizId(data) {
  return withExtTenantIdRequest({
    method: 'post',
    url: '/soyoungzg/api/businessRecordLog/listByBizId',
    data
  });
}
