import { withExtTenantIdRequest } from '@/utils/request';

// 获取全部的订单信息
export function listPage(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/order/listThirdPage',
    method: 'post',
    data
  });
}
// 获取全部的包裹信息
export function getDeliveries(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/order/getDeliveries?id=${id}`,
    method: 'get'
  });
}
// 获取订单详情
export function getById(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/order/getOrderDetailVO?id=${id}`,
    method: 'get'
  });
}

// 获取交易快照
export function getSnapshootById(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/order/getSnapshootByOrderDetailId?id=${id}`,
    method: 'get'
  });
}

// 手工发货
export function deliveryGoodsByHand(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/order/delivered',
    method: 'post',
    data
  });
}

// 订单列表导出
export function exportTable(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/order/orderExportAsync`,
    method: 'post',
    data
  });
}

// 获取物流详情
export function getDeliveryDetail(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/order/listDeliveryItemsByOrderId?id=${id}`,
    method: 'get'
  });
}

// 未匹配订单的合作店铺列表
export function listUnMatchDistributorPage(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/order/listUnMatchDistributorPage',
    method: 'post',
    data
  });
}

// 未匹配品牌的合作店铺列表
export function listUnMatchBrandPage(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/order/listUnMatchBrandPage',
    method: 'post',
    data
  });
}
// 未匹配品牌的下拉列表
export function listUnMatchBrand() {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/order/listUnMatchBrand',
    method: 'post'
  });
}

// 未匹配商品的合作店铺列表
export function listUnMatchCommodityPage(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/order/listUnMatchCommodityPage',
    method: 'post',
    data
  });
}

// 订单匹配分销商
export function matchDistributor(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/order/matchDistributor',
    method: 'post',
    data
  });
}

// 品牌匹配分销商
export function matchBrand(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/order/matchBrand',
    method: 'post',
    data
  });
}

// 订单匹配商品
export function matchCommodity(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/order/matchCommodity',
    method: 'post',
    data
  });
}

// 退款订单列表
export function listRefundPage(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/refundOrder/listThirdPage',
    method: 'post',
    data
  });
}

// 退款订单列表导出
export function exportForPc(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/refundOrder/exportForPc`,
    method: 'post',
    responseType: 'arraybuffer',
    data: {
      ...data,
      dataSources: 'THIRD_PARTY_ORDER'
    },
    timeout: 30 * 1000
  });
}

// 获取退款详情
export function getRefundById(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/refundOrder/get?id=${id}`,
    method: 'get'
  });
}

// 订单导入
export function createThirdInBatch(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/order/createThirdInBatch `,
    method: 'post',
    data
  });
}

// 订单导入校验
export function validThirdInBatch(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/order/validThirdInBatch',
    method: 'post',
    data
  });
}

// 退款导入
export function createRefundOrderNormalInBatch(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/refundOrder/createRefundOrderNormalInBatch `,
    method: 'post',
    data
  });
}

// 退款订单导入校验
export function validThirdRefundInBatch(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/refundOrder/validThirdRefundInBatch',
    method: 'post',
    data
  });
}

// 订单变更
export function orderUpdateOrderChange(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/order/updateOrderChange',
    method: 'post',
    data
  });
}

// 订单变更记录
export function orderChangeLogPage(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/order/changeLogPage',
    method: 'post',
    data
  });
}

// 三方订单导入批量业绩校验
export function validChangeOrderThirdInBatch(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/order/validChangeOrderThirdInBatch',
    method: 'post',
    data
  });
}

// 三方订单导入批量业绩
export function changeOrderThirdInBatch(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/order/changeOrderThirdInBatch `,
    method: 'post',
    data
  });
}

// 三方退款订单导入批量业绩校验
export function validChangeRefundOrderThirdInBatch(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/order/validChangeRefundOrderThirdInBatch',
    method: 'post',
    data
  });
}

// 三方退款订单导入批量业绩
export function changeRefundOrderThirdInBatch(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/order/changeRefundOrderThirdInBatch `,
    method: 'post',
    data
  });
}
// 三方订单-未匹配三方分销商-导出
export function orderDownloadExportUnMatchDistributor(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/order/downloadExportUnMatchDistributor',
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}
