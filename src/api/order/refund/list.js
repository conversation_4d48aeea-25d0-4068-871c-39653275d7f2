import { withExtTenantIdRequest } from '@/utils/request';

// 获取全部的参数信息
export function list(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/refundOrder/listPage',
    method: 'post',
    data
  });
}

// 获取退款方式列表
export function fetchStatusOptions() {
  return withExtTenantIdRequest({
    url: '/common/api/dict/listByType?type=refund_status',
    method: 'get'
  });
}

// 获取店铺默认地址
export function fetchAddress(type) {
  return withExtTenantIdRequest({
    url: `/ocean/api/shopAddress/getDefaultAddress?type=${type}`,
    method: 'get'
  });
}

// 获取退款详情
export function getById(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/refundOrder/get?id=${id}`,
    method: 'get'
  });
}

// 获取订单详情
export function getOrderById(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/order/getVO?id=${id}`,
    method: 'get'
  });
}
// 获取订单包裹信息
export function getDeliveryInfo(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/order/getDeliveries?id=${id}`,
    method: 'get'
  });
}
// 卖家重新打款
export function refundAgain(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/refundOrder/retryRefundPay?id=${id}`,
    method: 'post'
  });
}

// 卖家收到货后同意退款
export function refund(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/refundOrder/finishReceive`,
    method: 'post',
    data
  });
}

// 卖家同意退款并且发送退货地址
export function refundWithAddress(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/refundOrder/agree',
    method: 'post',
    data
  });
}

// 卖家驳回退款申请
export function reject(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/refundOrder/reject',
    method: 'post',
    data
  });
}

// 卖家拒绝退款收货
export function rejectReceive(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/refundOrder/rejectReceive`,
    method: 'post',
    data
  });
}

// 退款订单列表导出
export function exportTable(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/refundOrder/exportForPc`,
    method: 'post',
    responseType: 'arraybuffer',
    data: {
      ...data,
      dataSources: 'SELF_OPERATED_ORDER' 
    },
    timeout: 30 * 1000
  });
}

// 获取退货物流详情
export function listDeliveryItemsByRefundOrderId(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/refundOrder/listDeliveryItemsByRefundOrderId?id=${id}`,
    method: 'get'
  });
}
