import { withExtTenantIdRequest } from '@/utils/request';

// 公告管理 列表
export function listPage(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/announcement/listPage',
    method: 'post',
    data
  });
}

// 新增
export function create(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/announcement/create',
    method: 'post',
    data
  });
}

// 更新
export function update(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/announcement/update',
    method: 'post',
    data
  });
}
// 通过id获取店铺的信息
export function getById(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/announcement/get?id=${id}`,
    method: 'get'
  });
}
// 通过id删除
export function deleteById(id) {
  return withExtTenantIdRequest({
    url: `/notify/api/announcement/delete?id=${id}`,
    method: 'POST'
  });
}
// 批量删除
export function deleteByIds(data) {
  return withExtTenantIdRequest({
    url: `/notify/api/announcement/deleteByIds`,
    method: 'POST',
    data
  });
}
// 通过id发布
export function publishById(id) {
  return withExtTenantIdRequest({
    url: `notify/api/announcement/publish?id=${id}`,
    method: 'POST'
  });
}
// 撤回
export function cancelById(id) {
  return withExtTenantIdRequest({
    url: `/notify/api/announcement/cancel?id=${id}`,
    method: 'POST'
  });
}
