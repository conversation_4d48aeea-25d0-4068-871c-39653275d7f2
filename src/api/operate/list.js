import { withExtTenantIdRequest } from '@/utils/request';

// 运营端 - 拓展排行榜
export function zgRankListDevelopRankList(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/zgRankList/developRankList',
    method: 'post',
    data
  });
}

// 运营端 - 拓展回款明细(单个拓展下订单列表)
export function zgRankListDevelopRankListDetail(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/zgRankList/developRankListDetail',
    method: 'post',
    data
  });
}

// 直供报表- 拓展排行 - 汇总
export function zgRankListDevelopRankTotal(data = {}) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/zgRankList/developRankTotal',
    method: 'post',
    data
  });
}

// 运营端 - 拓展回款明细 - 汇总
export function zgRankListDevelopRankDetailTotal(data = {}) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/zgRankList/developRankDetailTotal',
    method: 'post',
    data
  });
}

// 运营端 - 质客明细 - 汇总
export function zgRankListQualityCustomerDetailCollect(data = {}) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/zgRankList/qualityCustomerDetailCollect',
    method: 'post',
    data
  });
}

// 运营端 - 导出拓展回款明细列表
export function zgRankListExportDevelopRankListDetailExcel(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/zgRankList/exportDevelopRankListDetailExcel',
    method: 'post',
    data,
    responseType: 'arraybuffer',
    timeout: 30 * 1000
  });
}

// 运营端 - 导出拓展排行列表
export function zgRankListExportDevelopRankListExcel(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/zgRankList/exportDevelopRankListExcel',
    method: 'post',
    data,
    responseType: 'arraybuffer',
    timeout: 30 * 1000
  });
}

// 直供报表-拓展排行-质客明细
export function zgRankListQualityCustomerDetail(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/zgRankList/qualityCustomerDetail',
    method: 'post',
    data
  });
}

// 直供报表- 运维排行
export function zgRankListOpsRankList(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/zgRankList/opsRankList',
    method: 'post',
    data
  });
}

// 直供报表- 运维排行 - 汇总
export function zgRankListOpsRankCollect(data = {}) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/zgRankList/opsRankCollect',
    method: 'post',
    data
  });
}

// 直供报表- 运维排行 - 动销分销商明细-汇总
export function zgRankListMovablePinDistributorDetailCollect(data = {}) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/zgRankList/movablePinDistributorDetailCollect',
    method: 'post',
    data
  });
}

// 直供报表- 运维排行 -动销分销商明细
export function zgRankListMovablePinDistributorDetail(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/zgRankList/movablePinDistributorDetail',
    method: 'post',
    data
  });
}

// 直供报表- 运维回款明细
export function zgRankListReturnedAmountDetail(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/zgRankList/returnedAmountDetail',
    method: 'post',
    data
  });
}

// 直供报表- 运维回款明细-汇总
export function zgRankListReturnedAmountDetailCollect(data = {}) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/zgRankList/returnedAmountDetailCollect',
    method: 'post',
    data
  });
}

// 直供报表-拓展排行-质客明细-导出
export function zgRankListExportQualityCustomerDetail(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/zgRankList/exportQualityCustomerDetail',
    method: 'post',
    data,
    responseType: 'arraybuffer',
    timeout: 30 * 1000
  });
}

// 直供报表- 运维排行-导出
export function zgRankListExportOpsRankList(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/zgRankList/exportOpsRankList',
    method: 'post',
    data,
    responseType: 'arraybuffer',
    timeout: 30 * 1000
  });
}

// 直供报表- 动销分销商明细-导出
export function zgRankListExportMovablePinDistributorDetail(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/zgRankList/exportMovablePinDistributorDetail',
    method: 'post',
    data,
    responseType: 'arraybuffer',
    timeout: 30 * 1000
  });
}

// 直供报表- 运维回款明细-导出
export function zgRankListExportReturnedAmountDetail(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/zgRankList/exportReturnedAmountDetail',
    method: 'post',
    data,
    responseType: 'arraybuffer',
    timeout: 30 * 1000
  });
}
