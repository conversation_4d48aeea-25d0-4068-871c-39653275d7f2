import request, { withExtTenantIdRequest } from '@/utils/request';
// import axios from 'axios';

export function login(username, password) {
  return withExtTenantIdRequest({
    url: '/ocean/api/auth/login',
    method: 'post',
    params: {
      username,
      password
    }
  });
}

export function loginWithCode(username, verificationCode) {
  return request({
    url: `/user/login?username=${username}&verificationCode=${verificationCode}`,
    method: 'post'
  });
}

export function logout() {
  return request({
    url: '/user/logout',
    method: 'post'
  });
}

export function ssoLogin(sessionId, csrfToken) {
  return request({
    url: '/user/api/user/self',
    method: 'get',
    processError: false,
    process404: false,
    headers: {
      'X-Session-Id': sessionId,
      'X-Csrf-Token': csrfToken
    }
  });
}
