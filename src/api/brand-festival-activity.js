import { withExtTenantIdRequest } from '@/utils/request';
// PC-活动列表（意向签约&产品推荐共用）
export function brandFestivalActivityList(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandFestivalActivity/list',
    method: 'post',
    data
  });
}
// PC-新增活动（意向签约&产品推荐共用）
export function brandFestivalActivityCreate(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandFestivalActivity/create',
    method: 'post',
    data
  });
}
// PC-活动详情（意向签约&产品推荐共用）
export function brandFestivalActivityGet (id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/brandFestivalActivity/get?id=${id}`,
    method: 'get'
  });
}

// PC-编辑活动（意向签约&产品推荐共用）
export function brandFestivalActivityUpdate(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandFestivalActivity/update',
    method: 'post',
    data
  });
}

// PC-删除活动（意向签约&产品推荐共用）
export function brandFestivalActivityDelete(id) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandFestivalActivity/delete?id=' + id,
    method: 'post',
    data: {}
  });
}

// PC-更改活动状态
export function brandFestivalActivityUpdateStatus(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandFestivalActivity/updateStatus',
    method: 'post',
    data
  });
}

// PC-数据列表（意向签约&产品推荐共用）
export function brandFestivalCustomerDataList(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandFestivalCustomerData/list',
    method: 'post',
    data
  });
}

// PC-大屏数据统计（真实数据）
export function brandFestivalActivityCustomerDataStat(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/brandFestivalActivity/customerDataStat?id=${id}`,
    method: 'post',
    data:{}
  });
}

// PC-更新意向签约初始数据
export function brandFestivalActivityUpdateInitData(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandFestivalActivity/updateInitData',
    method: 'post',
    data
  });
}

// PC-上传虚拟数据（产品推荐）
export function brandFestivalCustomerDataImportVirtualProductData(data = {}, activityId) {
  return withExtTenantIdRequest({
    url:  `/soyoungzg/api/brandFestivalCustomerData/importVirtualProductData?activityId=${activityId}`,
    method: 'post',
    data
  });
}

// PC-上传虚拟数据（意向签约）
export function brandFestivalCustomerDataImportVirtualSignData(data = {}, activityId) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/brandFestivalCustomerData/importVirtualSignData?activityId=${activityId}`,
    method: 'post',
    data
  });
}

// PC-更改数据显示状态
export function brandFestivalCustomerDataUpdateShowStatus(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandFestivalCustomerData/updateShowStatus',
    method: 'post',
    data
  });
}

// PC-获取系列产品信息
export function brandFestivalCustomerDataListAll(type, activityId) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/brandFestivalCustomerData/listAll?type=${type}&activityId=${activityId}`,
    method: 'get'
  });
}

// PC获取默认海报
export function brandFestivalActivityGetDefaultPoster(type) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/brandFestivalActivity/getDefaultPoster?type=${type}`,
    method: 'get'
  });
}

// PC设置默认海报
export function brandFestivalActivitySetDefaultPoster(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/brandFestivalActivity/setDefaultPoster',
    method: 'post',
    data
  });
}
