import request from '@/utils/request';

export function list() {
  return request({
    url: '/user/api/menu/listAllWithApis',
    method: 'post',
    data: { menu: {} }
  });
}

export function create(data) {
  return request({
    url: '/user/api/menu/create',
    method: 'post',
    data
  });
}

export function update(data) {
  return request({
    url: '/user/api/menu/update',
    method: 'post',
    data
  });
}

export function fetchDelete(id) {
  return request({
    url: '/user/api/menu/delete',
    method: 'post',
    params: { id }
  });
}

export function fetchResouceList() {
  return request({
    url: '/user/api/resource/listAll',
    method: 'post',
    data: {}
  });
}

export function fetchSystemApiList() {
  return request({
    url: '/user/api/systemApi/listAll',
    method: 'post',
    data: {}
  });
}

export function fetchGroupApis() {
  return request({
    url: '/user/api/systemApi/listAllGroup',
    method: 'post'
  });
}

//  获取映射域名租户关系列表
export function listAllApps() {
  return request({
    url: '/common/api/app/listAll',
    method: 'post',
    data: {}
  });
}
