import request from '@/utils/request';

export function fetchListPage(data) {
  return request({
    url: '/@DEFAULT_SERVICE/api/manager/listManagerPage',
    method: 'post',
    data
  });
}

export function fetchUpdate(data) {
  return request({
    url: '/@DEFAULT_SERVICE/api/manager/editManager',
    method: 'post',
    data
  });
}

export function fetchCreate(data) {
  return request({
    url: '/@DEFAULT_SERVICE/api/manager/createManager',
    method: 'post',
    data
  });
}

export function fetchView(id) {
  return request({
    url: '/@DEFAULT_SERVICE/api/manager/viewManager',
    method: 'post',
    params: { id }
  });
}

export function fetchDelete(id) {
  return request({
    url: '/@DEFAULT_SERVICE/api/manager/deleteManager',
    method: 'post',
    params: { id }
  });
}

export function fetchRoleList() {
  return request({
    url: '/user/api/role/listAll',
    method: 'post',
    data: {}
  });
}

export function modifyPasswordByUserId(userId, newPassword) {
  return request({
    url: '/user/api/user/modifyPassword',
    method: 'post',
    params: { userId, newPassword }
  });
}

export function modifyPassword(mobile, verificationCode, password) {
  return request({
    url: `/user/api/user/forgetPassword?mobile=${mobile}&verificationCode=${verificationCode}&password=${password}`,
    method: 'post'
  });
}

export function register(mobile, verificationCode, password) {
  return request({
    url: `/newretail/api/auth/register?mobile=${mobile}&verificationCode=${verificationCode}&password=${password}`,
    method: 'post'
  });
}

// 根据手机号获取验证码
export function getCode(mobile) {
  return request({
    url: `/newretail/api/verifyCode/send?mobile=${mobile}`,
    method: 'post'
  });
}
