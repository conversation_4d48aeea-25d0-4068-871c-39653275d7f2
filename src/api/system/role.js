import request from '@/utils/request';

export function fetchList(data) {
  return request({
    url: '/user/api/role/listAll',
    method: 'post',
    data
  });
}

export function fetchMenuList(roleId = '0') {
  return request({
    url: '/user/api/role/getMenuVO',
    method: 'post',
    params: { roleId }
  });
}

export function fetchCreate(data) {
  return request({
    url: '/user/api/role/create',
    method: 'post',
    data
  });
}

export function fetchUpdate(data) {
  return request({
    url: '/user/api/role/update',
    method: 'post',
    data
  });
}

export function fetchView(id) {
  return request({
    url: '/user/api/role/get',
    method: 'get',
    params: { id }
  });
}

export function fetchDelete(id) {
  return request({
    url: '/user/api/role/delete',
    method: 'post',
    params: { id }
  });
}

export function fetchAssign(data) {
  return request({
    url: '/user/api/role/assignRoleResource',
    method: 'post',
    data
  });
}
