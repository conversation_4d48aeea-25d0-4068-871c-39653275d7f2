import { withExtTenantIdRequest } from '@/utils/request';

// 白名单列表
export function list(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorWebsiteWhitelist/listPage',
    method: 'post',
    data
  });
}
// 白名单导出
export function exportExcel(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorWebsiteWhitelist/exportExcel`,
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}
// 白名单审核
export function audit(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorWebsiteWhitelist/audit',
    method: 'post',
    data
  });
}
// 平台名称字典
export function fetchChannelOptions () {
  return withExtTenantIdRequest({
    url: '/common/api/dict/listByType?type=soyoungzg_channel',
    method: 'get'
  });
}

// 白名单批量导入
export function distributorWebsiteWhitelistCreateInBatch(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorWebsiteWhitelist/createInBatch`,
    method: 'post',
    data
  });
}