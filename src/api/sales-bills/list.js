import { withExtTenantIdRequest } from '@/utils/request';

// 获取销售账单列表
export function saleBillSum (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/saleBillDetail/saleBillSum',
    method: 'post',
    data
  });
}

// 导出销售账单
export function exportSaleBillSum (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/saleBillDetail/exportSaleBillSum',
    method: 'post',
    data,
    responseType: 'arraybuffer',
    timeout: 30 * 1000
  });
}
// 更新上月数据
export function updateLastMonthBill (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/saleBillDetail/updateLastMonthBill',
    method: 'post',
    data
  });
}
// 获取品牌明细列表
export function saleBillDetailList (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/saleBillDetail/list',
    method: 'post',
    data
  });
}
// 导出品牌明细列表
export function exportSaleBillDetail (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/saleBillDetail/exportSaleBillDetail',
    method: 'post',
    data,
    responseType: 'arraybuffer',
    timeout: 30 * 1000
  });
}
// 导出品牌明细dss账单
export function exportDssSaleBillDetail (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/saleBillDetail/exportDssSaleBillDetail',
    method: 'post',
    data,
    responseType: 'arraybuffer',
    timeout: 30 * 1000
  });
}
// 获取品牌明细列表合计
export function listSum (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/saleBillDetail/listSum',
    method: 'post',
    data
  });
}
// 获取上一次更新时间
export function getLastUpdateTime () {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/saleBillDetail/getLastUpdateTime',
    method: 'get'
  });
}
