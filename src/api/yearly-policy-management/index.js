import { withExtTenantIdRequest } from '@/utils/request';

// 获取年框政策列表
export function distributorYearlyPolicyList(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorYearlyPolicy/list',
    method: 'post',
    data
  });
}

// 年框政策备注
export function distributorYearlyPolicyAddPolicyRemarks(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorYearlyPolicy/addPolicyRemarks',
    method: 'post',
    data
  });
}

// 年框政策-目标解析
export function distributorYearlyPolicyParseTargetContent(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorYearlyPolicy/parseTargetContent',
    method: 'post',
    data
  });
}

// 启用年框政策
export function distributorYearlyPolicyEnable(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorYearlyPolicy/enable',
    method: 'post',
    data
  });
}

// 停用年框政策
export function distributorYearlyPolicyStop(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorYearlyPolicy/stop',
    method: 'post',
    data
  });
}

// 年框政策列表导出
export function distributorYearlyPolicyExport(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorYearlyPolicy/export',
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}

// 获取年框政策合同
export function listPageForYearlyPolicy(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorContract/listPageForYearlyPolicy',
    method: 'post',
    data
  });
}

// 结算单审核列表
export function distributorYearlyPolicySettlementList(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorYearlyPolicySettlement/list',
    method: 'post',
    data
  });
}

// 获取年框政策结算单详情
export function distributorYearlyPolicySettlementGetDetail(settlementId) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorYearlyPolicySettlement/getDetail?settlementId=${settlementId}`,
    method: 'get'
  });
}

// 重新计算结算单
export function distributorYearlyPolicySettlementReCalSettlementOrder(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorYearlyPolicySettlement/reCalSettlementOrder',
    method: 'post',
    data
  });
}

// 结算订单明细
export function distributorYearlyPolicySettlementListSettlementOrderItem(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorYearlyPolicySettlement/listSettlementOrderItem',
    method: 'post',
    data
  });
}
// 结算订单明细-导出
export function distributorYearlyPolicySettlementSettlementOrderItemExportExcel(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorYearlyPolicySettlement/settlementOrderItem/exportExcel',
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}
// 查询采购金额构成-统计实收金额合计
export function distributorYearlyPolicySettlementStatSettlementOrderTotalAmount(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorYearlyPolicySettlement/statSettlementOrderTotalAmount',
    method: 'post',
    data
  });
}
// 结算单审批
export function distributorYearlyPolicySettlementApprovalSettlementOrder(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorYearlyPolicySettlement/approvalSettlementOrder',
    method: 'post',
    data
  });
}

// 年框政策-新增
export function distributorYearlyPolicyCreate(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorYearlyPolicy/create',
    method: 'post',
    data
  });
}

// 年框政策-获取详情
export function distributorYearlyPolicyGet(id) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorYearlyPolicy/get?id=' + id,
    method: 'get'
  });
}

// 年框政策-编辑
export function distributorYearlyPolicyUpdate(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorYearlyPolicy/update',
    method: 'post',
    data
  });
}

/**
 * 获取用身份
 * https://apidoc.syounggroup.com/doc.html#/HERMES-%E5%88%86%E9%94%80%E7%AE%A1%E7%90%86/%E5%B9%B4%E6%A1%86%E5%90%88%E5%90%8C%E5%A4%96%E7%94%B3%E8%AF%B7/listDepartmentTreeUsingGET
 */
export function distributorPolicyApplyExternalListUserCompany(id) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorPolicyApplyExternal/listUserCompany ',
    method: 'get'
  });
}

/**
 * 新增合同外申请
 * https://apidoc.syounggroup.com/doc.html#/HERMES-%E5%88%86%E9%94%80%E7%AE%A1%E7%90%86/%E5%B9%B4%E6%A1%86%E5%90%88%E5%90%8C%E5%A4%96%E7%94%B3%E8%AF%B7/createUsingPOST_54
 */
export function distributorPolicyApplyExternalCreate(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorPolicyApplyExternal/create',
    method: 'post',
    data
  });
}

/**
 * 获取成本中心列表
 * https://apidoc.syounggroup.com/doc.html#/HERMES-%E5%88%86%E9%94%80%E7%AE%A1%E7%90%86/%E5%B9%B4%E6%A1%86%E5%90%88%E5%90%8C%E5%A4%96%E7%94%B3%E8%AF%B7/listCostCenterUsingGET
 */
export function distributorPolicyApplyExternalListCostCenter(id) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorPolicyApplyExternal/listCostCenter',
    method: 'get'
  });
}

/**
 * 合同外申请列表分页查询
 * https://apidoc.syounggroup.com/doc.html#/HERMES-%E5%88%86%E9%94%80%E7%AE%A1%E7%90%86/%E5%B9%B4%E6%A1%86%E5%90%88%E5%90%8C%E5%A4%96%E7%94%B3%E8%AF%B7/listPageUsingPOST_67
 */
export function distributorPolicyApplyExternalList(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorPolicyApplyExternal/list',
    method: 'post',
    data
  });
}

/**
 * 标记已完结
 * https://apidoc.syounggroup.com/doc.html#/HERMES-%E5%88%86%E9%94%80%E7%AE%A1%E7%90%86/%E5%B9%B4%E6%A1%86%E5%90%88%E5%90%8C%E5%A4%96%E7%94%B3%E8%AF%B7/signFinishUsingGET
 */
export function distributorPolicyApplyExternalSignFinish(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorPolicyApplyExternal/signFinish?id=${id}`,
    method: 'get'
  });
}

/**
 * 修改合同外修改
 * https://apidoc.syounggroup.com/doc.html#/HERMES-%E5%88%86%E9%94%80%E7%AE%A1%E7%90%86/%E5%B9%B4%E6%A1%86%E5%90%88%E5%90%8C%E5%A4%96%E7%94%B3%E8%AF%B7/updateUsingPOST_49
 */
export function distributorPolicyApplyExternalUpdate(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorPolicyApplyExternal/update',
    method: 'post',
    data
  });
}

/**
 * 获取合同外政策详情
 * https://apidoc.syounggroup.com/doc.html#/HERMES-%E5%88%86%E9%94%80%E7%AE%A1%E7%90%86/%E5%B9%B4%E6%A1%86%E5%90%88%E5%90%8C%E5%A4%96%E7%94%B3%E8%AF%B7/getUsingGET_40
 */
export function distributorPolicyApplyExternalGet(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorPolicyApplyExternal/get?id=${id}`,
    method: 'get'
  });
}

/**
 * 返利发放-查询合同外发放列表
 * https://apidoc.syounggroup.com/doc.html#/HERMES-%E5%88%86%E9%94%80%E7%AE%A1%E7%90%86/%E5%90%88%E5%90%8C%E5%A4%96%E6%94%BF%E7%AD%96%E8%BF%94%E5%88%A9%E5%8F%91%E6%94%BE/listPageUsingPOST_68
 */
export function distributorPolicyIssueExternalList(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorPolicyIssueExternal/list',
    method: 'post',
    data
  });
}

/**
 * 新增返利发放
 * https://apidoc.syounggroup.com/doc.html#/HERMES-%E5%88%86%E9%94%80%E7%AE%A1%E7%90%86/%E5%90%88%E5%90%8C%E5%A4%96%E6%94%BF%E7%AD%96%E8%BF%94%E5%88%A9%E5%8F%91%E6%94%BE/createUsingPOST_55
 */
export function distributorPolicyIssueExternalCreate(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorPolicyIssueExternal/create',
    method: 'post',
    data
  });
}

/**
 * 获取返利发放详情
 * https://apidoc.syounggroup.com/doc.html#/HERMES-%E5%88%86%E9%94%80%E7%AE%A1%E7%90%86/%E5%90%88%E5%90%8C%E5%A4%96%E6%94%BF%E7%AD%96%E8%BF%94%E5%88%A9%E5%8F%91%E6%94%BE/getUsingGET_41
 */
export function distributorPolicyIssueExternalGet(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorPolicyIssueExternal/get?id=${id}`,
    method: 'get'
  });
}

/**
 * 修改返利发放记录
 * https://apidoc.syounggroup.com/doc.html#/HERMES-%E5%88%86%E9%94%80%E7%AE%A1%E7%90%86/%E5%90%88%E5%90%8C%E5%A4%96%E6%94%BF%E7%AD%96%E8%BF%94%E5%88%A9%E5%8F%91%E6%94%BE/updateUsingPOST_50
 */
export function distributorPolicyIssueExternalUpdate(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorPolicyIssueExternal/update',
    method: 'post',
    data
  });
}
/**
 * 年框政策-获取已关联的其他合同
 * https://apidoc.syounggroup.com/doc.html#/HERMES-%E5%88%86%E9%94%80%E7%AE%A1%E7%90%86/%E5%88%86%E9%94%80%E5%95%86%E5%B9%B4%E6%A1%86%E6%94%BF%E7%AD%96/listRelatedOtherContractUsingPOST
 */
export function distributorYearlyPolicyListRelatedOtherContract(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorYearlyPolicy/listRelatedOtherContract',
    method: 'post',
    data
  });
}
/**
 * PC-年框政策-关联其他合同-查询主合同关联的合同列表
 * https://apidoc.syounggroup.com/doc.html#/HERMES-%E5%88%86%E9%94%80%E7%AE%A1%E7%90%86/%E5%88%86%E9%94%80%E5%95%86%E5%90%88%E5%90%8C%E7%AE%A1%E7%90%86/listOthersForYearlyPolicyUsingPOST
 */
export function distributorContractListOthersForYearlyPolicy(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorContract/listOthersForYearlyPolicy',
    method: 'post',
    data
  });
}
/**
 * 年框政策-关联其他合同
 * https://apidoc.syounggroup.com/doc.html#/HERMES-%E5%88%86%E9%94%80%E7%AE%A1%E7%90%86/%E5%88%86%E9%94%80%E5%95%86%E5%B9%B4%E6%A1%86%E6%94%BF%E7%AD%96/relatedOtherContractUsingPOST
 */
export function distributorYearlyPolicyRelatedOtherContract(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorYearlyPolicy/relatedOtherContract',
    method: 'post',
    data
  });
}
/**
 * 年框政策-关联其他合同-校验关联的合同是否可以关联
 * https://apidoc.syounggroup.com/doc.html#/HERMES-%E5%88%86%E9%94%80%E7%AE%A1%E7%90%86/%E5%88%86%E9%94%80%E5%95%86%E5%B9%B4%E6%A1%86%E6%94%BF%E7%AD%96/validateRelatedOtherContractUsingPOST
 */
export function distributorYearlyPolicyValidateRelatedOtherContract(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorYearlyPolicy/validateRelatedOtherContract',
    method: 'post',
    data,
    autoErrorMsg: false
  });
}

/**
 * PC-年框政策-新增政策时选择合同，并自动解析
 * https://apidoc.syounggroup.com/doc.html#/HERMES-%E5%88%86%E9%94%80%E7%AE%A1%E7%90%86/%E5%88%86%E9%94%80%E5%95%86%E5%B9%B4%E6%A1%86%E6%94%BF%E7%AD%96/validateRelatedOtherContractUsingPOST
 */
export function distributorYearlyPolicyAnalyzeContract(id) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorYearlyPolicy/analyzeContract',
    method: 'post',
    data: { id: id }
  });
}
