import { withExtTenantIdRequest } from '@/utils/request';

export function getQuickBIAccessToken() {
  return withExtTenantIdRequest({
    url: '/ocean/api/aliyun/getQuickBIAccessToken',
    method: 'get'
  });
}
export function getQuickBiUrl(param) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/aliyunQuickBI/getQuickBiUrl` + param,
    method: 'get'
  });
}
export function queryByCondition(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/aliyunQuickBI/queryByCondition`,
    method: 'post',
    data
  });
}

// quickbi 新接口
export function quickbiConfigGetQuickBiUrl(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/quickbiConfig/getQuickBiUrl`,
    method: 'post',
    data
  });
}
