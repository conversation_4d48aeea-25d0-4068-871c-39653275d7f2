import { withExtTenantIdRequest } from '@/utils/request';

// 返利发放列表
export function listCommodityIssuePage(data) {
    return withExtTenantIdRequest({
      url: '/soyoungzg//api/creditBackActivitySnapshoot/listCommodityIssuePage',
      method: 'post',
      data
    });
}

// 返利发放导出
export function exportCommodityIssue(data) {
    return withExtTenantIdRequest({
      url: '/soyoungzg/api/creditBackActivitySnapshoot/exportCommodityIssue',
      method: 'post',
      data,
      responseType: 'arraybuffer',
      timeout: 30 * 1000
    });
}

// 返利发放数据汇总
export function commodityIssueStatistic(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackActivitySnapshoot/commodityIssueStatistic',
    method: 'post',
    data
  });
}
// 发放奖励和驳回
export function issueCommodityCredit(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg//api/creditBackDistributorRecord/issueCommodityCredit',
    method: 'post',
    data
  });
}
// 全部发放
export function issueAllCommodityCredit(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackDistributorRecord/issueAllCommodityCredit',
    method: 'post',
    data
  });
}
// 单品返利发放-采购明细
export function commodityIssuePurchaseList(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackOrder/commodityIssuePurchaseList',
    method: 'post',
    data
  });
}
// 单品返利发放-采购明细-查看订单
export function commodityOrderList(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackOrder/commodityOrderList',
    method: 'post',
    data
  });
}
// 单品返利发放-采购明细-合计栏
export function summaryCommodityOrder(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/creditBackOrder/summaryCommodityOrder?id=${id}`,
    method: 'get'
  });
}
// 单品返利审核-调整订单-最大调整金额
export function calculateCommodityMaxAmount(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/creditBackOrderDetail/calculateCommodityMaxAmount`,
    method: 'post',
    data
  });
}
// 单品返利审核-调整订单-调整订单
export function changeSingleCommodityAmount(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/creditBackOrderDetail/changeSingleCommodityAmount`,
    method: 'post',
    data
  });
}
