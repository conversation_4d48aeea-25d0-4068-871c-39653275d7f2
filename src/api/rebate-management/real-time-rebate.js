import {
  withExtTenantIdRequest
} from '@/utils/request';

export function listPage(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackActivitySnapshoot/listCommodityCurrentCreditBackPage',
    method: 'post',
    data: data
  });
}

export function exportData(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/creditBackActivitySnapshoot/exportCommodityCurrentCreditBackPage`,
    method: 'post',
    data,
    responseType: 'arraybuffer',
    timeout: 30 * 1000
  });
}
export function fetchCommodity(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackActivity/listCurrentCommodityActivity',
    method: 'post',
    data: data
  });
}
// 获取返利门槛
export function listMonthThresholds(id) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackActivity/listMonthThresholds?activityId=' + id
  });
}
// 快速查询统计
export function fetchCurrentStatistic(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackActivitySnapshoot/commodityCurrentFastStatistic',
    method: 'post',
    data: data
  });
}

export function listPageFast(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackActivitySnapshoot/listCommodityCurrentFastPage',
    method: 'post',
    data: data
  });
}

export function exportDataFast(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/creditBackActivitySnapshoot/exportCommodityCurrentFastCreditBack`,
    method: 'post',
    data,
    responseType: 'arraybuffer',
    timeout: 30 * 1000
  });
}
// 采购明细
export function commodityCurrentPurchaseList(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/creditBackOrder/commodityCurrentPurchaseList`,
    method: 'post',
    data
  });
}
// // 查看订单
export function commodityCurrentOrderList(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/creditBackOrder/commodityCurrentOrderList`,
    method: 'post',
    data
  });
}
// 采购明细汇总
export function summaryCurrentCommodityOrder(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/creditBackOrder/summaryCurrentCommodityOrder`,
    method: 'post',
    data
  });
}