import { withExtTenantIdRequest } from '@/utils/request';

export function listPage(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackActivitySnapshoot/listCommodityAuditPage',
    method: 'post',
    data: data
  });
}
export function exportData(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackActivitySnapshoot/exportCommodityAudit',
    method: 'post',
    data,
    responseType: 'arraybuffer',
    timeout: 30 * 1000
  });
}
export function fetchStatistic(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackActivitySnapshoot/commodityAuditStatistic',
    method: 'post',
    data: data
  });
}

export function reCalculateAll(data) {
  return withExtTenantIdRequest({
    url:
      '/soyoungzg/api/creditBackDistributorRecord/commodityCreditBack/reCalculateAll',
    method: 'post',
    data: data
  });
}
export function reCalculate(data) {
  return withExtTenantIdRequest({
    url:
      '/soyoungzg/api/creditBackDistributorRecord/commodityCreditBack/reCalculate',
    method: 'post',
    data: data
  });
}
export function audit(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackDistributorRecord/commodityCreditBack/audit',
    method: 'post',
    data: data
  });
}
// 返利审核-采购明细
export function commodityPurchaseList(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackOrder/commodityPurchaseList',
    method: 'post',
    data
  });
}
