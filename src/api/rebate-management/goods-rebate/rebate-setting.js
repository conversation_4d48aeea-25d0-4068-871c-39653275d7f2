import { withExtTenantIdRequest } from '@/utils/request';

export function listPage(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackActivity/list',
    method: 'post',
    data: data
  });
}

export function create(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackActivity/create',
    method: 'post',
    data: data
  });
}
export function update(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackActivity/update',
    method: 'post',
    data: data
  });
}
export function getDetail(id) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackActivity/get?id=' + id
  });
}
export function deleteData(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/creditBackActivity/delete`,
    method: 'post',
    data
  });
}
// 获取已经设置返利的商品id
export function listAllCommodityId(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackActivity/listAllCommodityId',
    method: 'post',
    data
  });
}

// 返利活动-获取活动快照详情 单品 月度写死
export function getSnapShootDetail(data) {
  const { creditBackActivityId, period, type = 'MONTH' } = data;
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/creditBackActivitySnapshoot/getSnapShootDetail?creditBackActivityId=${creditBackActivityId}&period=${period}&type=${type}`
  });
}
