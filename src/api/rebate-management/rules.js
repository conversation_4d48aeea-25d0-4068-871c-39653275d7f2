import { withExtTenantIdRequest } from '@/utils/request';

// 返利规则新增与修改
export function activityConfigInit(data) {
    return withExtTenantIdRequest({
      url: '/soyoungzg/api/activityConfig/init',
      method: 'post',
      data: data
    });
  }

// 查询返利规则
export function activityConfigGetByType(type) {
    return withExtTenantIdRequest({
      url: `/soyoungzg//api/activityConfig/getByType?type=${type}`,
      method: 'get',
    });
  }