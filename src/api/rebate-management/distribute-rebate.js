import { withExtTenantIdRequest } from '@/utils/request';

/**
 * 平台返利申请分页列表
 * https://apidoc.syounggroup.com/doc.html#/HERMES-%E5%88%86%E9%94%80%E7%AE%A1%E7%90%86/%E5%B9%B3%E5%8F%B0%E8%BF%94%E5%88%A9%E5%8F%91%E6%94%BE%E7%94%B3%E8%AF%B7/listPageUsingPOST_64
 */
export function distributorPlatformPolicyIssueApplyList(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorPlatformPolicyIssueApply/list',
    method: 'post',
    data: data
  });
}

/**
 * 发起OA流程
 * https://apidoc.syounggroup.com/doc.html#/HERMES-%E5%88%86%E9%94%80%E7%AE%A1%E7%90%86/%E5%B9%B3%E5%8F%B0%E8%BF%94%E5%88%A9%E5%8F%91%E6%94%BE%E7%94%B3%E8%AF%B7/startOAFlowUsingPOST
 */
export function distributorPlatformPolicyIssueApplyStartOAFlow(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorPlatformPolicyIssueApply/startOAFlow',
    method: 'post',
    data: data
  });
}

/**
 * 平台返利发放申请-导入发放分销商
 * https://apidoc.syounggroup.com/doc.html#/HERMES-%E5%88%86%E9%94%80%E7%AE%A1%E7%90%86/%E5%B9%B3%E5%8F%B0%E8%BF%94%E5%88%A9%E5%8F%91%E6%94%BE%E7%94%B3%E8%AF%B7/importIssueDistributorsUsingPOST
 */
export function distributorPlatformPolicyIssueApplyImportIssueDistributors(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorPlatformPolicyIssueApply/importIssueDistributors',
    method: 'post',
    data: data
  });
}

/**
 * 获取平台返利申请详情
 * https://apidoc.syounggroup.com/doc.html#/HERMES-%E5%88%86%E9%94%80%E7%AE%A1%E7%90%86/%E5%B9%B3%E5%8F%B0%E8%BF%94%E5%88%A9%E5%8F%91%E6%94%BE%E7%94%B3%E8%AF%B7/getUsingGET_37
 */
export function distributorPlatformPolicyIssueApplyGet(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorPlatformPolicyIssueApply/get?id=${id}`,
    method: 'get'
  });
}

/**
 * 平台返利发放列表-导出
 * https://apidoc.syounggroup.com/doc.html#/HERMES-%E5%88%86%E9%94%80%E7%AE%A1%E7%90%86/%E5%B9%B3%E5%8F%B0%E8%BF%94%E5%88%A9%E5%8F%91%E6%94%BE%E7%94%B3%E8%AF%B7/exportUsingPOST_5
 */
export function distributorPlatformPolicyIssueApplyExport(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorPlatformPolicyIssueApply/export',
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}

/**
 * PC-平台返利发放记录列表
 * https://apidoc.syounggroup.com/doc.html#/HERMES-%E5%88%86%E9%94%80%E7%AE%A1%E7%90%86/%E5%B9%B3%E5%8F%B0%E8%BF%94%E5%88%A9%E5%8F%91%E6%94%BE%E7%94%B3%E8%AF%B7/listPageForIssueRecordsUsingPOST
 */
export function distributorPlatformPolicyIssueApplyListPageForIssueRecords(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorPlatformPolicyIssueApply/listPageForIssueRecords',
    method: 'post',
    data
  });
}
