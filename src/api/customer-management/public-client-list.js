import { withExtTenantIdRequest } from '@/utils/request';
// 头部数据
export function getTodayStatistic(purchaseType) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/crm/getPublicStatistic?purchaseType=${purchaseType}`,
    method: 'get'
  });
}

// 客户公海池列表
export function listPublicMultiPage(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/crm/listPublicMultiPage',
    method: 'post',
    data
  });
}

// 客户公海池列表导出
export function exportPublicMulti(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/crm/exportPublicMulti',
    method: 'post',
    data,
    responseType: 'arraybuffer',
    timeout: 30 * 1000
  });
}

// 客户公海池列表-领取(批量)领取
export function drawInBatch(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/crm/drawInBatch',
    method: 'post',
    data
  });
}
