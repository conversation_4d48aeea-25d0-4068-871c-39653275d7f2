import { withExtTenantIdRequest } from '@/utils/request';
// 头部数据
export function getTodayStatistic (purchaseType) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/crm/getStatistic?purchaseType=${purchaseType}`,
    method: 'get'
  });
}

// 客户列表
export function listMultiPage (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/crm/listMultiPage',
    method: 'post',
    data
  });
}

// 导出
export function exportMulti (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/crm/exportMulti',
    method: 'post',
    data,
    responseType: 'arraybuffer',
    timeout: 30 * 1000
  });
}

// 获取店铺等级GET /api/distributorLevel/getBrief
export function getBrief (id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorLevel/getBrief?id=${id}`,
    method: 'get'
  });
}

// 修改店铺等级
export function updateLevel (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorLevel/updateLevel',
    method: 'post',
    data
  });
}
// 获取采购品牌列表
export function brandStatistics (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorOrderStatistic/listBrandSaleStatistic',
    method: 'post',
    data
  });
}
// 导出采购品牌列表
export function exportExcelBrandStatistics (data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorOrderStatistic/exportBrandSaleStatisticExcel`,
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}
// 获取商品采购明细列表
export function brandCommodityStatistics (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorOrderStatistic/listBrandCommoditySaleStatistic',
    method: 'post',
    data
  });
}
// 导出商品采购明细列表
export function exportExcelBrandCommodityStatistics (data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorPurchaseStatistics/exportExcelBrandCommodityStatistics`,
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}
// 获取采购商品列表
export function listCommoditySaleStatistic (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorOrderStatistic/listCommoditySaleStatistic',
    method: 'post',
    data
  });
}
// 导出采购商品列表
export function exportCommoditySaleStatisticExcel (data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorOrderStatistic/exportCommoditySaleStatisticExcel`,
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}
// 获取当月返点进度列表
export function listMonthProcess (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackStatistics/listMonthProcess',
    method: 'post',
    data
  });
}
// 当月返点进度列表-导出
export function exportMonthProcess (data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/creditBackStatistics/exportMonthProcess`,
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}

//  拓展客户管理统计数据
export function getStatistic () {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorExtInfo/getStatistic',
    method: 'get'
  });
}

// 拓展客户管理列表
export function distributorExtInfoList (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorExtInfo/list',
    method: 'post',
    data
  });
}

// 拓展客户管理列表-导出
export function distributorExtInfoExport (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorExtInfo/export',
    method: 'post',
    data,
    responseType: 'arraybuffer',
    timeout: 30 * 1000
  });
}
// 企业微信绑定记录
export function listWeachatPage (data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/crm/listWeachatPage`,
    method: 'post',
    data
  });
}