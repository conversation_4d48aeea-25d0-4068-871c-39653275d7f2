import { withExtTenantIdRequest } from '@/utils/request';

// 品类销售列表
export function listCategorySaleStatistic (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorOrderStatistic/listCategorySaleStatistic',
    method: 'post',
    data
  });
}

// 品类销售导出
export function exportCategorySaleStatisticExcel (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorOrderStatistic/exportCategorySaleStatisticExcel',
    method: 'post',
    data,
    responseType: 'arraybuffer',
    timeout: 30 * 1000
  });
}
// 获取品类名称
export function listCommodityParams () {
  return withExtTenantIdRequest({
    url: '/commodity/api/category/listAll',
    method: 'post',
    data: {}
  });
}
// 品类-商品采购明细列表
export function listCategoryCommoditySaleStatistic (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorOrderStatistic/listCategoryCommoditySaleStatistic',
    method: 'post',
    data
  });
}
// 商品采购明细导出
export function exportCategoryCommoditySaleStatisticExcel (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorOrderStatistic/exportCategoryCommoditySaleStatisticExcel',
    method: 'post',
    data,
    responseType: 'arraybuffer',
    timeout: 30 * 1000
  });
}
// 采购数据列表-按日
export function listDailyOrderStatisticPage (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorOrderStatistic/listDailyOrderStatisticPage',
    method: 'post',
    data
  });
}
// 采购数据列表-按月
export function listMonthOrderStatisticPage (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorOrderStatistic/listMonthOrderStatisticPage',
    method: 'post',
    data
  });
}
// 返点分布列表-月度
export function listDistributorMonthStatistic (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackStatistics/listDistributorMonthStatistic',
    method: 'post',
    data
  });
}
// 返点分布列表-季度
export function listDistributorQuarterStatistic (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackStatistics/listDistributorQuarterStatistic',
    method: 'post',
    data
  });
}
// 返点分布列表-月度-导出
export function exportDistributorMonthStatistic (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackStatistics/exportDistributorMonthStatistic',
    method: 'post',
    data,
    responseType: 'arraybuffer',
    timeout: 30 * 1000
  });
}
// 返点分布列表-季度-导出
export function exportDistributorQuarterStatistic (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackStatistics/exportDistributorQuarterStatistic',
    method: 'post',
    data,
    responseType: 'arraybuffer',
    timeout: 30 * 1000
  });
}
// 返点分布-采购明细
export function listDistributorCreditBack (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditBackStatistics/listDistributorCreditBack',
    method: 'post',
    data
  });
}
