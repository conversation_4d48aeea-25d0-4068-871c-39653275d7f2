import { withExtTenantIdRequest } from '@/utils/request';

// 品类销售列表
export function listOrderDeliveryFee (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/orderStatistics/listOrderDeliveryFee',
    method: 'post',
    data
  });
}

// 品类销售导出
export function exportOrderDeliveryFee (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/orderStatistics/exportOrderDeliveryFee',
    method: 'post',
    data,
    responseType: 'arraybuffer',
    timeout: 30 * 1000
  });
}
