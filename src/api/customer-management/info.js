import { withExtTenantIdRequest } from '@/utils/request';
// 获取客户资料详情：分销商信息,合作信息，客户资料,基础信息
export function getMultiDetail(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/crm/getMultiDetail?id=${id}`,
    method: 'get'
  });
}

// 获取分销商基本情况信息-包含专属顾问、店铺等级、合同情况、品牌授权情况、动销情况等    、、跟进管理使用
export function getDistributorBasicInfoVO(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributor/getDistributorBasicInfoVO?distributorId=${id}`,
    method: 'get'
  });
}

// 分销商信息-采购情况
export function getLatestPurchaseStatistic(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorStatistics/getLatestPurchaseStatistic?distributorId=${id}`,
    method: 'get'
  });
}

// 跟进记录、待办事项分页列表
export function gettodoPlanList(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/todoPlan/list`,
    method: 'post',
    data
  });
}

// 新增待办事项
export function settodoPlanCreate(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/todoPlan/create`,
    method: 'post',
    data
  });
}

// 修改待办事项
export function settodoPlanUpdate(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/todoPlan/update`,
    method: 'post',
    data
  });
}

// 新增跟进记录
export function createFinishedTodo(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/todoPlan/createFinishedTodo`,
    method: 'post',
    data
  });
}

// 新增跟进记录 v2
export function createLeadsManagerInBatch(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/todoPlan/createLeadsManagerInBatch`,
    method: 'post',
    data
  });
}

// 删除跟进记录
export function todoPlanDelete(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/todoPlan/delete?id=${id}`,
    method: 'post'
  });
}

// 完成待办事项
export function todoPlanFinish(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/todoPlan/finish`,
    method: 'post',
    data
  });
}

// 获取待办事项详情
export function getTodoPlanDetail(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/todoPlan/get?id=${id}`,
    method: 'get'
  });
}

// 违规记录列表
export function violateRecordList(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/violateRecord/list`,
    method: 'post',
    data
  });
}

// 删除违规记录
export function violateRecordDelete(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/violateRecord/delete?id=${id}`,
    method: 'post'
  });
}

// 更新店铺等级
export function updateLevel(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorLevel/updateLevel`,
    method: 'post',
    data
  });
}

// 修改分销商等级弹出窗口-获取信息
export function getBrief(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorLevel/getBrief?id=${id}`,
    method: 'get'
  });
}

// 补充完善意向品牌
export function improveInterestedBrand(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorExtInfo/improveInterestedBrand`,
    method: 'post',
    data
  });
}

// 获取分销商基础信息(分销商，专属客服)
export function getBasicInfo(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributor/getBasicInfo?id=${id}`,
    method: 'get'
  });
}

// 修改分销商采货类型、销量
export function updatePurchaseType(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributor/updatePurchaseType`,
    method: 'post',
    data
  });
}

// 运营端-客户管理-系统客户管理-专属顾问变动记录列表
export function listChangeRecord(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorCustomerServiceLog/listChangeRecord`,
    method: 'post',
    data
  });
}

// 运营端-客户管理-系统客户管理-客户领取记录列表
export function listDrawRecord(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorCustomerServiceLog/listDrawRecord`,
    method: 'post',
    data
  });
}

// 意向程度修改
export function updateExtInfoDistributorLabel(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorExtInfo/updateDistributorLabel`,
    method: 'post',
    data
  });
}

// 导出客户跟进记录
export function todoPlanExportExcel(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/todoPlan/exportExcel`,
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}

// 解绑企业微信
export function distributorUnBindDistributor(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributor/unBindDistributor`,
    method: 'post',
    data
  });
}