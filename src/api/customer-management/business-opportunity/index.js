import { withExtTenantIdRequest } from '@/utils/request';

// 获取店铺商机管理列表
export function getShopBusinessList(data = {}) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/shopBusiness/list',
    method: 'post',
    data
  });
}

// 店铺商机-验证 验证线索是否存在
export function getShopBusinessValidDistributorExtInfo(data = {}) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/shopBusiness/validDistributorExtInfo',
    method: 'post',
    data
  });
}

// 店铺商机-关联
export function getShopBusinessRelationDistributorExtInfo(data = {}) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/shopBusiness/relationDistributorExtInfo',
    method: 'post',
    data
  });
}

// 店铺商机-认领
export function shopBusinessReceive(data = {}) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/shopBusiness/receive',
    method: 'post',
    data
  });
}

// 店铺商机-转交
export function shopBusinessForward(data = {}) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/shopBusiness/forward',
    method: 'post',
    data
  });
}

// 店铺商机-无效
export function shopBusinessInvalid(data = {}) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/shopBusiness/invalid',
    method: 'post',
    data
  });
}
