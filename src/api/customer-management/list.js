import { withExtTenantIdRequest } from '@/utils/request';

// 查看授权进度
export function listBrandLicenseProcess(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/brandLicense/listBrandLicenseProcess`,
    method: 'post',
    data
  });
}

// 查看授权进度-详情列表
export function getlistBrandLicenseProcessDetail(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/brandLicense/listBrandLicenseProcessDetail`,
    method: 'post',
    data
  });
}

// 导出授权进度
export function exportExcel(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/brandLicense/exportBrandLicenseProcess`,
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}
