import {
  withExtTenantIdRequest
} from '@/utils/request';
// 数据概况-动销数据、分销商数据、销售数据、返点数据
export function list (data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/crm/home`,
    method: 'post',
    data
  });
}
// 数据概况-热销品牌
export function listBrandSaleStatistic (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/orderCommodityStatistics/listBrandSaleStatistic',
    method: 'post',
    data
  });
}

// 数据概况-热销商品
export function listCommoditySaleStatistic (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/orderCommodityStatistics/listCommoditySaleStatistic',
    method: 'post',
    data
  });
}
