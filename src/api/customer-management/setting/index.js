import { withExtTenantIdRequest } from '@/utils/request';

// 客户管理-基础设置-设置详情
export function getConfigDetail() {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorLevelConfig/getConfigDetail',
    method: 'get'
  });
}

// 客户管理-基础设置-新增店铺等级
export function create(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorLevelConfig/create',
    method: 'post',
    data
  });
}

// 客户管理-基础设置-修改店铺等级
export function update(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorLevelConfig/update',
    method: 'post',
    data
  });
}

// 客户管理-基础设置-删除店铺等级
export function deletes(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorLevelConfig/delete?id=${
      data.id
    }&toLevelId=${data.toLevelId || ''}`,
    method: 'post'
  });
}

// 客户管理-基础设置-获取等级下分销商人数
export function getLevelMember(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorLevelConfig/getLevelMember?id=${id}`,
    method: 'get'
  });
}

// 客户管理-基础设置-会员等级修改记录
export function listOperateLog(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorLevelConfig/listOperateLog',
    method: 'post',
    data
  });
}

// 客户管理-基础设置-删除其他标签设置
export function deleteLabel(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorLabelConfig/delete?id=${id}`,
    method: 'post'
  });
}

// 客户管理-基础设置-获取所有标签-类型必传
export function listAllLabel(data = { type: 'FAVORITE_COMMODITY' }) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorLabelConfig/listAll',
    method: 'post',
    data
  });
}

// 客户管理-基础设置-新增其他标签设置
export function saveLabel(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorLabelConfig/save',
    method: 'post',
    data
  });
}

// 获取分销商会员等级分页列表
export function listAllLevel(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorLevelConfig/listAll',
    method: 'post',
    data
  });
}

// 基础设置-新增/编辑公海规则
export function distributorChangeRuleUpdate(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorChangeRule/save',
    method: 'post',
    data
  });
}

// 基础设置-公海规则设置详情
export function distributorChangeRuleListAll(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorChangeRule/listAll',
    method: 'post',
    data
  });
}
