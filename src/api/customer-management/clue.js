import { withExtTenantIdRequest } from '@/utils/request';

// 获取线索管理列表
export function getClueList(data = {}) {
  return withExtTenantIdRequest({
    // url: '/soyoungzg/api/distributorLeadsManager/list',
    url: '/soyoungzg/api/distributorLeadsManager/listPage',
    method: 'post',
    data
  });
}

// 获取线索管理列表
export function getClue(data = {}) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorLeadsManager/get',
    method: 'post',
    params: data
  });
}

// 批量创建
export function createNormalInBatch(data = {}) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorLeadsManager/createNormalInBatch',
    method: 'post',
    data
  });
}

// 批量创建 - 国际
export function createGlobalInBatch(data = {}) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorLeadsManager/createGlobalInBatch',
    method: 'post',
    data
  });
}
// 批量创建线下线索
export function createOfflineInBatch(data = {}) {
  return withExtTenantIdRequest({
    url: '/soyoungzg//api/distributorLeadsManager/createOfflineInBatch',
    method: 'post',
    data
  });
}

// 判断是否存在
export function checkClueExist(data = {}) {
  data.contactPhone = data.mobile;
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorLeadsManager/validIsDuplicated',
    method: 'post',
    data
  });
}

// 删除
export function postClueDelete(data = {}) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorLeadsManager/delete',
    method: 'post',
    data
  });
}

// 修改
export function postClueUpdate(data = {}) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorLeadsManager/update',
    method: 'post',
    data
  });
}

// 获取线索详情
export function getClueDetail(id) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorLeadsManager/getDetail',
    method: 'get',
    params: { id }
  });
}

// 导入excel
export function uploadFile(data = {}) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorLeadsManager/uploadFileV2',
    method: 'post',
    data
  });
}

// 线索管理-批量跟进
export function createLeadsManagerInBatch(data = {}) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/todoPlan/createLeadsManagerInBatch',
    method: 'post',
    data
  });
}

// 待办事项-批量跟进
export function batchCompleteDetails(data = {}) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/crmTask/batchCompleteDetails',
    method: 'post',
    data
  });
}

// 线索管理-修改跟进
export function updateLeadsManager(data = {}) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/todoPlan/updateLeadsManager',
    method: 'post',
    data
  });
}

// 线索管理-删除跟进
export function deleteLeadsManager(id) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/todoPlan/delete',
    method: 'post',
    params: { id }
  });
}

// 线索管理-跟进列表
export function getListLeadsManager(data = {}) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/todoPlan/listLeadsManager',
    method: 'post',
    data
  });
}

// 运营任务设置-待办事项-列表查询
export function getListTaskDetails(data = {}) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/crmTask/listTaskDetails',
    method: 'post',
    data
  });
}

// 运营任务设置-待办事项-批量忽略
export function batchIgnoreDetail(data = {}) {
  return withExtTenantIdRequest({
    url: '/scrm-service/api/scrmTask/batchIgnoreDetail',
    method: 'post',
    data
  });
}

// 线索完善手机号码
export function improveClueMobile(data = {}) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorLeadsManager/improveMobile',
    method: 'post',
    data
  });
}

// 创建渠道线索
export function createChannelClue(data = {}) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorExtChannel/create',
    method: 'post',
    data
  });
}

// 获取渠道线索
export function getChannelClue(id) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorExtChannel/get',
    method: 'get',
    params: { id }
  });
}

// 修改渠道线索
export function updateChannelClue(data = {}) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorExtChannel/update',
    method: 'post',
    data
  });
}

// 删除渠道线索
export function deleteChannelClue(id) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorExtChannel/delete',
    method: 'post',
    params: { id }
  });
}

// 线索跟进导入
export function createLeadsManagerBatch(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/todoPlan/createLeadsManagerBatch `,
    method: 'post',
    data
  });
}

// 线索跟进导入校验
export function validLeadsManagerInBatch(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/todoPlan/validLeadsManagerInBatch',
    method: 'post',
    data
  });
}
