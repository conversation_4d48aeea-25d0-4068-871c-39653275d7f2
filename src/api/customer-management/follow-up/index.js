import { withExtTenantIdRequest } from '@/utils/request';
// 新增违规记录
export function createViolateRecord(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/violateRecord/create',
    method: 'post',
    data
  });
}
// 获取分销商网站白名单-下拉列表
export function getDistributorWebsiteWhitelist(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorWebsiteWhitelist/listAll',
    method: 'post',
    data
  });
}
// 补充完善资料
export function updateExtInfo(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorExtInfo/improve',
    method: 'post',
    data
  });
}

// 获取客户资料
export function getExtInfo(id) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorExtInfo/get?id=' + id,
    method: 'get'
  });
}

// 获取所有标签
export function getFavoriteCommodityList() {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorLabelConfig/listAll',
    method: 'post',
    data: {
      type: 'FAVORITE_COMMODITY'
    }
  });
}

//  跟进管理分销商统计数据

export function getStatisticList(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/todoPlan/statistic',
    method: 'post',
    data
  });
}

// 分销商待办事项统计列表
export function getDistributorList(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/todoPlan/listDistributorPage',
    method: 'post',
    data
  });
}

// 导出分销商待办事项统计列表
export function exportDistributorPage(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/todoPlan/exportDistributorPage',
    method: 'post',
    data,
    responseType: 'arraybuffer',
    timeout: 30 * 1000
  });
}

//  拓展跟进统计数据
export function developStatistic() {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/todoPlan/developStatistic',
    method: 'post'
  });
}

// 拓展跟进列表
export function listDevelopDistributorPage(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/todoPlan/listDevelopDistributorPage',
    method: 'post',
    data
  });
}

// 拓展跟列表-导出
export function exportDevelopDistributorPage(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/todoPlan/exportDevelopDistributorPage',
    method: 'post',
    data,
    responseType: 'arraybuffer',
    timeout: 30 * 1000
  });
}

// 拓展跟列表-导出
export function todoPlanExportLeadsManager(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/todoPlan/exportLeadsManager',
    method: 'post',
    data,
    responseType: 'arraybuffer',
    timeout: 30 * 1000
  });
}

// 待办事项列表-导出
export function todoListExportTaskDetails(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/crmTask/exportTaskDetails',
    method: 'post',
    data,
    responseType: 'arraybuffer',
    timeout: 30 * 1000
  });
}