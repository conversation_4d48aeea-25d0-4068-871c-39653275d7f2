import { withExtTenantIdRequest } from '@/utils/request';
// 头部数据
export function getTodayStatistic(purchaseType) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/crm/getTempStatistic?purchaseType=${purchaseType}`,
    method: 'get'
  });
}

// 客户临时池列表
export function listTempMultiPage(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/crm/listTempMultiPage',
    method: 'post',
    data
  });
}

// 客户临时池列表-导出
export function exportTempMulti(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/crm/exportTempMulti',
    method: 'post',
    data,
    responseType: 'arraybuffer',
    timeout: 30 * 1000
  });
}
