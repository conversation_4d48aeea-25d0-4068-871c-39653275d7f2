import { withExtTenantIdRequest } from '@/utils/request';

/**
 *
 * 政策列表
 * https://apidoc.syounggroup.com/doc.html#/HERMES-%E5%88%86%E9%94%80%E7%AE%A1%E7%90%86/RegularPolicy/listPageUsingPOST_108
 *
 */
export function regularPolicyList(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/regularPolicy/list',
    method: 'post',
    data
  });
}

/**
 *
 * 政策是否已配置
 * https://apidoc.syounggroup.com/doc.html#/HERMES-%E5%88%86%E9%94%80%E7%AE%A1%E7%90%86/RegularPolicy/existsPolicyUsingGET
 *
 */
export function regularPolicyExistsPolicy(data) {
  const { brandId = '', policyType = '' } = data;
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/regularPolicy/existsPolicy?brandId=${brandId}&policyType=${policyType}`,
    method: 'get',
    data
  });
}
/**
 *
 * PC-调整政策状态
 * https://apidoc.syounggroup.com/doc.html#/HERMES-%E5%88%86%E9%94%80%E7%AE%A1%E7%90%86/%E5%B8%B8%E8%A7%84%E6%94%BF%E7%AD%96%E8%BF%90%E8%90%A5%E7%AB%AF%E9%85%8D%E7%BD%AE/changeStatusUsingPOST_2
 *
 */
export function regularPolicyChangeStatus(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/regularPolicy/changeStatus',
    method: 'post',
    data
  });
}

/**
 *
 * 调整政策展示状态
 * https://apidoc.syounggroup.com/doc.html#/HERMES-%E5%88%86%E9%94%80%E7%AE%A1%E7%90%86/%E5%B8%B8%E8%A7%84%E6%94%BF%E7%AD%96%E8%BF%90%E8%90%A5%E7%AB%AF%E9%85%8D%E7%BD%AE/changeShowStatusUsingPOST
 *
 */
export function regularPolicyChangeShowStatus(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/regularPolicy/changeShowStatus',
    method: 'post',
    data
  });
}

/**
 *
 * 销售政策查看
 * https://apidoc.syounggroup.com/doc.html#/HERMES-%E5%88%86%E9%94%80%E7%AE%A1%E7%90%86/RegularPolicy/getSalePolicyUsingGET
 *
 */
export function regularPolicyGetSalePolicy(id) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/regularPolicy/getSalePolicy?id=' + id,
    method: 'get'
  });
}

/**
 *
 * 销售政策新增
 * https://apidoc.syounggroup.com/doc.html#/HERMES-%E5%88%86%E9%94%80%E7%AE%A1%E7%90%86/%E5%B8%B8%E8%A7%84%E6%94%BF%E7%AD%96%E8%BF%90%E8%90%A5%E7%AB%AF%E9%85%8D%E7%BD%AE/createSalePolicyUsingPOST
 *
 */
export function regularPolicyCreateSalePolicy(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/regularPolicy/createSalePolicy',
    method: 'post',
    data
  });
}

/**
 *
 * 销售政策编辑
 * https://apidoc.syounggroup.com/doc.html#/HERMES-%E5%88%86%E9%94%80%E7%AE%A1%E7%90%86/%E5%B8%B8%E8%A7%84%E6%94%BF%E7%AD%96%E8%BF%90%E8%90%A5%E7%AB%AF%E9%85%8D%E7%BD%AE/updateSalePolicyUsingPOST
 *
 */
export function regularPolicyUpdateSalePolicy(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/regularPolicy/updateSalePolicy',
    method: 'post',
    data
  });
}

/**
 *
 * 结算政策查看
 * https://apidoc.syounggroup.com/doc.html#/HERMES-%E5%88%86%E9%94%80%E7%AE%A1%E7%90%86/RegularPolicy/getSettlementPolicyUsingGET
 *
 */
export function regularPolicyGetSettlementPolicy(id) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/regularPolicy/getSettlementPolicy?id=' + id,
    method: 'get'
  });
}

/**
 *
 * 结算政策新增
 * https://apidoc.syounggroup.com/doc.html#/HERMES-%E5%88%86%E9%94%80%E7%AE%A1%E7%90%86/RegularPolicy/settlementPolicyAddUsingPOST
 *
 */
export function regularPolicySettlementCreateSettlementPolicy(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/regularPolicy/createSettlementPolicy',
    method: 'post',
    data
  });
}

/**
 *
 * 结算政策编辑
 * https://apidoc.syounggroup.com/doc.html#/HERMES-%E5%88%86%E9%94%80%E7%AE%A1%E7%90%86/%E5%B8%B8%E8%A7%84%E6%94%BF%E7%AD%96%E8%BF%90%E8%90%A5%E7%AB%AF%E9%85%8D%E7%BD%AE/updateSettlementPolicyUsingPOST
 *
 */
export function regularPolicyUpdateSettlementPolicy(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/regularPolicy/updateSettlementPolicy',
    method: 'post',
    data
  });
}

/**
 *
 * 政策发放列表
 * https://apidoc.syounggroup.com/doc.html#/HERMES-%E5%88%86%E9%94%80%E7%AE%A1%E7%90%86/%E7%BB%93%E7%AE%97%E5%8D%95/listPageUsingPOST_108
 *
 */
export function regularPolicySettlementList(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/regularPolicySettlement/list',
    method: 'post',
    data
  });
}
/**
 *
 * 人工调整采货金额
 * https://apidoc.syounggroup.com/doc.html#/HERMES-%E5%88%86%E9%94%80%E7%AE%A1%E7%90%86/%E7%BB%93%E7%AE%97%E5%8D%95/adjustPurchaseAmountUsingPOST
 *
 */
export function regularPolicySettlementAdjustPurchaseAmount(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/regularPolicySettlement/adjustPurchaseAmount',
    method: 'post',
    data
  });
}
/**
 *
 * 人工调整返利金额
 * https://apidoc.syounggroup.com/doc.html#/HERMES-%E5%88%86%E9%94%80%E7%AE%A1%E7%90%86/%E7%BB%93%E7%AE%97%E5%8D%95/adjustCreditBackUsingPOST
 *
 */
export function regularPolicySettlementAdjustCreditBack(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/regularPolicySettlement/adjustCreditBack',
    method: 'post',
    data
  });
}
/**
 *
 * 上传凭证
 * https://apidoc.syounggroup.com/doc.html#/HERMES-%E5%88%86%E9%94%80%E7%AE%A1%E7%90%86/%E7%BB%93%E7%AE%97%E5%8D%95/uploadAttachmentUsingPOST
 *
 */
export function regularPolicySettlementUploadAttachment(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/regularPolicySettlement/uploadAttachment',
    method: 'post',
    data
  });
}
/**
 *
 * 重新计算结算单
 * https://apidoc.syounggroup.com/doc.html#/HERMES-%E5%88%86%E9%94%80%E7%AE%A1%E7%90%86/%E7%BB%93%E7%AE%97%E5%8D%95/reCalSettlementOrderUsingPOST_1
 *
 */
export function regularPolicySettlementReCalSettlementOrder(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/regularPolicySettlement/reCalSettlementOrder',
    method: 'post',
    data
  });
}
/**
 *
 * 查看采购明细
 * https://apidoc.syounggroup.com/doc.html#/HERMES-%E5%88%86%E9%94%80%E7%AE%A1%E7%90%86/%E7%BB%93%E7%AE%97%E5%8D%95/listSettlementOrderItemUsingPOST_1
 *
 */
export function regularPolicySettlementListSettlementOrderItem(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/regularPolicySettlement/listSettlementOrderItem',
    method: 'post',
    data
  });
}
/**
 *
 * 导出结算订单明细
 * https://apidoc.syounggroup.com/doc.html#/HERMES-%E5%88%86%E9%94%80%E7%AE%A1%E7%90%86/%E7%BB%93%E7%AE%97%E5%8D%95/settlementOrderItemExportExcelUsingPOST_1
 */
export function regularPolicySettlementExportExcelDetail(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/regularPolicySettlement/regularSettlementOrderItem/exportExcel',
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}
/**
 *
 * 专属顾问审批结算单
 * https://apidoc.syounggroup.com/doc.html#/HERMES-%E5%88%86%E9%94%80%E7%AE%A1%E7%90%86/%E7%BB%93%E7%AE%97%E5%8D%95/settlementOrderItemExportExcelUsingPOST_1
 */
export function regularPolicySettlementApprovalSettlement(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/regularPolicySettlement/approvalSettlement',
    method: 'post',
    data
  });
}
/**
 *
 * 品牌审批结算单
 * https://apidoc.syounggroup.com/doc.html#/HERMES-%E5%88%86%E9%94%80%E7%AE%A1%E7%90%86/%E7%BB%93%E7%AE%97%E5%8D%95/brandApprovalSettlementUsingPOST
 */
export function regularPolicySettlementBrandApprovalSettlement(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/regularPolicySettlement/brandApprovalSettlement',
    method: 'post',
    data
  });
}
/**
 *
 * 统计实收金额合计
 * https://apidoc.syounggroup.com/doc.html#/HERMES-%E5%88%86%E9%94%80%E7%AE%A1%E7%90%86/%E7%BB%93%E7%AE%97%E5%8D%95/statSettlementOrderTotalAmountUsingPOST_1
 */
export function regularPolicySettlementStatSettlementOrderTotalAmount(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/regularPolicySettlement/statSettlementOrderTotalAmount',
    method: 'post',
    data
  });
}