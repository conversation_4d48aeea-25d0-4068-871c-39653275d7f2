import { withExtTenantIdRequest } from '@/utils/request';

// -------------------------积分管理-----------------------

// 获取分销商积分管理列表
export function listPage (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/customerCreditAccount/list',
    method: 'post',
    data
  });
}
// 积分导出
export function customerCreditAccountExport (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/customerCreditAccount/export',
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}
// 获取积分基础规则
export function getCreditRule () {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditRule/getCreditRule',
    method: 'get'
  });
}

// 获取分销商积分账户明细列表
export function listDetailForPC (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/customerCreditAccount/listDetailForPC',
    method: 'post',
    data
  });
}

// 调整积分
export function adjustPoint (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/customerCreditAccount/adjustPoint',
    method: 'post',
    data
  });
}

// 设置积分规则
export function setCreditRule (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/creditRule/setCreditRule',
    method: 'post',
    data
  });
}

// 导出积分明细列表
export function exportCreditAccountDetail (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/customerCreditAccount/exportCreditAccountDetail',
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}

// ----------------------分销商等级---------------------

// 导出分销商等级列表
export function exportExcel (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorGrade/exportGrade',
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}

// 调整单个分销商等级分数
export function changeScore (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorGrade/changeScore',
    method: 'post',
    data
  });
}

// 批量调整分销商等级分数
export function changeScoreInBatch (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorGrade/changeScoreInBatch',
    method: 'post',
    data
  });
}

// 获取分销商等级记录列表
export function listGradeChange (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorGrade/listGradeChange',
    method: 'post',
    data
  });
}

// 导出分销商等级记录
export function exportGradeChange (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorGrade/exportGradeChange',
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}

// 获取分销商等级分数调整记录列表
export function listGradeScoreDetail (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorGrade/listGradeScoreDetail',
    method: 'post',
    data
  });
}

// 导出分销商等级分数调整记录
export function exportGradeScoreDetail (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorGrade/exportGradeScoreDetail',
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}

// 获取分销商等级设置
export function listSettings () {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorGrade/listSettings',
    method: 'post'
  });
}
