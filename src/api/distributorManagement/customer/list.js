import { withExtTenantIdRequest } from '@/utils/request';

// 分销商列表
export function list(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/customerService/list',
    method: 'post',
    data
  });
}
// 员工分组列表
export function listAll(data) {
  return withExtTenantIdRequest({
    url: '/ocean/api/shopStaffGroup/listAll',
    method: 'post',
    data
  });
}
// 编辑保存
export function update(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/customerService/update',
    method: 'post',
    data
  });
}
// 新增保存
export function create(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/customerService/create',
    method: 'post',
    data
  });
}
// 通过id查询分销商信息信息
export function getById(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/customerService/get?id=${id}`,
    method: 'get'
  });
}
// 删除
export function deleteById(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/customerService/delete?id=${id}`,
    method: 'post'
  });
}
// 交接
export function handOver(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/customerService/handOver?fromCsId=${data.fromCsId}&toCsId=${data.toCsId}`,
    method: 'post'
  });
}

// 获取专属顾问标签
export function getAdviserTagList(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/csDefineMark/listAll',
    method: 'post',
    data
  });
}

// 获取专属顾问标签
export function createAdviserTag(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/csDefineMark/create',
    method: 'post',
    data
  });
}

// 获取分销商标签
export function getDistributorTagList(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorMark/listByDistributor',
    method: 'post',
    data
  });
}

// 获取分销商标签
export function createDistributorTag(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorMark/createBatch',
    method: 'post',
    data
  });
}

// 获取企业微信标签
export function getWechatTagList(data) {
  return withExtTenantIdRequest({
    url: '/scrm-service/api/qywxCustomerSidebar/listMyQywxTags',
    method: 'post',
    data
  });
}

// 验证专属顾问企业微信用户是否存在(不存在：false,存在：true
export function customerServiceValidCsQywxUser(data, type) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/customerService/validCsQywxUser',
    method: 'post',
    data,
    headers: {
      // 直供、国际二级租户
      'X-Ext-Tenant-Id': process.env.VUE_APP_X_EXT_TENANT_ID_GJ
    }
  });
}
