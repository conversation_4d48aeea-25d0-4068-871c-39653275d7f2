import { withExtTenantIdRequest } from '@/utils/request';

// 分销商企业主体信息-列表
export function list(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorContractInfo/list',
    method: 'post',
    data
  });
}

// 获取分销商合同主体信息(包含开票主体和收付款主体)
export function distributorContractInfoListAllInfo(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorContractInfo/listAllInfo',
    method: 'post',
    data
  });
}

// 分销商企业主体信息-新增
export function createForPC(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorContractInfo/createForPC',
    method: 'post',
    data
  });
}

// 分销商企业主体信息-修改
export function updateForPC(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorContractInfo/updateForPC',
    method: 'post',
    data
  });
}

// 分销商企业主体信息-详情
export function getDetail(distributorContractInfoId, id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorContractInfo/get?id=${distributorContractInfoId}&relationId=${id}`,
    method: 'get'
  });
}

// 获取Ai主体解析内容
export function distributorContractExternalGetParticipantInfoByAi(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorContractExternal/getParticipantInfoByAi',
    method: 'post',
    data
  });
}

// 分销商shopName
export function getBasicInfo(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributor/getBasicInfo?id=${id}`,
    method: 'get'
  });
}

// 获取简易分销商合同主体信息
export function distributorContractInfoListBrief(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorContractInfo/listBrief',
    method: 'post',
    data
  });
}
// 收款账户信息-单个信息编辑接口
export function distributorBankCardUpdate(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorBankCard/update',
    method: 'post',
    data
  });
}
// 签署合同-选择主体信息-创建收款账户
export function distributorBankCardCreateByContractInfo(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorBankCard/createByContractInfo',
    method: 'post',
    data
  });
}

// 签署合同-选择主体信息-查询主体信息下的收款信息
export function distributorBankCardListByContractInfoId(distributorContractInfoId) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorBankCard/listByContractInfoId?distributorContractInfoId=${distributorContractInfoId}`,
    method: 'get'
  });
}
// 获取银行卡列表（已绑定、已启用）
export function distributorBankCardListEnableByContractInfoId(distributorContractInfoId) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorBankCard/listEnableByContractInfoId?distributorContractInfoId=${distributorContractInfoId}`,
    method: 'get'
  });
}

// 分销商信息-收付款主体-获取收付款列表（新）
export function distributorBankCardListPageForPC(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorBankCard/listPageForPC',
    method: 'post',
    data
  });
}

// 分销商信息-收付款主体-编辑
export function distributorBankCardUpdateForPC(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorBankCard/updateForPC',
    method: 'post',
    data
  });
}

// 分销商信息-收付款主体-导出
export function distributorBankCardExportBankCard(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorBankCard/exportBankCard',
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}

// 分销商信息-收付款主体-补充修改提现资料
export function distributorBankCardUpdateAttachment(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorBankCard/updateAttachment',
    method: 'post',
    data
  });
}

// 分销商信息-收付款主体-查询主体信息列表
export function distributorContractInfoListByBankCard(distributorId) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorContractInfo/listByBankCard?distributorId=${distributorId}`,
    method: 'get'
  });
}

// 分销商信息-收付款主体-禁用启用接口
export function distributorBankCardUpdateStatus(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorBankCard/updateStatus',
    method: 'post',
    data
  });
}

// 获取分销商合同主体信息信息-运营端
export function distributorContractInfoListPage(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorContractInfo/listPage',
    method: 'post',
    data
  });
}

// 合同管理-列表导出
export function exportExcel(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorContractInfo/exportContractInfo`,
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}

// （运营端）完善主数据渠道信息
export function updateChannelInfo(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorContractInfo/updateChannelInfo',
    method: 'post',
    data
  });
}

// 分销商信息-合同/授权主体-oms渠道下拉查询
export function listOmsChannelInfo(data = {}) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/externalChannel/listOmsChannelInfo',
    method: 'post',
    data
  });
}

// 分销商信息-合同/授权主体-sap渠道下拉查询
export function listSapChannelInfo(data = {}) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/externalChannel/listSapChannelInfo',
    method: 'post',
    data
  });
}

// 新增银行卡-运营端
export function distributorBankCardCreateForPC(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorBankCard/createForPC',
    method: 'post',
    data
  });
}

// 获取银行卡列表
export function listByDistributor(distributorId) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorBankCard/listByDistributor?distributorId=${distributorId}`,
    method: 'get'
  });
}

// 分销商信息-合同/授权主体-编辑-获取银行卡列表（未绑定）（新）
export function listUnbind(distributorId) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorBankCard/listUnbind?distributorContractInfoId=${distributorId}`,
    method: 'get'
  });
}

export function bindBankCardList(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorContractInfo/bindBankCardList`,
    method: 'post',
    data
  });
}

// 获取发票主体列表
export function invoiceTitle(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/invoiceTitle/listPageForPC`,
    method: 'post',
    data
  });
}

// 发票主体-列表导出
export function invoiceExportExcel(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/invoiceTitle/exportInvoiceTitle`,
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}

// 获取发票主体列表
export function invoiceTitleGetById(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg//api/invoiceTitle/getById?id=${id}`,
    method: 'get'
  });
}

// 分销商信息-发票主体-更新发票主体信息（新）
export function invoiceTitleUpdateForPC(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/invoiceTitle/updateForPC`,
    method: 'post',
    data
  });
}

// 获取简易分销商发票主体信息
export function invoiceTitleListBrief(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/invoiceTitle/listBrief`,
    method: 'post',
    data
  });
}
// 同主体列表查询(处理了个人和企业的名称)-运营端
export function distributorContractInfoListBriefForPc(data = {}) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorContractInfo/listBriefForPc`,
    method: 'post',
    data
  });
}

// 获取乙方主体下面的有效合同信息
export function distributorContractInfoListValidContractByInfoIds(data = {}) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorContractInfo/listValidContractByInfoIds',
    method: 'post',
    data
  });
}

// 预览OA流程信息
export function distributorPartyRelationFlowGetPartyRelationFlowInfo(data = {}) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorPartyRelationFlow/getPartyRelationFlowInfo',
    method: 'post',
    data
  });
}

// 获取OA流程日志信息
export function distributorPartyRelationFlowListPartyRelationFlowLog(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorPartyRelationFlow/listPartyRelationFlowLog?id=${id}`,
    method: 'get'
  });
}

// 完善渠道信息-水羊直供一键获取渠道信息
export function distributorContractInfoGetBindChannelInfoBySyzg(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorContractInfo/getBindChannelInfoBySyzg',
    method: 'post',
    data
  });
}

// 完善渠道信息-水羊直供一键关联渠道
export function distributorContractInfoAutoBindChannelInfoBySyzg(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorContractInfo/autoBindChannelInfoBySyzg',
    method: 'post',
    data
  });
}

// 完善主数据渠道信息-自建渠道-提交OA表单字段
export function distributorPartyRelationFlowSaveSelfFlow(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorPartyRelationFlow/saveSelfFlow',
    method: 'post',
    data,
    autoErrorMsg: false
  });
}

// 完善主数据渠道信息-自建渠道-预览表单字段/建档表单信息
export function distributorPartyRelationFlowGetFlowForm(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorPartyRelationFlow/getFlowForm',
    method: 'post',
    data
  });
}

// 完善主数据渠道信息-已完成建档的渠道信息列表
export function distributorPartyRelationFlowListFinishFlowPage(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorPartyRelationFlow/listFinishFlowPage',
    method: 'post',
    data
  });
}

// 完善主数据渠道信息-建档申请中渠道信息列表
export function distributorPartyRelationFlowListUnFinishFlowPage(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorPartyRelationFlow/listUnFinishFlowPage',
    method: 'post',
    data
  });
}

// 完善主数据渠道信息-查询已关联的建档渠道
export function distributorPartyRelationListRelationPage(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorPartyRelation/listRelationPage',
    method: 'post',
    data
  });
}

// 完善主数据渠道信息-查看流程日志（自建）
export function distributorPartyRelationFlowListFlowLogPage(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorPartyRelationFlow/listFlowLogPage',
    method: 'post',
    data
  });
}

// 完善主数据渠道信息-自建渠道-建档表单信息-渠道下拉列表
export function distributorPartyRelationFlowListFlowChannel(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorPartyRelationFlow/listFlowChannel',
    method: 'post',
    data
  });
}

// 完善主数据渠道信息-基础信息-启用
export function distributorPartyRelationEnabled(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorPartyRelation/enabled',
    method: 'post',
    data
  });
}

// 完善主数据渠道信息-基础信息-禁用
export function distributorPartyRelationStopped(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorPartyRelation/stopped',
    method: 'post',
    data
  });
}

// 完善主数据渠道信息-基础信息-获取主数据客户简称信息
export function distributorContractInfoGetMainCustomerSimpleName(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorContractInfo/getMainCustomerSimpleName',
    method: 'post',
    data
  });
}

// 查询主数据成本中心接口(暂不用传查询条件)
export function distributorPartyRelationListMainCostCenter(data = {}) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorPartyRelation/listMainCostCenter',
    method: 'post',
    data
  });
}
