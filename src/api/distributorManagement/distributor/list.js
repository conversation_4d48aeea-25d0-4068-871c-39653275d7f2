import { withExtTenantIdRequest } from '@/utils/request';

// 分销商列表
export function list(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributor/list',
    method: 'post',
    data
  });
}

// 分销商列表（获取简易分销商分页列表-包含三方分销商、平台分销商）
export function listBrief(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributor/listBrief',
    method: 'post',
    data
  });
}

// 审核
export function audit(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributor/audit`,
    method: 'post',
    data
  });
}
// 状态字典
export function fetchStatusOptions() {
  return withExtTenantIdRequest({
    url: '/common/api/dict/listByType?type=soyoungzg_audit_status',
    method: 'get'
  });
}
// 线上线下字典
export function fetchChannelType() {
  return withExtTenantIdRequest({
    url: '/common/api/dict/listByType?type=soyoungzg_channel_type',
    method: 'get'
  });
}
// 了解渠道
export function fetchKnowChannel() {
  return withExtTenantIdRequest({
    url: '/common/api/dict/listByType?type=soyoungzg_know_channel',
    method: 'get'
  });
}
// 渠道名称字典
export function fetchChannel(type) {
  return withExtTenantIdRequest({
    url: `/common/api/dict/listByType?type=${type}`,
    method: 'get'
  });
}

// 专属顾问列表
export function customerList(data = {}) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/customerService/listAll',
    method: 'post',
    data
  });
}

// 冻结分销商
export function disable(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributor/disable',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json'
    }
  });
}
// 解冻分销商
export function enable(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributor/enable',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json'
    }
  });
}

// 导出分销商
export function exportExcel(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributor/exportExcel`,
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}

// 通过id查询分销商信息信息
export function getById(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributor/get?id=${id}`,
    method: 'get'
  });
}

// 后台手动创建分销商
export function distributorCreate(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributor/create',
    method: 'post',
    data
  });
}

// 基本信息编辑
export function update(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributor/update',
    method: 'post',
    data
  });
}

// 分销商客户类型编辑
export function updateDistributorType(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributor/updateDistributorType',
    method: 'post',
    data
  });
}

// 渠道授权信息
export function getChannelAuthorization(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorBrandChannelAuthorization/getChannelAuthorization?distributorId=${id}`,
    method: 'get'
  });
}

// 渠道授权信息编辑
export function saveChannelAuthorization(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorBrandChannelAuthorization/save',
    method: 'post',
    data
  });
}

// 获取分销商默认发票抬头
export function getDistributorDefault(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/invoiceTitle/getDistributorDefault?distributorId=${id}`,
    method: 'get'
  });
}

// 新增分销商默认发票抬头
// export function createForDistributor(data) {
//   return withExtTenantIdRequest({
//     url: '/soyoungzg/api/invoiceTitle/createForDistributor',
//     method: 'post',
//     data
//   });
// }

// 分销商默认发票抬头编辑
export function updateForDistributor(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/invoiceTitle/updateForDistributor',
    method: 'post',
    data
  });
}

// 开启特供采货
export function enableSpecialBuy(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributor/enableSpecialBuy',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json'
    }
  });
}

// 关闭特供采货
export function disableSpecialBuy(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributor/disableSpecialBuy',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json'
    }
  });
}

// 运营端获取商家获取已授权品牌列表
export function getDistributorAuthorizedBrandList(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorBrandAuthorization/getDistributorAuthorizedBrandList?distributorId=${id}`,
    method: 'get'
  });
}

// 合同列表
export function listContract(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorContract/list',
    method: 'post',
    data
  });
}
// 创建采销合同
export function createDistributorContract(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorContract/createDistributorContract',
    method: 'post',
    data
  });
}
// 补充授权渠道
export function addEmpower(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorContract/updateContractChannels',
    method: 'post',
    data
  });
}
// 创建赊销合同
export function createSaleCreditContract(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorContract/createSaleCreditContract',
    method: 'post',
    data
  });
}

// 创建线下合同
export function createOffLineContract(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorContract/createOffLineContract',
    method: 'post',
    data
  });
}

// 撤销签署中合同
export function revokeSigningContract(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorContract/revokeSigningContract?id=${id}`,
    method: 'get'
  });
}

// 终止合同
export function stopContract(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorContract/stopContract?id=${id}`,
    method: 'get'
  });
}

// 查询合同信息
export function getContractDetail(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorContractInfo/getDetail?id=${id}`,
    method: 'get'
  });
}
// 根据企业名称获取企业详细信息
export function queryByName(val) {
  return withExtTenantIdRequest({
    url: `/ocean/api/companyIdentity/queryByName?companyName=${val}`,
    method: 'post'
  });
}

// 根据企业名称获取企业详细信息  （只有开票时用）
export function queryInvoiceTitleInfoByName(val) {
  return withExtTenantIdRequest({
    url: `/ocean/api/companyIdentity/queryInvoiceTitleInfoByName?companyName=${val}`,
    method: 'post'
  });
}
// 获取分销商经营渠道信息
export function getDistributorChannel(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributor/getDistributorChannel?distributorId=${id}`,
    method: 'get'
  });
}

// 获取分销商冻结记录列表
export function getListDistributorFreezeLog(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributor/listDistributorFreezeLog`,
    method: 'post',
    data
  });
}

// 获取分销商冻结记录列表
export function updateDistributorFreezeLog(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributor/updateDistributorFreezeLog`,
    method: 'post',
    data
  });
}

// 冻结分销商
export function postDisable(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributor/disable`,
    method: 'post',
    data
  });
}

// 修改分销商备注
export function updateApproveMsg(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributor/updateApproveMsg`,
    method: 'post',
    data
  });
}

// 删除白名单店铺
export function deleteWhiteList(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorWebsiteWhitelist/delete?id=${id}`,
    method: 'post'
  });
}
// 更新白名单店铺
export function updateWhiteList(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorWebsiteWhitelist/update`,
    method: 'post',
    data
  });
}
// 更新白名单店铺管理的申请
export function whiteListUpdate(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorWebsiteWhitelist/update`,
    method: 'post',
    data
  });
}

// 查询审核分销商重复
export function checkRepeatDistributor(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributor/checkRepeat', // `/soyoungzg/api/distributor/checkRepeatDistributor`,
    method: 'post',
    data
  });
}

// 查询导入分销商重复
export function checkRepeatImportDistributor(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributor/checkRepeatImportDistributor`,
    method: 'post',
    data
  });
}

// 分销商重复列表
export function listRepeatDistributor(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributor/listPageAll`,
    method: 'post',
    data
  });
}

// 分销商专属顾问关系-分配
export function distributorCsRelationAssign(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorCsRelation/assign`,
    method: 'post',
    data
  });
}

// 获取外部渠道
export function getExternalChannelList(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributor/externalChannel/list`,
    method: 'post',
    data
  });
}

// 批量编辑拓展顾问
export function batchUpdateDevelopCs(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorCsRelation/batchUpdateDevelopCs`,
    method: 'post',
    data
  });
}

// 批量分配
export function batchAssign(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorCsRelation/batchAssign`,
    method: 'post',
    data
  });
}

// 批量编辑专属顾问
export function batchUpdateCs(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorCsRelation/batchUpdateCs`,
    method: 'post',
    data
  });
}

// 分销商建群
export function establishGroup(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributor/establishGroup`,
    method: 'post',
    data
  });
}

// 分销商取消建群
export function establishGroupCancel(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributor/establishGroupCancel`,
    method: 'post',
    data
  });
}

// 分销商列表-分销商详情-查询首单金额及首单时间
export function getDistributorFirstOrderInfoVO(id) {
  return withExtTenantIdRequest({
    url: `soyoungzg/api/distributor/getDistributorFirstOrderInfoVO?id=${id}`,
    method: 'get'
  });
}

// 导出分销商授权进度
export function exportBrandLicenseProcess(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/brandLicense/exportBrandLicenseProcess`,
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}

// 分销商店铺白名单
export function listPageByDistributor_shopWhite(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorWebsiteWhitelist/listPageByDistributor`,
    method: 'post',
    data
  });
}

// 分销商操作日志分页列表
export function distributorOperationLogList(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorOperationLog/list`,
    method: 'post',
    data
  });
}
// 分销商信息-进销存管控变动记录-分页列表查询
export function distributorPsiControlChangeLogList(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorPsiControlChangeLog/list`,
    method: 'post',
    data
  });
}

// 分销商是否有年框合同
export function distributorContractExternalListDistributorYearlyContract(distributorId) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorContractExternal/listDistributorYearlyContract?distributorId=${distributorId}`,
    method: 'get'
  });
}

// 飞书合同认领到分销商获取匹配信息
export function distributorContractExternalGetMatchDistributorInfo(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorContractExternal/getMatchDistributorInfo',
    method: 'post',
    data
  });
}

// 获取简易乙方信息
export function distributorContractInfoListBriefInfo(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorContractInfo/listBriefInfo',
    method: 'post',
    data
  })
}

// 补充年框合同
export function distributorContractSupplementYearlyBrand(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorContract/supplementYearlyBrand',
    method: 'post',
    data
  });
}

// 获取银行卡列表（已绑定）
export function listByContractInfoId(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorBankCard/listEnableByContractInfoId?distributorContractInfoId=${id}`,
    method: 'get'
  });
}
// 根据多个主体ID获取银行卡列表(已绑定、已启用)
export function listEnableByContractInfoIds(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorBankCard/listEnableByContractInfoIds',
    data,
    method: 'post'
  });
}

// 通过主体信息创建收款账户
export function createByContractInfo(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorBankCard/createByContractInfo',
    method: 'post',
    data
  });
}

export function supplementBankCard(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorContract/supplementBankCard',
    method: 'post',
    data
  });
}

// 查询外部sap列表
export function externalChannelListSap(data = {}) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/externalChannel/listSap',
    method: 'post',
    data
  });
}

// 查询可新增的SAP渠道
export function externalChannelListCreateManualChannelInfo(data = {}) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/externalChannel/listCreateManualChannelInfo',
    method: 'post',
    data
  });
}

// 查询外部渠道列表
export function externalChannelList(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/externalChannel/list',
    method: 'post',
    data
  });
}

export function matchDistributor(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorContractExternal/matchDistributor',
    method: 'post',
    data
  });
}

// 分销商管理-合同管理-未匹配的飞书合同-关联主体【新】
export function listDistributorPartyRelation(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorContractExternal/listDistributorPartyRelation',
    method: 'post',
    data
  });
}

// 下载批量导入失败记录
export function exportBatchDrawFailedRecord(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/couponDraw/exportBatchDrawFailedRecord',
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}

// 保存分销商的联系人信息
export function updateBatch(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorContactInfo/updateBatch',
    method: 'post',
    data
  });
}

// 获取分销商的联系方式
export function listByDistributor(distributorId) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorContactInfo/listByDistributorId?distributorId=' + distributorId,
    method: 'get'
  });
}

// 批量冻结分销商
export function distribtuorSaveImportRecord(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributor/saveImportRecord',
    method: 'post',
    data
  });
}

// 获取mars所有的渠道和品牌
export function customerAccountListMarsAccountChannel() {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/customerAccount/listMarsAccountChannel',
    method: 'get'
  });
}

// 修改分销商参与积分体系管控
export function distribtuorUpdateJoinCredit(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributor/updateJoinCredit',
    method: 'post',
    data
  });
}

// 获取分销商客情说明
export function distributorGetCustomerDescription(id) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributor/getCustomerDescription?id=' + id,
    method: 'get'
  });
}

// 保存分销商客情说明
export function distributorUpdateCustomerDescription(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributor/updateCustomerDescription',
    method: 'post',
    data
  });
}
// 获取分销商地址
export function distributorAddressListByDistributorId(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorAddress/listByDistributorId',
    method: 'post',
    data
  });
}

// 批量编辑分销商地址
export function distributorAddressUpdateBatch(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorAddress/updateBatch',
    method: 'post',
    data
  });
}
