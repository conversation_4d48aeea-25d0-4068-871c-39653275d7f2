import { withExtTenantIdRequest } from '@/utils/request';

// 分销商管理-专属顾问创建
export function distributorCsRelationCreate(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorCsRelation/create',
    method: 'post',
    data
  });
}

// 分销商管理-专属顾问修改
export function distributorCsRelationUpdate(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorCsRelation/update',
    method: 'post',
    data
  });
}

// 分销商管理-专属顾问删除
export function distributorCsRelationDelete(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorCsRelation/delete',
    method: 'post',
    data
  });
}

// 分销商管理-专属顾问列表
export function distributorCsRelationList(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorCsRelation/list',
    method: 'post',
    data
  });
}

// 通过id查询获取专属客服对象及其下面对应品牌的信息
export function customerServiceGetById(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/customerService/get?id=${id}`,
    method: 'get'
  });
}

// 分销商管理-查询专属顾问的拓展品牌
export function csBrandRelationListByCsId(csId) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/csBrandRelation/listByCsId?csId=${csId}`,
    method: 'post'
  });
}

// 分销商批量创建-匹配线索
export function createInBatch(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributor/createInBatch`,
    method: 'post',
    data
  });
}

// 分销商批量创建-匹配线索-校验
export function validInBatch(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributor/validInBatch`,
    method: 'post',
    data
  });
}

// 分销商管理-列表查询(新版本的线索列表查询)
export function distributorLeadsManagerListAll(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorLeadsManager/listAll`,
    method: 'post',
    data
  });
}
