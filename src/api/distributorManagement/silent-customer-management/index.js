import { withExtTenantIdRequest } from '@/utils/request';

// 获取沉寂客户池分页列表
export function distributorSilentList (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorSilent/list',
    method: 'post',
    data
  });
}

// 获取沉寂客户池统计数据
export function getSilentStatistics (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorSilent/querySilentStatistics',
    method: 'post',
    data
  });
}

// 导出沉寂客户池列表
export function distributorSilentExport (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorSilent/export',
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}

// 人工跟进-添加跟进记录
export function distributorSilentCreateFollow (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorSilent/createFollow',
    method: 'post',
    data
  });
}

// 认领沉寂客户
export function distributorSilentCreateClaimSilent (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorSilent/createClaimSilent',
    method: 'post',
    data
  });
}

// 批量/单个审核申领
export function distributorSilentBatchClaimAudit (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorSilent/batchClaimAudit',
    method: 'post',
    data
  });
}

// 批量更改沉寂客户触达方式
export function batchUpdateSilentContactWay (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorSilent/batchUpdateSilentContactWay',
    method: 'post',
    data
  });
}

// 查看跟进列表
export function distributorSilentListPageOfFollow (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorSilent/listPageOfFollow',
    method: 'post',
    data
  });
}

// 申请冻结
export function distributorSilentApplyDisable (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorSilent/applyDisable',
    method: 'post',
    data
  });
}

// 取消冻结
export function distributorSilentCancelDisable (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorSilent/cancelDisable',
    method: 'post',
    data
  });
}

// 获取沉寂规则配置数据
export function distributorSilentRuleConfigGet () {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorSilentRuleConfig/get',
    method: 'get',
  });
}