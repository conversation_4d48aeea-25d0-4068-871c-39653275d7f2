import { withExtTenantIdRequest } from '@/utils/request';

// 工作台-品牌找分销-分销商广场列表
export function distributorPortraitWorkbenchlist(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorPortrait/workbench/list`,
    method: 'post',
    data
  });
}
// 工作台-品牌找分销-添加收藏
export function distributorPortraitWorkbenchAddCollect(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorPortrait/workbench/addCollect`,
    method: 'post',
    data
  });
}
// 工作台-品牌找分销-移除收藏
export function distributorPortraitWorkbenchRemovedCollect(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorPortrait/workbench/removedCollect`,
    method: 'post',
    data
  });
}
// 工作台-品牌找分销-分销商详情
export function distributorPortraitWorkbenchRemovedGetInfo(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorPortrait/workbench/getInfo`,
    method: 'post',
    data
  });
}
// 工作台-品牌找分销-分销商详情-采购概况
export function distributorPortraitWorkbenchGetPurchaseOverviewTrend(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorPortrait/workbench/getPurchaseOverviewTrend`,
    method: 'post',
    data
  });
}
// 工作台-品牌找分销-分销商详情-商品明细
export function distributorPortraitWorkbenchListPurchaseCommodityByWorkbench(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorPortrait/workbench/listPurchaseCommodityByWorkbench`,
    method: 'post',
    data
  });
}
// 工作台-品牌找分销-分销商导出心愿清单
export function distributorPortraitWorkbenchExportExcel(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorPortrait/workbench/exportExcel`,
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}
