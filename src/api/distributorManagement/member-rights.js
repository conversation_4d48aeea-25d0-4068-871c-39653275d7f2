import { withExtTenantIdRequest } from '@/utils/request';
// 等级权益管理-等级权益-获取当前等级权益列表
export function distributorGradeRightsListAll(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorGradeRights/listAll',
    method: 'post',
    data
  });
}

// 获取分销商等级权益对象
export function distributorGradeRightsListByGrade(grade) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorGradeRights/listByGrade?grade=${grade}`,
    method: 'get'
  });
}

// 保存等级权益
export function distributorGradeRightsSave(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorGradeRights/save`,
    method: 'post',
    data,
  });
}

// 等级权益管理-添加当前展示权益-保存当前展示权益
export function distributorRightsShowConfigCreateBatch(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorRightsShowConfig/createBatch',
    method: 'post',
    data,
  });
}

// 获取对应等级下，所展示的权益信息
export function distributorRightsShowConfigGet(grade) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorRightsShowConfig/get?grade=${grade}`,
    method: 'get'
  });
}

