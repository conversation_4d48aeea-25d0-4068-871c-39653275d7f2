import { withExtTenantIdRequest } from '@/utils/request';

// 违规记录列表
export function violateRecordList(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/violateRecord/list',
    method: 'post',
    data
  });
}

// 违规记录 创建
export function violateRecordCreate(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/violateRecord/create',
    method: 'post',
    data
  });
}

// 违规记录 修改
export function violateRecordUpdate(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/violateRecord/update',
    method: 'post',
    data
  });
}

// 违规记录 删除
export function violateRecordDelete(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/violateRecord/delete',
    method: 'post',
    params: data
  });
}

// 违规记录 查看
export function violateRecordGet(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/violateRecord/get',
    method: 'get',
    params: data
  });
}

// 违规记录统计
export function violateRecordIllegaStatistics(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/violateRecord/illegaStatistics',
    method: 'post',
    data
  });
}

// 撤销违规记录处罚
export function violateRecordRevokeIllega(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/violateRecord/revokeIllega',
    method: 'post',
    data
  });
}

// 提交违规记录处罚
export function violateRecordSubmitVouchers(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/violateRecord/submitVouchers',
    method: 'post',
    data
  });
}

// 违规记录导出
export function violateRecordExportExcel(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/violateRecord/exportExcel',
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}
