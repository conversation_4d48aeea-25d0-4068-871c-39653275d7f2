import { withExtTenantIdRequest } from '@/utils/request';

// 合同列表
export function listContract(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorContract/list',
    method: 'post',
    data
  });
}

// 查看合同
export function distributorContractGet(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorContract/get?id=${id}`,
    method: 'get'
  });
}
// 更新合同
export function distributorContractUpdate(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorContract/update',
    method: 'post',
    data
  });
}

// 撤销签署中合同
export function revokeSigningContract(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorContract/revokeSigningContract?id=${id}`,
    method: 'get'
  });
}

// 终止合同
export function stopContract(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorContract/stopContract?id=${id}`,
    method: 'get'
  });
}

// 查询合同信息
export function getContractDetail(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorContractInfo/getDetail?id=${id}`,
    method: 'get'
  });
}
// 根据企业名称获取企业详细信息
export function queryByName(val) {
  return withExtTenantIdRequest({
    url: `/ocean/api/companyIdentity/queryInvoiceTitleInfoByName?companyName=${val}`,
    method: 'post'
  });
}

// 合同管理-统计
export function statistic(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorContract/statistic',
    method: 'post',
    data
  });
}

// 合同管理-列表导出
export function exportExcel(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorContract/exportExcel`,
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}

// 下拉获取甲方主体公司
export function distributorContractListOwnCompany(data = {}) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorContract/listOwnCompany',
    method: 'post',
    data
  });
}

// 分销商外部合同分页列表
export function distributorContractExternalList(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorContractExternal/list',
    method: 'post',
    data
  });
}

// 导出分销商外部合同列表
export function distributorContractExternalExportExcel(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorContractExternal/exportExcel',
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}

// 分销商外部合同匹配分销商
export function distributorContractExternalMatchDistributor(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorContractExternal/matchDistributor',
    method: 'post',
    data
  });
}

// 分销商外部合同创建人列表
export function distributorContractExternalListUserNames(data={}) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorContractExternal/listUserNames',
    method: 'post',
    data
  });
}

// 下载飞书合同文件
export function distributorContractExternalDownloadFeiShuFile(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorContractExternal/downloadFeiShuFile',
    method: 'post',
    data,
    responseType: 'arraybuffer',
    timeout: 30 * 1000
  });
}

// 查询Oms渠道列表-外部
export function listOmsChannelInfo(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/externalChannel/listOmsChannelInfo',
    method: 'post',
    data
  });
}

// 查询SAP渠道列表-外部
export function listSapChannelInfo(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/externalChannel/listSapChannelInfo',
    method: 'post',
    data
  });
}

// 分销商外部合同认领-二次确认-获取合同相关信息
export function distributorContractExternalFindClaimContractInfo(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorContractExternal/findClaimContractInfo',
    method: 'post',
    data
  });
}

// 分销商外部合同需求部门列表
export function listDemandDepartmentNames(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorContractExternal/listDemandDepartmentNames',
    method: 'post',
    data
  });
}
