import { withExtTenantIdRequest } from '@/utils/request';

// 获取分销商等级分页列表
export function listGradePage (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorGrade/listGradePage',
    method: 'post',
    data
  });
}

// 导出分销商等级列表
export function exportExcel (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorGrade/exportGrade',
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}

// 调整单个分销商等级分数
export function changeScore (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorGrade/changeScore',
    method: 'post',
    data
  });
}

// 批量调整分销商等级分数
export function changeScoreInBatch (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorGrade/changeScoreInBatch',
    method: 'post',
    data
  });
}

// 获取分销商等级记录列表
export function listGradeChange (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorGrade/listGradeChange',
    method: 'post',
    data
  });
}

// 导出分销商等级记录
export function exportGradeChange (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorGrade/exportGradeChange',
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}

// 获取分销商等级分数调整记录列表
export function listGradeScoreDetail (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorGrade/listGradeScoreDetail',
    method: 'post',
    data
  });
}

// 导出分销商等级分数调整记录
export function exportGradeScoreDetail (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorGrade/exportGradeScoreDetail',
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}

// 获取分销商等级设置
export function listSettings () {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorGrade/listSettings',
    method: 'post'
  });
}