import { withExtTenantIdRequest } from '@/utils/request';
// 拓展客户管理-编辑基础信息
export function getDistributorBasicInfoVO(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorExtInfo/getBasicInfo?id=${id}`,
    method: 'get'
  });
}

// 拓展客户管理-查看详情/编辑信息/跟进记录
export function getDetail(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorExtInfo/getDetail?id=${id}`,
    method: 'get'
  });
}

// 拓展客户管理-详情-编辑基础信息-保存
export function updateDistributorExtInfoBasicInfo(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorExtInfo/updateBasicInfo`,
    method: 'post',
    data
  });
}

// 意向程度修改
export function updateExtInfoDistributorLabel(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorExtInfo/updateDistributorLabel`,
    method: 'post',
    data
  });
}
