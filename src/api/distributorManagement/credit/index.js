import { withExtTenantIdRequest } from '@/utils/request';

// 获取赊销账单分页列表
export function listPage (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/saleCreditBill/listPage',
    method: 'post',
    data
  });
}

// 导出账单
export function exportExcel (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/saleCreditBill/exportExcel',
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}
// 导出账单明细
export function exportExcelDetail (id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/saleCreditBill/exportDetailExcel?id=${id}`,
    method: 'post',
    responseType: 'arraybuffer',
    timeout: 30 * 1000
  });
}
// 获取还款记录列表
export function recordList (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/saleCreditRepaymentRecord/list',
    method: 'post',
    data
  });
}

// 还款记录确认
export function confirm (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/saleCreditRepaymentRecord/confirm',
    method: 'post',
    data
  });
}

// 还款记录驳回
// export function reject(data) {
//   return withExtTenantIdRequest({
//     url: '/soyoungzg/api/saleCreditRepaymentRecord/reject',
//     method: 'post',
//     data
//   });
// }

// 账单明细
export function getDetailInfo (id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/saleCreditBill/getDetailInfo?id=${id}`,
    method: 'get'
  });
}

// 调整账单
export function addBillDetail (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/saleCreditBill/addBillDetail',
    method: 'post',
    data
  });
}

// 确认账单
export function confirmBillDetail (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/saleCreditBill/confirm',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json'
    }
  });
}
