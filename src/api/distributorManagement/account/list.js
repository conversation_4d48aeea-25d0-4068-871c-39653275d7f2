import { withExtTenantIdRequest } from '@/utils/request';

// 账户管理列表
export function list(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/customerAccount/list',
    method: 'post',
    data
  });
}
// 账户管理-调整金额
export function customerAccountAdjust(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/customerAccount/adjust`,
    method: 'post',
    data
  });
}
// 获取分销商账户金额统计
export function getTotalAccount(id) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/customerAccount/getTotalAccount`,
    method: 'get'
  });
}
// 导出分销商账户
export function exportDistributorAccount(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/customerAccount/exportExcel`,
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}
// 账户管理列表
export function detailList(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorAccountDetail/list',
    method: 'post',
    data
  });
}
// 账户类型
export function fetchTypeOptions() {
  return withExtTenantIdRequest({
    url: '/common/api/dict/listByType?type=soyoungzg_account_type',
    method: 'get'
  });
}
// 调整类型
export function fetchChannelOptions() {
  return withExtTenantIdRequest({
    url: '/common/api/dict/listByType?type=soyoungzg_account_channel',
    method: 'get'
  });
}
// mars调整类型
export function distributorAccountDetailListFreeAccountType() {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorAccountDetail/listFreeAccountType',
    method: 'get'
  });
}
// 导出分销商账户明细
export function exportDistributorAccountDetail(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorAccountDetail/exportExcel`,
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}

// 充值记录列表
export function listPageRecord(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorRechargeLog/listPage',
    method: 'post',
    data
  });
}

// 冻结冻结授信账号
export function saleCreditAccountDisable(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/saleCreditAccount/disable',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json'
    }
  });
}

// 解冻赊销账户
export function saleCreditAccountEnable(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/saleCreditAccount/enable',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json'
    }
  });
}
// 获取充值方式
export function fetchPayChannelOptions() {
  return withExtTenantIdRequest({
    url: '/common/api/dict/listByType?type=soyoungzg_recharge_pay_type',
    method: 'get'
  });
}

// 导出充值记录
export function exportRechargeLog(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/distributorRechargeLog/exportExcel`,
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}

// 提现记录列表
export function distributorWithdrawRecord_listPage(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorWithdrawRecord/listPage',
    method: 'post',
    data
  });
}

// 提现记录查看附件
export function distributorWithdrawRecord_getAttachment(id) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorWithdrawRecord/getAttachment',
    method: 'get',
    params: {id}
  });
}

// 提现记录驳回
export function distributorWithdrawRecord_reject(id) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorWithdrawRecord/reject',
    method: 'get',
    params: {id}
  });
}

// 提现记录审核
export function distributorWithdrawRecord_audit(id) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorWithdrawRecord/audit',
    method: 'get',
    params: {id}
  });
}

// 提现记录到账
export function distributorWithdrawRecord_accounted(id) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorWithdrawRecord/account',
    method: 'get',
    params: {id}
  });
}

// 提现记录下载资料
export function distributorWithdrawRecord_datumDownload(id) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorWithdrawRecord/datumDownload',
    method: 'get',
    params: {id},
    responseType: 'arraybuffer',
    headers:{ 'Content-Type': 'application/json; application/octet-stream'}
  });
}

// 提现记录导出
export function distributorWithdrawRecord_export(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorWithdrawRecord/withdrawalRecordExport',
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}

// 提现记录添加备注
export function distributorWithdrawRecord_addRemarks(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorWithdrawRecord/addRemarks',
    method: 'post',
    data
  });
}

// 获取经营主体信息
export function listOwnCompanyInfo () {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/customerAccount/listOwnCompanyInfo',
    method: 'post'
  });
}

// 模糊匹配已充值主体信息
export function listChargeContractInfo (data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorChargeRecord/listChargeContractInfo',
    method: 'get',
    params: data
  });
}

// PC-根据账户类型获取账户（用于调整返利账号）
export function customerAccountListAccountsByDistributorId(distributorId,accountType) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/customerAccount/listAccountsByDistributorId?distributorId=${distributorId}&accountType=${accountType}`,
    method: 'get',
  });
}

// PC-品牌返利账户管理
export function customerAccountListAccountsByPage(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/customerAccount/listAccountsByPage',
    method: 'post',
    data
  });
}

// 导出品牌返利账户
export function customerAccountAccountsExport(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/customerAccount/accountsExport `,
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}
