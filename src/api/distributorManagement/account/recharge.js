import { withExtTenantIdRequest } from '@/utils/request';

// 充值申请-分页查询
export function distributorChargeRecordListPage(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorChargeRecord/listPage',
    method: 'post',
    data
  });
}

// 充值申请-充值核销比对
export function distributorChargeRecordCollate(params) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorChargeRecord/collate',
    method: 'get',
    params
  });
}

// 充值申请-审核
export function distributorChargeRecordAudit(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorChargeRecord/audit',
    method: 'post',
    data
  });
}

// 充值申请-驳回
export function distributorChargeRecordReject(data = {}) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorChargeRecord/reject',
    method: 'post',
    data
  });
}

// 充值申请-获取
export function distributorChargeRecordGet(params) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorChargeRecord/get',
    method: 'get',
    params
  });
}

// 银行流水-分页查询
export function distributorBrankFlowRecordListPage(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorBrankFlowRecord/listPage',
    method: 'post',
    data
  });
}