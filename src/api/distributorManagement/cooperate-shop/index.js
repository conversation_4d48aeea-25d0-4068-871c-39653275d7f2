import { withExtTenantIdRequest } from '@/utils/request';

// 合作店铺导出
export function exportThirdExcel(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributor/exportThirdExcel',
    method: 'post',
    data,
    responseType: 'arraybuffer',
    timeout: 30 * 1000
  });
}

// 合作店铺新增
export function createThird(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributor/createThird',
    method: 'post',
    data
  });
}

// 合作店铺批量新增
export function createThirdInBatch(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributor/createThirdInBatch',
    method: 'post',
    data
  });
}

// 合作店铺线下批量新增
export function createThirdOfflineInBatch(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributor/createThirdOfflineInBatch',
    method: 'post',
    data
  });
}

// 合作店铺导入前校验
export function validThirdInBatch(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributor/validThirdInBatch',
    method: 'post',
    data
  });
}

//  三方分销商批量验证
export function validThirdOfflineInBatch(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributor/validThirdOfflineInBatch',
    method: 'post',
    data
  });
}

// 合作店铺 - 合作
export function cooperation(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributor/cooperation',
    method: 'post',
    data
  });
}

// 合作店铺 - 取消合作
export function cooperationCancel(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributor/cooperationCancel',
    method: 'post',
    data
  });
}

// 合作店铺 - 完善手机号
export function improveMobile(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributor/improveMobile',
    method: 'post',
    data
  });
}

// 合作店铺模板导出
export function downloadDistributorImportTemplate(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributor/downloadDistributorImportTemplate',
    method: 'get',
    data,
    responseType: 'arraybuffer',
    timeout: 30 * 1000
  });
}

// 合作线下店铺模板导出
export function downloadDistributorOfflineImportTemplate(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributor/downloadDistributorOfflineImportTemplate',
    method: 'get',
    data,
    responseType: 'arraybuffer',
    timeout: 30 * 1000
  });
}
// 更改分销商手机号码
export function distributorChangeMobile(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/distributorChangeMobile/changeMobileForPc',
    method: 'post',
    data
  });
}
