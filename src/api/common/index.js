import { withExtTenantIdRequest } from '@/utils/request';

// 批量上传图片并且添加入素材中心
export function batchUploadAndInsert(data, groupId, group) {
  return withExtTenantIdRequest({
    method: 'post',
    data,
    url: '/file/api/image/batchUploadAndInsert',
    params: {
      groupId,
      group
    }
  });
}

// 上传图片 FormData处理
export function uploadFormDataImage(file) {
  const formData = new FormData();
  formData.append('file', file);
  return withExtTenantIdRequest({
    url: '/file-service/api/image/upload',
    method: 'post',
    data: formData,
    params: 'image/soyoung-zg/admin-image'
  });
}

// 上传图片
export function uploadImage(data) {
  return withExtTenantIdRequest({
    url: '/file-service/api/image/upload',
    method: 'post',
    data,
    params: 'image/soyoung-zg/admin-image'
  });
}

// 获取商品分组 分页列表
export function fetchCategoryListPage(data) {
  return withExtTenantIdRequest({
    method: 'post',
    url: '/commodity/api/category/list',
    data
  });
}

// 判断用户是否登录
export function isLogin() {
  return withExtTenantIdRequest({
    url: '/ocean/api/auth/validSessionId'
  });
}

// 获取员工分组下拉数据
export function getStaffGroupSelect() {
  return withExtTenantIdRequest({
    method: 'post',
    url: '/ocean/api/shopStaffGroup/listAll',
    data: {}
  });
}
// 专属顾问列表
export function customerList(data = {}) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/customerService/listAll',
    method: 'post',
    data
  });
}

// 公共导出按钮组件接口
export function publicExportExcel(url, data) {
  return withExtTenantIdRequest({
    url: `${url}`,
    method: 'post',
    responseType: 'arraybuffer',
    data,
    timeout: 30 * 1000
  });
}

// 获取外部渠道 不分页
export function getExternalChannelList(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/externalChannel/list`,
    method: 'post',
    data
  });
}

// 中台记录日志接口
export function logOperatelogSummary(data) {
  return withExtTenantIdRequest({
    url: `/log/api/operatelog/summary`,
    method: 'post',
    data
  });
}

// 筛选条件获取接口
export function customQueryUserGet(params) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/customQueryUser/get`,
    method: 'get',
    params
  });
}

// 筛选条件获取接口
export function customQueryUserUpdate(data) {
  return withExtTenantIdRequest({
    url: `/soyoungzg/api/customQueryUser/update`,
    method: 'post',
    data
  });
}

// 获取操作记录
export function businessRecordLogListPageForUnifiedLogs(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/businessRecordLog/listPageForUnifiedLogs',
    method: 'post',
    data
  });
}

// 获取渠道列表
export function dataAssetsListChannel(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/dataAssets/listChannel',
    method: 'post',
    data
  });
}
