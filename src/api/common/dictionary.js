import { withExtTenantIdRequest } from '@/utils/request';
import sortBy from 'lodash/sortBy';

export const OPERATETASK_TASK_STATUS = [
  {
    value: '0',
    label: '待办'
  },
  {
    value: '1',
    label: '已办'
  },
  {
    value: '2',
    label: '已忽略'
  }
];

const INTERFACES = {
  // 分销商采购类型
  ISFLAG() {
    return Promise.resolve({
      data: [
        {
          value: '0',
          label: '否'
        },
        {
          value: '1',
          label: '是'
        }
      ]
    });
  },
  ADVISER_EXPAND(data = {}) {
    return withExtTenantIdRequest({
      url: '/soyoungzg/api/customerService/listAll',
      method: 'post',
      data
    });
  },
  BRAND_PREFER(data = {}) {
    return withExtTenantIdRequest({
      url: '/soyoungzg/api/brand/listAllBrandName',
      method: 'post',
      data
    });
  },
  BRAND_EXPAND(data = {}) {
    return withExtTenantIdRequest({
      url: '/soyoungzg/api/brand/listAllBrandName',
      method: 'post',
      data
    });
  },
  COMMODITY_LABEL(data = {}) {
    return withExtTenantIdRequest({
      url: '/commodity/api/commodityLabel/listAll',
      method: 'post',
      data
    });
  },
  // 偏好品类
  COMMODITY_CATEGORY(data = {}) {
    return withExtTenantIdRequest({
      url: '/commodity/api/category/listAll',
      method: 'post',
      data
    });
  },
  DISTRIBUTOR_WEBSITEWHITELIST(data = {}) {
    return withExtTenantIdRequest({
      url: '/soyoungzg/api/distributorWebsiteWhitelist/listAll',
      method: 'post',
      data
    });
  },
  STAFF(data = {}) {
    return withExtTenantIdRequest({
      url: '/ocean/api/shopStaff/listAll',
      method: 'post',
      data
    });
  },
  OPERATETASK_TASK_STATUS() {
    return {
      data: OPERATETASK_TASK_STATUS
    };
  },
  PUSHPRODUCT_DISTRIBUTOR_TYPE() {
    return Promise.resolve({
      data: [
        {
          value: 'ALL',
          label: '全部用户'
        },
        {
          value: 'PART',
          label: '指定用户'
        }
      ]
    });
  },
  // 分销商采购类型
  DISTRIBUTOR_PURCHASE_TYPE() {
    return Promise.resolve({
      data: [
        {
          value: 'PURCHASE',
          label: '采销'
        },
        {
          value: 'DROP_SHIPPING',
          label: '一件代发'
        }
      ]
    });
  }
};

// 数据字典
export class Dictionary {
  // 是否
  static ISFLAG = new Dictionary('ISFLAG');

  static FREEZE_REASON = new Dictionary('syzg_distributor_freeze_reason'); // 冻结原因
  static EXPAND_CHANNEL = new Dictionary('soyoungzg_channel'); // 拓展渠道
  static ENTERPRISE_NATURE = new Dictionary('distributor_merchant_type'); // 企业/个人
  static SHOP_LEVEL = new Dictionary('platform_shop_level'); // 店铺等级
  static SHOP_TYPE = new Dictionary('platform_shop_type'); // 店铺类型
  static SHOP_CATEGORY = new Dictionary('syzg_business_category'); // 主营类目
  static DISTIBUTOR_SHOP_TYPE = new Dictionary('soyoungzg_distributor_shop_type'); // 分销商类型
  static BRAND_PREFER = new Dictionary('BRAND_PREFER', {
    fields: ['id', 'name']
  }); // 偏好品牌
  static BRAND_EXPAND = new Dictionary('BRAND_EXPAND', {
    fields: ['id', 'name']
  }); // 拓展品牌
  static COMMODITY_LABEL = new Dictionary('COMMODITY_LABEL', {
    fields: ['id', 'name']
  }); // 商品标签
  static COMMODITY_CATEGORY = new Dictionary('COMMODITY_CATEGORY', {
    fields: ['id', 'name']
  }); // 商品标签
  static ENTERPRISE_SCALE = new Dictionary('syzg_enterprise_scale'); // 企业规模
  static CLUE_STATUS = new Dictionary('distributor_leads_status'); // 线索状态
  static CLUE_FOLLOWLABEL = new Dictionary('syzg_todo_follow_label'); // 线索跟进事项
  static CLUE_FOLLOWLABEL_DEVELOP = new Dictionary('syzg_todo_develop_follow_label'); // 线索跟进事项 - 拓展
  static CLUE_FOLLOWLABEL_DISTRIBUTOR = new Dictionary('syzg_todo_distributor_follow_label'); // 线索跟进事项 - 运维
  static CLUE_FOLLOWWAY = new Dictionary('syzg_todo_follow_way'); // 线索跟进方式
  static CLUE_FOLLOWRESULT_AFTERSALE = new Dictionary('syzg_todo_aftersale_follow_result'); // 线索跟进结果 售后
  static CLUE_FOLLOWRESULT_CONTACT = new Dictionary('syzg_todo_contact_follow_result'); // 线索跟进结果 触达
  static CLUE_FOLLOWRESULT_ADDWECHAT = new Dictionary('syzg_todo_addwechat_follow_result'); // 线索跟进结果 加微
  static CLUE_FOLLOWRESULT_REGISTER = new Dictionary('syzg_todo_register_follow_result'); // 线索跟进结果 注册
  static CLUE_FOLLOWRESULT_GP = new Dictionary('syzg_todo_guidepurchase_follow_result'); // 线索跟进结果 促单
  static CLUE_FOLLOWRESULT_CREDITBACK = new Dictionary('syzg_todo_creditback_follow_result'); // 线索跟进结果 返点
  static CLUE_FOLLOWRESULT_LICENSE = new Dictionary('syzg_todo_license_follow_result'); // 线索跟进结果 授权
  static CLUE_FOLLOWRESULT_POLICY = new Dictionary('syzg_todo_policy_follow_result'); // 线索跟进结果 政策
  static CLUE_FOLLOWRESULT_HOLIDAY = new Dictionary('syzg_todo_holiday_follow_result'); // 线索跟进结果 节假日
  static CLUE_FOLLOWRESULT_WAKE_UP = new Dictionary('syzg_todo_wakeup_follow_result'); // 线索跟进结果 唤醒
  static ADVISER_EXPAND = new Dictionary('ADVISER_EXPAND', {
    fields: ['id', 'name']
  }); // 拓展顾问， 专属顾问
  static OPERATETASK_TASK_CATEGORY = new Dictionary('syzg_task_category'); // 运营任务场景
  static OPERATETASK_TASK_TYPE = new Dictionary('syzg_task_type'); // 运营任务事项
  static OPERATETASK_TASK_TRIGGER = new Dictionary('push_type'); // 运营任务触发方式
  static OPERATETASK_TASK_STATUS = new Dictionary('OPERATETASK_TASK_STATUS'); // 运营任务状态

  /* 推品清单 */
  // 分销商类型
  static PUSHPRODUCT_DISTRIBUTOR_TYPE = new Dictionary('PUSHPRODUCT_DISTRIBUTOR_TYPE');

  /* 违规记录 */
  // 分销商白名单 渠道
  static DISTRIBUTOR_WEBSITEWHIT_CHANNEL = new Dictionary('DISTRIBUTOR_WEBSITEWHITELIST', {
    fields: ['platform', 'platformName'],
    isCatch: false
  });
  // 分销商白名单 店铺
  static DISTRIBUTOR_WEBSITEWHITE_SHOP = new Dictionary('DISTRIBUTOR_WEBSITEWHITELIST', {
    fields: ['id', 'platformShopName'],
    isCatch: false
  });
  // 分销商采购类型
  static DISTRIBUTOR_PURCHASE_TYPE = new Dictionary('DISTRIBUTOR_PURCHASE_TYPE');
  // 员工
  static STAFF = new Dictionary('STAFF', {
    fields: ['userId', 'name']
  });

  // 品牌管理
  static BRAND_GROUP = new Dictionary('syzg_brand_group'); // 业务分组
  static BRAND_STATUS = new Dictionary('syzg_brand_status'); // 是否启用
  static BRAND_AUTHORITY_AREA = new Dictionary('bc_brand_authority_area'); //  经销授权范围（地理范围）
  static BRAND_AUTHORITY_CHANNEL = new Dictionary('bc_brand_authority_channel'); // 经销授权范围（渠道）

  constructor(name, { fields = ['value', 'label'], isCatch = true } = {}) {
    this.name = name;
    // 下拉选择字段 value，label对应的数据中的字段
    this.fields = fields;
    // 是否缓存 默认使用缓存
    this.isCatch = isCatch;
    this.value;
  }
  toString() {
    return this.name;
  }

  setValue(value) {
    this.value = value;
  }

  getValue(type, params) {
    typeof params !== 'object' && (params = {});

    if (!this.value || !this.isCatch) {
      const name = this.name;
      const res = INTERFACES[name] ? INTERFACES[name](params) : getDictionaryType(name);

      this.handlevalue(
        type,
        new Promise((s, e) => {
          let v;
          res.then((res2) => {
            if (INTERFACES[name]) {
              const [k1, k2] = this.fields;
              // 剔除可能存在的重复数据
              v = res2.data.reduce(
                (cur, pre) =>
                  cur.some((i) => i.value === pre[k1])
                    ? cur
                    : [
                        ...cur,
                        {
                          value: pre[k1],
                          label: pre[k2]
                        }
                      ],
                []
              );
            } else {
              v = sortBy(res2.data, 'sort').map(({ value, label }) => ({
                value,
                label
              }));
            }
            s(v);
          });
        })
      );
    }

    return this.value;
  }

  handlevalue(type, v) {
    Dictionary[type].setValue(v);
    return Dictionary[type].value;
  }
}

export const CLUE_FOLLOWRESULTS = {
  CONTACT: 'CLUE_FOLLOWRESULT_CONTACT', // 触达
  ADD_WECHAT: 'CLUE_FOLLOWRESULT_ADDWECHAT', // 加微
  REGISTER: 'CLUE_FOLLOWRESULT_REGISTER', // 注册
  GUIDE_PURCHASE: 'CLUE_FOLLOWRESULT_GP', // 促单
  CREDIT_BACK: 'CLUE_FOLLOWRESULT_CREDITBACK', // 返点
  LICENSE: 'CLUE_FOLLOWRESULT_LICENSE', // 授权
  POLICY: 'CLUE_FOLLOWRESULT_POLICY', // 政策
  HOLIDAY: 'CLUE_FOLLOWRESULT_HOLIDAY', // 节假日
  AFTER_SALE: 'CLUE_FOLLOWRESULT_AFTERSALE', // 售后
  WAKE_UP: 'CLUE_FOLLOWRESULT_WAKE_UP' // 唤醒
};

// 获取数据字典
export function getDictionaryType(type) {
  return withExtTenantIdRequest({
    url: `/common/api/dict/listByType?type=${type}`,
    method: 'get'
  });
}

// 获取数字字典
export async function getDictionary(type, params) {
  return await Dictionary[type].getValue(type, params);
}
