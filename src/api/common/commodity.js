import { withExtTenantIdRequest } from '@/utils/request';

// 获取全部的商品列表
export function list(data) {
  return withExtTenantIdRequest({
    url: '/marketing/api/marketing/listCommodity',
    method: 'post',
    data
  });
}
export function customReq(
  data,
  url = '/soyoungzg/api/marketing/activity/listCommodityPage',
  method = 'post'
) {
  return withExtTenantIdRequest({
    url,
    method,
    data
  });
}

// 获取全部代言人商品列表
export function listSpokesManCommodity(data) {
  return withExtTenantIdRequest({
    url: '/marketing/api/spokesman/listSpokesmanCommodityForQuery',
    method: 'post',
    data
  });
}

// 获取赠品列表
export function listGift(data) {
  return withExtTenantIdRequest({
    url: '/soyoungzg/api/marketing/activity/listGiftPage',
    method: 'post',
    data
  });
}

// 获取礼包商品列表
export function listGiftBag(data) {
  return withExtTenantIdRequest({
    url: '/marketing/api/present/list',
    method: 'post',
    data
  });
}

// 获取根据商品id获取规格
export function listSpec(commodityId) {
  return withExtTenantIdRequest({
    url: `/commodity/api/commodity/getSkuDetailByCommodityId?commodityId=${commodityId}`
  });
}

// 获取全部的礼包商品列表
export function giftBagList(data) {
  return withExtTenantIdRequest({
    url: '/marketing/api/present/list',
    method: 'post',
    data
  });
}

// 获取全部的优惠券列表
export function couponList(data) {
  return withExtTenantIdRequest({
    url: '/marketing/api/couponTotal/listPage',
    method: 'post',
    data
  });
}

// 获取状态
export function fetchStatusOptions() {
  return withExtTenantIdRequest({
    url: '/common/api/dict/listByType?type=marketing_bale_status',
    method: 'get'
  });
}

export function fetchListPageCommodity(data) {
  return withExtTenantIdRequest({
    url: '/commodity/api/commodity/listCommodityBriefPage',
    method: 'post',
    data
  });
}

// 通过id获取商品参与的优惠活动
export function listCommodityTool(data) {
  return withExtTenantIdRequest({
    url: '/marketing/api/marketingToolCommodityEs/listAll',
    method: 'post',
    data
  });
}

// 获取商品活动属性对象
export function getCommodityActivityParam(id) {
  return withExtTenantIdRequest({
    url: `/commodity/api/commodityActivityParam/get?id=${id}`,
    method: 'get'
  });
}

// 修改商品活动属性
export function updateCommodityActivityParam(data) {
  return withExtTenantIdRequest({
    url: '/commodity/api/commodityActivityParam/update',
    method: 'post',
    data
  });
}

// 新增商品活动属性
export function createCommodityActivityParam(data) {
  return withExtTenantIdRequest({
    url: '/commodity/api/commodityActivityParam/create',
    method: 'post',
    data
  });
}

// 删除商品活动属性对象
export function deleteCommodityActivityParam(id) {
  return withExtTenantIdRequest({
    url: `/commodity/api/commodityActivityParam/delete?id=${id}`,
    method: 'post'
  });
}
