import { SHOP_INFO, ROUTES } from '../constants';
import { setItem, getItem, removeItem } from '@/utils/sessionStorage';

export function setShopInfo(shop) {
  setItem(SHOP_INFO, shop);
}

export function getShopInfo() {
  return getItem(SHOP_INFO);
}

export function removeShopInfo() {
  removeItem(SHOP_INFO);
}

// 路由信息
export function setRoutes(routes) {
  setItem(ROUTES, routes);
}

export function getRoutes() {
  return getItem(ROUTES);
}

export function removeRoutes() {
  removeItem(ROUTES);
}
