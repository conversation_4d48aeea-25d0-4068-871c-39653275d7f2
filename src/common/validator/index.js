import {
  isIDCard,
  isPhoneNumber,
  isSimplePhoneNum,
  validateMoney2,
  isPercent,
  isNumber
} from '@/utils/validate';
export function validateName(rule, str, callback) {
  const value = str.trim();
  if (value === '') {
    callback('请填写姓名');
    return;
  }
  if (value.length > 10) {
    callback('请填写1-10个字');
    return;
  }
  const number = parseFloat(value, 10);
  if (!isNaN(value) && number.toString() === value) {
    // 纯数字
    callback('请填写非纯数字');
    return;
  }
  callback();
}

export function validateIdCard(rule, str, callback) {
  const value = str.trim();
  if (!isIDCard(value)) {
    callback('请填写正确的身份证格式');
    return;
  }
  callback();
}

export function validatePhone(rule, str, callback) {
  const value = str.trim();
  if (!isPhoneNumber(value)) {
    callback('请填写正确的手机号码');
    return;
  }
  callback();
  return;
}

export function validatePhoneSimple(rule, str, callback) {
  const value = str.trim();
  if (!isSimplePhoneNum(value)) {
    callback('请填写正确的手机号码');
    return;
  }
  callback();
  return;
}

export function validateMaxNum(rule, str, callback) {
  if (!isNumber(str)) {
    callback('请输入数字');
    return;
  }
  if (str > 100000000) {
    callback('数据大于1个亿，请重新输入');
    return;
  }
  callback();
  return;
}

export function validateLength(rule, str, callback) {
  const value = str.trim();
  if (value.length > 15) {
    callback('标题不能超过15个文字');
    return;
  }
  callback();
}

export function validateMoney(rule, str, callback) {
  if (!str && str !== 0) {
    callback('数据有误,数据为空');
    return;
  }
  if (str) {
    if (typeof str === 'number') {
      str = str.toString();
    }
    const value = str.trim();
    if (!validateMoney2(value)) {
      callback('数据有误，请重新输入');
      return;
    }
  }
  callback();
}

export function validateInteger(rule, str, callback) {
  if (str) {
    if (typeof str === 'number') {
      str = str.toString();
    }
    const value = str.trim();
    const reg = /^([1-9]\d*|[0]{1,1})$/;
    if (!reg.test(value)) {
      callback('数据有误，请重新输入');
      return;
    } else if (value > 99999999) {
      callback('请输入小于1亿的数字');
      return;
    }
  }
  callback();
}

export function validatePercent(rule, str, callback) {
  if (str) {
    if (!isNumber(str) || str > 100) {
      callback('请输入正确的百分数');
      return;
    }
    str = str.toString();
    const value = str.trim();
    if (!isPercent(value)) {
      callback('请输入正确的百分数并保留两位小数');
      return;
    }
  }
  callback();
}

export function validateMaxMoney(rule, str, callback) {
  if (!isNumber(str)) {
    callback('输入的数据过大,请重新输入');
    return;
  }
  if (str > 100000000) {
    callback('数据大于1个亿，请重新输入');
    return;
  }
  callback();
  return;
}
// 标签添加判断
export function validateLabel(rule, str, callback) {
  if (str.match(/^\s*$/)) {
    callback('请输入内容再添加');
    return;
  }
  callback();
}

// 校验联系方式（手机，座机）
export function _validateContact(_, value, callback) {
  const isPhone = /^([0-9]{3,4}-)?[0-9]{7,8}$/;
  const isMob = /^(86){0,1}1\d{10}$/;

  if (isMob.test(value) || isPhone.test(value) || !value) {
    callback();
  } else {
    callback(new Error('输入正确的联系方式'));
  }
}

// 校验联系方式（手机）
export function _validateMob(_, value, callback) {
  const isMob = /^(86){0,1}1\d{10}$/;

  if (isMob.test(value) || !value) {
    callback();
  } else {
    callback(new Error('输入正确的联系方式'));
  }
}

// 校验联系方式（座机）
export function _validatePhone(_, value, callback) {
  const isPhone = /^([0-9]{3,4}-)?[0-9]{7,8}$/;

  if (isPhone.test(value) || !value) {
    callback();
  } else {
    callback(new Error('输入正确的联系方式'));
  }
}
// 校验邮箱地址
export function _validateEmail(_, value, callback) {
  if(!value) {
    callback();
  }
  const flag =
    /^[a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/.test(
      value
    ) ||
    /^[a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/.test(
      value
    );
  if (flag) {
    callback();
  } else {
    callback(new Error('请填写正确邮箱'));
  }
}
// 校验链接
export function validateUrl(_, value, callback) {
  const flag = /^(?:(?:(?:https?|ftp):)?\/\/)(?:\S+(?::\S*)?@)?(?:(?!(?:10|127)(?:\.\d{1,3}){3})(?!(?:169\.254|192\.168)(?:\.\d{1,3}){2})(?!172\.(?:1[6-9]|2\d|3[0-1])(?:\.\d{1,3}){2})(?:[1-9]\d?|1\d\d|2[01]\d|22[0-3])(?:\.(?:1?\d{1,2}|2[0-4]\d|25[0-5])){2}(?:\.(?:[1-9]\d?|1\d\d|2[0-4]\d|25[0-4]))|(?:(?:[a-z\u00a1-\uffff0-9]-*)*[a-z\u00a1-\uffff0-9]+)(?:\.(?:[a-z\u00a1-\uffff0-9]-*)*[a-z\u00a1-\uffff0-9]+)*(?:\.(?:[a-z\u00a1-\uffff]{2,})).?)(?::\d{2,5})?(?:[\/?#]\S*)?$/i.test(
    value
  );
  if (flag || !value) {
    callback();
  } else {
    callback(new Error('请填写正确链接'));
  }
}
