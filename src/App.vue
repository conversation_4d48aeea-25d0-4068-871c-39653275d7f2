<template>
  <div id="app">
    <sy-iframe-alive :activePages="activePages" :iframeCodes="iframeCodes">
      <div class="content-inner">
        <keep-alive :max="15" :include="activePages">
          <router-view />
        </keep-alive>
      </div>
    </sy-iframe-alive>
  </div>
</template>
<script>
import { IFRAME_PAGES } from '@/constants';
export default {
  name: 'App',
  components: {
    'sy-iframe-alive': window.QIANKUN_DATA.COMPS.syIframeAlive
  },
  data() {
    return {
      pageNav: [],
      iframeCodes: IFRAME_PAGES
    };
  },
  created() {
    this.$onGlobalStateChange && this.$onGlobalStateChange(this.setState, true);
  },
  computed: {
    activePages() {
      return this.pageNav.map((item) => item.code);
    }
  },
  methods: {
    setState(state) {
      this.pageNav = state.pageNav;
    }
  }
};
</script>
<style lang="scss" scoped>
.content-inner {
  height: 100%;
  overflow: auto;
  padding: 16px 16px 0;
}
</style>
