<template>
  <div>
    <sales-policy v-if="policyType === 'SALE'" :brandId.sync="brandId" :brandName.sync="brandName" :id.sync="id" isEdit />
    <settlement-policy v-if="policyType === 'SETTLEMENT'" :brandId.sync="brandId" :brandName.sync="brandName" isEdit :id.sync="id" />
  </div>
</template>

<script>
import SalesPolicy from './components/sales-policy';
import SettlementPolicy from './components/settlement-policy';
export default {
  name: 'regular-policy-policy-setting-policy-edit',
  components: {
    SalesPolicy,
    SettlementPolicy
  },
  props: {},
  data() {
    return {
      policyType: 'SALE', // 类型 sales-销售政策 SETTLEMENT-结算政策
      brandName: '',
      brandId: '',
      id: ''
    };
  },
  onShow() {
    this.getQueryData();
  },
  methods: {
    getQueryData() {
      console.log('getQueryData', this.$route.query);
      const { policyType = '', brandName = '', brandId = '', id = '' } = this.$route.query;
      this.policyType = policyType;
      this.brandName = brandName || '';
      this.brandId = brandId || '';
      this.id = id || '';
    }
  }
};
</script>

<style>
</style>