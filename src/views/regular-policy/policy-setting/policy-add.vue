<template>
  <div>
    <sales-policy v-if="policyType === 'SALE'" :brandId="brandId" :brandName="brandName" isAdd />
    <settlement-policy v-else :brandId="brandId" :brandName="brandName" isAdd />
  </div>
</template>

<script>
import SalesPolicy from './components/sales-policy';
import SettlementPolicy from './components/settlement-policy';
export default {
  name: 'regular-policy-policy-setting-policy-add',
  components: {
    SalesPolicy,
    SettlementPolicy
  },
  props: {},
  data() {
    return {
      policyType: 'SALE', // 类型 sales-销售政策 SETTLEMENT-结算政策
      brandName: '',
      brandId: ''
    };
  },
  created() {
    const { policyType = 'SALE', brandName = '', brandId = '' } = this.$route.query;
    this.policyType = policyType;
    this.brandName = brandName || '';
    this.brandId = brandId || '';
  }
};
</script>

<style>
</style>