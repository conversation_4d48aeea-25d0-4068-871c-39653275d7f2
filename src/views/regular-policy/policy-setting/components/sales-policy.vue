<template>
  <div class="page-container">
    <div class="page-title title-1">
      <div>{{ brandName || form.brandName }}-随单满赠-{{ formType }}</div>
    </div>
    <div class="page-tips">
      <div>1、适用于买A送B,买A送A+B的场景,门槛需要按照单品计算;满赠规则支持按照金额/件数设置,也支持循环满赠和阶梯满赠的场景。</div>
      <div>2、同一促销规则,需要买齐所有的促销商品和数量才送所有赠品</div>
    </div>
    <el-form label-width="110px" class="policy-type-add-form" :model="form" :rules="rules" ref="form">
      <el-form-item label="活动时间" prop="date">
        <el-date-picker v-model="form.date" type="datetimerange" align="right" start-placeholder="开始日期" end-placeholder="结束日期" :default-time="['00:00:00', '23:59:59']" :disabled="isDetail"> </el-date-picker>
      </el-form-item>
      <el-form-item label="活动参与人群" prop="type">
        <el-radio-group v-model="form.type" :disabled="isDetail">
          <p class="radio-block">
            <el-radio label="ALL">所有分销商</el-radio>
          </p>
          <p class="radio-block">
            <el-radio label="SUB_DISTRIBUTOR">选择分销商</el-radio>
            <selectDistributor :isDetail="isDetail" :showTabs="showTabs" source="salesPolicy" :relationData="form.relationData" v-show="form.type === 'SUB_DISTRIBUTOR'" @onSuccess="selectSpecialDistributorSuccess($event, 'commonCustomerConfig')" />
          </p>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="活动规则说明" prop="remarks">
        <el-input type="textarea" v-model="form.remarks" :disabled="isDetail"></el-input>
      </el-form-item>
      <!-- 促销活动 -->
      <div class="activity__wrap">
        <el-card class="box-card" v-for="(item, index) in form.activityList" :key="index">
          <!-- 促销活动标题 -->
          <div slot="header" class="activity__title clearfix">
            <span class="title-2">促销活动-{{ index + 1 }}</span>
            <el-button type="text" plain size="mini" v-if="!isDetail" @click="removeActivity(index)">移除</el-button>
            <i class="activity__title-expand" :class="item.expand ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" @click="item.expand = !item.expand"></i>
          </div>
          <div v-show="item.expand">
            <!-- 促销商品&赠品 -->
            <div class="goods-gift__wrap">
              <!-- 促销商品 -->
              <div class="goods">
                <div class="header">
                  <span class="title-3">促销商品</span>
                  <el-button v-if="!isDetail" type="primary" size="mini" plain icon="el-icon-plus" @click="clickAddCommodity(index, 'goods')">添加商品</el-button>
                </div>
                <el-form-item :prop="'activityList.' + index + '.goods'" :rules="[{ required: true, message: '请至少添加1个促销商品', trigger: 'blur' }]" label-width="0">
                  <el-table :data="item.goods" style="width: 100%" border>
                    <el-table-column prop="name" label="商品">
                      <template slot-scope="scope">
                        <div class="row__info">
                          <span>{{ scope.row.commodityName }}</span>
                          <div class="row__desc">
                            <div>条码：{{ scope.row.specCode }}</div>
                            <div>规格标志： {{ scope.row.skuId }}</div>
                          </div>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column align="center" label="操作" width="52px">
                      <template slot-scope="scope">
                        <el-button v-if="!isDetail" type="text" size="mini" @click="removeCommodity(scope.row.id, index, 'goods')">移除</el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-form-item>
              </div>
              <!-- 促销赠品 -->
              <div class="gift">
                <div class="header">
                  <span class="title-3">赠品</span>
                  <el-button type="primary" size="mini" plain icon="el-icon-plus" @click="clickAddCommodity(index, 'gift')" v-if="!isDetail">添加赠品</el-button>
                </div>
                <el-form-item :prop="'activityList.' + index + '.gift'" :rules="[{ required: true, message: '请至少添加1个赠品', trigger: 'blur' }]" label-width="0">
                  <el-table :data="item.gift" style="width: 100%" border>
                    <el-table-column prop="name" label="商品">
                      <template slot-scope="scope">
                        <div class="row__info">
                          <span>{{ scope.row.commodityName }}</span>
                          <div class="row__desc">
                            <div>条码：{{ scope.row.specCode }}</div>
                            <div>规格标志： {{ scope.row.skuId }}</div>
                          </div>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column align="center" label="操作" width="52px" v-if="!isDetail">
                      <template slot-scope="scope">
                        <el-button type="text" size="mini" @click="removeCommodity(scope.row.id, index, 'gift')">移除</el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-form-item>
              </div>
            </div>
            <el-divider></el-divider>

            <!-- 促销活动方式 -->
            <el-form-item label="满赠方式" label-width="78px" prop="type">
              <el-radio-group v-model="item.type" @input="changeType(index)" :disabled="isDetail">
                <el-radio label="LOOP">循环满赠</el-radio>
                <el-radio label="LADDER">阶梯满赠</el-radio>
              </el-radio-group>
            </el-form-item>
            <!-- 促销活动内容 -->
            <div class="activity__content-wrap">
              <div class="activity__content-title title-3">
                <div>活动内容</div>
                <el-button v-if="item.type === 'LADDER' && !isDetail" type="primary" size="mini" plain icon="el-icon-plus" @click="addThreshold(index)">添加阶梯</el-button>
              </div>
              <!-- 促销阶梯 START -->
              <div class="threshold__content" v-for="(threshold, thIdx) in item.threshold" :key="index + '-' + thIdx">
                <div class="threshold__title" v-if="item.type === 'LADDER'">
                  <div>阶梯 {{ thIdx + 1 }}</div>
                  <el-button v-if="thIdx !== 0 && !isDetail" type="text" plain size="mini" @click="removeThreshold(index, thIdx)">移除</el-button>
                </div>
                <div class="goods-gift__wrap">
                  <!-- 促销阶梯商品  -->
                  <div class="goods">
                    <el-table :data="threshold.goods" style="width: 100%" border>
                      <el-table-column prop="name" label="商品">
                        <template slot-scope="scope">
                          <div class="row__info">
                            <span>{{ scope.row.commodityName }}</span>
                            <div class="row__desc">
                              <div>条码：{{ scope.row.specCode }}</div>
                              <div>规格标志： {{ scope.row.skuId }}</div>
                            </div>
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column label="门槛件数" width="150px">
                        <template #default="scope">
                          <el-form-item label-width="0" :prop="'activityList.' + index + '.threshold.' + thIdx + '.goods.' + scope.$index + '.value'" :rules="[{ validator: validateNum, index, thIdx, type: 'goods', scope, trigger: 'blur' }]">
                            <el-input-number v-model="scope.row.value" :min="1" :max="999999" :disabled="isDetail" v-int />
                          </el-form-item>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                  <!-- 促销阶梯赠品 -->
                  <div class="gift">
                    <el-table :data="threshold.gift" style="width: 100%" border>
                      <el-table-column prop="name" label="商品">
                        <template slot-scope="scope">
                          <div class="row__info">
                            <span>{{ scope.row.commodityName }}</span>
                            <div class="row__desc">
                              <div>条码：{{ scope.row.specCode }}</div>
                              <div>规格标志： {{ scope.row.skuId }}</div>
                            </div>
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column label="赠送数量" width="150px">
                        <template #default="scope">
                          <el-form-item label-width="0" :prop="'activityList.' + index + '.threshold.' + thIdx + '.gift.' + scope.$index + '.value'" :rules="[{ validator: validateNum, index, thIdx, type: 'gift', scope, trigger: 'blur' }]">
                            <el-input-number v-model="scope.row.value" :min="1" :max="999999" :disabled="isDetail" v-int />
                          </el-form-item>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </div>
              </div>
              <!-- 阶梯-END -->
            </div>
          </div>
        </el-card>
      </div>
      <el-button type="primary" icon="el-icon-plus" @click="addAcitivity" v-if="!isDetail">添加促销活动</el-button>
      <!-- 添加商品 -->
      <AddCommodity operation="create" v-model="commodityPop.data" @updateTable="updateTable" :visible.sync="addCommodityVisble" :selectedCommodityList="selectedCommodityList" brandDisabled :defaultBrandIds="getDefaultBrandIds" />
    </el-form>
    <footer-bar v-if="isAdd || isEdit">
      <div class="footer-btn-box">
        <el-button @click="onCancel">取消</el-button>
        <el-button @click="submitForm" type="primary">保存</el-button>
      </div>
    </footer-bar>
  </div>
</template>

<script>
import selectDistributor from '@/components/selectDistributor/index.vue';
import AddCommodity from './add-commodity.vue';
import { cloneDeep } from 'lodash';
import { regularPolicyCreateSalePolicy, regularPolicyGetSalePolicy, regularPolicyUpdateSalePolicy } from '@/api/regular-policy';
const initExcludeCustomerInfo = {
  customerType: 'SUB_DISTRIBUTOR', // 适用客户类型
  subtype: 'CUSTOMER_TYPE_CS_ORG', // 关联分销商方式
  dataList: [] // 关联分销商数据
};
export default {
  name: 'SalesPolicy',
  props: {
    id: {
      type: String,
      default: ''
    },
    brandName: {
      type: String,
      default: ''
    },
    brandId: {
      type: String,
      default: ''
    },
    isEdit: {
      type: Boolean,
      default: false
    },
    isAdd: {
      type: Boolean,
      default: false
    },

    isDetail: {
      type: Boolean,
      default: false
    }
  },
  components: {
    selectDistributor,
    AddCommodity
  },
  watch: {
    id(newValue) {
      console.log('newValue===', newValue);
      if (newValue) {
        this.getData();
      }
    },
    $route(to, from) {
      // 只有当路由确实变化时才刷新（避免首次加载时重复调用）
      console.log('111111', to.path, from.path);
    }
  },
  data() {
    return {
      commodityPop: {
        data: [],
        type: 'goods',
        activityIndex: 0
      },
      addCommodityVisble: false,
      showTabs: ['CUSTOMER_TYPE_CS_ORG', 'CUSTOMER_TYPE_CUSTOMER_ATTRIBUTE'],
      form: {
        date: '',
        remarks: '',
        brandId: '',
        type: 'ALL', // 人群类型，ALL=所有分销商，SUB_DISTRIBUTOR=部分分销商,
        subType: '', // CUSTOMER_TYPE_CS_ORG=按分组，CUSTOMER_TYPE_CUSTOMER_ATTRIBUTE=按客户层级
        distributorRelValueList: [], // 销售政策关联的分销商关系
        relationData: cloneDeep(initExcludeCustomerInfo),
        activityList: [
          {
            expand: true, // 促销活动是否折叠
            salePolicyActivityId: '', // 销售政策活动ID-查看、新增时有值
            goods: [], // 促销商品
            gift: [], // 赠品
            type: 'LADDER',
            threshold: [{ goods: [], gift: [] }] // 门槛
          }
        ]
      },
      rules: {
        date: [{ required: true, message: '请选择活动时间', trigger: 'blur' }],
        remarks: [{ required: true, message: '请输入活动规则', trigger: 'blur' }],
        type: [{ required: true, message: '请选择人群类型', trigger: 'blur' }]
      }
    };
  },
  computed: {
    pickerOptions() {
      return {
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7;
        }
      };
    },
    formType() {
      if (this.isEdit) {
        return '编辑';
      }
      if (this.isAdd) {
        return '新增';
      }
      if (this.isDetail) {
        return '详情';
      }
    },
    // 所有活动已选择的商品：活动不允许重复添加商品，所以需要记录已选择的商品
    selectedCommodityList() {
      if (this.commodityPop.type === 'gift') return [];
      const arr = this.form.activityList.reduce((acc, activity) => {
        const goods = activity.goods.map((item) => item.skuId);
        return [...acc, ...goods];
      }, []);
      console.log('arr===', arr);
      return [...new Set(arr)];
    },
    getDefaultBrandIds() {
      if (this.brandId && this.brandId !== 'all') {
        return [this.brandId];
      } else if (this.form.brandId && this.form.brandId !== 'all') {
        return [this.form.brandId];
      }
      return [];
    }
  },
  created() {
    console.log('created');
    if (this.id) {
      this.getData();
    }
  },
  mounted() {
    console.log('mounted');
  },
  methods: {
    getData() {
      regularPolicyGetSalePolicy(this.id).then((res) => {
        const { activityList, brandId = '', brandName = '', remarks, type, subType = '', distributorRelValueList = [], ...rest } = res.data;
        console.log('销售政策详情=----', res);
        const newActivityList = activityList.map((item) => {
          // 循环满赠的没有阶梯，初始化一个阶梯0
          if (item.type === 'LOOP') {
            item.commodityList = item.commodityList.map((cItem) => {
              return { ...cItem, ladderNum: 0 };
            });
          }
          const newItem = {
            code: item.code,
            type: item.type,
            salePolicyActivityId: item.salePolicyActivityId,
            expand: true,
            goods: item.commodityList.filter((commodityItem) => commodityItem.commodityType === 'PROMOTIONAL_COMMODITY' && commodityItem.ladderNum === 0),
            gift: item.commodityList.filter((commodityItem) => commodityItem.commodityType === 'GIFT' && commodityItem.ladderNum === 0),
            threshold: []
          };
          const maxLadderNum = Math.max(...item.commodityList.map((item) => item.ladderNum));
          for (let i = 0; i <= maxLadderNum; i++) {
            newItem.threshold.push({ goods: [], gift: [] });
            item.commodityList.forEach((thresholdItem) => {
              if (thresholdItem.ladderNum === i) {
                newItem.threshold[i][thresholdItem.commodityType === 'PROMOTIONAL_COMMODITY' ? 'goods' : 'gift'].push(thresholdItem);
              }
            });
          }
          return newItem;
        });

        let relationData = cloneDeep(initExcludeCustomerInfo);
        if (type === 'SUB_DISTRIBUTOR') {
          relationData = {
            customerType: 'SUB_DISTRIBUTOR', // 适用客户类型
            subtype: subType, // 关联分销商方式
            dataList: [...distributorRelValueList] // 关联分销商数据
          };
          if (subType === 'CUSTOMER_TYPE_CS_ORG') {
            relationData.organizationIds = [...distributorRelValueList];
          } else if (subType === 'CUSTOMER_TYPE_CUSTOMER_ATTRIBUTE') {
            relationData.customerAttributeIds = [...distributorRelValueList];
          }
        }
        this.form = {
          brandId,
          brandName,
          type,
          subType,
          date: [rest.beginDate, rest.endDate],
          remarks,
          activityList: [...newActivityList],
          distributorRelValueList,
          relationData: relationData
        };
        console.log('this.form===', this.form);
      });
    },
    // 添加 促销商品｜赠品
    clickAddCommodity(index, type) {
      console.log('clickAddCommodity', index, type);
      this.commodityPop.activityIndex = index;
      this.commodityPop.type = type;
      this.addCommodityVisble = true;
      this.commodityPop.data = [...(this.form.activityList[index][type] || [])];
    },
    // 移除 促销商品｜赠品
    removeCommodity(id, index, type) {
      console.log('removeCommodity', id, index, type);
      const activity = this.form.activityList[index];
      const targetArray = activity[type];
      const itemIndex = targetArray.findIndex((item) => item.id === id);
      if (itemIndex !== -1) {
        targetArray.splice(itemIndex, 1);
        // 更新阈值
        this.updateThreshold(targetArray, index, type);
      }
    },
    // 更新选择的 促销商品｜赠品 的数据
    updateTable(data) {
      const { activityIndex, type } = this.commodityPop;
      this.form.activityList[activityIndex][type] = data;
      this.updateThreshold();
    },
    // 更新门槛数据，newList-目标商品数据，activityIndex-活动索引，type-商品或赠品
    updateThreshold(list, activityIndex, type) {
      if (!activityIndex && !type) {
        activityIndex = this.commodityPop.activityIndex;
        type = this.commodityPop.type;
      }
      console.log(activityIndex, type, this.form);
      const newList = list || this.form.activityList[activityIndex][type];
      console.log('updateCurrentTable', newList, activityIndex, type);
      this.form.activityList[activityIndex].threshold.forEach((thresholdItem) => {
        const thresholdArray = thresholdItem[type];
        const newThresholdArray = newList.map((item) => {
          const existingItem = thresholdArray.find((tItem) => tItem.id === item.id && tItem.skuId === item.skuId);
          return existingItem || { ...item, value: 1 };
        });
        this.$set(thresholdItem, type, newThresholdArray);
      });
    },
    // 添加营销活动
    addAcitivity() {
      this.form.activityList.push({
        expand: true,
        goods: [],
        gift: [],
        type: 'LADDER',
        threshold: [{ goods: [], gift: [] }] // 门槛
      });
    },
    removeActivity(index) {
      this.form.activityList.splice(index, 1);
    },
    // 切换活动方式
    changeType(activityIndex) {
      console.log('切换活动方式', activityIndex);
      const activity = this.form.activityList[activityIndex];
      console.log('activity.type====', activity.type);
      if (activity.type === 'LOOP') {
        if (activity.threshold.length > 1) {
          this.form.activityList[activityIndex].threshold.length = 1;
        } else if (activity.threshold.length === 0) {
          this.form.activityList[activityIndex].threshold.push({ goods: [], gift: [] });
        }
      }
    },
    // 添加阶梯
    addThreshold(index) {
      if (!this.form.activityList[index].gift.length && !this.form.activityList[index].goods.length) {
        this.$message.error('请先至少添加1个促销商品和1个赠品');
        return;
      }
      if (this.form.activityList[index].threshold.length >= 5) {
        this.$message.error('最多添加5个阶梯');
        return;
      }
      console.log('添加阶梯', index);
      this.form.activityList[index].threshold.push({
        goods: [...this.form.activityList[index].goods.map((item) => ({ ...item, value: 1 }))],
        gift: [...this.form.activityList[index].gift.map((item) => ({ ...item, value: 1 }))]
      });
    },
    // 移除阶梯
    removeThreshold(activityIndex, thresholdIndex) {
      this.$confirm('确定要移除该阶吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          console.log('移除阶梯', activityIndex, thresholdIndex);
          this.form.activityList[activityIndex].threshold.splice(thresholdIndex, 1);
        })
        .catch(() => {
          // 用户取消操作
          console.log('用户取消了移除阶梯操作');
        });
    },
    // 选择分销商
    selectSpecialDistributorSuccess(data, key) {
      console.log('data, relationData===', data, this.form.relationData);
      const dataKeyMap = {
        CUSTOMER_TYPE_CS_ORG: 'organizationIds',
        CUSTOMER_TYPE_CUSTOMER_ATTRIBUTE: 'customerAttributeIds'
      };
      const idsKey = dataKeyMap[data.subtype];
      this.form.relationData = {
        ...this.form.relationData,
        ...data,
        subtype: data.subtype, // 关联分销商方式
        dataList: [...data[idsKey]] // 关联分销商数据
      };
      this.form.subType = data.subtype;
      this.form.distributorRelValueList = [...data[idsKey]];
    },
    // 更新选择商品弹出框的可见性
    updateOuterDialogFormVisible(data) {
      console.log('updateOuterDialogFormVisible====', data);
      this.addCommodityVisble = data;
    },

    // 校验阶梯商品数量
    validateNum(rule, value, callback) {
      console.log('validateNum rule', rule);
      console.log('validateNum value', value);
      if (value < 1) {
        callback(new Error('数量必须大于0'));
      } else {
        const { index, thIdx, type, scope } = rule;
        // 获取上一个阶梯的值
        if (thIdx > 0) {
          const prevThreshold = this.form.activityList[index].threshold[thIdx - 1];
          console.log('prevThreshold', prevThreshold);
          if (prevThreshold && Number(value) < Number(prevThreshold[type][scope.$index].value)) {
            callback(new Error('不能小于上一个阶梯的值'));
            return;
          }
        }
        callback();
      }
    },
    // 取消
    onCancel() {
      this.$back();
    },
    // 保存
    submitForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          const { date, type, remarks, subType, distributorRelValueList } = this.form;
          const postData = {
            brandId: this.brandId || this.form.brandId,
            beginDate: date[0],
            endDate: date[1],
            type,
            remarks,
            activityList: this.form.activityList
              .map((item, index) => {
                const result = [];
                item.threshold.map((tItem, tIdx) => {
                  tItem.goods.map((goodsItem) => {
                    result.push({
                      commodityType: 'PROMOTIONAL_COMMODITY',
                      ladderNum: tIdx,
                      commodityId: goodsItem.commodityId,
                      skuId: goodsItem.skuId,
                      value: goodsItem.value,
                      commodityDetailId: goodsItem.commodityDetailId || '',
                      specCode: goodsItem.specCode
                    });
                  });
                  tItem.gift.map((giftItem) => {
                    result.push({
                      commodityType: 'GIFT',
                      ladderNum: tIdx,
                      commodityId: giftItem.commodityId,
                      commodityDetailId: giftItem.commodityDetailId || '',
                      skuId: giftItem.skuId,
                      value: giftItem.value,
                      specCode: giftItem.specCode
                    });
                  });
                });
                const newItem = {
                  code: index + 1,
                  type: item.type,
                  commodityList: [...result]
                };
                if (this.isEdit) {
                  newItem.salePolicyActivityId = item.salePolicyActivityId;
                }
                return newItem;
              })
              .filter((item) => item.commodityList.length > 0)
          };
          if (this.isEdit) {
            postData.id = this.id;
          }
          if (type === 'SUB_DISTRIBUTOR') {
            postData.subType = subType;
            postData.distributorRelValueList = distributorRelValueList;
          }
          const req = this.isAdd ? regularPolicyCreateSalePolicy : regularPolicyUpdateSalePolicy;
          req(postData).then((res) => {
            console.log('提交结果===', res);
            if (res.code === '0') {
              this.$message.success('操作成功');
              this.$back();
            }
          });
        } else {
          console.log('error submit!!');
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.title-1 {
  font-size: 18px;
}
.title-2 {
  font-size: 16px;
}
.title-3 {
  font-size: 14px;
}
.page-title {
  font-weight: bold;
  //   padding: 10px 0;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  gap: 10px;
  // background-color: #f7f8fa;

  //   position: -webkit-sticky; /* 兼容Safari */
  //   position: sticky;
  //   top: 0; /* 距离顶部的距离，设为0表示到达顶部时固定 */
  //   z-index: 100; /* 确保元素在其他内容之上 */
}
.page-tips {
  width: 1400px;
  line-height: 16px;
  margin-bottom: 16px;
  background: #f7f8fa;
  color: #909399;
  padding: 8px 16px;
  font-size: 12px;
}
.policy-type-add-form {
  width: 1400px;
}
.activity {
  &__wrap {
    width: 1400px;
    .el-card {
      margin-bottom: 20px;
    }
  }

  &__title {
    display: flex;
    align-items: center;
    gap: 10px;
    &-expand {
      margin-left: auto;
      cursor: pointer;
      font-size: 16px;
      color: #909399;
    }
  }
  &__content {
    &-title {
      display: flex;
      align-self: center;
      gap: 10px;
      margin-bottom: 10px;
      line-height: 28px;
    }
  }
}
.goods-gift__wrap {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  .goods,
  .gift {
    width: 670px;
    flex-shrink: 0;
    .header {
      display: flex;
      //   justify-content: space-between;
      align-items: center;
      padding: 10px 4px;
      gap: 10px;
      border: 1px solid #ebeef5;
      border-bottom: none;
      &__title {
        font-size: 14px;
        font-weight: bold;
      }
      &__btn {
        display: flex;
        align-items: center;
        gap: 10px;
      }
    }
    .table {
      border: 1px solid #ebeef5;
      border-top: none;
      .row__info {
      }
    }
    .row {
      &__desc {
        display: flex;
        align-items: center;
        gap: 20px;
        font-size: 12px;
        color: #909399;
      }
    }
  }
  ::v-deep .el-pagination {
    float: right;
  }
}
.threshold {
  &__title {
    display: flex;
    align-items: center;
    padding: 10px 4px;
    line-height: 16px;
    font-size: 12px;
    gap: 10px;
    border: 1px solid #ebeef5;
    border-bottom: none;
    &::before {
      content: '';
      display: inline-block;
      width: 4px;
      height: 14px;
      background-color: #5f3bce;
      margin-right: 4px;
      border-radius: 2px;
    }
  }
  &__content {
    margin-bottom: 10px;
  }
}
.radio-block {
  display: flex;
  align-items: center;
  &:not(:last-child) {
    margin-bottom: 10px;
  }
}
::v-deep .el-table.el-table--small td > .cell,
.el-table.el-table--small th > .cell {
  padding-right: 8px;
}
::v-deep .el-form-item__label {
  font-size: 14px;
  color: #0d1b3f;
}
</style>