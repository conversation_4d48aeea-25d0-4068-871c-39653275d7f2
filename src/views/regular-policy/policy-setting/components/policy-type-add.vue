<template>
  <el-dialog title="新增品牌政策设置" :visible.sync="visible" width="60%" top="6vh">
    <el-form label-width="100px" class="policy-type-add-form" :rules="rules" ref="policyTypeAddForm" :model="form">
      <el-form-item label="品牌名称" prop="brandId">
        <el-select v-model="form.brandId" placeholder="请选择品牌">
          <el-option v-for="item in getBrandList()" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="政策类型" prop="policyType">
        <el-radio-group v-model="form.policyType">
          <el-radio label="SALE">销售政策(随单政策)</el-radio>
          <el-radio label="SETTLEMENT">结算政策(后置月季返利)</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSubmit">确定</el-button>
        <el-button @click="visible = false">取消</el-button>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script>
import { regularPolicyExistsPolicy } from '@/api/regular-policy';
export default {
  props: {
    policyTypeAddVisible: Boolean
  },
  inject: ['getBrandList'],
  computed: {
    visible: {
      get() {
        return this.policyTypeAddVisible;
      },
      set(val) {
        this.$emit('update:policyTypeAddVisible', val);
      }
    }
  },

  data() {
    return {
      form: {
        brandId: '',
        policyType: ''
      },
      rules: {
        brandId: [{ required: true, message: '请选择品牌', trigger: 'change' }],
        policyType: [{ required: true, message: '请选择政策类型', trigger: 'change' }]
      }
    };
  },
  created() {},
  methods: {
    onSubmit() {
      this.$refs.policyTypeAddForm.validate((valid) => {
        if (valid) {
          console.log(' submit!!');
          const postData = {
            brandId: this.form.brandId,
            policyType: this.form.policyType
          };
          regularPolicyExistsPolicy(postData).then((res) => {
            if (res.data) {
              this.$message({
                type: 'error',
                message: '该品牌已有政策，请勿重复添加！'
              });
            } else {
              this.$router.push({
                path: '/regular-policy/policy-setting/policy-add',
                query: {
                  brandId: this.form.brandId,
                  brandName: this.getBrandList().find((item) => item.value === this.form.brandId).label,
                  policyType: this.form.policyType
                }
              });
              this.visible = false; // 关闭弹窗
            }
          });
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.policy-type-add-form {
  width: 500px;
}
::v-deep .el-select {
  width: 100%;
}
</style>