<template>
  <div class="page-container">
    <div class="page-title title-1">
      <div>{{ brandName || form.brandName }}-结算政策-{{ formType }}</div>
    </div>
    <div class="title-3 mg-btm-10">返利基础规则</div>
    <div class="page-tips">
      <div>默认按照最新的配置规则计算月季年返，对已发起OA审批的结算单不生效</div>
    </div>
    <el-form label-width="146px" class="settlement-form" :model="form" :rules="rules" ref="form">
      <el-form-item label="采购金额订单统计范围:" prop="orderType">
        <el-checkbox-group v-model="form.orderType" :disabled="isDetail">
          <el-checkbox label="DROPSHIPPING">水羊直供一件代发订单</el-checkbox>
          <el-checkbox label="NORMAL">水羊直供采销订单</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <!-- 月度|季度|年度返利规则 -->
      <div v-for="(item, index) in form.periodList" :key="index">
        <el-card class="box-card">
          <div slot="header" class="box-card__title clearfix">
            <span class="title-3">{{ settlementPeriodMap[item.settlementPeriod] }}返利规则</span>
            <i class="box-card__title-expand" :class="item.expand ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" @click="item.expand = !item.expand"></i>
          </div>
          <div v-show="item.expand">
            <el-table style="width: 100%" border :data="item.periodLadderList">
              <el-table-column label="阶梯" width="52px" type="index"></el-table-column>
              <el-table-column prop="amount" label="累计采购金额" width="400">
                <template slot-scope="scope">
                  <div class="column-inner flex-align-center gap-10">
                    <el-form-item label-width="0" :prop="`periodList[${index}].periodLadderList[${scope.$index}].purchaseMin`" :rules="[{ validator: validateNum, type: 'purchaseMin', index, periodIndex: scope.$index, scope, trigger: 'blur' }]">
                      <el-input v-model="scope.row.purchaseMin" placeholder="采购金额下限" size="mini" class="money-input" v-money :disabled="scope.$index > 0 || isDetail">
                        <template slot="append">
                          <span>元</span>
                        </template>
                      </el-input>
                    </el-form-item>
                    <i class="item-btn-text">-</i>
                    <el-form-item label-width="0" :prop="`periodList[${index}].periodLadderList[${scope.$index}].purchaseMax`" :rules="[{ validator: validateNum, type: 'purchaseMax', index, periodIndex: scope.$index, scope, trigger: 'change' }]">
                      <el-input v-model="scope.row.purchaseMax" placeholder="采购金额上限" size="mini" class="money-input" v-money :disabled="isDetail" @input="inputPurchaseMax($event, index, scope.$index)">
                        <template slot="append">
                          <span>元</span>
                        </template>
                      </el-input>
                    </el-form-item>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="condition" label="返利比例设置">
                <template slot-scope="scope">
                  <div class="column-inner">
                    <div v-for="(pItem, pIndex) in scope.row.periodDetailList" :key="pIndex" class="flex-align-center gap-10">
                      <!-- 返利类型 -->
                      <el-form-item label-width="0" :prop="`periodList[${index}].periodLadderList[${scope.$index}].periodDetailList[${pIndex}].configType`" :rules="{ required: true, message: '请选择', trigger: 'blur' }">
                        <el-select v-model="pItem.configType" placeholder="请选择返利类型" size="mini" class="type-select" :disabled="isDetail">
                          <!-- periodDetailList其他阶梯已经选过的configType，不允许再选 帮我实现下 -->

                          <el-option :label="item.label" :value="item.value" v-for="item in typeOptions" :key="item.value" :disabled="getUsedConfigTypes(index, scope.$index, pIndex).includes(item.value)"></el-option>
                        </el-select>
                      </el-form-item>
                      <!-- 前销激励门槛类型 -->
                      <el-form-item label-width="0" :prop="`periodList[${index}].periodLadderList[${scope.$index}].periodDetailList[${pIndex}].thresholdType`" v-if="pItem.configType === 'FRONT_END_SALES_INCENTIVES'" :rules="[{ required: true, message: '请选择', trigger: 'blur' }]">
                        <el-select v-model="pItem.thresholdType" placeholder="前销激励门槛类型" size="mini" class="type-select" :disabled="isDetail">
                          <el-option label="前销金额门槛" value="money"></el-option>
                          <el-option label="前销数量门槛" value="num"></el-option>
                        </el-select>
                      </el-form-item>
                      <!-- 前销激励门槛数量 -->
                      <el-form-item
                        label-width="0"
                        :prop="`periodList[${index}].periodLadderList[${scope.$index}].periodDetailList[${pIndex}].thresholdNum`"
                        v-if="pItem.configType === 'FRONT_END_SALES_INCENTIVES' && pItem.thresholdType === 'num'"
                        :rules="[{ validator: validatePositive, trigger: 'blur' }]"
                      >
                        <el-input v-model="pItem.thresholdNum" placeholder="输入门槛" size="mini" class="money-input" v-int :disabled="isDetail">
                          <template slot="append">
                            <span>件</span>
                          </template>
                        </el-input>
                      </el-form-item>
                      <!-- 前销激励门槛金额 -->
                      <el-form-item
                        label-width="0"
                        :prop="`periodList[${index}].periodLadderList[${scope.$index}].periodDetailList[${pIndex}].thresholdMoney`"
                        v-if="pItem.configType === 'FRONT_END_SALES_INCENTIVES' && pItem.thresholdType === 'money'"
                        :rules="[{ validator: validatePositive, trigger: 'blur' }]"
                      >
                        <el-input v-model="pItem.thresholdMoney" placeholder="输入门槛" size="mini" class="money-input" v-money :disabled="isDetail">
                          <template slot="append">
                            <span>元</span>
                          </template>
                        </el-input>
                      </el-form-item>
                      <!-- 比例 -->
                      <el-form-item label-width="0" :prop="`periodList[${index}].periodLadderList[${scope.$index}].periodDetailList[${pIndex}].rateValue`" :rules="[{ validator: validatePercent, trigger: 'blur' }]">
                        <el-input v-model="pItem.rateValue" placeholder="输入比例" size="mini" class="percent-input" :disabled="isDetail" v-money>
                          <template slot="append">%</template>
                        </el-input>
                      </el-form-item>
                      <el-button type="text" size="mini" class="item-btn-text" @click="onDelPolicyDetail(index, scope.$index, pIndex)" v-if="!isDetail">删除</el-button>
                    </div>
                    <el-button type="text" size="mini" class="mg-btm-10" v-if="scope.row.periodDetailList.length < 3 && !isDetail" @click="onAddPolicyDetail(index, scope.$index)">添加政策明细</el-button>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="操作" align="center" width="80px" v-if="!isDetail">
                <template slot-scope="scope">
                  <el-button type="text" size="mini" class="item-btn-text" @click="removePeriodLadder(index, scope.$index)">删除阶梯</el-button>
                </template>
              </el-table-column>
            </el-table>
            <div class="table-footer" v-if="!isDetail">
              <el-button type="primary" size="mini" class="fr" plain icon="el-icon-plus" @click="addPeriodLadder(index)" v-if="form.periodList[index].periodLadderList.length < 5">添加阶梯</el-button>
            </div>
          </div>
        </el-card>
      </div>
      <el-form-item label="活动规则说明" label-width="98px" prop="remarks">
        <el-input type="textarea" v-model="form.remarks" :disabled="isDetail"></el-input>
      </el-form-item>
    </el-form>
    <footer-bar v-if="isAdd || isEdit">
      <div class="footer-btn-box">
        <el-button @click="onCancel">取消</el-button>
        <el-button @click="submitForm" type="primary">保存</el-button>
      </div>
    </footer-bar>
  </div>
</template>

<script>
import { regularPolicySettlementCreateSettlementPolicy, regularPolicyUpdateSettlementPolicy, regularPolicyGetSettlementPolicy } from '@/api/regular-policy';
export default {
  props: {
    id: {
      type: String,
      default: ''
    },
    brandName: {
      type: String,
      default: ''
    },
    brandId: {
      type: String,
      default: ''
    },
    isEdit: {
      type: Boolean,
      default: false
    },
    isAdd: {
      type: Boolean,
      default: false
    },

    isDetail: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    formType() {
      if (this.isEdit) {
        return '编辑';
      }
      if (this.isAdd) {
        return '新增';
      }
      if (this.isDetail) {
        return '详情';
      }
    }
  },
  watch: {
    id(newValue) {
      console.log('watch id....', newValue);
      if (newValue) {
        this.getData();
      }
    }
  },
  data() {
    return {
      typeOptions: [
        // { label: '采货金额', value: 'PURCHASE_TARGET_ACHIEVED' },
        // { label: '前销激励', value: 'FRONT_END_SALES_INCENTIVES' },
        // { label: '合规经营返利', value: 'COMPLIANCE_SALES_AND_BUSINESS_INCENTIVES' }
      ],
      settlementPeriodMap: {
        MONTH: '月度',
        QUARTER: '季度',
        YEAR: '年度'
      },
      form: {
        brandId: '',
        brandName: '',
        orderType: [],
        remarks: '',
        periodList: [
          {
            settlementPeriod: 'MONTH',
            expand: true,
            periodLadderList: [
              {
                ladderNum: 0,
                purchaseMin: '',
                purchaseMax: '',
                periodDetailList: [
                  {
                    configType: '',
                    otherValue: '',
                    thresholdType: '',
                    thresholdNum: '',
                    thresholdMoney: '',
                    rateValue: ''
                  }
                ]
              }
            ]
          },
          {
            settlementPeriod: 'QUARTER',
            expand: true,
            periodLadderList: [
              {
                ladderNum: 0,
                purchaseMin: '',
                purchaseMax: '',
                periodDetailList: [
                  {
                    configType: '',
                    otherValue: '',
                    thresholdType: '',
                    thresholdNum: '',
                    thresholdMoney: '',
                    rateValue: ''
                  }
                ]
              }
            ]
          },
          {
            settlementPeriod: 'YEAR',
            expand: true,
            periodLadderList: [
              {
                ladderNum: 0,
                purchaseMin: '',
                purchaseMax: '',
                periodDetailList: [
                  {
                    configType: '',
                    otherValue: '',
                    thresholdType: '',
                    thresholdNum: '',
                    thresholdMoney: '',
                    rateValue: ''
                  }
                ]
              }
            ]
          }
        ]
      },
      rules: {
        orderType: [{ required: true, message: '请选择采购金额订单统计范围', trigger: 'change' }],
        remarks: [{ required: true, message: '请输入活动规则说明', trigger: 'blur' }],
        needSelectVal: [{ required: true, message: '请选择', trigger: 'blur' }]
      }
    };
  },
  created() {
    if (this.id) {
      this.getData();
    }
    this.dictData();
  },
  methods: {
    dictData() {
      this.typeOptions = [...this.$dict['regular_settlement_policy_rule_type']];
    },
    getData() {
      regularPolicyGetSettlementPolicy(this.id).then((res) => {
        res.data.periodList = res.data.periodList.map((item) => {
          item.periodLadderList = item.periodLadderList.map((ladder) => {
            ladder.periodDetailList = ladder.periodDetailList.map((detail) => {
              detail = {
                configType: detail.configType,
                rateValue: detail.rateValue,
                otherValue: detail.otherValue,
                thresholdType: '',
                thresholdNum: '',
                thresholdMoney: ''
              };
              if (detail.configType === 'FRONT_END_SALES_INCENTIVES') {
                const obj = JSON.parse(detail.otherValue);
                detail.thresholdType = obj.thresholdType;
                if (obj.thresholdType === 'num') {
                  detail.thresholdNum = obj.thresholdNum;
                } else {
                  detail.thresholdMoney = obj.thresholdMoney;
                }
              }
              return detail;
            });
            return ladder;
          });
          return { ...item, expand: true };
        });
        const monthData = res.data.periodList.find((item) => item.settlementPeriod === 'MONTH') || { periodLadderList: [] };
        const quarterData = res.data.periodList.find((item) => item.settlementPeriod === 'QUARTER') || { periodLadderList: [] };
        const yearData = res.data.periodList.find((item) => item.settlementPeriod === 'YEAR') || { periodLadderList: [] };
        this.form.periodList[0].periodLadderList = monthData.periodLadderList;
        this.form.periodList[1].periodLadderList = quarterData.periodLadderList;
        this.form.periodList[2].periodLadderList = yearData.periodLadderList;
        const { orderType, remarks, brandId, brandName } = res.data;
        this.form.orderType = orderType === 'ALL' ? ['DROPSHIPPING', 'NORMAL'] : [orderType];
        this.form.remarks = remarks;
        this.form.brandId = brandId;
        this.form.brandName = brandName;
      });
    },
    // 添加阶梯
    addPeriodLadder(index) {
      const ladderNum = this.form.periodList[index].periodLadderList.length;
      console.log('ladderNum===', ladderNum);
      let frontPeriodPurchaseMax = '';
      if (ladderNum > 0) {
        frontPeriodPurchaseMax = this.form.periodList[index].periodLadderList[ladderNum - 1]?.purchaseMax || '';
      }
      console.log('frontPeriodPurchaseMax===', frontPeriodPurchaseMax);
      this.form.periodList[index].periodLadderList.push({
        ladderNum: this.form.periodList[index].periodLadderList.length,
        purchaseMin: frontPeriodPurchaseMax,
        purchaseMax: '',
        periodDetailList: [
          {
            configType: '',
            otherValue: '',
            thresholdType: '',
            thresholdNum: '',
            thresholdMoney: '',
            rateValue: ''
          }
        ]
      });
    },
    // 删除阶梯
    removePeriodLadder(index, periodIndex) {
      this.form.periodList[index].periodLadderList.splice(periodIndex, 1);
    },
    // 输入采购金额上限时触发
    inputPurchaseMax(value, index, periodIndex) {
      // 如果是最后一个阶梯，则不需要设置下一个阶梯的下限
      if (this.form.periodList[index].periodLadderList.length - 1 === periodIndex) return;
      // 设置下一个阶梯的下限为当前阶梯的上限
      this.form.periodList[index].periodLadderList[periodIndex + 1].purchaseMin = value;
    },
    // 添加政策明细
    onAddPolicyDetail(index, periodIndex) {
      this.form.periodList[index].periodLadderList[periodIndex].periodDetailList.push({
        configType: '',
        otherValue: '',
        thresholdType: '',
        thresholdNum: '',
        thresholdMoney: '',
        rateValue: ''
      });
    },
    // 删除政策明细
    onDelPolicyDetail(index, periodIndex, detailIndex) {
      this.form.periodList[index].periodLadderList[periodIndex].periodDetailList.splice(detailIndex, 1);
      if (this.form.periodList[index].periodLadderList[periodIndex].periodDetailList.length === 0) {
        this.removePeriodLadder(index, periodIndex);
      }
    },
    // 获取当前阶梯已使用的configType
    getUsedConfigTypes(periodIdx, currentLadderIdx, currentDetailIdx) {
      const used = [];
      const periodDetailList = this.form.periodList[periodIdx]?.periodLadderList[currentLadderIdx]?.periodDetailList || [];
      periodDetailList.forEach((detail, detailIdx) => {
        if (currentDetailIdx !== detailIdx && detail.configType) {
          used.push(detail.configType);
        }
      });
      return used;
    },
    // 校验阶梯商品数量
    validateNum(rule, value, callback) {
      const { type, index, periodIndex, scope } = rule;
      console.log('validateNum rule', rule, type);
      console.log('validateNum value', value);
      if (value <= 0) {
        return callback(new Error('金额必须大于0'));
      }
      if (type === 'purchaseMax') {
        if (Number(scope.row.purchaseMax) <= Number(scope.row.purchaseMin)) {
          return callback(new Error('上限需 > 下限'));
        }
      }

      // 获取上一个阶梯的值
      if (periodIndex > 0) {
        const prevThreshold = this.form.periodList[index].periodLadderList[periodIndex - 1];
        console.log('prevThreshold', prevThreshold);
        if (type === 'purchaseMin') {
          if (Number(scope.row.purchaseMin) && Number(scope.row.purchaseMin) < Number(prevThreshold.purchaseMax)) {
            return callback(new Error('不能小于上一个阶梯的上限'));
          }
        }
      }
      console.log('sfsdfdfdsfdsf');
      return callback();
    },
    validatePositive(rule, value, callback) {
      if (value === '' || value === undefined) {
        return callback(new Error('请输入值'));
      }
      if (value <= 0) {
        return callback(new Error('值必须大于0'));
      }
      callback();
    },
    // 百分比校验
    validatePercent(rule, value, callback) {
      if (value === '' || value === undefined) {
        return callback(new Error('请输入比例'));
      }

      if (value > 100) {
        return callback(new Error('比例不能大于100'));
      }

      callback();
    },
    // 取消
    onCancel() {
      this.$back();
    },
    // 保存
    submitForm() {
      console.log('submitForm', this.form);
      this.$refs.form.validate((valid) => {
        console.log('valid====', valid);
        if (valid) {
          const postData = { ...this.form, brandId: this.brandId || this.form.brandId, brandName: this.brandName || this.form.brandName };
          // 月度｜季度｜年度 有一行数据就可以提交
          let hasData = false;
          // orderType数据处理 2个值时，设置为ALL
          postData.orderType = postData.orderType.length === 2 ? 'ALL' : postData.orderType[0];
          // 编辑需要加上政策ID
          if (this.isEdit) postData.id = this.id;
          // 处理阶梯数据
          postData.periodList = postData.periodList
            .map((item, index) => {
              if (item.periodLadderList.length !== 0) {
                hasData = true;
              }
              item.periodLadderList.map((periodLadder, periodIdx) => {
                periodLadder.periodDetailList.map((periodDetail, detailIdx) => {
                  if (periodDetail.configType === 'FRONT_END_SALES_INCENTIVES') {
                    const obj = { thresholdType: periodDetail.thresholdType };
                    if (periodDetail.thresholdType === 'num') {
                      obj.thresholdNum = periodDetail.thresholdNum;
                    } else {
                      obj.thresholdMoney = periodDetail.thresholdMoney;
                    }
                    periodDetail.otherValue = JSON.stringify(obj);
                  }
                  return { configType: periodDetail.configType, otherValue: periodDetail.otherValue, rateValue: periodDetail.rateValue };
                });
                return periodLadder;
              });
              return item;
            })
            .filter((item) => item.periodLadderList.length !== 0);
          if (!hasData) {
            this.$message.error('请至少添加一条: 月度｜季度｜年度阶梯数据');
            return;
          }
          const req = this.isAdd ? regularPolicySettlementCreateSettlementPolicy : regularPolicyUpdateSettlementPolicy;
          req(postData).then((res) => {
            console.log('res', res);
            if (res.code === '0') {
              this.$message({
                message: '操作成功',
                type: 'success'
              });
              this.$back();
            }
          });
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.flex-align-center {
  display: flex;
  align-items: center;
}

.gap-10 {
  gap: 10px;
}
.title-1 {
  font-size: 18px;
}
.title-2 {
  font-size: 16px;
}
.title-3 {
  font-size: 14px;
}
.mg-btm-10 {
  margin-bottom: 10px;
}
.page-title {
  font-weight: bold;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  gap: 10px;
}
.page-tips {
  width: 1400px;
  line-height: 16px;
  margin-bottom: 16px;
  background: #f7f8fa;
  color: #909399;
  padding: 8px 16px;
  font-size: 12px;
}
.settlement-form {
  width: 1400px;
  margin-bottom: 16px;
  .box-card {
    margin-bottom: 16px;
    &__title {
      display: flex;
      align-items: center;
      gap: 10px;
      &-expand {
        margin-left: auto;
        cursor: pointer;
        font-size: 16px;
        color: #909399;
      }
    }
    .table-footer {
      border: 1px solid #ebeef5;
      border-top-width: 0px;
      padding: 8px;
    }
  }
  .column-inner {
    .money-input {
      width: 180px;
    }
    .type-select {
      width: 150px;
    }
    .percent-input {
      width: 140px;
    }
    .item-btn-text {
      margin-bottom: 18px;
    }
    ::v-deep .el-input-group__append {
      background-color: #f5f7fa;
      color: #909399;
      border-color: #e6e8eb;
      border-left-width: 0px;
    }
  }
}
</style>