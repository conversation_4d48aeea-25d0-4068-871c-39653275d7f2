<template>
  <div class="add-commodity-container">
    <el-dialog :title="labelTitle || '选择商品'" :visible.sync="dialogVisible" :append-to-body="true" width="1200px" @close="handleDialogClose">
      <div class="top-info">
        <el-select v-model="brandIds" placeholder="品牌（可多选）" clearable multiple collapse-tags size="small" :disabled="brandDisabled">
          <el-option v-for="item in brandList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
        </el-select>
        <el-input placeholder="搜索商品名称" :validate-event="false" v-model="name" class="input-with-select" clearable></el-input>
        <el-input placeholder="搜索商品id" :validate-event="false" v-model="id" class="input-with-select" clearable></el-input>
        <el-input placeholder="搜索商品条码" :validate-event="false" v-model="specCode" class="input-with-select" clearable></el-input>
        <el-input placeholder="搜索规格标识" :validate-event="false" v-model="skuId" class="input-with-select" clearable></el-input>
        <el-select v-model="categoryId" placeholder="请选择分组" clearable>
          <el-option v-for="item in categoryIdOptions" :key="item.value" :label="item.name" :value="item.id"></el-option>
        </el-select>
        <el-select v-model="status" placeholder="请选择" clearable>
          <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
        <el-select v-model="isHidden" placeholder="是否是隐藏商品" clearable>
          <el-option v-for="item in isHiddenOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
        <el-button type="primary" @click="onSearch">查询</el-button>
        <el-button @click="onReset" type="primary">刷新</el-button>
        <div class="add-all">
          <p>
            如果找不到对应商品,可点击
            <span class="link" @click="clickLink('/commodity/list')">
              <span class="link">【商品管理】</span>
            </span>
            进行查看
          </p>
        </div>
      </div>
      <!-- 商品表格 -->
      <el-table :data="list" v-loading="listLoading" element-loading-text="加载中" fit highlight-current-row>
        <el-table-column property="id" label="id" width="160px"></el-table-column>
        <el-table-column label="商品缩略图">
          <template slot-scope="imgUrl">
            <img :src="imgUrl.row.thumbnailUrl" alt class="goods-img" v-if="!imgUrl.row.skuCommodityVO" />
            <img :src="imgUrl.row.skuCommodityVO.thumbnailUrl" alt class="goods-img" v-if="imgUrl.row.skuCommodityVO" />
          </template>
        </el-table-column>
        <el-table-column property="name" label="商品名称"></el-table-column>
        <el-table-column label="商品品牌">
          <template slot-scope="scope">
            <span>{{ scope.row.brandName || '-' }}</span>
          </template>
        </el-table-column>

        <el-table-column label="状态">
          <template slot-scope="scope">
            <div>
              {{ scope.row.statusName }}
            </div>
          </template>
        </el-table-column>
        <el-table-column property="stock" label="库存"></el-table-column>
        <el-table-column label="参与活动" max-width="250">
          <template slot-scope="scope">
            <div class="activity-list">
              <div v-for="(item, i) in scope.row.joinActivityList" :key="i">
                {{ item }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column property="isHidden" label="是否隐藏"
          ><template slot-scope="scope">
            {{ scope.row.isHidden === '1' ? '是' : '否' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right">
          <template slot-scope="operation">
            <div>
              <el-button :disabled="elFilterDisabled(operation.row)" @click="chooseSpec(operation.row)" type="text" key="addGift">选择规格</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="pageNo" :page-sizes="[5, 10, 20, 30]" :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper" :total="total" :disabled="listLoading"></el-pagination>
      </div>
    </el-dialog>
    <!-- 选择规格 -->
    <el-dialog width="40%" title="选择规格" :visible.sync="skuDialogVisble" append-to-body>
      <table class="stock-table" border="0" rules="none">
        <thead>
          <tr>
            <th v-for="(spec, i) in specList" :key="i">{{ spec.name }}</th>
            <th>价格</th>
            <th>库存</th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(sku, i) in skuList" :key="i">
            <td v-for="(spec, j) in specList" :key="j">
              {{ handleSpecData(i, j) }}
            </td>
            <td>{{ sku.price }}</td>
            <td>{{ sku.stock }}</td>
            <td>
              <span v-if="commoditySkuIdSet.has(sku.id)">已添加</span>
              <el-button @click="handleJoin(sku)" type="text" v-else>添加</el-button>
              <!-- 已添加按钮需要补齐 -->
            </td>
          </tr>
        </tbody>
      </table>
    </el-dialog>
  </div>
</template>

<script>
import { customReq, listSpec } from '@/api/common/commodity';
import { goodsType } from '@/api/commodity/list';
import { listAllBrandName } from '@/api/brand/brand-info';

export default {
  name: 'add-commodity',
  model: {
    prop: 'commodityList',
    event: 'updateTable'
  },
  props: {
    // 弹窗是否显示
    visible: {
      type: Boolean,
      default: false
    },
    // 按钮及弹窗标题文案
    labelTitle: String,
    // 外部控制组件的数据请求
    parseQuery: Function,
    // 已选择的全部商品，这里和commodityList的区别为，commodityList是当前活动的，selectedCommodityList是全部活动的
    selectedCommodityList: {
      type: Array,
      default: () => []
    },
    // 控制商品是否支持被选择
    filterDisabled: Function,
    // 单选传 false， 默认多选
    multiple: {
      type: Boolean,
      default: true
    },
    commodityList: Array,
    // create 不传默认表示新增， edit表示编辑， detail 表示详情
    operation: {
      type: String,
      default: 'create'
    },
    // 自定义的请求路径
    customReqUrl: {
      type: String
    },
    // 是否禁用品牌选择
    brandDisabled: {
      type: Boolean,
      default: false
    },
    // 默认品牌id列表
    defaultBrandIds: {
      type: Array,
      default: () => []
    }
  },
  filters: {
    parseStatus(status) {
      let result = '';
      switch (status) {
        case '0':
          result = '待上架';
          break;
        case '1':
          result = '上架';
          break;
        case '3':
          result = '下架';
          break;
      }
      return result;
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit('update:visible', val);
      }
    },
    commoditySet() {
      return new Set(this.commodityList.map(({ skuId }) => skuId));
    },
    commoditySkuIdSet() {
      return new Set([...this.commoditySet, ...this.selectedCommodityList]);
    },
    paginationList() {
      // 显示分页后的表格数据  已选中表格
      const commodityList = [...this.commodityList];
      if (commodityList.length <= this.tableSize) return commodityList;
      return commodityList.slice(this.tableSize * (this.tableCurrent - 1), this.tableSize * this.tableCurrent);
    },
    brandList() {
      return this.originBrandList.map((item) => ({
        value: item.id,
        label: item.name
      }));
    },
    // 已选中的商品sku数量
    skuNum() {
      const skuNum = this.commodityList.reduce((sum, { detailNum, commodityRelationDetailList: detailList = [] }) => sum + (detailList.length || detailNum), 0);
      return skuNum;
    }
  },
  created() {
    this.initData();
  },
  watch: {
    dialogVisible(val) {
      console.log('dialogVisible changed', val);
      if (val) {
        this.onReset();
      }
    },
    defaultBrandIds(val) {
      // 当默认品牌id变化时，更新品牌选择
      this.brandIds = val;
    },
    commodityList(val) {
      console.log('commodityList changed', val);
    }
  },
  data() {
    return {
      id: '', // 商品id搜索条件
      specCode: '',
      skuId: '',
      tableSize: 4,
      tableCurrent: 1,
      name: '',
      pageNo: 1,
      pageSize: 5,
      total: 0,
      skuDialogVisble: false,
      isHidden: '0', // 是否隐藏商品
      categoryId: '', // 商品分组id
      status: '1', // 商品上架状态
      brandIds: this.defaultBrandIds || [], // 品牌
      isHiddenOptions: [
        {
          value: '0',
          label: '非隐藏商品'
        },
        {
          value: '1',
          label: '隐藏商品'
        }
      ],
      statusOptions: [
        {
          value: '0',
          label: '待上架'
        },
        {
          value: '1',
          label: '上架中'
        },
        {
          value: '3',
          label: '已下架'
        }
      ],
      categoryIdOptions: [], // 商品分组
      originBrandList: [], // 所有的品牌
      specList: [], // 当前商品规格层级列表
      skuList: [], // 当前商品规格数据列表
      listLoading: false,
      list: [], // 商品列表数据
      currentRow: {},
      allChecked: false, // 是否全选 TODO
      allCheckedBtn: false // 能否全选 TODO
    };
  },
  methods: {
    // 数据初始化
    async initData() {
      await this.fetchBrand();
      this.getGoodsType();
    },
    // 获取品牌
    fetchBrand() {
      listAllBrandName().then((res) => {
        this.originBrandList = res?.data ?? [];
      });
    },
    // 获取商品分组
    getGoodsType() {
      goodsType().then((res) => {
        this.categoryIdOptions = res.data;
      });
    },
    // 控制商品是否可以被选择
    elFilterDisabled(row) {
      if (this.filterDisabled) return this.filterDisabled(row);
    },
    // 获取商品列表分页数据
    fetchData() {
      this.listLoading = true;
      const { parseQuery, pageNo, pageSize, name, status, id, specCode, skuId, categoryId, isHidden, brandIds } = this;
      const listQuery = {
        pageNo,
        pageSize,
        data: { name, status, id, specCode, skuId, categoryId, isHidden, brandIds }
      };
      const request = customReq;
      request(parseQuery ? parseQuery(listQuery) : listQuery, this.customReqUrl)
        .then((response) => {
          const { list = [], total = 0 } = response.data || {};
          list.forEach((i) => {
            if (this.commoditySet.has(i.id)) {
              i.isChecked = true;
            } else {
              i.isChecked = false;
              this.allCheckedBtn = true;
            }
          });
          this.list = list;
          this.total = total;
        })
        .finally(() => {
          this.listLoading = false;
        });
    },
    // 获取商品规格数据
    fetchSpec() {
      listSpec(this.currentRow.id)
        .then((res) => {
          if (res.data) {
            this.specList = res.data.specificationVOList;
            this.skuList = res.data.skuVOList;
          }
        })
        .finally(() => {
          this.listLoading = false;
        });
    },
    handleSpecData(i, j) {
      const levelName = this.specList[j].level;
      let level = '';
      switch (levelName) {
        case '1':
          level = 'firstLevel';
          break;
        case '2':
          level = 'secondLevel';
          break;
        case '3':
          level = 'threeLevel';
          break;
      }
      return this.skuList[i][level];
    },
    // 选择商品规格
    chooseSpec(row) {
      this.currentRow = row;
      this.skuDialogVisble = true;
      this.fetchSpec();
    },
    // 添加规格
    handleJoin(rowData) {
      let list = [...this.commodityList];
      const pushData = { ...this.currentRow };
      pushData.skuCommodityVO = { ...rowData };
      pushData.skuId = rowData.id;
      pushData.commodityId = this.currentRow.id;
      pushData.specCode = rowData.specCode;
      pushData.commodityName = this.currentRow.name;

      if (!this.multiple) {
        this.dialogVisible = false;
        list = [pushData];
      } else {
        list.push(pushData);
      }
      if (this.operation !== 'detail') {
        this.$emit('updateTable', list);
      }
    },
    onSearch() {
      this.pageNo = 1;
      this.pageSize = 5;
      this.fetchData();
    },
    onReset() {
      this.pageNo = 1;
      this.pageSize = 5;
      this.name = '';
      this.status = '1';
      this.id = '';
      this.specCode = '';
      this.skuId = '';
      this.categoryId = '';
      this.fetchData();
    },
    handleDialogClose() {
      if (this.list.length) {
        this.list.map((item) => (item.isChecked = false));
      }
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.pageNo = 1;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.pageNo = val;
      this.fetchData();
    },
    // 跳转到商品管理页面
    clickLink(link) {
      this.dialogVisible = false;
      this.$router.push(link);
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
$border: 1px solid #e5e5e5;
$color: rgba(0, 0, 0, 0.65);
$color-sub: rgba(27, 23, 23, 0.85);
.router-links {
  margin-top: 15px;
}
.goods-img {
  width: 60px;
  height: 60px;
}

.add-commodity {
  color: var(--color-primary);
  position: relative;
  .on-off {
    position: absolute;
    right: 0;
  }
  span {
    cursor: pointer;
  }
}

.top-info {
  margin-bottom: 16px;
  ::v-deep .el-input {
    width: 250px;
    margin: 0 16px 10px 0;
  }
  ::v-deep .el-select__tags {
    top: 38%;
  }
  .link {
    color: var(--color-primary);
    cursor: pointer;
  }
  ::v-deep .el-input__validateIcon {
    display: none;
  }
}

.stock-table {
  width: 100%;
  border: 1px solid #e5e5e5;
  text-align: left;
  & > thead {
    background-color: #f8f8f8;
    th {
      font-weight: 400;
      color: $color-sub;
    }
  }
  & > tfoot {
    border-top: $border;
  }
  td,
  th {
    padding: 16px 10px;
  }
  td {
    color: $color;
    border-top: $border;
    & + td {
      border-left: $border;
    }
  }
  .foot-label {
    color: $color-sub;
  }
  .links {
    > * + * {
      margin-left: 2px;
    }
  }
  .btns {
    margin-left: 8px;
  }
}

.activity-list {
  max-height: 200px;
  overflow: auto;
}

.add-all {
  display: flex;
  align-items: center;
  margin: 10px 0;
  .el-checkbox {
    margin-left: 20px;
  }
  .el-button {
    margin: 0 20px;
  }
}
.input-with-select {
  width: 250px;
  margin-left: 10px;
  ::v-deep .el-input-group__prepend {
    background-color: #fff;
  }
}

.commodity-tip {
  margin-left: 10px;
  font-size: 12px;
  color: #333;
  cursor: initial !important;
}
</style>
