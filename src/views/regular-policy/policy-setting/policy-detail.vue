<template>
  <div>
    <sales-policy v-if="policyType === 'SALE'" :brandId="brandId" :brandName="brandName" isDetail :id="id" />
    <settlement-policy v-else :brandId="brandId" :brandName="brandName" isDetail :id="id" />
  </div>
</template>

<script>
import SalesPolicy from './components/sales-policy';
import SettlementPolicy from './components/settlement-policy';
export default {
  name: 'regular-policy-policy-setting-policy-detail',
  components: {
    SalesPolicy,
    SettlementPolicy
  },
  props: {},
  data() {
    return {
      policyType: 'SALE', // 类型 sales-销售政策 SETTLEMENT-结算政策
      brandName: '',
      brandId: '',
      id: ''
    };
  },
  onShow() {
    const { policyType = 'SALE', brandName = '', brandId = '', id = '' } = this.$route.query;
    this.policyType = policyType;
    this.brandName = brandName || '';
    this.brandId = brandId || '';
    this.id = id || '';
  }
};
</script>

<style>
</style>