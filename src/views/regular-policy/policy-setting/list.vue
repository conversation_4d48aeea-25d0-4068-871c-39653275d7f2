<template>
  <div class="page-container">
    <sy-normal-table v-bind="table" ref="table" />
    <!-- 新增品牌政策 -->
    <PolicyTypeAdd :policyTypeAddVisible.sync="policyTypeAddVisible" v-if="policyTypeAddVisible" />
    <!-- 操作记录-->
    <el-dialog title="操作记录" :visible.sync="dialogVisible" width="60%" top="8vh">
      <common-operate-record v-if="dialogVisible" :biz-id="bizId" :biz-type="bizType" />
    </el-dialog>
  </div>
</template>

<script>
import { listAll } from '@/api/brand/list';
import { parseTime } from '@/utils';
import { regularPolicyList, regularPolicyChangeStatus, regularPolicyChangeShowStatus } from '@/api/regular-policy';

import CommonOperateRecord from '@/components/CommonOperateRecord';
import PolicyTypeAdd from './components/policy-type-add.vue';
export default {
  name: 'RegularPolicyList',
  data() {
    return {
      dialogVisible: false,
      policyTypeAddVisible: false,
      bizId: '',
      bizType: '',
      brandList: []
    };
  },
  provide() {
    return {
      getBrandList: () => [...this.brandList]
    };
  },
  components: { CommonOperateRecord, PolicyTypeAdd },
  computed: {
    table() {
      const that = this;
      return {
        tip: [
          {
            text: '1、该页面规则生效于在<span style="font-weight:bold;">水羊直供平台</span>下单的【渠道分类归属于线上分销】分销商'
          },
          {
            text: '2、活动仅生效于非年框客户(即在活动生效期间内,维护了年框政策的分销商不参与)。'
          }
        ],
        filters() {
          return [
            {
              tag: 'sy-select',
              prop: 'brandIdList',
              label: '品牌名称：',
              bind: {
                placeholder: '请选择品牌',
                filterable: true,
                flashOptions: true,
                multiple: true,
                options: async () => {
                  const res = await listAll();
                  console.log('res===', res);
                  const arr = (res.data || []).map((item) => ({
                    value: item.id,
                    label: item.name
                  }));
                  that.brandList = [...arr];
                  // arr.unshift({ value: '', label: '全部' });
                  return arr;
                }
              }
            }
          ];
        },
        btns() {
          return [
            {
              text: '新增品牌政策',
              type: 'primary',
              code: 'regular-policy-policy-setting-policy-add',
              call() {
                that.policyTypeAddVisible = true;
                // that.$router.push('/activity/integral/list');
              }
            }
          ];
        },
        colspan: [0],
        columns() {
          return [
            {
              label: '品牌名称',
              prop: 'brandId',
              render: (h, { row }) => (
                <div v-frag>
                  <span>{row.brandName}</span>
                </div>
              )
            },
            {
              label: '政策类型',
              prop: 'policyType',
              width: 180,
              render: (h, { row }) => (
                <div v-frag>
                  <div>{row.policyType === 'SALE' ? '销售政策' : '结算政策'}</div>
                </div>
              )
            },
            {
              label: '政策说明',
              prop: 'remarks'
            },
            {
              label: '活动时间',
              width: 220,
              render: (h, { row }) => {
                const children = [];

                if (row.policyType === 'SALE') {
                  children.push(h('div', [h('div', `开始：${parseTime(row.beginDate)}`), h('div', `结束：${parseTime(row.endDate)}`)]));
                }

                if (row.policyType === 'SETTLEMENT') {
                  if (row.status !== '1') {
                    children.push(h('div', '-'));
                  } else {
                    const settlementChildren = [];
                    if (row.periodTypeEnumList && row.periodTypeEnumList.includes('MONTH')) {
                      settlementChildren.push(h('div', '月度返利使用中'));
                    }
                    if (row.periodTypeEnumList && row.periodTypeEnumList.includes('QUARTER')) {
                      settlementChildren.push(h('div', '季度返利使用中'));
                    }
                    if (row.periodTypeEnumList && row.periodTypeEnumList.includes('YEAR')) {
                      settlementChildren.push(h('div', '年度返利使用中'));
                    }
                    settlementChildren.push(h('div'));

                    children.push(h('div', settlementChildren));
                  }
                }
                return h('div', children);
              }
            },
            {
              label: '活动状态',
              prop: 'status',
              width: 80,
              render: (h, { row }) => (
                <div v-frag>
                  <div>{row.status === '0' ? '停用' : '进行中'}</div>
                </div>
              )
            },
            {
              label: '是否展示给分销商',
              width: 120,
              render: (h, { row }) => (
                <div v-frag>
                  <el-switch value={row.izShow === '1'} on-change={() => that.changeEnable(row)}></el-switch>
                </div>
              )
            },
            {
              label: '操作',
              type: 'btns',
              itemBind: {
                fixed: 'right'
              },
              btns({ row }) {
                return [
                  {
                    text: '查看',
                    type: 'text',
                    call() {
                      that.$router.push(`/regular-policy/policy-setting/policy-detail?policyType=${row.policyType}&id=${row.id}`);
                    }
                  },
                  {
                    hide: row.status === '1',
                    text: '编辑',
                    type: 'text',
                    code: 'regular-policy-policy-setting-policy-edit',
                    call() {
                      that.$router.push(`/regular-policy/policy-setting/policy-edit?policyType=${row.policyType}&id=${row.id}`);
                    }
                  },
                  {
                    hide: row.status === '0',
                    text: '停用',
                    type: 'text',
                    call: () => that.handleSataus(row)
                  },
                  {
                    hide: row.status === '1',
                    text: '启用',
                    type: 'text',
                    confirm: '确定启用吗？',
                    call: () => that.handleSataus(row)
                  },
                  {
                    text: '规则变更记录',
                    type: 'text',
                    call() {
                      that.bizId = row.id;
                      that.bizType = row.policyType === 'SALE' ? 'DISTRIBUTOR_REGULAR_SALE_POLICY' : 'DISTRIBUTOR_REGULAR_SETTLEMENT_POLICY';
                      that.dialogVisible = true;
                    }
                  }
                ];
              }
            }
          ];
        },
        async search({ filtersValue, pageFilter }) {
          const { pageNo, pageSize } = pageFilter;
          const data = await regularPolicyList({
            data: filtersValue,
            pageNo,
            pageSize
          });
          const list = data.data?.list || [];
          const result = [];
          list.forEach((item) => {
            item.detail.forEach((detail, index) => {
              result.push({
                brandId: item.brandId,
                brandName: item.brandName,
                ...detail
              });
            });
          });
          const total = data.data?.total || 0;
          return {
            list: result,
            total
          };
        }
      };
    }
  },
  methods: {
    // 政策启用|停用
    handleSataus(row) {
      const { id, status, policyType } = row;
      const newStatus = status === '1' ? '0' : '1';
      regularPolicyChangeStatus({ id, status: newStatus, policyType })
        .then((res) => {
          if (res.code === '0') {
            this.$message.success(res.msg);
            this.$refs.table.handlerSearch();
          }
        })
        .catch(() => {
          this.$message.error('操作失败，请稍后再试');
        });
    },
    // 是否展示给分销商-切换状态
    changeEnable(row) {
      const { id, izShow, policyType } = row;
      this.$confirm(`确认${izShow === '1' ? '不展示' : '展示'}给分销商吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          const postData = {
            id,
            policyType,
            izShow: izShow === '1' ? '0' : '1'
          };
          regularPolicyChangeShowStatus(postData).then((res) => {
            if (res.code === '0') {
              this.$message.success(res.msg);
              this.$refs.table.handlerSearch();
            }
          });
        })
        .catch(() => {
          this.$message.info('已取消操作');
        });
    }
  }
};
</script>

<style>
</style>