<template>
  <el-dialog
    title="采购金额构成"
    :visible.sync="dialogVisible"
    width="78%"
    @open="refresh"
  >
    <sy-normal-table v-bind="table" ref="table" />
  </el-dialog>
</template>
<script>
import {
  regularPolicySettlementListSettlementOrderItem,
  regularPolicySettlementExportExcelDetail,
  regularPolicySettlementStatSettlementOrderTotalAmount
} from '@/api/regular-policy';
import download from '@/utils/download';
import { parseTime } from '@/utils';
import { isNumber } from 'lodash';
export default {
  props: {
    row: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      dialogVisible: false,
      exportLoading: false,
      totalNum: ''
    };
  },
  computed: {
    table() {
      const that = this;
      return {
        tableBind: {
          height: '400px'
        },
        btns: [
          {
            text: '导出',
            type: 'primary',
            code: 'yearly-policy-management-settle-policy-issuance-export',
            confirm: '是否导出数据？',
            bind: {
              loading: that.exportLoading
            },
            call: ({ filtersValue }) => that.onExport(filtersValue)
          },
          {
            hide: !isNumber(that.totalNum),
            type: 'text',
            text: `实收金额合计(元)：${that.totalNum}`
          }
        ],
        filters: [
          {
            tag: 'sy-date-picker',
            prop: ['startDate', 'endDate'],
            label: '发货时间范围',
            bind: {
              bind: {
                type: 'daterange',
                rangeSeparator: '至',
                startPlaceholder: '开始时间',
                endPlaceholder: '结束时间',
                valueFormat: 'timestamp',
                defaultTime: ['00:00:00', '23:59:59']
              }
            }
          },
          {
            tag: 'el-input',
            prop: 'omsOrderCode',
            label: 'OMS订单编号',
            bind: { placeholder: '请输入' }
          },
          {
            tag: 'el-input',
            prop: 'orderNo',
            label: '原始订单编号',
            bind: { placeholder: '请输入' }
          },
          {
            tag: 'el-input',
            prop: 'commodityBarcode',
            label: '条形码',
            bind: { placeholder: '请输入' }
          }
        ],
        columns() {
          return [
            { label: 'OMS订单编号', prop: 'omsOrderCode', width: 140 },
            { label: '原始订单编号', prop: 'orderNo', width: 180 },
            { label: '发货时间', prop: 'deliveryTime', type: 'time', width: 140, format: 'yyyy-MM-dd hh:mm:ss' },
            { label: '商品名称', prop: 'commodityName', multiLine: 3, itemBind: { minWidth: 200 } },
            { label: '商品条码', prop: 'commodityBarcode', width: 120 },
            { label: '商品数量', prop: 'commodityCnt' },
            { label: '实收金额', prop: 'orderItemAmt' },
            { label: '渠道主数据名称', prop: 'channelName' }
          ];
        },
        async search({ filtersValue, pageFilter }) {
          const data = { ...filtersValue, settlementId: that.row.settlementId };
          const params = {
            data,
            ...pageFilter
          };
          that.getTotalInfo(data);
          const res = await regularPolicySettlementListSettlementOrderItem(params);
          const { list = [], total = 0 } = res.data;
          return {
            list,
            total
          };
        }
      };
    }
  },
  methods: {
     openDialog () {
      this.dialogVisible = true;
    },
    getTotalInfo(data) {
      regularPolicySettlementStatSettlementOrderTotalAmount(data).then((res) => {
        this.totalNum = res.data;
      });
    },
    // 刷新表格
    refresh() {
      const ref = this.$refs.table;
      ref && ref.handlerSearch();
    },
    // 导出
    onExport(filtersValue) {
      this.exportLoading = true;
      regularPolicySettlementExportExcelDetail({
        ...filtersValue,
        settlementId: this.row.settlementId
      })
        .then((res) => {
          download(
            res,
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            `${this.row.settlementId}-采购金额明细数据导出-${parseTime(Date.now(),
            '{y}-{m}-{d}-{h}:{i}:{s}')}.xlsx`
          );
        })
        .finally(() => {
          this.exportLoading = false;
        });
    }
  }
};
</script>

<style scoped lang="scss"></style>
