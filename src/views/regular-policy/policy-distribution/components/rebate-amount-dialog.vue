<template>
  <el-dialog
    :visible.sync="dialogVisible"
    title="人工调整返利金额"
    width="600px"
  >
    <div class="dialog-tips">
      <p>说明：适用于某些项目没有达成需要特殊调减返利金额的场景</p>
    </div>
    <el-form label-width="80px" :model="formData" :rules="rules" ref="formData">
      <el-form-item label="调整类型" prop="operationType">
        <sy-select v-model="formData.operationType" v-bind="operationTypeBind" />
      </el-form-item>
      <el-form-item label="调整金额" prop="adjustAmount">
        <el-input
          v-model="formData.adjustAmount"
          type="number"
          placeholder="请输入调整金额"
        />
      </el-form-item>
      <el-form-item label="调整明细" prop="settlementRule">
        <sy-select v-model="formData.settlementRule" v-bind="{
          options: 'regular_settlement_policy_rule_type'
        }" />
      </el-form-item>
      <el-form-item label="调整原因" prop="adjustRemark">
        <el-input
          v-model="formData.adjustRemark"
          type="textarea"
          placeholder="请输入调整原因"
        />
      </el-form-item>
    </el-form>
    <div slot="footer">
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="saveBtn" :loading="saveBtnLoading">确 定</el-button>
    </div>
  </el-dialog>
</template>
<script>
import {
  regularPolicySettlementAdjustCreditBack
} from '@/api/regular-policy';

export default {
  props: {
    row: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      rules: {
        operationType: [
          { required: true, message: '请选择调整类型', trigger: 'change' }
        ],
        adjustAmount: [
          { required: true, message: '请输入调整金额', trigger: 'blur' },
        ],
        settlementRule: [
          { required: true, message: '请选择调整明细', trigger: 'change' }
        ],
        adjustRemark: [
          { required: true, message: '请输入调整原因', trigger: 'blur' }
        ]
      },
      dialogVisible: false,
      formData: {
        operationType: '',
        adjustAmount: '',
        settlementRule: '',
        adjustRemark: ''
      },
      operationTypeBind: {
        options: [
          { label: '调增', value: 'ADD' },
          { label: '调减', value: 'SUB' }
        ]
      },
      saveBtnLoading: false
    };
  },
  methods: {
    openDialog () {
      this.dialogVisible = true;
      this.formData = {
        operationType: '',
        adjustAmount: '',
        settlementRule: '',
        adjustRemark: ''
      };
      this.$nextTick(() => {
        this.$refs.formData.clearValidate();
      });
    },
    saveBtn () {
      this.$refs.formData.validate(valid => {
        if (valid) {
          this.saveBtnLoading = true;
          regularPolicySettlementAdjustCreditBack({
            ...this.formData,
            settlementId: this.row.id
          }).then(res => {
            this.$message.success(res.msg);
            this.dialogVisible = false;
            this.$emit('success-callback');
          }).finally(() => {
            this.saveBtnLoading = false;
          });
        }
      });
    }
  }
};
</script>