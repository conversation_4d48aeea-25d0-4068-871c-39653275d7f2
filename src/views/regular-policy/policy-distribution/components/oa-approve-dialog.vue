<template>
  <el-dialog
    :visible.sync="dialogVisible"
    :title="approveInfo.title"
    width="650px"
  >
    <div class="dialog-tips">
      <p>{{ approveInfo.content }}</p>
    </div>
    <p v-for="item in tableSelected" :key="item.distributorId" style="line-height: 30px;">
      <span>分销商ID：<b>{{ item.distributorId }}</b></span>
      <span style="margin-left: 10px;">分销商名称：<b>{{ item.shopName }}</b></span>
    </p>
    <div slot="footer">
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="saveBtn" :loading="saveBtnLoading">确 定</el-button>
    </div>
  </el-dialog>
</template>
<script>
import {
  regularPolicySettlementApprovalSettlement,
  regularPolicySettlementBrandApprovalSettlement
} from '@/api/regular-policy';

export default {
  props: {
    tableSelected: {
      type: Array,
      default: () => ([])
    },
    approveInfo: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      dialogVisible: false,
      saveBtnLoading: false
    };
  },
  methods: {
    openDialog () {
      this.dialogVisible = true;
    },
    saveBtn () {
      this.saveBtnLoading = true;
      const url = this.approveInfo.flag === 'brand' ? regularPolicySettlementBrandApprovalSettlement : regularPolicySettlementApprovalSettlement
      url({
        izIssue: this.approveInfo.izIssue,
        settlementIds: this.tableSelected.map(item => item.id)
      }).then(res => {
        this.$message.success(res.msg);
        this.dialogVisible = false;
        this.$emit('success-callback');
      }).finally(() => {
        this.saveBtnLoading = false;
      });
    }
  }
};
</script>