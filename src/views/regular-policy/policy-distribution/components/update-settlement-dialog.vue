<template>
  <el-dialog
    :visible.sync="dialogVisible"
    title="更新结算信息"
    width="600px"
  >
    <div class="dialog-tips">
      <p>重新获取分销商的发货金额和对应结算规则的返利;</p>
      <p>只对待审核的分销商生效,专属顾问已审批的不生效。</p>
    </div>
    <el-form label-width="80px" :model="formData" :rules="rules" ref="formData">
      <el-form-item label="品牌" prop="brandId">
        <sy-select v-model="formData.brandId" v-bind="brandListBind" />
      </el-form-item>
      <el-form-item label="结算周期" prop="settlementTypeValue" class="filters-item">
        <cycle-date-picker :type="settlementType" v-model="formData.settlementTypeValue" />
      </el-form-item>
    </el-form>
    <div slot="footer">
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="saveBtn" :loading="saveBtnLoading">确 定</el-button>
    </div>
  </el-dialog>
</template>
<script>
import {
  regularPolicySettlementReCalSettlementOrder
} from '@/api/regular-policy';
import { listAllBrandName } from '@/api/brand/brand-info';

export default {
  props: {
    settlementType: {
      type: String,
      default: ''
    },
    row: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      brandListBind: {
        options: async () => {
          const res = await listAllBrandName();
          return res.data.map((item) => ({
            value: item.id,
            label: item.name
          }));
        }
      },
      rules: {
        brandId: [
          { required: true, message: '请选择品牌', trigger: 'change' }
        ],
        settlementTypeValue: [
          { required: true, message: '请选择结算周期', trigger: 'change' }
        ]
      },
      dialogVisible: false,
      formData: {
        brandId: '',
        settlementTypeValue: ''
      },
      saveBtnLoading: false
    };
  },
  methods: {
    openDialog () {
      this.dialogVisible = true;
      this.formData = {
        brandId: '',
        settlementTypeValue: ''
      };
      this.$nextTick(() => {
        this.$refs.formData.clearValidate();
      });
    },
    saveBtn () {
      this.$refs.formData.validate(valid => {
        if (valid) {
          this.saveBtnLoading = true;
          regularPolicySettlementReCalSettlementOrder({
            ...this.formData,
            settlementType: this.settlementType
          }).then(res => {
            this.$message.success(res.msg);
            this.dialogVisible = false;
            this.$emit('success-callback');
          }).finally(() => {
            this.saveBtnLoading = false;
          });
        }
      });
    }
  }
};
</script>