<template>
  <el-dialog
    :visible.sync="dialogVisible"
    title="上传凭证"
    width="600px"
  >
    <sy-upload v-model="formData.attachment" v-bind="attachmentBind"></sy-upload>
    <p class="color-info">最多上传5个，每个不超过5M</p>
    <div slot="footer">
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="saveBtn" :loading="saveBtnLoading">确 定</el-button>
    </div>
  </el-dialog>
</template>
<script>
import {
  regularPolicySettlementUploadAttachment
} from '@/api/regular-policy';

export default {
  props: {
    row: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      dialogVisible: false,
      saveBtnLoading: false,
      formData: {
        attachment: []
      }
    };
  },
  computed: {
    attachmentBind() {
      return {
        bind: {
          limit: 5
        },
        size: 5
      };
    },
  },
  methods: {
    openDialog () {
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.formData = {
          attachment: this.row.attachmentList || []
        };
      });
    },
    saveBtn () {
      if (this.formData.attachment.length === 0) {
        this.$message.error('请上传凭证');
        return;
      }
      this.saveBtnLoading = true;
      regularPolicySettlementUploadAttachment({
        ...this.formData,
        settlementId: this.row.id
      }).then(res => {
        this.$message.success(res.msg);
        this.dialogVisible = false;
        this.$emit('success-callback');
      }).finally(() => {
        this.saveBtnLoading = false;
      });
    }
  }
};
</script>