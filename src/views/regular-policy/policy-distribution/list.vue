<template>
  <div class="page-container">
    <el-radio-group v-model="searchForm.settlementType" @change="settlementTypeChange">
      <el-radio-button
        v-for="item in settlementTypeOptions"
        :key="item.value"
        :label="item.value"
      >{{ item.label + '返利发放'}}</el-radio-button>
    </el-radio-group>
    <div class="distribution-tips">
      <p>每月1日9:00会结算并生成上月参与返利的品牌的审核记录</p>
      <p>
        <Authority auth="regular-policy-policy-distribution-update">
          <el-button type="primary" @click="openDialog({}, 'UpdateSettlementDialog')">更新结算信息</el-button>
        </Authority>
      </p>
      <p>使用说明：</p>
      <p>1、专属顾问在该页面审核符合单品牌采货金额门槛的分销商；</p>
      <p>2、可以根据分销商达成和品牌政策规则调整返利金额并上传前销凭证等数据；</p>
      <p>3、调整完成后，需要发起OA审批，注：只能针对同一品牌,同一结算周期一起提交OA审批流程；</p>
    </div>
    <div class="sy-normal-table">
      <!-- 查询条件 -->
      <div class="search-container"> 
        <el-form class="filters" :model="searchForm" :label-position="'right'">
          <el-form-item label="分销商名称" class="filters-item">
            <el-input v-model="searchForm.shopName" clearable />
          </el-form-item>
          <el-form-item label="分销商ID" class="filters-item">
            <el-input v-model="searchForm.distributorId" clearable />
          </el-form-item>
          <el-form-item label="状态" class="filters-item">
            <sy-select filterable multiple v-model="searchForm.auditStatusList" v-bind="{
              options: 'regular_settlement_policy_audit_status'
            }" />
          </el-form-item>
          <el-form-item label="专属顾问" class="filters-item">
            <sy-select filterable multiple v-model="searchForm.csIdList" v-bind="csIdListBind" />
          </el-form-item>
          <el-form-item label="品牌名称" class="filters-item">
            <sy-select filterable multiple v-model="searchForm.brandIdList" v-bind="brandListBind" />
          </el-form-item>
          <el-form-item label="结算周期" class="filters-item">
            <cycle-date-picker :type="searchForm.settlementType" v-model="searchForm.settlementTypeValue" />
          </el-form-item>
        </el-form>
        <div class="search-item-handle">
          <el-button type="primary" @click="searchBtn">查询</el-button>
          <el-button @click="clearBtn">清空</el-button>
        </div>
      </div>
      <!-- 按钮 -->
      <div>
        <Authority auth="regular-policy-policy-distribution-cs-approve-yes">
          <el-button
            :disabled="tableSelected.length === 0"
            type="primary"
            @click="oaApproveBtn({
              izIssue: 1,
              title: '提示',
              content: '提示：请确认勾选的以下的分销商发放返利，待品牌审批!',
              flag: 'cs'
            })"
          >通过，待发放</el-button>
        </Authority>
        <Authority auth="regular-policy-policy-distribution-cs-approve-no">
          <el-button
            :disabled="tableSelected.length === 0"
            type="primary"
            @click="oaApproveBtn({
              izIssue: 0,
              title: '专属顾问审核，不发放',
              content: '提示：请确认勾选的以下分销商不发放返利，后续也无法再发起返利发放流程!',
              flag: 'cs'
            })"
          >不通过，不发放</el-button>
        </Authority>
        <Authority auth="regular-policy-policy-distribution-brand-approve">
          <el-button
            :disabled="tableSelected.length === 0"
            type="primary"
            @click="oaApproveBtn({
              izIssue: 1,
              title: '发起OA审批',
              content: '提示：请确认勾选的以下分销商统一发起OA审批,若审批驳回则这一批分销商都需要重新提交OA审批!',
              flag: 'brand'
            })"
          >通过，发起OA流程</el-button>
        </Authority>
        <Authority auth="regular-policy-policy-distribution-brand-reject">
          <el-button
            :disabled="tableSelected.length === 0"
            type="primary"
            @click="oaApproveBtn({
              izIssue: 0,
              title: '提示',
              content: '提示：驳回到专属顾问重新提交资料!',
              flag: 'brand'
            })"
          >不通过，待重新修改</el-button>
        </Authority>
      </div>
      <!-- 表格 -->
      <el-table
        :data="tableData"
        v-loading="tableLoading"
        @select-all="tableSelect"
        @select="tableSelect"
      >
        <el-table-column type="selection" width="50" fixed="left"></el-table-column>
        <el-table-column label="序号" type="index" width="50" fixed="left"></el-table-column>
        <el-table-column label="结算信息" width="180">
          <template slot-scope="scope">
            <div class="table-cell-content">
              <span>品牌：{{ scope.row.brandName }}</span>
              <span>结算周期：{{ scope.row.settlementTypeValue }}</span>
              <span>结算单：{{ scope.row.settlementCode }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="分销商信息" width="220">
          <template slot-scope="scope">
            <div class="table-cell-content">
              <span>分销商ID：{{ scope.row.distributorId }}</span>
              <span>分销商名称：{{ scope.row.shopName }}</span>
              <span>专属顾问：{{ scope.row.csName }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="采购金额信息" align="center">
          <el-table-column prop="purchaseAmount" label="周期内发货金额（元）" width="120">
            <template slot-scope="scope">
              <span>{{ dataDeal.formatMoney(scope.row.purchaseAmount) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="人工调整参与计算发货金额（元）" width="140">
            <template slot-scope="scope">
              <div class="table-cell-content">
                <span>
                  <span>{{ scope.row.operationType === 'ADD' ? '调增' : '调减' }}</span>：
                  <span>{{ dataDeal.formatMoney(scope.row.adjustAmount) }}</span>
                </span>
                <span>备注：{{ scope.row.adjustRemark }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="actualPurchaseAmount" label="实际参与返利计算发货金额" width="120">
            <template slot-scope="scope">
              <span>{{ dataDeal.formatMoney(scope.row.actualPurchaseAmount) }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="返利金额发放信息" align="center">
          <el-table-column width="260">
            <template slot="header">
              <div class="table-header-content">
                <span>返利明细计算</span>
                <span style="font-weight: normal;">（返利金额=发货金额*返利比例+人工调整）</span>
              </div>
            </template>
            <template slot-scope="scope">
              <div
                class="table-cell-content"
                v-for="(item, index) in scope.row.settlementDetailVOList"
                :key="index"
              >
                <!-- 返利明细计算 -->
                <div>
                  <span>
                    <b>{{item.settlementRuleName}}</b>
                    <span v-if="item.otherValueVO && item.otherValueVO.thresholdMoney">（金额门槛：{{item.otherValueVO.thresholdMoney || '-'}} 元）</span>
                    <span v-if="item.otherValueVO && item.otherValueVO.thresholdNum">（数量门槛：{{item.otherValueVO.thresholdNum || '-'}} 件）</span>
                  </span>：
                  <div>
                    <span>{{ item.actualPurchaseAmount }} * {{ (item.calculateValue / 100).toFixed(4) }} {{ scope.row.operationType === 'ADD' ? '+' : '-' }} {{ item.adjustAmount }}</span>
                    = {{ dataDeal.formatMoney(item.actualIssueAmount) }}
                  </div>
                </div>
                <span v-if="item.remarks"><b>调整原因：</b>{{ item.remarks }}</span>
                <!-- 分割线 -->
                <div
                  v-if="index !== scope.row.settlementDetailVOList.length - 1"
                  style="display: block; height: 1px; width: 100%; margin: 6px 0; background-color: #dcdfe6;"
                ></div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="actualCreditBackAmount" label="实际发放返利金额合计（元）" width="120">
            <template slot-scope="scope">
              <span>{{ dataDeal.formatMoney(scope.row.actualCreditBackAmount) }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="审批凭证" align="center">
          <el-table-column prop="violateNum" label="控价违规记录（次）" width="100"></el-table-column>
          <el-table-column label="进销存凭证" width="150">
            <template slot-scope="scope">
              <div class="table-cell-content">
                <span>进销存录入次数：{{ scope.row.psiNum || '-' }}</span>
                <span>当月销量合计：{{ scope.row.psiSaleNum || '-' }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="专属顾问人工上传凭证" width="150">
            <template slot-scope="scope">
              <div class="table-cell-content">
                <a
                  v-for="item in scope.row.attachmentList"
                  :key="item.url"
                  :href="item.url"
                  target="_blank"
                  class="file-link"
                ><i class="el-icon-paperclip" style="margin-right: 3px;"></i>{{ item.name }}</a>
              </div>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column prop="auditStatusName" label="审批状态" width="100"></el-table-column>
        <el-table-column prop="settlementOaUrl" label="关联审批OA" width="100">
          <template slot-scope="scope">
              <div class="table-cell-content">
                <a
                  :href="scope.row.settlementOaUrl"
                  target="_blank"
                  class="file-link"
                  v-if="scope.row.settlementOaUrl"
                ><i class="el-icon-paperclip" style="margin-right: 3px;"></i>查看OA</a>
              </div>
            </template>
        </el-table-column>
        <el-table-column label="操作" width="180" fixed="right">
          <template slot-scope="scope">
            <div class="table-cell-content">
              <Authority auth="regular-policy-policy-distribution-purchase-amount">
                <el-button
                  type="text"
                  @click="openDialog(scope.row, 'RebatePurchaseAmountDialog')"
                  v-if="['WAIT_AUDIT', 'OA_REJECT'].includes(scope.row.auditStatus)"
                >调整参与返利计算发货金额</el-button>
              </Authority>
              <Authority auth="regular-policy-policy-distribution-amount">
                <el-button
                  type="text"
                  @click="openDialog(scope.row, 'RebateAmountDialog')"
                  v-if="['WAIT_AUDIT', 'OA_REJECT'].includes(scope.row.auditStatus)"
                >调整返利金额</el-button>
              </Authority>
              <Authority auth="regular-policy-policy-distribution-upload">
                <el-button
                  type="text"
                  @click="openDialog(scope.row, 'UploadAttachmentDialog')"
                  v-if="['WAIT_AUDIT', 'OA_REJECT'].includes(scope.row.auditStatus)"
                >上传前销凭证</el-button>
              </Authority>
              <Authority auth="regular-policy-policy-distribution-purchase-detail">
                <el-button
                  type="text"
                  @click="openDialog(scope.row, 'PurchaseAmountDialog')"
                >查看采购金额明细</el-button>
              </Authority>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination-container">
      <el-pagination
        :current-page="tableParams.pageNo"
        :page-size="tableParams.pageSize"
        :total="tableData.length"
        layout="total, prev, pager, next, jumper"
        @current-change="getTableData"
      />
    </div>
    <!-- 更新结算信息 弹窗 -->
    <UpdateSettlementDialog :settlementType="searchForm.settlementType" ref="UpdateSettlementDialog" @success-callback="getTableData" />
    <!-- 调整参与返利计算发货金额 弹窗 -->
    <RebatePurchaseAmountDialog :row="tableRow" ref="RebatePurchaseAmountDialog" @success-callback="getTableData" />
    <!-- 调整返利金额 弹窗 -->
    <RebateAmountDialog :row="tableRow" ref="RebateAmountDialog" @success-callback="getTableData" />
    <!-- 上传前销凭证 弹窗 -->
    <UploadAttachmentDialog :row="tableRow" ref="UploadAttachmentDialog" @success-callback="getTableData" />
    <!-- OA审批 弹窗 -->
    <OaApproveDialog :approveInfo="approveInfo" :tableSelected="tableSelected" ref="OaApproveDialog" @success-callback="getTableData" />
    <!-- 查看采购金额明细 弹窗 -->
    <PurchaseAmountDialog :row="tableRow" ref="PurchaseAmountDialog" />
  </div>
</template>
<script>
import {
  regularPolicySettlementList
} from '@/api/regular-policy';
import dict from '@/components/Common/dicts';
import dataDeal from '@/utils/dataDeal';
import { listAllBrandName } from '@/api/brand/brand-info';
import RebatePurchaseAmountDialog from './components/rebate-purchase-amount-dialog.vue';
import RebateAmountDialog from './components/rebate-amount-dialog.vue';
import UploadAttachmentDialog from './components/upload-attachment-dialog.vue';
import UpdateSettlementDialog from './components/update-settlement-dialog.vue';
import OaApproveDialog from './components/oa-approve-dialog.vue';
import PurchaseAmountDialog from './components/purchase-amount-dialog.vue';

export default {
  name: 'regular-policy-policy-distribution-list',
  components: {
    RebatePurchaseAmountDialog,
    RebateAmountDialog,
    UploadAttachmentDialog,
    UpdateSettlementDialog,
    OaApproveDialog,
    PurchaseAmountDialog
  },
  data() {
    return {
      approveInfo: {},
      tableRow: {},
      updateDialogVisible: false,
      tableData: [],
      activeName: 'month',
      tableParams: {
        data: {},
        pageNo: 1,
        pageSize: 10
      },
      searchForm: {
        shopName: '',
        distributorId: '',
        auditStatusList: [],
        csIdList: [],
        brandIdList: [],
        settlementType: 'MONTH',
        settlementTypeValue: ''
      },
      csIdListBind: {
        options: async () => await dict('COMMON_EXPAND_ADVISER')
      },
      brandListBind: {
        options: async () => {
          const res = await listAllBrandName();
          return res.data.map((item) => ({
            value: item.id,
            label: item.name
          }));
        }
      },
      settlementTypeOptions: window.QIANKUN_DATA.dictMap['regular_policy_time_period_type'],
      tableLoading: false,
      tableSelected: []
    };
  },
  computed: {
    dataDeal() {
      return dataDeal;
    },
  },
  created() {
    this.getTableData();
  },
  methods: {
    tableSelect (selection) {
      this.tableSelected = selection;
    },
    oaApproveBtn (item) {
      this.approveInfo = item;
      if (item.flag === 'cs') {
        const isAllow = this.tableSelected.every(item => ['WAIT_AUDIT', 'OA_REJECT', 'BRAND_REJECT'].includes(item.auditStatus));
        if (!isAllow) {
          this.$message.error('只能提交【待专属顾问审核】【OA审核驳回，待专属顾问修改资料】【品牌已驳回，待专属顾问修改】的记录');
          return;
        }
      }
      if (item.flag === 'brand') {
        const isAllow = this.tableSelected.every(item => item.auditStatus === 'CS_AUDITED');
        if (!isAllow) {
          this.$message.error('只能提交【专属顾问已审核，待发放】的记录');
          return;
        }
      }
      // 同品牌、同结算周期才可以提交
      const brandId = this.tableSelected[0].brandId;
      const settlementTypeValue = this.tableSelected[0].settlementTypeValue;
      const isSameBrand = this.tableSelected.every(item => item.brandId === brandId);
      const isSameSettlementType = this.tableSelected.every(item => item.settlementTypeValue === settlementTypeValue);
      if (!isSameBrand || !isSameSettlementType) {
        this.$message.error('只能提交同一品牌、同结算周期的记录');
        return;
      }
      this.$refs.OaApproveDialog.openDialog();
    },
    settlementTypeChange () {
      this.getTableData();
    },
    searchBtn () {
      this.tableParams.pageNo = 1;
      this.getTableData();
    },
    getTableData() {
      this.tableLoading = true;
      regularPolicySettlementList({
        ...this.tableParams,
        data: this.searchForm  
      }).then((res) => {
        this.tableData = res.data.list;
        this.tableSelected = [];
      }).finally(() => {
        this.tableLoading = false;
      });
    },
    clearBtn () {
      this.searchForm = {
        shopName: '',
        distributorId: '',
        auditStatusList: [],
        csIdList: [],
        brandIdList: [],
        settlementType: this.searchForm.settlementType,
        settlementTypeValue: ''
      };
    },
    openDialog(row, dialogName) {
      this.$refs[dialogName].openDialog();
      this.tableRow = row;
    },
  }
};
</script>

<style lang="scss">
.distribution-tips {
  margin: 10px 0;
  display: flex;
  flex-direction: column;
  row-gap: 4px;
  background-color: #e0dbff;
  font-size: 12px;
  padding: 10px;
}
.dialog-tips {
  display: flex;
  flex-direction: column;
  row-gap: 10px;
  background-color: #e0dbff;
  margin-bottom: 20px;
  padding: 10px;
}
.table-header-content {
  display: flex;
  flex-direction: column;
  row-gap: 2px;
}
.table-cell-content {
  display: flex;
  flex-direction: column;
  row-gap: 4px;
}
.dialog-content {
  width: 300px;
  margin: 0 auto;
}
.file-link{
  color: var(--color-primary) !important;
  text-decoration: underline;
  display: inline-block;
  max-width: 140px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  vertical-align: middle;
}
.pagination-container {
  text-align: right;
}
</style>
