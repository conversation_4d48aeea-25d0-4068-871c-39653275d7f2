<template>
  <div class="app-container">
    <div class="table-container">
      <div class="time">
        数据更新时间：{{ Date.now() | parseTime('{y}-{m}-{d} {h}:{i}:{s}') }}
      </div>
      <form @submit.prevent="onSearch" class="filter-list">
        <div class="filter-item">
          <span class="label">统计时间:</span>
          <el-radio-group v-model="time" size="medium">
            <el-radio-button label="all">全部</el-radio-button>
            <el-radio-button label="0">今日</el-radio-button>
            <el-radio-button label="7">近7天</el-radio-button>
            <el-radio-button label="15">近15天</el-radio-button>
            <el-radio-button label="THIS_MONTH">本月</el-radio-button>
            <el-radio-button label="LAST_MONTH">上月</el-radio-button>
          </el-radio-group>
          <el-date-picker
            v-model="timeRange"
            type="datetimerange"
            align="right"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :default-time="['00:00:00', '23:59:59']"
          ></el-date-picker>
          <!-- <el-button native-type="submit" size="small" type="primary"
            >查询</el-button
          > -->
        </div>
      </form>
      <Instructions :showTitle="true" :colNum="0">
        <template slot="title">
          <p>
            时间的筛选只会影响总数据的统计，针对统计到“今日”、“近30日”的统计数据是不按照时间筛选来统计的，默认按照当前时间来统计的
          </p>
        </template>
      </Instructions>
      <div class="row">
        <div class="row__left">
          <div class="row__title">分销商数据</div>
          <div class="row__wrap">
            <GeneralSituation
              :list="distributorStatisticsVO"
              v-loading="listLoading"
            ></GeneralSituation>
          </div>
        </div>
        <div class="row__right">
          <div class="row__title">
            <span
              >动销数据
              <span class="row__title--gray"
                >（统计的是分销商维度的数据）</span
              ></span
            >
          </div>
          <div class="row__wrap">
            <GeneralSituation
              :list="distributorBuyStatisticsVO"
              v-loading="listLoading"
            ></GeneralSituation>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="row__left">
          <div class="row__title">
            <span
              >销售数据<span class="row__title--gray"
                >（统计的是分销商维度的销售数据）</span
              ></span
            >
          </div>
          <div class="row__wrap">
            <GeneralSituation
              :list="saleAmountStatisticsVO"
              v-loading="listLoading"
            ></GeneralSituation>
          </div>
        </div>
        <div class="row__right">
          <div class="row__title">返点数据</div>
          <div class="row__wrap">
            <GeneralSituation
              :list="virtualCreditStatisticsVO"
              v-loading="listLoading"
            ></GeneralSituation>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="row__left">
          <div class="row__title">
            <div>平台热销品牌前5</div>
            <div class="row__title--gray" @click="readMore('brand')">
              查看更多>
            </div>
          </div>
          <div class="row__wrap">
            <TableDisplay
              v-loading="listBrand5Loading"
              :colum="columBrand"
              :tableData="listBrand5"
            ></TableDisplay>
          </div>
        </div>
        <div class="row__right">
          <div class="row__title">
            <div>
              <el-radio-group v-model="tabBrand" size="mini">
                <el-radio-button label="PURCHASE"
                  >采销热销品牌前5</el-radio-button
                >
                <el-radio-button label="DROP_SHIPPING"
                  >一件代发热销品牌前五</el-radio-button
                >
              </el-radio-group>
            </div>
            <div class="row__title--gray" @click="readMore('brand', tabBrand)">
              查看更多>
            </div>
          </div>
          <div class="row__wrap">
            <TableDisplay
              v-loading="purchaseTypeListBrandLoading"
              :colum="columBrand"
              :tableData="purchaseTypeListBrand5"
            ></TableDisplay>
          </div>
        </div>
      </div>
      <div class="row">
        <div class="row__left">
          <div class="row__title">
            <div>平台热销商品前5</div>
            <div class="row__title--gray" @click="readMore('commodity')">
              查看更多>
            </div>
          </div>
          <div class="row__wrap">
            <TableDisplay
              v-loading="listCommodity5Loading"
              :colum="columGoods"
              :tableData="listCommodity5"
            ></TableDisplay>
          </div>
        </div>
        <div class="row__right">
          <div class="row__title">
            <div>
              <el-radio-group v-model="tabGoods" size="mini">
                <el-radio-button label="PURCHASE"
                  >采销热销商品前5</el-radio-button
                >
                <el-radio-button label="DROP_SHIPPING"
                  >一件代发热销商品前五</el-radio-button
                >
              </el-radio-group>
            </div>
            <div
              class="row__title--gray"
              @click="readMore('commodity', tabGoods)"
            >
              查看更多>
            </div>
          </div>
          <div class="row__wrap">
            <TableDisplay
              v-loading="purchaseTypeListCommodityLoading"
              :colum="columGoods"
              :tableData="purchaseTypeListCommodity5"
            ></TableDisplay>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  list,
  listBrandSaleStatistic,
  listCommoditySaleStatistic
} from '@/api/customer-management/general-situation';
import GeneralSituation from '@/components/views/customer-management/GeneralSituation';
import TableDisplay from '@/components/views/customer-management/GeneralSituation/TableDisplay.vue';
import { getTimeRange } from '@/utils/date';
export default {
  name: 'general-situation',
  components: {
    GeneralSituation,
    TableDisplay
  },
  data() {
    return {
      listLoading: false,
      time: 'all', // 统计时间
      timeRange: null, // 筛选项时间范围,
      distributorBuyStatisticsVO: [], // 动销数据
      distributorStatisticsVO: [], // 分销商数据
      saleAmountStatisticsVO: [], // 销售数据
      virtualCreditStatisticsVO: [], // 返利数据
      listBrand5: [], // 平台热销品牌前5
      listBrand5Loading: false,
      purchaseTypeListBrand5: [], // 按商品采货类型展示的热销品牌前5
      purchaseTypeListBrandLoading: false,
      listCommodity5: [], // 平台热销商品前5
      listCommodity5Loading: false,
      purchaseTypeListCommodity5: [], // 按商品采货类型展示的热销商品前5
      purchaseTypeListCommodityLoading: false,
      // 热销品牌colum
      columBrand: [
        {
          label: '序号',
          value: 'rankingNum'
        },
        {
          label: '品牌名称',
          value: 'brandName'
        },
        {
          label: '销售金额',
          value: 'saleAmount'
        }
      ],
      // 热销商品colum
      columGoods: [
        {
          label: '序号',
          value: 'rankingNum'
        },
        {
          label: '商品id',
          value: 'commodityId'
        },
        {
          label: '商品名称',
          value: 'commodityName'
        },
        {
          label: '销售金额',
          value: 'saleAmount'
        }
      ],
      // 默认显示-采销热销品牌前5
      tabBrand: 'PURCHASE',
      // 默认显示-采销热销商品前5
      tabGoods: 'PURCHASE'
    };
  },
  activated() {
    this.fetchListData();
    this.fetchListBrand5('', 'listBrand5Loading');
    this.fetchListBrand5(this.tabBrand, 'purchaseTypeListBrandLoading');
    this.fetchListCommodity('', 'listCommodity5Loading');
    this.fetchListCommodity(this.tabGoods, 'purchaseTypeListCommodityLoading');
  },
  computed: {
    data() {
      const data = {};
      if (this.timeRange && this.timeRange.length) {
        data.startDate = this.timeRange[0].getTime();
        data.endDate = this.timeRange[1].getTime();
      }
      if (this.time && this.time !== 'all') {
        const { startDate, endDate } = getTimeRange(this.time);
        data.startDate = startDate;
        data.endDate = endDate;
      }
      return data;
    }
  },
  watch: {
    // time和timeRange不能同时有值
    time(value) {
      if (value) {
        this.timeRange = [];
      }
      this.onSearch();
    },
    timeRange(value) {
      if (value && value.length) {
        this.time = '';
      }
      this.onSearch();
    },
    // 当切换热销品牌前五的种类时（采销和一件代发）
    tabBrand(value) {
      this.fetchListBrand5(value, 'purchaseTypeListBrandLoading');
    },
    // 当切换热销商品前五的种类时（采销和一件代发）
    tabGoods(value) {
      this.fetchListCommodity(value, 'purchaseTypeListCommodityLoading');
    }
  },
  methods: {
    // 查询
    onSearch() {
      this.fetchListData();
      this.fetchListBrand5('', 'listBrand5Loading');
      this.fetchListBrand5(this.tabBrand, 'purchaseTypeListBrandLoading');
      this.fetchListCommodity('', 'listCommodity5Loading');
      this.fetchListCommodity(
        this.tabGoods,
        'purchaseTypeListCommodityLoading'
      );
    },
    // 获取平台热销品牌前5数据
    fetchListBrand5(purchaseType, loadingName) {
      this[loadingName] = true;
      const listQuery = {
        data: {
          ...this.data,
          purchaseType: purchaseType,
          isDataPermission: '0'
        },
        pageNo: 1,
        pageSize: 5
      };
      listBrandSaleStatistic(listQuery)
        .then((response) => {
          if (!purchaseType) {
            // 平台热销品牌前5
            this.listBrand5 = response.data.list || [];
          } else {
            // 不同商品采货类型（采销和一件代发）平台热销品牌前5数据
            this.purchaseTypeListBrand5 = response.data.list || [];
          }
        })
        .finally(() => {
          this[loadingName] = false;
        });
    },
    // 获取平台热销商品前5数据
    fetchListCommodity(purchaseType, loadingName) {
      this[loadingName] = true;
      const listQuery = {
        data: {
          ...this.data,
          purchaseType: purchaseType,
          isDataPermission: '0'
        },
        pageNo: 1,
        pageSize: 5
      };
      listCommoditySaleStatistic(listQuery)
        .then((response) => {
          if (!purchaseType) {
            // 平台热销商品前5
            this.listCommodity5 = response.data.list || [];
          } else {
            // 不同商品采货类型（采销和一件代发）平台热销商品前5数据
            this.purchaseTypeListCommodity5 = response.data.list || [];
          }
        })
        .finally(() => {
          this[loadingName] = false;
        });
    },
    // 获取分销商数据、动销数据、销售数据、返点数据
    fetchListData() {
      this.listLoading = true;
      list(this.data)
        .then((response) => {
          // this.list = response.data.list || [];
          const {
            distributorBuyStatisticsVO = {},
            distributorStatisticsVO = {},
            saleAmountStatisticsVO = {},
            virtualCreditStatisticsVO = {}
          } = response.data || {};

          // 分销商数据
          const distributorStatisticsTitleArr = [
            '平台分销商总数',
            '一件代发分销商总数',
            '采销分销商总数',
            '今日运维客户新增数',
            '今日一件代发新增数',
            '今日采销新增数'
          ];
          // 分销商数据今日接口字段，与title保持一一对应
          const distributorStatisticsAmountArr = [
            'totalNum',
            'totalDropShippingNum',
            'totalPurchaseNum',
            'todayNewNum',
            'todayNewDropShippingNum',
            'todayNewPurchaseNum'
          ];
          // 分销商数据昨日数据接口字段，与title保持一一对应
          const distributorStatisticsYesterdayAmountArr = [
            null,
            null,
            null,
            'yesterdayNewNum',
            'yesterdayNewDropShippingNum',
            'yesterdayNewPurchaseNum'
          ];
          // 是否展示箭头
          const distributorStatisticsIsArrowShowArr = [
            null,
            null,
            null,
            true,
            true,
            true
          ];
          // 分销商数据格式化
          this.distributorStatisticsVO = this.transformData(
            distributorStatisticsVO,
            distributorStatisticsTitleArr,
            distributorStatisticsAmountArr,
            distributorStatisticsYesterdayAmountArr,
            distributorStatisticsIsArrowShowArr
          );
          // 动销数据title
          const distributorBuyStatisticsTitleArr = [
            '未动销分销商数',
            '一件代发未动销数',
            '采销未动销数',
            '近30日未动销数',
            '近30日一件代发未动销数',
            '近30日采销未动销数'
          ];
          // 动销数据接口字段，与title保持一一对应
          const distributorBuyStatisticsAmountArr = [
            'totalNotBuyNum',
            'totalNotBuyDropShippingNum',
            'totalNotBuyPurchaseNum',
            'thirtyDaysNotBuyNum',
            'thirtyDaysNotBuyDropShippingNum',
            'thirtyDaysNotBuyPurchaseNum'
          ];
          // 动销数据格式化
          this.distributorBuyStatisticsVO = this.transformData(
            distributorBuyStatisticsVO,
            distributorBuyStatisticsTitleArr,
            distributorBuyStatisticsAmountArr
          );
          // 销售数据title
          const saleAmountStatisticsTitleArr = [
            '平台回款总额',
            '一件代发回款总额',
            '采销回款总额',
            '平台客单价',
            '一件代发客单价',
            '采销客单价'
          ];
          // 销售数据接口字段，与title保持一一对应
          const saleAmountStatisticsAmountArr = [
            'totalSaleAmount',
            'totalDropShippingSaleAmount',
            'totalPurchaseSaleAmount',
            'averageOrderAmount',
            'averageDropShippingOrderAmount',
            'averagePurchaseOrderAmount'
          ];
          // 销售数据格式化
          this.saleAmountStatisticsVO = this.transformData(
            saleAmountStatisticsVO,
            saleAmountStatisticsTitleArr,
            saleAmountStatisticsAmountArr
          );
          // 返点数据title
          const virtualCreditStatisticsTitleArr = [
            '平台总返点金额',
            '一件代发活动返点金额',
            '采销活动返点金额',
            '平台返点使用金额',
            '一件代发分销商返点使用金额',
            '采销分销商返点使用金额'
          ];
          // 返点数据接口字段，与title保持一一对应
          const virtualCreditStatisticsAmountArr = [
            'totalVirtualCredit',
            'totalDropShippingVirtualCredit',
            'totalPurchaseVirtualCredit',
            'totalUsedVirtualCredit',
            'totalUsedDropShippingVirtualCredit',
            'totalUsedPurchaseVirtualCredit'
          ];
          // 返点数据格式化
          this.virtualCreditStatisticsVO = this.transformData(
            virtualCreditStatisticsVO,
            virtualCreditStatisticsTitleArr,
            virtualCreditStatisticsAmountArr
          );
        })
        .finally(() => {
          this.listLoading = false;
        });
    },
    // 查看更多
    readMore(type, purchaseType = '') {
      let path = '';
      const params = {
        ...this.data,
        purchaseType: purchaseType,
        isDataPermission: '0'
      };
      if (type === 'brand') {
        path = '/shop/brand-data/list';
        sessionStorage.setItem('/shop/brand-data/list', JSON.stringify(params));
      }
      if (type === 'commodity') {
        path = '/shop/goods-data';
        sessionStorage.setItem('/shop/goods-data', JSON.stringify(params));
      }
      this.$router.push({
        path
      });
    },
    // 数据格式转换，将接口返回的数据转换成组件需要的数据格式,传参分别为：名称、今日价格，昨日价格，是否展示箭头
    transformData(
      listData,
      titleArr,
      amountArr,
      yesterdayAmountArr = [null, null, null, null, null, null],
      isArrowShowArr = [null, null, null, null, null, null]
    ) {
      const data = titleArr.map((titleItem, titleIndex) => {
        return {
          title: titleItem,
          amount: listData[amountArr[titleIndex]] || 0,
          yesterdayAmount: listData[yesterdayAmountArr[titleIndex]],
          isArrowShow: isArrowShowArr[titleIndex]
        };
      });
      return data;
    }
  }
};
</script>

<style lang="scss" scoped>
.time {
  margin-bottom: 16px;
}
.filter-list {
  margin-bottom: 16px;
}

.row {
  display: flex;
  // justify-content: space-between;
  &__wrap {
    width: 100%;
    max-width: 542px;
    border: 1px solid #ccc;
  }
  &__title {
    display: flex;
    justify-content: space-between;
    font-weight: 600;
    height: 60px;
    align-items: center;
    &--gray {
      color: #999;
      font-weight: 400;
      cursor: pointer;
    }
  }
  &__left {
    margin-right: 80px;
    width: 45%;
    max-width: 542px;
  }
  &__right {
    width: 45%;
    max-width: 542px;
  }
}
</style>
