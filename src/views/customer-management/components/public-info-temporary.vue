<template>
  <!-- 临时客户基础信息 -->
  <div class="public-info">
    <div class="base-info-top">
      <div class="base-info">
        <img
          src="https://oss.syounggroup.com/static/file/soyoung-zg/aliyun/soyoung-zg/mp/dptx.png"
          alt
          class="avatar"
          width="60"
          height="60"
          fit="cover"
        />
        <div class="base-info-middle">
          <div class="name">
            {{ distributorHeaderVO.name | filterVO }}
          </div>
          <div class="account">
            <!-- 1. 运维客户管理、临时客户池、公海客户池、跟进管理-运维跟进 点击 ‘查看信息’/‘跟进记录’ 进去的分销商信息页/分销商跟进记录页  左上角的注册账号都是解密的分销商登录手机号（DISTRIBUTOR_MOBILE），点击页面内的‘完善客户资料’/‘补充资料’都是解密DISTRIBUTOR_LEADS_CONTACT_PHONE，取的bizId都是资料弹窗的id。
            2. 跟进管理-线索跟进 点击跟进记录/查看信息 进去的左上角的手机号码都是团队线索手机号（DISTRIBUTOR_LEADS_MOBILE），用的id是整个页面的id。点击‘完善客户资料’/‘补充资料’和第一条一样，解密的是DISTRIBUTOR_LEADS_CONTACT_PHONE，取的bizId都是资料弹窗的id。但是点击‘编辑基础信息’取的是DISTRIBUTOR_LEADS_MOBILE -->
            手机号码：
            <CryptoBlock
              detail-auth="/distributor-management/:decrypt-mobile"
              :bizId="id"
              bizType="DISTRIBUTOR_LEADS_MOBILE"
              v-model.trim="distributorHeaderVO.mobile"
            />
          </div>
        </div>
        <div class="base-info-right">
          <span class="status gold"
            >意向程度：{{
              distributorHeaderVO.interestedDegreeName | filterVO
            }}</span
          >
          <div class="adviser">
            拓展顾问：{{
              distributorHeaderVO.developCustomerServiceName | filterVO
            }}
          </div>
        </div>
      </div>
      <div class="handler-btns" v-if="!isDetails">
        <Authority :auth="authorityCode">
          <el-button type="primary" @click="dialogBasicInfoVisible = true"
            >编辑基础信息</el-button
          >
        </Authority>
        <Authority :auth="authorityCode" v-if="!banEditInfo">
          <el-button type="primary" @click="editCustomerInfo"
            >完善客户资料</el-button
          >
        </Authority>
      </div>
    </div>
    <div class="base-info-bottom">
      <div class="data-list">
        <div>
          <div class="label">商家类型</div>
          <div class="value">
            {{ distributorHeaderVO.merchantTypeName | filterVO }}
          </div>
        </div>
        <div>
          <div class="label">意向采购</div>
          <div class="value">
            {{ distributorHeaderVO.purchaseTypeName | filterVO }}
          </div>
        </div>
        <div>
          <div class="label">店铺销量</div>
          <div class="value">
            {{ distributorHeaderVO.monthlySalesName | filterVO }}
          </div>
        </div>
      </div>
    </div>
    <!-- 弹窗 -->
    <el-dialog
      :title="title"
      :visible.sync="dialogVisible"
      width="800px"
      v-if="dialogVisible"
    >
      <customer-info
        :authorityCode="authorityCode"
        @onSuccess="() => $emit('onSuccess')"
        :id="id"
        @onCancel="onCancel"
        @closeDialog="dialogVisible = false"
      />
    </el-dialog>
    <!-- DialogBasicInfoUpdate 编辑基础信息-->
    <DialogBasicInfoUpdate
      :authorityCode="authorityCode"
      :dialogVisible.sync="dialogBasicInfoVisible"
      @onSuccess="$emit('onSuccess')"
      :id="id"
    />
  </div>
</template>
<script>
import CustomerInfo from './customer-Info';
import DialogBasicInfoUpdate from './DialogBasicInfoUpdate';
export default {
  components: {
    CustomerInfo,
    DialogBasicInfoUpdate
  },
  name: 'public-info',
  data() {
    return {
      dialogVisible: false,
      title: '',
      // 弹窗类型
      dialogUpdateTitle: '',
      dialogBasicInfoVisible: false
    };
  },

  props: {
    id: String,
    distributorBodyVO: Object, // 分销商合作信息
    distributorHeaderVO: Object, // 分销商头部信息
    isDetails: Boolean, // 是否是查看
    authorityCode: String, // 按钮权限编码
    banEditInfo: {
      // 是否显示完善客户资料
      type: Boolean,
      default: false
    }
  },
  filters: {
    filterVO(v) {
      if (v) return v;
      return '暂无';
    }
  },
  computed: {},
  activated() {},
  methods: {
    onCancel() {},
    editCustomerInfo() {
      this.title = '完善客户资料';
      this.dialogVisible = true;
    }
  }
};
</script>

<style lang="scss" scoped>
.public-info {
  &--mag {
    margin-bottom: 25px;
  }
  .base-info-top {
    display: flex;
    justify-content: space-between;
    .base-info {
      display: flex;
      align-items: center;
      .avatar {
        border: 1px solid #ddd;
      }
      .base-info-middle {
        font-size: 16px;
        margin-left: 20px;
        .account {
          font-size: 14px;
          margin-top: 20px;
          color: #666;
        }
      }
      .base-info-right {
        font-size: 14px;
        margin-left: 20px;
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        height: 60px;
        justify-content: flex-end;
        .status {
          font-size: 12px;
          color: #fff;
          padding: 5px 8px;
          &.normal {
            background: #67c23a;
          }
          &.frozen {
            background: #f56c6c;
          }
          &.gold {
            background: #daa520;
          }
        }
        .adviser {
          margin-top: 10px;
          padding: 5px 10px;
          background: #ddd;
        }
      }
    }
  }
  .base-info-bottom {
    margin-top: 10px;
    .data-list {
      display: flex;
      width: 80%;
      flex-wrap: wrap;
      & > div {
        width: 20%;
        margin: 10px 0;
        line-height: 30px;
      }
    }
  }
}
</style>
