<template>
  <div v-loading="loading">
    <!-- 列表展示 -->
    <table-exhibition @query="query" :table="table" :options="tableOptions" ref="table">
      <!-- 新老拓展顾问 -->
      <template v-for="i of ['oldCustomerServiceVO', 'newCustomerServiceVO']" :slot="`column_${i}`" slot-scope="option">
        <el-table-column v-bind="option" :key="i">
          <template slot-scope="{ row = {} }">
            <template v-if="row[option.prop]">
              <span> {{ row[option.prop].name }}{{ row[option.prop].groupName ? `-${row[option.prop].groupName}` : '' }} </span>
              <span v-if="row[option.prop].organizationName"> -{{ row[option.prop].organizationName }} </span>
            </template>
            <span v-else>--</span>
          </template>
        </el-table-column>
      </template>
    </table-exhibition>
  </div>
</template>

<script>
import TableExhibition from '@/components/Table/TableExhibition';
import { listChangeRecord } from '@/api/customer-management/info';

export default {
  name: 'DevelopServiceList', // 拓展顾问变动记录列表
  components: { TableExhibition },
  data() {
    return {
      table: {},
      loading: false,
      type: 'DEVELOPMENT'
    };
  },
  mounted() {
    this.params = {
      data: {},
      ...this.$refs.table.getParams()
    };
  },
  activated() {
    this.query();
  },
  props: {
    id: String
  },
  computed: {
    tableOptions() {
      return [
        {
          prop: 'createDate',
          label: '变动时间',
          width: '180',
          formatter: (r, c, v = []) => this.$options.filters.parseTime(v) || '--'
        },
        {
          prop: 'oldCustomerServiceVO',
          label: '历史拓展顾问',
          width: '200',
          isCustom: true
        },
        {
          prop: 'newCustomerServiceVO',
          label: '新拓展顾问',
          width: '200',
          isCustom: true
        },
        {
          prop: 'changeTypeName',
          label: '变动原因',
          showOverflowTooltip: true
        },
        {
          prop: 'createByName',
          width: '80',
          label: '操作人'
        }
      ];
    }
  },
  methods: {
    query(params = {}) {
      Object.assign(this.params, params);

      this.loading = true;
      if (this.id) this.params.data.distributorId = this.id;
      this.params.data.customerServiceType = this.type;
      listChangeRecord(this.params)
        .then((res) => {
          this.table = res.data;
        })
        .finally(() => {
          this.loading = false;
        });
    }
  }
};
</script>

<style>
</style>
