<template>
  <el-form ref="form" :model="form" label-width="120px" :rules="rules">
    <el-form-item label="平台名称" prop="channel">
      <el-select
        v-model="form.channel"
        placeholder="请选择对应渠道"
        style="width: 80%"
        @change="channelChange"
        key="2"
      >
        <el-option
          v-for="item in channelOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        ></el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="违规第三方店铺名称" prop="websiteWhitelistId">
      <el-select
        key="2"
        v-model="form.websiteWhitelistId"
        placeholder="请选择违规第三方店铺名称"
        style="width: 80%"
        @change="websiteWhitelistChange"
      >
        <el-option
          v-for="item in platformShopNameOptions"
          :key="item.id"
          :label="item.platformShopName"
          :value="item.id"
        ></el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="违规时间" prop="violateDate">
      <el-date-picker
        v-model="form.violateDate"
        type="date"
        placeholder="选择违规时间"
        style="width: 80%"
        value-format="timestamp"
      ></el-date-picker>
    </el-form-item>
    <el-form-item label="违规商品" prop="commodityId">
      <el-input
        type="text"
        v-model="form.commodityId"
        placeholder="请输入违规商品ID，点击查询商品查看商品信息"
        style="width: 80%"
        clearable
      ></el-input>
      <el-button
        type="primary"
        style="margin-left: 20px"
        @click="getGoodsListById"
        >查询商品</el-button
      >
    </el-form-item>
    <el-form-item v-if="goods && form.commodityId">
      <div class="goods-item">
        <img :src="goods.thumbnailUrl" alt class="avatar" height="60" />
        <div class="goods-detail">
          <div>商品名称：{{ goods.name }}</div>
          <div>最低售价：￥{{ goods.minControlPrice }}</div>
        </div>
      </div>
    </el-form-item>
    <el-form-item label="控价价格" prop="guidePrice">
      <el-input
        type="text"
        v-model.number="form.guidePrice"
        placeholder="请输入控价价格"
        style="width: 80%"
      >
        <template slot="prepend">￥</template>
      </el-input>
    </el-form-item>
    <el-form-item label="破价价格" prop="salePrice">
      <el-input
        type="text"
        v-model.number="form.salePrice"
        placeholder="请输入破价价格"
        style="width: 80%"
      >
        <template slot="prepend">￥</template>
      </el-input>
    </el-form-item>
    <el-form-item label="处罚方式" prop="checkList">
      <el-checkbox-group v-model="form.checkList">
        <el-checkbox label="isWarning">警告</el-checkbox>
        <el-checkbox label="isFine">罚款</el-checkbox>
        <el-checkbox label="isFreeze">冻结账号</el-checkbox>
      </el-checkbox-group>
    </el-form-item>
    <el-form-item label="违规次数" prop="violateNumber">
      <el-input-number
        v-model="form.violateNumber"
        controls-position="right"
        :min="1"
        :step="1"
        placeholder="请输入违规次数"
        :precision="0"
        style="width: 200px"
      ></el-input-number>
    </el-form-item>
    <el-form-item style="text-align: right">
      <el-button type="primary" @click="onSubmit('form')">确定</el-button>
      <el-button @click="onCancel">取消</el-button>
    </el-form-item>
  </el-form>
</template>
<script>
import {
  createViolateRecord,
  getDistributorWebsiteWhitelist
} from '@/api/customer-management/follow-up';
import { validateMoney2 } from '@/utils/validate';
import { list } from '@/api/commodity/list';
export default {
  props: {
    id: String // 分销商ID
  },
  data() {
    const validateMoney = (rule, value, callback) => {
      if (!value) {
        return callback(new Error('金额不能为空'));
      }
      setTimeout(() => {
        if (!validateMoney2(value)) {
          callback(new Error('请输入金额'));
        } else {
          callback();
        }
      }, 0);
    };
    return {
      rules: {
        channel: [
          { required: true, message: '请选择平台名称', trigger: 'change' }
        ],
        websiteWhitelistId: [
          { required: true, message: '请选择违规店铺名称', trigger: 'change' }
        ],
        violateDate: [
          { required: true, message: '请选择违规时间', trigger: 'change' }
        ],
        commodityId: [
          { required: true, message: '请输入违规商品', trigger: 'blur' }
        ],
        guidePrice: [
          { validator: validateMoney, trigger: 'blur', required: true }
        ],
        salePrice: [
          { validator: validateMoney, trigger: 'blur', required: true }
        ],
        violateNumber: [
          { required: true, message: '请输入违规次数', trigger: 'blur' }
        ],
        checkList: [
          { required: true, message: '请输入处罚方式', trigger: 'change' }
        ]
      },
      form: {
        channel: '',
        isWarning: '0',
        isFine: '0',
        isFreeze: '0',
        commodityId: '',
        commodityName: '',
        violateNumber: 1,
        websiteWhitelistId: '',
        violateDate: '',
        guidePrice: '',
        salePrice: '',
        imageUrl: '',
        platformShopName: '',
        checkList: []
      },
      // 授权渠道列表
      channelOptions: [],
      platformShopNameOptions: [],
      goods: null
    };
  },
  methods: {
    onSubmit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.createViolateRecord();
        } else {
          return false;
        }
      });
    },

    createViolateRecord() {
      this.form.checkList.forEach((key) => {
        this.form[key] = '1';
      });
      if (!this.form.imageUrl) {
        this.$message({
          type: 'error',
          message: '请选择违规商品'
        });
        return;
      }
      const data = Object.assign({}, this.form, { distributorId: this.id });
      delete data.checkList;
      createViolateRecord(data).then((res) => {
        this.$message({
          type: 'success',
          message: '新增成功'
        });
        this.$emit('onSuccess');
        this.$emit('closeDialog');
      });
    },
    channelChange() {
      this.getDistributorWebsiteWhitelist();
    },
    // 通过商品id查
    getGoodsListById() {
      if (!this.form.commodityId) return false;
      list({
        data: {
          id: this.form.commodityId
        }
      }).then((res) => {
        if (res.data.list.length) {
          this.goods = res.data.list.length ? res.data.list[0] : null;
          const { id, name, minControlPrice, thumbnailUrl } = this.goods;
          this.form.commodityId = id;
          this.form.commodityName = name;
          this.form.guidePrice = minControlPrice;
          this.form.imageUrl = thumbnailUrl;
        } else {
          this.$message({
            type: 'error',
            message: '未查询到对应的商品'
          });
        }
      });
    },
    onCancel() {
      this.$emit('onCancel');
      this.$emit('closeDialog');
    },
    addShop() {
      this.form.shopList.push({
        name: '',
        level: ''
      });
    },
    deleteShop(index) {
      this.form.shopList.splice(index, 1);
    },
    // 获取店铺白名单列表
    getDistributorWebsiteWhitelist() {
      this.form.websiteWhitelistId = '';
      this.platformShopNameOptions = [];
      getDistributorWebsiteWhitelist({
        distributorId: this.id,
        platform: this.form.channel,
        status: 'PASS'
      }).then((res) => {
        this.platformShopNameOptions = res.data;
      });
    },
    websiteWhitelistChange(val) {
      this.form.platformShopName = this.platformShopNameOptions.filter(
        (item) => item.id === val
      )[0].platformShopName;
    },
    // 平台名称
    getPlatformList() {
      getDistributorWebsiteWhitelist({
        distributorId: this.id,
        status: 'PASS'
      }).then((res) => {
        const data = res.data.reduce(
          (all, next) =>
            all.some((item) => item['platform'] === next['platform'])
              ? all
              : [...all, next],
          []
        );
        this.channelOptions = data.map((item) => ({
          label: item.platformName,
          value: item.platform
        }));
      });
    }
  },
  created() {
    this.getPlatformList();
  }
};
</script>
<style scoped lang="scss">
.list {
  .item {
    display: flex;
    margin-bottom: 10px;
    .label-name {
      width: 120px;
    }
  }
}
.second-title {
  color: #999;
}
.form-group {
  display: flex;
}
.goods-item {
  display: flex;
  .goods-detail {
    line-height: 30px;
    padding-left: 20px;
  }
}
</style>
