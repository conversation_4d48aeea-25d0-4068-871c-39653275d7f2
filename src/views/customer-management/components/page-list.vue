<template>
  <!-- 跟进管理代发列表 -->
  <div class="list-content">
    <!-- 数据总览 -->
    <div class="data-overview">
      <div class="title">
        <h3>{{ type === '' ? '全部' : type === 'PURCHASE' ? '采销' : type === 'DROP_SHIPPING' ? '一件代发' : '' }}分销商数据</h3>
        <!-- <h3>
          {{
            type === 'PURCHASE'
              ? '采销分销商数据'
              : type === 'DROP_SHIPPING'
              ? '一件代发分销商数据'
              : ''
          }}
        </h3> -->
        <div class="update-time">更新时间:{{ new Date() | parseTime('{y}-{m}-{d} {h}:{i}') }}</div>
      </div>
      <div class="distributor-data">
        <GeneralSituation :list="list"></GeneralSituation>
      </div>
    </div>
    <!-- 搜索区域 -->

    <div class="search">
      <FormSearch :searchType="'servicesUp'" :accountType="type" :exportShow="exportShow" @parameterDataSearch="parameterDataSearch" @reset="reset" @parameterDataExport="parameterDataExport" />
    </div>
    <!-- 表格数据列表 -->
    <el-table :data="tableData" style="width: 100%">
      <el-table-column align="center" label="分销商ID" prop="id"></el-table-column>
      <el-table-column label="店铺名称/账号" width="180" align="center">
        <template slot-scope="scope">
          <div>{{ scope.row.shopName }}</div>
          <div>{{ scope.row.mobile }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="levelName" label="店铺等级" width="100" align="center">
        <template slot-scope="scope">
          <div>{{ formatNull(scope.row.levelName) }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="customerServiceName" label="专属顾问" align="center">
        <template slot-scope="scope">
          <div>{{ formatNull(scope.row.customerServiceName) }}</div>
        </template>
      </el-table-column>
      <el-table-column label="最新拥有专属顾问时间" align="center" width="200">
        <template slot-scope="scope">
          <span v-if="scope.row.updateCustomerServiceDate">{{ scope.row.updateCustomerServiceDate | parseTime }}</span>
          <span v-else>/</span>
        </template>
      </el-table-column>
      <el-table-column label="入驻时间" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.auditDate | parseTime }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="lastPurchaseDate" label="最近采购时间" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.lastPurchaseDate">{{ scope.row.lastPurchaseDate | parseTime }}</span>
          <span v-else>/</span>
        </template>
      </el-table-column>
      <el-table-column prop="lastOrderAmount" label="最近采购总金额" align="center"></el-table-column>
      <el-table-column prop="totalTodoPlanCount" label="跟进总次数" align="center"></el-table-column>
      <el-table-column prop="unPurchaseDays" label="连续未动销天数" align="center"></el-table-column>
      <el-table-column prop="unTodoDays" label="连续未跟进天数" align="center"></el-table-column>
      <el-table-column prop="unFinishTodoPlanCount" align="center" width="136px">
        <template slot="header" slot-scope="scope">
          <div style="padding-right: 0">
            超时跟进次数
            <el-tooltip class="item" effect="dark" placement="top">
              <div slot="content" class="header-tooltip-sort">已设置的待办事项，当天没有完成之后跟进完成的次数</div>
              <i class="el-icon-question"></i>
            </el-tooltip>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="nextTodoPlanDate" label="下次跟进时间" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.nextTodoPlanDate">{{ scope.row.nextTodoPlanDate | parseTime('{y}-{m}-{d}') }}</span>
          <span v-else>/</span>
        </template>
      </el-table-column>
      <el-table-column prop="address" label="操作" align="center">
        <template slot-scope="scope">
          <Authority auth="/customer-management/follow-up/:edit">
            <!-- <followUpDialog
              btnTitle="快速跟进"
              btnType="text"
              :id="scope.row.id"
            /> -->
            <el-button
              type="text"
              @click="
                $refs.dialog_followUp.setVisible(true, {
                  ...scope.row,
                  CLUEFOLLOWUPTYPE: 'distributor'
                })
              "
              >快速跟进</el-button
            >
          </Authority>

          <div>
            <router-link :to="`/operations-follow-up/recode/${scope.row.id}`">
              <el-button type="text">跟进记录</el-button>
            </router-link>
          </div>
          <div>
            <router-link :to="`/distributor-management/distributor/detail/${scope.row.id}`">
              <el-button type="text">查看信息</el-button>
            </router-link>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <div class="pagination">
      <el-pagination :current-page="form.pageNo" :page-size="form.pageSize" :page-sizes="[10, 20, 30, 40, 50, 100]" :total="total" @current-change="handleCurrentChange" @size-change="handleSizeChange" background layout="total, sizes, prev, pager, next, jumper"></el-pagination>
    </div>
    <Dialog :callback="onSubmitFollowUp" class="dialog" type="CLUE_FOLLOWUP" ref="dialog_followUp"> </Dialog>
  </div>
</template>
<script>
import GeneralSituation from '@/components/views/customer-management/GeneralSituation';
import FormSearch from '@/components/views/customer-management/FormSearch';
import download from '@/utils/download';
import { parseTime } from '@/utils';
import { getStatisticList, getDistributorList, exportDistributorPage } from '@/api/customer-management/follow-up';
import Dialog from '@/components/Dialog/index.vue';
import { createFinishedTodo } from '@/api/customer-management/info';
import { mapGetters } from 'vuex';
export default {
  props: {
    type: {
      type: String,
      default() {
        return '';
      }
    }
  },
  name: 'client-list',
  components: {
    GeneralSituation,
    FormSearch,
    Dialog
  },
  computed: {
    ...mapGetters('shop', ['btnSet'])
  },
  data() {
    return {
      list: [],
      // 列表客户类型 'PURCHASE':采销客户 'DROP_SHIPPING':一件代发客户
      tableData: [],
      total: 0,
      form: {
        data: {
          purchaseType: this.type
        },
        pageNo: 1,
        pageSize: 10
      },
      exportShow: false
    };
  },
  methods: {
    init() {
      this.getStatisticList();
      this.getDistributorList();
    },
    handleCurrentChange(pageNo) {
      this.form.pageNo = pageNo;
      this.getDistributorList();
    },
    handleSizeChange(pageSize) {
      this.form.pageSize = pageSize;
      this.getDistributorList();
    },
    // 获取统计数据
    getStatisticList() {
      getStatisticList({ type: this.type }).then((res) => {
        const { averageOrderAmount, distributorNum, todayDoneNum, todayTodoNum, unPurchaseNum, yesterdayDoneNum, yesterdayTodoNum } = res.data;
        this.list = [
          {
            title: '分销商总数', // 标题
            amount: distributorNum // 今日金额
          },
          {
            title: '今日超时未跟进分销商数',
            amount: todayTodoNum,
            yesterdayAmount: yesterdayTodoNum,
            isArrowShow: true,
            tips: '已设置的待办事项，超时了一直没有跟进的，一个分销商最多计算一次'
          },
          {
            title: '今日已跟进分销商数',
            amount: todayDoneNum,
            yesterdayAmount: yesterdayDoneNum,
            isArrowShow: true,
            tips: '今日在跟进管理里面设置跟进记录的分销商的数量'
          },
          {
            title: '近30天未动销分销商数',
            amount: unPurchaseNum,
            tips: '近30天没有下单的分销商数量(小样订单不算动销)'
          },
          {
            title: '分销商平均客单价',
            amount: averageOrderAmount,
            tips: '每个分销商的客单价的总和/分销商的总数量'
          }
        ];
      });
    },
    // 空值用斜杠替换
    formatNull(val) {
      return val || '/';
    },
    // 获取统计列表数据
    getDistributorList() {
      getDistributorList(this.form).then((res) => {
        this.tableData = res.data.list;
        this.total = res.data.total;
      });
    },
    parameterDataSearch(data) {
      this.form.data = Object.assign({}, data, { purchaseType: this.type });
      this.getDistributorList();
    },
    parameterDataExport(data) {
      const parameter = data;
      parameter.purchaseType = this.type;
      exportDistributorPage(parameter).then((res) => {
        download(res, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', `运维跟进${this.type === '' ? '全部' : this.type === 'PURCHASE' ? '采销' : this.type === 'DROP_SHIPPING' ? '一件代发' : ''}分销商列表-${parseTime(Date.now(), '{y}-{m}-{d}-{h}:{i}:{s}')}.xlsx`);
      });
    },
    reset() {
      this.form = {
        data: { purchaseType: this.type },
        pageNo: 1,
        pageSize: 10
      };
      this.getDistributorList();
    },
    onSubmitFollowUp(followUpData, dialog) {
      const data = {
        ...followUpData,
        distributorId: dialog.externalData.id
      };
      createFinishedTodo(data).then((res) => {
        console.dir(res);
      });
    }
  },
  activated() {
    // this.getStatisticList();
    // this.getDistributorList();
    this.exportShow = this.btnSet.has('/customer-management/follow-up/:export');
  }
};
</script>

<style lang="scss" scoped>
.data-overview {
  .title {
    display: flex;
    align-items: center;
    .update-time {
      margin-left: 15px;
      color: #999;
    }
  }
  .distributor-data {
    border: 1px solid #ccc;
    margin-bottom: 20px;
  }
}
.search-session {
  padding: 20px 0;
}
</style>
