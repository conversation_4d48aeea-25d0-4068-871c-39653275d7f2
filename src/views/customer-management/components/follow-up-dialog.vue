<!-- 跟进记录弹出框 -->
<template>
  <div>
    <el-button :type="btnType" @click="dialogVisible = true">{{
      btnTitle
    }}</el-button>

    <el-dialog
      :title="type === 'FINISH' ? '跟进记录' : 'WAIT'"
      :visible.sync="dialogVisible"
      width="800px"
      :before-close="() => (dialogVisible = false)"
    >
      <div class="form-box">
        <el-form ref="form" :model="form" :rules="rules" label-width="140px">
          <div class="form-box-title">
            {{ type === 'FINISH' ? '本次跟进记录' : '下次跟进设置' }}
          </div>
          <el-form-item
            :label="type === 'FINISH' ? '跟进完成时间' : '跟进时间'"
            prop="expectedFinishDate"
          >
            <el-date-picker
              :disabled="isDetail"
              type="date"
              v-model="form.expectedFinishDate"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="跟进事项" prop="todoEvent">
            <el-input
              v-model="form.todoEvent"
              type="textarea"
              :autosize="{ minRows: 4, maxRows: 8 }"
            ></el-input>
          </el-form-item>
          <template v-if="type === 'FINISH'">
            <div class="form-box-title">下次跟进设置</div>
            <!-- 下次跟进时间 -->
            <el-form-item label="跟进时间">
              <el-date-picker
                :disabled="isDetail"
                type="date"
                v-model="form.nextExpectedFinishDate"
              ></el-date-picker>
            </el-form-item>
            <!-- 下次跟进事项 -->
            <el-form-item label="跟进事项">
              <el-input
                v-model="form.nextTodoEvent"
                type="textarea"
                :autosize="{ minRows: 4, maxRows: 8 }"
              ></el-input>
            </el-form-item>
          </template>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="onSubmit" :loading="btnLoading">{{
          isFinish ? '确 定' : '保 存'
        }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
// 跟进记录、待办事项分页列表
import {
  createFinishedTodo,
  todoPlanFinish
} from '@/api/customer-management/info';
export default {
  data() {
    return {
      title: '',
      dialogVisible: false,
      form: {
        expectedFinishDate: new Date().getTime(), // 跟进时间 默认当前时间
        todoEvent: '', // 跟进事项
        nextExpectedFinishDate: '', // 下次跟进时间
        nextTodoEvent: '' // 下次跟进事项
      },
      rules: {
        expectedFinishDate: [
          { required: true, message: '请选择跟进完成时间', trigger: 'change' }
        ],
        todoEvent: [
          { required: true, message: '请输入跟进事项', trigger: 'blur' }
        ]
      },
      btnLoading: false,
      isFinish: false // 是否是跟进完成确认
    };
  },
  props: {
    id: {
      type: String,
      default: '' // 分销商ID
    },
    type: {
      type: String,
      default: 'FINISH' // FINISH 跟进记录  WAIT   待办
    },
    btnTitle: {
      type: String,
      default: '快速跟进'
    },
    btnType: {
      type: String,
      default: 'primary'
    },
    isDetail: {
      type: Boolean,
      default: false
    }
  },
  components: {},

  computed: {
    formData() {
      const obj = { ...this.form, distributorId: this.id };
      if (this.type === 'WAIT') {
        delete obj.nextExpectedFinishDate;
        delete obj.nextTodoEvent;
      } else {
        if (obj.nextExpectedFinishDate instanceof Date) {
          obj.nextExpectedFinishDate =
            this.form.nextExpectedFinishDate.getTime();
        }
      }
      if (obj.expectedFinishDate instanceof Date) {
        obj.expectedFinishDate = this.form.expectedFinishDate.getTime();
      }
      return obj;
    }
  },

  activated() {},


  methods: {
    onSubmit() {
      this.btnLoading = true;
      this.$refs['form'].validate((valid) => {
        if (valid) {
          if (this.type === 'FINISH') {
            // 新增跟进记录
            this.addFINISH();
          } else {
            if (this.isFinish) {
              // 跟进完成
              this.finish();
            } else {
              // 新增/编辑待办
              this.updateWAIT();
            }
          }
        } else {
          this.btnLoading = false;
        }
      });
    },
    // 新增跟进记录
    addFINISH() {
      const { nextExpectedFinishDate, nextTodoEvent } = this.formData;
      if (
        (nextExpectedFinishDate && nextTodoEvent.trim() === '') ||
        (!nextExpectedFinishDate && nextTodoEvent.trim() !== '')
      ) {
        this.$message.warning(
          '下次跟进设置-跟进时间与跟进事项必须同时填写或者同时不填'
        );
        this.btnLoading = false;
        return;
      }
      createFinishedTodo(this.formData)
        .then((res) => {
          this.$message.success('操作成功');
          this.onSuccess();
          this.dialogVisible = false;
        })
        .finally(() => {
          this.btnLoading = false;
        });
    },
    // 跟进完成
    finish() {
      const formData = this.formData;
      if (this.waitId) {
        formData.id = this.waitId;
        formData.finishDate = formData.expectedFinishDate;
      }
      todoPlanFinish(formData)
        .then((res) => {
          this.$message.success('操作成功');
          this.onSuccess();
          this.dialogVisible = false;
        })
        .finally(() => {
          this.btnLoading = false;
        });
    },
    onSuccess() {
      this.$emit('onSuccess');
    }
  }
};
</script>
<style lang='scss' scoped>
.form-box {
  text-align: left;
  .form-box-title {
    color: #333;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 10px;
  }
}
</style>