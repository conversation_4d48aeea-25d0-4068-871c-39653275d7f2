<template>
  <!-- 基础信息 -->
  <div class="public-info" :class="isHideBody ? 'public-info--mag' : ''">
    <div class="base-info-top">
      <div class="base-info">
        <img
          src="https://oss.syounggroup.com/static/file/soyoung-zg/aliyun/soyoung-zg/mp/dptx.png"
          alt
          class="avatar"
          width="60"
          height="60"
          fit="cover"
        />
        <div class="base-info-middle">
          <div class="name">{{ distributorHeaderVO.shopName || '' }}</div>
          <div class="account">
            注册账号：
            <CryptoBlock
              detail-auth="/distributor-management/:decrypt-mobile"
              :bizId="distributorHeaderVO.id"
              bizType="DISTRIBUTOR_MOBILE"
              v-model.trim="distributorHeaderVO.mobile"
            />
          </div>
        </div>
        <div class="base-info-right">
          <span class="status normal">{{
            distributorHeaderVO.isEnable === '0' ? '已冻结' : '正常'
          }}</span>
          <div class="adviser">
            专属顾问：{{ distributorHeaderVO.customerServiceName | filterVO }}
            <span v-if="distributorHeaderVO.flowStatus">{{
              distributorHeaderVO.flowStatus === 'PUBLIC' ? '(回收)' : ''
            }}</span>
          </div>
        </div>
        <div class="base-info-right">
          <div class="adviser">
            拓展顾问：{{
              distributorHeaderVO.developCustomerServiceName | filterVO
            }}
          </div>
        </div>
      </div>
      <div class="handler-btns" v-if="!isDetails">
        <Authority :auth="authorityCode">
          <el-button type="primary" @click="editCustomerInfo"
            >完善客户资料</el-button
          >
        </Authority>
        <Authority :auth="authorityCode">
          <el-button type="primary" @click="addRecord">新增违规记录</el-button>
        </Authority>
      </div>
    </div>
    <div class="base-info-bottom" v-if="!isHideBody">
      <div class="data-list">
        <div>
          <div class="label">业务类型</div>
          <div class="value">
            {{ distributorHeaderVO.typeName | filterVO }}
          </div>
        </div>
        <div>
          <div class="label">采货类型</div>
          <div class="value">
            {{ distributorHeaderVO.purchaseTypeName | filterVO }}
            <Authority :auth="authorityCode" v-if="!isDetails">
              <el-button
                class="ic-btn"
                type="text"
                icon="el-icon-edit-outline"
                @click="openDialogUpdate('purchaseType')"
              ></el-button>
            </Authority>
          </div>
        </div>
        <div>
          <div class="label">店铺等级</div>
          <div class="value">
            {{ distributorHeaderVO.levelName | filterVO }}
            <Authority :auth="authorityCode" v-if="!isDetails">
              <el-button
                class="ic-btn"
                type="text"
                icon="el-icon-edit-outline"
                @click="openDialogUpdate('level')"
              ></el-button>
            </Authority>
          </div>
        </div>
        <div>
          <div class="label">合同签订</div>
          <div class="value">
            已签订{{ distributorHeaderVO.contractNum || 0 }}份，生效中{{
              distributorHeaderVO.validContractNum || 0
            }}份
            <LinkBtn
              :to="`/distributor-management/statistic?mobile=${distributorBodyVO.mobile}`"
            />
          </div>
        </div>
        <div>
          <div class="label">品牌授权</div>
          <div class="value">
            授权品牌{{ distributorHeaderVO.licenseBrandNum || 0 }}个，授权书{{
              distributorHeaderVO.licenseNum || 0
            }}份
            <LinkBtn
              :to="`/brand/authority/certificate/list?mobile=${distributorBodyVO.mobile}`"
            />
          </div>
        </div>
        <div>
          <div class="label">审核通过时间</div>
          <div class="value">
            {{
              distributorHeaderVO.auditDate
                | parseTime('{y}-{m}-{d} {h}:{i}:{s}')
                | filterVO
            }}
          </div>
        </div>
        <div>
          <div class="label">最近采购时间</div>
          <div class="value">
            {{
              distributorHeaderVO.lastPurchaseDate
                | parseTime('{y}-{m}-{d} {h}:{i}:{s}')
                | filterVO
            }}
          </div>
        </div>
        <div>
          <div class="label">未动销天数</div>
          <div class="value">
            {{ distributorHeaderVO.unPurchaseDays | filterVO
            }}<span v-if="distributorHeaderVO.unPurchaseDays">天</span>
          </div>
        </div>
      </div>
    </div>
    <!-- 弹窗 -->
    <el-dialog
      :title="title"
      :visible.sync="dialogVisible"
      width="800px"
      v-if="dialogVisible"
    >
      <customer-info
        :authorityCode="authorityCode"
        v-if="btnType === 1"
        @onSuccess="() => $emit('onSuccess')"
        :id="id"
        @onCancel="onCancel"
        @closeDialog="dialogVisible = false"
      />
      <violationr-ecord
        v-else
        @onSuccess="$emit('onSuccess')"
        @onCancel="onCancel"
        :id="id"
        @closeDialog="dialogVisible = false"
      />
    </el-dialog>
    <!-- DialogUpdate-->
    <DialogUpdate
      :dialogVisible.sync="dialogUpdateVisible"
      :dialogUpdateType="dialogUpdateType"
      @onSuccess="$emit('onSuccess')"
      :myPurchaseType="distributorHeaderVO.purchaseType"
      :id="id"
    />
  </div>
</template>
<script>
// import GeneralSituation from '@/components/views/customer-management/GeneralSituation';
import CustomerInfo from './customer-Info';
import ViolationrEcord from './violationr-ecord';
import DialogUpdate from './DialogUpdate';
import LinkBtn from '@/components/LinkBtn';
export default {
  components: {
    //  GeneralSituation,
    CustomerInfo,
    ViolationrEcord,
    DialogUpdate,
    LinkBtn
  },
  name: 'public-info',
  data() {
    return {
      dialogVisible: false,
      title: '',
      // 弹窗类型
      btnType: 1,
      dialogUpdateType: '',
      dialogUpdateTitle: '',
      dialogUpdateVisible: false
    };
  },

  props: {
    authorityCode: String, // 按钮权限编码
    id: String,
    distributorBodyVO: Object, // 分销商合作信息
    distributorHeaderVO: Object, // 分销商头部信息
    isHideBody: {
      type: Boolean,
      default: false
    },
    isDetails: Boolean // 是否是查看
  },
  filters: {
    filterVO(v) {
      if (v) return v;
      return '暂无';
    }
  },
  computed: {},
  activated() {},
  methods: {
    editCustomerInfo() {
      this.title = '完善客户资料';
      this.btnType = 1;
      this.dialogVisible = true;
    },
    onSuccess() {},
    onCancel() {},
    addRecord() {
      this.title = '新增违规记录';
      this.btnType = 2;
      this.dialogVisible = true;
    },
    openDialogUpdate(type) {
      this.dialogUpdateType = type;
      this.dialogUpdateVisible = true;
    }
  }
};
</script>

<style lang="scss" scoped>
.public-info {
  &--mag {
    margin-bottom: 25px;
  }
  .base-info-top {
    display: flex;
    justify-content: space-between;
    .base-info {
      display: flex;
      align-items: center;
      .avatar {
        border: 1px solid #ddd;
      }
      .base-info-middle {
        font-size: 16px;
        margin-left: 20px;
        .account {
          font-size: 14px;
          margin-top: 20px;
          color: #666;
        }
      }
      .base-info-right {
        font-size: 14px;
        margin-left: 20px;
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        height: 60px;
        justify-content: flex-end;
        .status {
          font-size: 12px;
          color: #fff;
          padding: 5px 8px;
          &.normal {
            background: #67c23a;
          }
          &.frozen {
            background: #f56c6c;
          }
          &.gold {
            background: #daa520;
          }
        }
        .adviser {
          margin-top: 10px;
          padding: 5px 10px;
          background: #ddd;
        }
      }
    }
  }
  .base-info-bottom {
    margin-top: 10px;
    .data-list {
      display: flex;
      width: 80%;
      flex-wrap: wrap;
      & > div {
        width: 20%;
        margin: 10px 0;
        line-height: 30px;
      }
    }
  }
}
</style>
