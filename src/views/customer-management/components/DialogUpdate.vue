<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogVisible"
    :before-close="onClose"
    width="600px"
  >
    <!-- 修改采货类型 -->
    <div v-if="dialogUpdateType === 'purchaseType'">
      <el-form
        ref="purchaseTypeForm"
        :model="purchaseTypeForm"
        label-width="150px"
      >
        <el-form-item label="采货类型" prop="purchaseType">
          <el-radio-group
            v-model="purchaseTypeForm.purchaseType"
            @change="purchaseTypeChange"
          >
            <el-radio :key="item.id" :label="item.id" v-for="item in options">{{
              item.name
            }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="店铺销量:">
          <el-select clearable v-model="purchaseTypeForm.monthlySales">
            <el-option
              :key="idx"
              :label="item.label"
              :value="item.value"
              v-for="(item, idx) in monthlySalesList"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <!-- 修改店铺等级 -->
    <div v-if="dialogUpdateType === 'level'">
      <el-form ref="levelForm" :model="levelForm" label-width="150px">
        <el-form-item label="上月店铺回款金额:">
          <div>
            {{
              levelForm.quarterTotalAmount && levelForm.quarterTotalAmount >= 0
                ? levelForm.quarterTotalAmount
                : '暂无'
            }}
          </div>
        </el-form-item>
        <el-form-item label="当前店铺等级为:">
          {{ levelForm.levelName ? levelForm.levelName : '暂无' }}
        </el-form-item>
        <el-form-item label="修改后店铺等级:">
          <el-select clearable v-model="levelForm.levelId">
            <el-option
              :key="idx"
              :label="item.label"
              :value="item.value"
              v-for="(item, idx) in levelOptions"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="修改备注:">
          <el-input
            :rows="3"
            placeholder="请输入内容"
            type="textarea"
            v-model="levelForm.remarks"
          />
        </el-form-item>
      </el-form>
    </div>
    <!-- 修改意向品牌 -->
    <div v-if="dialogUpdateType === 'brand'">
      <el-form ref="brandForm" :model="brandForm" label-width="150px">
        <el-form-item label="意向品牌">
          <el-select
            v-model="brandForm.interestedBrandList"
            multiple
            placeholder="请选择(可多选)"
            style="width: 80%"
            value-key="typeValue"
          >
            <el-option
              v-for="item in brandListOptions"
              :key="item.typeValue"
              :label="item.typeValueName"
              :value="item"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <!-- 修改客户意向程度 -->
    <div v-if="dialogUpdateType === 'interestedDegree'">
      <el-form
        ref="interestedDegreeForm"
        :model="interestedDegreeForm"
        label-width="150px"
      >
        <el-form-item label="意向程度">
          <el-select
            v-model="interestedDegreeForm.interestedDegreeValue"
            placeholder="请选择"
            style="width: 80%"
          >
            <el-option
              v-for="item in interestedDegreeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="$emit('update:dialogVisible', false)">取 消</el-button>
      <el-button type="primary" @click="onSave" :loading="btnLoading"
        >确 定</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
import {
  getBrief,
  updateLevel,
  improveInterestedBrand,
  getBasicInfo,
  updatePurchaseType,
  updateExtInfoDistributorLabel
} from '@/api/customer-management/info';
import { fechDictData } from '@/api/dict';
import { listAllLevel } from '@/api/customer-management/setting';
import { listBrand } from '@/api/shop-decoration/website-brand';
export default {
  data() {
    return {
      options: [
        { id: 'PURCHASE', name: '采销' },
        { id: 'DROP_SHIPPING', name: '一件代发' }
      ],
      purchaseTypeForm: {
        purchaseType: '',
        monthlySales: ''
      },
      levelForm: {
        levelId: '',
        levelName: '',
        quarterTotalAmount: '',
        remarks: ''
      },
      btnLoading: false,
      brandForm: {
        interestedBrandList: []
      },
      interestedDegreeForm: {
        interestedDegreeValue: ''
      },
      interestedDegreeOptions: [],
      brandListOptions: [],
      levelOptions: [],
      monthlySalesList: []
    };
  },
  props: {
    id: String,
    myPurchaseType: String,
    dialogVisible: {
      type: Boolean,
      default: false
    },
    dialogUpdateType: String,
    brandInitData: {
      // 修改意向品牌初始值
      type: Array,
      default: () => []
    },
    interestedDegreeValue: {
      // 修改意向初始值
      type: String,
      default: ''
    }
  },
  created() {
    if (this.dialogUpdateType === 'brand') {
      this.getListBrand();
    } else {
      if (this.dialogUpdateType === 'interestedDegree') return;
      this.fetchListAllLevel();
    }
  },
  computed: {
    title() {
      let title = '';
      const dialogUpdateType = this.dialogUpdateType;
      if (dialogUpdateType === 'purchaseType') {
        title = '修改采货类型';
      }
      if (dialogUpdateType === 'level') {
        title = '修改店铺等级';
      }
      if (dialogUpdateType === 'brand') {
        title = '修改意向品牌';
      }
      if (dialogUpdateType === 'interestedDegree') {
        title = '修改客户意向程度';
      }
      return title;
    }
  },
  watch: {
    dialogVisible(v) {
      if (!v) return;
      if (this.dialogUpdateType === 'brand') {
        this.brandForm.interestedBrandList = this.brandInitData;
      }
      if (this.dialogUpdateType === 'purchaseType') {
        // 初始化
        this.getBasicInfo();
      }
      if (this.dialogUpdateType === 'interestedDegree') {
        this.fetchInterested_degree();
      }
    }
  },
  methods: {
    // 获取品牌列表
    formatMapList(data) {
      return data.map((item) => ({
        typeValueName: item.name,
        typeValue: item.id
      }));
    },
    async getListBrand() {
      const { data: res } = await listBrand({});
      this.brandListOptions = this.formatMapList(res);
    },
    onSave() {
      if (this.dialogUpdateType === 'level') {
        this.updateLevel();
      }
      if (this.dialogUpdateType === 'brand') {
        this.improveInterestedBrand();
      }
      if (this.dialogUpdateType === 'purchaseType') {
        this.updatePurchaseType();
      }
      if (this.dialogUpdateType === 'interestedDegree') {
        this.updateInterestedDegree();
      }
    },
    onClose() {
      this.$emit('update:dialogVisible', false);
    },
    getBrief() {
      getBrief(this.id).then((res) => {
        this.levelForm = res.data || {};
      });
    },
    // 修改分销商等级
    updateLevel() {
      const { levelId, remarks } = this.levelForm;
      const data = {
        id: this.id,
        levelId,
        remarks
      };
      this.btnLoading = true;
      updateLevel(data)
        .then((res) => {
          this.$message.success('操作成功');
          this.getBrief();
          this.$emit('onSuccess');
          this.onClose();
        })
        .finally(() => {
          this.btnLoading = false;
        });
    },
    // 店铺等级
    fetchListAllLevel() {
      listAllLevel({
        purchaseType: this.myPurchaseType // 采销 赊销
      }).then((res) => {
        this.levelOptions = res.data.map((item) => ({
          value: item.id,
          label: item.name
        }));
        this.getBrief();
      });
    },
    // 修改意向品牌
    improveInterestedBrand() {
      const { interestedBrandList } = this.brandForm;
      const data = {
        id: this.id,
        interestedBrandList
      };
      if (interestedBrandList.length === 0) {
        this.$message.error('请选择意向品牌');
        return;
      }
      this.btnLoading = true;
      improveInterestedBrand(data)
        .then((res) => {
          this.$message.success('操作成功');
          this.$emit('onSuccess');
          this.onClose();
        })
        .finally(() => {
          this.btnLoading = false;
        });
    },
    // 获取分销商信息
    getBasicInfo() {
      getBasicInfo(this.id).then((res) => {
        const { purchaseType = '', monthlySales = '' } = res.data;
        this.purchaseTypeForm.purchaseType = purchaseType;
        this.purchaseTypeForm.monthlySales = monthlySales;
        this.fetchMonthlySales();
      });
    },
    // 店铺销量
    fetchMonthlySales() {
      const type =
        this.purchaseTypeForm.purchaseType === 'PURCHASE'
          ? 'soyoungzg_monthly_sales_amount_type'
          : 'soyoungzg_monthly_sales_quantity_type';
      fechDictData(type).then((res) => {
        this.monthlySalesList = res.data.map((item) => ({
          value: item.value,
          label: item.label
        }));
      });
    },
    // 客户意向字典
    fetchInterested_degree() {
      const type = 'soyoungzg_interested_degree';
      fechDictData(type).then((res) => {
        this.interestedDegreeOptions = res.data.map((item) => ({
          value: item.value,
          label: item.label
        }));
        this.interestedDegreeForm.interestedDegreeValue =
          this.interestedDegreeValue;
      });
    },
    // 修改分销商采货类型、销量
    updatePurchaseType() {
      const { monthlySales, purchaseType } = this.purchaseTypeForm;
      const data = {
        id: this.id,
        monthlySales,
        purchaseType
      };
      this.btnLoading = true;
      updatePurchaseType(data)
        .then((res) => {
          this.$message.success('操作成功');
          this.$emit('onSuccess');
          this.onClose();
        })
        .finally(() => {
          this.btnLoading = false;
        });
    },
    // 意向程度修改
    updateInterestedDegree() {
      const { interestedDegreeValue } = this.interestedDegreeForm;
      const data = {
        id: this.id,
        type: 'soyoungzg_interested_degree',
        typeValue: interestedDegreeValue
      };
      this.btnLoading = true;
      updateExtInfoDistributorLabel(data)
        .then((res) => {
          this.$message.success('操作成功');
          this.$emit('onSuccess');
          this.onClose();
        })
        .finally(() => {
          this.btnLoading = false;
        });
    },
    purchaseTypeChange() {
      this.purchaseTypeForm.monthlySales = '';
      this.fetchMonthlySales();
    }
  }
};
</script>

<style>
</style>
