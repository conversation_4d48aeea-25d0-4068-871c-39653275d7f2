<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogVisible"
    :before-close="onClose"
    width="400px"
  >
    <el-form ref="form" :model="form" label-width="85px" v-loading="loading">
      <el-form-item label="用户名称:" prop="name">
        <el-input
          placeholder="请输入内容"
          type="text"
          v-model="form.name"
          class="form-input"
        />
      </el-form-item>
      <el-form-item label="手机号码:" prop="mobile">
        <!-- <el-input
          placeholder="请输入内容"
          type="text"
          v-model="form.mobile"
          class="form-input"
          :disabled="status === 'WAIT_AUDIT'"
        /> -->
        <CryptoBlock
          class="input-mobile"
          tag-type="input"
          :bizId="id"
          detail-auth="/distributor-management/:decrypt-mobile"
          :edit-auth="authorityCode"
          bizType="DISTRIBUTOR_LEADS_MOBILE"
          v-model.trim="form.mobile"
          :reset="dialogVisible"
          :disabled="status === 'WAIT_AUDIT'"
        />
      </el-form-item>
      <el-form-item label="商家类型:" prop="merchantType">
        <el-radio-group v-model="form.merchantType">
          <el-radio
            :key="item.id"
            :label="item.id"
            v-for="item in merchantTypeOptions"
            >{{ item.name }}</el-radio
          >
        </el-radio-group>
      </el-form-item>
      <el-form-item label="意向采购:" prop="purchaseType">
        <el-radio-group
          v-model="form.purchaseType"
          @change="purchaseTypeChange"
        >
          <el-radio :key="item.id" :label="item.id" v-for="item in options">{{
            item.name
          }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="店铺销量:">
        <el-select clearable v-model="form.monthlySales">
          <el-option
            :key="idx"
            :label="item.label"
            :value="item.value"
            v-for="(item, idx) in monthlySalesList"
          ></el-option>
        </el-select>
        <div class="tip">店铺销量选择需要先选择采购意向类型</div>
      </el-form-item>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button @click="$emit('update:dialogVisible', false)">取 消</el-button>
      <el-button type="primary" @click="onSave" :loading="btnLoading"
        >确 定</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
// 编辑基础信息弹出框
import {
  getDistributorBasicInfoVO,
  updateDistributorExtInfoBasicInfo
} from '@/api/distributorManagement/info';
import { fechDictData } from '@/api/dict';
export default {
  data() {
    return {
      title: '编辑客户基础信息',
      options: [
        { id: 'PURCHASE', name: '采销' },
        { id: 'DROP_SHIPPING', name: '一件代发' }
      ],
      merchantTypeOptions: [
        { id: 'PERSONAL', name: '个人' },
        { id: 'ENTERPRISE', name: '企业' }
      ],
      form: {
        purchaseType: '',
        monthlySales: '',
        merchantType: '',
        mobile: '',
        name: ''
      },
      status: '',
      loading: false,
      btnLoading: false,
      monthlySalesList: []
    };
  },
  props: {
    authorityCode: String, // 权限编码
    id: String,
    dialogVisible: {
      type: Boolean,
      default: false
    },
    brandInitData: {
      // 修改意向品牌初始值
      type: Array,
      default: () => []
    }
  },
  activated() {
    // this.getBasicInfo()
  },
  computed: {},
  watch: {
    dialogVisible(v) {
      if (!v) return;
      this.getBasicInfo();
    }
  },
  methods: {
    onSave() {
      this.btnLoading = true;
      updateDistributorExtInfoBasicInfo({
        ...this.form,
        id: this.id
      })
        .then((res) => {
          this.$message.success('操作成功');
          this.$emit('onSuccess');
          this.onClose();
        })
        .finally(() => {
          this.btnLoading = false;
        });
    },
    onClose() {
      this.$emit('update:dialogVisible', false);
    },
    // 获取分销商信息
    getBasicInfo() {
      this.loading = true;
      getDistributorBasicInfoVO(this.id)
        .then((res) => {
          const {
            status = '',
            purchaseType = '',
            monthlySales = '',
            merchantType = '',
            mobile = '',
            name = ''
          } = res.data;
          this.form = {
            purchaseType,
            monthlySales,
            merchantType,
            mobile,
            name
          };
          this.status = status;
          this.fetchMonthlySales();
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 店铺销量
    fetchMonthlySales() {
      const type =
        this.form.purchaseType === 'PURCHASE'
          ? 'soyoungzg_monthly_sales_amount_type'
          : 'soyoungzg_monthly_sales_quantity_type';
      fechDictData(type).then((res) => {
        this.monthlySalesList = res.data.map((item) => ({
          value: item.value,
          label: item.label
        }));
      });
    },
    purchaseTypeChange() {
      this.form.monthlySales = '';
      this.fetchMonthlySales();
    }
  }
};
</script>

<style lang="scss" scoped>
.form-input,
.input-mobile {
  width: 195px;
}
.tip {
  font-size: 12px;
  color: #999;
}
</style>
