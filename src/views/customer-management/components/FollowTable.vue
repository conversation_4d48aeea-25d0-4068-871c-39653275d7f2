<template>
  <div>
    <Authority auth="/customer-management/follow-up/:edit">
      <!-- <el-button
        type="primary"
        v-if="type === 'FINISH' && isShowAddBtn"
        @click="add"
        >新增跟进记录</el-button
      > -->
      <el-button
        type="primary"
        v-if="type === 'FINISH' && isShowAddBtn"
        @click="$refs.dialog_followUp.setVisible(true, { CLUEFOLLOWUPTYPE })"
        >新增跟进记录</el-button
      >
      <el-button
        type="primary"
        v-if="type === 'FINISH' && isShowAddBtn"
        @click="onExport"
        :loading="exportLoading"
        >导出记录</el-button
      >
    </Authority>
    <Authority auth="/customer-management/follow-up/:edit">
      <el-button
        type="primary"
        v-if="type === 'WAIT' && isShowAddBtn"
        @click="adddb"
        >新增待办</el-button
      >
    </Authority>
    <el-table
      :data="list"
      style="width: 100%"
      v-if="type === 'FINISH'"
      v-loading.body="listLoading"
    >
      <el-table-column prop="labelName" label="跟进事项"> </el-table-column>
      <el-table-column prop="followWayName" label="跟进方式"> </el-table-column>
      <el-table-column
        prop="followResultName"
        label="跟进结果"
      ></el-table-column>
      <el-table-column prop="todoEvent" label="跟进说明"></el-table-column>
      <el-table-column prop="expectedFinishDate" label="跟进时间" width="180">
        <template slot-scope="scope">
          {{ scope.row.expectedFinishDate | parseTime('{y}-{m}-{d}') }}
        </template>
      </el-table-column>
      <!-- <el-table-column label="序号" width="80">
        <template slot-scope="scope">
          {{ pageSize * (pageNo - 1) + 1 + scope.$index }}
        </template>
      </el-table-column> -->
      <!-- <el-table-column
        prop="platformShopName"
        label="拓展店铺"
      ></el-table-column>
      <el-table-column prop="shopName" label="店铺名称"></el-table-column> -->
      <el-table-column prop="createByName" label="跟进人"></el-table-column>
    </el-table>
    <div class="pagination">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pageNo"
        :page-sizes="[10, 20, 30, 40, 50, 100]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        :disabled="listLoading"
      ></el-pagination>
    </div>
    <el-dialog
      :title="title"
      :visible.sync="dialogVisible"
      width="800px"
      :before-close="() => (dialogVisible = false)"
    >
      <div class="form-box">
        <el-form ref="form" :model="form" :rules="rules" label-width="140px">
          <div class="form-box-title">
            {{ type === 'FINISH' ? '本次跟进记录' : '下次跟进设置' }}
          </div>
          <el-form-item
            :label="type === 'FINISH' ? '跟进完成时间' : '跟进时间'"
            prop="expectedFinishDate"
          >
            <el-date-picker
              :disabled="isDetail"
              type="date"
              v-model="form.expectedFinishDate"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="跟进事项" prop="todoEvent">
            <el-input
              v-model="form.todoEvent"
              type="textarea"
              :autosize="{ minRows: 4, maxRows: 8 }"
            ></el-input>
          </el-form-item>
          <template v-if="type === 'FINISH'">
            <div class="form-box-title">下次跟进设置</div>
            <!-- 下次跟进时间 -->
            <el-form-item label="跟进时间">
              <el-date-picker
                :disabled="isDetail"
                type="date"
                v-model="form.nextExpectedFinishDate"
              ></el-date-picker>
            </el-form-item>
            <!-- 下次跟进事项 -->
            <el-form-item label="跟进事项">
              <el-input
                v-model="form.nextTodoEvent"
                type="textarea"
                :autosize="{ minRows: 4, maxRows: 8 }"
              ></el-input>
            </el-form-item>
          </template>
        </el-form>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="onSubmit" :loading="btnLoading">{{
          isFinish ? '确 定' : '保 存'
        }}</el-button>
      </span>
    </el-dialog>
    <Dialog
      :callback="onSubmitFollowUp"
      class="dialog"
      type="CLUE_FOLLOWUP"
      ref="dialog_followUp"
    >
    </Dialog>
  </div>
</template>

<script>
// 跟进记录、待办事项分页列表
import {
  gettodoPlanList,
  settodoPlanCreate,
  createFinishedTodo,
  createLeadsManagerInBatch,
  settodoPlanUpdate,
  todoPlanDelete,
  todoPlanFinish,
  todoPlanExportExcel
  // getTodoPlanDetail
} from '@/api/customer-management/info';
import { getListLeadsManager } from '@/api/customer-management/clue.js';
import download from '@/utils/download';
import { parseTime } from '@/utils';
import Dialog from '@/components/Dialog/index.vue';

export default {
  components: { Dialog },
  data() {
    return {
      list: [],
      pageNo: 1,
      pageSize: 10,
      total: 0,
      dialogVisible: false,
      listLoading: false,
      title: '',
      waitId: '', // 待办id
      exportLoading: false,
      form: {
        expectedFinishDate: new Date().getTime(), // 跟进时间 默认当前时间
        todoEvent: '', // 跟进事项
        nextExpectedFinishDate: '', // 下次跟进时间
        nextTodoEvent: '' // 下次跟进事项
      },
      rules: {
        expectedFinishDate: [
          { required: true, message: '请选择跟进完成时间', trigger: 'change' }
        ],
        todoEvent: [
          { required: true, message: '请输入跟进事项', trigger: 'blur' }
        ]
      },
      btnLoading: false,
      isFinish: false // 是否是跟进完成确认
    };
  },
  activated() {
    this.fetchDataList();
  },
  props: {
    id: String,
    type: String,
    isDetail: {
      type: Boolean,
      default: false
    },
    // 是否展示新增按钮，默认是true
    isShowAddBtn: {
      type: Boolean,
      default: true
    },
    CLUEFOLLOWUPTYPE: {
      type: String
    }
  },
  computed: {
    formData() {
      const obj = { ...this.form, distributorId: this.id };
      if (this.type === 'WAIT') {
        delete obj.nextExpectedFinishDate;
        delete obj.nextTodoEvent;
      } else {
        if (obj.nextExpectedFinishDate instanceof Date) {
          obj.nextExpectedFinishDate =
            this.form.nextExpectedFinishDate.getTime();
        }
      }
      if (obj.expectedFinishDate instanceof Date) {
        obj.expectedFinishDate = this.form.expectedFinishDate.getTime();
      }

      return obj;
    }
  },
  methods: {
    // 跟进记录
    add() {
      this.title = '跟进备注';
      this.dialogVisible = true;
    },
    // 待办
    adddb() {
      this.isFinish = false;
      this.title = '待办跟进事项';
      this.form.expectedFinishDate = '';
      this.form.todoEvent = '';
      this.waitId = '';
      this.dialogVisible = true;
    },
    onSubmit() {
      this.btnLoading = true;
      this.$refs['form'].validate((valid) => {
        if (valid) {
          if (this.type === 'FINISH') {
            // 新增跟进记录
            this.addFINISH();
          } else {
            if (this.isFinish) {
              // 跟进完成
              this.finish();
            } else {
              // 新增/编辑待办
              this.updateWAIT();
            }
          }
        } else {
          this.btnLoading = false;
        }
      });
    },
    // 新增跟进记录
    addFINISH() {
      const { nextExpectedFinishDate, nextTodoEvent } = this.formData;
      if (
        (nextExpectedFinishDate && nextTodoEvent.trim() === '') ||
        (!nextExpectedFinishDate && nextTodoEvent.trim() !== '')
      ) {
        this.$message.warning(
          '下次跟进设置-跟进时间与跟进事项必须同时填写或者同时不填'
        );
        this.btnLoading = false;
        return;
      }
      createFinishedTodo(this.formData)
        .then((res) => {
          this.$message.success('操作成功');
          this.fetchDataList();
          this.dialogVisible = false;
        })
        .finally(() => {
          this.btnLoading = false;
        });
    },
    // 新增/编辑待办
    updateWAIT() {
      const req = this.waitId ? settodoPlanUpdate : settodoPlanCreate;
      const formData = this.formData;
      if (this.waitId) {
        formData.id = this.waitId;
      }
      req(formData)
        .then((res) => {
          this.$message.success('操作成功');
          this.fetchDataList();
          this.dialogVisible = false;
        })
        .finally(() => {
          this.btnLoading = false;
        });
    },
    onDelete(id) {
      this.$confirm('是否删除该条待办?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          todoPlanDelete(id).then((res) => {
            this.$message({
              type: 'success',
              message: '删除成功!'
            });
            this.fetchDataList();
          });
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
    },
    onUpdata(item) {
      this.waitId = item.id;
      this.form.expectedFinishDate = item.expectedFinishDate;
      this.form.todoEvent = item.todoEvent;
      this.title = '待办跟进事项';
      this.dialogVisible = true;
    },
    confirm(item) {
      this.waitId = item.id;
      this.isFinish = true;
      this.form.expectedFinishDate = item.expectedFinishDate;
      this.form.todoEvent = item.todoEvent;
      this.title = '跟进完成确认';
      this.dialogVisible = true;
    },
    // 导出客户跟进记录
    onExport() {
      this.exportLoading = true;
      todoPlanExportExcel({ distributorExtId: this.id, status: this.type })
        .then((res) => {
          download(
            res,
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            `客户跟进记录-${parseTime(
              Date.now(),
              '{y}-{m}-{d}-{h}:{i}:{s}'
            )}.xlsx`
          );
        })
        .finally(() => {
          this.exportLoading = false;
        });
    },
    // 跟进完成
    finish() {
      const formData = this.formData;
      if (this.waitId) {
        formData.id = this.waitId;
        formData.finishDate = formData.expectedFinishDate;
      }
      todoPlanFinish(formData)
        .then((res) => {
          this.$message.success('操作成功');
          this.fetchDataList();
          this.dialogVisible = false;
        })
        .finally(() => {
          this.btnLoading = false;
        });
    },
    handleSizeChange: function (val) {
      this.pageSize = val;
      this.pageNo = 1;
      this.fetchDataList();
    },
    handleCurrentChange: function (val) {
      this.pageNo = val;
      this.fetchDataList();
    },
    fetchDataList() {
      this.listLoading = true;
      const listQuery = {
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        data: {
          [this.CLUEFOLLOWUPTYPE === 'develop'
            ? 'distributorExtId'
            : 'distributorId']: this.id,
          status: this.type
        }
      };
      (this.CLUEFOLLOWUPTYPE === 'develop'
        ? getListLeadsManager
        : gettodoPlanList)(listQuery)
        .then((response) => {
          this.list = response.data.list;
          this.total = response.data.total;
        })
        .finally(() => {
          this.listLoading = false;
        });
    },
    onSubmitFollowUp(followUpData) {
      const data = {
        ...followUpData
      };
      let requestUrl;
      if (this.CLUEFOLLOWUPTYPE === 'develop') {
        requestUrl = createLeadsManagerInBatch;
        data.distributorIds = [this.id];
      } else {
        requestUrl = createFinishedTodo;
        data.distributorId = this.id;
      }

      requestUrl(data).then((res) => {
        this.fetchDataList();
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.form-box {
  .form-box-title {
    color: #333;
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 10px;
  }
}
</style>
