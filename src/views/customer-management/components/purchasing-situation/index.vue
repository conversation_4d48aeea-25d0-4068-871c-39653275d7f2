<template>
  <div>
    <div class="info-box">
      <h3 class="info-box-title">
        采购统计
        <router-link
          class="link trend"
          :to="`/distributor-data/distributor-purchase-trend/${distributorBodyVO.id}`"
          >采购趋势
        </router-link>
      </h3>
      <div class="info-box-row">
        <div class="info-box-row--call">
          <div class="info-box-row--call--title">采购总金额：</div>
          <div class="info-box-row--call--text">
            {{ distributorPurchaseDataVO.totalPurchaseAmount | fnAmount }}
            <LinkBtn
              linkType="windowOpen"
              :linkParams="linkParams"
              :to="
                `/customer-management/system-customer-management/purchasing-data`
              "
            />
          </div>
        </div>
        <div class="info-box-row--call">
          <div class="info-box-row--call--title">客单价：</div>
          <div class="info-box-row--call--text">
            ￥{{ distributorPurchaseDataVO.averageOrderAmount }}
          </div>
        </div>
        <div class="info-box-row--call">
          <div class="info-box-row--call--title">返点总金额：</div>
          <div class="info-box-row--call--text">
            {{ distributorPurchaseDataVO.totalVirtualCredit | fnAmount }}
            <LinkBtn
              linkType="windowOpen"
              :linkParams="linkParams"
              :to="
                `/customer-management/system-customer-management/rebates-data/list`
              "
            />
          </div>
        </div>
        <div class="info-box-row--call">
          <div class="info-box-row--call--title">返点使用金额：</div>
          <div class="info-box-row--call--text">
            {{ distributorPurchaseDataVO.totalUsedVirtualCredit | fnAmount }}
          </div>
        </div>
      </div>
      <div class="info-box-row">
        <div class="info-box-row--call">
          <div class="info-box-row--call--title">采购品牌数：</div>
          <div class="info-box-row--call--text">
            {{ distributorPurchaseDataVO.purchaseBrandCount | fnCount }}
            <LinkBtn
              linkType="windowOpen"
              :linkParams="linkParams"
              :to="
                `/customer-management/system-customer-management/purchasing-brand/list`
              "
            />
          </div>
        </div>
        <div class="info-box-row--call">
          <div class="info-box-row--call--title">采购商品数：</div>
          <div class="info-box-row--call--text">
            {{ distributorPurchaseDataVO.purchaseCommodityCount | fnCount }}
            <LinkBtn
              linkType="windowOpen"
              :linkParams="linkParams"
              :to="
                `/customer-management/system-customer-management/purchase-goods`
              "
            />
          </div>
        </div>
        <div class="info-box-row--call">
          <div class="info-box-row--call--title">偏好品类前3：</div>
          <div class="info-box-row--call--text">
            <span
              v-if="
                distributorPurchaseDataVO.favoriteCategoryList &&
                  distributorPurchaseDataVO.favoriteCategoryList.length > 0
              "
            >
              {{
                distributorPurchaseDataVO.favoriteCategoryList.join('、')
              }}</span
            >
            <span v-else>{{ noTxt }}</span>
            <LinkBtn
              linkType="windowOpen"
              :linkParams="linkParams"
              :to="
                `/customer-management/system-customer-management/category-sales/list`
              "
            />
          </div>
        </div>
      </div>
    </div>
    <div class="info-box">
      <h3 class="info-box-title">最近采购</h3>
      <table border="0" class="table-box">
        <tr class="table-box-row">
          <td class="table-box-title">采购时间</td>
          <td class="table-box-text">
            <span v-if="lastBuyVO.payTime && lastBuyVO.payTime > 0">{{
              lastBuyVO.payTime | parseTime
            }}</span>
            <span v-else>{{ noTxt }}</span>
          </td>
          <td class="table-box-title">订单实付金额(含运费)</td>
          <td class="table-box-text">
            <span v-if="lastBuyVO.payAmount >= 0">￥</span
            >{{ lastBuyVO.payAmount }}
          </td>
        </tr>
        <tr class="table-box-row">
          <td class="table-box-title">采购商品名称</td>
          <td class="table-box-text">
            <div v-for="(item, inx) of lastBuyVO.commodityInfoList" :key="inx">
              {{ inx + 1 }}、{{ item }}
            </div>
            <div
              v-if="
                lastBuyVO.commodityInfoList &&
                  lastBuyVO.commodityInfoList.length === 0
              "
            >
              {{ noTxt }}
            </div>
          </td>
          <td class="table-box-title">采购品牌</td>
          <td class="table-box-text">
            {{ lastBuyVO.brandInfoList.join('、') }}
            <div
              v-if="
                lastBuyVO.brandInfoList && lastBuyVO.brandInfoList.length === 0
              "
            >
              {{ noTxt }}
            </div>
          </td>
        </tr>
        <tr class="table-box-row">
          <td class="table-box-title">采购订单号</td>
          <td class="table-box-text">{{ lastBuyVO.orderNo }}</td>
          <td class="table-box-title">平均毛利率</td>
          <td class="table-box-text">
            <span
              v-if="
                isRealNum(lastBuyVO.averageGrossProfitRate) &&
                  lastBuyVO.averageGrossProfitRate >= 0
              "
            >
              {{ (lastBuyVO.averageGrossProfitRate * 100).toFixed(2) }}%</span
            >
            <span v-else>{{ noTxt }}</span>
          </td>
        </tr>
      </table>
    </div>
    <div class="info-box">
      <h3 class="info-box-title">首采情况</h3>
      <table border="0" class="table-box">
        <tr class="table-box-row">
          <td class="table-box-title">首采时间</td>
          <td class="table-box-text">
            <span v-if="firstBuyVO.payTime && firstBuyVO.payTime > 0">{{
              firstBuyVO.payTime | parseTime
            }}</span>
            <span v-else>{{ noTxt }}</span>
          </td>
          <td class="table-box-title">首采订单实付金额</td>
          <td class="table-box-text">
            <span v-if="secondBuyVO.payAmount >= 0">￥</span
            >{{ firstBuyVO.payAmount }}
          </td>
        </tr>
        <tr class="table-box-row">
          <td class="table-box-title">首采商品名称</td>
          <td class="table-box-text">
            <div v-for="(item, inx) of firstBuyVO.commodityInfoList" :key="inx">
              {{ inx + 1 }}、{{ item }}
            </div>
            <div
              v-if="
                firstBuyVO.commodityInfoList &&
                  firstBuyVO.commodityInfoList.length === 0
              "
            >
              {{ noTxt }}
            </div>
          </td>
          <td class="table-box-title">首采品牌</td>
          <td class="table-box-text">
            {{ firstBuyVO.brandInfoList.join('、') }}
            <div
              v-if="
                firstBuyVO.brandInfoList &&
                  firstBuyVO.brandInfoList.length === 0
              "
            >
              {{ noTxt }}
            </div>
          </td>
        </tr>
        <tr class="table-box-row">
          <td class="table-box-title">首采订单号</td>
          <td class="table-box-text">{{ firstBuyVO.orderNo }}</td>
          <td class="table-box-title">平均毛利率</td>
          <td class="table-box-text">
            <span
              v-if="
                isRealNum(lastBuyVO.averageGrossProfitRate) &&
                  firstBuyVO.averageGrossProfitRate >= 0
              "
            >
              {{ (firstBuyVO.averageGrossProfitRate * 100).toFixed(2) }}%</span
            >
            <span v-else>{{ noTxt }}</span>
          </td>
        </tr>
      </table>
    </div>
    <div class="info-box">
      <h3 class="info-box-title">复购情况</h3>
      <table border="0" class="table-box">
        <tr class="table-box-row">
          <td class="table-box-title">复购时间</td>
          <td class="table-box-text">
            <span v-if="secondBuyVO.payTime && secondBuyVO.payTime > 0">{{
              secondBuyVO.payTime | parseTime
            }}</span>
            <span v-else>{{ noTxt }}</span>
          </td>
          <td class="table-box-title">复购订单实付金额</td>
          <td class="table-box-text">
            <span v-if="secondBuyVO.payAmount >= 0">￥</span>
            {{ secondBuyVO.payAmount }}
          </td>
        </tr>
        <tr class="table-box-row">
          <td class="table-box-title">复购商品名称</td>
          <td class="table-box-text">
            <div
              v-for="(item, inx) of secondBuyVO.commodityInfoList"
              :key="inx"
            >
              {{ inx + 1 }}、{{ item }}
            </div>
            <div
              v-if="
                lastBuyVO.commodityInfoList &&
                  lastBuyVO.commodityInfoList.length === 0
              "
            >
              {{ noTxt }}
            </div>
          </td>
          <td class="table-box-title">复购品牌</td>
          <td class="table-box-text">
            <span
              v-if="
                secondBuyVO.brandInfoList.length &&
                  secondBuyVO.brandInfoList.length >= 0
              "
              >{{ secondBuyVO.brandInfoList.join('、') }}</span
            >
            <div v-else>{{ noTxt }}</div>
          </td>
        </tr>
        <tr class="table-box-row">
          <td class="table-box-title">复购订单号</td>
          <td class="table-box-text">{{ secondBuyVO.orderNo }}</td>
          <td class="table-box-title">平均毛利率</td>
          <td class="table-box-text">
            <span
              v-if="
                isRealNum(lastBuyVO.averageGrossProfitRate) &&
                  secondBuyVO.averageGrossProfitRate >= 0
              "
            >
              {{ (secondBuyVO.averageGrossProfitRate * 100).toFixed(2) }}%</span
            >
            <span v-else>{{ noTxt }}</span>
          </td>
        </tr>
      </table>
    </div>
  </div>
</template>

<script>
// 采购情况
import LinkBtn from '@/components/LinkBtn';
export default {
  components: {
    LinkBtn
  },
  data() {
    const noTxt = '/';
    return {
      tableData: [],
      dialogVisible: false,
      noTxt,
      initData: {
        averageGrossProfitRate: null,
        brandInfoList: [],
        commodityInfoList: [],
        orderId: noTxt,
        orderNo: noTxt,
        payAmount: noTxt,
        payTime: noTxt
      }
    };
  },
  filters: {
    fnAmount(v) {
      if (v && v >= 0) {
        return '￥' + v;
      } else {
        return '￥0';
      }
    },
    fnCount(v) {
      if (v && v >= 0) {
        return v + '个';
      } else {
        return '0个';
      }
    }
  },
  props: {
    distributorPurchaseDataVO: Object,
    distributorBodyVO: Object
  },
  methods: {
    isRealNum(val) {
      // isNaN()函数 把空串 空格 以及NUll 按照0来处理 所以先去除，
      if (val === '' || val == null) {
        return false;
      }
      if (!isNaN(val)) {
        // 对于空数组和只有一个数值成员的数组或全是数字组成的字符串，isNaN返回false，例如：'123'、[]、[2]、['123'],isNaN返回false,
        // 所以如果不需要val包含这些特殊情况，则这个判断改写为if(!isNaN(val) && typeof val === 'number' )
        return true;
      } else {
        return false;
      }
    }
  },
  computed: {
    // 跳转参数
    linkParams() {
      const { id, shopName, mobile } = this.distributorBodyVO;
      return { id, shopName, mobile };
    },
    // 最近采购
    lastBuyVO() {
      const obj = { ...this.initData };
      const { lastBuyVO = null } = this.distributorPurchaseDataVO;
      if (lastBuyVO) {
        Object.assign(obj, lastBuyVO);
      }
      return obj;
    },
    // 首采情况
    firstBuyVO() {
      const obj = { ...this.initData };
      const { firstBuyVO = null } = this.distributorPurchaseDataVO;
      if (firstBuyVO) {
        Object.assign(obj, firstBuyVO);
      }
      return obj;
    },
    // 复购情况
    secondBuyVO() {
      const obj = { ...this.initData };
      const { secondBuyVO = null } = this.distributorPurchaseDataVO;
      if (secondBuyVO) {
        Object.assign(obj, secondBuyVO);
      }
      return obj;
    }
  }
};
</script>

<style lang="scss" scoped>
@import './style';
.add-record-btn {
  margin-left: 10px;
}
.table-box {
  border: 0;
  border-collapse: collapse;
  width: 80%;
  min-width: 800px;
  td {
    border: 1px solid #ccc;
    padding: 20px 10px;
  } //这里改
  &-title {
    background: #f6f6f6;
    color: #333;
    font-size: 16px;
    font-weight: 600;
    width: 160px;
  }
  &-text {
    font-size: 14px;
    color: #666;
    width: 400px;
  }
}
.trend{
  font-size: 12px;
  font-weight: normal;
  margin-left: 10px;
}
</style>
