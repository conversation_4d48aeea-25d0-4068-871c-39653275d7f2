<template>
  <el-form ref="form" :model="form" label-width="100px">
    <h3 class="title">基础信息</h3>
    <el-form-item label="拓展渠道">
      <el-select
        v-model="form.channels"
        multiple
        placeholder="请选择"
        style="width: 80%"
        value-key="typeValue"
      >
        <el-option
          v-for="item in dicTypeKeyList['soyoungzg_channel'].options"
          :key="item.typeValue"
          :label="item.typeValueName"
          :value="item.typeValue"
        ></el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="店铺ID">
      <el-input
        type="text"
        v-model="form.platformShopId"
        placeholder="请输入店铺ID"
        style="width: 80%"
      ></el-input>
    </el-form-item>
    <el-form-item label="拓展店铺名称">
      <el-input
        type="text"
        v-model="form.platformShopName"
        placeholder="请输入拓展店铺名称"
        style="width: 80%"
      ></el-input>
    </el-form-item>
    <el-form-item label="拓展关键词">
      <el-input
        type="text"
        v-model="form.keyWord"
        placeholder="请输入拓展关键词"
        style="width: 80%"
      ></el-input>
    </el-form-item>
    <el-form-item label="拓展品牌">
      <el-select
        v-model="form.favoriteBrandList"
        multiple
        value-key="typeValue"
        placeholder="请选择(可多选)"
        style="width: 80%"
      >
        <el-option
          v-for="item in brandListOptions"
          :key="item.typeValue"
          :label="item.typeValueName"
          :value="item"
        ></el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="主营类目">
      <el-select
        v-model="form.businessCategoryList"
        multiple
        placeholder="请选择"
        style="width: 80%"
        value-key="typeValue"
      >
        <el-option
          v-for="item in dicTypeKeyList['syzg_business_category'].options"
          :key="item.typeValue"
          :label="item.typeValueName"
          :value="item"
        ></el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="意向品牌">
      <el-select
        v-model="form.interestedBrandList"
        multiple
        placeholder="请选择(可多选)"
        style="width: 80%"
        value-key="typeValue"
      >
        <el-option
          v-for="item in brandListOptions"
          :key="item.typeValue"
          :label="item.typeValueName"
          :value="item"
        ></el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="来源渠道">
      <el-select
        v-model="chanelIdsList"
        multiple
        placeholder="请选择(可多选)"
        style="width: 80%"
        @change="handlerChanel"
        value-key="typeValue"
      >
        <el-option
          v-for="item in dicTypeKeyList['soyoungzg_channel'].options"
          :key="item.typeValue"
          :label="item.typeValueName"
          :value="item"
        ></el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="来源店铺" v-if="shopList.length">
      <div class="list">
        <div v-for="(item, index) in shopList" :key="index" class="item">
          <div class="label-name">{{ item[0].channelName }}店铺名称</div>
          <div v-for="(_item, _index) in item" :key="_index">
            <el-input
              placeholder="请输入来源店铺名称"
              v-model="_item.platformShopName"
              style="width: 300px; margin-right: 20px"
            ></el-input>
            <el-select
              v-model="_item.platformShopLevel"
              placeholder="请选择店铺等级"
              style="width: 200px; margin-right: 20px"
              v-if="channelMeet(_item)"
              value-key="label"
            >
              <el-option
                v-for="item in dicTypeKeyChannelList[_item.channel].options"
                :key="item"
                :label="item"
                :value="item"
              ></el-option>
            </el-select>
            <el-input
              v-else
              type="text"
              placeholder="请输入店铺等级"
              maxlength="20"
              style="width: 200px; margin-right: 20px"
              v-model="_item.platformShopLevel"
            ></el-input>
            <div class="handle-btns" style="width: 76px">
              <el-button
                type="primary"
                v-if="_index === 0"
                @click="
                  addShop(item, {
                    channelName: _item.channelName,
                    channel: _item.channel
                  })
                "
                >添加</el-button
              >
              <el-button v-if="_index >= 1" @click="deleteShop(_index, item)"
                >删除</el-button
              >
            </div>
          </div>
        </div>
      </div>
    </el-form-item>
    <el-form-item label="微信号码">
      <el-input
        type="text"
        v-model="form.wechat"
        placeholder="请输入微信号"
        style="width: 80%"
      ></el-input>
    </el-form-item>
    <el-form-item label="不合作原因">
      <el-input
        type="text"
        v-model="form.rejectReason"
        placeholder="请输入不合作原因"
        style="width: 80%"
      ></el-input>
    </el-form-item>
    <h3 class="title">其他信息</h3>
    <el-form-item label="触达政策">
      <el-select
        v-model="form.favoritePolicyList"
        multiple
        placeholder="请选择(可多选)"
        style="width: 80%"
        value-key="typeValue"
      >
        <el-option
          v-for="item in dicTypeKeyList['soyoungzg_favorite_policy'].options"
          :key="item.typeValue"
          :label="item.typeValueName"
          :value="item"
        ></el-option>
      </el-select>
    </el-form-item>
    <h4 class="second-title">背调信息资料录入</h4>
    <el-form-item label="商家类型">
      <el-radio-group v-model="form.merchantType">
        <el-radio label="ENTERPRISE">企业</el-radio>
        <el-radio label="PERSONAL">个人</el-radio>
      </el-radio-group>
    </el-form-item>
    <template v-if="form.merchantType === 'ENTERPRISE'">
      <el-form-item label="公司名称">
        <el-input
          type="text"
          v-model="form.company"
          placeholder="请输入公司名称"
          maxlength="50"
        ></el-input>
      </el-form-item>
      <el-form-item label="企业规模">
        <el-select
          v-model="form.companyScale"
          placeholder="请选择"
          style="width: 80%"
        >
          <el-option
            v-for="item in dicTypeKeyList['syzg_enterprise_scale'].options"
            :key="item.typeValue"
            :label="item.typeValueName"
            :value="item.typeValue"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="注册资本">
        <div class="inp-row">
          <el-input
            type="text"
            class="input"
            v-model="form.registerCapital"
            placeholder="请输入注册资本"
          ></el-input>
          万元
        </div>
      </el-form-item>
    </template>
    <el-form-item label="店铺人群">
      <span class="form-group">
        <el-form-item>
          <el-select
            v-model="form.ageGroupList"
            multiple
            placeholder="请选择年龄段"
            value-key="typeValue"
          >
            <el-option
              v-for="item in dicTypeKeyList['soyoungzg_age_group'].options"
              :key="item.typeValue"
              :label="item.typeValueName"
              :value="item"
            ></el-option> </el-select
        ></el-form-item>
        <el-form-item
          ><el-select
            v-model="form.consumerGroupList"
            multiple
            placeholder="请选择用户群体"
            value-key="typeValue"
          >
            <el-option
              v-for="item in dicTypeKeyList['soyoungzg_consumer_group'].options"
              :key="item.typeValue"
              :label="item.typeValueName"
              :value="item"
            ></el-option> </el-select
        ></el-form-item>
        <el-form-item>
          <el-select
            v-model="form.sex"
            multiple
            placeholder="请选择用户性别"
            value-key="typeValue"
          >
            <el-option
              v-for="item in dicTypeKeyList['sex'].options"
              :key="item.typeValue"
              :label="item.typeValueName"
              :value="item"
            ></el-option>
          </el-select>
        </el-form-item>
      </span>
    </el-form-item>
    <el-form-item label="法人姓名" v-if="form.merchantType === 'ENTERPRISE'">
      <div class="inp-row">
        <el-input
          type="text"
          v-model="form.companyLegalName"
          placeholder="请输入法人姓名"
          class="input"
        ></el-input>
      </div>
    </el-form-item>
    <el-form-item
      label="企业联系方式"
      v-if="form.merchantType === 'ENTERPRISE'"
    >
      <div class="inp-row">
        <!-- <el-input
          type="text"
          v-model="form.companyLegalMobile"
          placeholder="请输入企业联系方式"
          class="input"
        ></el-input> -->
        <CryptoBlock
          class="input"
          tag-type="input"
          :biz-id="form.id"
          detail-auth="/distributor-management/:decrypt-mobile"
          :edit-auth="authorityCode"
          biz-type="DISTRIBUTOR_LEADS_COMPANY_MOBILE"
          v-model.trim="form.companyLegalMobile"
        />
      </div>
    </el-form-item>
    <el-form-item label="联系人姓名">
      <div class="inp-row">
        <el-input
          type="text"
          v-model="form.contactName"
          placeholder="请输入联系人姓名"
          class="input"
        ></el-input>
      </div>
    </el-form-item>
    <el-form-item label="联系人电话">
      <div class="inp-row">
        <!-- <el-input
          type="text"
          v-model="form.contactPhone"
          placeholder="请输入联系人电话"
          class="input"
        ></el-input> -->
        <CryptoBlock
          class="input"
          tag-type="input"
          :biz-id="form.id"
          detail-auth="/distributor-management/:decrypt-mobile"
          :edit-auth="authorityCode"
          biz-type="DISTRIBUTOR_LEADS_CONTACT_PHONE"
          v-model.trim="form.contactPhone"
        />
      </div>
    </el-form-item>
    <h3 class="title">客户潜力</h3>
    <el-form-item label="客户潜力">
      <el-select
        v-model="form.customerTypeValue"
        placeholder="请选择客户潜力"
        style="width: 80%"
        @change="customerTypeChange"
      >
        <el-option
          v-for="item in dicTypeKeyList['soyoungzg_consumer_type'].options"
          :key="item.typeValue"
          :label="item.typeValueName"
          :value="item.typeValue"
        ></el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="偏好品类">
      <el-select
        v-model="form.favoriteCategoryList"
        multiple
        placeholder="请选择(可多选)"
        style="width: 80%"
        value-key="typeValue"
      >
        <el-option
          v-for="item in favoriteCategoryListOptions"
          :key="item.typeValue"
          :label="item.typeValueName"
          :value="item"
        ></el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="商品偏好">
      <el-select
        v-model="form.favoriteCommodityList"
        multiple
        placeholder="请选择(可多选)"
        style="width: 80%"
        value-key="typeValue"
      >
        <el-option
          v-for="item in favoriteCommodityOptions"
          :key="item.typeValue"
          :label="item.typeValueName"
          :value="item"
        ></el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="意向程度">
      <el-select
        v-model="form.interestedDegree"
        placeholder="请选择"
        style="width: 80%"
      >
        <el-option
          v-for="item in dicTypeKeyList['soyoungzg_interested_degree'].options"
          :key="item.typeValue"
          :label="item.typeValueName"
          :value="item.typeValue"
        ></el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="有无仓库">
      <el-select
        v-model="form.isWarehouse"
        placeholder="请选择"
        style="width: 80%"
      >
        <el-option label="无" value="0"></el-option>
        <el-option label="有" value="1"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item style="text-align: right">
      <el-button type="primary" @click="onSubmit">确定</el-button>
      <el-button @click="onCancel">取消</el-button>
    </el-form-item>
  </el-form>
</template>
<script>
import { fechDictData } from '@/api/dict';
import { listCategories } from '@/api/commodity';
import { listBrand } from '@/api/shop-decoration/website-brand';
import {
  updateExtInfo,
  getExtInfo,
  getFavoriteCommodityList
} from '@/api/customer-management/follow-up';
export default {
  props: {
    id: String,
    authorityCode: String
  },
  data() {
    return {
      chanelIdsList: [],
      favoriteCategoryListOptions: [],
      brandListOptions: [],
      favoriteCommodityOptions: [],
      form: {
        channels: [], // 拓展渠道
        platformShopId: '', // 店铺ID
        platformShopName: '', // 拓展店铺名称
        keyWord: '', // 拓展关键词
        businessCategoryList: [], // 主营类目
        companyLegalName: '', // 法人姓名
        companyLegalMobile: '', // 企业联系方式
        id: '',
        favoriteBrandList: [],
        favoritePolicyList: [],
        distributorPlatformShopVOList: [],
        consumerGroupList: [],
        ageGroupList: [],
        sex: [],
        merchantType: 'ENTERPRISE',
        wechat: '',
        rejectReason: '', // 不合作原因
        companyScale: '',
        registerCapital: '',
        contactPhone: '',
        contactName: '',
        customerTypeList: [],
        customerTypeValue: '',
        customerTypeTypeValueName: '',
        favoriteCategoryList: [],
        interestedBrandList: [],
        favoriteCommodityList: [],
        interestedDegree: '', // 意向程度: 很高-VERY_HIGH,高-HIGH,一般-GENERAL,低-LOW。字典：soyoungzg_interested_degree ,
        isWarehouse: '' // 有无仓库-新增字段.0-无，1-有
      },
      dicTypeKeyChannelList: {
        TAOBAO: {
          options: [
            '一钻',
            '两钻',
            '三钻',
            '四钻',
            '一蓝冠',
            '两蓝冠',
            '三蓝冠',
            '四蓝冠',
            '一金冠',
            '两金冠',
            '三金冠',
            '四金冠'
          ]
        },
        TMALL: {
          options: ['专营店', '专卖店']
        },
        PDD: {
          options: ['个人店', '专营店', '专卖店']
        }
      },
      dicTypeKeyList: {
        syzg_enterprise_scale: {
          value: '企业规模',
          options: [],
          resultKey: ''
        },
        soyoungzg_channel: {
          value: '来源渠道',
          options: [],
          resultKey: ''
        },
        syzg_business_category: {
          value: '主营类目',
          options: [],
          resultKey: ''
        },
        soyoungzg_favorite_policy: {
          value: '触达政策',
          options: [],
          resultKey: 'favoritePolicyList'
        },
        soyoungzg_age_group: {
          value: '年龄阶段',
          options: [],
          resultKey: 'ageGroupList'
        },
        soyoungzg_consumer_group: {
          value: '用户群体',
          options: [],
          resultKey: 'consumerGroupList'
        },
        sex: {
          value: '用户性别',
          options: [],
          resultKey: 'sex'
        },
        soyoungzg_consumer_type: {
          value: '客户潜力',
          options: [],
          resultKey: 'customerTypeList'
        },
        soyoungzg_interested_degree: {
          value: '意向程度',
          options: [],
          resultKey: 'soyoungzg_interested_degree'
        }
      },
      shopList: []
    };
  },
  methods: {
    onSubmit() {
      this.form.distributorPlatformShopVOList = JSON.parse(
        JSON.stringify([].concat.apply([], this.shopList))
      );
      this.updateExtInfo();
    },
    // 获取客户资料
    getExtInfo() {
      getExtInfo(this.form.id).then((res) => {
        const { distributorPlatformShopVOList } = res.data;
        this.form = Object.assign({}, this.form, res.data);
        // 来源渠道
        if (distributorPlatformShopVOList) {
          this.shopList = this.formatArray(distributorPlatformShopVOList);
          this.shopList.forEach((item) => {
            this.chanelIdsList.push({
              typeValue: item[0].channel,
              typeValueName: item[0].channelName
            });
          });
        }
      });
    },
    // 将二维数组根据指定字段转成一维数组
    formatArray(array) {
      return Object.values(
        array.reduce((res, item) => {
          res[item.channel]
            ? res[item.channel].push(item)
            : (res[item.channel] = [item]);
          return res;
        }, {})
      );
    },
    onCancel() {
      this.$emit('onCancel');
      this.$emit('closeDialog');
    },
    addShop(list, { channel, channelName }) {
      list.push({
        channel,
        channelName,
        platformShopLevel: '',
        platformShopName: ''
      });
    },
    handlerChanel(val) {
      const shopList = JSON.parse(
        JSON.stringify([].concat.apply([], this.shopList))
      );
      const tagIds = val.map((item) => item.typeValue);
      const shopIdsList = shopList.map((item) => item.channel);
      // 更新店铺列表
      for (const k of val) {
        // 新增的来源渠道标签 插入一条默认（空）店铺
        if (shopIdsList.indexOf(k.typeValue) < 0) {
          shopList.push({
            channel: k.typeValue,
            channelName: k.typeValueName,
            platformShopLevel: '',
            platformShopName: ''
          });
        }
      }

      // 不存在的来源标签 从列表中删除
      const result = shopList.filter(
        (item) => tagIds.indexOf(item.channel) > -1
      );
      this.shopList = this.formatArray(result);
    },
    deleteShop(index, list) {
      list.splice(index, 1);
    },
    fetchOptionsByDicType() {
      Object.getOwnPropertyNames(this.dicTypeKeyList).map((key) => {
        fechDictData(key).then((res) => {
          this.dicTypeKeyList[key].options = res.data.map((item) => ({
            typeValueName: item.label,
            typeValue: item.value
          }));
        });
      });
    },
    // 获取商品爱好列表
    async getFavoriteCommodityList() {
      const { data: res } = await getFavoriteCommodityList();
      this.favoriteCommodityOptions = res.map((item) => ({
        typeValueName: item.typeValue,
        typeValue: item.id
      }));
    },
    // 获取商品品类
    async getListCategories() {
      const { data: res } = await listCategories();
      this.favoriteCategoryListOptions = this.formatMapList(res);
    },
    // 获取品牌列表
    formatMapList(data) {
      return data.map((item) => ({
        typeValueName: item.name,
        typeValue: item.id
      }));
    },
    async getListBrand() {
      const { data: res } = await listBrand({});
      this.brandListOptions = this.formatMapList(res);
    },
    // 提交资料
    updateExtInfo() {
      updateExtInfo(this.form).then((res) => {
        this.$message({
          type: 'success',
          message: '新增成功'
        });
        this.$emit('onSuccess');
        this.$emit('closeDialog');
      });
    },
    customerTypeChange(id) {
      this.form.customerTypeTypeValueName = this.dicTypeKeyList[
        'soyoungzg_consumer_type'
      ].options.find((item) => item.typeValue === id).typeValueName;
    },
    initPage() {
      this.form.id = this.id;
      this.fetchOptionsByDicType();
      this.getFavoriteCommodityList();
      this.getListCategories();
      this.getListBrand();
      this.getExtInfo();
    },
    // 判断是否满足
    channelMeet(_item) {
      const arr = ['TAOBAO', 'TMALL', 'PDD'];
      if (arr.includes(_item.channel)) return true;
      return false;
    }
  },
  created() {
    this.initPage();
  }
};
</script>
<style scoped lang="scss">
.list {
  .item {
    border: 1px solid #ddd;
    margin-bottom: 10px;
    padding: 10px;
    & > div {
      display: flex;
      margin-bottom: 2px;
      .label-name {
        width: 120px;
      }
    }
  }
}
.second-title {
  color: #999;
}
.form-group {
  display: flex;
  & > div {
    width: 250px;
  }
}
.inp-row {
  display: flex;
  .input {
    width: 528px;
    margin-right: 10px;
  }
}
</style>
