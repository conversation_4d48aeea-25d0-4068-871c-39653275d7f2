<template>
  <div class="info-box">
    <h3 class="info-box-title">
      <div class="info-box-title--leftbox">
        违规记录(共{{ violateRecordVOList.length || '0' }}条)
        <Authority :auth="authorityCode" v-if="!isDetails">
          <el-button
            type="primary"
            @click="addRecord"
            size="mini"
            class="add-record-btn"
            >新增违规记录</el-button
          >
        </Authority>
      </div>
      <div class="info-box-title--rightbox">
        <router-link
          :to="{
            name: '/customer-management/system-customer-management/operations-client-list/criminal-records',
            params: { id: id }
          }"
        >
          <el-button type="text" size="mini">查看全部违规</el-button>
        </router-link>
      </div>
    </h3>
    <el-table :data="violateRecordVOList" style="width: 100%">
      <el-table-column prop="channelName" label="平台名称" width="180">
      </el-table-column>
      <el-table-column prop="platformShopName" label="店铺名称" width="180">
      </el-table-column>
      <el-table-column prop="violateDate" label="违规时间">
        <template slot-scope="scope">{{
          scope.row.violateDate | parseTime
        }}</template>
      </el-table-column>
      <el-table-column prop="commodityName" label="违规商品"> </el-table-column>
      <el-table-column prop="guidePrice" label="控价价格（元/件）">
      </el-table-column>
      <el-table-column prop="salePrice" label="破价价格（元/件）">
      </el-table-column>
      <el-table-column prop="isFine" label="是否交罚款">
        <template slot-scope="scope">
          {{ scope.row.isFine === '1' ? '是' : '否' }}
        </template>
      </el-table-column>
      <el-table-column prop="isFreeze" label="是否冻结账号">
        <template slot-scope="scope">
          {{ scope.row.isFreeze === '1' ? '是' : '否' }}
        </template>
      </el-table-column>
      <el-table-column prop="violateNumber" label="违规次数"> </el-table-column>
    </el-table>
    <!-- 弹窗 -->
    <el-dialog :title="title" :visible.sync="dialogVisible" width="800px">
      <violationr-ecord
        v-if="dialogVisible"
        :id="id"
        @closeDialog="dialogVisible = false"
        @onSuccess="$emit('onSuccess')"
      />
    </el-dialog>
  </div>
</template>

<script>
import ViolationrEcord from '../violationr-ecord';
// 违规记录
export default {
  components: { ViolationrEcord },
  data() {
    return {
      tableData: [],
      dialogVisible: false,
      title: '新增违规记录'
    };
  },
  props: {
    authorityCode: String, // 按钮权限编码
    isDetails: Boolean,
    id: String,
    violateRecordVOList: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    addRecord() {
      this.dialogVisible = true;
    }
  }
};
</script>

<style lang="scss" scoped>
@import './style';
.add-record-btn {
  margin-left: 10px;
}
</style>
