<template>
  <div class="info-box">
    <h3 class="info-box-title">
      客户资料
      <Authority :auth="authorityCode" v-if="!isDetails">
        <el-button
          type="primary"
          class="addbtn"
          @click="editCustomerInfo"
          size="mini"
          >{{ isEXPAND ? '补充客户资料' : '补充资料' }}</el-button
        >
      </Authority>
    </h3>
    <div class="info-box-row">
      <div class="info-box-row--call">
        <h3 class="info-box-row--call--h3">基础信息</h3>
      </div>
      <div class="info-box-row--call">
        <h3 class="info-box-row--call--h3">其他信息</h3>
      </div>
    </div>
    <div class="info-box-row">
      <div class="info-box-row--call">
        <div class="info-box-row--call--title">拓展渠道</div>
        <div class="info-box-row--call--text">
          <div
            v-if="
              !distributorExtInfoVO.channelNames ||
              distributorExtInfoVO.channelNames.length === 0
            "
          >
            {{ noTxt }}
          </div>
          <div v-else>
            <span
              v-for="(val, idx) in distributorExtInfoVO.channelNames"
              :key="idx"
              ><span v-if="idx !== 0">, </span>{{ val }}</span
            >
          </div>
        </div>
      </div>
      <div class="info-box-row--call">
        <div class="info-box-row--call--title">触达政策</div>
        <div class="info-box-row--call--text">
          {{ favoritePolicyList }}
        </div>
      </div>
    </div>
    <div class="info-box-row">
      <div class="info-box-row--call">
        <div class="info-box-row--call--title">拓展店铺名称</div>
        <div class="info-box-row--call--text">
          {{ distributorExtInfoVO.platformShopName || noTxt }}
        </div>
      </div>
      <div class="info-box-row--call">
        <div class="info-box-row--call--title">背调信息</div>
        <div
          class="info-box-row--call--text"
          v-if="distributorExtInfoVO.merchantType"
        >
          <div class="info-box-row--call--text--row">
            <div v-if="distributorExtInfoVO.merchantType === 'PERSONAL'">
              个人名称： {{ distributorExtInfoVO.contactName | filterVONoText }}
            </div>
            <div v-if="distributorExtInfoVO.merchantType === 'ENTERPRISE'">
              公司名称： {{ distributorExtInfoVO.company | filterVONoText }}
            </div>
          </div>
          <div class="info-box-row--call--text--row">
            <span
              v-if="
                distributorExtInfoVO.companyScaleName &&
                distributorExtInfoVO.merchantType === 'ENTERPRISE'
              "
            >
              企业规模：{{ distributorExtInfoVO.companyScaleName }}，</span
            >
            <span
              v-if="
                distributorExtInfoVO.registerCapital &&
                distributorExtInfoVO.merchantType === 'ENTERPRISE'
              "
            >
              注册资本{{ distributorExtInfoVO.registerCapital }}万元</span
            >
          </div>
          <div class="info-box-row--call--text--row">
            <span v-if="distributorExtInfoVO.ageGroupList">
              店铺人群： 年龄段：{{ ageGroupList }}；
            </span>
            <span v-if="distributorExtInfoVO.consumerGroupList">
              用户群体：{{ consumerGroupList }}；</span
            >
            <span v-if="distributorExtInfoVO.sex">群体性别：{{ sex }} </span>
          </div>
          <div
            class="info-box-row--call--text--row"
            v-if="
              distributorExtInfoVO.merchantType &&
              distributorExtInfoVO.merchantType === 'ENTERPRISE'
            "
          >
            法人姓名：{{ distributorExtInfoVO.companyLegalName || noTxt }}
          </div>
          <div
            class="info-box-row--call--text--row"
            v-if="
              distributorExtInfoVO.merchantType &&
              distributorExtInfoVO.merchantType === 'ENTERPRISE'
            "
          >
            企业联系方式：
            <!-- {{ distributorExtInfoVO.companyLegalMobile || noTxt }} -->
            <CryptoBlock
              v-if="distributorExtInfoVO.companyLegalMobile"
              class="input"
              :biz-id="distributorExtInfoVO.id"
              detail-auth="/distributor-management/:decrypt-mobile"
              biz-type="DISTRIBUTOR_LEADS_COMPANY_MOBILE"
              v-model.trim="distributorExtInfoVO.companyLegalMobile"
            />
            <span v-if="!distributorExtInfoVO.companyLegalMobile">{{
              noTxt
            }}</span>
          </div>
          <div
            class="info-box-row--call--text--row"
            v-if="distributorExtInfoVO.contactName"
          >
            联系人姓名：{{ distributorExtInfoVO.contactName }}
          </div>
          <div
            class="info-box-row--call--text--row"
            v-if="distributorExtInfoVO.contactPhone"
          >
            线索联系方式：
            <CryptoBlock
              detail-auth="/distributor-management/:decrypt-mobile"
              :bizId="distributorExtInfoVO.id"
              bizType="DISTRIBUTOR_LEADS_CONTACT_PHONE"
              v-model.trim="distributorExtInfoVO.contactPhone"
            />
          </div>
        </div>
        <div v-else>{{ noTxt }}</div>
      </div>
    </div>
    <div class="info-box-row">
      <div class="info-box-row--call">
        <div class="info-box-row--call--title">拓展店铺ID</div>
        <div class="info-box-row--call--text">
          {{ distributorExtInfoVO.platformShopId || noTxt }}
        </div>
      </div>
    </div>
    <div class="info-box-row">
      <div class="info-box-row--call">
        <div class="info-box-row--call--title">拓展关键词</div>
        <div class="info-box-row--call--text">
          {{ distributorExtInfoVO.keyWord || noTxt }}
        </div>
      </div>
    </div>
    <div class="info-box-row">
      <div class="info-box-row--call">
        <div class="info-box-row--call--title">来源渠道</div>
        <div class="info-box-row--call--text">
          {{ channelNameList }}
        </div>
      </div>
    </div>
    <div class="info-box-row">
      <div class="info-box-row--call">
        <div class="info-box-row--call--title">来源店铺</div>
        <div class="info-box-row--call--text">
          <div v-for="(item, inx) of platformShopNameList" :key="inx">
            {{ item }}
          </div>
          <div v-if="platformShopNameList.length === 0">{{ noTxt }}</div>
        </div>
      </div>
    </div>

    <div class="info-box-row">
      <div class="info-box-row--call">
        <div class="info-box-row--call--title">拓展品牌</div>
        <div
          class="info-box-row--call--text"
          v-if="distributorExtInfoVO.favoriteBrandList"
        >
          <span
            style="padding-right: 5px"
            v-for="(item, idx) in distributorExtInfoVO.favoriteBrandList"
            :key="idx"
            >{{ item.typeValueName }}</span
          >
        </div>
        <div v-else>{{ noTxt }}</div>
      </div>
    </div>
    <div class="info-box-row">
      <div class="info-box-row--call">
        <div class="info-box-row--call--title">主营类目</div>
        <div
          class="info-box-row--call--text"
          v-if="distributorExtInfoVO.businessCategoryList"
        >
          <span
            style="padding-right: 5px"
            v-for="(item, idx) in distributorExtInfoVO.businessCategoryList"
            :key="idx"
            >{{ item.typeValueName }}</span
          >
        </div>
        <div v-else>{{ noTxt }}</div>
      </div>
    </div>
    <div class="info-box-row">
      <div class="info-box-row--call">
        <div class="info-box-row--call--title">意向品牌</div>
        <div class="info-box-row--call--text">
          {{ interestedBrandList }}
          <Authority :auth="authorityCode" v-if="!isDetails">
            <el-button
              type="text"
              icon="el-icon-edit-outline"
              class="outline-btn"
              @click="openDialogUpdate('brand')"
            ></el-button>
          </Authority>
        </div>
      </div>
    </div>
    <div class="info-box-row">
      <div class="info-box-row--call">
        <div class="info-box-row--call--title">微信号码</div>
        <div class="info-box-row--call--text">
          {{
            distributorExtInfoVO.wechat ? distributorExtInfoVO.wechat : noTxt
          }}
        </div>
      </div>
    </div>
    <div class="info-box-row">
      <div class="info-box-row--call">
        <div class="info-box-row--call--title">不合作原因</div>
        <div class="info-box-row--call--text">
          {{
            distributorExtInfoVO.rejectReason
              ? distributorExtInfoVO.rejectReason
              : noTxt
          }}
        </div>
      </div>
    </div>
    <div class="info-box-row">
      <div class="info-box-row--call">
        <h3 class="info-box-row--call--h3">客户潜力</h3>
      </div>
    </div>
    <div class="info-box-row">
      <div class="info-box-row--call">
        <div class="info-box-row--call--title">客户潜力</div>
        <div class="info-box-row--call--text">{{ customerTypeList }}</div>
      </div>
    </div>
    <div class="info-box-row">
      <div class="info-box-row--call">
        <div class="info-box-row--call--title">偏好品类</div>
        <div class="info-box-row--call--text">{{ favoriteCategoryList }}</div>
      </div>
    </div>
    <div class="info-box-row">
      <div class="info-box-row--call">
        <div class="info-box-row--call--title">商品偏好</div>
        <div class="info-box-row--call--text">{{ favoriteCommodityList }}</div>
      </div>
    </div>
    <div class="info-box-row">
      <div class="info-box-row--call">
        <div class="info-box-row--call--title">意向程度</div>
        <div class="info-box-row--call--text">
          {{
            distributorExtInfoVO.interestedDegreeName
              ? distributorExtInfoVO.interestedDegreeName
              : noTxt
          }}
          <Authority :auth="authorityCode" v-if="!isDetails">
            <el-button
              type="text"
              icon="el-icon-edit-outline"
              class="outline-btn"
              @click="openDialogUpdate('interestedDegree')"
            ></el-button>
          </Authority>
        </div>
      </div>
    </div>
    <div class="info-box-row">
      <div class="info-box-row--call">
        <div class="info-box-row--call--title">有无仓库</div>
        <div class="info-box-row--call--text">
          {{ distributorExtInfoVO.isWarehouse === '1' ? '有' : '无' }}
        </div>
      </div>
    </div>
    <!-- 弹窗 -->
    <el-dialog
      :title="title"
      :visible.sync="dialogVisible"
      width="800px"
      v-if="dialogVisible"
    >
      <customer-info
        :authorityCode="authorityCode"
        :id="id"
        @closeDialog="dialogVisible = false"
        @onSuccess="() => $emit('onSuccess')"
      />
      <!-- DialogUpdate-->
    </el-dialog>
    <DialogUpdate
      :dialogVisible.sync="dialogUpdateVisible"
      :dialogUpdateType="dialogUpdateType"
      @onSuccess="$emit('onSuccess')"
      :brandInitData="distributorExtInfoVO.interestedBrandList"
      :interestedDegreeValue="distributorExtInfoVO.interestedDegree"
      :id="id"
    />
  </div>
</template>

<script>
// 客户资料
import CustomerInfo from '../customer-Info';
import DialogUpdate from '../DialogUpdate';
export default {
  components: { CustomerInfo, DialogUpdate },
  data() {
    return {
      noTxt: '/',
      title: '完善客户资料',
      dialogVisible: false,
      dialogUpdateVisible: false,
      dialogUpdateType: 'brand'
    };
  },
  props: {
    authorityCode: String, // 按钮权限编码
    id: String,
    distributorExtInfoVO: Object,
    isDetails: Boolean,
    isEXPAND: Boolean // 是否是拓展客户
  },
  computed: {
    interestedBrandList() {
      return this.mapFilters(this.distributorExtInfoVO.interestedBrandList);
    },
    favoritePolicyList() {
      return this.mapFilters(this.distributorExtInfoVO.favoritePolicyList);
    },
    favoriteBrandList() {
      return this.mapFilters(this.distributorExtInfoVO.favoriteBrandList);
    },
    ageGroupList() {
      return this.mapFilters(
        this.distributorExtInfoVO.ageGroupList,
        'typeValueName',
        '-'
      );
    },
    consumerGroupList() {
      return this.mapFilters(this.distributorExtInfoVO.consumerGroupList);
    },
    sex() {
      return this.mapFilters(
        this.distributorExtInfoVO.sex,
        'typeValueName',
        '+'
      );
    },
    channelNameList() {
      return this.mapFilters(
        this.distributorExtInfoVO.distributorPlatformShopVOList,
        'channelName'
      );
    },
    platformShopNameList() {
      const arr = [];
      if (
        this.distributorExtInfoVO.distributorPlatformShopVOList &&
        Array.isArray(this.distributorExtInfoVO.distributorPlatformShopVOList)
      ) {
        this.distributorExtInfoVO.distributorPlatformShopVOList.forEach(
          (element) => {
            arr.push(
              `${element.channelName ? element.channelName + ': ' : ''}${
                element.platformShopName
              } ${element.platformShopLevel ? element.platformShopLevel : ''}`
            );
          }
        );
      }

      return arr;
    },
    customerTypeList() {
      return this.mapFilters(this.distributorExtInfoVO.customerTypeList);
    },
    favoriteCategoryList() {
      return this.mapFilters(this.distributorExtInfoVO.favoriteCategoryList);
    },
    favoriteCommodityList() {
      return this.mapFilters(this.distributorExtInfoVO.favoriteCommodityList);
    }
  },
  methods: {
    mapFilters(arr = [], key = 'typeValueName', str = '、') {
      const a = arr.map((item) => item[key]).join(str);
      return a && a.length > 0 ? a : this.noTxt;
    },
    editCustomerInfo() {
      this.title = '完善客户资料';
      this.dialogVisible = true;
    },
    openDialogUpdate(type) {
      this.dialogUpdateType = type;
      this.dialogUpdateVisible = true;
    }
  }
};
</script>

<style lang="scss" scoped>
@import './style';
.addbtn {
  margin-left: 10px;
}
.outline-btn {
  padding: 0;
}
</style>
