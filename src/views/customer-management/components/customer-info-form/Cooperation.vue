<template>
  <div class="info-box">
    <h3 class="info-box-title">合作信息</h3>
    <div class="info-box-row">
      <div class="info-box-row--call">
        <div class="info-box-row--call--title">店铺名称</div>
        <div class="info-box-row--call--text">
          {{ distributorBodyVO.shopName }}
        </div>
      </div>
      <div class="info-box-row--call">
        <div class="info-box-row--call--title">经营渠道</div>
        <div class="info-box-row--call--text">
          {{ channelInfoVOList }}
        </div>
      </div>
    </div>
    <div class="info-box-row">
      <div class="info-box-row--call">
        <div class="info-box-row--call--title">注册账号</div>
        <div class="info-box-row--call--text">
          <CryptoBlock
            detail-auth="/distributor-management/:decrypt-mobile"
            :bizId="distributorBodyVO.id"
            bizType="DISTRIBUTOR_MOBILE"
            v-model.trim="distributorBodyVO.mobile"
          />
        </div>
      </div>
      <div class="info-box-row--call">
        <div class="info-box-row--call--title">店铺月销量</div>
        <div class="info-box-row--call--text">
          {{ distributorBodyVO.monthlySalesName }}
        </div>
      </div>
    </div>
    <div class="info-box-row">
      <div class="info-box-row--call">
        <div class="info-box-row--call--title">真实姓名</div>
        <div class="info-box-row--call--text">
          {{ distributorBodyVO.company }}
        </div>
      </div>
      <div class="info-box-row--call">
        <div class="info-box-row--call--title">商家类型</div>
        <div class="info-box-row--call--text">
          {{ distributorBodyVO.merchantTypeName }}
        </div>
      </div>
    </div>
    <div class="info-box-row">
      <div class="info-box-row--call">
        <div class="info-box-row--call--title">分销商联系方式</div>
        <div class="info-box-row--call--text">
          <CryptoBlock
            detail-auth="/distributor-management/:decrypt-mobile"
            :bizId="distributorBodyVO.id"
            bizType="DISTRIBUTOR_CONTACT_PHONE"
            v-model.trim="distributorBodyVO.contactPhone"
          />
        </div>
      </div>
    </div>
    <div class="info-box-row">
      <div class="info-box-row--call">
        <div class="info-box-row--call--title">邮箱地址</div>
        <div class="info-box-row--call--text">
          {{ distributorBodyVO.email }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// 合作信息
export default {
  props: {
    distributorBodyVO: Object,
    distributorExtInfoVO: Object
  },
  computed: {
    channelInfoVOList() {
      const channelInfoVOList = this.distributorBodyVO.channelInfoVOList.map(
        (item) => item.channelName
      );
      return channelInfoVOList.join('、');
    }
  }
};
</script>

<style lang="scss" scoped>
@import './style';
</style>
