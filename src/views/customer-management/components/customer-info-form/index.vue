<template>
  <div>
    <Cooperation v-bind="$attrs" v-on="$listeners" v-if="!isEXPAND" />
    <CustomerData
      :authorityCode="authorityCode"
      v-bind="$attrs"
      v-on="$listeners"
      :isEXPAND="isEXPAND"
    />
    <WhiteList v-bind="$attrs" v-on="$listeners" v-if="!isEXPAND" />
    <CriminalRecords
      :authorityCode="authorityCode"
      v-bind="$attrs"
      v-on="$listeners"
      v-if="!isEXPAND"
    />
  </div>
</template>

<script>
// 分销商信息
import Cooperation from './Cooperation'; // 合作信息
import CustomerData from './CustomerData'; // 客户资料
import WhiteList from './WhiteList'; // 白名单
import CriminalRecords from './CriminalRecords'; // 违规记录

export default {
  components: { Cooperation, CustomerData, WhiteList, CriminalRecords },
  props: {
    authorityCode: String, // 按钮权限编码
    isEXPAND: Boolean // 是否是拓展客户
  },
  methods: {}
};
</script>

<style lang="scss" scoped>
</style>
