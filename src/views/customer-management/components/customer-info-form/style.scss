.info-box {
  padding-bottom: 10px;
  &-title {
    border-left: 4px solid var(--color-primary);
    padding-left: 10px;
    font-size: 18px;
    display: flex;
    align-items: center;
    &--leftbox {
      flex: 1;
    }
  }
  &-row {
    display: flex;
    align-items: flex-end;
    margin-bottom: 15px;
    &--call {
      flex: 1;
      display: flex;
      align-items: flex-end;
      &--title {
        color: #333;
        font-size: 16px;
        margin-right: 20px;
        min-width: 80px;
      }
      &--text {
        color: #666;
      }
      &--h3 {
        font-size: 17px;
        margin-bottom: 5px;
      }
    }
  }
}
