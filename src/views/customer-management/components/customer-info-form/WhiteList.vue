<template>
  <div class="info-box">
    <h3 class="info-box-title">店铺白名单信息（在第三方平台开店店铺信息）</h3>
    <el-table :data="whitelistBriefVOList" style="width: 100%">
      <el-table-column prop="platformName" label="平台名称" width="180">
      </el-table-column>
      <el-table-column prop="platformShopName" label="店铺名称" width="180">
      </el-table-column>
      <el-table-column prop="platformShopId" label="店铺ID"> </el-table-column>
      <el-table-column prop="shopWebsite" label="店铺网址"> </el-table-column>
      <el-table-column prop="violateNum" label="违规总次数"> </el-table-column>
    </el-table>
  </div>
</template>

<script>
// 店铺白名单信息
export default {
  data() {
    return {};
  },
  props: {
    whitelistBriefVOList: {
      type: Array,
      default: () => []
    }
  }
};
</script>

<style lang="scss" scoped>
@import './style';
</style>
