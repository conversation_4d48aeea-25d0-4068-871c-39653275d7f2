import Vue from 'vue';
import dict from '@/components/Common/dicts';
import { parseTime } from '@/utils';
import { checkGroup } from '@/components/Common/SelectGroup';
import { todoPlanExportLeadsManager } from '@/api/customer-management/follow-up/index.js';
import cloneDeep from 'lodash/cloneDeep'; // 对象深拷贝
import download from '@/utils/download';

// 权限配置
export const Authority = {
  edit: '/customer-management/follow-up-record/list:edit',
  export: '/customer-management/follow-up-record/list:export'
};
export const ExportOptions = {
  auth: Authority.export,
  name: '导出',
  request(data) {
    return new Promise((resolve) => {
      todoPlanExportLeadsManager(data)
        .then((res) => {
          // 流文件 文件流
          try {
            const resObj = JSON.parse(new TextDecoder('utf-8').decode(new Uint8Array(res)));
            if (resObj.success) {
              this.$message.success(resObj.msg);
            } else {
              this.$message.error(resObj.msg || '系统错误');
            }
          } catch (error) {
            // 获取指标名称
            download(res, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', `跟进记录-${parseTime(Date.now(), '{y}-{m}-{d}-{h}:{i}:{s}')}.xlsx`);
          }
        })
        .finally(resolve);
    });
  }
};
export const CLUE_FOLLOWRESULTS = {
  CONTACT: 'syzg_todo_contact_follow_result', // 触达
  ADD_WECHAT: 'syzg_todo_addwechat_follow_result', // 加微
  REGISTER: 'syzg_todo_register_follow_result', // 注册
  GUIDE_PURCHASE: 'syzg_todo_guidepurchase_follow_result', // 新客
  CREDIT_BACK: 'syzg_todo_creditback_follow_result', // 返点
  LICENSE: 'syzg_todo_license_follow_result', // 授权
  POLICY: 'syzg_todo_policy_follow_result', // 政策
  HOLIDAY: 'syzg_todo_holiday_follow_result', // 节假日
  AFTER_SALE: 'syzg_todo_aftersale_follow_result', // 售后
  WAKE_UP: 'syzg_todo_wakeup_follow_result', // 老客
  AI_INTENTION_AUTO_REACH: 'syzg_todo_ai_follow_result', // AI意向-自动触达
  AI_INTENTION_CUSTOMER_REACH: 'syzg_todo_silent_follow_result', // AI意向-客户跟进
  SILENT_ACTIVATION: 'syzg_todo_silent_follow_result' // 沉寂激活
};

const followResultOptions = [];
export const FilterFormOptions = [
  {
    component: 'select',
    label: '跟进类型',
    prop: 'followType',
    options: Vue.prototype.$dict['syzg_todo_follow_type']
  },
  {
    component: 'input',
    label: '拓展店铺',
    prop: 'platformShopName'
  },
  {
    component: 'input',
    label: '店铺名称',
    prop: 'shopName'
  },
  {
    prop: 'queryDistributorIds',
    label: '分销商ID',
    component: 'input',
    placeholder: '请输入分销商ID，支持逗号分隔查询多条'
  },
  {
    prop: 'daterangeTime',
    label: '跟进时间',
    component: 'dateRange',
    type: 'daterange',
    props: ['finishBeginDate', 'finishEndDate']
  },
  {
    component: 'select',
    label: '跟进事项',
    prop: 'label',
    change(value) {
      const options = this.$dict[CLUE_FOLLOWRESULTS[value]] ?? [];
      followResultOptions.splice(0, followResultOptions.length, ...cloneDeep(options));
      this.setFormItemVal('followResult', '');
    },
    options: Vue.prototype.$dict['syzg_todo_follow_label']
  },
  {
    component: 'select',
    label: '跟进结果',
    prop: 'followResult',
    placeholder: '需先选择跟进事项',
    options: followResultOptions
  },
  {
    component: 'select',
    label: '跟进方式',
    prop: 'followWay',
    options: Vue.prototype.$dict['syzg_todo_follow_way']
  },
  {
    component: 'select',
    label: '跟进人',
    prop: 'csIds',
    options: dict('COMMON_EXPAND_ADVISER'),
    multiple: true
  },
  {
    component: 'select',
    label: '跟进团队',
    prop: 'groupId',
    visible() {
      return !checkGroup(this.userGroup);
    },
    options: dict('COMMON_SHOPTAFFGROUP_LIST')
  },
  {
    component: 'select',
    label: '任务场景',
    prop: 'taskCategory',
    options: []
  },
  {
    component: 'input',
    label: '任务名称',
    prop: 'taskTitle'
  }
];

// 列表展示内容
export const TableOptions = [
  {
    prop: 'expectedFinishDate',
    label: '跟进时间',
    width: '100',
    isCustom: true
  },
  {
    prop: 'groupName',
    label: '团队',
    formatter: (r, c, v = '--') => v
  },
  {
    prop: 'followTypeName',
    label: '跟进类型'
  },
  {
    prop: 'labelName',
    label: '跟进事项'
  },
  {
    prop: 'followWayName',
    label: '跟进方式'
  },
  {
    prop: 'platformShopName',
    label: '拓展店铺'
  },
  {
    prop: 'shopName',
    label: '店铺名称'
  },
  {
    prop: 'distributorId',
    label: '分销商ID'
  },
  {
    prop: 'taskCategoryName',
    label: '任务场景',
    formatter: (r, c, v = '--') => v
  },
  {
    prop: 'taskTitle',
    label: '任务名称',
    formatter: (r, c, v = '--') => v
  },
  {
    prop: 'taskDetailCreateDate',
    label: '任务日期',
    width: '90',
    isCustom: true
  },
  {
    prop: 'createByName',
    label: '跟进人'
  },
  {
    prop: 'followResultName',
    label: '跟进结果'
  },
  {
    prop: 'todoEvent',
    label: '跟进说明',
    isCustom: true
  }
];

// 列表操作
export const TableBarOptions = [
  {
    id: 'editFollowUpRecord',
    label: '编辑',
    authority: Authority.edit,
    isWrap: true,
    loadCondition: ({ label }) => label !== 'AI_INTENTION_AUTO_REACH',
    button: {
      type: 'text'
    }
  }
];

export const EditRecordDialogOptions = [
  {
    prop: 'expectedFinishDate',
    label: '跟进日期',
    formatter: (v) => (v ? parseTime(v, '{y}-{m}-{d}') : '--'),
    component: 'text'
  },
  {
    prop: 'labelName',
    label: '跟进事项',
    component: 'text'
  },
  {
    prop: 'followWayName',
    label: '跟进方式',
    component: 'text'
  },
  {
    prop: 'followResultName',
    label: '跟进结果',
    component: 'text'
  },
  {
    prop: 'todoEvent',
    label: '跟进说明',
    rules: [
      { required: true, message: '请输入跟进说明', trigger: 'blur' },
      { min: 5, message: '最少5个字，最多500字', trigger: 'blur' }
    ]
  },
  {
    prop: 'picUrls',
    label: '上传图片',
    component: 'upload',
    limit: 10,
    multiple: true,
    accept: '.jpg,.jpeg,.png',
    _size: { company: 'KB', size: 5120 },
    _tip: '最多可上传10张图片，每张大小控制在5M以内'
  }
];
