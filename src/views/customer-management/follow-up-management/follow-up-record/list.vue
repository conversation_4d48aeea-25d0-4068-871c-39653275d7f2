<template>
  <div class="table-container">
    <filter-form :options="_filterFormOptions" @query="query" @reset="reset" ref="filterForm" :exportOptions="_exportOptions"></filter-form>
    <table-exhibition @query="query" :options="_tableOptions" :table="table" ref="table" v-loading="loading" style="margin-top: 20px" v-el-horizontal-scroll>
      <!-- 时间显示 -->
      <template v-for="i of ['expectedFinishDate']" :slot="`column_${i}`" slot-scope="{ index, ...option }">
        <el-table-column :key="i + index" v-bind="option">
          <template slot-scope="{ row = {} }">
            <div>
              {{ $options.filters.parseTime(row[i], '{y}-{m}-{d}') || '--' }}
            </div>
            <div>
              {{ row[i] | parseTime('{h}:{i}:{s}') }}
            </div>
          </template>
        </el-table-column>
      </template>
      <el-table-column slot="column_todoEvent" slot-scope="{ index, ...option }" v-bind="option">
        <template slot-scope="{ row }">
          <el-tooltip popper-class="tableExhibition-tooltip" :content="row.todoEvent" placement="top">
            <p class="commo-ellipsis-2">{{ row.todoEvent }}</p>
          </el-tooltip>
          <el-button v-if="row.picUrls && row.picUrls.length" type="text" @click="viewImages(row)">查看图片</el-button>
        </template>
      </el-table-column>
      <el-table-column slot="column_taskDetailCreateDate" slot-scope="{ index, ...option }" v-bind="option">
        <template slot-scope="{ row }">
          <span v-if="row.taskDetailCreateDate">{{ row.taskDetailCreateDate | parseTime('{y}-{m}-{d} {h}:{i}:{s}') }}</span>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="100" slot="column_append">
        <template slot-scope="scope">
          <action-bar :itemOptions="_tableBarOptions" :record="scope.row" @actionBarClick="({ id }, row) => _self[id](row)"></action-bar>
        </template>
      </el-table-column>
    </table-exhibition>
    <el-dialog title="编辑跟进记录" :visible.sync="editRecord_dialogVisible" width="800px" v-loading="editRecord_dialogLoading">
      <submit-form :options="editRecordDialogOptions" label-width="100px" @onCancel="editRecord_dialogVisible = false" @onSubmit="editRecordOnSubmit" ref="editRecordDialog">
        <template v-slot:formItem_todoEvent="{ form }">
          <div v-if="todoEventTips" class="color-info">{{ todoEventTips }}</div>
          <el-input
            style="width: 500px"
            v-model.trim="form.todoEvent"
            :placeholder="todoEventPlaceholder"
            type="textarea"
            maxlength="500"
            show-word-limit
            :autosize="{
              minRows: 6,
              maxRows: 12
            }"
          ></el-input>
        </template>
      </submit-form>
    </el-dialog>
  </div>
</template>

<script>
import FilterForm from '@/components/Form/FilterForm';
import SubmitForm from '@/components/Form/SubmitForm';
import ActionBar from '@/components/Common/ActionBar';
import TableExhibition from '@/components/Table/TableExhibition';
import { FilterFormOptions, TableOptions, TableBarOptions, EditRecordDialogOptions, ExportOptions } from './config';
import { getListLeadsManager, updateLeadsManager } from '@/api/customer-management/clue.js';
import { mapGetters } from 'vuex';
import { TASK_FOLLOW_UP_DESC, TASK_FOLLOW_UP_TIPS } from '@/constants';

export default {
  name: 'customer-management-follow-up-management-follow-up-record-list',
  components: { FilterForm, SubmitForm, ActionBar, TableExhibition },
  data() {
    return {
      loading: false,
      table: {},
      itemImages: [],
      editRecord_dialogVisible: false,
      editRecord_dialogLoading: false,
      editRecordDialogOptions: [],
      todoEventPlaceholder: '最少5个字，最多500字（精简总结跟客户沟通的结论和注意点）',
      todoEventTips: ''
    };
  },
  computed: {
    ...mapGetters(['userGroup'])
  },
  created() {
    this.init();
  },
  mounted() {
    this.params = {
      data: this.$refs.filterForm.getParams(),
      ...this.$refs.table.getParams()
    };
  },
  activated() {
    this.query();
  },
  methods: {
    init() {
      this.editRecordDialogOptions = EditRecordDialogOptions;
      this._filterFormOptions = FilterFormOptions;
      this._tableOptions = TableOptions;
      this._tableBarOptions = TableBarOptions;
      this._exportOptions = ExportOptions;

      const taskCategory = FilterFormOptions.find(({ prop }) => prop === 'taskCategory');
      taskCategory.options = this.$dict[this.userGroup === '3' ? 'sygj_task_category' : 'syzg_task_category'];
    },
    query(params = {}) {
      Object.assign(this.params, params);
      // 分销商ID查询支持输入多个
      const ids = this.params.data.queryDistributorIds;
      if (ids && ids.includes(',')) {
        this.params.data.queryDistributorIds = ids.split(',').filter((i) => !!i);
      }
      this.loading = true;
      getListLeadsManager(this.params)
        .then((res) => {
          this.table = res.data;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    reset() {
      const formItem_followResult = this.$refs.filterForm.$refs.formItem_followResult?.[0];
      if (formItem_followResult) {
        this.$nextTick(() => {
          formItem_followResult.clearOptions();
        });
      }
    },
    async editFollowUpRecord(row) {
      this.editRecord_dialogVisible = this.editRecord_dialogLoading = true;
      // 分组下拉选择值重新获取
      // const options = await organization_tree({});
      this.editRecord_dialogLoading = false;
      // 弹框表单重新赋值
      if (!this.$refs.editRecordDialog) await this.$nextTick();
      this.$refs.editRecordDialog.setData({
        ...row
      });
      this.initTips(row);
    },
    initTips({ followResult }) {
      this.todoEventTips = TASK_FOLLOW_UP_TIPS[followResult] || '';
      this.todoEventPlaceholder = TASK_FOLLOW_UP_DESC[followResult] || '最少5个字，最多500字（精简总结跟客户沟通的结论和注意点）';
    },
    editRecordOnSubmit({ form, callback }) {
      updateLeadsManager(form)
        .then(() => {
          this.query();
          this.$message.success('操作成功');
          this.editRecord_dialogVisible = false;
        })
        .finally(callback);
    },
    // 查看图片
    viewImages({ picUrls = [] }) {
      this.$viewerApi({
        images: picUrls
      });
    }
  }
};
</script>

<style lang="scss" scoped></style>
