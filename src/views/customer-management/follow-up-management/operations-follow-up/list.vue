<template>
  <!-- 跟进管理代发列表 -->
  <div class="app-container">
    <div class="table-container">
      <el-tabs v-model="activeName" type="card">
         <el-tab-pane label="全部" name='0'> 
          <PageList ref="listItemW" :type="type" />
        </el-tab-pane>
        <el-tab-pane label="采销分销商数据" name="1">
          <PageList ref="listItemO" :type="type" />
        </el-tab-pane>
        <el-tab-pane label="一件代发分销商数据" name="2">
          <PageList ref="listItemT" :type="type" />
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>
<script>
import PageList from '@/views/customer-management/components/page-list'; 
export default {
  name: 'client-list',
  components: {
    PageList
  },
  data() {
    return {
      activeName: '0',
      list: [],
      // 列表客户类型 'PURCHASE':采销客户 'DROP_SHIPPING':一件代发客户
      type: ''
    };
  },
  watch: {
    activeName(val) {
      if (val === '0') {
        this.type = '';
        this.$nextTick(() => {
          this.$refs.listItemW.init();
        });
      } else if (val === '1') {
        this.type = 'PURCHASE';
        this.$nextTick(() => {
          this.$refs.listItemO.init();
        });
      } else if (val === '2') {
        this.type = 'DROP_SHIPPING';
        this.$nextTick(() => {
          this.$refs.listItemT.init();
        });
      }
    }
  },
  activated() {
    this.getInit();
  },
  methods: {
    // 权限
    getInit() {
      this.$nextTick(() => {
        this.$refs.listItemW.init();
      });
    }
  }
};
</script>

<style lang="scss" scoped>
@import '../../../../styles/filter-list.scss';
.data-overview {
  .title {
    display: flex;
    align-items: center;
    .update-time {
      margin-left: 15px;
      color: #999;
    }
  }
  .distributor-data {
    border: 1px solid #ccc;
    margin-bottom: 20px;
  }
}
.search-session {
  padding: 20px 0;
}
</style>
