<template>
  <!-- 分销商跟进记录 -->
  <div class="app-container">
    <div class="table-container">
      <h3>分销商跟进记录</h3>
      <public-info
        :isDetails="isDetails"
        v-if="distributorHeaderVO"
        :distributorBodyVO="distributorHeaderVO"
        :distributorHeaderVO="distributorHeaderVO"
        :id="id"
        @onSuccess="getDistributorBasicInfoVO()"
      />
      <div class="tabs-container">
        <el-tabs v-model="activeName" type="card">
          <el-tab-pane label="跟进记录" name="FINISH">
            <FollowTable
              type="FINISH"
              :id="id"
              :CLUEFOLLOWUPTYPE="CLUEFOLLOWUPTYPE"
              :isShowAddBtn="!isDetails"
              v-if="activeName === 'FINISH'"
            />
          </el-tab-pane>
          <!-- <el-tab-pane label="待办事项" name="WAIT">
            <FollowTable
              type="WAIT"
              :id="id"
              :isShowAddBtn="!isDetails"
              v-if="activeName === 'WAIT'"
            />
          </el-tab-pane> -->
        </el-tabs>
      </div>
    </div>
  </div>
</template>
<script>
import PublicInfo from '@/views/customer-management/components/public-info';
import { getDistributorBasicInfoVO } from '@/api/customer-management/info';
import FollowTable from '@/views/customer-management/components/FollowTable';
export default {
  name: 'follow-recode',
  components: {
    PublicInfo,
    FollowTable
  },
  props: {
    // 是否是详情，默认是false
    isDetails: {
      type: Boolean,
      default: false
    },
    CLUEFOLLOWUPTYPE: {
      type: String,
      default: 'distributor'
    }
  },
  data() {
    return {
      activeName: 'FINISH',
      id: this.$route.params.id,
      distributorHeaderVO: null // 分销商头部信息
    };
  },
  methods: {
    onSearch() {},
    handleCurrentChange() {},
    handleSizeChange() {},
    // 获取客户资料详情：分销商信息,合作信息，客户资料,基础信息
    getDistributorBasicInfoVO() {
      this.listLoading = true;
      getDistributorBasicInfoVO(this.id)
        .then((response) => {
          this.distributorHeaderVO = response.data;
        })
        .finally(() => {
          this.listLoading = false;
        });
    }
  },
  activated() {
    this.getDistributorBasicInfoVO();
  }
};
</script>

<style lang="scss" scoped>
@import '../../../../styles/filter-list.scss';
.data-overview {
  .title {
    display: flex;
    align-items: center;
    .update-time {
      margin-left: 15px;
      color: #999;
    }
  }
  .distributor-data {
    border: 1px solid #ccc;
  }
}
.search-session {
  padding: 20px 0;
}
</style>
