<template>
  <!-- 拓展客户跟进记录 -->
  <div class="app-container">
    <div class="table-container">
      <h3>客户跟进记录</h3>
      <public-info-temporary
        v-if="distributorHeaderVO"
        :distributorBodyVO="distributorHeaderVO"
        :distributorHeaderVO="distributorHeaderVO"
        :id="id"
        banEditInfo
        authorityCode="/customer-management/expand-follow-up/list:edit"
        @onSuccess="getDetail()"
      />
      <div class="tabs-container">
        <el-tabs v-model="activeName" type="card">
          <el-tab-pane label="跟进记录" name="FINISH">
            <FollowTable
              type="FINISH"
              :id="id"
              CLUEFOLLOWUPTYPE='develop'
              v-if="activeName === 'FINISH'"
            />
          </el-tab-pane>
          <!-- <el-tab-pane label="待办事项" name="WAIT">
            <FollowTable type="WAIT" :id="id" v-if="activeName === 'WAIT'" />
          </el-tab-pane> -->
        </el-tabs>
      </div>
    </div>
  </div>
</template>
<script>
import PublicInfoTemporary from '@/views/customer-management/components/public-info-temporary';
import { getDetail } from '@/api/distributorManagement/info';
import FollowTable from '@/views/customer-management/components/FollowTable';
export default {
  name: 'follow-recode',
  components: {
    PublicInfoTemporary,
    FollowTable
  },
  data() {
    return {
      activeName: 'FINISH',
      id: this.$route.params.id,
      distributorHeaderVO: null // 拓展客户头部信息
    };
  },
  methods: {
    onSearch() {},
    handleCurrentChange() {},
    handleSizeChange() {},
    // 运营端-客户管理-拓展跟进-分销商跟进记录-分销商基本信息
    getDetail() {
      this.listLoading = true;
      getDetail(this.id)
        .then((response) => {
          const { header, distributorExtInfoVO } = response.data;
          this.distributorExtInfoVO = distributorExtInfoVO;
          this.distributorHeaderVO = header;
        })
        .finally(() => {
          this.listLoading = false;
        });
    }
  },
  activated() {
    this.getDetail();
  }
};
</script>

<style lang="scss" scoped>
.data-overview {
  .title {
    display: flex;
    align-items: center;
    .update-time {
      margin-left: 15px;
      color: #999;
    }
  }
  .distributor-data {
    border: 1px solid #ccc;
  }
}
.search-session {
  padding: 20px 0;
}
</style>
