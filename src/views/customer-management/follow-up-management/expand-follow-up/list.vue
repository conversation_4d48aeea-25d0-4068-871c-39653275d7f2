<template>
  <div class="app-container">
    <div class="table-container">
      <div class="distributor-data">
        <GeneralSituation :list="infoList" v-loading="infoListLoading"></GeneralSituation>
      </div>
      <form @submit.prevent="onSearch" class="commo-search-container commo-search-flex commo-search-margin-bottom10">
        <div class="commo-search-item">
          <span class="commo-search-item-label">客户名称:</span>
          <div class="commo-search-item-content">
            <el-input clearable v-model="filter.name"></el-input>
          </div>
        </div>
        <div class="commo-search-item">
          <span class="commo-search-item-label">手机号码:</span>
          <div class="commo-search-item-content">
            <el-input clearable v-model="filter.mobile"></el-input>
          </div>
        </div>
        <div class="commo-search-item">
          <span class="commo-search-item-label">拓展顾问:</span>
          <div class="commo-search-item-content">
            <SelectCustomerService multiple v-model="filter.developCsIds" />
          </div>
        </div>
        <div class="commo-search-item">
          <span class="commo-search-item-label">意向程度:</span>
          <div class="commo-search-item-content">
            <el-select clearable filterable multiple placeholder="请选择" v-model="filter.interestedDegreeList">
              <el-option :key="category.value" :label="category.label" :value="category.value" v-for="category in interestedDegreeList"></el-option>
            </el-select>
          </div>
        </div>
        <div class="commo-search-item">
          <span class="commo-search-item-label">加好友时间:</span>
          <div class="commo-search-item-content">
            <el-date-picker v-model="addFriendDate" type="datetimerange" align="right" start-placeholder="开始日期" end-placeholder="结束日期" :default-time="['00:00:00', '23:59:59']"></el-date-picker>
          </div>
        </div>
        <div class="commo-search-item">
          <span class="commo-search-item-label">跟进状态:</span>
          <div class="commo-search-item-content">
            <el-select clearable filterable placeholder="请选择" v-model="filter.todoStatus">
              <el-option v-for="item in todoStatusList" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </div>
        </div>
        <div class="commo-search-item">
          <span class="commo-search-item-label">未跟进天数:</span>
          <div class="commo-search-item-content">
            <el-select clearable filterable placeholder="请选择" v-model="filter.unTodoDays">
              <el-option v-for="item in unTodoDaysList" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </div>
        </div>
        <div class="commo-search-item">
          <span class="commo-search-item-label">最近跟进时间:</span>
          <div class="commo-search-item-content">
            <el-date-picker v-model="lastTodoPlanDate" type="datetimerange" align="right" start-placeholder="开始日期" end-placeholder="结束日期" :default-time="['00:00:00', '23:59:59']"></el-date-picker>
          </div>
        </div>
        <div class="commo-search-btns">
          <el-button native-type="submit" size="small" type="primary">查询</el-button>
          <el-button @click="onReset" size="small">重置</el-button>
          <Authority auth="/customer-management/expand-follow-up/list:export">
            <el-button :loading="exportLoading" @click="onExport" size="small">导出</el-button>
          </Authority>
        </div>
      </form>
      <el-table :data="list" element-loading-text="加载中" fit highlight-current-row ref="multipleTable" v-loading.body="listLoading">
        <el-table-column align="center" label="客户名称" prop="name"></el-table-column>
        <el-table-column align="center" label="手机号码" prop="mobile"></el-table-column>
        <el-table-column align="center" label="拓展顾问" prop="developCsName"></el-table-column>
        <el-table-column align="center" label="加好友时间">
          <template slot-scope="scope">
            <p v-if="scope.row.addFriendDate === '/'">/</p>
            <p v-else>{{ scope.row.addFriendDate | parseTime }}</p>
          </template>
        </el-table-column>
        <el-table-column align="center" label="意向程度" prop="interestedDegreeName"></el-table-column>
        <el-table-column align="center" label="最近跟进时间">
          <template slot-scope="scope">
            <p v-if="scope.row.lastTodoPlanDate === '/'">/</p>
            <p v-else>{{ scope.row.lastTodoPlanDate | parseTime }}</p>
          </template>
        </el-table-column>
        <el-table-column align="center" label="跟进总次数" prop="totalTodoPlanCount"></el-table-column>
        <el-table-column align="center" label="超时跟进次数" prop="unFinishTodoPlanCount"></el-table-column>
        <el-table-column align="center" label="下次跟进时间">
          <template slot-scope="scope">
            <p v-if="scope.row.nextTodoPlanDate === '/'">/</p>
            <p v-else>
              {{ scope.row.nextTodoPlanDate | parseTime('{y}-{m}-{d}') }}
            </p>
          </template>
        </el-table-column>
        <el-table-column prop="address" label="操作" align="center">
          <template slot-scope="scope">
            <Authority auth="/customer-management/follow-up/:follow-up">
              <!-- <followUpDialog
                btnTitle="快速跟进"
                btnType="text"
                :id="scope.row.id"
              /> -->
              <el-button
                type="text"
                @click="
                  $refs.dialog_followUp.setVisible(true, {
                    ...scope.row,
                    CLUEFOLLOWUPTYPE: 'develop'
                  })
                "
                >快速跟进</el-button
              >
            </Authority>
            <div>
              <router-link :to="`/follow-up-management/expand-follow-up/recode/${scope.row.id}`">
                <el-button type="text">跟进记录</el-button>
              </router-link>
            </div>
            <router-link :to="`/customer-management/expand-customer-management/info/${scope.row.id}`">
              <el-button type="text">查看信息</el-button>
            </router-link>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination :current-page="pageNo" :disabled="listLoading" :page-size="pageSize" :page-sizes="[10, 20, 30, 40, 50, 100]" :total="total" @current-change="handleCurrentChange" @size-change="handleSizeChange" background layout="total, sizes, prev, pager, next, jumper"></el-pagination>
      </div>
      <Dialog :callback="onSubmitFollowUp" class="dialog" type="CLUE_FOLLOWUP" ref="dialog_followUp"> </Dialog>
    </div>
  </div>
</template>

<script>
import GeneralSituation from '@/components/views/customer-management/GeneralSituation';
import { developStatistic, listDevelopDistributorPage, exportDevelopDistributorPage } from '@/api/customer-management/follow-up';
import { fechDictData } from '@/api/dict';
import pickBy from 'lodash/pickBy'; // 返回一个新对象，值由真值组成
import cloneDeep from 'lodash/cloneDeep'; // 对象深拷贝
import download from '@/utils/download';
import { parseTime } from '@/utils';
import SelectCustomerService from '@/components/ListSearch/SelectCustomerService';
// import followUpDialog from '@/views/customer-management/components/follow-up-dialog';
import Dialog from '@/components/Dialog/index.vue';
import { createLeadsManagerInBatch } from '@/api/customer-management/info';

export default {
  name: 'expand-follow-up',
  data() {
    const initFilter = {
      name: '', // 客户名称
      mobile: '', // 手机号码
      developCsIds: [], // 拓展顾问ids
      interestedDegreeList: [], // 意向程度
      todoStatus: '', // 跟进状态
      unTodoDays: '', // 未跟进天数
      categoryIds: [] // 品类名称
    };
    return {
      infoList: [],
      infoListLoading: false,
      listLoading: true,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      initFilter, // 搜索条件初始值加入到Data
      filter: cloneDeep(initFilter), // 搜索条件的值
      list: [], // 列表
      exportLoading: false,
      defaultDate: ['00:00:00', '23:59:59'], // 默认时间段
      categories: [], // 分组
      todoStatusList: [], // 跟进状态
      interestedDegreeList: [],
      unTodoDaysList: [
        { label: '近3天', value: '3' },
        { label: '近5天', value: '5' },
        { label: '近12天', value: '12' },
        { label: '近15天', value: '15' }
      ], // 未跟进天数list
      addFriendDate: [], // 加好友时间
      lastTodoPlanDate: [] // 最近跟进时间
    };
  },
  components: {
    GeneralSituation,
    SelectCustomerService,
    // followUpDialog,
    Dialog
  },
  watch: {
    'filter.unTodoDays'(value) {
      if (value) {
        this.lastTodoPlanDate = [];
      }
    },
    lastTodoPlanDate(value) {
      if (value.length > 0) {
        this.filter.unTodoDays = '';
      }
    }
  },
  computed: {
    // 表单接口传入的参数
    interfaceData() {
      const listQuery = pickBy(this.filter, (val) => !!val);
      if (Array.isArray(this.addFriendDate) && this.addFriendDate.length > 0) {
        // 加好友时间
        const [addFriendBeginDate, addFriendEndDate] = this.addFriendDate;
        listQuery.addFriendBeginDate = addFriendBeginDate.getTime();
        listQuery.addFriendEndDate = addFriendEndDate.getTime();
      }
      if (Array.isArray(this.lastTodoPlanDate) && this.lastTodoPlanDate.length > 0) {
        // 最近跟进时间
        const [lastTodoPlanBeginDate, lastTodoPlanEndDate] = this.lastTodoPlanDate;
        listQuery.lastTodoPlanBeginDate = lastTodoPlanBeginDate.getTime();
        listQuery.lastTodoPlanEndDate = lastTodoPlanEndDate.getTime();
      }
      return listQuery;
    }
  },
  activated() {
    // 页面加载事 自动运行
    this.fetchData();
    this.fechTodoStatus();
    this.developStatistic();
    this.fetchInterestedDegreel();
  },
  methods: {
    // 意向程度soyoungzg_interested_degree
    fetchInterestedDegreel() {
      fechDictData('soyoungzg_interested_degree').then((res) => {
        this.interestedDegreeList = res.data.map((item) => ({
          value: item.value,
          label: item.label
        }));
      });
    },
    // 获取跟进状态
    fechTodoStatus() {
      fechDictData('syzg_todo_status').then((res) => {
        this.todoStatusList = res.data.map((item) => ({
          value: item.value,
          label: item.label
        }));
      });
    },
    // 获取统计数据
    developStatistic() {
      developStatistic().then((res) => {
        const { distributorNum = 0, todayDoneNum = 0, todayTodoNum = 0, yesterdayDoneNum = 0, yesterdayTodoNum = 0 } = res.data || {};
        this.infoList = [
          {
            title: '客户总数', // 标题
            amount: distributorNum // 今日金额
          },
          {
            title: '超时未跟进分销商数',
            amount: todayTodoNum,
            yesterdayAmount: yesterdayTodoNum,
            isArrowShow: true,
            tips: '已设置的待办事项，超时了一直没有跟进的，一个分销商最多计算一次'
          },
          {
            title: '今日已跟进分销商数',
            amount: todayDoneNum,
            yesterdayAmount: yesterdayDoneNum,
            isArrowShow: true,
            tips: '今日在跟进管理里面设置跟进记录的分销商的数量'
          }
        ];
      });
    },
    // 金额格式换处理
    getMoney(data) {
      return data === 0 || data ? '¥' + data : '/';
    },
    // 分页控制pageSize
    handleSizeChange(val) {
      this.pageSize = val;
      this.pageNo = 1;
      this.fetchData();
    },
    // 分页控制pageNo
    handleCurrentChange(val) {
      this.pageNo = val;
      this.fetchData();
    },
    // 重置
    onReset() {
      this.pageNo = 1;
      this.pageSize = 10;
      this.filter = cloneDeep(this.initFilter);
      this.addFriendDate = [];
      this.lastTodoPlanDate = [];
      this.fetchData();
    },
    // 搜索
    onSearch() {
      this.pageNo = 1;
      this.pageSize = 10;
      this.fetchData();
    },
    // 获取列表信息
    fetchData() {
      this.listLoading = true;
      const listQuery = {
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        data: this.interfaceData
      };
      listDevelopDistributorPage(listQuery)
        .then((response) => {
          const { list = [], total = 0 } = response.data || {};
          // 对list进行数据格式化，当传值不存在时用‘/’ 占位
          const list2 = list.map((item) => {
            return Object.assign(
              {
                lastTodoPlanDate: '/',
                name: '/',
                mobile: '/',
                developCustomerServiceName: '/',
                addFriendDate: '/',
                interestedBrand: '/',
                favoriteBrand: '/',
                totalTodoPlanCount: '/',
                interestedDegreeName: '/',
                channelName: '/',
                statusName: '/',
                unFinishTodoPlanCount: '/',
                nextTodoPlanDate: '/'
              },
              item
            );
          });
          this.list = list2;
          this.total = total;
        })
        .finally(() => {
          this.listLoading = false;
        });
    },
    onExport() {
      this.exportLoading = true;
      exportDevelopDistributorPage(this.interfaceData)
        .then((res) => {
          download(res, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', `拓展跟进列表-${parseTime(Date.now(), '{y}-{m}-{d}-{h}:{i}:{s}')}.xlsx`);
        })
        .finally(() => {
          this.exportLoading = false;
        });
    },
    toLink(type, row) {
      const { categoryName, categoryId } = row;
      const listData = {
        id: this.queryData.id,
        shopName: this.queryData.shopName,
        mobile: this.queryData.mobile,
        categoryName,
        categoryId
      };
      const list = [
        {
          key: 'detail',
          label: '商品采购明细',
          value: '/customer-management/client-list/category-sales/detail'
        }
      ];
      for (const item of list) {
        if (item.key === type) {
          sessionStorage.setItem(item.value, JSON.stringify(listData));
          this.$router.push({
            path: item.value
          });
          break;
        }
      }
    },
    onSubmitFollowUp(followUpData, dialog) {
      const data = {
        ...followUpData,
        distributorIds: [dialog.externalData.id]
      };
      createLeadsManagerInBatch(data).then((res) => {
        console.dir(res);
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.distributor-data {
  border: 1px solid #ccc;
  margin-bottom: 20px;
}
</style>
