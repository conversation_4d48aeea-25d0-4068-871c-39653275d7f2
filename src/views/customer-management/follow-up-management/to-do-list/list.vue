<template>
  <div class="page-container">
    <Instructions :showTitle="true" :colNum="0">
      <template slot="title">
        <div>
          <p>注意：任务场景或者专属顾问至少需要选择一个进行查询跟进设置</p>
        </div>
      </template>
    </Instructions>
    <filter-form :options="_filterFormOptions" @query="query" ref="filterForm" :exportOptions="_exportOptions"></filter-form>
    <!-- 操作栏 -->
    <action-bar style="padding: 10px 0" :itemOptions="_actionBarOptions" @actionBarClick="({ id }) => this[id] && this[id]()"></action-bar>
    <table-exhibition @query="query" :options="_tableOptions" :table="table" ref="table" @selection-change="handleSelectionChange" v-loading="loading" v-el-horizontal-scroll>
      <template slot="column_prepend">
        <!-- 选择栏 -->
        <el-table-column type="selection" width="55" :selectable="({ completionFlag }) => completionFlag === '0'"></el-table-column>
        <el-table-column type="index" width="120" label="任务发布编号"></el-table-column>
      </template>
      <el-table-column slot="column_shopName" slot-scope="options" v-bind="options" width="160">
        <template slot-scope="{ row }">
          <div v-if="row.shopName">店铺名称：{{ row.shopName }}</div>
          <div v-if="row.distributorId">分销商ID：{{ row.distributorId }}</div>
        </template>
      </el-table-column>
      <!-- 时间显示 -->
      <template v-for="i of ['createDate']" :slot="`column_${i}`" slot-scope="{ index, ...option }">
        <el-table-column :key="i + index" v-bind="option">
          <template slot-scope="{ row = {} }">
            <div>
              {{ $options.filters.parseTime(row[i], '{y}-{m}-{d}') || '--' }}
            </div>
            <div>
              {{ row[i] | parseTime('{h}:{i}:{s}') }}
            </div>
          </template>
        </el-table-column>
      </template>
      <el-table-column label="操作" width="100" slot="column_append">
        <template slot-scope="scope">
          <action-bar :itemOptions="_tableBarOptions" :record="scope.row" @actionBarClick="({ id }, row) => _self[id](row)"></action-bar>
        </template>
      </el-table-column>
    </table-exhibition>
    <el-dialog title="编辑跟进记录" :visible.sync="followUp_dialogVisible" width="800px" v-loading="followUp_dialogLoading">
      <instructions show-title is-hide v-if="currentRow">
        <template slot="title">
          <ul class="tips-text">
            <li class="custom-tips">
              <span>分销商名称：{{ currentRow.shopName }}</span>
              <span>分销商ID：{{ currentRow.distributorId }}</span>
              <span>专属顾问：{{ currentRow.csName }}</span>
            </li>
          </ul>
        </template>
      </instructions>
      <submit-form :options="followUpDialogOptions" label-width="100px" @onCancel="followUp_dialogVisible = false" @onSubmit="followUpOnSubmit" ref="followUpDialog">
        <template v-slot:formItem_todoEvent="{ form }">
          <div class="color-info">{{ getTodoEventTips(form) }}</div>
          <el-input
            style="width: 500px"
            v-model.trim="form.todoEvent"
            :placeholder="getTodoEventPlaceholder(form)"
            type="textarea"
            maxlength="500"
            show-word-limit
            :autosize="{
              minRows: 6,
              maxRows: 12
            }"
          ></el-input>
        </template>
      </submit-form>
    </el-dialog>
  </div>
</template>

<script>
import FilterForm from '@/components/Form/FilterForm';
import SubmitForm from '@/components/Form/SubmitForm';
import ActionBar from '@/components/Common/ActionBar';
import TableExhibition from '@/components/Table/TableExhibition';
import { FilterFormOptions, ActionBarOptions, TableOptions, TableBarOptions, FollowUpDialogOptions, ExportOptions } from './config';
import { getListTaskDetails, batchCompleteDetails, batchIgnoreDetail } from '@/api/customer-management/clue.js';
import { mapGetters } from 'vuex';
import moment from 'moment';
import { TASK_FOLLOW_UP_DESC, TASK_FOLLOW_UP_TIPS } from '@/constants';

export default {
  name: 'customer-management-follow-up-management-to-do-list-list',
  components: { FilterForm, SubmitForm, ActionBar, TableExhibition },
  data() {
    return {
      loading: false,
      table: {},
      multipleSelection: [],
      followUp_dialogVisible: false,
      followUp_dialogLoading: false,
      followUpDialogOptions: [],
      currentRow: {}
    };
  },
  computed: {
    ...mapGetters(['userGroup'])
  },
  created() {
    this.init();
  },
  mounted() {
    this.params = {
      data: this.$refs.filterForm.getParams(),
      ...this.$refs.table.getParams()
    };
  },
  activated() {},
  methods: {
    getTodoEventPlaceholder({ followResult = '' }) {
      return TASK_FOLLOW_UP_DESC[followResult] || '最少5个字，最多500字（精简总结跟客户沟通的结论和注意点）';
    },
    getTodoEventTips({ followResult = '' }) {
      return TASK_FOLLOW_UP_TIPS[followResult] || '';
    },
    init() {
      this.followUpDialogOptions = FollowUpDialogOptions;
      this._filterFormOptions = FilterFormOptions;
      this._actionBarOptions = ActionBarOptions;
      this._tableOptions = TableOptions;
      this._tableBarOptions = TableBarOptions;
      this._exportOptions = ExportOptions;

      const taskCategory = FilterFormOptions.find(({ prop }) => prop === 'taskCategory');
      taskCategory.options = this.$dict[this.userGroup === '3' ? 'sygj_task_category' : 'syzg_task_category'];
    },
    query(params = {}) {
      Object.assign(this.params, params);
      const { data } = this.params;
      if (!data || (!data.csIds && !data.taskCategory)) {
        this.$message.warning('任务场景或者专属顾问至少需要选择一个进行查询');
        return;
      }
      this.loading = true;
      getListTaskDetails(this.params)
        .then((res) => {
          this.table = res.data;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    batchFollowUp() {
      if (!this.multipleSelection.length) {
        this.$message.warning('至少选择一条数据');
        return;
      }

      const taskTypes = [...new Set(this.multipleSelection.map((i) => i.taskType))];
      if (taskTypes.length > 1) {
        this.$message.warning('任务事项不同不能批量跟进');
        return;
      }

      this.followUp({
        _taskDetailIds: this.multipleSelection.map((i) => i.id),
        taskType: taskTypes[0],
        type: 'batch'
      });
    },
    async followUp({ id, taskType, _taskDetailIds = [id], type = 'single', ...params }) {
      if (type === 'single') {
        this.currentRow = params;
      } else {
        this.currentRow = '';
      }
      this.followUp_dialogVisible = this.followUp_dialogLoading = true;

      this.followUp_dialogLoading = false;
      // 弹框表单重新赋值
      if (!this.$refs.followUpDialog) await this.$nextTick();
      this.$refs.followUpDialog.setData({
        taskDetailIds: _taskDetailIds,
        label: taskType,
        expectedFinishDate: moment(new Date()).valueOf()
      });
    },
    followUpOnSubmit({ form, callback }) {
      const { taskDetailIds, ...todoPlan } = form;
      batchCompleteDetails({
        taskDetailIds,
        todoPlan
      })
        .then(() => {
          this.query();
          this.$message.success('操作成功');
          this.followUp_dialogVisible = false;
        })
        .finally(callback);
    },
    batchIgnoreThing() {
      if (!this.multipleSelection.length) {
        this.$message.warning('至少选择一条数据');
        return;
      }

      this.ignoreThing({ _ids: this.multipleSelection.map((i) => i.id) });
    },
    ignoreThing({ id, _ids = [id] }) {
      this.$confirm('是否确认忽略?')
        .then(() => {
          batchIgnoreDetail(_ids).then(() => {
            this.query();
            this.$message.success('操作成功');
          });
        })
        .catch(() => {
          this.$message.info('已取消操作');
        });
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep .submitForm——form——operate {
  width: 49% !important;
}
.custom-tips {
  > span {
    margin-right: 15px;
    color: var(--color-info);
  }
}
</style>
