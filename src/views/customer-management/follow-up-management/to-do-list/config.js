import Vue from 'vue';
import dict from '@/components/Common/dicts';
import { parseTime } from '@/utils';
// import { OPERATETASK_TASK_STATUS } from '@/components/Common/dicts/static/operatetask';
import { checkGroup } from '@/components/Common/SelectGroup';
import cloneDeep from 'lodash/cloneDeep'; // 对象深拷贝
import { CLUE_FOLLOWRESULTS } from '@/views/customer-management/follow-up-management/follow-up-record/config';
import { todoListExportTaskDetails } from '@/api/customer-management/follow-up/index.js';
import download from '@/utils/download';

// 权限配置
export const Authority = {
  edit: '/customer-management/follow-up-management/to-do-list/list:edit',
  export: 'customer-management-follow-up-management-to-do-list-export'
};

export const ExportOptions = {
  auth: Authority.export,
  name: '导出',
  request(data) {
    return new Promise((resolve) => {
      todoListExportTaskDetails(data)
        .then((res) => {
          // 流文件 文件流
          try {
            const { data } = this.$parent.params;
            if (!data || (!data.csIds && !data.taskCategory)) return this.$message.warning('请先根据任务场景或者专属顾问查询数据后进行导出');
            const resObj = JSON.parse(new TextDecoder('utf-8').decode(new Uint8Array(res)));
            if (resObj.success) {
              this.$message.success(resObj.msg);
            } else {
              this.$message.error(resObj.msg || '系统错误');
            }
          } catch (error) {
            // 获取指标名称
            download(res, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', `待办事项-${parseTime(Date.now(), '{y}-{m}-{d}-{h}:{i}:{s}')}.xlsx`);
          }
        })
        .finally(resolve);
    });
  }
};

export const FilterFormOptions = [
  {
    component: 'select',
    label: '专属顾问',
    prop: 'csIds',
    options: dict('COMMON_EXPAND_ADVISER'),
    multiple: true
  },
  {
    component: 'select',
    label: '任务场景',
    prop: 'taskCategory',
    options: []
  },
  {
    component: 'input',
    label: '任务名称',
    prop: 'title'
  },
  {
    component: 'select',
    label: '任务事项',
    prop: 'taskType',
    options: Vue.prototype.$dict['syzg_task_type']
  },

  {
    prop: 'daterangeTime',
    label: '任务日期',
    component: 'dateRange',
    type: 'daterange',
    props: ['createDateStart', 'createDateEnd']
  },
  {
    component: 'select',
    label: '任务状态',
    prop: 'completionFlag',
    options: Vue.prototype.$dict['task_detail_completion_flag']
  },
  {
    component: 'select',
    label: '顾问团队',
    prop: 'csGroupId',
    options: dict('COMMON_SHOPTAFFGROUP_LIST')
  },
  {
    component: 'input',
    label: '店铺名称',
    prop: 'shopName'
  },
  {
    component: 'input',
    label: '联系方式',
    prop: 'mobile'
  },
  {
    component: 'select',
    label: '任务团队',
    prop: 'groupId',
    visible() {
      return !checkGroup(this.userGroup);
    },
    options: dict('DISTRIBUTOR_TEAM')
  },
  {
    prop: 'distributorId',
    label: '分销商ID',
    component: 'input',
    placeholder: '请输入分销商ID'
  },
];

// 列表操作
export const ActionBarOptions = [
  {
    id: 'batchIgnoreThing',
    label: '忽略',
    authority: Authority.edit
  },
  {
    id: 'batchFollowUp',
    label: '跟进',
    authority: Authority.edit
  }
];

// 列表展示内容
export const TableOptions = [
  {
    prop: 'taskTypeName',
    label: '任务事项'
  },
  {
    prop: 'groupName',
    label: '任务团队',
    formatter: (r, c, v = '--') => v
  },
  {
    prop: 'taskCategoryName',
    label: '任务场景'
  },
  {
    prop: 'title',
    label: '任务名称'
  },
  {
    prop: 'shopName',
    label: '店铺信息',
    isCustom: true
  },
  {
    prop: 'mobile',
    label: '联系方式'
  },
  {
    prop: 'distributorStatus',
    label: '店铺阶段',
    formatter: (r, c, v) =>
      ({
        DISTRIBUTOR_LEADS: '线索',
        DISTRIBUTOR: '分销商'
      }[v] ?? '--')
  },
  {
    prop: 'completionFlagName',
    label: '任务状态',
    formatter: (r, c, v) => v || '--'
  },
  {
    prop: 'csName',
    label: '专属顾问'
  },
  {
    prop: 'csGroupIdName',
    label: '专属顾问团队'
  },
  {
    prop: 'createDate',
    label: '任务日期',
    width: '100',
    isCustom: true
  }
];

// 列表操作
export const TableBarOptions = [
  {
    id: 'ignoreThing',
    label: '忽略',
    authority: Authority.edit,
    loadCondition: ({ completionFlag }) => completionFlag === '0',
    isWrap: true,
    button: {
      type: 'text'
    }
  },
  {
    id: 'followUp',
    label: '跟进',
    authority: Authority.edit,
    loadCondition: ({ completionFlag, taskType }) => completionFlag === '0' && taskType !== 'REGISTER',
    isWrap: true,
    button: {
      type: 'text'
    }
  }
];

const followResultOptions = [];
export const FollowUpDialogOptions = [
  {
    prop: 'expectedFinishDate',
    label: '跟进日期',
    component: 'dateRange',
    type: 'date',
    clearable: false,
    disabled: true,
    pickerOptions: {
      disabledDate(date) {
        return date.getTime() > Date.now();
      }
    },
    rules: [{ required: true, message: '请选择跟进日期', trigger: 'change' }],
    isFinite: true
  },
  {
    prop: 'followWay',
    label: '跟进方式',
    component: 'select',
    options: Vue.prototype.$dict['syzg_todo_follow_way'].filter((i) => i.value !== 'AI'),
    rules: [{ required: true, message: '请选择跟进方式', trigger: 'change' }]
  },
  {
    prop: 'label',
    label: '跟进事项',
    component: 'text',
    disabled: true,
    formatter: (v) => {
      return Vue.prototype.$dict['syzg_todo_follow_label'].find((i) => i.value === v)?.label ?? '--';
    },
    change(value) {
      const options = this.$dict[CLUE_FOLLOWRESULTS[value]] ?? [];
      followResultOptions.splice(0, followResultOptions.length, ...cloneDeep(options));
    },
    options: Vue.prototype.$dict['syzg_todo_follow_label']
  },
  {
    prop: 'followResult',
    label: '跟进结果',
    component: 'select',
    options: followResultOptions,
    rules: [{ required: true, message: '请选择跟进结果', trigger: 'change' }]
  },
  {
    prop: 'todoEvent',
    label: '跟进说明',
    rules: [
      { required: true, message: '请输入跟进说明', trigger: 'blur' },
      { min: 5, message: '最少5个字，最多500字', trigger: 'blur' }
    ]
  },
  {
    prop: 'picUrls',
    label: '上传图片',
    component: 'upload',
    limit: 10,
    multiple: true,
    accept: '.jpg,.jpeg,.png',
    _size: { company: 'KB', size: 5120 },
    _tip: '最多可上传10张图片，每张大小控制在5M以内'
  }
];
