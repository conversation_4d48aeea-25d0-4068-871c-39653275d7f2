<template>
  <div class="table-container create">
    <template v-if="groupId">
      <Instructions :showTitle="true" :colNum="0">
        <template slot="title">
          <div>
            <p v-for="(i, index) of instructions || []" :key="index">{{ index + 1 }}、{{ i }}；</p>
          </div>
        </template>
      </Instructions>

      <batchCreateClue :groupId="groupId" type="Form" ref="batchCreateClue" :channelType="channelType"></batchCreateClue>
    </template>
  </div>
</template>

<script>
import batchCreateClue from './batchCreateClue';
import { getGroupLabel } from '@/components/Common/SelectGroup';

export default {
  name: '/customer-management/clue-management/:add',
  components: { batchCreateClue },
  data() {
    return {
      groupId: '',
      channelType: ''
    };
  },
  computed: {
    instructions() {
      if (this.channelType === 'OFFLINE') {
        return ['线索重复说明：相同团队下面的【客户名称】不允许重复'];
      }
      const Instructions = {
        ZG: [
          '必填字段说明：渠道为淘系时，店铺ID为必填项，其他渠道联系人和联系方式为必填项',
          '线索重复说明：系统会将下列情况的线索判定为重复线索渠道相同且联系方式相同；渠道为淘系时店铺ID相同则认为是重复线索',
          '操作人员如果选择提交重复线索系统会对重复线索做以下处理：同一批次系统不允许录入重复的线索；不同批次手机号码相同属于不同渠道的线索会进行合并'
        ],
        GJ: ['线索重复说明：店铺ID和手机号码都不能重复，如果重复则不能提交'],
        CP: ['线索重复说明：店铺ID和手机号码都不能重复，如果重复则不能提交'],
        GF: ['线索重复说明：店铺ID和手机号码都不能重复，如果重复则不能提交']
      };
      return Instructions[getGroupLabel(this.groupId)];
    }
  },
  mounted() {
    this.groupId = this.$route.query.groupId;
    this.channelType = this.$route.query.channelType || 'ONLINE';
  },
  methods: {}
};
</script>

<style lang="scss" scoped>
.create {
  display: flex;
  flex-direction: column;
  height: 100%;
  & > div:first-child {
    flex: none;
  }
}
</style>
