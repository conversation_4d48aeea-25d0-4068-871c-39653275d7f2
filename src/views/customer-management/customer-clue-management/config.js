import { parseTime } from '@/utils';
import { _validateContact } from '@/common/validator';
import dict from '@/components/Common/dicts';
import { createGlobalInBatch, createNormalInBatch, createOfflineInBatch } from '@/api/customer-management/clue.js';

// 权限
const Authority = {
  detail: '/customer-management/clue-management/:detail',
  edit: '/customer-management/clue-management/:edit',
  mobile: '/distributor-management/:decrypt-mobile',
  add: '/customer-management/clue-management/:add',
  import: '/customer-management/clue-management/:import',
  followUp: '/customer-management/clue-management/:follow-up',
  delete: '/customer-management/clue-management/:delete',
  createDistributor: 'customer-management-clue-management-create-distributor', // 生成分销商
  contact: '/customer-management/clue-management/:contact' // 完善联系方式
};

// ZG
const ClueFormItems = [
  {
    key: 'csGroupId',
    type: 'select',
    label: '线索团队',
    selectOptions: dict('DISTRIBUTOR_TEAM'),
    disabled: true
  },
  // 渠道归属
  {
    key: 'customerChannelKind',
    type: 'select',
    label: '渠道归属',
    selectOptions: dict('SOYOUNGZG_CHANNEL_TYPE'),
    disabled: true
  },
  // 拓展渠道
  {
    key: 'developChannel',
    type: 'select',
    label: '拓展渠道',
    selectOptions: dict('SOYOUNGZG_CHANNEL_ONLINE'),
    rule: [
      {
        required: true,
        message: '请选择拓展渠道'
      }
    ],
    observers: ['platformShopLevel', 'platformShopId', 'mobile', 'contactName']
  },
  // 店铺名称
  {
    key: 'platformShopName',
    type: 'input',
    label: '店铺名称',
    rule: [
      {
        required: true,
        message: '请输入店铺名称'
      }
    ]
  },
  // 店铺ID
  {
    key: 'platformShopId',
    type: 'input',
    label: '店铺ID',
    rule: [
      {
        required: false,
        message: '请输入店铺ID'
      }
    ],
    info: '渠道为淘宝，天猫时必须填写',
    watcherRun(value, options, observerKey) {
      changeRule.apply(this, [
        {
          value,
          options,
          observerKey,
          callback(v) {
            options.rule[0].required = !!v && TaoBaoTypes.includes(v);
          }
        }
      ]);
    }
  },
  // 联系电话
  {
    key: 'mobile',
    type: 'input',
    label: '联系方式',
    info: '渠道非淘宝，天猫时必须填写',
    rule: [
      {
        required: false,
        message: '请输入联系电话'
      },
      {
        validator: _validateContact,
        trigger: 'blur'
      }
    ],
    watcherRun(value, options, observerKey) {
      changeRule.apply(this, [
        {
          value,
          options,
          observerKey,
          callback(v) {
            options.rule[0].required = !!v && !TaoBaoTypes.includes(v);
          }
        }
      ]);
    }
  },
  // 联系人
  {
    key: 'contactName',
    type: 'input',
    label: '联系人',
    info: '渠道非淘宝，天猫时必须填写',
    rule: [
      {
        required: false,
        message: '请输入联系人'
      }
    ],
    watcherRun(value, options, observerKey) {
      changeRule.apply(this, [
        {
          value,
          options,
          observerKey,
          callback(v) {
            options.rule[0].required = !!v && !TaoBaoTypes.includes(v);
          }
        }
      ]);
    }
  },
  // 店铺等级/类型
  {
    key: 'platformShopLevel',
    type: 'select',
    label: '店铺等级/类型',
    selectOptions: Promise.resolve([]),
    watcherRun(value, options, observerKey) {
      const channels = ['TAOBAO'];

      // 改变下拉选项
      const selectOptions = value ? dict(channels.includes(value) ? 'SHOP_LEVEL' : 'SHOP_TYPE') : Promise.resolve([]);

      options.selectOptions = selectOptions;
    }
  },
  // 主营类目
  {
    key: 'businessCategoryList',
    type: 'select',
    label: '主营类目',
    selectOptions: dict('COMMON_CATEGORY'),

    multiple: true,
    'collapse-tags': true,
    rule: [
      {
        required: true,
        message: '请选择主营类目'
      }
    ],
    change(options) {
      const value = this.formData[options.key];
      if (value.length > 1 && value.includes('_ditto')) {
        this.formData[options.key].splice(...(value[0] === '_ditto' ? [0, 1] : [0, value.length - 1]));
      }
    }
  },
  // 企业/个人
  {
    key: 'merchantType',
    type: 'select',
    label: '商家类型',
    selectOptions: dict('COMMON_ENTERPRISE_NATURE'),
    observers: ['company'],
    rule: [
      {
        required: true,
        message: '请选择企业/个人'
      }
    ]
  },
  // 企业名称
  {
    key: 'company',
    type: 'input',
    label: '企业名称',
    rule: [
      {
        required: false,
        message: '请输入企业名称'
      }
    ],
    watcherRun(value, options, observerKey) {
      const keys = ['ENTERPRISE'];
      changeRule.apply(this, [
        {
          value,
          options,
          observerKey,
          callback(v) {
            options.rule[0].required = keys.includes(v);
          }
        }
      ]);
    }
  },
  // 企业联系方式
  {
    key: 'companyLegalMobile',
    type: 'input',
    label: '企业联系方式',
    rule: [
      {
        validator: _validateContact,
        trigger: 'blur'
      }
    ]
  },
  // 企业法人
  {
    key: 'companyLegalName',
    type: 'input',
    label: '企业法人'
  },
  // 拓展关键词
  {
    key: 'keyWord',
    type: 'input',
    label: '拓展关键词',
    rule: [
      {
        required: true,
        message: '请输入拓展关键字'
      }
    ]
  },
  // 拓展品牌
  {
    key: 'favoriteBrandList',
    type: 'select',
    label: '拓展品牌',
    selectOptions: dict('COMMON_EXPAND_BRAND'),

    multiple: true,
    'collapse-tags': true,
    rule: [
      {
        required: true,
        message: '请选择拓展品牌'
      }
    ],
    change(options) {
      const value = this.formData[options.key];
      if (value.length > 1 && value.includes('_ditto')) {
        this.formData[options.key].splice(...(value[0] === '_ditto' ? [0, 1] : [0, value.length - 1]));
      }
    }
  },
  // 企业规模
  {
    key: 'companyScale',
    type: 'select',
    label: '企业规模',
    selectOptions: dict('COMMON_ENTERPRISE_SCALE')
  },
  // 偏好品牌
  {
    key: 'interestedBrandList',
    type: 'select',
    label: '偏好品牌',
    selectOptions: dict('COMMON_PREFER_BRAND'),

    multiple: true,
    'collapse-tags': true,
    change(options) {
      const value = this.formData[options.key];
      if (value.length > 1 && value.includes('_ditto')) {
        this.formData[options.key].splice(...(value[0] === '_ditto' ? [0, 1] : [0, value.length - 1]));
      }
    }
  },
  // 微信ID
  {
    key: 'wechat',
    type: 'input',
    label: '微信ID'
  },
  // 店铺链接
  {
    key: 'platformShopLink',
    type: 'input',
    label: '店铺链接'
    /* rule: [{
      validator: _validateUrl
    }] */
  },
  // 联系人岗位
  {
    key: 'contactPost',
    type: 'input',
    label: '联系人岗位'
  },
  // 推广属性
  {
    key: 'promoteChannel',
    type: 'select',
    label: '推广属性',
    selectOptions: dict('COMMON_PROMOTION_NATURE'),
    multiple: true,
    change(options) {
      const value = this.formData[options.key];
      if (value.length > 1 && value.includes('_ditto')) {
        this.formData[options.key].splice(...(value[0] === '_ditto' ? [0, 1] : [0, value.length - 1]));
      }
    }
  },
  // 店铺平均客单
  {
    key: 'averageCustomerPrice',
    type: 'select',
    selectOptions: dict('SHOP_AVERAGE_CUSTOMERORDER'),
    label: '店铺平均客单 '
  },
  // 店铺月均销量
  {
    key: 'monthlySales',
    type: 'select',
    selectOptions: dict('SHOP_AVERAGE_MONTHSALE'),
    label: '店铺平均月销'
  },
  // 店铺描述
  {
    key: 'description',
    type: 'input',
    elType: 'textarea',
    rows: 3,
    maxlength: 500,
    showWordLimit: true,
    label: '店铺描述'
  },
  // 行业评级
  {
    key: 'industryLevel',
    type: 'select',
    label: '行业评级',
    selectOptions: dict('COMMON_TRADE_LEVEL'),
    rule: [
      {
        required: true,
        message: '请选择行业评级'
      }
    ]
  }
];

// GJ
const ClueFormItems_GJ = [
  {
    key: 'csGroupId',
    type: 'select',
    label: '线索团队',
    selectOptions: dict('DISTRIBUTOR_TEAM'),
    disabled: true
  },
  // 渠道归属
  {
    key: 'customerChannelKind',
    type: 'select',
    label: '渠道归属',
    selectOptions: dict('SOYOUNGZG_CHANNEL_TYPE'),
    disabled: true
  },
  // 拓展渠道
  {
    key: 'developChannel',
    type: 'select',
    label: '拓展渠道',
    selectOptions: dict('SOYOUNGZG_CHANNEL_ONLINE'),
    rule: [
      {
        required: true,
        message: '请选择拓展渠道'
      }
    ],
    observers: ['platformShopLevel']
  },
  // 店铺名称
  {
    key: 'platformShopName',
    type: 'input',
    label: '店铺名称',
    rule: [
      {
        required: true,
        message: '请输入店铺名称'
      }
    ]
  },
  // 店铺ID
  {
    key: 'platformShopId',
    type: 'input',
    label: '店铺ID',
    rule: [
      {
        required: true,
        message: '请输入店铺ID'
      }
    ],
    watcherRun(value, options, observerKey) {
      changeRule.apply(this, [
        {
          value,
          options,
          observerKey,
          callback(v) {
            options.rule[0].required = !!v && TaoBaoTypes.includes(v);
          }
        }
      ]);
    }
  },
  // 联系电话
  {
    key: 'mobile',
    type: 'input',
    label: '联系方式',
    rule: [
      {
        required: false,
        message: '请输入联系电话'
      },
      {
        validator: _validateContact,
        trigger: 'blur'
      }
    ],
    watcherRun(value, options, observerKey) {
      changeRule.apply(this, [
        {
          value,
          options,
          observerKey,
          callback(v) {
            options.rule[0].required = !!v && !TaoBaoTypes.includes(v);
          }
        }
      ]);
    }
  },
  // 联系人
  {
    key: 'contactName',
    type: 'input',
    label: '联系人',
    rule: [
      {
        required: false,
        message: '请输入联系人'
      }
    ],
    watcherRun(value, options, observerKey) {
      changeRule.apply(this, [
        {
          value,
          options,
          observerKey,
          callback(v) {
            options.rule[0].required = !!v && !TaoBaoTypes.includes(v);
          }
        }
      ]);
    }
  },
  // 店铺等级/类型
  {
    key: 'platformShopLevel',
    type: 'select',
    label: '店铺等级/类型',
    selectOptions: Promise.resolve([]),
    watcherRun(value, options, observerKey) {
      const channels = ['TAOBAO'];

      // 改变下拉选项
      const selectOptions = value ? dict(channels.includes(value) ? 'SHOP_LEVEL' : 'SHOP_TYPE') : Promise.resolve([]);

      options.selectOptions = selectOptions;
    }
  },
  // 主营类目
  {
    key: 'businessCategoryList',
    type: 'select',
    label: '主营类目',
    selectOptions: dict('COMMON_CATEGORY'),
    multiple: true,
    'collapse-tags': true,
    rule: [
      {
        required: true,
        message: '请选择主营类目'
      }
    ],
    change(options) {
      const value = this.formData[options.key];
      if (value.length > 1 && value.includes('_ditto')) {
        this.formData[options.key].splice(...(value[0] === '_ditto' ? [0, 1] : [0, value.length - 1]));
      }
    }
  },
  // 企业/个人
  {
    key: 'merchantType',
    type: 'select',
    label: '商家类型',
    selectOptions: dict('COMMON_ENTERPRISE_NATURE'),
    observers: ['company'],
    rule: [
      {
        required: true,
        message: '请选择企业/个人'
      }
    ]
  },
  // 企业名称
  {
    key: 'company',
    type: 'input',
    label: '企业名称',
    rule: [
      {
        required: false,
        message: '请输入企业名称'
      }
    ],
    watcherRun(value, options, observerKey) {
      const keys = ['ENTERPRISE'];
      changeRule.apply(this, [
        {
          value,
          options,
          observerKey,
          callback(v) {
            options.rule[0].required = keys.includes(v);
          }
        }
      ]);
    }
  },
  // 企业联系方式
  {
    key: 'companyLegalMobile',
    type: 'input',
    label: '企业联系方式',
    rule: [
      {
        validator: _validateContact,
        trigger: 'blur'
      }
    ]
  },
  // 企业法人
  {
    key: 'companyLegalName',
    type: 'input',
    label: '企业法人'
  },
  // 拓展关键词
  {
    key: 'keyWord',
    type: 'input',
    label: '拓展关键词',
    rule: [
      {
        required: true,
        message: '请输入拓展关键字'
      }
    ]
  },
  // 拓展品牌
  {
    key: 'favoriteBrandList',
    type: 'select',
    label: '拓展品牌',
    selectOptions: dict('COMMON_EXPAND_BRAND'),

    multiple: true,
    'collapse-tags': true,
    rule: [
      {
        required: true,
        message: '请选择拓展品牌'
      }
    ],
    change(options) {
      const value = this.formData[options.key];
      if (value.length > 1 && value.includes('_ditto')) {
        this.formData[options.key].splice(...(value[0] === '_ditto' ? [0, 1] : [0, value.length - 1]));
      }
    }
  },
  // 企业规模
  {
    key: 'companyScale',
    type: 'select',
    label: '企业规模',
    selectOptions: dict('COMMON_ENTERPRISE_SCALE')
  },
  // 偏好品牌
  {
    key: 'interestedBrandList',
    type: 'select',
    label: '偏好品牌',
    selectOptions: dict('COMMON_PREFER_BRAND'),

    multiple: true,
    'collapse-tags': true,
    change(options) {
      const value = this.formData[options.key];
      if (value.length > 1 && value.includes('_ditto')) {
        this.formData[options.key].splice(...(value[0] === '_ditto' ? [0, 1] : [0, value.length - 1]));
      }
    }
  },
  // 微信ID
  {
    key: 'wechat',
    type: 'input',
    label: '微信ID'
  },
  // 店铺链接
  {
    key: 'platformShopLink',
    type: 'input',
    label: '店铺链接'
    /* rule: [{
      validator: _validateUrl
    }] */
  },
  // 联系人岗位
  {
    key: 'contactPost',
    type: 'input',
    label: '联系人岗位'
  },
  // 推广属性
  {
    key: 'promoteChannel',
    type: 'select',
    label: '推广属性',
    selectOptions: dict('COMMON_PROMOTION_NATURE'),
    multiple: true,
    change(options) {
      const value = this.formData[options.key];
      if (value.length > 1 && value.includes('_ditto')) {
        this.formData[options.key].splice(...(value[0] === '_ditto' ? [0, 1] : [0, value.length - 1]));
      }
    }
  },
  // 店铺平均客单
  {
    key: 'averageCustomerPrice',
    type: 'select',
    selectOptions: dict('SHOP_AVERAGE_CUSTOMERORDER'),
    label: '店铺平均客单价'
  },
  // 店铺月均销量
  {
    key: 'monthlySales',
    type: 'select',
    selectOptions: dict('SHOP_AVERAGE_MONTHSALE'),
    label: '店铺平均月销'
  },
  // 店铺描述
  {
    key: 'description',
    type: 'input',
    elType: 'textarea',
    rows: 3,
    maxlength: 500,
    showWordLimit: true,
    label: '店铺描述'
  },
  // 行业评级
  {
    key: 'industryLevel',
    type: 'select',
    label: '行业评级',
    selectOptions: dict('COMMON_TRADE_LEVEL'),
    rule: [
      {
        required: true,
        message: '请选择行业评级'
      }
    ]
  }
];

const FormItemsOffline = [
  {
    key: 'csGroupId',
    type: 'select',
    label: '线索团队',
    selectOptions: dict('DISTRIBUTOR_TEAM'),
    disabled: true
  },
  // 渠道归属
  {
    key: 'customerChannelKind',
    type: 'select',
    label: '渠道归属',
    selectOptions: dict('SOYOUNGZG_CHANNEL_TYPE'),
    disabled: true
  },
  // 拓展渠道
  {
    key: 'developChannel',
    type: 'select',
    label: '拓展渠道',
    selectOptions: dict('SOYOUNGZG_CHANNEL_OFFLINE'),
    rule: [
      {
        required: true,
        message: '请选择拓展渠道'
      }
    ],
    observers: ['platformShopLevel', 'platformShopId', 'mobile', 'contactName', 'platformShopName']
  },
  // 客户名称
  {
    key: 'platformShopName',
    type: 'input',
    label: '客户名称',
    rule: [
      {
        required: true,
        message: '请输入客户名称'
      }
    ],
    observers: ['platformShopName']
  },
  // 客户业务类型
  {
    key: 'businessTypeAndExtend',
    label: '客户业务类型',
    type: 'select',
    selectOptions: dict('DISTRIBUTOR_BUSINESS_TYPE_AND_EXTEND'),
    rule: [
      {
        required: true,
        message: '请选择客户业务类型'
      }
    ]
  },
  // 主营类目
  {
    key: 'businessCategoryList',
    type: 'select',
    label: '主营类目',
    selectOptions: dict('COMMON_CATEGORY'),

    multiple: true,
    'collapse-tags': true,
    rule: [
      {
        required: false,
        message: '请选择主营类目'
      }
    ],
    change(options) {
      const value = this.formData[options.key];
      if (value.length > 1 && value.includes('_ditto')) {
        this.formData[options.key].splice(...(value[0] === '_ditto' ? [0, 1] : [0, value.length - 1]));
      }
    }
  },

  // 联系人
  {
    key: 'contactName',
    type: 'input',
    label: '联系人',
    rule: [
      {
        required: false,
        message: '请输入联系人'
      }
    ]
  },
  // 联系人岗位
  {
    key: 'contactPost',
    type: 'input',
    label: '联系人岗位',
    rule: [
      {
        required: false,
        message: '请输入联系人岗位'
      }
    ]
  },
  // 联系方式
  {
    key: 'mobile',
    type: 'input',
    label: '联系方式',
    info: '联系方式支持格式：\n1、移动手机号码：如13222222228 \n2、座机号码：如0745-12345678',
    rule: [
      {
        required: false,
        message: '请输入联系方式'
      },
      {
        validator: _validateContact,
        trigger: 'blur'
      }
    ]
  },
  // 微信ID
  {
    key: 'wechat',
    type: 'input',
    label: '微信ID',
    rule: [
      {
        required: false,
        message: '请输入微信ID'
      }
    ]
  },
  // 偏好品牌
  {
    key: 'interestedBrandList',
    type: 'select',
    label: '偏好品牌',
    selectOptions: dict('COMMON_PREFER_BRAND'),

    multiple: true,
    'collapse-tags': true,
    change(options) {
      const value = this.formData[options.key];
      if (value.length > 1 && value.includes('_ditto')) {
        this.formData[options.key].splice(...(value[0] === '_ditto' ? [0, 1] : [0, value.length - 1]));
      }
    }
  },
  // 店铺平均客单价
  {
    key: 'averageCustomerPrice',
    type: 'select',
    selectOptions: dict('SHOP_AVERAGE_CUSTOMERORDER'),
    label: '店铺平均客单价'
  },
  // 店铺数量
  {
    key: 'shopQuantity',
    type: 'input',
    label: '店铺数量',
    rule: [
      {
        required: false,
        message: '请输入店铺数量'
      }
    ]
  },
  // 店铺数量
  {
    key: 'shopQuantity',
    type: 'input',
    label: '店铺数量',
    rule: [
      {
        required: false,
        message: '请输入店铺数量'
      }
    ]
  },
  // 店铺位置
  {
    key: 'shopAddress',
    type: 'input',
    label: '店铺位置',
    rule: [
      {
        required: false,
        message: '请输入店铺位置'
      }
    ]
  },
  // 客户描述
  {
    key: 'description',
    type: 'input',
    label: '客户描述',
    rule: [
      {
        required: false,
        message: '请输入客户描述'
      }
    ]
  }
];

const TaoBaoTypes = ['TAOBAO', 'TMALL'];

const CreateChannelClueTemplate = {
  ZG: [
    // 线索团队
    {
      key: 'csGroupId',
      style: {
        width: '100px'
      },
      elOptions: {
        width: '100px'
      }
    },
    // 渠道归属
    {
      key: 'customerChannelKind',
      style: {
        width: '100px'
      },
      elOptions: {
        width: '100px'
      }
    },
    {
      key: 'developChannel',
      style: {
        width: '100px'
      },
      elOptions: {
        width: '100px'
      }
    },
    {
      key: 'platformShopName',
      style: {
        width: '200px'
      },
      elOptions: {
        width: '200px'
      }
    },
    {
      key: 'platformShopId',
      style: {
        width: '200px'
      },
      elOptions: {
        width: '200px'
      }
    },
    // 行业评级
    {
      key: 'industryLevel',
      style: {
        width: '200px'
      },
      elOptions: {
        width: '200px'
      }
    },
    {
      key: 'contactName',
      style: {
        width: '120px'
      },
      elOptions: {
        width: '120px'
      }
    },
    {
      key: 'mobile',
      style: {
        width: '200px'
      },
      elOptions: {
        width: '200px'
      }
    },
    {
      key: 'businessCategoryList',
      style: {
        width: '200px'
      },
      elOptions: {
        width: '200px'
      }
    },
    {
      key: 'keyWord',
      style: {
        width: '150px'
      },
      elOptions: {
        width: '150px'
      }
    },
    {
      key: 'favoriteBrandList',
      style: {
        width: '200px'
      },
      elOptions: {
        width: '200px'
      }
    },
    {
      key: 'merchantType',
      style: {
        width: '100px'
      },
      elOptions: {
        width: '100px'
      }
    },
    {
      key: 'platformShopLevel',
      style: {
        width: '120px'
      },
      elOptions: {
        width: '120px'
      }
    },
    {
      key: 'interestedBrandList',
      style: {
        width: '200px'
      },
      elOptions: {
        width: '200px'
      }
    },
    // 店铺描述
    {
      key: 'description',
      style: {
        width: '250px'
      },
      elOptions: {
        width: '250px',
        maxlength: '500',
        showOverflowTooltip: true
      }
    },
    {
      key: 'company',
      style: {
        width: '200px'
      },
      elOptions: {
        width: '200px'
      }
    },
    {
      key: 'companyLegalName',
      style: {
        width: '120px'
      },
      elOptions: {
        width: '120px'
      }
    },
    {
      key: 'companyLegalMobile',
      style: {
        width: '200px'
      },
      elOptions: {
        width: '200px'
      }
    },
    {
      key: 'companyScale',
      style: {
        width: '150px'
      },
      elOptions: {
        width: '150px'
      }
    },
    {
      key: 'wechat',
      style: {
        width: '200px'
      },
      elOptions: {
        width: '200px'
      }
    }
  ],
  GJ: [
    // 线索团队
    {
      key: 'csGroupId',
      style: {
        width: '100px'
      },
      elOptions: {
        width: '100px'
      }
    },
    // 渠道归属
    {
      key: 'customerChannelKind',
      style: {
        width: '100px'
      },
      elOptions: {
        width: '100px'
      }
    },
    // 拓展渠道
    {
      key: 'developChannel',
      style: {
        width: '100px'
      },
      elOptions: {
        width: '100px'
      }
    },
    // 店铺名称
    {
      key: 'platformShopName',
      style: {
        width: '200px'
      },
      elOptions: {
        width: '200px'
      }
    },
    // 店铺ID
    {
      key: 'platformShopId',
      style: {
        width: '200px'
      },
      elOptions: {
        width: '200px'
      }
    },
    // 行业评级
    {
      key: 'industryLevel',
      style: {
        width: '200px'
      },
      elOptions: {
        width: '200px'
      }
    },
    // 主营类目
    {
      key: 'businessCategoryList',
      style: {
        width: '200px'
      },
      elOptions: {
        width: '200px'
      }
    },
    // 店铺链接
    {
      key: 'platformShopLink',
      style: {
        width: '200px'
      },
      elOptions: {
        width: '200px'
      }
    },
    // 店铺等级/类型
    {
      key: 'platformShopLevel',
      style: {
        width: '120px'
      },
      elOptions: {
        width: '120px'
      }
    },
    // 推广属性
    {
      key: 'promoteChannel',
      style: {
        width: '200px'
      },
      elOptions: {
        width: '200px'
      }
    },
    // 店铺平均客单价
    {
      key: 'averageCustomerPrice',
      style: {
        width: '200px'
      },
      elOptions: {
        width: '200px'
      }
    },
    // 店铺平均月销

    {
      key: 'monthlySales',
      style: {
        width: '200px'
      },
      elOptions: {
        width: '200px'
      }
    },
    // 偏好品牌
    {
      key: 'interestedBrandList',
      style: {
        width: '200px'
      },
      elOptions: {
        width: '200px'
      }
    },
    // 店铺描述
    {
      key: 'description',
      style: {
        width: '250px'
      },
      elOptions: {
        width: '250px',
        maxlength: '500',
        showOverflowTooltip: true
      }
    },
    // 联系人
    {
      key: 'contactName',
      style: {
        width: '120px'
      },
      elOptions: {
        width: '120px'
      }
    },
    // 联系人岗位
    {
      key: 'contactPost',
      style: {
        width: '200px'
      },
      elOptions: {
        width: '200px'
      }
    },
    // 联系方式
    {
      key: 'mobile',
      style: {
        width: '200px'
      },
      elOptions: {
        width: '200px'
      }
    },
    // 微信ID
    {
      key: 'wechat',
      style: {
        width: '200px'
      },
      elOptions: {
        width: '200px'
      }
    },
    // 企业名称
    {
      key: 'company',
      style: {
        width: '200px'
      },
      elOptions: {
        width: '200px'
      }
    },
    // 企业法人
    {
      key: 'companyLegalName',
      style: {
        width: '120px'
      },
      elOptions: {
        width: '120px'
      }
    },
    // 企业联系方式
    {
      key: 'companyLegalMobile',
      style: {
        width: '200px'
      },
      elOptions: {
        width: '200px'
      }
    },
    // 企业规模
    {
      key: 'companyScale',
      style: {
        width: '150px'
      },
      elOptions: {
        width: '150px'
      }
    }
  ]
};

const CreateChannelClueOffineTemplate = [
  // 线索团队
  {
    key: 'csGroupId',
    style: {
      width: '100px'
    },
    elOptions: {
      width: '100px'
    }
  },
  // 渠道归属
  {
    key: 'customerChannelKind',
    style: {
      width: '100px'
    },
    elOptions: {
      width: '100px'
    }
  },
  // 拓展渠道
  {
    key: 'developChannel',
    style: {
      width: '100px'
    },
    elOptions: {
      width: '100px'
    }
  },
  // 客户名称
  {
    key: 'platformShopName',
    style: {
      width: '200px'
    },
    elOptions: {
      width: '200px'
    }
  },
  // 客户业务类型
  {
    key: 'businessTypeAndExtend',
    style: {
      width: '180px'
    },
    elOptions: {
      width: '180px'
    }
  },
  // 主营类目
  {
    key: 'businessCategoryList',
    style: {
      width: '200px'
    },
    elOptions: {
      width: '200px'
    }
  },
  // 联系人
  {
    key: 'contactName',
    style: {
      width: '120px'
    },
    elOptions: {
      width: '120px'
    }
  },
  // 联系人岗位
  {
    key: 'contactPost',
    style: {
      width: '200px'
    },
    elOptions: {
      width: '200px'
    }
  },
  // 联系方式
  {
    key: 'mobile',
    style: {
      width: '200px'
    },
    elOptions: {
      width: '200px'
    }
  },
  // 微信ID
  {
    key: 'wechat',
    style: {
      width: '200px'
    },
    elOptions: {
      width: '200px'
    }
  },
  // 偏好品牌
  {
    key: 'interestedBrandList',
    style: {
      width: '200px'
    },
    elOptions: {
      width: '200px'
    }
  },
  // 店铺平均客单价
  {
    key: 'averageCustomerPrice',
    style: {
      width: '200px'
    },
    elOptions: {
      width: '200px'
    }
  },
  // 店铺数量
  {
    key: 'shopQuantity',
    style: {
      width: '80px'
    },
    elOptions: {
      width: '80px'
    }
  },
  // 店铺位置
  {
    key: 'shopAddress',
    style: {
      width: '180px'
    },
    elOptions: {
      width: '180px'
    }
  },
  // 客户描述
  {
    key: 'description',
    style: {
      width: '250px'
    },
    elOptions: {
      width: '250px',
      maxlength: '500',
      showOverflowTooltip: true
    }
  }
];

const InfoChannelClueTemplate = {
  ZG: [
    {
      label: '添加人',
      key: 'createByName'
    },
    {
      label: '添加时间',
      key: 'createDate',
      handle(v) {
        return parseTime(v, '{y}-{m}-{d}');
      }
    },
    {
      label: '拓展渠道',
      key: 'developChannelName'
    },
    // 店铺描述
    {
      key: 'description',
      elAttr: {
        showOverflowTooltip: true
      }
    },
    {
      key: 'platformShopId'
    },
    {
      key: 'industryLevel'
    },

    {
      key: 'platformShopName'
    },
    {
      label: '店铺等级/类型',
      key: 'platformShopLevelName'
    },
    {
      key: 'businessCategoryNames',
      label: '主营类目',
      handle: (v = []) => v.join(',')
    },
    {
      key: 'developCsName',
      label: '拓展顾问'
    },
    {
      key: 'contactName'
    },
    {
      key: 'keyWord'
    },
    {
      key: 'favoriteBrandNames',
      label: '拓展品牌',
      showOverflowTooltip: true,
      handle: (v = []) => v.join(',')
    },
    {
      key: 'interestedBrandNames',
      label: '偏好品牌',
      showOverflowTooltip: true,
      handle: (v = []) => v.join(',')
    },
    {
      key: 'merchantTypeName',
      label: '商家类型'
    },
    {
      key: 'company'
    },
    {
      key: 'companyLegalMobile'
    },
    {
      key: 'companyLegalName'
    },
    {
      key: 'companyScaleName',
      label: '企业规模'
    },
    {
      label: '更新时间',
      key: 'updateDate',
      handle(v) {
        return parseTime(v, '{y}-{m}-{d}');
      }
    }
  ],
  GJ: [
    {
      label: '添加人',
      key: 'createByName'
    },
    {
      label: '添加时间',
      key: 'createDate',
      handle(v) {
        return parseTime(v, '{y}-{m}-{d}');
      }
    },
    {
      label: '拓展渠道',
      key: 'developChannelName'
    },
    {
      key: 'description',
      elAttr: {
        showOverflowTooltip: true
      }
    },
    {
      key: 'platformShopId'
    },
    {
      key: 'industryLevel'
    },
    {
      key: 'platformShopName'
    },
    {
      key: 'platformShopLink'
    },
    {
      label: '店铺等级/类型',
      key: 'platformShopLevelName'
    },
    {
      key: 'businessCategoryNames',
      label: '主营类目',
      handle: (v = []) => v.join(',')
    },
    {
      key: 'promoteChannelNames',
      label: '推广属性',
      handle: (v = []) => v.join(',')
    },
    {
      key: 'averageCustomerPriceName',
      label: '店铺平均客单'
    },
    {
      key: 'monthlySalesName',
      label: '店铺月均销量'
    },
    {
      key: 'developCsName',
      label: '拓展顾问'
    },
    {
      key: 'contactName'
    },
    {
      key: 'contactPost'
    },
    {
      key: 'interestedBrandNames',
      label: '偏好品牌',
      showOverflowTooltip: true,
      handle: (v = []) => v.join(',')
    },
    {
      label: '更新时间',
      key: 'updateDate',
      handle(v) {
        return parseTime(v, '{y}-{m}-{d}');
      }
    }
  ]
};

// 改变规则
async function changeRule({ value, options, observerKey, callback }) {
  const key = options.key;
  callback(value);
  this.$refs.form && this.$refs.form.clearValidate(key);
}

const GroupIds = {
  1: 'ZG',
  2: 'GJ',
  3: 'GJ',
  10: 'GJ'
};
const TemplateTypes = {
  ZG: {
    id: ['1'],
    FormItems: ClueFormItems,
    createAxios: createNormalInBatch,
    statusLimit: 5,
    uploadFileUrl: '/soyoungzg/api/distributorLeadsManager/uploadFileNormal',
    failFileUrl: '/soyoungzg/api/distributorLeadsManager/downloadNormalExportResult',
    tipIcons: ['el-icon-close', 'el-icon-close', 'el-icon-check', 'el-icon-close', 'el-icon-close', 'el-icon-check'],
    xlsx: process.env.VUE_APP_MUSHUROOMFILEURL + '/static/file/soyoung-zg/template/线上-直供线索导入模板.xlsx'
  },
  GJ: {
    id: ['3', '2', '10'],
    FormItems: ClueFormItems_GJ,
    createAxios: createGlobalInBatch,
    statusLimit: 5,
    uploadFileUrl: '/soyoungzg/api/distributorLeadsManager/uploadFileGlobal',
    failFileUrl: '/soyoungzg/api/distributorLeadsManager/downloadGlobalExportResult',
    tipIcons: ['el-icon-close', 'el-icon-close', 'el-icon-check', 'el-icon-close', 'el-icon-close', 'el-icon-check'],
    xlsx: process.env.VUE_APP_MUSHUROOMFILEURL + '/static/file/soyoung-zg/template/线上-线索导入模板.xlsx'
  }
};

const OfflineTemplate = {
  FormItems: FormItemsOffline,
  createAxios: createOfflineInBatch,
  statusLimit: 5,
  uploadFileUrl: '/soyoungzg/api/distributorLeadsManager/uploadFileForOffline',
  failFileUrl: '/soyoungzg/api/distributorLeadsManager/downloadOfflineExportResult',
  tipIcons: ['el-icon-close', 'el-icon-close', 'el-icon-check', 'el-icon-close', 'el-icon-close', 'el-icon-check'],
  xlsx: process.env.VUE_APP_MUSHUROOMFILEURL + '/static/file/soyoung-zg/template/线下-线索导入模板.xlsx'
};

// 获取模板
function getTeamFormItems(index, type = 'create', channelType = 'ONLINE') {
  if (!GroupIds[index] && channelType === 'ONLINE') return [];

  const template = type === 'create' ? CreateChannelClueTemplate : InfoChannelClueTemplate;
  let templateValue = '';

  if (channelType === 'OFFLINE') {
    templateValue = CreateChannelClueOffineTemplate;
  } else {
    templateValue = template[GroupIds[index]];
  }
  return templateValue.map(({ key, ...args }) => {
    // 初始化csGroupId值
    if (key === 'csGroupId') {
      args.value = index;
    }
    // 渠道归属：线上 线下
    if (key === 'customerChannelKind') {
      args.value = channelType;
    }
    let templateTypesValue = '';
    if (channelType === 'OFFLINE') {
      templateTypesValue = OfflineTemplate;
    } else {
      templateTypesValue = TemplateTypes[GroupIds[index]];
    }
    const options = {
      ...(templateTypesValue.FormItems.find((x) => x.key === key) || {
        key
      }),
      ...args
    };
    return {
      ...options,
      change() {
        options.change && options.change.apply(this, arguments);
        const { rule } = arguments[0];

        // 触发校验
        if (rule) {
          this.$emit('onFormItemChange', arguments);
        }
      }
    };
  });
}

function getTeamOptions(id, channelType) {
  if (channelType === 'OFFLINE') {
    return OfflineTemplate;
  } else {
    const options = Object.values(TemplateTypes).find((i) => i.id.includes(id));
    return options;
  }
}

const infoFormItems = [
  {
    key: 'createDate',
    label: '创建日期',
    handle(v) {
      return parseTime(v, '{y}-{m}-{d}');
    }
  },
  {
    key: 'statusName',
    label: '线索状态'
  },
  {
    key: 'mobile',
    label: '联系方式'
  },
  /* {
    key: 'platformShopId',
    label: '店铺ID'
  }, */
  {
    key: 'channelNames',
    label: '拓展渠道',
    handle: (v = []) => v.join(',')
  },
  {
    key: 'interestedBrandNames',
    label: '偏好品牌',
    handle: (v = []) => v.join(',')
  },
  {
    key: 'businessCategoryNames',
    label: '主营类目',
    handle: (v = []) => v.join(',')
  },
  {
    key: 'tag',
    label: '标签'
  }
];

const infoOperateItems = [
  {
    id: 'addChannelClue',
    authority: Authority.add,
    // routerLink: '/customer-management/customer-clue-management/create',
    label: '添加',
    type: 'Button'
  }
];

export default {
  getTeamFormItems,
  GroupIds,
  TemplateTypes,
  Info: {
    infoFormItems,
    infoOperateItems
  },
  TaoBaoTypes,
  getTeamOptions,
  Authority
};
