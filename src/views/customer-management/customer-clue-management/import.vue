<template>
  <div class="table-container clueImport">
    <ul class="clueImport_step">
      <li class="clueImport_step_info">
        <span>第一步</span>
        <span>下载模板</span>
      </li>
      <li class="clueImport_step_arrow">
        <svg-icon icon-class="arrow-right"></svg-icon>
      </li>
      <li class="clueImport_step_info">
        <span>第二步</span>
        <span>按照要求填写内容</span>
      </li>
      <li class="clueImport_step_arrow">
        <svg-icon icon-class="arrow-right"></svg-icon>
      </li>
      <li class="clueImport_step_info">
        <span>第三步</span>
        <span>上传文档，自动去重</span>
      </li>
      <li class="clueImport_step_arrow">
        <svg-icon icon-class="arrow-right"></svg-icon>
      </li>
      <li class="clueImport_step_info">
        <span>第四步</span>
        <span>确认导入</span>
      </li>
    </ul>
    <div class="clueImport_operation">
      <div>
        当前选择客户渠道归属：<strong>{{ channelType === 'OFFLINE' ? '线下' : '线上' }}</strong>
      </div>
      <div>批量导入线索：</div>
      <el-upload ref="upload" class="upload-demo" :headers="headers" :action="action" :show-file-list="false" accept=".xlsx,.xls,.csv" :before-upload="beforeUploadFile" :on-success="handleSuccess" :on-error="handleError" :on-change="fileChange" :limit="1">
        <el-button type="primary" :loading="uploadLoading">+上传文档</el-button>
      </el-upload>
      <div class="clueImport_operation_download" @click="downloadTemplate">
        <span>下载 《{{ channelTypeMap[channelType] }}-线索导入模板》<svg-icon icon-class="cloud-rain"></svg-icon></span>
      </div>
      <div class="clueImport_operation_download_tip">文件只支持上传大小为：5M以内，条数200条以内；文件格式支持：xlsx格式</div>
    </div>
    <div class="clueImport_info" v-show="isPreview">
      本次导入<span class="clueImport_info_num">{{ importDataCount }}</span
      >条记录，成功<span class="clueImport_info_num">{{ importDataSCount }}</span
      >条，失败<span class="clueImport_info_num">{{ importDataFCount }}</span
      >条<span class="clueImport_info_fail" v-if="importDataFCount" @click="downLoadFailRecord">下载失败记录</span>
    </div>
    <div class="clueImport__preview" v-if="groupId">
      <batchCreateClue :showOperation="false" :showDelete="false" deleteAllErorData="删除全部错误数据" v-show="isPreview" :channelType="channelType" :groupId="groupId" type="Table" ref="batchCreateClue" tipType="export"></batchCreateClue>
    </div>
  </div>
</template>

<script>
import download from '@/utils/download';
import batchCreateClue from './batchCreateClue';
import { publicExportExcel } from '@/api/common';
import { getHeaders, handleError } from '@/utils/request';
import options from './config';

export default {
  components: { batchCreateClue },
  data() {
    return {
      action: '',
      headers: getHeaders(),
      submitLoading: false,
      tableLoading: false,
      uploadLoading: false,
      exportLoading: false, // 导出缓冲
      isPreview: false,
      tableData: [],
      repeats: [],
      importDataCount: 0,
      importDataSCount: 0,
      importDataFCount: 0,
      fileList: [],
      busiSeq: '',

      formDatas: [],
      groupId: '',
      channelType: '',
      channelTypeMap: {
        ONLINE: '线上',
        OFFLINE: '线下'
      }
    };
  },
  created() {},
  mounted() {
    this.groupId = this.$route.query.groupId;
    this.channelType = this.$route.query.channelType || 'ONLINE';
    this.init();
  },
  methods: {
    init() {
      const template = options.getTeamOptions(this.groupId, this.channelType);
      this.action = process.env.VUE_APP_BASE_URL + template.uploadFileUrl + `?csGroupId=${this.groupId}`;
    },
    // 下载模板
    downloadTemplate() {
      const template = options.getTeamOptions(this.groupId, this.channelType);

      if (!template) {
        this.$message.warning('您不属于任何团队');
        return;
      }

      window.location.href = template.xlsx;
    },
    // 下载失败记录
    downLoadFailRecord() {
      const template = options.getTeamOptions(this.groupId, this.channelType);

      if (!template) {
        this.$message.warning('您不属于任何团队');
        return;
      }

      if (this.exportLoading && this.busiSeq) return;
      this.exportLoading = true;

      publicExportExcel(template.failFileUrl, {
        busiSeq: this.busiSeq
      })
        .then((res) => {
          download(res, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', `${this.channelTypeMap[this.channelType]}-线索导入结果下载.xlsx`);
        })
        .finally(() => {
          this.exportLoading = false;
        });
    },
    onUploadSuccess() {},
    onUploadError() {},
    // 上传文件之前的钩子, 参数为上传的文件,若返回 false 或者返回 Promise 且被 reject，则停止上传
    beforeUploadFile(file) {
      this.uploadLoading = true;

      const size = file.size / 1024 / 1024;
      if (size > 5) {
        this.$message.warning('文件大小不得超过5M');
        return false;
      }
      return true;
    },
    // 文件上传成功时的钩子
    handleSuccess(res, file, fileList) {
      this.uploadLoading = false;

      if (res.code === '0') {
        this.$message.success(res.msg);

        const { totalNum, successNum, failNum, dataDetails, busiSeq } = res.data;
        this.busiSeq = busiSeq;
        this.importDataCount = totalNum;
        this.importDataSCount = successNum;
        this.importDataFCount = failNum;
        this.$refs.batchCreateClue.setBatchFormData(dataDetails);
        this.isPreview = true;
      } else {
        this.$message.error(res.msg);
      }
      this.$refs.upload.clearFiles();
    },
    // 文件上传成功时的钩子
    handleError(err) {
      this.uploadLoading = false;
      handleError(err);
      this.$refs.upload.clearFiles();
    },
    // 文件状态改变时的钩子
    fileChange(file, fileList) {
      this.fileList.push(file.raw);
    }
  }
};
</script>

<style lang="scss" scoped>
.clueImport {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 20px 40px;
  &_step {
    font-size: 16px;
    display: flex;
    padding: 0;
    margin: 0 0 40px 0;
    text-align: center;
    align-items: center;
    & > li {
      list-style: none;
      padding: 0;
      margin: 0 100px 0 0;
      & > span {
        display: block;
        &:last-child {
          margin-top: 10px;
          font-size: 14px;
        }
      }
      &.clueImport_step_arrow {
        font-size: 48px;
      }
      &.clueImport_step_info {
        flex: none;
      }
    }
  }
  &_operation {
    display: flex;
    align-items: center;
    & > div {
      margin-right: 20px;
    }
    &_download {
      color: #5f3bce;
      cursor: pointer;
      &_tip {
        color: var(--color-info);
        font-size: 14px;
      }
    }
  }
  &_info {
    width: 100%;
    height: 40px;
    line-height: 40px;
    padding-left: 20px;
    border: 1px solid #ebeef5;
    background: #f0f2f6;
    margin: 20px 0 40px;
    &_num {
      color: var(--color-primary);
      padding: 0 4px;
    }
    &_fail {
      color: var(--color-danger);
      margin-left: 20px;
      cursor: pointer;
    }
  }
  &__preview {
    flex: 1;
    height: 0;
  }
  ::v-deep {
    .createForm {
      position: absolute;
      height: 44px;
      background-color: #fff;
      .form {
        height: 100%;
        .el-form {
          height: 100%;
          .el-form-item {
            margin-left: 0;
            margin-bottom: 0;
            display: flex;
            align-items: center;
          }
        }
      }
      .checkFormDataRepeat {
        transform: translate(-100%, -50%);
        top: 50%;
        left: 30px;
      }
    }
  }
}
</style>
