<template>
  <div class="table-container clue">
    <!-- 搜索表单 -->
    <filter-form :options="options2.FilterFormOptions" @query="onSubmit" ref="filterForm"></filter-form>

    <!-- 操作栏 -->
    <action-bar style="padding: 10px 0" v-bind="_operateOptions" @actionBarClick="onBarOptions"></action-bar>

    <!-- 列表展示 -->
    <ListExhibition ref="listExhibition" @getData="getData" v-model="multipleSelection" v-bind="tableOptions" :tableOptions="{ 'span-method': clueSpanMethod }" v-el-horizontal-scroll>
      <template slot="table_cell_lastFollowDate" slot-scope="{ scope }">
        {{ scope.row.lastFollowDate_handle }}
        <svg-icon icon-class="liebiao" v-if="scope.row.lastFollowDate_handle" @click="setDialogListFollowUpVisible({ params: scope.row })"></svg-icon>
      </template>
      <!-- 操作栏 -->
      <el-table-column label="操作" align="center" fixed="right" width="120">
        <template slot-scope="scope">
          <action-bar v-bind="_tableOperateOptions" :record="scope.row" @actionBarClick="({ id }) => actionBarClick(id, scope.row)"></action-bar>
        </template>
      </el-table-column>
    </ListExhibition>

    <!-- 跟进记录列表弹框 -->
    <el-dialog :visible.sync="dialogListFollowUpVisible" :title="options2.Dialog_ListFollowUp.title" width="35%" center @opened="$refs.listFollowUp_listExhibition.resetData()">
      <!-- 操作栏 -->
      <BatchOperation :operateList="options2.Dialog_ListFollowUp.ActionBar.items" @operateClick="operateClick"></BatchOperation>
      <ListExhibition ref="listFollowUp_listExhibition" @getData="getData_FollowUpList" v-bind="options2.Dialog_ListFollowUp.Table">
        <!-- 列表操作栏 -->
        <el-table-column label="操作" fixed="right">
          <template slot-scope="scope">
            <Authority :auth="options2.Authority.followUp">
              <el-button @click="operateClick({ id: 'editFollwUpToClue' }, scope.row)" type="text">编辑</el-button>
            </Authority>
          </template>
        </el-table-column>
      </ListExhibition>
    </el-dialog>

    <!-- 表单提交弹框 -->
    <el-dialog :visible.sync="dialogSubmitFormVisible" :title="dialogSubmitFormOptions.title" :width="dialogSubmitFormOptions.width || '35%'" center @opened="$refs.dialogSubmitForm.init()" @closed="$refs.dialogSubmitForm.clear()">
      <SubmitForm :formItems="dialogSubmitFormOptions.items" :colNum="dialogSubmitFormOptions.colNum" :basicformData="dialogSubmitFormData" ref="dialogSubmitForm" @onSubmit="onSubmit_dialog">
        <el-button @click="dialogSubmitFormVisible = false" size="small">取消</el-button>
        <el-button @click="$refs.dialogSubmitForm.onSubmit()" size="small" :loading="dialogLoading" type="primary">保存</el-button>
      </SubmitForm>
    </el-dialog>
    <!-- 选择渠道和团队 -->
    <SelectChannelAndGroup :visible.sync="selectChannelVisible" @confirm="selecChannelType" />
  </div>
</template>

<script>
import FilterForm from '@/components/Form/FilterForm';
import SubmitForm from '@/components/Common/SubmitForm/index.vue';
import ListExhibition from '@/components/Common/ListExhibition';
import ActionBar from '@/components/Common/ActionBar';
import BatchOperation from '@/components/BatchOperation';
import options2 from './config/list.js';
import pickBy from 'lodash/pickBy';
import { getClueList, getClueDetail, createLeadsManagerInBatch, postClueDelete, updateLeadsManager, getListLeadsManager, improveClueMobile } from '@/api/customer-management/clue.js';
import { mapGetters } from 'vuex';
import { _validateContact } from '@/common/validator';
import dict from '@/components/Common/dicts';
import SelectChannelAndGroup from '@/components/SelectChannelAndGroup';

export default {
  name: 'customer-management-customer-clue-management-list',
  components: {
    FilterForm,
    ActionBar,
    BatchOperation,
    ListExhibition,
    SubmitForm,
    SelectChannelAndGroup
  },
  data() {
    return {
      tableData: [],
      multipleSelection: [], // 选择的数据
      distributorIds: [], // 选择的分销商
      options2: options2,

      dialogListFollowUpVisible: false,
      listFollowUpRow: {},

      dialogSubmitFormVisible: false, // 表单弹框
      dialogSubmitFormId: '',
      dialogSubmitFormData: {},
      loading: false,
      dialogLoading: false,
      selectChannelVisible: false,
      importType: 'add' // add 新增，batchImport 批量导入
    };
  },
  activated() {
    this.onSubmit();
  },
  async created() {
    this._operateOptions = options2.Operate;
    this._tableOperateOptions = options2.TableOperate;
  },
  computed: {
    ...mapGetters(['userGroup']),
    dialogFollowUpTitle() {
      return (this.dialogFollowUpStatus ? '编辑' : '添加') + this.options2.Dialog.operateFollwUp.title;
    },
    dialogSubmitFormOptions() {
      return this.dialogSubmitFormId ? this.options2.Dialog[this.dialogSubmitFormId] : {};
    },
    tableOptions() {
      const items = [...options2.Table.items];
      return {
        ...options2.Table,
        items
      };
    }
  },
  methods: {
    // 列表查询
    onSubmit() {
      this.$refs.listExhibition.refreshData();
    },
    // 列表获取数据
    getData({ pageNo, pageSize, callback }) {
      const formData = this.$refs.filterForm.getParams();
      const params = pickBy(formData, (val) => !!val);

      let data;
      return getClueList({
        data: params,
        pageNo,
        pageSize
      })
        .then((res) => {
          data = res.data;

          res.data.list = res.data.list.reduce((pre, cur) => {
            const channelClues = cur.distributorLeadsChannelVOList || [cur];
            pre.push(
              ...channelClues.map((i, index) => ({
                ...cur,
                ...i,
                id: cur.id,
                mobile: cur.mobile,
                developCsName: i.developCsName,
                platformShopId: cur.platformShopId,
                _isMerge: !index,
                _mergeNum: channelClues.length
              }))
            );
            return pre;
          }, []);
        })
        .finally(() => {
          callback(data);
        });
    },
    // 跟进记录列表数据
    getData_FollowUpList({ pageNo, pageSize, callback }) {
      let data;
      return getListLeadsManager({
        data: {
          distributorExtId: this.listFollowUpRow.id
        },
        pageNo,
        pageSize
      })
        .then((res) => {
          data = res.data;
        })
        .finally(() => {
          callback(data);
        });
    },

    // 添加线索
    batchAddClue() {
      this.importType = 'add';
      this.selectChannelVisible = true;
    },

    // 批量导入线索
    batchImportClue() {
      this.$router.push({
        path: `/customer-management/customer-clue-management/import`
      });
    },

    // 弹框选择团队
    async selectGroup() {
      const options = await dict('COMMON_TEAM_CLUETEMPLATE');

      const selectTeam = {};
      const vm = (
        <el-select
          value={selectTeam}
          valueKey="value"
          on-input={(v) => {
            Object.assign(selectTeam, v);
            vm.componentInstance.setSelected();
          }}
          placeholder="请选择团队"
        >
          {options.map((i) => (
            <el-option label={i.label} value={i} key={i.value}></el-option>
          ))}
        </el-select>
      );

      return this.$msgbox({
        title: '选择团队',
        message: vm,
        showCancelButton: true,
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      })
        .then(() => selectTeam.value)
        .catch(() => void 0);
    },

    // 添加线索跟进
    addFollowUp() {
      if (!this.mixins_requireChoose(this.multipleSelection)) return;

      this.updateFollowUpOptions({
        params: { _distributorIds: this.multipleSelection.map((i) => i.id) }
      });
    },

    // 批量导入跟进
    batchImportFollowUp() {
      this.$router.push({
        path: '/customer-management/customer-clue-management/follow-up-import'
      });
    },
    actionBarClick(id, row) {
      this[id] && this[id](row);
    },
    // 店铺跳转
    shopPageJump: ({ platformShopLink }) => window.open(platformShopLink),
    // 线索详情
    clueDetail(row) {
      this.$router.push({
        path: `/customer-management/customer-clue-management/info/${row.id}`
      });
    },
    // 线索编辑
    clueEdit(row) {
      this.$router.push({
        path: `/customer-management/customer-clue-management/edit/${row.id}`
      });
    },
    // 线索删除
    clueDelete(row) {
      this.mixins_confirm({
        name: '删除',
        resolve: () => {
          return postClueDelete([row.id]).then(() => {
            this.onSubmit();
          });
        }
      });
    },
    // 国际线索生成分销商
    async createDistributor({ id }) {
      const { data = {} } = await getClueDetail(id);

      if (data.status === 'NOT_REGISTER') {
        this.$router.push({
          path: '/distributor-management/distributor/add',
          query: {
            id: id
          }
        });
      } else {
        this.$message.info('选中的线索已经注册，无法再次创建分销商');
      }
    },
    // 完善联系方式
    perfectContact({ id }) {
      this.$prompt('请输入联系方式', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValidator: (v) => {
          if (!v) return '请输入联系方式';

          let msg;
          _validateContact(null, v, (s) => {
            msg = s;
          });
          return !msg;
        }
      })
        .then(async ({ value: mobile }) => {
          const { data = {} } = await getClueDetail(id);

          // 判断是否重复 - 完善时 可能被其他人完善，需要校验
          if (!data.mobile) {
            const res = await improveClueMobile({
              mobile,
              id
            });

            this.$message.success(res.msg);
          } else {
            this.$message.info('选中的线索已经存在联系方式');
          }

          this.onSubmit();
        })
        .catch(() => {});
    },

    // 操作栏点击
    async operateClick({ id }, params) {
      switch (id) {
        case 'addFollwUpToClue':
          this.updateFollowUpOptions({
            params: { _distributorIds: [this.listFollowUpRow.id] }
          });
          this.dialogListFollowUpVisible = false;
          break;
        case 'editFollwUpToClue':
          this.updateFollowUpOptions({
            params,
            isAdd: false
          });
          this.dialogListFollowUpVisible = false;
          break;
      }
    },
    setDialogListFollowUpVisible({ params = {}, visible = true }) {
      visible && (this.listFollowUpRow = params);

      this.dialogListFollowUpVisible = visible;
    },
    setDialogSubmitFormDVisible({ params = {}, id, visible = true }) {
      if (visible) {
        this.dialogSubmitFormData = params;
        this.dialogSubmitFormId = id;
      }
      this.dialogSubmitFormVisible = visible;
    },
    // 跟进记录配置修改 根据添加/编辑
    updateFollowUpOptions({ params = {}, isAdd = true } = {}) {
      const id = 'operateFollwUp';

      // 不能被编辑的表单项
      this.options2.Dialog[id].items.forEach((i) => {
        if (['expectedFinishDate', 'label', 'followWay', 'followResult'].includes(i.key)) {
          i.disabled = !isAdd;
        }

        if (i.key === 'label') {
          i.selectOptions = isAdd ? dict('CLUE_FOLLOWUP_ITEM') : dict('CLUE_FOLLOWUP_ITEM_ALL');
        }
      });

      this.setDialogSubmitFormDVisible({
        params,
        id
      });
    },
    // 操作跟进记录
    onSubmit_operateFollwUp({ formData, callback }) {
      let axiosInterface;
      if (this.dialogSubmitFormData._distributorIds) {
        formData.distributorIds = this.dialogSubmitFormData._distributorIds;
        axiosInterface = createLeadsManagerInBatch;
      } else {
        // 编辑跟进
        formData.id = this.dialogSubmitFormData.id;
        axiosInterface = updateLeadsManager;
      }
      // 请求接口
      axiosInterface(formData).finally(() => {
        this.onSubmit();
        callback();
      });
    },
    onSubmit_dialog(formData) {
      this.dialogLoading = true;
      this[`onSubmit_${this.dialogSubmitFormId}`]({
        formData,
        basicData: this.dialogSubmitFormData,
        callback: () => {
          this.dialogLoading = false;
          this.dialogSubmitFormVisible = false;
        }
      });
    },
    // element 合并列表列规则
    clueSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex < 5 || columnIndex >= this.options2.Table.items.length) {
        if (row._isMerge) {
          return {
            rowspan: row._mergeNum,
            colspan: 1
          };
        } else {
          return {
            rowspan: 0,
            colspan: 0
          };
        }
      }
    },
    onBarOptions(item) {
      const { id } = item;
      if (id === 'batchImportClue') {
        this.importType = 'batchImport';
        this.selectChannelVisible = true;
        return;
      }
      this[id] && this[id]();
    },
    selecChannelType({ channelType, groupId }) {
      if (this.importType === 'batchImport') {
        this.$router.push(`/customer-management/customer-clue-management/import?channelType=${channelType}&groupId=${groupId}`);
      }
      if (this.importType === 'add') {
        this.$router.push(`/customer-management/customer-clue-management/create?channelType=${channelType}&groupId=${groupId}`);
      }
    }
  }
};
</script>

<style lang="scss" scoped></style>
