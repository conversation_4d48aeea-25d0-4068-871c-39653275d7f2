<template>
  <submit-form v-loading="loading" :model="form" :options="formOptions" ref="submitForm" @onSubmit="onSubmit" @onCancel="$emit('onCancel')" valid-fail-scrolled>
    <template slot="formItem_mobile">
      <CryptoBlock tag-type="input" :reset="!loading" :disabled="!isOffline" :bizId="id || distributorExtId" :detail-auth="authority.mobile" :edit-auth="authority.contact" :bizType="id ? 'DISTRIBUTOR_LEADS_CHANNEL_MOBILE' : 'DISTRIBUTOR_LEADS_CONTACT_PHONE'" v-model.trim="form.mobile" />
    </template>
  </submit-form>
</template>

<script>
import Options from '../../config';
import { FormOptions, FormOptionsOffline } from './config';
import SubmitForm from '@/components/Form/SubmitFormV2';
import { getGroupLabel } from '@/components/Common/SelectGroup';
import { getChannelClue } from '@/api/customer-management/clue.js';
import { createChannelClue, updateChannelClue } from '@/api/customer-management/clue.js';
import dict from '@/components/Common/dicts';
import pick from 'lodash/pick';

export default {
  // 渠道线索操作表单组件
  name: 'customer-clue-management-channelClueForm',
  components: { SubmitForm },
  data() {
    return {
      form: {},
      formOptions: [],
      loading: false,
      disabledMobile: true
    };
  },
  props: {
    id: String,
    mobile: String, // 创建时使用
    status: String, // 编辑时使用
    distributorExtId: String, // 线索id
    // 线索渠道渠道归属
    customerChannelKind: {
      type: String,
      default: 'ONLINE'
    },
    groupId: {
      type: String,
      require: true
    }
  },
  computed: {
    authority() {
      return Options.Authority;
    },
    isOffline() {
      const { customerChannelKind = '' } = this;
      return customerChannelKind === 'OFFLINE';
    }
  },
  created() {
    // 这里不使用computed获取formOptions是为了能监听配置项中具体属性的变化如：rules.required等
    this.initFormOptions();
  },
  methods: {
    initFormOptions() {
      console.log('initFormOptions', this.customerChannelKind);
      if (this.groupId) {
        this.formOptions = this.isOffline ? FormOptionsOffline : FormOptions[getGroupLabel(this.groupId)];
      } else {
        this.formOptions = [];
      }
    },
    init() {
      this.disabledMobile = true;
      this.id ? this.getData() : (this.form.mobile = this.mobile);

      const developChannelOptions = this.formOptions.find((i) => i.prop === 'developChannel');
      if (developChannelOptions) developChannelOptions.disabled = !!this.id;
    },
    clear() {
      this.$refs.submitForm.perfectForm(true);
    },
    getData() {
      if (!this.id) return;

      this.loading = true;
      getChannelClue(this.id)
        .then(async (res) => {
          // 线索这里使用的店铺平均月销为[采货方式：一件代发]的选项值（月销量...），但存在商家端入驻创建[采货方式：采销]时选择的店铺平均月销（月销金额..），询问产品告知此匹配不上的情况需要将其置空处理；
          if (res.data.monthlySales) {
            try {
              const monthlySaleOptions = await dict('SHOP_AVERAGE_MONTHSALE');
              if (!monthlySaleOptions.find((i) => i.value === res.data.monthlySales)) res.data.monthlySales = '';
            } catch (e) {
              console.dir(e);
            }
          }

          Object.assign(this.form, pick(res.data, Object.keys(this.form)));
          console.log('form', this.form);
          this.disabledMobile = !!this.form.mobile || this.status === 'PASS';
        })
        .finally(() => {
          this.loading = false;
        });
    },
    onSubmit(cb) {
      const request = this.id ? updateChannelClue : createChannelClue;
      request({
        ...this.form,
        id: this.id,
        distributorExtId: this.distributorExtId
      })
        .then(() => {
          this.$message.success('操作成功');
          this.$emit('onSubmit');
        })
        .finally(cb);
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep {
  .form-content {
    display: flex;
    flex-wrap: wrap;
    & > div {
      width: 33%;
      display: flex;
      .el-form-item__label {
        width: 120px;
      }
      .el-form-item__content {
        flex: 1;
        .el-select {
          width: 100%;
        }
      }
    }
  }
}
</style>
