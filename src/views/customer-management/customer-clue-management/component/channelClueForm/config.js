import dict from '@/components/Common/dicts';
import { _validateContact } from '@/common/validator';

const TaoBaoTypes = ['TAOBAO', 'TMALL'];
// 线上
// 水羊直供团队
const ZG_FormOptions = [
  {
    prop: 'developChannel',
    component: 'select',
    label: '拓展渠道',
    disabled: false,
    options: dict('COMMON_EXPAND_CHANNEL'),
    rules: [
      {
        required: true,
        message: '请选择拓展渠道'
      }
    ]
  },
  // 店铺名称
  {
    prop: 'platformShopName',
    component: 'input',
    label: '店铺名称',
    maxlength: 100,
    rules: [
      {
        required: true,
        message: '请输入店铺名称'
      }
    ]
  },
  // 店铺ID
  {
    prop: 'platformShopId',
    component: 'input',
    label: '店铺ID',
    maxlength: 100,
    rules: [
      {
        required: false,
        message: '请输入店铺ID'
      }
    ],
    // '渠道为淘宝，天猫时必须填写'
    watcher(val, proxyModel, options) {
      const channel = proxyModel['developChannel'];

      options.rules[0].required = TaoBaoTypes.includes(channel);
    }
  },
  // 行业评级
  {
    prop: 'industryLevel',
    component: 'select',
    label: '行业评级',
    options: dict('COMMON_TRADE_LEVEL'),
    rules: [
      {
        required: true,
        message: '请选择行业评级'
      }
    ]
  },
  // 联系人
  {
    prop: 'contactName',
    component: 'input',
    label: '联系人',
    maxlength: 100,
    rules: [
      {
        required: false,
        message: '请输入联系人'
      }
    ],
    // 渠道非淘宝，天猫时必须填写
    watcher(val, proxyModel, options) {
      const channel = proxyModel['developChannel'];

      channel && (options.rules[0].required = !TaoBaoTypes.includes(channel));
    }
  },
  // 联系方式
  {
    prop: 'mobile',
    label: '联系方式',
    maxlength: 100,
    rules: [
      {
        required: false,
        message: '请输入联系电话'
      }
    ],
    // 渠道非淘宝，天猫时必须填写
    watcher(val, proxyModel, options) {
      const channel = proxyModel['developChannel'];

      channel && (options.rules[0].required = !TaoBaoTypes.includes(channel));
    }
  },
  // 主营类目
  {
    prop: 'businessCategoryList',
    component: 'select',
    label: '主营类目',
    options: dict('COMMON_CATEGORY'),
    multiple: true,
    collapseTags: true,
    rules: [
      {
        required: true,
        message: '请选择主营类目'
      }
    ]
  },
  // 拓展关键词
  {
    prop: 'keyWord',
    component: 'input',
    label: '拓展关键词',
    maxlength: 100,
    rules: [
      {
        required: true,
        message: '请输入拓展关键字'
      }
    ]
  },
  // 拓展品牌
  {
    prop: 'favoriteBrandList',
    component: 'select',
    label: '拓展品牌',
    options: dict('COMMON_EXPAND_BRAND'),
    multiple: true,
    collapseTags: true,
    rules: [
      {
        required: true,
        message: '请选择拓展品牌'
      }
    ]
  },
  // 企业/个人
  {
    prop: 'merchantType',
    component: 'select',
    label: '商家类型',
    options: dict('COMMON_ENTERPRISE_NATURE'),
    rules: [
      {
        required: true,
        message: '请选择企业/个人'
      }
    ]
  },
  // 店铺等级/类型
  {
    prop: 'platformShopLevel',
    component: 'select',
    label: '店铺等级/类型',
    options: [],
    watcher(val, proxyModel) {
      const channel = proxyModel['developChannel'];

      if (channel) {
        const channels = ['TAOBAO'];
        const dictName = channels.includes(channel) ? 'SHOP_LEVEL' : 'SHOP_TYPE';
        dict(dictName).then((data) => this.setItemOptions(data));
      }
    }
  },
  // 偏好品牌
  {
    prop: 'interestedBrandList',
    component: 'select',
    label: '偏好品牌',
    options: dict('COMMON_PREFER_BRAND'),
    multiple: true,
    collapseTags: true
  },
  // 店铺描述
  {
    prop: 'description',
    component: 'input',
    type: 'textarea',
    rows: 3,
    maxlength: 500,
    trimed: false,
    showWordLimit: true,
    label: '店铺描述'
  },
  // 企业名称
  {
    prop: 'company',
    component: 'input',
    label: '企业名称',
    maxlength: 100,
    rules: [
      {
        required: false,
        message: '请输入企业名称'
      }
    ],
    // 渠道非淘宝，天猫时必须填写
    watcher(val, proxyModel, options) {
      options.rules[0].required = proxyModel['merchantType'] === 'ENTERPRISE';
    }
  },
  // 企业法人
  {
    prop: 'companyLegalName',
    component: 'input',
    label: '企业法人',
    maxlength: 100
  },
  // 企业联系方式
  {
    prop: 'companyLegalMobile',
    component: 'input',
    label: '企业联系方式',
    maxlength: 100,
    rules: [
      {
        validator: _validateContact,
        trigger: 'blur'
      }
    ]
  },
  // 企业规模
  {
    prop: 'companyScale',
    component: 'select',
    label: '企业规模',
    options: dict('COMMON_ENTERPRISE_SCALE')
  },
  // 微信ID
  {
    prop: 'wechat',
    component: 'input',
    label: '微信ID',
    maxlength: 100
  }
];
// 水羊国际团队
const GJ_FormOptions = [
  // 拓展渠道
  {
    prop: 'developChannel',
    component: 'select',
    label: '拓展渠道',
    disabled: false,
    options: dict('COMMON_EXPAND_CHANNEL'),
    rules: [
      {
        required: true,
        message: '请选择拓展渠道'
      }
    ]
  },
  // 店铺名称
  {
    prop: 'platformShopName',
    component: 'input',
    label: '店铺名称',
    maxlength: 100,
    rules: [
      {
        required: true,
        message: '请输入店铺名称'
      }
    ]
  },
  // 店铺ID
  {
    prop: 'platformShopId',
    component: 'input',
    label: '店铺ID',
    maxlength: 100,
    rules: [
      {
        required: true,
        message: '请输入店铺ID'
      }
    ]
  },
  // 行业评级
  {
    prop: 'industryLevel',
    component: 'select',
    label: '行业评级',
    options: dict('COMMON_TRADE_LEVEL'),
    rules: [
      {
        required: true,
        message: '请选择行业评级'
      }
    ]
  },
  // 主营类目
  {
    prop: 'businessCategoryList',
    component: 'select',
    label: '主营类目',
    options: dict('COMMON_CATEGORY'),
    multiple: true,
    collapseTags: true,
    rules: [
      {
        required: true,
        message: '请选择主营类目'
      }
    ]
  },
  // 店铺链接
  {
    prop: 'platformShopLink',
    component: 'input',
    label: '店铺链接'
  },
  // 店铺等级/类型
  {
    prop: 'platformShopLevel',
    component: 'select',
    label: '店铺等级/类型',
    options: [],
    watcher(val, proxyModel) {
      const channel = proxyModel['developChannel'];

      if (channel) {
        const channels = ['TAOBAO'];
        const dictName = channels.includes(channel) ? 'SHOP_LEVEL' : 'SHOP_TYPE';
        dict(dictName).then((data) => this.setItemOptions(data));
      }
    }
  },
  // 推广属性
  {
    prop: 'promoteChannel',
    component: 'select',
    label: '推广属性',
    options: dict('COMMON_PROMOTION_NATURE'),
    multiple: true
  },
  // 店铺平均客单
  {
    prop: 'averageCustomerPrice',
    component: 'select',
    options: dict('SHOP_AVERAGE_CUSTOMERORDER'),
    label: '店铺平均客单价'
  },
  // 店铺平均月销
  {
    prop: 'monthlySales',
    component: 'select',
    options: dict('SHOP_AVERAGE_MONTHSALE'),
    label: '店铺平均月销'
  },
  // 偏好品牌
  {
    prop: 'interestedBrandList',
    component: 'select',
    label: '偏好品牌',
    options: dict('COMMON_PREFER_BRAND'),
    multiple: true,
    collapseTags: true
  },
  // 店铺描述
  {
    prop: 'description',
    component: 'input',
    type: 'textarea',
    rows: 3,
    maxlength: 500,
    trimed: false,
    showWordLimit: true,
    label: '店铺描述'
  },
  // 联系人
  {
    prop: 'contactName',
    component: 'input',
    label: '联系人',
    maxlength: 100
  },
  // 联系人岗位
  {
    prop: 'contactPost',
    component: 'input',
    label: '联系人岗位',
    maxlength: 100
  },
  // 联系方式
  {
    prop: 'mobile',
    // component: 'input',
    label: '联系方式',
    maxlength: 100
  },
  // 微信ID
  {
    prop: 'wechat',
    component: 'input',
    label: '微信ID',
    maxlength: 100
  },
  // 企业名称
  {
    prop: 'company',
    component: 'input',
    label: '企业名称',
    maxlength: 100
  },
  // 企业法人
  {
    prop: 'companyLegalName',
    component: 'input',
    label: '企业法人',
    maxlength: 100
  },
  // 企业联系方式
  {
    prop: 'companyLegalMobile',
    component: 'input',
    label: '企业联系方式',
    maxlength: 100,
    rules: [
      {
        validator: _validateContact,
        trigger: 'blur'
      }
    ]
  },
  // 企业规模
  {
    prop: 'companyScale',
    component: 'select',
    label: '企业规模',
    options: dict('COMMON_ENTERPRISE_SCALE')
  }
];

// 线下
export const FormOptionsOffline = [
  {
    prop: 'developChannel',
    component: 'select',
    label: '拓展渠道',
    disabled: false,
    options: dict('COMMON_EXPAND_CHANNEL'),
    rules: [
      {
        required: true,
        message: '请选择拓展渠道'
      }
    ]
  },
  // 客户名称
  {
    prop: 'platformShopName',
    component: 'input',
    label: '客户名称',
    maxlength: 100,
    rules: [
      {
        required: true,
        message: '请输入客户名称'
      }
    ]
  },
  // 客户业务类型
  {
    prop: 'businessTypeAndExtend',
    component: 'select',
    label: '客户业务类型',
    options: window.$vue.$dict['distributor_business_type_and_extend'],
    rules: [
      {
        required: true,
        message: '请选择客户业务类型'
      }
    ]
  },
  // 主营类目
  {
    prop: 'businessCategoryList',
    component: 'select',
    label: '主营类目',
    options: dict('COMMON_CATEGORY'),
    multiple: true,
    collapseTags: true,
    rules: [
      {
        required: true,
        message: '请选择主营类目'
      }
    ]
  },
  // 偏好品牌
  {
    prop: 'interestedBrandList',
    component: 'select',
    label: '偏好品牌',
    options: dict('COMMON_PREFER_BRAND'),
    multiple: true,
    collapseTags: true
  },
  // 联系人
  {
    prop: 'contactName',
    component: 'input',
    label: '联系人',
    maxlength: 100
  },
  // 联系人岗位
  {
    prop: 'contactPost',
    component: 'input',
    label: '联系人岗位',
    maxlength: 100
  },
  // 联系人手机号
  {
    prop: 'mobile',
    // component: 'input',
    label: '联系人手机号'
  },
  // 微信ID
  {
    prop: 'wechat',
    component: 'input',
    label: '微信ID',
    maxlength: 100
  },
  // 店铺平均客单
  {
    prop: 'averageCustomerPrice',
    component: 'select',
    options: dict('SHOP_AVERAGE_CUSTOMERORDER'),
    label: '店铺平均客单价'
  },
  // 店铺数量
  {
    prop: 'shopQuantity',
    component: 'input',
    label: '店铺数量',
    append: '个'
  },
  // 客户描述
  {
    prop: 'description',
    component: 'input',
    type: 'textarea',
    rows: 3,
    maxlength: 500,
    trimed: false,
    showWordLimit: true,
    label: '客户描述'
  },
  // 店铺位置
  {
    prop: 'shopAddress',
    component: 'input',
    type: 'textarea',
    rows: 3,
    maxlength: 500,
    trimed: false,
    showWordLimit: true,
    label: '店铺位置'
  }
];

// 除了直供，其他都用国际
export const FormOptions = {
  ZG: ZG_FormOptions,
  CP: GJ_FormOptions,
  GJ: GJ_FormOptions,
  GF: GJ_FormOptions
};
