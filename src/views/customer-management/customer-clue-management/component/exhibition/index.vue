<template>
  <div class="detail" v-loading="loading">
    <el-descriptions title="用户信息" :column="2" label-class-name="description-item" size="medium">
      <el-descriptions-item :label="i.label" v-for="i in detailItems" :key="i.key" :labelStyle="{ color: '' }">
        <!-- 联系方式 -->
        <CryptoBlock v-if="i.key === 'mobile'" v-model.trim="clueDetail.mobile" :detail-auth="authority.mobile" :bizId="id" bizType="DISTRIBUTOR_LEADS_MOBILE" />
        <!-- 标签 -->
        <customer-tag v-else-if="i.key === 'tag'" :isEdit="isEdit" :distributorExtId="clueDetail.id" :csId="clueDetail.developCsId" :wechatId="clueDetail.enterpriseWechat"> </customer-tag>
        <!-- 默认显示 -->
        <template v-else>
          {{ (i.formatter ? i.formatter(clueDetail[i.key]) : clueDetail[i.key]) || '--' }}
        </template>
      </el-descriptions-item>
    </el-descriptions>

    <div class="detail-title">拓店记录</div>

    <action-bar v-if="isEdit" style="margin: 16px 0" :itemOptions="actionBarItems" @actionBarClick="({ id }) => this[id] && this[id]()"></action-bar>

    <table-exhibition :options="tableOptions" :table="tableData" ref="table" style="margin-top: 20px" :show-pagination="false" v-if="groupId">
      <el-table-column label="操作" width="100" slot="column_append" fixed="right" v-if="isEdit">
        <template slot-scope="scope">
          <action-bar :itemOptions="tableActionBarItems" :record="scope.row" @actionBarClick="({ id }, row) => _self[id](row)"></action-bar>
        </template>
      </el-table-column>
    </table-exhibition>

    <!-- 操作渠道线索弹框 -->
    <el-dialog :visible.sync="channelClueFormDialogVisible" :title="channelClueFormDialogTitle" width="75%" @opened="$refs.channelClueForm.init()" @closed="$refs.channelClueForm.clear()">
      <channel-clue-form
        :customerChannelKind="channelType"
        :groupId="groupId"
        :id="channelClueId"
        :mobile="clueDetail.mobile"
        :status="clueDetail.status"
        :distributorExtId="id"
        ref="channelClueForm"
        @onSubmit="channelClueFormOnSubmit"
        @onCancel="channelClueFormDialogVisible = false"
      ></channel-clue-form>
    </el-dialog>
  </div>
</template>

<script>
import { getClueDetail, deleteChannelClue } from '@/api/customer-management/clue.js';
import Options from '../../config';
import { TableOptions } from './config';
import customerTag from '@/views/distributor-management/component/customer-tag';
import moment from 'moment';
import ActionBar from '@/components/Common/ActionBar';
import TableExhibition from '@/components/Table/TableExhibition';
import { selectGroup, checkGroup, getGroupLabel } from '@/components/Common/SelectGroup';
import ChannelClueForm from '../channelClueForm';

export default {
  // 线索详情展示组件
  name: 'customer-clue-management-exhibition',
  components: { ActionBar, TableExhibition, customerTag, ChannelClueForm },
  data() {
    return {
      id: '',
      loading: false,
      clueDetail: {},
      groupId: '',
      channelClueId: '',
      channelClueFormDialogVisible: false,
      channelType: ''
    };
  },
  props: {
    isEdit: Boolean
  },
  computed: {
    authority() {
      return Options.Authority;
    },
    tableData() {
      return this.clueDetail?.distributorLeadsChannelVOList ?? [];
    },
    detailItems() {
      return [
        {
          key: 'createDate',
          label: '创建日期',
          formatter: (v) => moment(v).format('YYYY-MM-DD')
        },
        {
          key: 'statusName',
          label: '线索状态'
        },
        {
          key: 'mobile',
          label: '联系方式'
        },
        {
          key: 'channelNames',
          label: '拓展渠道',
          formatter: (v = []) => v.join(',')
        },
        {
          key: 'interestedBrandNames',
          label: '偏好品牌',
          formatter: (v = []) => v.join(',')
        },
        {
          key: 'businessCategoryNames',
          label: '主营类目',
          formatter: (v = []) => v.join(',')
        },
        {
          key: 'tag',
          label: '标签'
        }
      ];
    },
    actionBarItems() {
      return [
        /* {
          id: 'addChannelClue',
          label: '添加',
          authority: this.authority.add,
          loadCondition: () => checkGroup(this.groupId, 'ZG')
        } */
      ];
    },
    tableOptions() {
      if (this.channelType === 'OFFLINE') {
        return TableOptions.OFFLINE;
      }
      return this.groupId ? TableOptions[getGroupLabel(this.groupId)] : [];
    },
    tableActionBarItems() {
      return [
        {
          id: 'editChannelClue',
          label: '编辑',
          authority: this.authority.edit,
          isWrap: true,
          button: {
            type: 'text'
          }
        },
        {
          id: 'deleteChannelClue',
          label: '删除',
          authority: this.authority.delete,
          loadCondition: () => this.tableData.length > 1,
          isWrap: true,
          button: {
            type: 'text'
          }
        }
      ];
    },
    channelClueFormDialogTitle() {
      return `${this.channelClueId ? '编辑' : '添加'}渠道线索`;
    }
  },
  mounted() {
    this.init();
  },
  methods: {
    async init() {
      this.id = this.$route.params.id;

      this.loading = true;
      let res;
      try {
        res = await getClueDetail(this.id);
      } catch (error) {
        console.dir(error);
      }
      this.loading = false;
      this.clueDetail = res.data || {};
      this.channelType = this.clueDetail.channelType;
      // 判断线索本身所属团队
      if (this.clueDetail.csGroupId && checkGroup(this.clueDetail.csGroupId)) {
        this.groupId = this.clueDetail.csGroupId;
      } else {
        // 判断当前用户所属团队
        selectGroup((groupId) => {
          if (groupId) {
            this.groupId = groupId;
          } else {
            this.onCancal();
          }
        }).apply(this);
      }
    },
    // 添加渠道线索
    addChannelClue() {
      if (!this.clueDetail.mobile) {
        this.$message.warning('请先去完善联系方式');
        return;
      }

      this.editChannelClue();
    },
    // 编辑渠道线索
    editChannelClue({ id } = {}) {
      this.channelClueId = id;
      this.channelClueFormDialogVisible = true;
    },
    // 删除渠道线索
    deleteChannelClue({ id }) {
      this.$confirm('是否确认删除？')
        .then(() => {
          deleteChannelClue(id)
            .then(() => {
              this.$message.success('操作成功');
            })
            .finally(this.init);
        })
        .catch(() => {
          this.$message.info('取消操作');
        });
    },
    // 渠道线索表单提交回调
    channelClueFormOnSubmit() {
      this.init();

      this.channelClueFormDialogVisible = false;
    },
    onCancal() {
      this.$back('/customer-management/customer-clue-management/list');
    }
  }
};
</script>

<style lang="scss" scoped>
.detail {
  &-title {
    font-size: 16px;
    font-weight: 700;
    color: #303133;
  }
}

::v-deep {
  .el-descriptions-item__container {
    margin-bottom: 10px;
    align-items: center;

    .description-item {
      font-weight: 700;
      font-size: 14px;
      color: #000;
    }
  }
}
</style>
