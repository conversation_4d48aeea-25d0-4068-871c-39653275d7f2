import { parseTime } from '@/utils';

const ZG_TableOptions = [
  {
    prop: 'createByName',
    label: '添加人',
    width: 80,
    fixed: 'left'
  },
  {
    prop: 'createDate',
    label: '添加时间',
    width: 100,
    formatter: (r, c, v) => (v ? parseTime(v, '{y}-{m}-{d}') : '--')
  },
  {
    prop: 'developChannelName',
    label: '拓展渠道',
    width: 100
  },
  // 店铺描述
  {
    prop: 'description',
    label: '店铺描述',
    width: 160,
    showOverflowTooltip: true
  },
  {
    prop: 'platformShopId',
    label: '店铺ID',
    width: 160
  },
  {
    prop: 'industryLevel',
    label: '行业评级',
    width: 100
  },
  {
    prop: 'platformShopName',
    label: '店铺名称',
    width: 160
  },
  {
    prop: 'platformShopLevelName',
    label: '店铺等级/类型',
    width: 160
  },
  {
    prop: 'businessCategoryNames',
    label: '主营类目',
    width: 160,
    showOverflowTooltip: true,
    formatter: (r, c, v = []) => v.join(',')
  },
  {
    prop: 'developCsName',
    label: '拓展顾问',
    width: 100
  },
  {
    prop: 'contactName',
    label: '联系人',
    width: 100
  },
  {
    prop: 'keyWord',
    label: '拓展关键词',
    width: 100
  },
  {
    prop: 'favoriteBrandNames',
    label: '拓展品牌',
    width: 100,
    showOverflowTooltip: true,
    formatter: (r, c, v = []) => v.join(',')
  },
  {
    prop: 'interestedBrandNames',
    label: '偏好品牌',
    width: 100,
    showOverflowTooltip: true,
    formatter: (r, c, v = []) => v.join(',')
  },
  {
    prop: 'merchantTypeName',
    label: '商家类型',
    width: 100
  },
  {
    prop: 'company',
    label: '企业名称',
    width: 100
  },
  {
    prop: 'companyLegalMobile',
    label: '企业联系方式',
    width: 160
  },
  {
    prop: 'companyLegalName',
    label: '企业法人',
    width: 100
  },
  {
    prop: 'companyScaleName',
    label: '企业规模',
    width: 100
  },
  {
    label: '更新时间',
    prop: 'updateDate',
    width: 100,
    formatter: (r, c, v) => (v ? parseTime(v, '{y}-{m}-{d}') : '--')
  }
];

const GJ_TableOptions = [
  {
    prop: 'createByName',
    label: '添加人',
    width: 80,
    fixed: 'left'
  },
  {
    prop: 'developCsName',
    label: '拓展顾问',
    width: 100,
    fixed: 'left'
  },
  {
    prop: 'createDate',
    label: '添加时间',
    width: 100,
    formatter: (r, c, v) => (v ? parseTime(v, '{y}-{m}-{d}') : '--')
  },
  {
    prop: 'updateDate',
    label: '更新时间',
    width: 100,
    formatter: (r, c, v) => (v ? parseTime(v, '{y}-{m}-{d}') : '--')
  },
  {
    prop: 'developChannelName',
    label: '拓展渠道',
    width: 100
  },
  {
    prop: 'platformShopName',
    label: '店铺名称',
    width: 160
  },
  {
    prop: 'platformShopId',
    label: '店铺ID',
    width: 160
  },
  {
    prop: 'industryLevel',
    label: '行业评级',
    width: 100
  },
  {
    prop: 'businessCategoryNames',
    label: '主营类目',
    width: 160,
    showOverflowTooltip: true,
    formatter: (r, c, v = []) => v.join(',')
  },
  {
    prop: 'platformShopLink',
    label: '店铺链接',
    width: 160
  },
  {
    prop: 'platformShopLevelName',
    label: '店铺等级/类型',
    width: 160
  },
  {
    prop: 'promoteChannelNames',
    label: '推广属性',
    width: 100,
    formatter: (r, c, v = []) => v.join(',')
  },
  {
    prop: 'averageCustomerPriceName',
    label: '店铺平均客单价',
    width: 160
  },
  {
    prop: 'monthlySalesName',
    label: '店铺平均月销',
    width: 160
  },
  {
    prop: 'interestedBrandNames',
    label: '偏好品牌',
    width: 160,
    showOverflowTooltip: true,
    formatter: (r, c, v = []) => v.join(',')
  },
  {
    prop: 'description',
    label: '店铺描述',
    width: 160,
    showOverflowTooltip: true
  },
  {
    prop: 'contactName',
    label: '联系人',
    width: 100
  },
  {
    prop: 'contactPost',
    label: '联系人岗位',
    width: 140
  },
  {
    prop: 'mobile',
    label: '联系方式',
    width: 100
  },
  {
    prop: 'wechat',
    label: '微信ID',
    width: 100
  },
  // 企业名称
  {
    prop: 'company',
    label: '企业名称',
    width: 100
  },
  // 企业法人
  {
    prop: 'companyLegalName',
    label: '企业法人',
    width: 100
  },
  // 企业联系方式
  {
    prop: 'companyLegalMobile',
    label: '企业联系方式',
    width: 160
  },
  // 企业规模
  {
    prop: 'companyScaleName',
    label: '企业规模',
    width: 100
  }
];

const OffLIneOptions = [
  {
    prop: 'createByName',
    label: '添加人',
    width: 80,
    fixed: 'left'
  },
  {
    prop: 'developCsName',
    label: '拓展顾问',
    width: 100
  },
  {
    prop: 'createDate',
    label: '添加时间',
    width: 100,
    formatter: (r, c, v) => (v ? parseTime(v, '{y}-{m}-{d}') : '--')
  },
  {
    label: '更新时间',
    prop: 'updateDate',
    width: 100,
    formatter: (r, c, v) => (v ? parseTime(v, '{y}-{m}-{d}') : '--')
  },
  {
    prop: 'developChannelName',
    label: '拓展渠道',
    width: 100
  },
  {
    prop: 'platformShopName',
    label: '客户名称',
    width: 160
  },
  {
    prop: 'businessTypeAndExtendName',
    label: '客户业务类型',
    width: 160
  },
  {
    prop: 'businessCategoryNames',
    label: '主营类目',
    width: 160,
    showOverflowTooltip: true,
    formatter: (r, c, v = []) => v.join(',')
  },
  {
    prop: 'interestedBrandNames',
    label: '偏好品牌',
    width: 100,
    showOverflowTooltip: true,
    formatter: (r, c, v = []) => v.join(',')
  },
  {
    prop: 'shopAddress',
    label: '店铺位置',
    width: 160
  },
  {
    prop: 'shopQuantity',
    label: '店铺数量',
    width: 100
  },
  {
    prop: 'averageCustomerPriceName',
    label: '店铺平均客单价',
    width: 160
  },
  {
    prop: 'contactName',
    label: '联系人',
    width: 100
  },
  {
    prop: 'contactPost',
    label: '联系人岗位',
    width: 140
  },
  {
    prop: 'mobile',
    label: '联系人手机号',
    width: 100
  },
  {
    prop: 'wechat',
    label: '微信ID',
    width: 100
  },
  // 店铺描述
  {
    prop: 'description',
    label: '店铺描述',
    width: 160,
    showOverflowTooltip: true
  }
];

export const TableOptions = {
  ZG: ZG_TableOptions,
  GJ: GJ_TableOptions,
  GF: GJ_TableOptions,
  CP: GJ_TableOptions,
  OFFLINE: OffLIneOptions
};
