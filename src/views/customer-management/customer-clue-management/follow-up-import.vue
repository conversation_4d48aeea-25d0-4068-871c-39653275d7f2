<template>
  <import-template name="线上-线索跟进" v-bind="importOptions" :fileRowNumLimit="200" :fileAcceptLimit="['xls', 'xlsx', 'csv']"></import-template>
</template>

<script>
import importTemplate from '@/components/Common/Import/importTemplate.vue';
import { validLeadsManagerInBatch, createLeadsManagerBatch } from '@/api/customer-management/clue.js';
import { parseTime } from '../../../components/ListSearch/ExportBtn/parseTime';

export default {
  name: 'follow-up-import',
  components: { importTemplate },
  data() {
    return {};
  },
  computed: {
    importOptions() {
      return {
        importRequest: createLeadsManagerBatch,
        validRequest: validLeadsManagerInBatch,
        uploadRequest: '/soyoungzg/api/todoPlan/uploadLeadsManager',
        downloadFailRequest: '/soyoungzg/api/todoPlan/downloadErrorResult',
        // 线索跟进模板地址
        templateUrl: process.env.VUE_APP_MUSHUROOMFILEURL + `/static/file/soyoung-zg/template/线上-线索跟进导入模板.xlsx`,
        backUrl: '/customer-management/customer-clue-management/list',
        tableOptions: [
          {
            key: 'platformShopId',
            label: '店铺ID'
          },
          {
            key: 'finishDate',
            label: '跟进日期',
            handle: (v) => parseTime(v)
          },
          {
            key: 'finishBy',
            label: '跟进人'
          },
          {
            key: 'todoEvent',
            label: '跟进内容'
          }
        ]
      };
    }
  }
};
</script>
