import { parseTime } from '@/utils';
import dict from '@/components/Common/dicts';
import { checkGroup } from '@/components/Common/SelectGroup';

// 权限
const Authority = {
  detail: '/customer-management/clue-management/:detail',
  edit: '/customer-management/clue-management/:edit',
  mobile: '/distributor-management/:decrypt-mobile',
  add: '/customer-management/clue-management/:add',
  import: '/customer-management/clue-management/:import',
  followUp: '/customer-management/clue-management/:follow-up',
  delete: '/customer-management/clue-management/:delete',
  createDistributor: 'customer-management-clue-management-create-distributor', // 生成分销商
  contact: '/customer-management/clue-management/:contact' // 完善联系方式
};

const FilterFormOptions = [
  {
    component: 'input',
    label: '店铺名称',
    prop: 'platformShopName',
    placeholder: '请输入店铺名称'
  },
  {
    component: 'input',
    label: '店铺ID',
    prop: 'platformShopId',
    placeholder: '请输入店铺ID'
  },
  {
    prop: 'createDate_daterangeTime',
    component: 'dateRange',
    type: 'daterange',
    props: ['createDateStart', 'createDateEnd'],
    label: '拓展时间'
  },
  {
    prop: 'daterangeTime',
    component: 'dateRange',
    type: 'daterange',
    props: ['lastFollowDateStart', 'lastFollowDateEnd'],
    label: '最近跟进'
  },
  {
    prop: 'updateDate',
    component: 'dateRange',
    type: 'daterange',
    props: ['updateDateStart', 'updateDateEnd'],
    label: '最近更新时间'
  },
  {
    component: 'select',
    label: '拓展渠道',
    prop: 'developChannels',
    options: dict('COMMON_EXPAND_CHANNEL'),
    multiple: true,
    placeholder: '请选择拓展渠道'
  },
  {
    component: 'select',
    label: '拓展顾问',
    prop: 'developCsIds',
    options: dict('COMMON_EXPAND_ADVISER'),
    multiple: true,
    placeholder: '请选择拓展顾问'
  },
  {
    component: 'select',
    label: '线索状态',
    prop: 'status',
    options: dict('COMMON_CLUEDISTRIBUTOR_STATUS'),
    placeholder: '请输入线索状态'
  },
  {
    component: 'input',
    label: '线索联系方式',
    prop: 'contactPhone',
    placeholder: '请输入线索联系方式'
  },
  {
    component: 'select',
    label: '跟进人',
    prop: 'followCsIds',
    options: dict('COMMON_EXPAND_ADVISER'),
    multiple: true,
    placeholder: '请选择拓展顾问'
  },
  {
    component: 'select',
    prop: 'developGroupIds',
    label: '拓展顾问所属团队',
    options: dict('COMMON_SHOPTAFFGROUP_LIST'),
    multiple: true,
    placeholder: '请选择所属团队'
  },
  {
    prop: 'industryLevel',
    component: 'select',
    label: '行业评级',
    options: dict('COMMON_TRADE_LEVEL'),
    placeholder: '请选择行业评级',
    visible() {
      // 不为直供团队时显示
      return !checkGroup(this.userGroup, 'ZG');
    }
  },
  {
    prop: 'groupIds',
    component: 'select',
    label: '所属团队',
    options: dict('DISTRIBUTOR_TEAM_DS'),
    placeholder: '请选择所属团队',
    visible() {
      return !checkGroup(this.userGroup);
    }
  },
  {
    component: 'select',
    label: '线索渠道归属',
    prop: 'customerChannelKind',
    options: dict('SOYOUNGZG_CHANNEL_TYPE'),
    multiple: false,
    placeholder: '请选择线索渠道归属'
  }
];

// 列表展示内容
const Table = {
  isSelect: true,
  items: [
    {
      key: 'csGroupName',
      label: '所属团队'
    },
    {
      key: 'customerChannelKindName',
      label: '线索渠道归属'
    },
    {
      key: 'developChannelName',
      label: '拓展渠道'
    },
    {
      key: 'platformShopName',
      label: '店铺/客户名称',
      width: 120
    },
    {
      key: 'platformShopId',
      label: '店铺ID'
    },
    {
      key: 'mobile',
      label: '联系方式'
    },
    {
      key: 'statusName',
      label: '是否已注册'
    },
    {
      key: 'businessCategoryNames',
      label: '主营类目',
      handle: (v = []) => v.join(',')
    },
    {
      key: 'interestedBrandNames',
      label: '偏好品牌',
      showOverflowTooltip: true,
      handle: (v = []) => v.join(',')
    },
    {
      key: 'developCsName',
      label: '拓展顾问'
    },
    {
      key: 'createByName',
      label: '添加人'
    },
    {
      key: 'createDate',
      label: '添加时间',
      handle(v) {
        return parseTime(v, '{y}-{m}-{d}');
      }
    },
    {
      key: 'lastFollowDate',
      label: '最近跟进日期',
      handle(v) {
        return parseTime(v, '{y}-{m}-{d}');
      },
      isSlot: true
    }
  ],
  paginationOptions: {
    pageSize: 20,
    pageSizes: [20, 30, 40, 50, 100]
  }
};

const TableOperate = {
  itemOptions: [
    {
      id: 'shopPageJump',
      label: '店铺主页',
      loadCondition: ({ platformShopLink }) => !!platformShopLink,
      button: {
        type: 'text'
      }
    },
    {
      id: 'clueDetail',
      authority: Authority.detail,
      button: {
        type: 'text'
      },
      isWrap: true,
      label: '详情'
    },
    {
      id: 'clueEdit',
      authority: Authority.edit,
      button: {
        type: 'text'
      },
      label: '编辑'
    },
    {
      id: 'clueDelete',
      authority: Authority.delete,
      label: '删除',
      button: {
        type: 'text'
      },
      loadCondition: ({ status, lastFollowDate }) => status === 'NOT_REGISTER' && !lastFollowDate
    },
    {
      id: 'perfectContact',
      authority: Authority.contact,
      label: '完善联系方式',
      button: {
        type: 'text'
      },
      isWrap: true,
      loadCondition: ({ status, mobile }) => !mobile && status !== 'PASS'
    },
    {
      id: 'createDistributor',
      authority: Authority.createDistributor,
      label: '生成三方分销商',
      button: {
        type: 'text'
      },
      loadCondition: ({ csGroupId, status }) => ['3', '2', '10'].includes(csGroupId) && status === 'NOT_REGISTER',
      isWrap: true
    }
  ]
};

const Dialog = {
  operateFollwUp: {
    title: '跟进记录',
    colNum: 1,
    items: [
      {
        key: 'expectedFinishDate',
        value: Date.now(),
        type: 'datePicker',
        elType: 'date',
        label: '跟进日期',
        'picker-options': {
          disabledDate(date) {
            return date.getTime() > Date.now();
          }
        },
        placeholder: '请选择跟进日期',
        rule: [{ required: true, message: '请选择跟进日期' }]
      },
      {
        key: 'label',
        type: 'select',
        observers: ['followResult'],
        selectOptions: dict('CLUE_FOLLOWUP_ITEM'),
        label: '跟进事项',
        placeholder: '请选择跟进事项',
        rule: [{ required: true, message: '请选择跟进事项' }]
      },
      {
        key: 'followWay',
        type: 'select',
        selectOptions: dict('CLUE_FOLLOWUP_WAY'),
        label: '跟进方式',
        placeholder: '请选择跟进方式',
        rule: [{ required: true, message: '请选择跟进方式' }]
      },
      {
        key: 'followResult',
        type: 'select',
        label: '跟进结果',
        watcherRun(value, { selectOptions, key }) {
          // 跟进事项改变 -> 跟进结果改变
          this.$set(this.formData, key, void 0);

          const labelVal = this.formData['label'];
          dict(`CLUE_FOLLOWUP_RESULT_${labelVal}`).then((res) => {
            selectOptions.splice(0, selectOptions.length, ...res);
          });
        },
        selectOptions: [],
        placeholder: '请选择跟进结果',
        rule: [{ required: true, message: '请选择跟进结果' }]
      },
      {
        key: 'todoEvent',
        type: 'input',
        elType: 'textarea',
        rows: 2,
        label: '跟进说明',
        placeholder: '请输入跟进说明',
        rule: [{ required: true, message: '请输入跟进说明' }]
      }
    ]
  }
};

// 弹框 - 跟进记录列表
const Dialog_ListFollowUp = {
  title: '跟进记录',
  Table: {
    items: [
      {
        key: 'expectedFinishDate',
        label: '跟进时间',
        handle(v) {
          return parseTime(v, '{yyyy}-{mm}-{dd}');
        }
      },
      {
        key: 'labelName',
        label: '跟进事项'
      },
      {
        key: 'followWayName',
        label: '跟进方式'
      },
      {
        key: 'platformShopName',
        label: '跟进店铺'
      },
      {
        key: 'todoEvent',
        label: '跟进说明'
      },
      {
        key: 'createByName',
        label: '跟进人'
      },
      {
        key: 'followResultName',
        label: '跟进结果'
      }
    ],
    paginationOptions: {
      pageSize: 5,
      pageSizes: [5, 10, 20, 30, 50]
    }
  },
  ActionBar: {
    items: [
      {
        id: 'addFollwUpToClue',
        authority: Authority.followUp,
        label: '添加',
        type: 'Button'
      }
    ]
  }
};

export default {
  Operate: {
    itemOptions: [
      {
        id: 'batchAddClue',
        authority: Authority.add,
        label: '添加',
        button: {
          type: 'primary'
        }
      },
      {
        id: 'addFollowUp',
        authority: Authority.followUp,
        label: '跟进',
        button: {
          type: 'primary'
        }
      },
      {
        id: 'batchImportClue',
        authority: Authority.import,
        label: '导入',
        button: {
          type: 'primary'
        }
      },
      {
        id: 'batchImportFollowUp',
        authority: Authority.followUp,
        label: '批量导入跟进',
        button: {
          type: 'primary'
        }
      }
    ]
  },
  Authority,
  FilterFormOptions,
  Table,
  TableOperate,
  Dialog_ListFollowUp,
  Dialog,
  Pagination: {
    pageSize: 20,
    pageSizes: [20, 30, 40, 50, 100]
  }
};
