<template>
  <div class="createForm">
    <Form
      :options="myFormOptions"
      ref="form"
      :externalData="formData"
      class="form"
    >
      <div></div>
    </Form>
    <el-tooltip
      class="item"
      effect="dark"
      :content="getTip()"
      placement="top-end"
      v-show="getTip()"
    >
      <span class="checkFormDataRepeat">!</span>
    </el-tooltip>
  </div>
</template>

<script>
import Form from '@/components/Dialog/Form';
import { checkClueExist } from '@/api/customer-management/clue.js';
import cloneDeep from 'lodash/cloneDeep';
import { queryByName } from '@/api/distributorManagement/statistic/list.js';

export default {
  name: 'createForm',
  components: { Form },
  data() {
    return {
      tips: [],
      checkRepeats: [],
      myFormOptions: {}
    };
  },
  props: [
    'formItems',
    'formData',
    'formOptions',
    'formItemIndex',
    'isValidate'
  ],
  provide() {
    return {
      change: this.change
    };
  },
  created() {
    const that = this;
    this.myFormOptions = {
      ...this.formOptions,
      items: cloneDeep(this.formOptions.items).map((i) => ({
        ...i,
        change: function (...args) {
          i.change && i.change.apply(this, args);

          // 改变同上
          !that.isValidate && that.formItemsChageDitto(...args);
          that.formItemValueChage(...args);
        }
      })),
      rules: cloneDeep(this.formOptions.rules)
    };
  },
  mounted() {
    const form = this.$refs.form;
    this.$parent.formDatas[this.formItemIndex] = form.templateData;

    if (this.isValidate) {
      this.formOptions.items.forEach((i) => {
        if (i.change && i.key !== 'company') {
          i.change.apply(form, [i]);
          this.formItemValueChage(i);
        }
      });
    }
  },
  methods: {
    getTip() {
      const tip = this.tips.reduce((c, p) => {
        return (c += (c !== '' ? '; ' : '') + p);
      }, '');

      return this.checkRepeats.reduce((c, p) => {
        return (c += (c !== '' ? '; ' : '') + p);
      }, tip);
    },
    // 选择同上时，获取其对应值
    getDittoValue(key) {
      const index = this.formItemIndex;
      let i = index - 1;
      for (; i >= 0; i--) {
        const fd = this.$parent.formDatas[i];
        if (
          !fd ||
          fd[key] === 'ditto' ||
          (Array.isArray(fd[key]) &&
            fd[key].length === 1 &&
            fd[key][0] === 'ditto')
        ) {
          continue;
        }

        return fd[key];
      }
      return '';
    },
    // 改变下一行的店铺等级下拉选项
    changeAfterPSLevelVal(key, val) {
      const index = this.formItemIndex + 1;
      const cfs = this.$parent.$refs.createForm;
      if (!cfs || !cfs[index]) return;

      const cf = cfs[index];
      const form = cf.$refs.form;

      let options;
      if (
        cf &&
        (options = cf.formOptions.items.find((i) => i.key === key)) &&
        form.templateData[key] === 'ditto'
      ) {
        form.emitObserver(options);
      }
    },
    /**
      拓展渠道改变
      rule改变规则：
      1. TAOBAO 
        [店铺ID] 必填 [店铺名称,联系电话，联系人] 选填
      2. 其他	
        [店铺名称,联系电话，联系人] 必填 [店铺ID] 选填
      
      实现：
        1. 遍历 0 -> t 获取之前最近的一条，判断是否(true/false)需要改变rule规则
        2. (true) 遍历 t -> end(直到拓展渠道值不为同上) 改变这中间rule规则
     */
    developChannel_changeRules(key, val) {
      const index = this.formItemIndex;

      /* const beforeV = this.getDittoValue(key);

      for (let i = 0; i < index; i++) {
        if (
          this.formData[i][key] !== 'ditto' &&
          (this.formData[i][key] === form[key] ||
            ![this.formData[i][key], form[key]].includes('TAOBAO'))
        ) {
          return;
        }
      } */

      val === 'ditto' && (val = this.getDittoValue(key));

      const len = this.$parent.formDatas.length;
      const cfs = this.$parent.$refs.createForm;
      const flag = val !== 'TAOBAO';
      for (let i = index; i < len; i++) {
        const form = cfs[i].$refs.form;
        if (form.templateData[key] !== 'ditto' && i !== index) break;

        // 拓展渠道改变影响系电话、联系人：为非必填
        ['contactPhone', 'contactName', 'platformShopName'].forEach((x) => {
          this.changeRule({
            index: i,
            field: x,
            flag
          });
        });
        ['platformShopId'].forEach((x) => {
          this.changeRule({
            index: i,
            field: x,
            flag: !flag
          });
        });
      }
    },
    changeRule({ index, field, flag }) {
      const cfs = this.$parent.$refs.createForm;

      cfs[index].myFormOptions.rules[field][0].required = flag;
      if (!flag) {
        const _field = cfs[index].$refs.form.$refs.form.fields;
        _field.map((y) => {
          if (y.prop === field) {
            y.clearValidate();
          }
        });
      }
    },
    // 企业/个人字段 影响企业名称
    merchantType_changeRules(key, val) {
      const index = this.formItemIndex;

      val === 'ditto' && (val = this.getDittoValue(key));

      const len = this.$parent.formDatas.length;
      const cfs = this.$parent.$refs.createForm;
      const flag = val === 'ENTERPRISE';

      for (let i = index; i < len; i++) {
        const form = cfs[i].$refs.form;
        if (form.templateData[key] !== 'ditto' && i !== index) break;

        ['company'].forEach((x) => {
          this.changeRule({
            index: i,
            field: x,
            flag: flag
          });
        });
      }
    },
    // 企业名称改变
    fieldChange_company(key, val) {
      let flag = false;
      queryByName(val)
        .then((res) => {
          if (res.code === '0') {
            // 4.7 法人联系方式不自动覆盖，不校验；
            /* this.$set(
              this.$refs.form.templateData,
              'companyLegalMobile',
              res.data.contactPhone
            ); */
            flag = true;
          }
        })
        .finally(() => {
          this.$message({
            type: flag ? 'success' : 'info',
            message: `查询${flag ? '' : '不'}到该企业`
          });
        });
    },
    formItemValueChage(options) {
      this.tips = [];

      const form = this.$refs.form.$refs.form;
      this.checkRepeat(options.key, form);

      form.validate().catch((res) => {
        const _field = form.fields;
        _field.forEach((i) => {
          if (i.validateMessage) {
            this.tips.push(i.validateMessage);
          }
        });
        form.clearValidate();
      });
    },
    /**
     * 表单下拉项值change时,触发同上校验
     * 1. 检查所有含有同上的项
     *    1.1 判断其最近值是否存在
     *         1.1.1 不存在清空该行的同上下拉选择项
     *         1.1.2 不存在清空该行的同上选择值
     * 2. row -> endRow 编辑赋予之前被清空的同上选择值和下拉选择
     */
    formItemsChageDitto(options) {
      const { key, type } = options;
      if (type !== 'select') return

      const form = this.$refs.form;
      const selectOptions1 = form.templateData[key + 'Options'];
      // 1. 检查所有含有同上的项
      this.myFormOptions.items.forEach((i) => {
        if (i.key !== key && i.isDitto) {
          const beforeVal = this.getDittoValue(i.key);
          // 1.1 判断其最近值是否存在
          if (
            beforeVal === '' ||
            (Array.isArray(beforeVal) && beforeVal.length === 0)
          ) {
            // 1.1.1 不存在清空该行的同上下拉选择项
            const selectOptions = form.templateData[i.key + 'Options'];
            selectOptions[0] &&
              selectOptions[0].value === 'ditto' &&
              selectOptions.shift();

            // 1.1.2 不存在清空该行的同上选择值
            const value = form.templateData[i.key];
            if (value === 'ditto') {
              form.templateData[i.key] = '';
            }
            if (
              Array.isArray(value) &&
              value.length === 1 &&
              value[0] === 'ditto'
            ) {
              form.templateData[i.key] = [];
            }
          }
        }
      });

      // 2. row -> endRow 编辑赋予之前被清空的同上选择值和下拉选择

      const index = this.formItemIndex;
      const formDatas = this.$parent.formDatas;
      const str1 = selectOptions1
        .filter((x) => x.value !== 'ditto')
        .map((x) => x.value)
        .join('');
      let i = index + 1;
      for (; i < formDatas.length; i++) {
        const fd = formDatas[i];
        const selectOptions2 = fd[key + 'Options'];
        const str2 = selectOptions2
          .filter((x) => x.value !== 'ditto')
          .map((x) => x.value)
          .join('');
        if (str1 !== str2) break;
        if (
          fd[key] === '' ||
          (Array.isArray(fd[key]) && fd[key].length === 0)
        ) {
          selectOptions2[0].value !== 'ditto' &&
            selectOptions2.unshift({
              value: 'ditto',
              label: '同上'
            });
          fd[key] = Array.isArray(fd[key]) ? ['ditto'] : 'ditto';

          this.$parent.$refs.createForm[i].formItemValueChage(options);
        } else if (
          fd[key] !== 'ditto' &&
          !(
            Array.isArray(fd[key]) &&
            fd[key].length === 1 &&
            fd[key][0] === 'ditto'
          )
        ) {
          break;
        }
      }
    },
    // 检查 【店铺名称】、【店铺id】、【联系方式】【企业名称】是否与数据库重复
    checkRepeat(key, form) {
      const keys = ['platformShopName', 'platformShopId', 'contactPhone'];
      const keyNames = ['店铺名称', '店铺id', '联系方式'];
      const keyIndex = keys.indexOf(key);
      if (keyIndex === -1) return;

      const index = this.formItemIndex;

      const formItemValue = this.$refs.form.templateData[key];
      form.validateField(key, (error) => {
        if (!error && formItemValue !== '') {
          // 检查与系统是否有重复
          this.submitLoading = true;
          let flag;
          checkClueExist({ [key]: formItemValue })
            .then(() => {
              flag = false;
              this.$message.success(`${keyNames[keyIndex]}可以使用`);
            })
            .catch(() => {
              flag = true;
              this.$message.error(`${keyNames[keyIndex]}已经存在`);
            })
            .finally(() => {
              this.submitLoading = false;
              let message = '';
              if (flag) {
                message = `${keyNames[keyIndex]}已经存在`;
              } else {
                // 检查是否与已填写的有重复
                const formDatas = this.$parent.formDatas;
                for (let i = 0; i < formDatas.length; i++) {
                  // 跳过自身
                  if (i === index) continue;
                  const checkV = formDatas[i][key];
                  if (checkV === formItemValue) {
                    // 含有重复数据
                    message = `${keyNames[keyIndex]}与第${i + 1}条数据重复`;
                    break;
                  }
                }
              }
              this.setRepeatMessage(key, message);
            });
        }
      });
    },
    setRepeatMessage(key, message) {
      const keys = ['platformShopName', 'platformShopId', 'contactPhone'];
      const keyNames = ['店铺名称', '店铺id', '联系方式'];
      const keyIndex = keys.indexOf(key);
      if (keyIndex === -1) return;
      typeof message === 'undefined' &&
        (message = `${keyNames[keyIndex]}已经存在`);

      this.$set(this.checkRepeats, keyIndex, message);
    },
    setItemRepeat(flag, { key, index, keyIndex }) {
      const keys = [
        'platformShopName',
        'platformShopId',
        'contactPhone',
        'company22'
      ];

      Array.isArray(this.checkRepeats[index]) ||
        this.$set(this.checkRepeats, index, Array(keys.length).fill(false));

      this.checkRepeats[index].splice(keyIndex, 1, flag);
      this.$refs[`formItems_${key}`] &&
        (this.$refs[`formItems_${key}`][index].isRepeat = flag);
    }
  }
};
</script>

<style rel="stylesheet/scss" lang='scss' scoped>
::v-deep {
  .el-form {
    display: flex;

    .el-form-item {
      &:not(:first-child) {
        margin-left: 10px;
      }
      position: relative;
      .checkDataRepeat::after {
        content: '!';
        color: #ff0000;
        font-size: 20px;
        font-weight: 700;
        position: absolute;
        right: 0;
        top: 50%;
        transform: translate(120%, -50%);
      }
    }
  }
}
.createForm {
  position: relative;
  .checkFormDataRepeat {
    color: #fff;
    font-size: 16px;
    text-align: center;
    font-weight: 700;
    position: absolute;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    background-color: #ff0000;
    left: 0;
    transform: translate(-100%, 35%);
    top: 0;
  }
}
</style>