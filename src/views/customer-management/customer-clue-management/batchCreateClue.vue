<template>
  <div class="batchCreateClue">
    <BatchCreate
      :deleteAllErorData="deleteAllErorData"
      :showDelete="showDelete"
      ref="batchCreate"
      class="batchCreateClue__table"
      :type="type"
      :formRowLen="10"
      :tableColumns="tableColumns"
      @onFormItemChange="onFormItemChange"
      @onSubmitFormMounted="onSubmitFormMounted"
      :tipType="tipType"
      :showOperation="showOperation"
    >
      <template slot="tip" slot-scope="{ scope }">
        <el-tooltip class="batchCreateClue__tip" effect="dark" :content="getTip(scope.$index)" placement="top-end" v-if="formStates[scope.$index] && formStates[scope.$index].status !== 0">
          <div :class="'batchCreateClue__tip--status batchCreateClue__tip--status' + formStates[scope.$index].status">
            <i :class="groupTemplate.tipIcons[formStates[scope.$index].status]"></i>
          </div>
        </el-tooltip>
      </template>
    </BatchCreate>
    <div class="footer-wrap">
      <div class="footer">
        <el-button @click="onSubmit" :loading="submitLoading" type="primary">保存</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import BatchCreate from '@/components/Common/BatchCreate';
import options from './config';
import { checkClueExist } from '@/api/customer-management/clue.js';

export default {
  components: { BatchCreate },
  name: 'batchCreateClue',
  data() {
    return {
      submitLoading: false,
      tableColumns: [],
      formStates: [],
      formDatas: [],
      batchAddFormNum: 10,
      groupTemplate: {},
      repeatKeys: ['developChannel', 'platformShopId', 'mobile', 'platformShopName']
    };
  },
  props: {
    groupId: {
      type: String
    },
    type: {
      type: 'Form' | 'Table',
      default: 'Form'
    },
    channelType: {
      type: String,
      default: 'ONLINE'
    },
    deleteAllErorData: {
      type: String,
      default: ''
    },
    showDelete: {
      type: Boolean,
      default: true
    },
    tipType: {
      type: String,
      default: 'add' // add: 添加 export：导入
    },
    showOperation: {
      type: Boolean,
      default: true
    }
  },
  created() {
    this.tableColumns = options.getTeamFormItems(this.groupId, 'create', this.channelType);

    this.groupTemplate = options.getTeamOptions(this.groupId, this.channelType);
  },
  mounted() {
    if (this.type === 'Form') {
      this.initForm();
    }
  },
  methods: {
    initForm() {
      this.formDatas = Array(this.batchAddFormNum)
        .fill(0)
        .map((i, index) => {
          this.$set(this.formStates, index, {
            status: 0,
            message: '',
            observerRepeatIndexs: [],
            watcherRepeatIndex: void 0
          });
          return {};
        });

      this.$refs.batchCreate.init(this.formDatas);
    },
    // 监听表单是否渲染完成
    onSubmitFormMounted(index) {
      // 给除第一条外的条赋值同上选项
      if (this.type === 'Form' && index) {
        const form = this.$refs.batchCreate.$refs.searchForm[index];
        form.perfectFormItems.forEach((i) => {
          if (i.watcherRun) {
            const watcherRun = i.watcherRun;
            const that = this;
            i.watcherRun = function (value, options, observerKey) {
              const isDitto = value === '_ditto';

              let realValue = value;
              if (isDitto) {
                realValue = that.transformDittoValue(index, observerKey, -1);
                // 清除可能存在的表单校验
                // this.$refs.form && this.$refs.form.clearValidate(options.key);
              }

              watcherRun.apply(this, [realValue, options, observerKey]);

              const { key, selectOptions } = options;
              if (key === 'platformShopLevel') {
                this.formData[key] = isDitto ? '_ditto' : '';
                if (isDitto) {
                  selectOptions.then(([...res]) => {
                    res.unshift({
                      label: '同上',
                      value: '_ditto'
                    });
                    options.selectOptions = res;
                  });
                }
              }
            };
          }

          if (i.selectOptions) {
            i.selectOptions.then((res) => {
              i.selectOptions = [{ label: '同上', value: '_ditto' }, ...res];
              form.formData[i.key] = i.multiple ? ['_ditto'] : '_ditto';
            });
          }
        });
      }
    },
    // 批量导入设置初始状态
    setBatchFormData(formData = []) {
      const Status = {
        EMPTY_FILED: 1,
        DUPLICATED: 3,
        SYSTEM_DUPLICATED: 4
      };

      formData.forEach(({ errorMsg, duplicatedMsg, importStatus }, index) => {
        this.$set(this.formStates, index, {
          status: Status[importStatus] || 5,
          message: errorMsg || duplicatedMsg || '成功',
          observerRepeatIndexs: [],
          watcherRepeatIndex: void 0
        });
      });

      this.formDatas = formData;

      this.$refs.batchCreate.init(formData);
    },
    onFormItemChange(index, args) {
      if (this.type === 'Table') {
        this.onFormItemChange_Table(index, args);
        return;
      } else {
        this.onFormItemChange_Form(index, args);
        return;
      }
    },
    onFormItemChange_Table(index, args) {
      const { key, rule } = args[0];

      // 只针对配置必填rule进行校验
      if (!rule) return;

      // 当前正在编辑的表单数据
      const formData = args[2];

      // 进行校验
      this.validateForm_2(index, key, formData);
    },

    onFormItemChange_Form(index, args) {
      const { key, rule, observers, type } = args[0];

      // 当前正在编辑的表单数据
      const formData = args[2];

      // 判断是否有有效值
      let isValid = true;
      if (
        Object.values(formData).every((i) => {
          return !this.checkValidValue(i);
        })
      ) {
        if (this.formStates[index].status === 1) {
          Object.assign(this.formStates[index], { status: 0, message: '' });
        }
        isValid = false;
      }

      // 进行校验
      if (rule && isValid) {
        // 获取表单真实值
        let formData = this.$refs.batchCreate.getFormData(index);
        formData = this.transformDittoFormData2(index, formData);
        this.validateForm_2(index, key, formData);
      }

      if (observers || type === 'select') {
        this.triggerSiblingWatcher(index, key);
      }
    },
    validateForm_2(index, key, formData) {
      // 校验
      this.$refs.batchCreate.validateFormToTemplate({
        index,
        formData,
        callback: (formData, valid, props) => {
          // 判断是否进入页面重复校验
          // if (valid && this.repeatKeys.includes(key)) {
          if (valid) {
            // 页面重复校验
            this.validatePage(index, formData);
            // 后台系统重复性校验
          } else {
            // 获取校验提示
            const message = Object.values(props)
              .flat()
              .map((i) => i.message)
              .join(';');

            // 更新状态值
            if (this.formStates[index]) {
              const { status } = this.formStates[index];

              if (status >= 4) {
                this.unboundPageRepeat_o(index);
              }

              this.formStates[index].status = 1;
              this.formStates[index].message = message;
            } else {
              this.$set(this.formStates, index, {
                status: 1,
                message
              });
            }
          }
        }
      });
    },
    validatePage(index, { developChannel: a, platformShopId: b, mobile: c, platformShopName, customerChannelKind }) {
      // 解绑自身原有重复信息
      const { status } = this.formStates[index];

      if (status === 3) {
        this.unboundPageRepeat_w(index);
      }

      if (status >= 4) {
        // 设置为过渡态 2
        this.unboundPageRepeat_o(index);
      }

      this.formStates[index].status = 2;
      this.formStates[index].message = '';

      let repeatIndex = -1; // 重复索引

      for (let i = 0; i < this.formStates.length; i++) {
        // 跳过自身
        if (i === index) continue;

        const state = this.formStates[i];
        // 只与通过form的rule校验的做判断
        if (state && state.status >= 1) {
          const formData = this.$refs.batchCreate.getFormData(i);
          const { developChannel: a1, platformShopId: b1, mobile: c1 } = this.transformDittoFormData2(i, formData);

          if (this.groupId === '3') {
            if ((c && c === c1) || (b && b === b1)) {
              repeatIndex = i;
              break;
            }
          } else {
            if (a === a1 && ((c && c === c1) || (!c && !c1 && b === b1))) {
              repeatIndex = i;
              break;
            }
          }
        }
      }

      if (repeatIndex !== -1) {
        // 目标行位于上面
        const { status: s1, watcherRepeatIndex: w1 } = this.formStates[repeatIndex];

        if (index < repeatIndex) {
          if (s1 === 3) {
            if (index > w1) {
              this.formStates[w1].observerRepeatIndexs.push(index);
              this.formStates[index].watcherRepeatIndex = w1;
              this.formStates[index].status = 3;
              return;
            }
            repeatIndex = w1;
          }

          this.unboundPageRepeat_o(repeatIndex, index);
        } else {
          this.formStates[index].status = 3;

          s1 === 3 && (repeatIndex = w1);
          this.formStates[repeatIndex].observerRepeatIndexs.push(index);
          this.formStates[index].watcherRepeatIndex = repeatIndex;

          return;
        }
      }

      // 与系统判断重复
      if (this.formStates[index].status === 2) {
        if (customerChannelKind === 'OFFLINE') {
          this.validateServer(index, {
            developChannel: a,
            platformShopId: b,
            mobile: c,
            platformShopName
          });
        } else {
          this.validateServer(index, {
            developChannel: a,
            platformShopId: b,
            mobile: c
          });
        }
      }
    },
    // 解除页面重复observer
    unboundPageRepeat_o(index, index2) {
      const { status, observerRepeatIndexs: o } = this.formStates[index];

      if (index2) {
        this.formStates[index2].observerRepeatIndexs.push(index, ...o);
        this.formStates[index].watcherRepeatIndex = index2;
        this.formStates[index].status = 3;
      } else if (o.length) {
        index2 = Math.min(...o);
        o.splice(o.indexOf(index2), 1);
        this.formStates[index2].status = status;
        this.formStates[index2].watcherRepeatIndex = void 0;
        this.formStates[index2].observerRepeatIndexs.push(...o);
      }

      for (let i = 0; i < o.length; i++) {
        this.unboundPageRepeat_w(o[i], index2);
        i--;
      }
    },
    // 解除页面重复watcher
    unboundPageRepeat_w(index, index2) {
      const { watcherRepeatIndex: w } = this.formStates[index];
      if (!w) return;

      this.formStates[index].watcherRepeatIndex = index2;

      const { observerRepeatIndexs: o } = this.formStates[w];
      const i = o.indexOf(index);
      if (i !== -1) {
        o.splice(i, 1);
      }
    },

    validateServer(index, params) {
      // 与系统中做对比
      checkClueExist({
        ...params,
        csGroupId: this.groupId,
        customerChannelKind: this.channelType
      }).then((res) => {
        this.formStates[index].status = res.data ? 4 : 5;
      });
    },
    getTip(index) {
      const { status, message, watcherRepeatIndex: w } = this.formStates[index];
      if (message) {
        return message;
      }

      let tip = message;
      switch (status) {
        case 3:
          tip = `与第${w + 1}条数据重复`;
          break;
        case 4:
          tip = `与系统数据重复`;
          break;
        case 5:
          tip = `成功`;
          break;
      }

      return tip;
    },
    // 批量添加 触发下一行同上事件(对应值也为同上)
    triggerSiblingWatcher(index, key) {
      const nextSiblingForm = this.getSiblingSubmitForm(index, 1);

      // nextSiblingForm.formData[key] === '_ditto'
      if (nextSiblingForm) {
        const nextSiblingFormItem = nextSiblingForm.perfectFormItems.find((i) => i.key === key);

        nextSiblingForm.onFormItemChange(nextSiblingFormItem, nextSiblingForm.formData[key]);
      }
    },
    checkValidValue(v) {
      Array.isArray(v) && (v = v[0]);
      return !!v && v !== '_ditto';
    },
    // 获取兄弟表单
    getSiblingSubmitForm(index, step = 0) {
      index += step;
      if (index >= 0 && index < this.batchAddFormNum) {
        return this.$refs.batchCreate.getSubmitForm(index);
      }
      return;
    },
    // 转化同上的表单数据 - 获取真实值
    transformDittoFormData(index) {
      const form = this.getSiblingSubmitForm(index) || {};
      const formData = { ...form.formData };
      Object.keys(formData).forEach((k) => {
        const i = formData[k];
        const v = Array.isArray(i) ? i[0] : i;
        if (v === '_ditto') {
          formData[k] = this.transformDittoValue(index, k, -1);
        }
      });

      return formData;
    },
    // 转化同上的表单数据 - 获取真实值
    transformDittoFormData2(index, { ...formData }) {
      Object.keys(formData).forEach((k) => {
        const i = formData[k];
        const v = Array.isArray(i) ? i[0] : i;
        if (v === '_ditto') {
          formData[k] = this.transformDittoValue(index, k, -1);
        }
      });

      return formData;
    },
    /**
     * 获取兄弟表单项值
     */
    transformDittoValue(index, key, step = 0) {
      index += step;

      for (; index > -1 && index < this.batchAddFormNum; index += step) {
        const siblingForm = this.getSiblingSubmitForm(index);

        const value = siblingForm.formData[key];
        if (Array.isArray(value) ? value[0] !== '_ditto' : typeof value !== 'undefined' && value !== '_ditto') {
          return value;
        }
      }
      return;
    },
    onSubmit() {
      if (this.type === 'Table' && this.$refs.batchCreate.editFormStates.length) {
        this.$message.warning('当前正在编辑不能保存');
        return;
      }
      if (this.tipType === 'export') {
        const errList = this.$refs.batchCreate.tableData.filter((item) => item.isSuccess === '0');
        if (errList.length) {
          this.$message.warning(`存在${errList.length}条校验失败数据，请删除后再保存`);
          return;
        }
      }
      const submitData = [];
      // 不属于任何团队不能添加
      const template = options.getTeamOptions(this.groupId, this.channelType);

      const { createAxios, statusLimit } = template;
      const repeats = [];
      this.formStates.forEach((i, index) => {
        if (i && i.status >= statusLimit) {
          let formData = this.$refs.batchCreate.getFormData(index);

          if (formData) {
            formData = this.transformDittoFormData2(index, formData);
            i.status === 4 && repeats.push(index + 1);
            submitData.push({
              ...formData,
              csGroupId: this.groupId
            });
          }
        }
      });

      const len = submitData.length;
      if (!len) {
        this.$message.warning('没有有效数据，无法保存');
        return;
      }
      this.mixins_confirm({
        name: `保存${len}条有效数据` + (repeats.length ? `;第${repeats.join(',')}条线索将会合并保存` : ''),
        resolve: () => {
          this.submitLoading = true;
          // 提交数据
          createAxios(submitData)
            .then((res) => {
              this.$message({
                type: 'success',
                message: `共有${res.data.successNum}条数据保存成功`
              });
              this.$back({
                path: '/customer-management/customer-clue-management/list'
              });
            })
            .catch((err) => {
              // this.$message.error(err.message);
              console.log(err);
            })
            .finally(() => {
              this.submitLoading = false;
            });
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.batchCreateClue {
  height: 100%;
  flex: 1;
  display: flex;
  flex-direction: column;

  .el-table {
    flex: 1;
    display: flex;
    flex-direction: column;
    ::v-deep {
      .el-table__header-wrapper {
        height: 44px;
      }
      .el-table__body-wrapper {
        overflow: auto;
        flex: 1;
      }
    }
  }
  &__table {
    flex: 1;
    height: 0;
  }
  &__tip {
    font-size: 20px;
    &--status {
      i {
        border-radius: 50%;
        color: #fff;
      }
      .el-icon-close {
        background: var(--color-danger);
      }
    }
    &--status4 {
      .el-icon-check {
        background: var(--color-warning);
      }
    }
    &--status5 {
      i {
        background: var(--color-success);
      }
    }
  }
  &__footer {
    flex: none;
    height: 50px;
    line-height: 60px;
    text-align: right;
  }
}
</style>
