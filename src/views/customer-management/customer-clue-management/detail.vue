<template>
  <div>
    <exhibition class="table-container"></exhibition>
    <footer-bar>
      <el-button type="primary" @click="close">关闭</el-button>
    </footer-bar>
  </div>
</template>

<script>
import Exhibition from './component/exhibition';

export default {
  name: 'customer-clue-management-detail',
  components: { Exhibition },
  methods: {
    close() {
      this.$back();
    }
  }
};
</script>

<style></style>
