<!-- 弹出框 -->
<template>
  <ui-popup
    class="selectImg"
    width="500px"
    :title="params.title || '标题'"
    append-to-body
    @onCancel="reject"
    @onSubmit="onSubmit"
  >
    <YSForm
      :content="content"
      ref="YSForm"
      label-width="100px"
      label-position="right"
      :inline="true"
    />
  </ui-popup>
</template>

<script>
import YSForm from '@/components/YSForm';
import dict from '@/components/Common/dicts';
// import { fechDictData } from '@/api/dict';
export default {
  data() {
    return {
      staffInfo: null,
      toClaim: [
        // 认领
        {
          $type: 'input',
          $id: 'platformShopId',
          label: '店铺id',
          $el: {
            disabled: true
          },
          rules: [{ required: true, message: '请输入店铺id', trigger: 'blur' }]
        },
        {
          $type: 'select',
          $id: 'developChannel',
          label: '来源渠道',
          $el: {
            filterable: true
          },
          $options: dict('COMMON_EXPAND_CHANNEL'),
          rules: [
            { required: true, message: '请选择来源渠道', trigger: 'change' }
          ],
          change: (value, updateValue) => {
            const index = this.toClaim.findIndex((i) => {
              return i.$id === 'platformShopLevel';
            });
            const cs = ['TAOBAO', 'TMALL'];
            if (index !== -1) {
              this.$set(this.toClaim, index, {
                ...this.toClaim[index],
                $options: dict(cs.includes(value) ? 'SHOP_LEVEL' : 'SHOP_TYPE')
              });
              updateValue({ id: 'platformShopLevel', value: '' });
            }
          }
        },
        {
          $type: 'select',
          $id: 'platformShopLevel',
          label: '店铺等级/类型',
          $options: dict('SHOP_LEVEL'),
          $el: { filterable: true },
          rules: [
            {
              required: true,
              message: '请选择店铺等级/类型',
              trigger: 'change'
            }
          ]
        },
        {
          $type: 'select',
          $id: 'businessCategory',
          label: '主营类目',
          $options: dict('COMMON_CATEGORY'),
          $el: { filterable: true },
          rules: [
            { required: true, message: '请选择主营类目', trigger: 'change' }
          ]
        },
        {
          $type: 'select',
          $id: 'industryLevel',
          label: '行业评级',
          $el: { filterable: true },
          $options: dict('COMMON_TRADE_LEVEL'),

          rules: [
            { required: true, message: '请选择行业评级', trigger: 'change' }
          ]
        }
      ],

      // 转交
      transfer: [
        {
          $type: 'select',
          $id: 'forwardDevelopCsId',
          label: '转交人',
          $options: dict('COMMON_EXPAND_ADVISER_GJ').then((res) => {
            if (this.staffInfo && this.staffInfo.name) {
              // 过滤自己
              return res.filter(({ label }) => label !== this.staffInfo.name);
            }
            return res;
          }),
          $el: { filterable: true },
          rules: [
            { required: true, message: '请选择转交人', trigger: 'change' }
          ]
        }
      ],

      // 无效
      invalid: [
        {
          $type: 'input',
          $id: 'remarks',
          label: '说明',
          $default: '类目不符，信息无效',
          rules: [
            { required: true, message: '描述无效的原因', trigger: 'blur' }
          ]
        }
      ]
    };
  },
  props: {
    params: {
      type: Object,
      default() {
        return {
          title: '',
          rowData: {}
        };
      }
    }
  },
  components: { YSForm },

  computed: {
    content() {
      const { type = 'df', csGroupId } = this.params;
      const x = {
        toClaim: this.toClaim,
        transfer: this.transfer,
        invalid: this.invalid,
        df: []
      };
      if (csGroupId && !['1', '2', '3'].includes(csGroupId)) {
        return [
          {
            $type: 'select',
            $id: 'csGroupId',
            label: '直供/国际',
            $options: [
              {
                label: '直供',
                value: '1'
              },
              {
                label: '国际',
                value: '3'
              }
            ],
            rules: [
              { required: true, message: '请选择认领团队', trigger: 'change' }
            ]
          },
          ...x[type]
        ];
      }
      return x[type];
    }
  },

  created() {},

  mounted() {
    const { rowData, staffInfo } = this.params;
    this.staffInfo = staffInfo;
    if (rowData && Object.keys(rowData).length > 0) {
      this.$nextTick(() => {
        this.content.forEach((item) => {
          if (rowData[item.$id]) {
            this.$refs.YSForm.updateValue({
              id: item.$id,
              value: rowData[item.$id]
            });
          }
        });
      });
    }
  },

  methods: {
    onSubmit() {
      this.$refs.YSForm.validate().then((res) => {
        this.resolve(this.$refs.YSForm.getFormValue());
      }); // element 校验函数
    }
  }
};
</script>
<style lang='scss' scoped>
::v-deep {
  .el-input {
    width: 300px;
  }
}
</style>