<!-- 店铺商机管理 -->
<template>
  <div class="app-container">
    <div class="table-container">
      <Instructions :showTitle="true" :colNum="0">
        <template slot="title">
          <div>
            <p>功能说明: ① 支持在此页面查看淘宝商家名片，快速链接淘系商家。② 拓店时无须动记录商家信息，直接关联商家信息到线索，提升拓店效率。</p>
            <p>分派规则: 系统每天8:00发送给每个拓展150条商机信息，如果当天拓展未认领系统自动回收商机重新发放</p>
          </div>
        </template>
      </Instructions>
      <!-- 搜索栏 -->
      <filter-form :options="searchFormOptions" :basicData="{ status: 'UNRECEIVED' }" @query="onSubmit_searchForm" ref="filterForm"></filter-form>

      <el-table :data="list" element-loading-text="加载中" fit highlight-current-row ref="multipleTable" v-loading="listLoading">
        <!-- <el-table-column type="selection" width="55"> </el-table-column> -->
        <el-table-column align="center" label="店铺名称" prop="platformShopName"></el-table-column>
        <el-table-column align="center" label="经营渠道" prop="developChannelName"></el-table-column>
        <el-table-column align="center" label="店铺ID" prop="platformShopId"></el-table-column>
        <el-table-column align="center" label="类目" prop="type1Name"></el-table-column>
        <el-table-column align="center" label="店铺等级" prop="platformShopLevelName"></el-table-column>
        <!-- <el-table-column
          align="center"
          label="店铺地区"
          prop="region"
        ></el-table-column> -->
        <el-table-column align="center" label="销售额" prop="saleroom">
          <template slot-scope="scope">
            <div class="column-box">￥{{ scope.row.saleroom | thousandBitSeparatorFilter }}</div>
          </template>
        </el-table-column>
        <el-table-column align="center" label="销售额行业占比" prop="industrySaleroomPct">
          <template slot-scope="{ row }"> {{ row.industrySaleroomPct }}%</template>
        </el-table-column>
        <el-table-column align="center" label="行业销售额" prop="industrySaleroom">
          <template slot-scope="scope">
            <div class="column-box">￥{{ scope.row.industrySaleroom | thousandBitSeparatorFilter }}</div>
          </template></el-table-column
        >
        <el-table-column align="center" label="销量" prop="sales"></el-table-column>
        <el-table-column align="center" label="销量行业占比" prop="industrySalesPct">
          <template slot-scope="{ row }"> {{ row.industrySalesPct }}%</template>
        </el-table-column>
        <el-table-column align="center" label="行业总销量" prop="industrySales"></el-table-column>
        <el-table-column align="center" label="状态" prop="status">
          <template slot-scope="{ row }"> {{ row.status | fisListStatus }}</template>
        </el-table-column>
        <el-table-column align="center" label="上榜时间" prop="status">
          <template slot-scope="{ row }"> {{ row.ym }}</template>
        </el-table-column>
        <el-table-column align="center" label="操作" fixed="right">
          <template slot-scope="{ row }">
            <div>
              <!-- <router-link :to="'/demo/detail/' + scope.row.id" class="link"> -->
              <!-- <Authority auth="/demo/detail"> -->
              <el-button size="small" v-if="row.status === 'UNRECEIVED'" @click="toClaim(row)" type="text">认领</el-button>
              <!-- </Authority> -->
              <!-- </router-link> -->

              <!-- <router-link :to="'/demo/detail/' + scope.row.id" class="link"> -->
              <!-- <Authority auth="/demo/detail"> -->
              <el-button size="small" @click="transfer(row)" type="text" v-if="row.status === 'UNRECEIVED'">转交</el-button>
              <!-- </Authority> -->
              <!-- </router-link> -->

              <el-button @click="invalid(row)" v-if="row.status === 'UNRECEIVED'" size="small" type="text">无效</el-button>
            </div>
            <router-link v-if="row.status === 'RECEIVE'" :to="'/customer-management/customer-clue-management/info/' + row.distributorExtId" class="link">
              <!-- <Authority auth="/demo/detail"> -->
              <el-button size="small" type="text">查看线索</el-button>
              <!-- </Authority> -->
            </router-link>
            <a :href="row.platformShopLink" class="link" target="_blank">
              <!-- <Authority auth="/demo/detail"> -->
              <el-button size="small" type="text">店铺主页</el-button>
              <!-- </Authority> -->
            </a>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination :current-page="pageNo" :disabled="listLoading" :page-size="pageSize" :page-sizes="[10, 20, 30, 40, 50]" :total="total" @current-change="handleCurrentChange" @size-change="handleSizeChange" background layout="total, sizes, prev, pager, next, jumper"></el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import FilterForm from '@/components/Form/FilterForm';
import formPopup from './popup/form-popup';
import pickBy from 'lodash/pickBy';
import omit from 'lodash/omit';
import { getShopBusinessList, getShopBusinessValidDistributorExtInfo, getShopBusinessRelationDistributorExtInfo, shopBusinessReceive, shopBusinessInvalid, shopBusinessForward } from '@/api/customer-management/business-opportunity';
import { mapGetters } from 'vuex';
import dict from '@/components/Common/dicts';

export default {
  name: 'customer-management-business-opportunity',
  data() {
    return {
      list: [],
      pageNo: 1,
      pageSize: 10,
      total: 0,
      listLoading: false
    };
  },
  filters: {
    fisListStatus(v = 'df') {
      const o = {
        UNRECEIVED: '待认领',
        RECEIVE: '已认领',
        df: ''
      };
      return o[v];
    }
  },
  components: { FilterForm },

  computed: {
    ...mapGetters(['userGroup', 'staffInfo']),
    searchFormOptions() {
      return [
        {
          component: 'select',
          label: '经营渠道',
          prop: 'developChannel',
          options: dict('COMMON_EXPAND_CHANNEL')
        },
        {
          component: 'input',
          label: '店铺ID',
          prop: 'platformShopId'
        },
        {
          component: 'input',
          label: '店铺名称',
          prop: 'platformShopName'
        },
        {
          component: 'select',
          label: '所属类目',
          prop: 'type1Name',
          /* eslint-disable */ // 关闭对整段代码的校验
          options: dict('COMMON_CATEGORY').then((res) => res.map((item) => ({ ...item, value: item.label })))
          /* eslint-enable */
        },
        {
          component: 'select',
          label: '状态',
          prop: 'status',
          options: [
            { value: 'UNRECEIVED', label: '待认领' },
            { value: 'RECEIVE', label: '已认领' }
          ]
        },
        {
          component: 'rangeInput',
          label: '销量占比',
          unit: '%',
          prop: 'industrySalesPctIntRange',
          props: ['startIndustrySalesPctInt', 'endIndustrySalesPctInt'],
        },
        {
          component: 'rangeInput',
          label: '销售额占比',
          unit: '%',
          prop: 'industrySaleroomPctInt',
          props: ['startIndustrySaleroomPctInt', 'endIndustrySaleroomPctInt'],
        },
        {
          component: 'select',
          label: '店铺等级',
          prop: 'platformShopLevel',
          options: dict('SHOP_LEVEL')
        },
        {
          component: 'dateRange',
          prop: 'ymDate',
          label: '上榜时间',
          type: 'month'
        }
      ];
    }
  },

  created() {},
  activated() {
    // 页面加载 自动运行
    this.fetchData();
  },
  mounted() {},

  methods: {
    //  点击认领
    toClaim(row) {
      const { platformShopId, id } = row;
      // 验证是否有线索
      getShopBusinessValidDistributorExtInfo({
        csGroupId: this.userGroup,
        platformShopId
      }).then((res) => {
        const { distributorExtId } = res.data || {};
        if (distributorExtId) {
          // 有线索
          this.openConfirm({ ...res.data, id });
        } else {
          // 无线索
          this.$popup(formPopup, {
            type: 'toClaim',
            title: '认领',
            rowData: row,
            csGroupId: this.userGroup
          }).then((res) => {
            this.shopBusinessReceive({ csGroupId: this.userGroup, ...res, id });
          });
        }
      });
    },
    // 认领接口
    shopBusinessReceive(data) {
      shopBusinessReceive(data).then((res) => {
        this.$message({
          type: 'success',
          message: '认领成功!'
        });
        this.fetchData();
      });
    },
    // 有线索直接关联
    openConfirm(data) {
      this.$confirm('已存在对应线索, 是否确认关联?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.associated(data);
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
    },
    // 店铺商机-关联
    associated(data) {
      getShopBusinessRelationDistributorExtInfo({
        distributorExtId: data.distributorExtId,
        id: data.id
      }).then((res) => {
        this.$message({
          type: 'success',
          message: '关联成功!'
        });
        this.fetchData();
      });
    },
    // 转交
    transfer(row) {
      const { id } = row;
      this.$popup(formPopup, {
        type: 'transfer',
        title: '选择转交人',
        rowData: row,
        staffInfo: this.staffInfo
      }).then((res) => {
        shopBusinessForward({ ...res, id }).then((res) => {
          this.$message({
            type: 'success',
            message: '转交成功!'
          });
          this.fetchData();
        });
      });
    },
    fetchData() {
      this.listLoading = true;
      const listQuery = {
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        data: this.getParams()
      };
      getShopBusinessList(listQuery)
        .then((response) => {
          const { list = [], total = 0 } = response.data || {};
          this.list = list;
          this.total = total;
        })
        .finally(() => {
          this.listLoading = false;
        });
    },
    // 获取列表参数
    getParams() {
      const formData = this.$refs.filterForm.getParams();

      const params = pickBy(
        formData,
        (val) => !!val
      );
      return omit(params, ['industrySalesPctIntRange', 'industrySaleroomPctInt']);
    },
    // 搜索
    onSubmit_searchForm() {
      this.pageNo = 1;
      this.pageSize = 10;
      this.fetchData();
    },
    // 分页控制pageSize
    handleSizeChange(val) {
      this.pageSize = val;
      this.pageNo = 1;
      this.fetchData();
    },
    // 分页控制pageNo
    handleCurrentChange(val) {
      this.pageNo = val;
      this.fetchData();
    },
    // 无效
    invalid(row) {
      const { remarks, id } = row;
      const conf = {
        type: 'invalid',
        title: '无效',
        remarks
      };
      this.$popup(formPopup, {
        ...conf
      }).then((res) => {
        shopBusinessInvalid({ ...res, id }).then((res) => {
          this.$message({
            type: 'success',
            message: '操作成功!'
          });
          this.fetchData();
        });
      });
    }
  }
};
</script>
<style lang='scss' scoped>
</style>
