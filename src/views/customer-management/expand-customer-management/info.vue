<template>
  <!-- 拓展客户信息信息 -->
  <div class="app-container">
    <div class="table-container">
      <h3>客户信息</h3>
      <PublicInfoTemporary
        v-if="distributorHeaderVO"
        :distributorHeaderVO="distributorHeaderVO"
        :id="id"
        authorityCode="/customer-management/expand-customer-management/info:edit"
        @onSuccess="getMultiDetail()"
      />
      <div class="tabs-container">
        <CustomerInfoForm
          v-if="distributorExtInfoVO"
          :distributorExtInfoVO="distributorExtInfoVO"
          :id="id"
          authorityCode="/customer-management/expand-customer-management/info:edit"
          isEXPAND
          @onSuccess="getMultiDetail()"
        />
      </div>
    </div>
  </div>
</template>
<script>
import PublicInfoTemporary from '@/views/customer-management/components/public-info-temporary';
import CustomerInfoForm from '@/views/customer-management/components/customer-info-form/index';
import { getDetail } from '@/api/distributorManagement/info';
export default {
  name: 'distributor-info',
  components: {
    PublicInfoTemporary,
    CustomerInfoForm
  },
  data() {
    return {
      activeName: 'basic',
      id: this.$route.params.id,
      distributorBodyVO: null, // 分销商合作信息
      distributorExtInfoVO: null, // 客户资料+客户潜力
      distributorHeaderVO: null, // 分销商头部信息
      violateRecordVOList: [], // 违规记录
      whitelistBriefVOList: [], // 店铺白名单信息
      distributorPurchaseDataVO: null // 分销商信息-采购情况
    };
  },
  props: {
    isDetails: {
      type: Boolean,
      default: false
    } // 是否是查看
  },
  activated() {
    this.getMultiDetail();
  },
  methods: {
    // 获取客户资料详情：分销商信息,合作信息，客户资料,基础信息
    getMultiDetail() {
      this.listLoading = true;
      getDetail(this.id)
        .then((response) => {
          const { header, distributorExtInfoVO } = response.data;
          this.distributorExtInfoVO = distributorExtInfoVO;
          this.distributorHeaderVO = header;
        })
        .finally(() => {
          this.listLoading = false;
        });
    },
    onSearch() {},
    handleCurrentChange() {},
    handleSizeChange() {}
  }
};
</script>

<style lang="scss" scoped>
.data-overview {
  .title {
    display: flex;
    align-items: center;
    .update-time {
      margin-left: 15px;
      color: #999;
    }
  }
  .distributor-data {
    border: 1px solid #ccc;
  }
}
.search-session {
  padding: 20px 0;
}
</style>
