<template>
  <div class="app-container wrap">
    <div class="table-container">
      <el-table :data="list" style="width: 100%">
        <el-table-column label="修改时间">
          <template slot-scope="scope">
            {{ scope.row.createDate | parseTime('{y}-{m}-{d} {h}:{i}') }}
          </template>
        </el-table-column>
        <el-table-column label="修改后结果">
          <template slot-scope="scope" v-if="scope.row.levelConfigVOList">
            <p v-for="item in scope.row.levelConfigVOList" :key="item.id">
              {{ item.name }}等级:{{ item.minAmount }}元{{
                item.lastFlag === '1' ? '及以上' : `-${item.maxAmount}元`
              }}
            </p>
          </template>
        </el-table-column>
        <el-table-column prop="createByName" label="修改人"> </el-table-column>
        <el-table-column prop="operateTypeName" label="类型"> </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          :current-page="listData.pageNo"
          :page-size="listData.pageSize"
          :page-sizes="[10, 20, 30, 40,50,100]"
          :total="total"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
          background
          layout="total, sizes, prev, pager, next, jumper"
        ></el-pagination>
      </div>
    </div>
  </div>
</template>
<script>
import { listOperateLog } from '@/api/customer-management/setting/index';
export default {
  data() {
    return {
      list: [],
      listData: {
        data: 'PURCHASE',
        pageNo: 1,
        pageSize: 10
      },
      total: 0
    };
  },
  activated() {
    this.getList();
  },
  methods: {
    getList() {
      listOperateLog(this.listData).then((res) => {
        this.list = res.data.list;
        this.total = res.data.total;
      });
    },
    handleSizeChange(val) {
      this.listData.pageSize = val;
      this.listData.pageNo = 1;
      this.getList();
    },
    handleCurrentChange(val) {
      this.listData.pageNo = val;
      this.getList();
    }
  }
};
</script>
<style lang="scss" scoped>
</style>
