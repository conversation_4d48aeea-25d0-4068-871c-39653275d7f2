<template>
  <div>
    <el-form :model="ruleForm" :rules="rules" ref="ruleForm" class="form" disabled>
      <div class="form-title">临时客户池回收规则</div>
      <div class="form-item">
        <span>是否启用回收到公海池规则：</span>
        <el-switch v-model="ruleForm.publicEnable"></el-switch>
      </div>
      <p class="form-text">以下条件只要达到一个就会直接回收到公海池</p>
      <div class="form-item">
        <span>条件一：</span> <span>领取当日是否需要有跟进记录：</span>
        <el-radio-group v-model="ruleForm.receiveFollowUp">
          <el-radio label="1">是</el-radio>
          <el-radio label="0">否</el-radio>
        </el-radio-group>
      </div>
      <div class="form-item">
        <span>条件二：</span> <span>领取后</span>
        <el-form-item label="" prop="receiveDay">
          <el-input v-model="ruleForm.receiveDay">
            <template slot="append">天</template>
          </el-input>
        </el-form-item>
        <span>内需要有动销</span>
      </div>
      <div class="form-title">运维客户池回收规则</div>
      <div class="form-item">
        <span>是否启用回收到公海池规则：</span>
        <el-switch v-model="ruleForm.tempEnable"></el-switch>
      </div>
      <p class="form-text">以下条件只要达到一个就会直接回收到公海池</p>
      <div class="form-item">
        <span>条件一：</span> <span>注册后</span>
        <el-form-item label="" prop="registerDay">
          <el-input v-model="ruleForm.registerDay">
            <template slot="append">天</template>
          </el-input>
        </el-form-item>
        <span>内无跟进记录并且无动销</span>
      </div>
      <div class="form-item">
        <span>条件二：</span> <span>最近</span>
        <el-form-item label="" prop="movingDay">
          <el-input v-model="ruleForm.movingDay">
            <template slot="append">天</template>
          </el-input>
        </el-form-item>
        <span>内无动销,并且近</span>
        <el-form-item label="" prop="followUpDay">
          <el-input v-model="ruleForm.followUpDay">
            <template slot="append">天</template>
          </el-input>
        </el-form-item>
        <span>内无跟进记录</span>
      </div>
      <!-- <el-form-item>
        <el-button
          type="primary"
          @click="submitForm('ruleForm')"
          :loading="saveLoding"
          >保存</el-button
        >
      </el-form-item> -->
    </el-form>
  </div>
</template>
<script>
import { distributorChangeRuleListAll, distributorChangeRuleUpdate } from '@/api/customer-management/setting/index';
export default {
  data() {
    const checkValidateInteger = (rule, value, callback) => {
      if (/^\+?[0-9]\d*$/.test(value)) {
        callback();
      } else {
        callback(new Error('请填写整数'));
      }
    };
    return {
      saveLoding: false,
      ruleForm: {
        publicEnable: true,
        receiveFollowUp: '1',
        receiveDay: '1',
        tempEnable: true,
        registerDay: '1',
        movingDay: '1',
        followUpDay: '1'
      },
      rules: {
        receiveDay: [{ required: true, validator: checkValidateInteger, trigger: 'blur' }],
        registerDay: [{ required: true, validator: checkValidateInteger, trigger: 'blur' }],
        movingDay: [{ required: true, validator: checkValidateInteger, trigger: 'blur' }],
        followUpDay: [{ required: true, validator: checkValidateInteger, trigger: 'blur' }]
      }
    };
  },
  created() {
    this.getInit();
  },
  methods: {
    getInit() {
      distributorChangeRuleListAll().then((res) => {
        res.data.forEach((item) => {
          if (item.type === 'TEMP') {
            this.ruleForm.publicEnable = item.isEnable === '1';
            item.ruleText.forEach((items) => {
              if (items.type === 'FINISH_TODO_PLAN') {
                this.ruleForm.receiveFollowUp = items.threshold;
              } else {
                this.ruleForm.receiveDay = items.threshold;
              }
            });
          } else {
            this.ruleForm.tempEnable = item.isEnable === '1';
            item.ruleText.forEach((items) => {
              if (items.type === 'FINISH_TODO_PLAN') {
                this.ruleForm.registerDay = items.threshold;
              } else {
                this.ruleForm.movingDay = items.threshold;
                this.ruleForm.followUpDay = items.nextThreshold;
              }
            });
          }
        });
      });
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.saveLoding = true;
          const parameter = [
            {
              ruleText: [
                {
                  threshold: this.ruleForm.receiveFollowUp,
                  type: 'FINISH_TODO_PLAN'
                },
                {
                  threshold: this.ruleForm.receiveDay,
                  type: 'FINISH_PURCHASE'
                }
              ],
              isEnable: this.ruleForm.tempEnable ? '1' : '0',
              type: 'TEMP'
            },
            {
              ruleText: [
                {
                  threshold: this.ruleForm.registerDay,
                  type: 'FINISH_TODO_PLAN'
                },
                {
                  threshold: this.ruleForm.movingDay,
                  nextThreshold: this.ruleForm.followUpDay,
                  type: 'MULTI'
                }
              ],
              isEnable: this.ruleForm.publicEnable ? '1' : '0',
              type: 'PUBLIC'
            }
          ];
          distributorChangeRuleUpdate(parameter)
            .then((res) => {
              if (res.code === '0') {
                this.$message({
                  message: '保存成功',
                  type: 'success'
                });
              }
            })
            .finally(() => {
              this.saveLoding = false;
            });
        } else {
          return false;
        }
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.form {
  &-title {
    border-left: 4px solid var(--color-primary);
    font-weight: bold;
    padding-left: 12px;
    margin-bottom: 20px;
  }
  &-text {
    color: var(--color-info);
    margin-top: 0;
    margin-bottom: 10px;
  }
  &-item {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    padding-bottom: 20px;
    .el-form-item {
      margin: 0 12px;
    }
  }
}
</style>
