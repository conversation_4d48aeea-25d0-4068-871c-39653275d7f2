<template>
  <div class="set-home">
    <el-form :model="form" :rules="rules" ref="form">
      <table
        cellspacing="0"
        cellpadding="0"
        border="0"
        class="table"
        style="width: 100%"
      >
        <thead class="table-head">
          <tr class="line">
            <th>
              <div class="cell">等级名称(依次递增)</div>
            </th>
            <th>
              <div class="cell">
                等级金额(依次递增)
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="等级金额设置是大于等于金额的下限，小于金额的上限；(例如：1≤D1等级＜100)"
                  placement="top"
                >
                  <i class="el-icon-question icon-style"></i>
                </el-tooltip>
              </div>
            </th>
            <th>
              <div class="cell">操作</div>
            </th>
          </tr>
        </thead>
        <tbody
          class="table-body"
          v-for="(item, index) in form.list"
          :key="index"
        >
          <tr class="line">
            <td class="set-home-level set-home-priceBox">
              <el-form-item
                :prop="`list[${index}].name`"
                :rules="{
                  required: true,
                  message: '请输入等级名称',
                  trigger: 'blur'
                }"
                label
              >
                <el-input
                  type="text"
                  size="small"
                  v-model="item.name"
                ></el-input>
              </el-form-item>
            </td>
            <td class="set-home-priceBox">
              <el-form-item
                :prop="`list[${index}].minAmount`"
                :rules="{
                  required: true,
                  validator: checkValidateInteger,
                  trigger: 'blur'
                }"
                class="minAmount-input"
                label
              >
                <el-input
                  type="input"
                  v-model="item.minAmount"
                  :disabled="index !== 0"
                >
                  <template slot="append">元</template>
                </el-input>
              </el-form-item>
              <template v-if="item.maxAmount !== 99999999">
                <span class="set-home-priceBox-span">至</span>
                <el-form-item
                  :prop="`list[${index}].maxAmount`"
                  :rules="{
                    required: true,
                    validator: checkValidateInteger,
                    trigger: 'blur'
                  }"
                  class="minAmount-input"
                  label
                >
                  <el-input
                    @input="inputmaxAmount(index)"
                    type="input"
                    v-model="item.maxAmount"
                  >
                    <template slot="append">元</template>
                  </el-input>
                </el-form-item>
              </template>
              <template v-else>
                <span>及以上</span>
                <span>最高等级</span>
              </template>
            </td>
            <td class="set-home-edit">
              <template v-if="index !== 0 && index === form.list.length - 1">
                <Authority auth="/customer-management/setting/:edit">
                  <el-button type="text" @click="del(index, item)"
                    >删除</el-button
                  >
                </Authority>
                <el-button type="text" @click="onDown(index)" v-if="maxLevel"
                  >取消最高等级</el-button
                >
                <el-button type="text" @click="onUp(index)" v-else
                  >设为最高等级</el-button
                >
              </template>
              <span v-else>/</span>
            </td>
          </tr>
        </tbody>
      </table>
    </el-form>
    <div class="set-home-table-btnBox">
      <el-button type="primary" size="small" @click="add" v-if="!maxLevel"
        >确定添加等级</el-button
      >
      <p v-else>如需添加等级，请取消当前的最高等级</p>
    </div>
    <el-dialog title=" 删除店铺等级" :visible.sync="dialogFormVisible">
      <p>确定要删除{{ ruleForm.name }}等级吗？</p>
      <div v-if="ruleForm.num">
        <p>
          该等级下有{{
            ruleForm.num
          }}个分销商，请将这些分销商转移等级后才能删除该等级！
        </p>
        <el-form
          :model="ruleForm"
          :rules="DiaRules"
          label-width="80px"
          ref="ruleForm"
        >
          <el-form-item label="转移等级" prop="toLevelId">
            <el-select v-model="ruleForm.toLevelId" placeholder="请选择">
              <el-option
                :label="item.label"
                :value="item.value"
                v-for="item in levelList"
                :key="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      <div v-else>该等级下面暂无分销商</div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="delSure" :loading="delLoading"
          >确 定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>
<script>
import {
  deletes,
  getLevelMember,
  listAllLevel
} from '@/api/customer-management/setting';
export default {
  props: {
    list: {
      type: Array,
      default() {
        return [];
      }
    },
    type: {
      type: String,
      default() {
        return 'PURCHASE';
      }
    }
  },
  data() {
    return {
      form: {
        list: []
      },
      rules: {},
      dialogFormVisible: false,
      ruleForm: {
        id: '',
        toLevelId: '',
        name: '',
        num: 0,
        region: ''
      },
      delIndex: '',
      DiaRules: {
        toLevelId: [
          { required: true, message: '请选择转移等级', trigger: 'change' }
        ]
      },
      levelList: [],
      delLoading: false
    };
  },
  watch: {
    list(val) {
      this.form.list = val;
    }
  },
  computed: {
    maxLevel() {
      let flag = false;
      this.form.list.forEach((item) => {
        if (item.maxAmount === 99999999) {
          flag = true;
        }
      });
      return flag;
    }
  },
  methods: {
    add() {
      if (this.form.list.length < 1) {
        this.form.list.push({
          name: '',
          minAmount: '',
          maxAmount: ''
        });
      } else {
        this.form.list.push({
          name: '',
          minAmount: this.form.list[this.form.list.length - 1].maxAmount,
          maxAmount: ''
        });
      }
    },
    del(index, item) {
      this.dialogFormVisible = true;
      this.delIndex = index;
      this.ruleForm.id = item.id;
      this.ruleForm.name = item.name;
      if (item.id) {
        this.fetchListAllLevel();
        getLevelMember(item.id).then((res) => {
          this.ruleForm.num = res.data;
        });
      }
    },
    onUp(index) {
      this.form.list[index].maxAmount = 99999999;
    },
    onDown(index) {
      this.form.list[index].maxAmount = '';
    },
    inputmaxAmount(index) {
      if (index !== this.form.list.length - 1) {
        this.form.list[index + 1].minAmount = this.form.list[index].maxAmount;
      }
    },
    delSure() {
      this.delLoading = true;
      if (this.ruleForm.id) {
        if (this.ruleForm.num > 0) {
          this.$refs['ruleForm'].validate((valid) => {
            if (valid) {
              const parameter = {
                id: this.ruleForm.id,
                toLevelId: this.ruleForm.toLevelId
              };
              deletes(parameter)
                .then((res) => {
                  if (res.code === '0') {
                    this.delFun();
                  }
                })
                .finally(() => {
                  this.delLoading = false;
                });
            } else {
              this.delLoading = false;
            }
          });
        } else {
          deletes({ id: this.ruleForm.id })
            .then((res) => {
              if (res.code === '0') {
                this.delFun();
              }
            })
            .finally(() => {
              this.delLoading = false;
            });
        }
      } else {
        this.delFun();
        this.delLoading = false;
      }
    },
    delFun() {
      this.form.list.splice(this.delIndex, 1);
      this.$message({
        type: 'success',
        message: '删除成功!'
      });
      this.dialogFormVisible = false;
    },
    // 店铺等级
    fetchListAllLevel() {
      listAllLevel({ purchaseType: this.type }).then((res) => {
        this.levelList = [];
        for (const item of res.data) {
          if (item.id !== this.ruleForm.id) {
            this.levelList.push({ value: item.id, label: item.name });
          }
        }
      });
    },
    checkValidateInteger(rule, value, callback) {
      if (/^(([1-9]{1}\d*)|(0{1}))(\.\d{1,2})?$/.test(value)) {
        if (value > 99999999) {
          callback(new Error('金额最大值为99999999'));
        } else {
          callback();
        }
      } else {
        callback(new Error('请填写数字'));
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.set-home {
  .table {
    text-align: center;
    th,
    td {
      text-overflow: ellipsis;
      word-wrap: break-word;
      vertical-align: middle;
      white-space: normal;
      word-break: break-all;
    }
    .cell {
      padding: 10px 0;
      width: 100%;
      display: inline-block;
      line-height: 23px;
    }
    .cell-div {
      min-height: 108px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      border-bottom: 1px solid #ebeef5;
      &:nth-last-of-type(1) {
        border-bottom: none;
      }
    }
    &-head {
      .line {
        background-color: #fafafa !important;
        th {
          font-size: 14px;
          font-weight: 700;
          color: #909399;
          border-bottom: 1px solid #ebeef5;
        }
      }
    }
    &-body {
      .line {
        background-color: #fff;
        transition: background-color 0.25s ease;
        &:hover {
          background-color: #f5f7fa;
        }
        td {
          font-size: 14px;
          color: #606266;
          border-bottom: 1px solid #ebeef5;
        }
      }
    }
  }
  &-table {
    &-btnBox {
      margin: 20px 0;
      text-align: center;
      border-bottom: 1px solid #ebeef5;
      padding: 10px;
    }
  }
  .el-input {
    width: 200px;
  }
  &-level {
    width: 300px;
  }
  &-priceBox {
    padding: 14px 0;
    .el-form-item {
      display: inline-block;
      margin-bottom: 0;
    }
  }
  &-edit {
    width: 240px;
  }
}
</style>
