<template>
  <div>
    <h3>“商品偏好”标签：做多可以添加20个标签</h3>
    <div class="form-box">
      <div class="form-box-row" v-for="(item, inx) of list" :key="inx">
        <el-input
          type="text"
          class="form-box-row-input"
          v-model="item.typeValue"
        ></el-input>
        <el-button type="text" @click="remove(inx)">删除</el-button>
      </div>
      <el-button
        type="text"
        @click="add"
        class="addbtn"
        v-if="this.list.length < 20"
        >+添加标签</el-button
      >
    </div>
    <div class="form-box-footer">
      <el-button type="primary" @click="onSave" :loading="submitLoading"
        >保存</el-button
      >
    </div>
  </div>
</template>

<script>
import { saveLabel, listAllLabel } from '@/api/customer-management/setting';
import { fn_debounce } from '@/utils/enhance';
export default {
  data() {
    return {
      list: [],
      submitLoading: false
    };
  },
  props: {
    activeName: {
      type: String,
      default() {
        return '1';
      }
    }
  },
  watch: {
    activeName(val) {
      if (val === '1') {
        this.getList();
      }
    }
  },
  methods: {
    add() {
      if (this.list.length > 20) {
        this.$message.error('最多添加20个');
        return;
      }
      const obj = {
        type: 'FAVORITE_COMMODITY',
        typeValue: ''
      };
      this.list.push(obj);
    },
    remove(inx) {
      this.list.splice(inx, 1);
    },
    onSave: fn_debounce(function () {
      const fn = this.list.find((item) => item.typeValue.trim() === '');
      if (fn) {
        this.$message.error('添加的标签不能为空');

        return;
      }
      this.submitLoading = true;
      this.$confirm('确认保存？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          saveLabel(this.list)
            .then((res) => {
              this.$message.success('保存成功');
              this.getList();
            })
            .finally(() => {
              this.submitLoading = false;
            });
        })
        .catch(() => {
          this.submitLoading = false;
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
    }),
    getList() {
      listAllLabel().then((res) => {
        this.list = res.data || [];
      });
    }
  }
};
</script>

<style  lang="scss" scoped>
.form-box {
  width: 500px;
  &-row {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    &-input {
      margin-right: 25px;
    }
  }
}
.addbtn {
  margin-bottom: 20px;
}
</style>
