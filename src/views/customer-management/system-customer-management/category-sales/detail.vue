<template>
  <div class="app-container">
    <div class="table-container">
      <Instructions :showTitle="true" :colNum="0">
        <template slot="title">
          <p>店铺名称：{{ queryData.shopName || '/' }}</p>
          <p>登录账号：{{ queryData.mobile || '/' }}</p>
          <p>品类名称：{{ queryData.categoryName || '/' }}</p>
        </template>
      </Instructions>
      <form @submit.prevent="onSearch" class="commo-search-container commo-search-flex commo-search-margin-bottom10">
        <div class="commo-search-item">
          <span class="commo-search-item-label">商品名称:</span>
          <div class="commo-search-item-content">
            <el-input clearable v-model="filter.commodityName"></el-input>
          </div>
        </div>
        <div class="commo-search-item">
          <span class="commo-search-item-label">商品ID:</span>
          <div class="commo-search-item-content">
            <el-input clearable v-model="filter.commodityId"></el-input>
          </div>
        </div>
        <div class="commo-search-item">
          <span class="commo-search-item-label">商品条码:</span>
          <div class="commo-search-item-content">
            <el-input clearable v-model="filter.commodityCode"></el-input>
          </div>
        </div>
        <div class="commo-search-item">
          <span class="commo-search-item-label">商品标识:</span>
          <div class="commo-search-item-content">
            <el-input clearable v-model="filter.skuId"></el-input>
          </div>
        </div>
        <div class="commo-search-btns">
          <el-button native-type="submit" size="small" type="primary">查询</el-button>
          <el-button @click="onReset" size="small">重置</el-button>
          <Authority auth="/customer-management/client-list/:export">
            <el-button :loading="exportLoading" @click="onExport" size="small">导出</el-button>
          </Authority>
        </div>
      </form>
      <el-table :data="list" element-loading-text="加载中" fit highlight-current-row ref="multipleTable" v-loading.body="listLoading">
        <el-table-column align="center" label="排名" prop="rankingNum"></el-table-column>
        <el-table-column align="center" label="商品名称" prop="commodityName"></el-table-column>
        <el-table-column align="center" label="商品ID" prop="commodityId"></el-table-column>
        <el-table-column align="center" label="采购总金额">
          <template slot-scope="scope">
            <p>{{ getMoney(scope.row.saleAmount) }}</p>
          </template>
        </el-table-column>
        <el-table-column align="center" label="订单总数" prop="purchaseOrderQuantity"></el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination :current-page="pageNo" :disabled="listLoading" :page-size="pageSize" :page-sizes="[10, 20, 30, 40, 50, 100]" :total="total" @current-change="handleCurrentChange" @size-change="handleSizeChange" background layout="total, sizes, prev, pager, next, jumper"></el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import { listCategoryCommoditySaleStatistic, exportCategoryCommoditySaleStatisticExcel } from '@/api/customer-management/client-list/category-sales';
import pickBy from 'lodash/pickBy'; // 返回一个新对象，值由真值组成
import cloneDeep from 'lodash/cloneDeep'; // 对象深拷贝
import download from '@/utils/download';
import { parseTime } from '@/utils';
import { getItem } from '@/utils/sessionStorage';
export default {
  name: 'detail',
  data() {
    const initFilter = {
      commodityName: '', // 商品名称
      commodityId: '', // 商品id
      commodityCode: '', // 商品条码
      skuId: '' // 商品标识
    };
    return {
      queryData: {},
      listLoading: true,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      initFilter, // 搜索条件初始值加入到Data
      filter: cloneDeep(initFilter), // 搜索条件的值
      list: [], // 列表
      exportLoading: false,
      defaultDate: ['00:00:00', '23:59:59'], // 默认时间段
      payData: null // 申请时间，由申请开始时间和申请结束时间组成
    };
  },
  computed: {
    // 表单接口传入的参数
    interfaceData() {
      const listQuery = pickBy(this.filter, (val) => !!val);
      if (Array.isArray(this.payData) && this.payData.length > 0) {
        // 时间条件
        const [startTime, endTime] = this.payData;
        listQuery.startTime = startTime.getTime();
        listQuery.endTime = endTime.getTime();
      }
      listQuery.categoryId = this.queryData.categoryId;
      listQuery.distributorId = this.queryData.id;
      return listQuery;
    }
  },
  activated() {
    // 页面加载事 自动运行
    const params = getItem(this.$route.path) || {};
    this.queryData = params;
    this.fetchData();
  },
  methods: {
    // 金额格式换处理
    getMoney(data) {
      return data === 0 || data ? '¥' + data : '/';
    },
    // 分页控制pageSize
    handleSizeChange(val) {
      this.pageSize = val;
      this.pageNo = 1;
      this.fetchData();
    },
    // 分页控制pageNo
    handleCurrentChange(val) {
      this.pageNo = val;
      this.fetchData();
    },
    // 重置
    onReset() {
      this.pageNo = 1;
      this.pageSize = 10;
      this.filter = cloneDeep(this.initFilter);
      this.payData = null;
      this.fetchData();
    },
    // 搜索
    onSearch() {
      this.pageNo = 1;
      this.pageSize = 10;
      this.fetchData();
    },
    // 获取列表信息
    fetchData() {
      this.listLoading = true;
      const listQuery = {
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        data: this.interfaceData
      };
      listCategoryCommoditySaleStatistic(listQuery)
        .then((response) => {
          const { list = [], total = 0 } = response.data || {};
          this.list = list;
          this.total = total;
        })
        .finally(() => {
          this.listLoading = false;
        });
    },
    onExport() {
      this.exportLoading = true;
      exportCategoryCommoditySaleStatisticExcel(this.interfaceData)
        .then((res) => {
          download(res, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', `商品采购明细-${parseTime(Date.now(), '{y}-{m}-{d}-{h}:{i}:{s}')}.xlsx`);
        })
        .finally(() => {
          this.exportLoading = false;
        });
    }
  }
};
</script>

<style lang="scss" scoped></style>
