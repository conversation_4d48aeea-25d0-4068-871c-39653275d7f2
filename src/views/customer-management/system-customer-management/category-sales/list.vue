<template>
  <div class="app-container">
    <div class="table-container">
      <Instructions :showTitle="true" :colNum="0">
        <template slot="title">
          <p>店铺名称：{{ queryData.shopName || '/' }}</p>
          <p>登录账号：{{ queryData.mobile || '/' }}</p>
        </template>
      </Instructions>
      <form @submit.prevent="onSearch" class="commo-search-container commo-search-flex commo-search-margin-bottom10">
        <div class="commo-search-item">
          <span class="commo-search-item-label">品类名称:</span>
          <div class="commo-search-item-content">
            <el-select clearable filterable multiple placeholder="请选择" v-model="filter.categoryIds" :loading="categoryLoading">
              <el-option :key="category.id" :label="category.name" :value="category.id" v-for="category in categories"></el-option>
            </el-select>
          </div>
        </div>
        <div class="commo-search-item">
          <span class="commo-search-item-label">下单时间:</span>
          <div class="commo-search-item-content">
            <el-date-picker v-model="payData" type="datetimerange" align="right" start-placeholder="开始日期" end-placeholder="结束日期" :default-time="['00:00:00', '23:59:59']"></el-date-picker>
          </div>
        </div>
        <div class="commo-search-btns">
          <el-button native-type="submit" size="small" type="primary">查询</el-button>
          <el-button @click="onReset" size="small">重置</el-button>
          <Authority auth="/customer-management/client-list/:export">
            <el-button :loading="exportLoading" @click="onExport" size="small">导出</el-button>
          </Authority>
        </div>
      </form>
      <el-table :data="list" element-loading-text="加载中" fit highlight-current-row ref="multipleTable" v-loading.body="listLoading">
        <el-table-column align="center" label="排名" prop="rankingNum"></el-table-column>
        <el-table-column align="center" label="品类名称" prop="categoryName"></el-table-column>
        <el-table-column align="center" label="已采购商品数" prop="purchaseCommodityQuantity"></el-table-column>
        <el-table-column align="center" label="销售金额">
          <template slot-scope="scope">
            <p class="link" @click="toLink('detail', scope.row)">
              {{ getMoney(scope.row.saleAmount) }}
            </p>
          </template>
        </el-table-column>
        <el-table-column align="center" label="订单总数" prop="purchaseOrderQuantity"></el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination :current-page="pageNo" :disabled="listLoading" :page-size="pageSize" :page-sizes="[10, 20, 30, 40, 50, 100]" :total="total" @current-change="handleCurrentChange" @size-change="handleSizeChange" background layout="total, sizes, prev, pager, next, jumper"></el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import { listCategorySaleStatistic, exportCategorySaleStatisticExcel, listCommodityParams } from '@/api/customer-management/client-list/category-sales';
import pickBy from 'lodash/pickBy'; // 返回一个新对象，值由真值组成
import cloneDeep from 'lodash/cloneDeep'; // 对象深拷贝
import download from '@/utils/download';
import { parseTime } from '@/utils';
import { getItem } from '@/utils/sessionStorage';
export default {
  name: 'category-sales',
  data() {
    const initFilter = {
      categoryIds: [] // 品类名称
    };
    return {
      queryData: {},
      listLoading: true,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      initFilter, // 搜索条件初始值加入到Data
      filter: cloneDeep(initFilter), // 搜索条件的值
      list: [], // 列表
      exportLoading: false,
      defaultDate: ['00:00:00', '23:59:59'], // 默认时间段
      categories: [], // 分组
      categoryLoading: false,
      payData: [] // 申请时间，由申请开始时间和申请结束时间组成
    };
  },
  computed: {
    // 表单接口传入的参数
    interfaceData() {
      const listQuery = pickBy(this.filter, (val) => !!val);
      if (Array.isArray(this.payData) && this.payData.length > 0) {
        // 时间条件
        const [startTime, endTime] = this.payData;
        listQuery.startTime = startTime.getTime();
        listQuery.endTime = endTime.getTime();
      }
      listQuery.distributorId = this.queryData.id;
      return listQuery;
    }
  },
  activated() {
    // 页面加载事 自动运行
    const params = getItem(this.$route.path) || {};
    this.queryData = params;
    this.fetchData();
    this.fetchCategory();
  },
  methods: {
    // 金额格式换处理
    getMoney(data) {
      return data === 0 || data ? '¥' + data : '/';
    },
    fetchCategory() {
      // 品类名称列表
      this.categoryLoading = true;
      listCommodityParams()
        .then((res) => {
          this.categories = res.data;
        })
        .finally(() => {
          this.categoryLoading = false;
        });
    },
    // 分页控制pageSize
    handleSizeChange(val) {
      this.pageSize = val;
      this.pageNo = 1;
      this.fetchData();
    },
    // 分页控制pageNo
    handleCurrentChange(val) {
      this.pageNo = val;
      this.fetchData();
    },
    // 重置
    onReset() {
      this.pageNo = 1;
      this.pageSize = 10;
      this.filter = cloneDeep(this.initFilter);
      this.payData = [];
      this.fetchData();
    },
    // 搜索
    onSearch() {
      this.pageNo = 1;
      this.pageSize = 10;
      this.fetchData();
    },
    // 获取列表信息
    fetchData() {
      this.listLoading = true;
      const listQuery = {
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        data: this.interfaceData
      };
      listCategorySaleStatistic(listQuery)
        .then((response) => {
          const { list = [], total = 0 } = response.data || {};
          this.list = list;
          this.total = total;
        })
        .finally(() => {
          this.listLoading = false;
        });
    },
    onExport() {
      this.exportLoading = true;
      exportCategorySaleStatisticExcel(this.interfaceData)
        .then((res) => {
          download(res, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', `品类销售列表-${parseTime(Date.now(), '{y}-{m}-{d}-{h}:{i}:{s}')}.xlsx`);
        })
        .finally(() => {
          this.exportLoading = false;
        });
    },
    toLink(type, row) {
      const { categoryName, categoryId } = row;
      const listData = {
        id: this.queryData.id,
        shopName: this.queryData.shopName,
        mobile: this.queryData.mobile,
        categoryName,
        categoryId
      };
      const list = [
        {
          key: 'detail',
          label: '商品采购明细',
          value: '/customer-management/system-customer-management/category-sales/detail'
        }
      ];
      for (const item of list) {
        if (item.key === type) {
          sessionStorage.setItem(item.value, JSON.stringify(listData));
          this.$router.push({
            path: item.value
          });
          break;
        }
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.link {
  color: var(--color-primary);
  cursor: pointer;
}
</style>
