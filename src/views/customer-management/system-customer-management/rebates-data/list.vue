<template>
  <div class="app-container">
    <div class="table-container">
      <Instructions :showTitle="true" :colNum="0">
        <template slot="title">
          <p>店铺名称：{{ queryData.shopName || '/' }}</p>
          <p>登录账号：{{ queryData.mobile || '/' }}</p>
        </template>
      </Instructions>
      <el-radio-group v-model="type" class="tab-class">
        <el-radio-button label="MONTH">品牌月度返点</el-radio-button>
        <el-radio-button label="QUARTER">品牌季度返点</el-radio-button>
      </el-radio-group>
      <el-table
        :data="list"
        element-loading-text="加载中"
        fit
        highlight-current-row
        ref="multipleTable"
        v-loading.body="listLoading"
      >
        <el-table-column
          align="center"
          label="时间"
          prop="periodDesc"
        ></el-table-column>
        <el-table-column align="center" label="活动返点金额">
          <template slot-scope="scope">
            <p
              v-if="scope.row.activityVirtualCredit"
              class="link"
              @click="toLink('detail', scope.row)"
            >
              {{ getMoney(scope.row.activityVirtualCredit) }}
            </p>
            <p v-else>¥0</p>
          </template>
        </el-table-column>
        <el-table-column align="center" label="返点使用金额">
          <template slot-scope="scope">
            <p>{{ getMoney(scope.row.usedVirtualCredit) }}</p>
          </template>
        </el-table-column>
        <el-table-column align="center" label="返点充值金额">
          <template slot-scope="scope">
            <p>{{ getMoney(scope.row.rechargeVirtualCredit) }}</p>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination" v-if="type === 'MONTH'">
        <el-pagination
          :current-page="pageNo"
          :disabled="listLoading"
          :page-size="pageSize"
          :page-sizes="[10, 20, 30, 40,50,100]"
          :total="total"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
          background
          layout="total, sizes, prev, pager, next, jumper"
        ></el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import {
  listDistributorMonthStatistic,
  listDistributorQuarterStatistic,
  exportDistributorMonthStatistic,
  exportDistributorQuarterStatistic
} from '@/api/customer-management/client-list/category-sales';
import download from '@/utils/download';
import { parseTime } from '@/utils';
import { getItem } from '@/utils/sessionStorage';
export default {
  name: 'category-sales',
  data() {
    return {
      type: 'MONTH', // 默认显示月度返点
      queryData: {},
      listLoading: true,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      list: [], // 列表
      exportLoading: false
    };
  },
  computed: {
    // 表单接口传入的参数
    interfaceData() {
      return { distributorId: this.queryData.id };
    }
  },
  watch: {
    type(value) {
      this.fetchData();
    }
  },
  activated() {
    // 页面加载事 自动运行
    const params = getItem(this.$route.path) || {};
    this.queryData = params;
    this.fetchData();
  },
  methods: {
    // 金额格式换处理
    getMoney(data) {
      return data === 0 || data ? '¥' + data : '/';
    },
    // 分页控制pageSize
    handleSizeChange(val) {
      this.pageSize = val;
      this.pageNo = 1;
      this.fetchData();
    },
    // 分页控制pageNo
    handleCurrentChange(val) {
      this.pageNo = val;
      this.fetchData();
    },
    // 获取列表信息
    fetchData() {
      this.listLoading = true;
      const listQuery =
        this.type === 'MONTH'
          ? {
              pageNo: this.pageNo,
              pageSize: this.pageSize,
              data: this.interfaceData
            }
          : { ...this.interfaceData };
      const request =
        this.type === 'MONTH'
          ? listDistributorMonthStatistic
          : listDistributorQuarterStatistic;
      request(listQuery)
        .then((response) => {
          if (this.type === 'MONTH') {
            const { list = [], total = 0 } = response.data || {};
            this.list = list;
            this.total = total;
          } else {
            this.list = response.data || [];
          }
        })
        .finally(() => {
          this.listLoading = false;
        });
    },
    onExport() {
      this.exportLoading = true;
      const request =
        this.type === 'MONTH'
          ? exportDistributorMonthStatistic
          : exportDistributorQuarterStatistic;
      request(this.interfaceData)
        .then((res) => {
          download(
            res,
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            `品类销售明细-${parseTime(
              Date.now(),
              '{y}-{m}-{d}-{h}:{i}:{s}'
            )}.xlsx`
          );
        })
        .finally(() => {
          this.exportLoading = false;
        });
    },
    toLink(type, row) {
      const { periodDesc, period } = row;
      const listData = {
        id: this.queryData.id,
        shopName: this.queryData.shopName,
        mobile: this.queryData.mobile,
        periodDesc,
        period,
        type: this.type
      };
      const list = [
        {
          key: 'detail',
          label: '商品采购明细',
          value:
            '/customer-management/system-customer-management/rebates-data/detail'
        }
      ];
      for (const item of list) {
        if (item.key === type) {
          sessionStorage.setItem(item.value, JSON.stringify(listData));
           this.$router.push({
            path:item.value
          });
          break;
        }
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.tab-class {
  margin-bottom: 20px;
}
.link {
  color: var(--color-primary);
  cursor: pointer;
}
</style>
