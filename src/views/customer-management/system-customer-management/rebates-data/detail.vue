<template>
  <div class="app-container">
    <div class="table-container">
      <Instructions :showTitle="true" :colNum="0">
        <template slot="title">
          <p>店铺名称：{{ queryData.shopName || '/' }}</p>
          <p>登录账号：{{ queryData.mobile || '/' }}</p>
          <p>{{ queryData.periodDesc }}</p>
        </template>
      </Instructions>
      <el-table
        :data="list"
        element-loading-text="加载中"
        fit
        highlight-current-row
        ref="multipleTable"
        v-loading.body="listLoading"
      >
        <el-table-column
          align="center"
          label="品牌名称"
          prop="brandName"
        ></el-table-column>
        <el-table-column align="center" label="采购总金额">
          <template slot-scope="scope">
            <p>{{ getMoney(scope.row.totalAmount) }}</p>
          </template>
        </el-table-column>
        <el-table-column align="center" label="一件代发采货金额">
          <template slot-scope="scope">
            <p>{{ getMoney(scope.row.totalDropshippingAmount) }}</p>
          </template>
        </el-table-column>
        <el-table-column align="center" label="采销采货金额">
          <template slot-scope="scope">
            <p>{{ getMoney(scope.row.totalPurchaseAmount) }}</p>
          </template>
        </el-table-column>
        <el-table-column align="center" label="返点总金额">
          <template slot-scope="scope">
            <p>{{ getMoney(scope.row.totalActualCreditBack) }}</p>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          :current-page="pageNo"
          :disabled="listLoading"
          :page-size="pageSize"
          :page-sizes="[10, 20, 30, 40,50,100]"
          :total="total"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
          background
          layout="total, sizes, prev, pager, next, jumper"
        ></el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import { listDistributorCreditBack } from '@/api/customer-management/client-list/category-sales';
import { getItem } from '@/utils/sessionStorage';
export default {
  name: 'detail',
  data() {
    return {
      queryData: {},
      listLoading: true,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      list: [], // 列表
      exportLoading: false,
      defaultDate: ['00:00:00', '23:59:59'], // 默认时间段
      payData: null // 申请时间，由申请开始时间和申请结束时间组成
    };
  },
  computed: {
    // 表单接口传入的参数
    interfaceData() {
      return {
        type: this.queryData.type,
        distributorId: this.queryData.id,
        period: this.queryData.period
      };
    }
  },
  activated() {
    // 页面加载事 自动运行
    const params = getItem(this.$route.path) || {};
    this.queryData = params;
    this.fetchData();
  },
  methods: {
    // 金额格式换处理
    getMoney(data) {
      return data === 0 || data ? '¥' + data : '/';
    },
    // 分页控制pageSize
    handleSizeChange(val) {
      this.pageSize = val;
      this.pageNo = 1;
      this.fetchData();
    },
    // 分页控制pageNo
    handleCurrentChange(val) {
      this.pageNo = val;
      this.fetchData();
    },
    // 获取列表信息
    fetchData() {
      this.listLoading = true;
      const listQuery = {
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        data: this.interfaceData
      };
      listDistributorCreditBack(listQuery)
        .then((response) => {
          const { list = [], total = 0 } = response.data || {};
          this.list = list;
          this.total = total;
        })
        .finally(() => {
          this.listLoading = false;
        });
    }
  }
};
</script>

<style lang="scss" scoped>
</style>
