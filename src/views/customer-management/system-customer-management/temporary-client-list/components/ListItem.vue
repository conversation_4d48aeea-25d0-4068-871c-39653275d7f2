<template>
  <!-- 客户列表代发 -->
  <div>
    <!-- 数据总览 -->
    <div class="top">
      <div class="top-data">
        <GeneralSituation
          :list="list"
          v-loading="listLoading"
        ></GeneralSituation>
      </div>
    </div>
    <div class="top-text">
      <p>怎样将【临时客户池】里面的客户转到自己的【运维客户池】：</p>
      <p>
        1、客户领取的当天记得去跟进客户然后填写好自己的跟进记录(必须当天完成)；
      </p>
      <p>2、领取7天内需要促使客户动销，样品订单不计算在动销内；</p>
      <p>
        3、以上两点只要有一个没有满足的话客户就被自动被回收到公海池！(如果用户产生了动销，那么是不会划入公海池)
      </p>
    </div>
    <!-- 搜索区域 -->
    <div class="search">
      <FormSearch
        :searchType="'temporary'"
        :accountType="type"
        :exportShow="exportShow"
        @parameterDataSearch="parameterDataSearch"
        @reset="reset"
        @parameterDataExport="parameterDataExport"
      />
    </div>
    <!-- 表格数据列表 -->
    <el-table
      :data="tableData"
      ref="tableData"
      style="width: 100%"
      @sort-change="sortChange"
    >
      <el-table-column align="center" label="分销商ID" prop="id"></el-table-column>
      <el-table-column label="店铺信息" align="left" width="200">
        <template slot-scope="scope">
          <div>店铺：{{ scope.row.shopName || '/' }}</div>
          <div>账号：{{ scope.row.mobile || '/' }}</div>
          <div>店铺ID：{{ scope.row.platformShopId || '/' }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="levelName" label="店铺等级" align="center">
        <template slot-scope="scope">
          {{ scope.row.levelName || '/' }}
        </template></el-table-column
      >
      <el-table-column
        label="本月采购金额（元）"
        align="center"
        sortable="custom"
        width="190px"
        prop="totalMonthPurchaseAmount"
      >
        <template slot="header" slot-scope="scope">
          本月采购金额（元）
          <el-tooltip class="item" effect="dark" placement="top-start">
            <div slot="content">
              本月采货金额统计的时间为截止当天零点的数据，非实时数据
            </div>
            <i class="el-icon-question"></i>
          </el-tooltip>
        </template>
        <template slot-scope="scope">
          <el-button
            type="text"
            @click="toLink('purchasingDataMonth', scope.row)"
            v-if="scope.row.totalMonthPurchaseAmount > 0"
            >{{ scope.row.totalMonthPurchaseAmount }}</el-button
          >
          <span v-else>{{ scope.row.totalMonthPurchaseAmount }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center">
        <template slot="header" slot-scope="scope">
          采购总金额(元)
          <el-tooltip class="item" effect="dark" placement="top-start">
            <div slot="content">采购总金额是不含运费，并且扣除了退款的金额</div>
            <i class="el-icon-question"></i>
          </el-tooltip>
        </template>
        <template slot-scope="scope">
          <el-button
            type="text"
            @click="toLink('purchasingData', scope.row)"
            v-if="scope.row.totalPurchaseAmount > 0"
            >{{ scope.row.totalPurchaseAmount }}</el-button
          >
          <span v-else>{{ scope.row.totalPurchaseAmount }}</span>
        </template>
      </el-table-column>
      <el-table-column label="返点金额（元）" align="center" width="130">
        <template slot-scope="scope">
          <div>返点总额:{{ scope.row.totalVirtualCredit }}</div>
          <div>可用返点:{{ scope.row.totalUsableVirtualCredit }}</div>
          <ViewRebatesProgress
            :id="scope.row.id"
            :shopName="scope.row.shopName"
          ></ViewRebatesProgress>
        </template>
      </el-table-column>
      <el-table-column
        prop="averageOrderAmount"
        label="客单价（元）"
        align="center"
      ></el-table-column>
      <el-table-column label="采购数据" align="center">
        <template slot-scope="scope">
          <div>
            <span>采购品牌数:</span>
            <el-button
              v-if="scope.row.purchaseBrandCount > 0"
              type="text"
              @click="toLink('purchasingBrandList', scope.row)"
              >{{ scope.row.purchaseBrandCount }}</el-button
            >
            <span v-else>{{ scope.row.purchaseBrandCount }}</span>
          </div>
          <div>
            <span>采购商品数:</span>
            <el-button
              v-if="scope.row.purchaseCommodityCount > 0"
              type="text"
              @click="toLink('purchaseGoods', scope.row)"
              >{{ scope.row.purchaseCommodityCount }}</el-button
            >
            <span v-else>{{ scope.row.purchaseCommodityCount }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="偏好品类前三" align="center" width="130">
        <template slot-scope="scope">
          <div v-if="scope.row.favoriteCategoryList.length > 0">
            {{ scope.row.favoriteCategoryList.join('、') }}
          </div>
          <span v-else>/</span>
          <div>
            <el-button
              type="text"
              @click="toLink('categorySalesList', scope.row)"
              >品类销售排行</el-button
            >
          </div>
        </template>
      </el-table-column>
      <el-table-column label="合作信息" align="center" width="100px">
        <template slot-scope="scope">
          <div>
            合同:
            {{ scope.row.distributorContractNum }}份
          </div>
          <div>
            授权书:
            {{ scope.row.distributorLicenseNum }}份
          </div>
          <AuthorizationProgress :rowData="scope.row" />
        </template>
      </el-table-column>
      <el-table-column align="center" label="专属顾问">
        <template slot-scope="scope">
          {{ scope.row.customerServiceName || '/' }}
        </template>
      </el-table-column>
      <el-table-column align="center">
        <template slot="header" slot-scope="scope">
          领取时间
          <el-tooltip class="item" effect="dark" placement="top-start">
            <div slot="content">客户最新从公海池领取的时间</div>
            <i class="el-icon-question"></i>
          </el-tooltip>
        </template>
        <template slot-scope="scope">
          {{ scope.row.flowDate | parseTime }}
        </template>
      </el-table-column>
      <el-table-column label="入驻时间" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.auditDate | parseTime }}</span>
        </template>
      </el-table-column>
      <el-table-column label="绑定企业微信时间" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.firstWechatBindingDate | parseTime }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="address" label="操作" align="center">
        <template slot-scope="scope">
          <Authority auth="/customer-management/follow-up/:edit">
            <!-- <followUpDialog
              btnTitle="快速跟进"
              btnType="text"
              :id="scope.row.id"
            /> -->
            <el-button
              type="text"
              @click="$refs.dialog_followUp.setVisible(true, scope.row)"
              >快速跟进</el-button
            >
          </Authority>

          <router-link
            :to="`/customer-management/system-customer-management/temporary-client-list/recode/${scope.row.id}`"
          >
            <el-button type="text">跟进记录</el-button>
          </router-link>
          <router-link
            :to="`/customer-management/system-customer-management/temporary-client-list/info/${scope.row.id}`"
          >
            <el-button type="text">查看信息</el-button>
          </router-link>
        </template>
      </el-table-column>
    </el-table>
    <div class="pagination">
      <el-pagination
        :current-page="parameter.pageNo"
        :page-size="parameter.pageSize"
        :page-sizes="[10, 20, 30, 40, 50, 100]"
        :total="total"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
        background
        layout="total, sizes, prev, pager, next, jumper"
      ></el-pagination>
    </div>
    <Dialog
      :callback="onSubmitFollowUp"
      class="dialog"
      type="CLUE_FOLLOWUP"
      ref="dialog_followUp"
    >
    </Dialog>
  </div>
</template>
<script>
import {
  getTodayStatistic,
  listTempMultiPage,
  exportTempMulti
} from '@/api/customer-management/temporary-client-list';
import GeneralSituation from '@/components/views/customer-management/GeneralSituation';
import FormSearch from '@/components/views/customer-management/FormSearch';
import ViewRebatesProgress from '@/components/views/customer-management/ViewRebatesProgress';
import download from '@/utils/download';
import { parseTime } from '@/utils';
import { mapGetters } from 'vuex';
import AuthorizationProgress from '@/components/AuthorizationProgress';
// import followUpDialog from '@/views/customer-management/components/follow-up-dialog';
import Dialog from '@/components/Dialog/index.vue';
import { createFinishedTodo } from '@/api/customer-management/info';

export default {
  name: 'temporary-client-list',
  components: {
    GeneralSituation,
    FormSearch,
    ViewRebatesProgress,
    AuthorizationProgress,
    //   followUpDialog,
    Dialog
  },
  props: {
    type: {
      type: String,
      default() {
        return '';
      }
    }
  },
  data() {
    return {
      exportShow: false,
      parameter: { data: {}, pageNo: 1, pageSize: 10 },
      list: [],
      listLoading: false,
      tableData: [],
      total: 0
    };
  },
  computed: {
    ...mapGetters('shop', ['btnSet'])
  },
  created() {
    this.exportShow = this.btnSet.has(
      '/customer-management/temporary-client-list/:export'
    );
  },
  methods: {
    init() {
      this.getStatistic();
      this.getList();
    },
    getStatistic() {
      this.listLoading = true;
      getTodayStatistic(this.type)
        .then((res) => {
          this.list = [
            {
              title: '客户总数',
              amount: res.data.totalNum
            },
            {
              title: '即将回收客户数',
              amount: res.data.comingToPublicNum,
              tips: '即将回收客户数：是指客户离划入公海池的时间小于等于3天的客户总数'
            },
            {
              title: '今日领取客户数',
              amount: res.data.todayDrawNum
            },
            {
              title: '今日回收公海池客户数',
              amount: res.data.todayNewPublicNum
            },
            {
              title: '今日已入运维池客户数',
              amount: res.data.todayNewExclusiveNum
            }
          ];
        })
        .finally(() => {
          this.listLoading = false;
        });
    },
    handleSizeChange(val) {
      this.parameter.pageSize = val;
      this.parameter.pageNo = 1;
      this.getList();
    },
    handleCurrentChange(val) {
      this.parameter.pageNo = val;
      this.getList();
    },
    getList() {
      const parameter = this.parameter;
      parameter.data.purchaseType = this.type;
      listTempMultiPage(parameter).then((res) => {
        this.tableData = res.data.list;
        this.total = res.data.total;
      });
    },
    // 查询
    parameterDataSearch(data) {
      this.parameter.data = data;
      this.getList();
    },
    // 重置
    reset() {
      this.parameter.data = {};
      this.$refs.tableData.clearSort();
      this.getList();
    },
    // 导出
    parameterDataExport(data) {
      const parameter = data;
      parameter.purchaseType = this.type;
      exportTempMulti(parameter).then((res) => {
        download(
          res,
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          `临时池客户列表-${
            this.type === ''
              ? '全部'
              : this.type === 'PURCHASE'
              ? '采销'
              : this.type === 'DROP_SHIPPING'
              ? '一件代发'
              : ''
          }-${parseTime(Date.now(), '{y}-{m}-{d}-{h}:{i}:{s}')}.xlsx`
        );
      });
    },
    // 品类销售
    toLink(type, row) {
      const { id, shopName, mobile, purchaseBrandCount } = row;
      const categorySalesListData = {
        id,
        shopName,
        mobile,
        purchaseBrandCount
      };
      const list = [
        {
          key: 'purchasingData',
          label: '采购数据',
          value:
            '/customer-management/system-customer-management/purchasing-data'
        },
        {
          key: 'rebatesDataList',
          label: '返点分布',
          value:
            '/customer-management/system-customer-management/rebates-data/list'
        },
        {
          key: 'purchasingDataMonth',
          label: '采购数据本月',
          value:
            '/customer-management/system-customer-management/purchasing-data'
        },
        {
          key: 'purchasingBrandList',
          label: '采购品牌',
          value:
            '/customer-management/system-customer-management/purchasing-brand/list'
        },
        {
          key: 'purchaseGoods',
          label: '采购商品',
          value:
            '/customer-management/system-customer-management/purchase-goods'
        },
        {
          key: 'categorySalesList',
          label: '品类销售',
          value:
            '/customer-management/system-customer-management/category-sales/list'
        },
        {
          key: 'freightDetail',
          label: '运费金额',
          value:
            '/customer-management/system-customer-management/freight-detail'
        }
      ];
      for (const item of list) {
        if (item.key === type) {
          if (item.key === 'purchasingDataMonth') {
            categorySalesListData.statisticalType = 'month';
          }
          sessionStorage.setItem(
            item.value,
            JSON.stringify(categorySalesListData)
          );
          this.$router.push({
            path: item.value
          });
          break;
        }
      }
    },
    sortChange(data) {
      const { prop, order } = data;
      const orderKey = {
        ascending: 'ASC',
        descending: 'DESC'
      }[order];
      if (prop === 'totalMonthPurchaseAmount') {
        this.parameter.data.orderByClause = orderKey;
      }
      if (!prop) {
        delete this.parameter.data.orderByClause;
      }
      this.getList();
    },
    onSubmitFollowUp(followUpData, dialog) {
      const data = {
        ...followUpData,
        distributorId: dialog.externalData.id
      };
      createFinishedTodo(data).then((res) => {
        console.dir(res);
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.top {
  &-title {
    display: flex;
    align-items: center;
  }
  &-time {
    margin-left: 15px;
    color: #999;
  }
  &-data {
    border: 1px solid #ccc;
  }
  &-text {
    margin: 20px 0;
    padding: 10px 20px;
    background-color: rgba(64, 158, 255, 0.19);
    border: 1px solid var(--color-primary);
    color: #000;
    font-size: 14px;
    p {
      margin-top: 0;
      margin-bottom: 4px;
    }
  }
}
.search {
  margin-top: 20px;
}
.level {
  .el-input,
  .el-select {
    width: 240px;
  }
}
</style>
