<template>
  <!-- 分销商信息 -->
  <div class="app-container">
    <div class="table-container">
      <h3>分销商信息</h3>
      <public-info
        :authorityCode="authorityCode"
        v-if="distributorBodyVO && distributorHeaderVO"
        :distributorBodyVO="distributorBodyVO"
        :distributorHeaderVO="distributorHeaderVO"
        :id="id"
        :isDetails="isDetails"
        @onSuccess="getMultiDetail()"
      />
      <div class="tabs-container">
        <el-tabs v-model="activeName" type="card">
          <el-tab-pane label="基础信息" name="basic">
            <CustomerInfoForm
              :authorityCode="authorityCode"
              v-if="distributorBodyVO && distributorExtInfoVO"
              :distributorBodyVO="distributorBodyVO"
              :distributorExtInfoVO="distributorExtInfoVO"
              :violateRecordVOList="violateRecordVOList"
              :whitelistBriefVOList="whitelistBriefVOList"
              :id="id"
              @onSuccess="getMultiDetail()"
              :isDetails="isDetails"
          /></el-tab-pane>
          <el-tab-pane label="采购情况" name="purchasing">
            <PurchasingSituation
              v-if="distributorPurchaseDataVO && distributorBodyVO"
              :distributorPurchaseDataVO="distributorPurchaseDataVO"
              :distributorBodyVO="distributorBodyVO"
              :isDetails="isDetails"
            />
          </el-tab-pane>
          <el-tab-pane label="专属顾问变动记录" name="changeRecord">
            <keep-alive>
              <CustomerServiceCList
                v-if="activeName === 'changeRecord'"
                :isDetails="isDetails"
                :id="id"
                ref="CSCL"
              />
            </keep-alive>
          </el-tab-pane>
          <el-tab-pane label="客户领取记录" name="getTheRecord">
            <DrawRecordList
              v-if="activeName === 'getTheRecord'"
              :isDetails="isDetails"
              :id="id"
            />
          </el-tab-pane>
          <el-tab-pane label="企业微信绑定记录" name="bindRecord">
            <bind-list
              v-if="activeName === 'bindRecord'"
              :isDetails="isDetails"
              :id="distributorExtInfoVO.id"
            />
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>
<script>
import PublicInfo from '@/views/customer-management/components/public-info';
import CustomerInfoForm from '@/views/customer-management/components/customer-info-form/index';
import PurchasingSituation from '@/views/customer-management/components/purchasing-situation/index';
import CustomerServiceCList from '@/views/customer-management/components/CustomerServiceCList';
import DrawRecordList from '@/views/customer-management/components/DrawRecordList';
import BindList from './components/bind-list';
import {
  getMultiDetail,
  getLatestPurchaseStatistic
} from '@/api/customer-management/info';
export default {
  name: 'distributor-info',
  components: {
    PublicInfo,
    CustomerInfoForm,
    PurchasingSituation,
    CustomerServiceCList,
    DrawRecordList,
    BindList
  },
  data() {
    return {
      activeName: 'basic',
      id: this.$route.params.id,
      distributorBodyVO: null, // 分销商合作信息
      distributorExtInfoVO: null, // 客户资料+客户潜力
      distributorHeaderVO: null, // 分销商头部信息
      violateRecordVOList: [], // 违规记录
      whitelistBriefVOList: [], // 店铺白名单信息
      distributorPurchaseDataVO: null // 分销商信息-采购情况
    };
  },
  props: {
    authorityCode: {
      type: String,
      default: '/customer-management/client-list/:edit'
    }, // 按钮权限-编码
    isDetails: {
      type: Boolean,
      default: false
    } // 是否是查看
  },
  activated() {
    if (this.$route.query && this.$route.query.activeName) {
      this.activeName = this.$route.query.activeName;
    }
    this.getMultiDetail();
    this.getLatestPurchaseStatistic();
  },
  methods: {
    // 获取客户资料详情：分销商信息,合作信息，客户资料,基础信息
    getMultiDetail() {
      this.listLoading = true;
      getMultiDetail(this.id)
        .then((response) => {
          const {
            distributorBodyVO,
            distributorHeaderVO,
            distributorExtInfoVO,
            violateRecordVOList,
            whitelistBriefVOList
          } = response.data;
          this.distributorBodyVO = distributorBodyVO;
          this.distributorExtInfoVO = distributorExtInfoVO;
          this.distributorHeaderVO = distributorHeaderVO;
          this.violateRecordVOList = violateRecordVOList;
          this.whitelistBriefVOList = whitelistBriefVOList;
        })
        .finally(() => {
          this.listLoading = false;
        });

      if (this.$refs.CSCL) {
        // 刷新专属顾问变更记录
        this.$refs.CSCL.query();
      }
    },
    // 分销商信息-采购情况
    getLatestPurchaseStatistic() {
      this.listLoading = true;
      getLatestPurchaseStatistic(this.id)
        .then((response) => {
          this.distributorPurchaseDataVO = response.data;
        })
        .finally(() => {
          this.listLoading = false;
        });
    },
    onSearch() {},
    handleCurrentChange() {},
    handleSizeChange() {}
  }
};
</script>

<style lang="scss" scoped>
@import '../../../../styles/filter-list.scss';
.data-overview {
  .title {
    display: flex;
    align-items: center;
    .update-time {
      margin-left: 15px;
      color: #999;
    }
  }
  .distributor-data {
    border: 1px solid #ccc;
  }
}
.search-session {
  padding: 20px 0;
}
</style>
