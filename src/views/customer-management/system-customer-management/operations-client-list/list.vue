<template>
  <!-- 客户列表代发 -->
  <div class="app-container">
    <div class="table-container">
      <el-tabs v-model="activeName" type="card">
        <el-tab-pane label="全部" name="0" v-if="purchaseShow && shippingShow">
          <ListComponents key="listItemW" ref="listItemW" :type="type" />
        </el-tab-pane>
        <el-tab-pane label="采销分销商数据" name="1" v-if="purchaseShow">
          <ListComponents key="listItemO" ref="listItemO" :type="type" />
        </el-tab-pane>
        <el-tab-pane label="一件代发分销商数据" name="2" v-if="shippingShow"> 
          <ListComponents key="listItemT" ref="listItemT" :type="type" />
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>
<script>
import ListComponents from './components/ListComponents';
import { mapGetters } from 'vuex';
export default {
  name: 'client-list',
  components: {
    ListComponents
  },
  data() {
    return {
      activeName: '0',
      purchaseShow: false,
      shippingShow: false,
      type: ''
    };
  },
  watch: {
    activeName(val) {
      if (val === '0') {
        this.type = '';
        this.$nextTick(() => {
          this.$refs.listItemW.init();
        });
      } else if (val === '1') {
        this.type = 'PURCHASE';
        this.$nextTick(() => {
          this.$refs.listItemO.init();
        });
      } else if (val === '2') {
        this.type = 'DROP_SHIPPING';
        this.$nextTick(() => {
          this.$refs.listItemT.init();
        });
      }
    }
  },
  computed: {
    ...mapGetters('shop', ['btnSet'])
  },
  activated() {
    this.getInit();
  },
  methods: {
    // 权限
    getInit() {
      this.purchaseShow = this.btnSet.has(
        '/customer-management/client-list/:purchase'
      );
      this.shippingShow = this.btnSet.has(
        '/customer-management/client-list/:drop'
      );
      this.activeName = (this.purchaseShow && this.shippingShow) ? '0' : this.purchaseShow ? '1' : this.shippingShow ? '2' : '';
      this.type = (this.purchaseShow && this.shippingShow) ? '' : this.purchaseShow ? 'PURCHASE' : this.shippingShow ? 'DROP_SHIPPING' : '';

      this.$nextTick(() => {
        if (this.purchaseShow && this.shippingShow) {
          this.$refs.listItemW.init();
        } else {
          if (this.purchaseShow) {
            this.$refs.listItemO.init();
          } else if (this.shippingShow) {
            this.$refs.listItemT.init();
          }
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
</style>
