<template>
  <div class="commo-page-container">
    <div class="table-container">
      <form @submit.prevent="onSearch" class="commo-search-container commo-search-flex commo-search-margin-bottom10">
        <div class="commo-search-item">
          <span class="commo-search-item-label">平台类型:</span>
          <div class="commo-search-item-content">
            <el-select clearable v-model="filter.channel">
              <el-option :key="idx" :label="item.label" :loading="statusOptionsLoading" :value="item.value" v-for="(item, idx) in statusOptions"></el-option>
            </el-select>
          </div>
        </div>
        <div class="commo-search-item">
          <span class="commo-search-item-label">店铺名称:</span>
          <div class="commo-search-item-content">
            <el-input clearable v-model="filter.platformShopName"></el-input>
          </div>
        </div>
        <div class="commo-search-item">
          <span class="commo-search-item-label">商品名称:</span>
          <div class="commo-search-item-content">
            <el-input clearable v-model="filter.commodityName"></el-input>
          </div>
        </div>
        <div class="commo-search-btns">
          <el-button native-type="submit" size="small" type="primary">查询</el-button>
          <el-button @click="onReset" size="small">重置</el-button>
        </div>
      </form>
      <el-table :data="list" element-loading-text="加载中" fit highlight-current-row ref="multipleTable" v-loading.body="listLoading">
        <el-table-column align="center" label="平台名称" prop="channelName"></el-table-column>
        <el-table-column align="center" label="店铺名称" prop="platformShopName"></el-table-column>
        <el-table-column align="center" label="违规时间">
          <template :default-time="defaultDate" slot-scope="scope">
            {{ scope.row.violateDate | parseTime('{y}-{m}-{d} {h}:{i}:{s}') }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="违规商品" prop="commodityName"></el-table-column>
        <el-table-column align="center" label="控价价格（元/件）" prop="guidePrice"></el-table-column>
        <el-table-column align="center" label="破价价格（元/件）" prop="salePrice"></el-table-column>
        <el-table-column prop="isFine" label="是否交罚款">
          <template slot-scope="scope">
            {{ scope.row.isFine === '1' ? '是' : '否' }}
          </template>
        </el-table-column>
        <el-table-column prop="isFreeze" label="是否冻结账号">
          <template slot-scope="scope">
            {{ scope.row.isFreeze === '1' ? '是' : '否' }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="违规次数" prop="violateNumber"></el-table-column>
        <el-table-column align="center" label="操作" fixed="right">
          <template slot-scope="scope">
            <el-button @click="violateRecordDelete(scope.row.id)" size="small" type="text">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination :current-page="pageNo" :disabled="listLoading" :page-size="pageSize" :page-sizes="[10, 20, 30, 40, 50, 100]" :total="total" @current-change="handleCurrentChange" @size-change="handleSizeChange" background layout="total, sizes, prev, pager, next, jumper"></el-pagination>
      </div>
    </div>
  </div>
</template>
<script>
import { fechDictData } from '@/api/dict';
import { violateRecordList, violateRecordDelete } from '@/api/customer-management/info';
import pickBy from 'lodash/pickBy'; // 返回一个新对象，值由真值组成
import cloneDeep from 'lodash/cloneDeep'; // 对象深拷贝
export default {
  components: {},
  data() {
    const initFilter = {
      // 搜索条件初始值
      platformShopName: '',
      channel: '',
      commodityName: ''
    };
    return {
      initFilter, // 搜索条件初始值加入到Data
      filter: cloneDeep(initFilter), // 搜索条件的值
      list: [], // 列表
      applyDate: null, // 申请时间，由申请开始时间和申请结束时间组成
      listLoading: true,
      id: this.$route.params.id,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      defaultDate: ['00:00:00', '23:59:59'], // 默认时间段
      statusOptions: [], // 订单状态
      statusOptionsLoading: false,
      exportLoading: false
    };
  },
  computed: {
    // 表单接口传入的参数
    interfaceData() {
      const listQuery = pickBy(this.filter, (val) => !!val);
      listQuery.distributorId = this.id;
      return listQuery;
    }
  },
  activated() {
    // 页面加载事 自动运行
    this.fetchData();
    this.fetchStatus();
  },
  methods: {
    // 分页控制pageSize
    handleSizeChange(val) {
      this.pageSize = val;
      this.pageNo = 1;
      this.fetchData();
    },
    // 分页控制pageNo
    handleCurrentChange(val) {
      this.pageNo = val;
      this.fetchData();
    },
    // 删除
    violateRecordDelete(id) {
      this.$confirm('确认删除该条违规记录?', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          violateRecordDelete(id)
            .then((response) => {
              this.fetchData();
            })
            .finally(() => {
              this.listLoading = false;
            });
          this.$message({
            type: 'success',
            message: '删除成功！'
          });
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });
        });
    },
    // 获取列表信息
    fetchData() {
      this.listLoading = true;
      const listQuery = {
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        data: this.interfaceData
      };
      violateRecordList(listQuery)
        .then((response) => {
          const { list = [], total = 0 } = response.data || {};
          this.list = list;
          this.total = total;
        })
        .finally(() => {
          this.listLoading = false;
        });
    },
    // 搜索
    onSearch() {
      this.pageNo = 1;
      this.pageSize = 10;
      this.fetchData();
    },
    // 重置
    onReset() {
      this.pageNo = 1;
      this.pageSize = 10;
      this.filter = cloneDeep(this.initFilter);
      this.fetchData();
    },
    fetchStatus() {
      // 订单状态列表
      this.statusOptionsLoading = true;
      fechDictData('soyoungzg_channel')
        .then((rs) => {
          const res = rs.data.map((item) => ({
            value: item.value,
            label: item.label
          }));
          this.statusOptions = res;
        })
        .finally(() => {
          this.statusOptionsLoading = false;
        });
    }
  }
};
</script>
<style  lang="scss" scoped>
</style>
