<template>
  <div>
    <el-table
      :data="list"
      element-loading-text="加载中"
      fit
      highlight-current-row
      ref="multipleTable"
      v-loading.body="listLoading"
    >
      <el-table-column align="center" label="绑定/解绑">
       <template slot-scope="scope">
          <span v-if="scope.row.type === 'BIND'">绑定</span>
          <span v-if="scope.row.type === 'UN_BIND'">解绑</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作时间">
        <template :default-time="defaultDate" slot-scope="scope">
          {{ scope.row.createDate | parseTime('{y}-{m}-{d} {h}:{i}:{s}') }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        label="操作人"
        prop="customerServiceName"
      ></el-table-column>
    </el-table>
    <div class="pagination">
      <el-pagination
        :current-page="pageNo"
        :disabled="listLoading"
        :page-size="pageSize"
        :page-sizes="[10, 20, 30, 40,50,100]"
        :total="total"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
        background
        layout="total, sizes, prev, pager, next, jumper"
      ></el-pagination>
    </div>
  </div>
</template>

<script>
// 企业微信绑定记录列表
import { listWeachatPage } from '@/api/customer-management/client-list';
export default {
  data() {
    return {
      list: [],
      listLoading: false,
      pageNo: 1,
      pageSize: 10,
      total: 0
    };
  },
  props: {
    id: String
  },
  created() {
    this.fetchData();
  },
  methods: {
    // 分页控制pageSize
    handleSizeChange(val) {
      this.pageSize = val;
      this.pageNo = 1;
      this.fetchData();
    },
    // 分页控制pageNo
    handleCurrentChange(val) {
      this.pageNo = val;
      this.fetchData();
    },
    // 获取列表信息
    fetchData() {
      this.listLoading = true;
      const listQuery = {
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        data: {
          id: this.id
        }
      };
      listWeachatPage(listQuery)
        .then((response) => {
          const { list = [], total = 0 } = response.data || {};
          this.list = list;
          this.total = total;
        })
        .finally(() => {
          this.listLoading = false;
        });
    }
  }
};
</script>

<style>
</style>
