<template>
  <!-- 客户列表代发 -->
  <div>
    <!-- 数据总览 -->
    <div class="top">
      <div class="top-title">
        <h3>
          {{ type === '' ? '全部' : type === 'PURCHASE' ? '采销' : type === 'DROP_SHIPPING' ? '一件代发' : '' }}
        </h3>
        <div class="top-time">更新时间:{{ newDate | parseTime('{y}-{m}-{d} {h}:{i}') }}</div>
      </div>
      <div class="top-data">
        <GeneralSituation :list="list" v-loading="listLoading"></GeneralSituation>
      </div>
      <div class="top-text">
        <p>运维客户池客户的来源：1、注册审核通过选择专属顾问直接被划分到运维客户池；2、从临时客户池维护进入运维客户池；</p>
        <p>运维客户池的客户怎样才会被划入公海池：1、当客户注册审核通过后3天内无跟进记录且无动销；2、近30天内无动销且最近15天无跟进记录；</p>
        <p>(判断的时间为最新成为分销商的专属顾问的时候开始计算)</p>
      </div>
    </div>
    <!-- 搜索区域 -->
    <div class="search">
      <FormSearch :searchType="'services'" :accountType="type" :exportShow="exportShow" @parameterDataSearch="parameterDataSearch" @reset="reset" @parameterDataExport="parameterDataExport" />
    </div>
    <!-- 表格数据列表 -->
    <el-table :data="tableData" ref="tableData" style="width: 100%" :sort-orders="sortArray" @sort-change="sortChange">
      <el-table-column align="center" label="分销商ID" prop="id"></el-table-column>
      <el-table-column label="店铺信息" align="left" width="200">
        <template slot-scope="scope">
          <div>店铺：{{ scope.row.shopName || '/' }}</div>
          <div>账号：{{ scope.row.mobile || '/' }}</div>
          <div>店铺ID：{{ scope.row.platformShopId || '/' }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="levelName" label="店铺等级" align="center">
        <template slot-scope="scope">
          {{ scope.row.levelName || '/' }}
        </template></el-table-column
      >
      <el-table-column label="本月采购金额（元）" align="center" sortable="custom" width="190px" prop="totalMonthPurchaseAmount">
        <template slot="header" slot-scope="scope">
          本月采购金额（元）
          <el-tooltip class="item" effect="dark" placement="top-start">
            <div slot="content">本月采货金额统计的时间为截止当天零点的数据，非实时数据</div>
            <i class="el-icon-question"></i>
          </el-tooltip>
        </template>
        <template slot-scope="scope">
          <el-button type="text" @click="toLink('purchasingDataMonth', scope.row)" v-if="scope.row.totalMonthPurchaseAmount > 0">{{ scope.row.totalMonthPurchaseAmount }}</el-button>
          <span v-else>{{ scope.row.totalMonthPurchaseAmount }}</span>
        </template>
      </el-table-column>
      <el-table-column label="采购总金额（元）" align="center">
        <template slot-scope="scope">
          <el-button type="text" @click="toLink('purchasingData', scope.row)" v-if="scope.row.totalPurchaseAmount > 0">{{ scope.row.totalPurchaseAmount }}</el-button>
          <span v-else>{{ scope.row.totalPurchaseAmount }}</span>
        </template>
      </el-table-column>
      <el-table-column label="返点金额（元）" align="center" width="130">
        <template slot-scope="scope">
          <div>
            返点总额:
            <el-button v-if="scope.row.totalVirtualCredit > 0" type="text" @click="toLink('rebatesDataList', scope.row)">{{ scope.row.totalVirtualCredit }}</el-button>
            <span v-else>{{ scope.row.totalVirtualCredit }}</span>
          </div>
          <div>可用返点:{{ scope.row.totalUsableVirtualCredit }}</div>
          <ViewRebatesProgress :id="scope.row.id" :shopName="scope.row.shopName"></ViewRebatesProgress>
        </template>
      </el-table-column>
      <el-table-column prop="averageOrderAmount" label="客单价（元）" align="center"></el-table-column>
      <el-table-column label="采购数据" align="center">
        <template slot-scope="scope">
          <div>
            <span>采购品牌数:</span>
            <el-button v-if="scope.row.purchaseBrandCount > 0" type="text" @click="toLink('purchasingBrandList', scope.row)">{{ scope.row.purchaseBrandCount }}</el-button>
            <span v-else>{{ scope.row.purchaseBrandCount }}</span>
          </div>
          <div>
            <span>采购商品数:</span>
            <el-button v-if="scope.row.purchaseCommodityCount > 0" type="text" @click="toLink('purchaseGoods', scope.row)">{{ scope.row.purchaseCommodityCount }}</el-button>
            <span v-else>{{ scope.row.purchaseCommodityCount }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="偏好品类前三" align="center" width="130">
        <template slot-scope="scope">
          <div v-if="scope.row.favoriteCategoryList.length > 0">
            {{ scope.row.favoriteCategoryList.join('、') }}
          </div>
          <span v-else>/</span>
          <div>
            <el-button type="text" @click="toLink('categorySalesList', scope.row)">品类销售排行</el-button>
          </div>
        </template>
      </el-table-column>
      <el-table-column align="center" width="100px">
        <template slot="header">
          合作信息
          <el-tooltip class="item" effect="dark" placement="top-start">
            <div slot="content">合作信息-授权书：显示的为授权书的总数量</div>
            <i class="el-icon-question"></i>
          </el-tooltip>
        </template>
        <template slot-scope="scope">
          <div>
            合同:
            {{ scope.row.distributorContractNum }}份
          </div>
          <div>
            授权书:
            {{ scope.row.distributorLicenseNum }}份
          </div>
          <AuthorizationProgress :rowData="scope.row" />
        </template>
      </el-table-column>
      <el-table-column prop="customerServiceName" label="专属顾问" align="center">
        <template slot-scope="scope">
          {{ scope.row.customerServiceName || '/' }}
        </template>
      </el-table-column>
      <el-table-column prop="approval_time" label="最新拥有专属顾问的时间" align="center">
        <template slot-scope="scope">
          <span v-if="scope.row.flowDate"> {{ scope.row.flowDate | parseTime }}</span>
          <span v-else>/</span>
        </template>
      </el-table-column>
      <el-table-column label="入驻时间" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.auditDate | parseTime }}</span>
        </template>
      </el-table-column>
      <el-table-column label="绑定企业微信时间" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.firstWechatBindingDate | parseTime }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="address" label="操作" align="center">
        <template slot-scope="scope">
          <Authority auth="/customer-management/follow-up/:edit">
            <!-- <FollowUpDialog
              btnTitle="快速跟进"
              btnType="text"
              :id="scope.row.id"
            /> -->
            <el-button
              type="text"
              @click="
                $refs.dialog_followUp.setVisible(true, {
                  ...scope.row,
                  CLUEFOLLOWUPTYPE: 'distributor'
                })
              "
              >快速跟进</el-button
            >
          </Authority>
          <router-link :to="`/distributor-management/distributor/detail/${scope.row.id}`">
            <el-button type="text">查看信息</el-button>
          </router-link>
          <div>
            <Authority auth="/customer-management/client-list/:edit">
              <el-button type="text" @click="onLevelShow(scope.row.id)">修改等级</el-button>
            </Authority>
          </div>
          <div>
            <router-link :to="`/customer-management/system-customer-management/operations-client-list/recode/${scope.row.id}`">
              <el-button type="text">跟进记录</el-button>
            </router-link>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <div class="pagination">
      <el-pagination :current-page="parameter.pageNo" :page-size="parameter.pageSize" :page-sizes="[10, 20, 30, 40, 50, 100]" :total="total" @current-change="handleCurrentChange" @size-change="handleSizeChange" background layout="total, sizes, prev, pager, next, jumper"></el-pagination>
    </div>
    <el-dialog title="修改店铺等级" :visible.sync="dialogFormVisible">
      <el-form :model="form" label-width="130px" :rules="rules" ref="ruleForm" class="level">
        <el-form-item label="上月店铺回款金额:">
          <span>￥{{ levelObj.quarterTotalAmount }}</span>
        </el-form-item>
        <el-form-item label="当前店铺等级为:">
          <span>{{ levelObj.levelName }}</span>
        </el-form-item>
        <el-form-item label="修改店铺等级:" prop="levelId">
          <el-select v-model="form.levelId" placeholder="请选择">
            <el-option v-for="item in levelList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注:" prop="remarks">
          <el-input v-model="form.remarks"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="growth-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="onLevelChange" :loading="levelLoading">确 定</el-button>
      </div>
    </el-dialog>
    <Dialog :callback="onSubmitFollowUp" class="dialog" type="CLUE_FOLLOWUP" ref="dialog_followUp"> </Dialog>
  </div>
</template>
<script>
import { getTodayStatistic, listMultiPage, exportMulti, getBrief, updateLevel } from '@/api/customer-management/client-list';
import { listAllLevel } from '@/api/customer-management/setting';
import GeneralSituation from '@/components/views/customer-management/GeneralSituation';
import ViewRebatesProgress from '@/components/views/customer-management/ViewRebatesProgress';
import FormSearch from '@/components/views/customer-management/FormSearch';
import download from '@/utils/download';
import { parseTime } from '@/utils';
import { mapGetters } from 'vuex';
import AuthorizationProgress from '@/components/AuthorizationProgress';
// import FollowUpDialog from '@/views/customer-management/components/follow-up-dialog';
import Dialog from '@/components/Dialog/index.vue';
import { createFinishedTodo } from '@/api/customer-management/info';

export default {
  name: 'client-list',
  components: {
    GeneralSituation,
    ViewRebatesProgress,
    FormSearch,
    AuthorizationProgress,
    // FollowUpDialog,
    Dialog
  },
  props: {
    type: {
      type: String,
      default() {
        return '';
      }
    }
  },
  data() {
    return {
      sortArray: ['ascending', 'descending'],
      exportShow: false,
      newDate: new Date(),
      parameter: { data: {}, pageNo: 1, pageSize: 10 },
      list: [],
      listLoading: false,
      tableData: [],
      total: 0,
      dialogFormVisible: false,
      levelList: [],
      form: {
        id: '',
        levelId: '',
        remarks: ''
      },
      rules: {
        levelId: [{ required: true, message: '请选择店铺等级', trigger: 'change' }],
        remarks: [{ required: true, message: '请填写备注', trigger: 'blur' }]
      },
      levelLoading: false,
      levelObj: {
        quarterTotalAmount: 0,
        levelName: '暂无'
      }
    };
  },
  computed: {
    ...mapGetters('shop', ['btnSet'])
  },
  created() {
    this.exportShow = this.btnSet.has('/customer-management/client-list/:export');
    this.getQuery();
    this.fetchListAllLevel();
  },
  activated() {
    this.getQuery();
  },
  methods: {
    getQuery() {
      const { csId, toPublicDay } = this.$route.query;
      if (csId && toPublicDay) {
        this.parameter.data.csIds = [csId];
        this.parameter.data.toPublicDay = toPublicDay;
      }
    },
    init() {
      this.getStatistic();
      this.getList();
    },
    // 店铺等级
    fetchListAllLevel() {
      listAllLevel({ purchaseType: this.type }).then((res) => {
        this.levelList = res.data.map((item) => ({
          value: item.id,
          label: item.name
        }));
      });
    },
    getStatistic() {
      this.listLoading = true;
      getTodayStatistic(this.type)
        .then((res) => {
          this.list = [
            {
              title: '分销商总数',
              amount: res.data.totalNum
            },
            {
              title: '今日运维客户新增数',
              amount: res.data.todayNewNum,
              yesterdayAmount: res.data.yesterdayNewNum,
              isArrowShow: true
            },
            {
              title: '今日活跃分销商数',
              amount: res.data.todayAccessNum,
              yesterdayAmount: res.data.yesterdayAccessNum,
              isArrowShow: true,
              tips: '今日活跃分销商数：是指今日有登录并访问过系统（商家PC和小程序）的分销商数量'
            },
            {
              title: '分销商平均客单价',
              amount: res.data.averageOrderAmount,
              tips: '分销商平均客单价：分销商的付款总金额/分销商的已付款订单总数（是包含退款的订单）'
            },

            {
              title: '今日动销分销商数',
              amount: res.data.todayBuyNum,
              yesterdayAmount: res.data.yesterdayBuyNum,
              isArrowShow: true,
              tips: '是指今日下单分销商购买了商品除了小样以外的分销商总数量'
            }
          ];
        })
        .finally(() => {
          this.listLoading = false;
        });
    },
    handleSizeChange(val) {
      this.parameter.pageSize = val;
      this.parameter.pageNo = 1;
      this.getList();
    },
    handleCurrentChange(val) {
      this.parameter.pageNo = val;
      this.getList();
    },
    getList() {
      const parameter = this.parameter;
      parameter.data.purchaseType = this.type;
      listMultiPage(parameter).then((res) => {
        this.tableData = res.data.list;
        this.total = res.data.total;
      });
    },
    // 查询
    parameterDataSearch(data) {
      this.parameter.data = data;
      this.getList();
    },
    // 重置
    reset() {
      this.parameter.data = {};
      this.$refs.tableData.clearSort();
      this.getList();
    },
    // 导出
    parameterDataExport(data) {
      const parameter = data;
      parameter.purchaseType = this.type;
      exportMulti(parameter).then((res) => {
        download(res, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', `客户列表-${this.type === '' ? '全部' : this.type === 'PURCHASE' ? '采销' : this.type === 'DROP_SHIPPING' ? '一件代发' : ''}-${parseTime(Date.now(), '{y}-{m}-{d}-{h}:{i}:{s}')}.xlsx`);
      });
    },
    // 获取等级
    onLevelShow(id) {
      getBrief(id).then((res) => {
        this.form.id = id;
        this.dialogFormVisible = true;
        this.$nextTick(() => {
          this.$refs['ruleForm'].resetFields();
        });
        this.levelObj.quarterTotalAmount = res.data ? res.data.quarterTotalAmount : 0;
        this.levelObj.levelName = res.data ? res.data.levelName : '暂无';
      });
    },
    // 修改等级
    onLevelChange() {
      this.$refs['ruleForm'].validate((valid) => {
        if (valid) {
          this.levelLoading = true;
          updateLevel(this.form)
            .then((res) => {
              if (res.code === '0') {
                this.getList();
                this.$message({
                  message: '修改成功',
                  type: 'success'
                });
                this.dialogFormVisible = false;
              }
            })
            .finally(() => {
              this.levelLoading = false;
            });
        } else {
          return false;
        }
      });
    },
    // 品类销售
    toLink(type, row) {
      const { id, shopName, mobile, purchaseBrandCount } = row;
      const categorySalesListData = {
        id,
        shopName,
        mobile,
        purchaseBrandCount
      };

      const list = [
        {
          key: 'purchasingData',
          label: '采购数据',
          value: '/customer-management/system-customer-management/purchasing-data'
        },
        {
          key: 'purchasingDataMonth',
          label: '采购数据本月',
          value: '/customer-management/system-customer-management/purchasing-data'
        },
        {
          key: 'rebatesDataList',
          label: '返点分布',
          value: '/customer-management/system-customer-management/rebates-data/list'
        },
        {
          key: 'purchasingBrandList',
          label: '采购品牌',
          value: '/customer-management/system-customer-management/purchasing-brand/list'
        },
        {
          key: 'purchaseGoods',
          label: '采购商品',
          value: '/customer-management/system-customer-management/purchase-goods'
        },
        {
          key: 'categorySalesList',
          label: '品类销售',
          value: '/customer-management/system-customer-management/category-sales/list'
        },
        {
          key: 'freightDetail',
          label: '运费金额',
          value: '/customer-management/system-customer-management/freight-detail'
        }
      ];
      for (const item of list) {
        if (item.key === type) {
          if (item.key === 'purchasingDataMonth') {
            categorySalesListData.statisticalType = 'month';
          }
          sessionStorage.setItem(item.value, JSON.stringify(categorySalesListData));
          this.$router.push({
            path: item.value
          });
          break;
        }
      }
    },
    sortChange(data) {
      const { prop, order } = data;
      const orderKey = {
        ascending: 'ASC',
        descending: 'DESC'
      }[order];
      if (prop === 'totalMonthPurchaseAmount') {
        this.parameter.data.orderByClause = orderKey;
      }
      if (!prop) {
        delete this.parameter.data.orderByClause;
      }
      this.getList();
    },
    onSubmitFollowUp(followUpData, dialog) {
      const data = {
        ...followUpData,
        distributorId: dialog.externalData.id
      };
      createFinishedTodo(data).then((res) => {
        console.dir(res);
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.top {
  &-title {
    display: flex;
    align-items: center;
  }
  &-time {
    margin-left: 15px;
    color: #999;
  }
  &-data {
    border: 1px solid #ccc;
  }
  &-text {
    margin: 20px 0;
    padding: 10px 20px;
    background-color: rgba(64, 158, 255, 0.19);
    border: 1px solid var(--color-primary);
    color: #000;
    font-size: 14px;
    p {
      margin: 4px 0;
    }
  }
}
.search {
  margin-top: 20px;
}
.level {
  .el-input,
  .el-select {
    width: 240px;
  }
}
</style>
