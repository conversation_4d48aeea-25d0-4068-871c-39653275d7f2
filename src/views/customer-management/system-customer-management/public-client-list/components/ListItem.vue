<template>
  <!-- 客户列表代发 -->
  <div>
    <!-- 数据总览 -->
    <div class="top">
      <div class="top-data">
        <GeneralSituation :list="list" v-loading="listLoading"></GeneralSituation>
      </div>
    </div>
    <!-- 搜索区域 -->
    <div class="search">
      <FormSearch :searchType="'waters'" :accountType="type" @parameterDataSearch="parameterDataSearch" @reset="reset" @parameterDataExport="parameterDataExport" :exportShow="exportShow">
        <el-button v-if="editShow" slot="receive" type="primary" size="small" @click="onReceiveAll">批量领取</el-button>
      </FormSearch>
    </div>
    <!-- 表格数据列表 -->
    <el-table :data="tableData" style="width: 100%" ref="tableData" @selection-change="handleSelectionChange" @sort-change="sortChange">
      <el-table-column type="selection" width="55" fixed="left"> </el-table-column>
      <el-table-column align="center" label="分销商ID" prop="id"></el-table-column>
      <el-table-column label="店铺信息" align="left" width="200" fixed="left">
        <template slot-scope="scope">
          <div>店铺：{{ scope.row.shopName || '/' }}</div>
          <div>账号：{{ scope.row.mobile || '/' }}</div>
          <div>店铺ID：{{ scope.row.platformShopId || '/' }}</div>
        </template>
      </el-table-column>
      <el-table-column label="店铺等级" align="center">
        <template slot-scope="scope">
          {{ scope.row.levelName || '/' }}
        </template></el-table-column
      >
      <el-table-column label="本月采购金额（元）" align="center" sortable="custom" width="190px" prop="totalMonthPurchaseAmount">
        <template slot="header" slot-scope="scope">
          本月采购金额（元）
          <el-tooltip class="item" effect="dark" placement="top-start">
            <div slot="content">本月采货金额统计的时间为截止当天零点的数据，非实时数据</div>
            <i class="el-icon-question"></i>
          </el-tooltip>
        </template>
        <template slot-scope="scope">
          <el-button type="text" @click="toLink('purchasingDataMonth', scope.row)" v-if="scope.row.totalMonthPurchaseAmount > 0">{{ scope.row.totalMonthPurchaseAmount }}</el-button>
          <span v-else>{{ scope.row.totalMonthPurchaseAmount }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center">
        <template slot="header" slot-scope="scope">
          采购总金额(元)
          <el-tooltip class="item" effect="dark" placement="top-start">
            <div slot="content">采购总金额是不含运费，并且扣除了退款的金额</div>
            <i class="el-icon-question"></i>
          </el-tooltip>
        </template>
        <template slot-scope="scope">
          <el-button type="text" @click="toLink('purchasingData', scope.row)" v-if="scope.row.totalPurchaseAmount > 0">{{ scope.row.totalPurchaseAmount }}</el-button>
          <span v-else>{{ scope.row.totalPurchaseAmount }}</span>
        </template>
      </el-table-column>
      <el-table-column label="返点金额（元）" align="center" width="130">
        <template slot-scope="scope">
          <div>返点总额:{{ scope.row.totalVirtualCredit }}</div>
          <div>可用返点:{{ scope.row.totalUsableVirtualCredit }}</div>
          <ViewRebatesProgress :id="scope.row.id" :shopName="scope.row.shopName"></ViewRebatesProgress>
        </template>
      </el-table-column>
      <el-table-column prop="averageOrderAmount" label="客单价（元）" align="center"></el-table-column>
      <el-table-column label="采购数据" align="center">
        <template slot-scope="scope">
          <div>
            <span>采购品牌数:</span>
            <el-button v-if="scope.row.purchaseBrandCount > 0" type="text" @click="toLink('purchasingBrandList', scope.row)">{{ scope.row.purchaseBrandCount }}</el-button>
            <span v-else>{{ scope.row.purchaseBrandCount }}</span>
          </div>
          <div>
            <span>采购商品数:</span>
            <el-button v-if="scope.row.purchaseCommodityCount > 0" type="text" @click="toLink('purchaseGoods', scope.row)">{{ scope.row.purchaseCommodityCount }}</el-button>
            <span v-else>{{ scope.row.purchaseCommodityCount }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="偏好品类前三" align="center" width="130">
        <template slot-scope="scope">
          <div v-if="scope.row.favoriteCategoryList.length > 0">
            {{ scope.row.favoriteCategoryList.join('、') }}
          </div>
          <span v-else>/</span>
        </template>
      </el-table-column>
      <el-table-column label="合作信息" align="center" width="100px">
        <template slot-scope="scope">
          <div>
            合同:
            {{ scope.row.distributorContractNum }}份
          </div>
          <div>
            授权书:
            {{ scope.row.distributorLicenseNum }}份
          </div>
          <AuthorizationProgress :rowData="scope.row" />
        </template>
      </el-table-column>
      <el-table-column align="center">
        <template slot="header" slot-scope="scope">
          上次专属顾问
          <el-tooltip class="item" effect="dark" placement="top-start">
            <div slot="content">上次的专属顾问是指客户在最近一次被划入公海池前的专属顾问</div>
            <i class="el-icon-question"></i>
          </el-tooltip>
        </template>
        <template slot-scope="scope">
          {{ scope.row.lastCsName || '/' }}
        </template>
      </el-table-column>
      <el-table-column label="划入时间" align="center">
        <template slot-scope="scope">
          {{ scope.row.flowDate | parseTime }}
        </template>
      </el-table-column>
      <el-table-column label="入驻时间" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.auditDate | parseTime }}</span>
        </template>
      </el-table-column>
      <el-table-column label="绑定企业微信时间" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.firstWechatBindingDate | parseTime }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="address" label="操作" align="center" fixed="right">
        <template slot-scope="scope">
          <Authority auth="/customer-management/public-client-list/:edit">
            <el-button type="text" @click="onReceive(scope.row.id)">领取客户</el-button>
          </Authority>
          <router-link :to="`/distributor-management/distributor/detail/${scope.row.id}`">
            <el-button type="text">查看信息</el-button>
          </router-link>
          <el-button type="text" @click="toLink('categorySalesList', scope.row)">品类销售</el-button>
          <div>
            <!-- <router-link
              :to="`/customer-management/system-customer-management/public-client-list/recode/${scope.row.id}`"
            > -->
            <el-button type="text" @click="(id = scope.row.id), (followUpRecordDialogVisible = true)">跟进记录</el-button>
            <!-- </router-link> -->
          </div>
        </template>
      </el-table-column>
    </el-table>
    <div class="pagination">
      <el-pagination :current-page="parameter.pageNo" :page-size="parameter.pageSize" :page-sizes="[10, 20, 30, 40, 50, 100]" :total="total" @current-change="handleCurrentChange" @size-change="handleSizeChange" background layout="total, sizes, prev, pager, next, jumper"></el-pagination>
    </div>
    <el-dialog :title="receiveAll ? '批量领取提示' : '确定领取提示'" :visible.sync="dialogFormVisible" width="400px">
      <div>确定需要{{ receiveAll ? `批量去领这${multipleSelection.length}个` : '领取' }}客户吗？</div>
      <div>领取成功后请记得当天完成该客户的跟进哦！</div>
      <div slot="footer" class="growth-footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="onReceiveSave" :loading="receiveLoading">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 跟进记录 -->
    <el-dialog title="跟进记录" :visible.sync="followUpRecordDialogVisible" width="1000px" v-if="followUpRecordDialogVisible">
      <follow-up-record :distributorId="id" @hook:mounted="() => $refs.followUpRecord.query()" ref="followUpRecord"></follow-up-record>
    </el-dialog>
  </div>
</template>
<script>
import { getTodayStatistic, listPublicMultiPage, exportPublicMulti, drawInBatch } from '@/api/customer-management/public-client-list';
import GeneralSituation from '@/components/views/customer-management/GeneralSituation';
import FormSearch from '@/components/views/customer-management/FormSearch';
import ViewRebatesProgress from '@/components/views/customer-management/ViewRebatesProgress';
import download from '@/utils/download';
import { parseTime } from '@/utils';
import { mapGetters } from 'vuex';
import AuthorizationProgress from '@/components/AuthorizationProgress';
import followUpRecord from '@/components/views/distributorManagement/distributor/components/follow-up-record';

export default {
  components: {
    GeneralSituation,
    FormSearch,
    ViewRebatesProgress,
    AuthorizationProgress,
    followUpRecord
  },
  props: {
    type: {
      type: String,
      default() {
        return '';
      }
    }
  },
  data() {
    return {
      parameter: { data: {}, pageNo: 1, pageSize: 10 },
      list: [],
      multipleSelection: [],
      listLoading: false,
      tableData: [],
      total: 0,
      dialogFormVisible: false,
      ids: [],
      receiveLoading: false,
      receiveAll: false,
      editShow: false,
      exportShow: false,

      id: '',
      followUpRecordDialogVisible: false
    };
  },
  computed: {
    ...mapGetters('shop', ['btnSet'])
  },
  created() {
    this.editShow = this.btnSet.has('/customer-management/public-client-list/:edit');
    this.exportShow = this.btnSet.has('/customer-management/public-client-list/:export');
  },
  methods: {
    init() {
      this.getStatistic();
      this.getList();
    },
    getStatistic() {
      this.listLoading = true;
      getTodayStatistic(this.type)
        .then((res) => {
          this.list = [
            {
              title: '客户总数',
              amount: res.data.totalNum
            },
            {
              title: '今日新增回收客户数',
              amount: res.data.todayNewNum,
              yesterdayAmount: res.data.yesterdayNewNum,
              isArrowShow: true
            },
            {
              title: '今日已领取客户数',
              amount: res.data.todayDrawNum,
              yesterdayAmount: res.data.yesterdayDrawNum,
              isArrowShow: true
            },
            {
              title: '超过30天未被领取客户数',
              amount: res.data.limitThirtyDayNotDrawNum
            },
            {
              title: '已动销过客户数',
              amount: res.data.purchaseNum,
              tips: '已动销：是指下过单的分销商数量(小样订单不算动销)'
            },
            {
              title: '未动销过客户数',
              amount: res.data.notPurchaseNum,
              tips: '未动销：是指没有下过单的分销商数量(小样订单不算动销)'
            }
          ];
        })
        .finally(() => {
          this.listLoading = false;
        });
    },
    handleSizeChange(val) {
      this.parameter.pageSize = val;
      this.parameter.pageNo = 1;
      this.getList();
    },
    handleCurrentChange(val) {
      this.parameter.pageNo = val;
      this.getList();
    },
    getList() {
      const parameter = this.parameter;
      parameter.data.purchaseType = this.type;
      listPublicMultiPage(parameter).then((res) => {
        this.tableData = res.data.list;
        this.total = res.data.total;
      });
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    // 查询
    parameterDataSearch(data) {
      this.parameter.data = data;
      this.getList();
    },
    // 重置
    reset() {
      this.parameter.data = {};
      this.$refs.tableData.clearSort();
      this.getList();
    },
    // 导出
    parameterDataExport(data) {
      const parameter = data;
      parameter.purchaseType = this.type;
      exportPublicMulti(parameter).then((res) => {
        download(res, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', `公海池客户列表-${this.type === '' ? '全部' : this.type === 'PURCHASE' ? '采销' : this.type === 'DROP_SHIPPING' ? '一件代发' : ''}-${parseTime(Date.now(), '{y}-{m}-{d}-{h}:{i}:{s}')}.xlsx`);
      });
    },
    // 领取客户弹窗
    onReceive(id) {
      this.receiveAll = false;
      this.ids = [id];
      this.dialogFormVisible = true;
    },
    onReceiveAll() {
      if (this.multipleSelection.length < 1) {
        return this.$message({
          message: '请先选择客户',
          type: 'warning'
        });
      }
      this.receiveAll = true;
      this.ids = this.multipleSelection.map((item) => {
        return item.id;
      });
      this.dialogFormVisible = true;
    },
    // 修改等级
    onReceiveSave() {
      this.receiveLoading = true;
      drawInBatch(this.ids)
        .then((res) => {
          if (res.code === '0') {
            this.getList();
            this.$message({
              message: '领取成功',
              type: 'success'
            });
            this.dialogFormVisible = false;
          }
        })
        .finally(() => {
          this.receiveLoading = false;
        });
    },
    // 品类销售
    toLink(type, row) {
      const { id, shopName, mobile, purchaseBrandCount } = row;
      const categorySalesListData = {
        id,
        shopName,
        mobile,
        purchaseBrandCount
      };
      const list = [
        {
          key: 'purchasingData',
          label: '采购数据',
          value: '/customer-management/system-customer-management/purchasing-data'
        },
        {
          key: 'purchasingDataMonth',
          label: '采购数据本月',
          value: '/customer-management/system-customer-management/purchasing-data'
        },
        {
          key: 'rebatesDataList',
          label: '返点分布',
          value: '/customer-management/system-customer-management/rebates-data/list'
        },
        {
          key: 'purchasingBrandList',
          label: '采购品牌',
          value: '/customer-management/system-customer-management/purchasing-brand/list'
        },
        {
          key: 'purchaseGoods',
          label: '采购商品',
          value: '/customer-management/system-customer-management/purchase-goods'
        },
        {
          key: 'categorySalesList',
          label: '品类销售',
          value: '/customer-management/system-customer-management/category-sales/list'
        },
        {
          key: 'freightDetail',
          label: '运费金额',
          value: '/customer-management/system-customer-management/freight-detail'
        }
      ];
      for (const item of list) {
        if (item.key === type) {
          if (item.key === 'purchasingDataMonth') {
            categorySalesListData.statisticalType = 'month';
          }
          sessionStorage.setItem(item.value, JSON.stringify(categorySalesListData));
          this.$router.push({
            path: item.value
          });
          break;
        }
      }
    },
    sortChange(data) {
      const { prop, order } = data;
      const orderKey = {
        ascending: 'ASC',
        descending: 'DESC'
      }[order];
      if (prop === 'totalMonthPurchaseAmount') {
        this.parameter.data.orderByClause = orderKey;
      }
      if (!prop) {
        delete this.parameter.data.orderByClause;
      }
      this.getList();
    }
  }
};
</script>

<style lang="scss" scoped>
.top {
  &-title {
    display: flex;
    align-items: center;
  }
  &-time {
    margin-left: 15px;
    color: #999;
  }
  &-data {
    border: 1px solid #ccc;
  }
}
.search {
  margin-top: 20px;
}
.level {
  .el-input,
  .el-select {
    width: 240px;
  }
}
</style>
