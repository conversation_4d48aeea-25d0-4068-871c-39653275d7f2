<template>
  <div class="app-container">
    <div class="table-container">
      <div class="time">
        更新时间：{{ new Date() | parseTime('{y}-{m}-{d} {h}:{i}') }}
      </div>
      <el-tabs v-model="activeName" type="card">
        <el-tab-pane label="全部" name="0" v-if="purchaseShow && shippingShow">
          <ListItem ref="listItemW" :type="''" />
        </el-tab-pane>
        <el-tab-pane label="采销客户池" name="1" v-if="purchaseShow">
          <ListItem ref="listItemO" :type="'PURCHASE'" />
        </el-tab-pane>
        <el-tab-pane label="一件代发客户池" name="2" v-if="shippingShow">
          <ListItem ref="listItemT" :type="'DROP_SHIPPING'" />
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import ListItem from './components/ListItem';
import { mapGetters } from 'vuex';
export default {
  name: 'customer-management-system-customer-management-public-client-list',
  components: {
    ListItem
  },
  data() {
    return {
      activeName: '0',
      purchaseShow: false,
      shippingShow: false,
      type: ''
    };
  },
  watch: {
    activeName: {
      handler(val) {
        if (val === '0') {
          this.type = '';
          this.$nextTick(() => {
            this.$refs.listItemW.init();
          });
        } else if (val === '1') {
          this.type = 'PURCHASE';
          this.$nextTick(() => {
            this.$refs.listItemO.init();
          });
        } else if (val === '2') {
          this.type = 'DROP_SHIPPING';
          this.$nextTick(() => {
            this.$refs.listItemT.init();
          });
        }
      }
    }
  },
  computed: {
    ...mapGetters('shop', ['btnSet'])
  },
  activated() {
    this.getInit();
  },
  methods: {
    // 权限
    getInit() {
      this.purchaseShow = this.btnSet.has(
        '/customer-management/public-client-list/:purchase'
      );
      this.shippingShow = this.btnSet.has(
        '/customer-management/public-client-list/:drop'
      );
      this.activeName = (this.purchaseShow && this.shippingShow) ? '0' : this.purchaseShow ? '1' : this.shippingShow ? '2' : '';
      this.type = (this.purchaseShow && this.shippingShow) ? '' : this.purchaseShow ? 'PURCHASE' : this.shippingShow ? 'DROP_SHIPPING' : '';

      this.$nextTick(() => {
        if (this.purchaseShow && this.shippingShow) {
          this.$refs.listItemW.init();
        } else {
          if (this.purchaseShow) {
            this.$refs.listItemO.init();
          } else if (this.shippingShow) {
            this.$refs.listItemT.init();
          }
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.time {
  margin-bottom: 20px;
}
</style>
