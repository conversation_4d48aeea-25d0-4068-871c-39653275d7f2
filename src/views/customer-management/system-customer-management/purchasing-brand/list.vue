<template>
  <div class="app-container wrap">
    <div class="table-container">
      <div class="detail-tips">店铺名称：{{ queryData.shopName }}&nbsp;&nbsp;&nbsp;&nbsp;登录账号：{{ queryData.mobile }}&nbsp;&nbsp;&nbsp;&nbsp;分销商采购品牌总数：{{ queryData.purchaseBrandCount }}</div>
      <form @submit.prevent="onSearch" class="commo-search-container commo-search-flex commo-search-margin-bottom10">
        <div class="commo-search-item">
          <span class="commo-search-item-label">下单时间:</span>
          <div class="commo-search-item-content">
            <el-date-picker size="small" :default-time="['00:00:00', '23:59:59']" align="right" end-placeholder="结束日期" start-placeholder="开始日期" type="datetimerange" v-model="createDate"></el-date-picker>
          </div>
        </div>
        <div class="commo-search-item">
          <span class="commo-search-item-label">商品品牌:</span>
          <div class="commo-search-item-content">
            <el-select clearable size="small" style="width: 100%" v-model="form.data.brandId" filterable>
              <el-option v-for="item in brandList" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </div>
        </div>
        <div class="commo-search-btns">
          <el-button native-type="submit" size="small" type="primary">查询</el-button>
          <el-button @click="onReset" size="small">重置</el-button>
          <Authority auth="/customer-management/client-list/:export">
            <el-button :loading="exportLoading" @click="onExport" size="small" type="primary">导出</el-button>
          </Authority>
        </div>
      </form>
      <el-table :data="list" element-loading-text="加载中" fit highlight-current-row ref="multipleTable" v-loading.body="listLoading">
        <el-table-column align="center" label="排名" prop="ranking"></el-table-column>
        <el-table-column align="center" label="品牌名称">
          <template :default-time="defaultDate" slot-scope="scope">
            <p>
              {{ scope.row.brandName || '/' }}
            </p>
          </template>
        </el-table-column>
        <el-table-column align="center" label="采购总金额">
          <template slot-scope="scope">
            <p v-if="scope.row.totalAmount" class="link" @click="toLink('purchasingDataDetail', scope.row)">
              {{ getMoney(scope.row.totalAmount) }}
            </p>
            <p v-else>¥{{ scope.row.totalAmount }}</p>
          </template>
        </el-table-column>
        <el-table-column align="center" label="订单总数" prop="orderCount"></el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          :current-page="form.pageNo"
          :disabled="listLoading"
          :page-size="form.pageSize"
          :page-sizes="[10, 20, 30, 40, 50, 100]"
          :total="total"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
          background
          layout="total, sizes, prev, pager, next, jumper"
        ></el-pagination>
      </div>
    </div>
  </div>
</template>
<script>
import { brandStatistics, exportExcelBrandStatistics } from '@/api/customer-management/client-list';
import { listAll } from '@/api/setting/commodity/brand';
import cloneDeep from 'lodash/cloneDeep';
import pickBy from 'lodash/pickBy';
import download from '@/utils/download';
import { parseTime } from '@/utils';
import { getItem } from '@/utils/sessionStorage';
export default {
  name: 'purchasing-brand',
  data() {
    return {
      form: {
        data: {
          brandId: '' // 品牌id
        },
        pageNo: 1,
        pageSize: 10
      },
      distributorId: '', // 分销商id
      createDate: null, // 下单时间，由申请开始时间和申请结束时间组成
      list: [],
      listLoading: true,
      brandList: [], // 品牌列表选项
      total: 0,
      queryData: {},
      exportLoading: false // 导出按钮loading
    };
  },
  watch: {},
  computed: {
    // 过滤
    data() {
      const obj = { ...this.form.data };
      obj.distributorId = this.distributorId;
      if (this.createDate) {
        obj.startTime = this.createDate[0].getTime();
        obj.endTime = this.createDate[1].getTime();
      }
      return pickBy(obj, (val) => !!val);
    }
  },
  activated() {
    const params = getItem(this.$route.path) || {};
    this.distributorId = params.id;
    this.queryData = params;
    this.fetchData();
    this.fetchBrand(); // 获取所有品牌
  },
  methods: {
    // 金额格式换处理
    getMoney(data) {
      return data === 0 || data ? '¥' + data : '/';
    },
    toLink(type, row) {
      const { brandId, brandName } = row;
      const categorySalesListData = {
        distributorId: this.distributorId,
        brandId,
        shopName: this.queryData.shopName,
        mobile: this.queryData.mobile,
        brandName
      };
      const list = [
        {
          key: 'purchasingDataDetail',
          label: '采购数据',
          value: '/customer-management/system-customer-management/purchasing-brand/detail'
        }
      ];
      for (const item of list) {
        if (item.key === type) {
          sessionStorage.setItem(item.value, JSON.stringify(categorySalesListData));
          this.$router.push({
            path: item.value
          });
          break;
        }
      }
    },
    // 导出
    onExport() {
      this.exportLoading = true;
      exportExcelBrandStatistics(this.data)
        .then((res) => {
          download(res, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', `采购品牌列表-${parseTime(Date.now(), '{y}-{m}-{d}-{h}:{i}:{s}')}.xlsx`);
        })
        .finally(() => {
          this.exportLoading = false;
        });
    },
    // 获取列表数据
    fetchData() {
      this.listLoading = true;
      const data = cloneDeep(this.form);
      data.data = this.data;
      brandStatistics(data)
        .then((response) => {
          if (response.data) {
            this.list = response.data.list;
            this.total = response.data.total;
          }
        })
        .finally(() => {
          this.listLoading = false;
        });
    },
    // 获取所有品牌
    fetchBrand() {
      listAll({}).then((rs) => {
        const res = rs.data.map((item) => ({
          value: item.id,
          label: item.name
        }));
        this.brandList = res;
      });
    },
    handleSizeChange(val) {
      this.form.pageSize = val;
      this.form.pageNo = 1;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.form.pageNo = val;
      this.fetchData();
    },
    onSearch() {
      this.form.pageNo = 1;
      this.form.pageSize = 10;
      this.fetchData();
    },
    onReset() {
      ['brandId'].forEach((item) => {
        this.form.data[item] = '';
      });
      this.createDate = null;
      this.onSearch();
    }
  }
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
@import 'src/styles/goods-table.scss';
.create {
  margin-bottom: 8px;
}
.filter-item .link {
  margin-left: 10px;
}
.goods-img {
  width: 60px;
  height: 60px;
}
.ruleForm {
  .radio-label {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
  }
  .el-radio {
    margin-right: 20px;
  }
  .el-input {
    width: 200px;
  }
}
.detail-tips {
  width: 100%;
  height: 50px;
  line-height: 50px;
  background-color: rgba(64, 158, 255, 0.19);
  border: 1px solid var(--color-primary);
  font-size: 16px;
  padding: 0 20px;
  margin-bottom: 12px;
}
.link {
  color: var(--color-primary);
  cursor: pointer;
}
</style>
