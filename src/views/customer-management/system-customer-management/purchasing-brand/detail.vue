<template>
  <div class="app-container wrap">
    <div class="table-container">
      <div class="detail-tips">店铺名称：{{ queryData.shopName }}&nbsp;&nbsp;&nbsp;&nbsp;登录账号：{{ queryData.mobile }}&nbsp;&nbsp;&nbsp;&nbsp;采购品牌：{{ queryData.brandName === 'undefined' ? '-' : queryData.brandName }}</div>
      <form @submit.prevent="onSearch" class="commo-search-container commo-search-flex commo-search-margin-bottom10">
        <div class="commo-search-item">
          <span class="commo-search-item-label">商品名称:</span>
          <div class="commo-search-item-content">
            <el-input size="small" v-model="form.data.commodityName"></el-input>
          </div>
        </div>
        <div class="commo-search-item">
          <span class="commo-search-item-label">商品ID:</span>
          <div class="commo-search-item-content">
            <el-input size="small" v-model="form.data.commodityId"></el-input>
          </div>
        </div>
        <div class="commo-search-item">
          <span class="commo-search-item-label">商品条码:</span>
          <div class="commo-search-item-content">
            <el-input size="small" v-model="form.data.commodityCode"></el-input>
          </div>
        </div>
        <div class="commo-search-item">
          <span class="commo-search-item-label">商品标识:</span>
          <div class="commo-search-item-content">
            <el-input size="small" v-model="form.data.skuId"></el-input>
          </div>
        </div>
        <div class="commo-search-btns">
          <el-button native-type="submit" size="small" type="primary">查询</el-button>
          <el-button @click="onReset" size="small">重置</el-button>
          <Authority auth="/customer-management/client-list/:export">
            <!-- 权限找不到 -->
            <el-button :loading="exportLoading" @click="onExport" size="small">导出</el-button>
          </Authority>
        </div>
      </form>
      <el-table :data="list" element-loading-text="加载中" fit highlight-current-row ref="multipleTable" v-loading.body="listLoading">
        <el-table-column align="center" label="排名" prop="ranking"></el-table-column>
        <el-table-column align="center" label="商品名称" prop="commodityName"></el-table-column>
        <el-table-column align="center" label="商品ID" prop="commodityId"></el-table-column>
        <el-table-column align="center" label="采购总金额">
          <template slot-scope="scope">
            <p>¥{{ scope.row.totalAmount || 0 }}</p>
          </template>
        </el-table-column>
        <el-table-column align="center" label="订单总数" prop="orderCount"></el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          :current-page="form.pageNo"
          :disabled="listLoading"
          :page-size="form.pageSize"
          :page-sizes="[10, 20, 30, 40, 50, 100]"
          :total="total"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
          background
          layout="total, sizes, prev, pager, next, jumper"
        ></el-pagination>
      </div>
    </div>
  </div>
</template>
<script>
import { brandCommodityStatistics, exportExcelBrandCommodityStatistics } from '@/api/customer-management/client-list';
import cloneDeep from 'lodash/cloneDeep';
import pickBy from 'lodash/pickBy';
import download from '@/utils/download';
import { parseTime } from '@/utils';
import { getItem } from '@/utils/sessionStorage';
export default {
  name: 'list',
  data() {
    return {
      form: {
        data: {
          commodityName: '', // 商品名称
          commodityId: '', // 商品id
          commodityCode: '', // 商品条码
          skuId: '' // 商品标识
        },
        pageNo: 1,
        pageSize: 10
      },
      queryData: {},
      list: [],
      listLoading: true,
      total: 0,
      exportLoading: false // 导出按钮loading
    };
  },
  watch: {},
  computed: {
    // 过滤
    data() {
      const obj = { ...this.form.data };
      obj.distributorId = this.queryData.distributorId;
      obj.brandId = this.queryData.brandId;
      return pickBy(obj, (val) => !!val);
    }
  },
  activated() {
    const params = getItem(this.$route.path) || {};
    this.distributorId = params.id;
    this.queryData = params;
    this.fetchData();
  },
  methods: {
    // 导出
    onExport() {
      this.exportLoading = true;
      exportExcelBrandCommodityStatistics(this.data)
        .then((res) => {
          download(res, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', `品牌采购明细-${parseTime(Date.now(), '{y}-{m}-{d}-{h}:{i}:{s}')}.xlsx`);
        })
        .finally(() => {
          this.exportLoading = false;
        });
    },
    // 获取列表数据
    fetchData() {
      this.listLoading = true;
      const data = cloneDeep(this.form);
      data.data = this.data;
      brandCommodityStatistics(data)
        .then((response) => {
          if (response.data) {
            this.list = response.data.list;
            this.total = response.data.total;
          }
        })
        .finally(() => {
          this.listLoading = false;
        });
    },
    handleSizeChange(val) {
      this.form.pageSize = val;
      this.form.pageNo = 1;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.form.pageNo = val;
      this.fetchData();
    },
    onSearch() {
      this.form.pageNo = 1;
      this.form.pageSize = 10;
      this.fetchData();
    },
    onReset() {
      ['commodityName', 'commodityId', 'commodityCode', 'skuId'].forEach((item) => {
        this.form.data[item] = '';
      });
      this.createDate = null;
      this.onSearch();
    }
  }
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
@import 'src/styles/goods-table.scss';
.create {
  margin-bottom: 8px;
}
.filter-item .link {
  margin-left: 10px;
}
.goods-img {
  width: 60px;
  height: 60px;
}
.ruleForm {
  .radio-label {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
  }
  .el-radio {
    margin-right: 20px;
  }
  .el-input {
    width: 200px;
  }
}
.detail-tips {
  width: 100%;
  height: 50px;
  line-height: 50px;
  background-color: rgba(64, 158, 255, 0.19);
  border: 1px solid var(--color-primary);
  font-size: 16px;
  padding: 0 20px;
  margin-bottom: 12px;
}
</style>
