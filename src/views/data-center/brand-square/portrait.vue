<!-- 品牌广场 - 品牌画像 -->
<template>
  <el-container class="brand-portrait__container">
    <el-aside width="320px" v-show="isShow">
      <header class="aside-header">
        <div class="aside-header__logo">
          <img :src="brandInfo.logoUrl | defaultPlaceholderImg" />
        </div>
        <h3 class="aside-header__title">{{ brandInfo.name | fs }}</h3>
        <div class="aside-header__tags">
          <el-tag type="primary" v-if="brandInfo.brandExtInfo">{{ brandInfo.brandExtInfo.stageName }}</el-tag>
          <el-tag type="primary" v-if="brandInfo.brandCategoryName">{{ brandInfo.brandCategoryName }}</el-tag>
          <el-tag type="primary" v-if="brandInfo.tradeTypeName">{{ brandInfo.tradeTypeName }}</el-tag>
        </div>
      </header>
      <div class="aside-co-box">
        <div class="aside-title">
          <h4>基本信息</h4>
          <router-link :to="'/brand/BasicInfo/view/' + brandId + '?brandCode=' + brandCode">
            <Authority auth="/brand/list/:view">
              <el-button type="text">查看详细</el-button>
            </Authority>
          </router-link>
        </div>
        <ul class="row-wrap">
          <li class="aside-co-box-row">
            <div class="aside-co-box-row-title">业务分组</div>
            <div class="aside-co-box-row-tip">{{ brandInfo.brandGroupName | fs }}</div>
          </li>
          <li class="aside-co-box-row">
            <div class="aside-co-box-row-title">品牌分类</div>
            <div class="aside-co-box-row-tip">{{ brandInfo.brandCategoryName | fs }}</div>
          </li>
          <li class="aside-co-box-row">
            <div class="aside-co-box-row-title">贸易类型</div>
            <div class="aside-co-box-row-tip">{{ brandInfo.tradeTypeName | fs }}</div>
          </li>
          <li class="aside-co-box-row">
            <div class="aside-co-box-row-title">品牌销售状态</div>
            <div class="aside-co-box-row-tip">{{ brandInfo.saleStatus | saleStatusFilter }}</div>
          </li>
          <li class="aside-co-box-row">
            <div class="aside-co-box-row-title">经营品类</div>
            <div class="aside-co-box-row-tip">{{ brandInfo.businessCategoryName | fs }}</div>
          </li>
        </ul>
      </div>

      <div class="aside-co-box">
        <h4 class="aside-title">品牌画像标签</h4>
        <ul class="row-wrap">
          <li class="aside-co-box-row aside-co-box-row-hz">
            <div class="aside-co-box-row-title">品牌阶段</div>
            <div class="aside-co-box-row-tip">{{ brandInfo.brandExtInfo ? brandInfo.brandExtInfo.stageName : '--' }}</div>
          </li>
          <li class="aside-co-box-row aside-co-box-row-hz">
            <div class="aside-co-box-row-title">品牌客单</div>
            <div class="aside-co-box-row-tip">
              <div class="tags" v-if="brandInfo.averagePriceNameList && brandInfo.averagePriceNameList.length">
                <el-tag size="mini" type="primary" v-for="item in brandInfo.averagePriceNameList" :key="item">{{ item }}</el-tag>
              </div>
              <span v-else>--</span>
            </div>
          </li>
          <li class="aside-co-box-row aside-co-box-row-hz">
            <div class="aside-co-box-row-title">品牌毛利区间</div>
            <div class="aside-co-box-row-tip">
              <div class="tags" v-if="brandInfo.grossProfitNameList && brandInfo.grossProfitNameList.length">
                <el-tag size="mini" type="primary" v-for="item in brandInfo.grossProfitNameList" :key="item">{{ item }}</el-tag>
              </div>
              <span v-else>--</span>
            </div>
          </li>
          <li class="aside-co-box-row aside-co-box-row-hz">
            <div class="aside-co-box-row-title">竞品品牌</div>
            <div class="aside-co-box-row-tip">
              <div class="tags" v-if="brandInfo.brandExtCompetitiveNameList && brandInfo.brandExtCompetitiveNameList.length">
                <el-tag size="mini" type="primary" v-for="item in brandInfo.brandExtCompetitiveNameList" :key="item">{{ item }}</el-tag>
              </div>
              <span v-else>--</span>
            </div>
          </li>
          <li class="aside-co-box-row aside-co-box-row-hz">
            <div class="aside-co-box-row-title">
              <span>关联品牌</span>
              <el-tooltip effect="dark" placement="top">
                <div slot="content">关联品牌是指跟我司其他品牌人群相似或互补人群；</div>
                <i style="margin-left: 4px" class="el-icon-question"></i>
              </el-tooltip>
            </div>
            <div class="aside-co-box-row-tip">
              <div class="tags" v-if="brandInfo.brandExtRelationNameList && brandInfo.brandExtRelationNameList.length">
                <el-tag size="mini" type="primary" v-for="item in brandInfo.brandExtRelationNameList" :key="item">{{ item }}</el-tag>
              </div>
              <span v-else>--</span>
            </div>
          </li>
        </ul>
      </div>
    </el-aside>
    <el-container>
      <el-main class="right-main">
        <i class="toggle-btn-icon hermes" :class="isShow ? 'hermes-caidanshouqi' : 'hermes-caidanzhankai'" @click="toggleAside"></i>
        <el-tabs v-model="tabPosition" class="tabs-box custom-border-tabs">
          <el-tab-pane label="销售概况" name="SaleInfo"></el-tab-pane>
          <el-tab-pane label="客户矩阵" name="CustomerMatrix"></el-tab-pane>
          <el-tab-pane label="销售商品" name="SalesGoods"></el-tab-pane>
          <el-tab-pane label="匹配客户" name="MatchCustomers"></el-tab-pane>
        </el-tabs>
        <div class="right-main-content">
          <component :is="tabPosition" :brandId="brandId" :key="tabPosition"></component>
        </div>
      </el-main>
    </el-container>
  </el-container>
</template>

<script>
import SaleInfo from './components/sale-info.vue';
import CustomerMatrix from './components/customer-matrix.vue';
import SalesGoods from './components/sales-goods.vue';
import MatchCustomers from './components/match-customers.vue';
import { brandDistributorPortraitGetByBrand } from '@/api/data-center/brand-square';

export default {
  name: 'data-center-brand-square-portrait',
  data() {
    return {
      brandInfo: {},
      tabPosition: 'SaleInfo',
      categoryBrand: [],
      isShow: true
    };
  },
  components: { SaleInfo, CustomerMatrix, SalesGoods, MatchCustomers },
  computed: {
    brandId() {
      return this.$route.params?.id || '';
    },
    brandCode() {
      return this.$route.query?.brandCode || '';
    }
  },
  mounted() {
    this.getBrandInfo();
    this.setPageScrollTo();
  },
  filters: {
    saleStatusFilter(val) {
      const options = window.$vue.$dict['syzg_brand_sale_status'];
      return options.find((item) => item.value === val)?.label || '--';
    },
    fs(v) {
      return v || '--';
    }
  },
  methods: {
    toggleAside() {
      this.isShow = !this.isShow;
    },
    setPageScrollTo() {
      const pagedom = document.getElementsByClassName('content-inner-scrollTo')[0];
      if (pagedom) {
        pagedom.scrollTo(0, 0);
      }
    },
    getBrandInfo() {
      brandDistributorPortraitGetByBrand(this.brandId).then((res) => {
        this.brandInfo = res?.data?.brandVO || {};
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.brand-portrait__container {
  height: 100%;
}
.el-aside {
  background-color: #fff;
  color: #333;
  position: relative;
  padding: 24px 16px;
  font-size: 14px;
  margin-right: 8px;
  border-radius: 8px;
}

.el-main {
  background-color: #fff;
  color: #333;
  border-radius: 8px;
  position: relative;
  .toggle-btn-icon {
    cursor: pointer;
    position: absolute;
    left: 8px;
    top: 16px;
    z-index: 1;
  }
}

.aside-header {
  display: flex;
  align-items: center;
  flex-direction: column;
  padding-bottom: 24px;
  &__title {
    font-size: 16px;
    color: #0d1b3f;
    font-weight: 600;
    line-height: 150%;
    margin: 8px 0;
  }
  &__logo {
    border: 1px solid #e6e8eb;
    border-radius: 8px;
    width: 100px;
    height: 100px;
    display: flex;
    align-items: center;
    overflow: hidden;
    img {
      width: 100%;
    }
  }
  &__tags {
    display: flex;
    align-items: center;
    ::v-deep .el-tag {
      margin: 0 4px;
    }
  }
}
.aside-co-box {
  background: #f7f8fa;
  &:not(:last-child) {
    margin-bottom: 16px;
  }
  .aside-title {
    display: flex;
    padding: 12px;
    justify-content: space-between;
    align-items: center;
    color: #0d1b3f;
    border-bottom: 1px solid #e6e8eb;
    h4 {
      line-height: 20px;
    }
    .el-button {
      border: none;
      line-height: 20px;
      padding: 0;
    }
  }
  .row-wrap {
    padding: 16px 12px;
  }
  &-hz {
    align-items: center;
  }
}
.aside-co-box-row {
  display: flex;
  line-height: 150%;
  &:not(:last-child) {
    margin-bottom: 16px;
  }
  &-title {
    flex-shrink: 0;
    width: 84px;
    color: #555f78;
  }
  &-tip {
    flex: 1;
    text-align: right;
    color: #0d1b3f;
    .tags {
      width: 100%;
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      justify-content: flex-end;
      ::v-deep .el-tag {
        margin: 2px;
      }
    }
  }
}
.tabs-box {
  position: relative;

  ::v-deep {
    .el-tabs__nav-scroll {
      padding-left: 32px;
    }
    .el-tabs__header {
      margin: 0;
    }
    .el-tabs__item {
      height: 46px;
      line-height: 46px;
      &.is-active {
        color: var(--color-primary);
      }
    }
  }
}
.right-main {
  padding: 0;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  &-content {
    flex: 1;
    overflow-y: auto;
  }
}
.channels-img-box {
  text-align: right;
}
.channels-img {
  width: 24px;
  height: 24px;
  margin-left: 5px;
}
.el-icon-link:hover {
  color: #5f3bce;
}
.font12 {
  font-size: 12px;
}
.category-names-tags {
  margin-left: 3px;
}
</style>
