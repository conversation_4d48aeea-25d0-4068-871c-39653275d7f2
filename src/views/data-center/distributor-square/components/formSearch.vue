<!-- 搜索 -->
<template>
  <div class="form-search-box">
    <div class="search-box">
      <CombinedFormItem :show-label="false" :configuration="distributorSearch" size="small" ref="distributorSearch" />
      <div class="search-box__content">
        <button-hoc icon="el-icon-search" @click="onSubmit" size="small" type="primary" :loading="searchLoading">搜索</button-hoc>
        <el-button @click="onReset" size="small">重置</el-button>
        <Authority auth="/data-center/distributor-square/list/:export">
          <button-hoc :loading="exportLoading" @click="onExport" size="small">基础模板导出</button-hoc>
        </Authority>
        <Authority auth="/data-center/distributor-square/list/:advancedExport">
          <button-hoc :loading="exportLoading" @click="onAdvancedExport" size="small">高级模板导出</button-hoc>
        </Authority>
        <Authority auth="/data-center/distributor-square/list/:exportWithSilentTemplate">
          <button-hoc :loading="exportLoading" @click="onExportWithSilentTemplate" size="small">沉寂客户模板导出</button-hoc>
        </Authority>
        <el-button type="text" @click="showFilters = !showFilters" size="small" :icon="showFilters ? 'el-icon-arrow-up' : 'el-icon-arrow-down'">{{ showFilters ? '收起筛选' : '展开筛选' }}</el-button>
      </div>
      <div class="search-box__tip">
        <span>数据更新时间：截止到昨天的23:59:59</span>
        <a target="_blank" href="https://syounggroup.feishu.cn/sheets/shtcnFJHCPnZSiVniC1bH3pPJHd" class="wd-link">
          <el-button icon="el-icon-document" type="primary" plain size="mini">标签指标定义</el-button>
        </a>
      </div>
    </div>
    <div class="combined-group" v-show="showFilters">
      <CombinedFormItem label="分销商信息" :labelWidth="labelWidth" :configuration="distributorInformation" size="small" ref="distributorInformation" />
      <CombinedFormItem label="客户层级" :labelWidth="labelWidth" :configuration="customerCustomerLevel" size="small" ref="customerCustomerLevel" />
      <CombinedFormItem label="客户意向类目" :labelWidth="labelWidth" :configuration="customerIntentionCategory" size="small" ref="intentionCategoryValues" />
      <div class="combined-box-collapse">
        <CombinedFormItem label="业务模块" :labelWidth="labelWidth" :configuration="partnerOne" size="small" ref="partnerOne" />
        <el-button type="text" @click="showMorePartner = !showMorePartner" size="small" :icon="showMorePartner ? 'el-icon-arrow-up' : 'el-icon-arrow-down'">{{ showMorePartner ? '收起' : '展开' }}</el-button>
      </div>
      <div v-show="showMorePartner">
        <CombinedFormItem :labelWidth="labelWidth" :configuration="partnerTwo" size="small" ref="partnerTwo" />
        <CombinedFormItem :labelWidth="labelWidth" :configuration="partnerThree" size="small" ref="partnerThree" />
      </div>
      <CombinedFormItem label="客户等级" :labelWidth="labelWidth" :configuration="customerCustomerGrade" size="small" ref="customerCustomerGrade" />
      <CombinedFormItem label="采货类型" :labelWidth="labelWidth" :configuration="purchaseType" size="small" ref="purchaseType" />
      <CombinedFormItem label="采货渠道" :labelWidth="labelWidth" :configuration="purchaseChannels" size="small" ref="purchaseChannels" />
      <CombinedFormItem label="客户生命周期" :labelWidth="labelWidth" :configuration="customerLifeCycle" ref="customerLifeCycle" size="small" />
      <CombinedFormItem label="客户经营渠道" :labelWidth="labelWidth" :configuration="customerBusinessChannels" ref="customerBusinessChannels" size="small" />
      <div class="combined-box-collapse">
        <CombinedFormItem label="其他标签" :labelWidth="labelWidth" :configuration="otherTagsOne" ref="otherTagsOne" size="small" />
        <el-button type="text" @click="showMoreOtherTags = !showMoreOtherTags" size="small" :icon="showMoreOtherTags ? 'el-icon-arrow-up' : 'el-icon-arrow-down'">{{ showMoreOtherTags ? '收起' : '展开' }}</el-button>
      </div>
      <div v-show="showMoreOtherTags">
        <CombinedFormItem :labelWidth="labelWidth" :configuration="otherTagsTwo" ref="otherTagsTwo" size="small" />
        <CombinedFormItem :labelWidth="labelWidth" :configuration="otherTagsThree" ref="otherTagsThree" size="small" />
        <CombinedFormItem :labelWidth="labelWidth" :configuration="otherTagsFour" ref="otherTagsFour" size="small" />
      </div>
    </div>
    <div class="tags-box" v-show="tagsList.length">
      <span class="tags-box__label">已选标签：</span>
      <div class="tags-box__content">
        <!-- 已选标签信息： -->
        <el-tag v-for="tag in tagsList" :key="tag.key" closable class="tag" @close="onDeleteTag(tag.key, tag.slotKey)">
          {{ tag.name }}
        </el-tag>
      </div>
      <div class="tags-box__btns">
        <el-button type="text" size="mini" @click="onReset">清除所有选项</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import CombinedFormItem from '@/components/CombinedFormItem/index.vue';
import dict from '@/components/Common/dicts';
import omit from 'lodash/omit';
import combinedFormMixins from '@/mixins/combined-form';
import { parseTime } from '@/utils';
import download from '@/utils/download';
import { distributorPortraitExportExcel, distributorPortraitHighLevelExportExcel, distributorPortraitExportWithSilentTemplate } from '@/api/data-center/distributor-square';
import moment from 'moment';
import { organization_tree } from '@/api/setting/divide-group';
export default {
  name: 'formSearch',
  mixins: [combinedFormMixins],
  data() {
    return {
      treeData: [],
      exportLoading: false,
      formOptions: null, // 所有下拉选项集合
      showFilters: true,
      tagsList: [],
      filter: {
        name: '',
        status: '',
        applyDate: '',
        statusOptions: [], // 订单状态
        statusOptionsLoading: false
      },
      labelWidth: '100px',
      showMorePartner: false,
      showMoreOtherTags: false
    };
  },
  props: {
    searchLoading: Boolean,
    defaultData: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  components: { CombinedFormItem },
  computed: {
    customerCustomerLevel() {
      return [
        {
          type: 'selectTag',
          key: 'customerAttributeList',
          options: [...window.$vue.$dict['soyoungzg_distributor_customer_attribute'], { label: '未维护', value: 'EMPTY' }],
          tagName: '客户层级：',
          width: '100%'
        }
      ];
    },
    customerCustomerGrade() {
      return [
        {
          type: 'selectTag',
          key: 'grades',
          options: window.$vue.$dict['distributor_grade'],
          tagName: '客户等级：',
          width: '100%'
        }
      ];
    },
    customerIntentionCategory() {
      return [
        {
          type: 'selectTag',
          key: 'intentionCategoryValues',
          options: window.$vue.$dict['syzg_business_category'],
          tagName: '客户意向类目：',
          width: '100%'
        }
      ];
    },
    partnerOne() {
      return [
        {
          type: 'selectTag',
          key: 'businessPlateList',
          options: window.$vue.$dict['syzg_customer_module_all'],
          tagName: '业务模块：',
          width: '100%'
        }
      ];
    },
    partnerTwo() {
      return [
        {
          type: 'select',
          key: 'purchaseBrandIds',
          options: dict('BRAND_LIST_ALL'),
          tagName: '采购品牌：',
          $attrs: {
            multiple: true,
            collapseTags: true,
            placeholder: '采购品牌'
          }
        },
        {
          type: 'select',
          key: 'purchaseCategoryIds',
          options: dict('BRAND_COMMODITY_CATEGORY'),
          tagName: '采购品类：',
          $attrs: {
            multiple: true,
            collapseTags: true,
            placeholder: '采购品类'
          }
        },
        {
          type: 'select',
          key: 'favoritePriceRangeList',
          options: dict('COMMON_COMMODITY_PRICE_RANGE'),
          tagName: '偏好商品价格区间：',
          $attrs: {
            multiple: true,
            collapseTags: true,
            placeholder: '偏好商品价格区间'
          }
        },
        {
          type: 'datePicker',
          key: 'latestPurchaseTimeList',
          value: [],
          tagName: '最新采购时间：',
          width: '300px',
          $attrs: {
            type: 'daterange',
            width: '100%',
            defaultTime: ['00:00:00', '23:59:59'],
            'start-placeholder': '采购开始时间',
            'end-placeholder': '采购结束时间',
            pickerOptions: this.mixPickerOptions
          }
        },
        {
          type: 'datePicker',
          key: 'payDateList',
          value: [],
          tagName: '支付时间：',
          width: '300px',
          newline: true,
          $attrs: {
            type: 'daterange',
            width: '100%',
            defaultTime: ['00:00:00', '23:59:59'],
            'start-placeholder': '支付开始时间',
            'end-placeholder': '支付结束时间',
            pickerOptions: {
              ...this.mixPickerOptions,
              disabledDate(time) {
                return time.getTime() > moment().endOf('days').valueOf();
              }
            }
          }
        }
      ];
    },
    partnerThree() {
      return [
        // {
        //   type: 'select',
        //   key: 'systemStarLevels',
        //   options: [...window.$vue.$dict['soyoungzg_distributor_star_level'], { label: '未维护', value: 'EMPTY' }],
        //   tagName: '系统星级评级：',
        //   $attrs: {
        //     multiple: true,
        //     collapseTags: true,
        //     placeholder: '系统星级评级'
        //   }
        // },
        {
          type: 'select',
          key: 'izYearlyContract',
          options: [...window.$vue.$dict['soyoungzg_common_whether'], { label: '未维护', value: 'EMPTY' }],
          tagName: '是否为年框客户：',
          defaultValue: this.defaultData.izYearlyContract,
          $attrs: {
            placeholder: '是否为年框客户'
          }
        },
        {
          type: 'select',
          key: 'status',
          options: [
            { value: 'PASS', label: '审核通过' },
            { value: 'LOGOUT_WAIT_AUDIT', label: '注销中' }
          ],
          tagName: '审核状态：',
          defaultValue: this.defaultData.status,
          $attrs: {
            collapseTags: true,
            placeholder: '审核状态'
          }
        },
        {
          type: 'datePicker',
          key: 'auditDateList',
          value: [],
          tagName: '入驻时间：',
          width: '300px',
          $attrs: {
            type: 'daterange',
            width: '100%',
            defaultTime: ['00:00:00', '23:59:59'],
            'start-placeholder': '入驻开始时间',
            'end-placeholder': '入驻结束时间',
            pickerOptions: this.mixPickerOptions
          }
        }
      ];
    },
    distributorSearch() {
      return [
        {
          type: 'selectInput',
          key: 'shopNameAndplatformShopId',
          width: '400px',
          tagName: (k) => {
            if (k === 'shopName') {
              return '分销商名称：';
            } else if (k === 'platformShopId') {
              return '店铺ID：';
            } else if (k === 'ids') {
              return '分销商ID：';
            }
          },
          $attrs: {
            width: '100%',
            placeholder: '请输入'
          },
          slot: {
            options: [
              {
                value: 'shopName',
                label: '分销商名称'
              },
              {
                value: 'platformShopId',
                label: '店铺ID'
              },
              {
                value: 'ids',
                label: '分销商ID'
              }
            ],
            defaultValue: 'shopName',
            slotKey: 'shopNameAndplatformShopIdSlot',
            width: '120px',
            $attrs: {
              placeholder: '分销商类型'
            }
          }
        }
      ];
    },
    distributorInformation() {
      return [
        {
          type: 'select',
          key: 'csIds',
          options: dict('COMMON_EXPAND_ADVISER'),
          tagName: '专属顾问：',
          defaultValue: this.defaultData.csIds,
          $attrs: {
            multiple: true,
            collapseTags: true,
            placeholder: '专属顾问'
          }
        },
        {
          type: 'cascader',
          key: 'customerOrganizationIdList',
          options: this.treeData,
          tagName: '业务分组：',
          $attrs: {
            props: {
              multiple: true,
              label: 'name',
              value: 'id',
              emitPath: false
            },
            collapseTags: true,
            placeholder: '业务分组'
          }
        },
        {
          type: 'select',
          key: 'groupIds',
          options: dict('DISTRIBUTOR_TEAM_DS'),
          tagName: '所属团队：',
          $attrs: {
            placeholder: '所属团队'
          }
        }
      ];
    },
    purchaseType() {
      return [
        {
          type: 'selectTag',
          key: 'purchaseTypeList',
          options: dict('COMMON_DISTRIBUTOR_PURCHASE_TYPE'),
          tagName: '采货类型：',
          width: '100%',
          $attrs: {
            multiple: true,
            collapseTags: true,
            placeholder: '采货类型'
          }
        }
      ];
    },
    purchaseChannels() {
      return [
        {
          type: 'selectTag',
          key: 'purchaseChannelTypeList',
          options: window.$vue.$dict['syzg_purchase_channel_type_sub'],
          tagName: '采货渠道：',
          width: '100%',
          $attrs: {
            multiple: true,
            collapseTags: true,
            placeholder: '采货渠道'
          }
        }
      ];
    },
    customerLifeCycle() {
      return [
        {
          type: 'selectTag',
          key: 'activeDegreeList',
          options: dict('COMMON_ACTIVE_AEGREE'),
          tagName: '客户生命周期：',
          defaultValue: this.defaultData.activeDegreeList,
          width: '100%',
          $attrs: {
            multiple: true,
            collapseTags: true,
            placeholder: '客户生命周期'
          }
        }
      ];
    },
    customerBusinessChannels() {
      return [
        {
          type: 'select',
          key: 'mainChannel',
          options: dict('COMMON_CHANNEL'),
          tagName: '主营渠道：',
          $attrs: {
            collapseTags: true,
            placeholder: '主营渠道'
          }
        },
        {
          type: 'select',
          key: 'channels',
          options: dict('COMMON_CHANNEL'),
          tagName: '经营渠道：',
          $attrs: {
            multiple: true,
            collapseTags: true,
            placeholder: '经营渠道'
          }
        }
      ];
    },
    otherTagsOne() {
      return [
        {
          type: 'select',
          key: 'excludePurchaseBrandIdList',
          options: dict('BRAND_LIST_ALL'),
          tagName: '采购品牌剔除：',
          $attrs: {
            multiple: true,
            collapseTags: true,
            placeholder: '采购品牌剔除'
          }
        },
        {
          type: 'select',
          key: 'levels',
          options: dict('COMMON_PORTRAIT_LEVEL'),
          tagName: '客户内部评级：',
          $attrs: {
            multiple: true,
            collapseTags: true,
            placeholder: '客户内部评级'
          }
        },
        {
          type: 'select',
          key: 'industryLevelList',
          options: dict('COMMON_TRADE_LEVEL'),
          tagName: '客户行业评级：',
          $attrs: {
            multiple: true,
            collapseTags: true,
            placeholder: '客户行业评级'
          }
        },
        {
          type: 'select',
          key: 'platformShopLevelList',
          options: dict('COMMON_SHOP_LEVEL_TYPE'),
          tagName: '店铺等级：',
          $attrs: {
            multiple: true,
            collapseTags: true,
            placeholder: '店铺等级'
          }
        },
        {
          type: 'select',
          key: 'warehouseList',
          options: window.$vue.$dict['soyoungzg_warehouse'],
          tagName: '仓储能力：',
          $attrs: {
            multiple: true,
            collapseTags: true,
            placeholder: '仓储能力'
          }
        }
      ];
    },
    otherTagsTwo() {
      return [
        {
          type: 'select',
          key: 'distributionVolumeList',
          options: window.$vue.$dict['syzg_purchase_volume'],
          tagName: '分销发货总体量：',
          $attrs: {
            multiple: true,
            collapseTags: true,
            placeholder: '分销发货总体量'
          }
        },
        {
          type: 'select',
          key: 'monthlySalesList',
          options: window.$vue.$dict['soyoungzg_monthly_sales_type'],
          tagName: '店铺平均月销：',
          $attrs: {
            multiple: true,
            collapseTags: true,
            placeholder: '店铺平均月销'
          }
        },
        {
          type: 'select',
          key: 'favoriteGrossProfitRangeList',
          options: dict('COMMON_GROSS_PROFIT_RANGE'),
          tagName: '偏好商品利润空间：',
          $attrs: {
            multiple: true,
            collapseTags: true,
            placeholder: '偏好商品利润空间'
          }
        }
      ];
    },
    otherTagsThree() {
      return [
        {
          type: 'RangeInput',
          key: 'purchaseAverageNumList',
          width: '300px',
          tagName: '采购频率：',
          $attrs: {
            maxPlaceholder: '采购频率天数上限',
            minPlaceholder: '采购频率天数下限'
          }
        },
        {
          type: 'RangeInput',
          key: 'currentMonthPurchaseCountList',
          width: '300px',
          tagName: '月度采买次数：',
          $attrs: {
            maxPlaceholder: '月度采买次数上限',
            minPlaceholder: '月度采买次数下限'
          }
        },
        {
          type: 'RangeInput',
          key: 'commodityAverageAmountList',
          width: '300px',
          tagName: '平均商品价格：',
          $attrs: {
            maxPlaceholder: '平均商品价格上限',
            minPlaceholder: '平均商品价格下限'
          }
        },
        {
          type: 'RangeInput',
          key: 'purchaseAverageAmountList',
          width: '300px',
          tagName: '平均订单价格：',
          $attrs: {
            maxPlaceholder: '平均订单价格上限',
            minPlaceholder: '平均订单价格下限'
          }
        }
      ];
    },
    otherTagsFour() {
      return [
        {
          type: 'datePicker',
          key: 'firstPurchaseTimeList',
          value: [],
          tagName: '首采时间：',
          width: '300px',
          $attrs: {
            type: 'daterange',
            width: '100%',
            defaultTime: ['00:00:00', '23:59:59'],
            'start-placeholder': '首采开始时间',
            'end-placeholder': '首采结束时间',
            pickerOptions: this.mixPickerOptions
          }
        },
        {
          type: 'selectRangeInput',
          key: 'latestRange',
          value: [],
          width: '400px',
          tagName: (k) => {
            return k + '：';
          },
          $attrs: {
            maxPlaceholder: '金额上限',
            minPlaceholder: '金额下限'
          },
          slot: {
            options: dict('COMMON_PORTRAIT_PURCHASE_AMOUNT'),
            defaultValue: 'latestThirtyDayPurchaseAmountList',
            slotKey: 'latestTime',
            width: '133px',
            $attrs: {
              placeholder: ''
            }
          }
        },
        {
          type: 'selectRangeInput',
          key: 'currentRange',
          width: '367px',
          tagName: (k) => {
            return k + '：';
          },
          $attrs: {
            maxPlaceholder: '订单数量上限',
            minPlaceholder: '订单数量下限'
          },
          slot: {
            defaultValue: 'currentMonthPurchaseNumList',
            options: dict('COMMON_PORTRAIT_PURCHASE_NUM'),
            slotKey: 'currentTime',
            width: '116px',
            $attrs: {
              placeholder: '订单时段'
            }
          }
        }
      ];
    }
  },
  mounted() {
    this.getTree();
    // 同步标签
    if (Object.keys(this.defaultData).length) {
      setTimeout(() => {
        this.setTags();
      }, 500);
    }
  },
  activated() {
    this.onKeydown();
  },
  deactivated() {
    document.onkeydown = null;
  },
  methods: {
    // 监听回车键，触发搜索
    onKeydown() {
      const self = this;
      document.onkeydown = function (e) {
        const ev = document.all ? window.event : e;
        if (ev.keyCode === 13 && !self.searchLoading) {
          self.onSubmit();
        }
      };
    },
    getTree() {
      organization_tree({}).then((res) => {
        this.treeData = res.data;
      });
    },
    parseTime,
    // 高级导出
    onAdvancedExport() {
      this.exportLoading = true;
      const data = {};
      Object.keys(this.$refs).forEach((key) => {
        if (this.$refs[key].getValue) {
          const { value } = this.$refs[key].getValue();
          Object.assign(data, value); // 获取所有值
        }
      });
      const newData = this.processTheData(data);
      distributorPortraitHighLevelExportExcel(newData)
        .then((res) => {
          // 流文件 文件流
          try {
            const resObj = JSON.parse(new TextDecoder('utf-8').decode(new Uint8Array(res)));
            if (resObj.success) {
              this.$message.success(resObj.msg);
            } else {
              this.$message.error(resObj.msg || '系统错误');
            }
          } catch (error) {
            // 获取指标名称
            download(res, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', `分销商广场-${parseTime(Date.now(), '{y}-{m}-{d}-{h}:{i}:{s}')}.xlsx`);
          }
        })
        .finally(() => {
          this.exportLoading = false;
        });
    },
    // 沉寂客户模板导出
    onExportWithSilentTemplate() {
      this.exportLoading = true;
      const data = {};
      Object.keys(this.$refs).forEach((key) => {
        if (this.$refs[key].getValue) {
          const { value } = this.$refs[key].getValue();
          Object.assign(data, value); // 获取所有值
        }
      });
      const newData = this.processTheData(data);
      distributorPortraitExportWithSilentTemplate(newData)
        .then((res) => {
          // 流文件 文件流
          try {
            const resObj = JSON.parse(new TextDecoder('utf-8').decode(new Uint8Array(res)));
            if (resObj.success) {
              this.$message.success(resObj.msg);
            } else {
              this.$message.error(resObj.msg || '系统错误');
            }
          } catch (error) {
            // 获取指标名称
            download(res, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', `分销商广场-${parseTime(Date.now(), '{y}-{m}-{d}-{h}:{i}:{s}')}.xlsx`);
          }
        })
        .finally(() => {
          this.exportLoading = false;
        });
    },
    // 导出
    onExport() {
      this.exportLoading = true;
      const data = {};
      Object.keys(this.$refs).forEach((key) => {
        if (this.$refs[key].getValue) {
          const { value } = this.$refs[key].getValue();
          Object.assign(data, value); // 获取所有值
        }
      });
      const newData = this.processTheData(data);
      distributorPortraitExportExcel(newData)
        .then((res) => {
          // 流文件 文件流
          try {
            const resObj = JSON.parse(new TextDecoder('utf-8').decode(new Uint8Array(res)));
            if (resObj.success) {
              this.$message.success(resObj.msg);
            } else {
              this.$message.error(resObj.msg || '系统错误');
            }
          } catch (error) {
            // 获取指标名称
            download(res, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', `分销商广场-${parseTime(Date.now(), '{y}-{m}-{d}-{h}:{i}:{s}')}.xlsx`);
          }
        })
        .finally(() => {
          this.exportLoading = false;
        });
    },

    onSubmit() {
      console.log('onSubmit', this.$refs.distributorSearch);
      const data = {};
      const options = {};
      const tagsList = [];
      Object.keys(this.$refs).forEach((key) => {
        if (this.$refs[key].getValue) {
          const { value, config } = this.$refs[key].getValue();
          Object.assign(data, value); // 获取所有值
          if (config) {
            tagsList.push(...this.mixSetTags(value, config));
          }
        }
      });
      this.tagsList = tagsList;
      const newData = this.processTheData(data);
      if (!this.formOptions) {
        // 只触发一次
        this.formOptions = new Map(Object.entries(options));
      }
      this.$emit('onSubmit', newData);
    },
    // 初始化标签
    setTags() {
      const tagsList = [];
      Object.keys(this.$refs).forEach((key) => {
        if (this.$refs[key].getValue) {
          const { value, config } = this.$refs[key].getValue();
          if (config) {
            tagsList.push(...this.mixSetTags(value, config));
          }
        }
      });
      this.tagsList = tagsList;
    },
    onReset() {
      Object.keys(this.$refs).forEach((key) => {
        if (this.$refs[key].onItemReset) {
          this.$refs[key].onItemReset();
        }
      });
      this.tagsList = [];
      this.$emit('onReset', {});
    },
    // 复合数据特殊处理
    processTheData(data = {}) {
      this.mixTypeCheck(data, ['shopNameAndplatformShopIdSlot', 'shopNameAndplatformShopId']); //  分销商类型处理
      this.mixTypeCheck(data, ['commodityNameAndcommodityCodeSlot', 'commodityNameAndcommodityCode']); //  商品类型处理
      this.mixTypeCheck(data, ['latestTime', 'latestRange']); //  采购金额区间处理
      this.mixTypeCheck(data, ['currentTime', 'currentRange']); //  采购订单数区间处理
      this.mixTypeCheck(data, ['currentAmount', 'currentAmountRate']); //  采购金额环比处理
      this.mixTypeCheck(data, ['currentOrderCount', 'currentOrderCountRate']); //  采购订单数环比处理
      return omit(data, this.blacklist());
    },
    blacklist() {
      return ['shopNameAndplatformShopId', 'shopNameAndplatformShopIdSlot', 'currentRange', 'currentTime', 'latestRange', 'latestTime', 'commodityNameAndcommodityCode', 'commodityNameAndcommodityCodeSlot', 'currentAmount', 'currentAmountRate', 'currentOrderCount', 'currentOrderCountRate'];
    },
    onDeleteTag(Delkey, slotKey) {
      Object.keys(this.$refs).forEach((key) => {
        if (this.$refs[key].onItemReset) {
          this.$refs[key].onItemReset(Delkey, slotKey, key==='distributorSearch');
        }
      });
      const index = this.tagsList.findIndex((o) => o.key === Delkey);
      this.tagsList.splice(index, 1);
      this.onSubmit();
    }
  }
};
</script>
<style lang="scss" scoped>
.tags-box {
  margin-top: 16px;
  background: #f7f8fa;
  padding: 12px;
  display: flex;
  align-items: flex-start;
  &__label {
    flex-shrink: 0;
    font-size: 12px;
    line-height: 24px;
    margin-top: 4px;
  }

  .tag {
    min-height: 24px;
    height: auto;
    margin: 4px;
    white-space: pre-wrap;
  }
  &__content {
    flex: 1;
  }
  &__btns {
    flex-shrink: 0;
    margin-left: 4px;
  }
}
.form-search-box {
  background: #fff;
  padding: 16px;
  border-radius: 4px;
}
.search-box {
  font-size: 14px;
  display: flex;
  align-items: center;
  margin-top: 3px;
  .combined-box {
    margin: 0 10px 0 0;
  }
  &__content {
    flex: 1;
  }
  &__tip {
    color: var(--color-info);
    display: flex;
    align-items: center;
    justify-content: flex-end;
    span {
      font-size: 12px;
    }
    .wd-link {
      margin-left: 12px;
    }
  }
}

.combined-group {
  background: #f7f8fa;
  padding: 12px;
  margin-top: 16px;
}
.combined-box-collapse {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
</style>
