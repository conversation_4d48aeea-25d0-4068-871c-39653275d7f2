<template>
  <import-template selection="" :channelType="channelType" :isShowDelete="false" deleteError="删除全部错误数据" :name="name" v-bind="importOptions" :fileRowNumLimit="200" :fileAcceptLimit="['xls', 'xlsx', 'csv']"></import-template>
</template>

<script>
import importTemplate from '@/components/Common/Import/importTemplate.vue';
import { createThirdInBatch, createThirdOfflineInBatch, validThirdInBatch, validThirdOfflineInBatch } from '@/api/distributorManagement/cooperate-shop';
import { checkRepeatImportDistributor } from '@/api/distributorManagement/distributor/list.js';
import pick from 'lodash/pick';
import { checkRepeatDistributorFn } from '@/views/distributor-management/component';
import { selectGroup } from '@/components/Common/SelectGroup';
import { downloadDistributorImportTemplate, downloadDistributorOfflineImportTemplate } from '@/api/distributorManagement/cooperate-shop';

export default {
  name: 'order-trilateral-refund-import',
  components: { importTemplate },
  data() {
    return {
      groupId: '',
      channelType: '',
      tableOptionsData: {
        ONLINE: [
          {
            key: 'shopName',
            label: '店铺名称',
            required: true,
            style: {
              width: 180
            }
          },
          {
            key: 'platformShopId',
            label: '店铺ID',
            required: true,
            style: {
              width: 180
            }
          },
          {
            key: 'developTypeName',
            label: '拓展方式',
            required: true,
            style: {
              width: 180
            }
          },
          {
            key: 'businessPlateName',
            label: '业务板块',
            required: true,
            style: {
              width: 180
            }
          },
          {
            key: 'intentionCategoryName',
            label: '客户类目',
            required: true,
            style: {
              width: 180
            }
          },
          {
            key: 'contactTypeName',
            label: '联系方式类型',
            style: {
              width: 180
            }
          },
          {
            key: 'contactPhone',
            label: '联系方式',
            style: {
              width: 180
            }
          },
          {
            key: 'contactName',
            label: '真实姓名',
            style: {
              width: 180
            }
          },
          {
            key: 'email',
            label: '邮箱地址',
            style: {
              width: 180
            }
          },
          {
            key: 'channelName',
            label: '经营渠道',
            required: true,
            style: {
              width: 180
            }
          },
          {
            key: 'mainChannelName',
            label: '主营渠道',
            required: true,
            style: {
              width: 180
            }
          },
          {
            key: 'customerAttributeName',
            label: '客户层级',
            required: true,
            style: {
              width: 180
            }
          },
          {
            key: 'knowChannelName',
            label: '了解渠道',
            style: {
              width: 180
            }
          },
          {
            key: 'externalPlatformName',
            label: '外部平台名称',
            style: {
              width: 180
            }
          },
          {
            key: 'buyerTypeName',
            label: '绑定OMS买家信息',
            style: {
              width: 180
            }
          },
          {
            key: 'externalBuyerName',
            label: '买家昵称/渠道名称',
            style: {
              width: 180
            }
          },
          {
            key: 'purchaseTypeName',
            label: '客户类型',
            required: true,
            style: {
              width: 180
            }
          },
          {
            key: 'developCustomerServiceName',
            label: '拓展顾问',
            required: true,
            style: {
              width: 180
            }
          },
          {
            key: 'csName',
            label: '专属顾问',
            required: true,
            style: {
              width: 180
            }
          },
          {
            key: 'typeName',
            label: '结算方式',
            required: true,
            style: {
              width: 120
            }
          },
          {
            key: 'customerDescription',
            label: '客情说明',
            style: {
              width: 180
            }
          }
        ],
        OFFLINE: [
          {
            key: 'shopName',
            label: '客户名称',
            required: true,
            style: {
              width: 180
            }
          },
          {
            key: 'businessTypeAndExtendName',
            label: '客户业务类型',
            required: true,
            style: {
              width: 100
            }
          },
          {
            key: 'businessPlateName',
            label: '业务模块',
            required: true,
            style: {
              width: 80
            }
          },
          {
            key: 'intentionCategoryName',
            label: '客户类目',
            required: true,
            style: {
              width: 100
            }
          },
          {
            key: 'contactName',
            label: '联系人',
            required: true,
            style: {
              width: 100
            }
          },
          {
            key: 'contactTypeName',
            label: '联系方式类型',
            required: true,
            style: {
              width: 120
            }
          },
          {
            key: 'contactPhone',
            label: '联系方式',
            style: {
              width: 180
            }
          },
          {
            key: 'email',
            label: '邮箱地址',
            required: false,
            style: {
              width: 180
            }
          },
          {
            key: 'mainChannelName',
            label: '主营渠道',
            required: true,
            style: {
              width: 110
            }
          },
          {
            key: 'developCustomerServiceName',
            label: '拓展顾问',
            required: true,
            style: {
              width: 100
            }
          },
          {
            key: 'developTypeName',
            label: '拓展方式',
            required: true,
            style: {
              width: 100
            }
          },
          {
            key: 'csName',
            label: '专属顾问',
            required: true,
            style: {
              width: 100
            }
          },
          {
            key: 'typeName',
            label: '结算方式',
            required: true,
            style: {
              width: 120
            }
          },
          {
            key: 'shopQuantity',
            label: '店铺数量',
            required: false,
            style: {
              width: 80
            }
          },
          {
            key: 'address',
            label: '店铺位置',
            required: false,
            style: {
              width: 180
            }
          },
          {
            key: 'customerDescription',
            label: '客情说明',
            style: {
              width: 180
            }
          }
        ]
      }
    };
  },
  computed: {
    name() {
      return this.channelType === 'OFFLINE' ? '线下-三方分销商' : '线上-三方分销商';
    },
    importOptions() {
      return {
        importRequest: this.checkRepeatDistributor,
        validRequest: this.channelType === 'OFFLINE' ? validThirdOfflineInBatch : validThirdInBatch,
        uploadRequest: this.channelType === 'OFFLINE' ? `/soyoungzg/api/distributor/batchImportThirdOffline?groupId=${this.groupId}` : `/soyoungzg/api/distributor/batchImportThird?groupId=${this.groupId}`,
        downloadFailRequest: this.channelType === 'OFFLINE' ? '/soyoungzg/api/distributor/exportOfflineThirdError' : '/soyoungzg/api/distributor/exportThirdError',
        // 三方分销商管理模板地址
        templateUrl: this.channelType === 'OFFLINE' ? downloadDistributorOfflineImportTemplate : downloadDistributorImportTemplate,
        backUrl: '/distributor-management/distributor/list',
        tableOptions: this.tableOptionsData[this.channelType]
      };
    }
  },
  mounted() {
    this.groupId = this.$route.query.groupId;
    this.channelType = this.$route.query.channelType || 'ONLINE';
    if (this.$route.query.groupId) return;
    selectGroup((groupId) => {
      if (groupId) {
        this.groupId = groupId;
      } else {
        this.$back(this.backUrl);
      }
    }).apply(this);
  },
  methods: {
    // 检查是否存在重复分销商
    async checkRepeatDistributor(list) {
      if (this.channelType === 'OFFLINE') {
        return createThirdOfflineInBatch(
          list.map((i) => ({
            ...i
          }))
        );
      } else {
        const res = await checkRepeatImportDistributor(list.map((i, lineNumber) => pick({ ...i, lineNumber }, ['contactPhone', 'lineNumber'])));

        const data = res.data.filter((i) => i.repeatDistributorIdList.length);
        return checkRepeatDistributorFn.apply(this, [data]).then(() => {
          return createThirdInBatch(
            list.map((i) => ({
              ...i
            }))
          );
        });
      }
    }
  }
};
</script>
