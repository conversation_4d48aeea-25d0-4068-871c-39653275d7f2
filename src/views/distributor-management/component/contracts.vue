<template>
  <div v-frag>
    <sy-normal-table v-bind="table" ref="table" v-el-horizontal-scroll />
    <!--查看合同及合同附件-->
    <contract-preview :title="currentContractTitle" v-model="contractCausesDialog" :list="currentContractList" />
    <!-- 补充收付款信息 -->
    <markup-subject-information v-if="makeUpPayDialog" :visible.sync="makeUpPayDialog" :currentRow="currentRow" @success="refreshTableList"></markup-subject-information>
    <!--合同详情-->
    <contract-info v-if="contractInfoDialog" :visible.sync="contractInfoDialog" :currentRow="currentRow" @success="refreshTableList"></contract-info>
  </div>
</template>

<script>
import { parseTime } from '@/utils';
import { distributorContractExternalListUserNames, distributorContractListOwnCompany, exportExcel, listContract, revokeSigningContract, statistic, stopContract } from '@/api/distributorManagement/statistic/list';
import { externalChannelList, externalChannelListSap } from '@/api/distributorManagement/distributor/list';
import download from '@/utils/download';
import { customerList } from '@/api/common';
import { organization_tree } from '@/api/setting/divide-group';
import { downloadOssFile } from '@/utils/downloadUrl';
import ContractPreview from '@/components/ContractPreview';
import MarkupSubjectInformation from '@/components/MarkupSubjectInformation';
import ContractInfo from '@/components/ContractInfo';
import dict from '@/components/Common/dicts';

export default {
  name: 'contracts',
  components: {
    ContractInfo,
    ContractPreview,
    MarkupSubjectInformation
  },
  data() {
    return {
      exportLoading: false,
      saveLoading: false,
      statisticObj: {
        cancelNum: '', // 已撤销合同
        endNum: '', // 已过期合同
        invalidNum: '', // 未生效合同
        stoppedNum: '', // 已终止合同
        validNum: '' // 当前生效中合同
      },
      empForm: {
        id: '', // 主体ID
        channelInfo: [],
        otherChannelInfo: []
      },
      rules: {
        channelInfo: [{ required: true, message: '必填信息', trigger: 'change' }],
        otherChannelInfo: [{ required: true, message: '必填信息', trigger: 'change' }]
      },
      contractCausesDialog: false,
      currentContractTitle: '',
      currentContractList: [], // 当前合同及合同附件
      omsOptions: [],
      sapOptions: [],
      makeUpPayDialog: false,
      currentRow: {},
      contractInfoDialog: false
    };
  },
  mounted() {
    this.getAllOptions();
    this.initFilter();
  },
  computed: {
    table() {
      const that = this;
      return {
        initSearch: false,
        tip: [
          {
            text: `当前生效中：<span class="red">${that.statisticObj.validNum}</span> 份，
                  已过期：<span class="red">${that.statisticObj.endNum}</span> 份，
                  已终止：<span class="red">${that.statisticObj.stoppedNum}</span> 份，
                  未生效：<span class="red">${that.statisticObj.invalidNum}</span> 份`
          }
        ],
        filters: [
          {
            tag: 'el-input',
            prop: 'ids',
            label: '合同ID',
            bind: {
              placeholder: '请输入合同ID,多个用逗号隔开'
            }
          },
          {
            tag: 'el-input',
            prop: 'contractName',
            label: '合同名称',
            bind: {
              placeholder: '请输入合同名称',
              clearable: true
            }
          },
          {
            tag: 'el-input',
            prop: 'distributorIds',
            label: '分销商ID',
            bind: {
              placeholder: '请输入分销商ID，多个用逗号隔开'
            }
          },
          {
            tag: 'el-input',
            prop: 'shopName',
            label: '分销商名称',
            bind: {
              placeholder: '请输入分销商名称'
            }
          },
          {
            tag: 'el-input',
            prop: 'externalContractNumber',
            label: '飞书合同编号',
            bind: {
              placeholder: '请输入飞书合同编号',
              clearable: true
            }
          },
          {
            tag: 'sy-select',
            prop: 'csIds',
            label: '专属顾问',
            bind: {
              placeholder: '请选择专属顾问',
              showEye: true,
              filterable: true,
              multiple: true,
              clearable: true,
              options: async () => {
                const res = await customerList({});
                return res.data.map((item) => {
                  return {
                    label: `${item.name}${item.mobile}`,
                    value: item.id
                  };
                });
              }
            }
          },
          {
            tag: 'el-cascader',
            prop: 'customerOrganizationIdList',
            label: '专属顾问分组',
            bind: {
              placeholder: '可多选',
              options: that.organization,
              collapseTags: true,
              props: {
                emitPath: false,
                multiple: true,
                expandTrigger: 'hover',
                value: 'id',
                label: 'name'
              }
            }
          },
          {
            tag: 'sy-select',
            prop: 'signStatusList',
            label: '签署状态',
            bind: {
              placeholder: '请选择',
              filterable: true,
              multiple: true,
              options: [
                { label: '全部', value: '' },
                { label: '待签署', value: 'WAITING' },
                { label: '拒签', value: 'REJECT' },
                { label: '撤销', value: 'CANCEL' },
                { label: '签署完成', value: 'FINISH' }
              ]
            }
          },
          {
            tag: 'sy-select',
            prop: 'statusList',
            label: '合同状态',
            bind: {
              placeholder: '请选择',
              filterable: true,
              multiple: true,
              options: window.$vue.$dict['contract_status']
            }
          },
          {
            tag: 'sy-date-picker',
            prop: ['startSignTime', 'endSignTime'],
            label: '合同签署时间',
            bind: {
              bind: {
                'start-placeholder': '开始时间',
                'end-placeholder': '结束时间',
                type: 'daterange',
                valueFormat: 'timestamp',
                'default-time': ['00:00:00', '23:59:59']
              }
            }
          },
          {
            tag: 'sy-date-picker',
            prop: ['startDate', 'endDate'],
            label: '合同到期时间',
            bind: {
              bind: {
                'start-placeholder': '开始时间',
                'end-placeholder': '结束时间',
                type: 'daterange',
                valueFormat: 'timestamp',
                'default-time': ['00:00:00', '23:59:59']
              }
            }
          },
          {
            tag: 'sy-select',
            prop: 'expiredTypeList',
            label: '合同即将到期',
            bind: {
              placeholder: '请选择',
              showEye: true,
              filterable: true,
              multiple: true,
              options: window.$vue.$dict['distributor_contract_expired_type']
            }
          },
          {
            tag: 'sy-select',
            prop: 'appId',
            label: '甲方主体',
            bind: {
              placeholder: '请选择',
              filterable: true,
              flashOptions: true,
              options: async () => {
                const res = await distributorContractListOwnCompany();
                return res.data;
              },
              optionsProps: {
                label: 'company',
                value: 'id'
              }
            }
          },
          {
            tag: 'el-input',
            prop: 'company',
            label: '乙方主体',
            bind: {
              placeholder: '请输入乙方主体'
            }
          },
          {
            tag: 'sy-select',
            prop: 'platformOwner',
            label: '主体平台归属',
            bind: {
              filterable: true,
              placeholder: '请选择',
              options: [...that.$dict['distributor_contract_platform_owner_type'], { label: '未完善', value: 'NONE' }]
            }
          },
          {
            tag: 'sy-select',
            prop: 'sapChannelCodes',
            label: 'SAP主数据渠道',
            bind: {
              filterable: true,
              multiple: true,
              showEye: true,
              placeholder: '请选择',
              options: that.sapOptions,
              flashOptions: true,
              maxLen: 10000
            }
          },
          {
            tag: 'custom-select-group',
            prop: ['dataKey', 'dataValue'],
            label: '数据完善情况',
            bind: {
              bindSelectFirst: {
                options: [
                  { label: '主数据信息', value: 'izCompleteChannelInfo' },
                  { label: '收付款信息', value: 'izCompleteBankCard' },
                  { label: '年框政策信息', value: 'izCompleteYearyPolicy' }
                ]
              },
              bindSelectSecond: {
                options: [
                  { label: '全部', value: '' },
                  { label: '已完善', value: '1' },
                  { label: '未完善', value: '0' }
                ]
              }
            }
          },
          {
            tag: 'sy-select',
            prop: 'izYearlyContract',
            label: '是否年框合同',
            bind: {
              placeholder: '请选择',
              options: window.$vue.$dict['soyoungzg_common_whether']
            }
          },
          {
            tag: 'sy-select',
            prop: 'labelTypes',
            label: '合同变更类型',
            bind: {
              placeholder: '请选择',
              filterable: true,
              multiple: true,
              options: window.$vue.$dict['distributor_contract_label_type']
            }
          },
          {
            tag: 'sy-select',
            prop: 'izFrameworkAgreement',
            label: '是否框架合同',
            bind: {
              placeholder: '请选择',
              options: window.$vue.$dict['soyoungzg_common_whether']
            }
          },
          {
            tag: 'sy-select',
            prop: 'settlementType',
            label: '结算方式',
            bind: {
              placeholder: '请选择',
              options: [...window.$vue.$dict['soyoungzg_distributor_type'], { label: '未完善', value: 'EMPTY' }]
            }
          },
          {
            tag: 'sy-select',
            prop: 'izContractRelation',
            label: '是否关联其他合同',
            bind: {
              placeholder: '请选择',
              options: window.$vue.$dict['soyoungzg_common_whether']
            }
          },
          {
            tag: 'sy-select',
            prop: 'externalContractCreateUserId',
            label: '飞书合同申请人',
            bind: {
              placeholder: '请选择',
              filterable: true,
              options: async () => {
                const { data = [] } = await distributorContractExternalListUserNames();
                return data.filter((item) => item && item.createUserId);
              },
              optionsProps: { label: 'createUserName', value: 'createUserId' }
            }
          },
          {
            tag: 'el-input',
            prop: 'externalRelationContractNumber',
            label: '飞书关联合同编号',
            bind: {
              placeholder: '请输入飞书关联合同编号',
              clearable: true
            }
          },
          {
            tag: 'sy-select',
            prop: 'dataType',
            label: '创建方式',
            bind: {
              placeholder: '请选择',
              filterable: true,
              options: window.$vue.$dict['contract_data_type']
            }
          },
          {
            tag: 'sy-select',
            prop: 'yearlyBrandIdsList',
            label: '年框合同品牌',
            bind: {
              filterable: true,
              multiple: true,
              placeholder: '可多选',
              options: async () => await dict('BRAND_LIST_ALL'),
              flashOptions: true
            }
          }
        ],
        btns: [
          {
            text: '导出',
            type: 'primary',
            code: '/distributor-management/statistic/:export',
            confirm: '是否导出数据？',
            bind: {
              loading: that.exportLoading
            },
            call: ({ filtersValue }) => that.onExport(filtersValue)
          }
        ],
        columns() {
          return [
            {
              label: '合同信息',
              width: 160,
              prop: 'render',
              itemBind: {
                fixed: 'left'
              },
              render: (h, { row }) => (
                <div class="link-type" onClick={() => that.handleContractInfo(row, 'detail')}>
                  <p class="leading-small">{row.dataType === 'ONLINE' ? '水羊直供产品经销合同' : row.contractName}</p>
                  <p>{row.id}</p>
                  {row.contractRelationNum && (
                    <p>
                      关联合同{row.contractRelationNum}个<span class="m-l-4">></span>
                    </p>
                  )}
                  <common-copy-text desc="合同ID" btnType="primary" value={row.id} />
                </div>
              )
            },
            {
              label: '分销商信息',
              itemBind: {
                minWidth: 170
              },
              prop: 'render',
              render: (h, { row }) => (
                <div style="text-align: left">
                  <p>分销商ID：{row.distributorId || '-'}</p>
                  <p>店铺名称：{row.shopName || '-'}</p>
                  <p>登录手机号：{row.mobile || '-'}</p>
                  <p>专属顾问：{row.customerServiceName || '-'}</p>
                </div>
              )
            },
            {
              label: '签署主体',
              prop: 'render',
              itemBind: {
                minWidth: 180
              },
              render: (h, { row }) => (
                <div v-frag>
                  <p>甲方：{row.ownCompanyName || '-'}</p>
                  {row.contractParticipantList && row.contractParticipantList.length
                    ? row.contractParticipantList.map((item, index) => (
                        <p>
                          乙方{row.contractParticipantList.length > 1 ? Number(index + 1) : ''}：{item.company}
                        </p>
                      ))
                    : '乙方：-'}
                </div>
              )
            },
            {
              label: '合作状态',
              width: 110,
              render: (h, { row }) => (
                <div v-frag>
                  <p>
                    <el-tag type={that.getTagType(row.status)}>{row.statusName}</el-tag>
                  </p>
                  {row.startDate ? <span>{parseTime(row.startDate, '{y}-{m}-{d}')}</span> : ''} 至 {row.endDate ? <span>{parseTime(row.endDate, '{y}-{m}-{d}')}</span> : ''}
                </div>
              )
            },
            {
              label: '签署状态',
              prop: 'signStatusName',
              width: 80
            },
            {
              label: '授权渠道',
              type: 'array',
              prop: 'authChannelNames',
              itemBind: {
                minWidth: 100
              }
            },
            {
              label: '渠道主数据',
              itemBind: {
                minWidth: 150
              },
              render: (h, { row }) => (
                <div v-frag>
                  {row.contractParticipantList && row.contractParticipantList.length
                    ? row.contractParticipantList.map((item, index) =>
                        item.channelRelationList.length ? (
                          <div v-frag>
                            {row.contractParticipantList.length > 1 && <span>{'乙方' + Number(index + 1)}</span>}
                            {item.channelRelationList.map((channel) => (
                              <div>
                                <span>{channel.partyRelation.sapChannelName || '-'}</span>
                                {channel.brandList && channel.brandList.length && <span>-</span>}
                                {channel.brandList &&
                                  channel.brandList.length &&
                                  channel.brandList.map((brand, bIndex) => (
                                    <span>
                                      {brand.name}
                                      {bIndex < channel.brandList.length - 1 ? '、' : ''}
                                    </span>
                                  ))}
                              </div>
                            ))}
                          </div>
                        ) : (
                          '-'
                        )
                      )
                    : '-'}
                </div>
              )
            },
            {
              label: '合作信息',
              width: 140,
              render: (h, { row }) => (
                <div v-frag>
                  {row.classificationName && <p>{row.classificationName}</p>}
                  <p>
                    {row.izFrameworkAgreement === '1' ? (
                      <div v-frag>
                        <span>框架合同</span>
                        {row.izYearlyContract === '1' && <span>-年框合同</span>}
                      </div>
                    ) : (
                      '非框架合同'
                    )}
                  </p>
                  {row.settlementType && <p>{that.getSettlementTypeName(row.settlementType)}</p>}
                </div>
              )
            },
            {
              label: '合同创建信息',
              itemBind: {
                minWidth: 100
              },
              render: (h, { row }) => (
                <div v-frag>
                  <p>{row.dataTypeName}</p>
                  {row.externalContractCreateName ? <p>申请人：{row.externalContractCreateName}</p> : ''}
                  {row.externalContractNumber && (
                    <a class="link-type" title="飞书合同编号" onClick={() => that.jumpFeishuContract(row.externalFeiShuContractId)}>
                      {row.externalContractNumber}
                    </a>
                  )}
                </div>
              )
            },
            {
              label: '关联授权书',
              width: 100,
              render: (h, { row }) => (
                <div v-frag>
                  {row.licenses ? (
                    <span class="link-type" onClick={() => that.jumpCertificate(row.id)}>
                      {row.licenses.length}
                    </span>
                  ) : (
                    0
                  )}
                </div>
              )
            },
            {
              label: '操作',
              type: 'btns',
              width: 120,
              itemBind: {
                fixed: 'right'
              },
              btns({ row }) {
                if (row.dataType === 'MANUALLY_RELEVANCY' || row.dataType === 'FEISHU_REL') {
                  return [
                    {
                      type: 'text',
                      code: 'complete-contract-information',
                      text: '完善合同信息',
                      call: () => that.handleContractInfo(row, 'edit')
                    },
                    {
                      hide: !row.contractCauses || row.contractCauses.length === 0,
                      type: 'text',
                      text: '查看合同附件',
                      code: 'distributor-management-statistic-view-contract-attachments',
                      call() {
                        that.currentContractList = row.contractCauses.map((item) => {
                          return {
                            name: item.fileName,
                            url: item.ossFileUrl
                          };
                        });
                        that.currentContractTitle = '查看合同附件';
                        that.contractCausesDialog = true;
                      }
                    },
                    {
                      hide: row.izSupplementBankCard === '1',
                      type: 'text',
                      code: 'improve-the-receipt-and-payment-information',
                      text: '完善收付款信息',
                      call() {
                        that.handleMakeUpPay(row);
                      }
                    },
                    {
                      hide: that.isHideGeneratePolicyButton(row),
                      type: 'text',
                      code: 'yearly-policy-management-policy-list-add',
                      text: '生成年框政策台账',
                      call() {
                        that.$router.push(`/yearly-policy-management/policy-list/created?contractId=${row.id}`);
                      }
                    },
                    {
                      hide: row.izYearlyContract !== '1' || !row.distributorYearlyPolicyIds || !row.distributorYearlyPolicyIds.length,
                      type: 'text',
                      code: 'yearly-policy-management-policy-list-detail',
                      text: '查看年框政策',
                      call: () => that.jumpYearlyPolicy(row)
                    }
                  ];
                }
                return [
                  {
                    hide: !row.contractUrl,
                    type: 'text',
                    text: '查看合同',
                    call() {
                      that.currentContractList = [{ name: row.contractName, url: row.contractUrl }];
                      that.currentContractTitle = '查看合同';
                      that.contractCausesDialog = true;
                    }
                  },
                  {
                    hide: !row.contractUrl,
                    type: 'text',
                    code: 'distributor-management-statistic-download-contract',
                    text: '下载合同',
                    confirm: '确认下载该合同吗?',
                    call() {
                      that.downloadContract(row);
                    }
                  },
                  {
                    hide: row.signStatus !== 'WAITING',
                    type: 'text',
                    text: '撤销签署',
                    confirm: '此操作将撤销签署该合同, 是否继续?',
                    call() {
                      that.revokeContract(row.id);
                    }
                  },
                  {
                    hide: !(['USE', 'NOT_START'].includes(row.status) && row.signStatus === 'FINISH'),
                    type: 'text',
                    text: '终止合同',
                    confirm: '此操作将终止该合同, 是否继续?',
                    call() {
                      that.stopContract(row.id);
                    }
                  },
                  {
                    hide: row.signStatus !== 'WAITING',
                    type: 'text',
                    text: '复制签署链接',
                    call() {
                      that.onCopy(row.shortSignUrl);
                    }
                  },
                  {
                    hide: row.izSupplementBankCard === '1',
                    type: 'text',
                    code: 'improve-the-receipt-and-payment-information',
                    text: '完善收付款信息',
                    call() {
                      that.handleMakeUpPay(row);
                    }
                  }
                ];
              }
            }
          ];
        },
        async search({ filtersValue, pageFilter }) {
          const { pageNo, pageSize } = pageFilter;
          const { distributorIds = '', ids = '', dataKey = '', dataValue = '', ...other } = filtersValue;
          const data = {
            ...other,
            distributorIds: distributorIds ? distributorIds.split(',') : [],
            ids: ids ? ids.split(',') : []
          };
          // 处理组合组件数据
          if (dataKey) {
            data[dataKey] = dataValue;
          }
          that.getStatistic(data);
          const res = await listContract({
            data,
            pageNo,
            pageSize
          });
          const { list = [], total = 0 } = res.data || {};
          return {
            list: list.map((item) => {
              return { ...item, isShowMore: false };
            }),
            total
          };
        }
      };
    }
  },
  methods: {
    // 获取结算方式名称
    getSettlementTypeName(val) {
      return window.$vue.$dict['soyoungzg_distributor_type'].find((item) => item.value === val)?.label || '';
    },
    // 渲染合同状态标签
    getTagType(status) {
      if (status === 'USE') {
        return 'success';
      }
      if (['STOPPED', 'END'].includes(status)) {
        return 'danger';
      }
      return 'info';
    },
    // 初始化过滤条件
    initFilter() {
      const { mobile, shopName, appId, contactRealName, state = '', csIds = '', ids = '', distributorIds = '', expiredTypeList = '', sapChannelCode = '' } = this.$route.query;
      if (ids) {
        this.$refs.table.setFiltersValue(ids, 'ids');
      }
      if (distributorIds) {
        this.$refs.table.setFiltersValue(distributorIds, 'distributorIds');
      }
      if (expiredTypeList) {
        this.$refs.table.setFiltersValue(expiredTypeList.split(','), 'expiredTypeList');
      }
      if (mobile) {
        this.$refs.table.setFiltersValue(mobile, 'mobile');
      }
      if (shopName) {
        this.$refs.table.setFiltersValue(shopName, 'shopName');
      }
      if (appId && appId !== 'undefined') {
        this.$refs.table.setFiltersValue(appId, 'appId');
      }
      if (contactRealName && contactRealName !== 'undefined') {
        this.$refs.table.setFiltersValue(contactRealName, 'company');
      }
      if (state) {
        if (state === 'all') {
          // 合同所有 : 待签署/签署完成  合同状态是所有
          this.$refs.table.setFiltersValue(['WAITING', 'FINISH'], 'signStatusList');
        } else if (state === 'valid') {
          // 有效 ： 合同未生效、合同生效中
          this.$refs.table.setFiltersValue(['NOT_START', 'USE'], 'statusList');
          this.$refs.table.setFiltersValue(['FINISH'], 'signStatusList');
        } else {
          this.$refs.table.setFiltersValue(state.split(','), 'statusList');
        }
      }
      if (csIds) {
        this.$refs.table.setFiltersValue([csIds], 'csIds');
      }
      if (sapChannelCode) {
        this.$refs.table.setFiltersValue([sapChannelCode], 'sapChannelCodes');
      }
      this.$router.push({ query: {} });
      this.refreshTableList();
    },
    // 专属顾问分组
    getOrganization() {
      organization_tree({}).then((res) => {
        this.organization = res?.data ?? [];
      });
    },
    // 获取所有下拉框数据
    getAllOptions() {
      this.getOrganization();
      // table filter options
      externalChannelListSap({}).then((res) => {
        this.sapOptions = res.data.map((i) => {
          return { value: i.sapChannelCode, label: `${i.sapChannelCode}-${i.sapChannelName}` };
        });
      });
      externalChannelList({}).then((res) => {
        this.omsOptions = res.data.map((i) => {
          return { value: i.channelId, label: `${i.channelId}-${i.channelName}` };
        });
      });
    },
    // 刷新表格数据
    refreshTableList() {
      const ref = this.$refs.table;
      ref && ref.handlerSearch();
    },
    // 复制合同链接
    onCopy: function (e) {
      const input = document.createElement('input');
      input.value = e;
      document.body.appendChild(input);
      input.select();
      document.execCommand('Copy');
      document.body.removeChild(input);
      this.$message.success('合同链接' + e + '复制成功');
    },
    // 获取数据
    getStatistic(filtersValue) {
      statistic(filtersValue).then((res) => {
        this.statisticObj = res.data;
      });
    },
    // 导出
    onExport(filtersValue) {
      this.exportLoading = true;
      exportExcel(filtersValue)
        .then((res) => {
          download(res, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', `合同管理列表-${parseTime(Date.now(), '{y}-{m}-{d}-{h}:{i}:{s}')}.xlsx`);
        })
        .finally(() => {
          this.exportLoading = false;
        });
    },
    // 撤销签署
    revokeContract(id) {
      revokeSigningContract(id).then(() => {
        this.refreshTableList();
        this.statistic();
        this.$message({
          type: 'success',
          message: '撤销签署成功!'
        });
      });
    },
    // 终止合同
    stopContract(id) {
      stopContract(id).then(() => {
        this.refreshTableList();
        this.statistic();
        this.$message({
          type: 'success',
          message: '终止合同成功!'
        });
      });
    },
    // 跳转到授权书管理
    jumpCertificate(id) {
      this.$router.push({
        path: '/brand/authority/certificate/list',
        query: {
          distributorContractId: id,
          signStatus: 'SUCCESS'
        }
      });
    },
    // 查看年框政策,一个合同对应多个年框政策的情况下，跳转到年框政策台账列表，带上合同id
    jumpYearlyPolicy(row) {
      const { id, izYearlyContract, distributorYearlyPolicyIds = [] } = row;
      if (izYearlyContract === '1' && distributorYearlyPolicyIds.length > 1) {
        this.$router.push(`/yearly-policy-management/policy-list?relContractId=${id}`);
      } else {
        const policyId = distributorYearlyPolicyIds[0];
        this.$router.push(`/yearly-policy-management/policy-list/detail/${policyId}`);
      }
    },
    // 补充收付款信息
    handleMakeUpPay(row) {
      this.currentRow = row;
      this.makeUpPayDialog = true;
    },
    // 复制主体信息
    handleCopyImproveInformation(row) {
      const { platformOwner, buildSelfPartyRelationVOList = [] } = row;
      let value = '';
      if (platformOwner === 'SYZG_CHANNEL') {
        value = `${row.sapChannelCode}-${row.sapChannelCodeName}-${row.omsChannelId}-${row.omsChannelIdName}`;
      } else if (buildSelfPartyRelationVOList.length) {
        // 换行显示
        value = buildSelfPartyRelationVOList
          .map((item) => `${item.sapChannelCode}-${item.sapChannelName}-${item.omsChannelId}-${item.omsChannelName}`)
          .toString()
          .replace(/,/g, '\n');
      }
      this.$copyText(value)
        .then(() => {
          this.$message.success('复制成功');
        })
        .catch(() => {
          this.$message.info(`复制失败,请手动复制`);
        });
    },
    //  跳转飞书合同
    jumpFeishuContract(externalFeiShuContractId) {
      if (!externalFeiShuContractId) {
        return;
      }
      const url = `https://contract.feishu.cn/management/all/detail/${externalFeiShuContractId}`;
      window.open(url, '_blank');
    },
    // 下载合同
    downloadContract({ contractUrl, contractName }) {
      if (!contractUrl) {
        return;
      }
      downloadOssFile(contractUrl, contractName);
    },
    // 是否隐藏生成年框政策台账按钮
    isHideGeneratePolicyButton(row) {
      const { izYearlyContract, distributorYearlyPolicyIds = [], yearlyBrandIds, status } = row;
      if (!(izYearlyContract === '1' && distributorYearlyPolicyIds.length === 0)) {
        return true;
      }
      return !(distributorYearlyPolicyIds.length === 0 && yearlyBrandIds && ['USE', 'NOT_START'].includes(status));
    },
    // 查看/完善合同信息操作
    handleContractInfo(row, type) {
      if (!type || !['detail', 'edit'].includes(type)) {
        return;
      }
      this.currentRow = { ...row, operateType: type };
      this.contractInfoDialog = true;
    }
  }
};
</script>

<style scoped lang="scss">
::v-deep {
  .el-table th > .cell {
    white-space: pre-line;
  }
  .columns-operation-info {
    white-space: pre-line;
    pointer-events: none;
    color: #0d1b3f;
    line-height: 1.5;
  }
  .red {
    color: var(--color-danger);
  }
  .columns-operation .cell {
    .el-button {
      width: 100%;
      margin: 0;
      text-align: center;
    }
  }
  .contract-main-data {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    .main-data-btns {
      padding-left: 4px;
      display: flex;
      flex-direction: column;
      .el-button {
        margin: 4px 0 0 0;
        font-size: 16px;
      }
    }
  }
  .sap-channel-name-container {
    p {
      display: none;
      // 前3个元素显示
      &:nth-of-type(-n + 3) {
        display: block;
      }
    }
  }
}
</style>
