<!-- 积分明细数据 -->
<template>
  <div class="quick-bi-container">
    <div class="bi-radio-group">
      <el-radio-group v-model="pageType">
        <el-radio-button label="YearlyAccrual">年框政策计提</el-radio-button>
        <el-radio-button label="RegularAccrual">常规政策计提</el-radio-button>
      </el-radio-group>
    </div>
    <component :is="pageType" :key="pageType" />
  </div>
</template>

<script>
import YearlyAccrual from './components/yearly-accrual.vue';
import RegularAccrual from './components/regular-accrual.vue';
export default {
  name: 'distributor-management-rebate-accrual',
  components: {
    YearlyAccrual,
    RegularAccrual
  },
  data() {
    return {
      pageType: 'YearlyAccrual'
    };
  }
};
</script>
<style scoped lang="scss">
::v-deep {
  .bi-view {
    border-radius: 0;
  }
  .bi-view-head {
    position: absolute;
    top: -56px;
    right: 0;
  }
}
.bi-radio-group {
  background-color: #fff;
  border-radius: 8px 8px 0 0;
  padding: 12px;
  display: flex;
  align-items: center;
}
</style>
