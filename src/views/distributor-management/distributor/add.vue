<!-- 手动新增分销商 -->
<template>
  <DistributorDetail is-add :selectedGroupId="groupId" />
</template>

<script>
import DistributorDetail from '@/components/views/distributorManagement/distributor/DistributorDetail';
import { selectGroup } from '@/components/Common/SelectGroup';

export default {
  name: 'customer-management-clue-management-create-distributor',
  data() {
    return {
      groupId: ''
    };
  },
  mounted() {
    selectGroup((groupId) => {
      if (groupId) {
        this.groupId = groupId;
      } else {
        this.$back('/distributor-management/distributor/list');
      }
    }).apply(this);
  },
  components: {
    DistributorDetail
  }
};
</script>
