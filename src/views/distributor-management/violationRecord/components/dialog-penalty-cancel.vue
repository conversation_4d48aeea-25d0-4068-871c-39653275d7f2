<!-- 撤销处罚弹框 -->
<template>
  <el-dialog :title="title" :visible.sync="dialogVisible" width="50%" :close-on-click-modal="false" :before-close="() => (dialogVisible = false)" top="10vh" class="dialog-penalty-submit" @close="() => (dialogVisible = false)">
    <div class="sub-title">请确认分销商控价违规申诉成功并上传对应的凭证</div>
    <el-form ref="submitForm" :model="form" label-width="100px" class="dialog-penalty-submit-form">
      <el-form-item label="撤销说明">
        <el-input v-model="form.revokeRemarks" placeholder="请输入撤销处罚说明" type="textarea" :rows="4" />
      </el-form-item>
      <el-form-item label="上传图片">
        <PictureCardCrypto accept=".doc,.docx,.pdf,.png,.jpeg,.jpg" :listType="'text'" :limit="5" :maxSize="5120 * 1024" v-model="form.revokeImageList" :izDecryption="true"></PictureCardCrypto>
        <div class="tips">最多可上传5张，单张不超过5M</div>
      </el-form-item>
    </el-form>
    <div class="dialog-footer">
      <el-button type="primary" @click="onSubmit">提交</el-button>
      <el-button @click="dialogVisible = false">取消</el-button>
    </div>
  </el-dialog>
</template>
   
<script>
import PictureCardCrypto from '@/components/Upload/PictureCardCrypto/index.vue';
export default {
  components: {
    PictureCardCrypto
  },
  props: {
    dialogPenaltyCancelVisible: Boolean
  },
  computed: {
    dialogVisible: {
      get() {
        return this.dialogPenaltyCancelVisible;
      },
      set(val) {
        this.$emit('update:dialogPenaltyCancelVisible', val);
      }
    }
  },
  data() {
    return {
      title: '撤销处罚(任选其一必填)',
      form: {
        revokeRemarks: '',
        revokeImageList: []
      }
    };
  },
  methods: {
    onSubmit() {
      if (this.form.revokeRemarks.trim() === '' && this.form.revokeImageList.length < 1) {
        this.$message.error('撤销处罚(任选其一必填)');
        return;
      }
      const postData = {
        ...this.form,
        revokeImageList: this.form.revokeImageList.length > 0 ? this.form.revokeImageList.map((item) => item.url) : []
      };
      this.$emit('onSubmit', postData);
      this.dialogVisible = false;
    }
  }
};
</script>
   
<style lang="scss" scoped>
.sub-title {
  font-size: 12px;
  font-weight: 500;
  margin-bottom: 10px;
}
.tips {
  font-size: 12px;
  color: #999;
}
</style>