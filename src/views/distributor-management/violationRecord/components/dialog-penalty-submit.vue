<!-- 提交处罚弹框 -->
 <template>
  <div>
    <el-dialog :title="title" :visible.sync="dialogVisible" width="50%" :close-on-click-modal="false" :before-close="() => (dialogVisible = false)" top="10vh" class="dialog-penalty-submit" @close="() => (dialogVisible = false)">
      <el-form ref="submitForm" :model="form" :rules="rules" label-width="100px" class="dialog-penalty-submit-form">
        <el-form-item label="处罚说明" prop="revokeRemarks">
          <el-input v-model="form.revokeRemarks" placeholder="请输入处罚说明" type="textarea" :rows="4" />
        </el-form-item>
        <el-form-item label="上传图片" prop="revokeImageList">
          <PictureCardCrypto accept=".doc,.docx,.pdf,.png,.jpeg,.jpg" :listType="'text'" :limit="5" :maxSize="5120 * 1024" v-model="form.revokeImageList" :izDecryption="true"></PictureCardCrypto>
          <div class="tips">最多可上传5张，单张不超过5M</div>
        </el-form-item>
      </el-form>
      <div class="dialog-footer">
        <el-button type="primary" @click="onSubmit">提交</el-button>
        <el-button @click="dialogVisible = false">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
 
 <script>
import PictureCardCrypto from '@/components/Upload/PictureCardCrypto/index.vue';
export default {
  components: {
    PictureCardCrypto
  },
  props: {
    dialogPenaltySubmitVisible: Boolean
  },
  computed: {
    dialogVisible: {
      get() {
        return this.dialogPenaltySubmitVisible;
      },
      set(val) {
        this.$emit('update:dialogPenaltySubmitVisible', val);
      }
    }
  },
  data() {
    return {
      title: '提交处罚说明',
      form: {
        revokeRemarks: '',
        revokeImageList: []
      },
      rules: {
        revokeImageList: [
          { required: true, message: '请选择上传图片', trigger: 'blur' },
          { type: 'array', min: 1, max: 5, message: '需上传 1 - 5 张图片', trigger: 'blur' }
        ]
      }
    };
  },
  methods: {
    onSubmit() {
      this.$refs.submitForm.validate((valid) => {
        if (valid) {
          const postData = { ...this.form, revokeImageList: this.form.revokeImageList.map((item) => item.url) };
          this.$emit('onSubmit', postData);
          this.dialogVisible = false;
        }
      });
    }
  }
};
</script>
 
 <style>
</style>