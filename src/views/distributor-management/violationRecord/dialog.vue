<template>
  <el-dialog :title="title" :visible.sync="dialogVisible" width="700px" append-to-body>
    <div class="jump-distributor"><sy-button type="text" code="distributor-management-edit" :call="jumpDistributorEdit" text="新增白名单店铺"></sy-button></div>
    <submit-form :options="recordDialogOptions" @onCancel="dialogVisible = false" label-width="100px" @onSubmit="onSubmit" ref="submitForm" :disabled="type === 'view'">
      <!-- 手机号 -->
      <template v-slot:formItem_mobile="{ form }">
        <CryptoBlock style="width: 351px" tag-type="input" :bizId="form.distributorId" detail-auth="/distributor-management/:decrypt-mobile" bizType="DISTRIBUTOR_MOBILE" v-model.trim="form.mobile" disabled />
      </template>
      <!-- 违规商品 -->
      <template v-slot:formItem_commoditySimpleVOList="{ form }">
        <AddCommodity source="violationRecord" :multipleSelectionData="goodsList" editable :brandId="brandId" @save="commodityChange" />
      </template>
      <template v-slot:formItem_footerTips>
        <div v-if="statisticsData.length" class="statistics-data">
          <p>品牌历史处罚情况（剔除申诉成功）：</p>
          <span v-for="(item, index) in statisticsData" :key="index">{{ item.illegalTypeName }}{{ item.illegalNum }}次</span>
        </div>
      </template>
    </submit-form>
  </el-dialog>
</template>

<script>
import AddCommodity from '@/components/AddCommodityMultiplex';
import SubmitForm from '@/components/Form/SubmitForm';
import { RecordDialogOptions } from './config';
import moment from 'moment';
import dict from '@/components/Common/dicts';
import { violateRecordUpdate, violateRecordCreate, violateRecordGet, violateRecordIllegaStatistics } from '@/api/distributorManagement/violationRecord/index.js';

export default {
  name: 'violationRecordDialog',
  components: { AddCommodity, SubmitForm },
  data() {
    return {
      type: '',
      dialogVisible: false,
      loading: false,
      goodsList: [],
      statisticsData: [],
      brandId: ''
    };
  },
  computed: {
    title() {
      const types = {
        add: '添加',
        edit: '编辑',
        view: '查看'
      };
      return (types[this.type] ?? '') + '违规记录';
    },
    recordDialogOptions() {
      return RecordDialogOptions;
    }
  },
  methods: {
    // 跳转到分销商编辑
    jumpDistributorEdit() {
      const { distributorId = '' } = this.$refs.submitForm.form || {};
      if (!distributorId) return;
      const routeUrl = this.$router.resolve({
        path: '/white-list/list',
        query: { distributorId }
      });
      window.open(routeUrl.href, '_blank');
    },
    commodityChange(list) {
      this.goodsList = list;
      const formatList = list.map(({ id, barcode, commodityCode, commodityName }) => ({
        id,
        barcode,
        commodityCode,
        commodityName
      }));
      this.$refs.submitForm.setFormItemVal('commoditySimpleVOList', formatList);
    },
    async openDialog(type, data) {
      this.type = type;
      this.dialogVisible = true;
      let params;
      if (type === 'add') {
        const { id: distributorId, applyMobile: mobile } = data;
        params = { distributorId, mobile, violateDate: moment().startOf('day').valueOf() };
        this.goodsList = [];
        this.brandId = '';
      } else {
        this.loading = true;
        const res = await violateRecordGet({ id: data.id }).finally(() => {
          this.loading = false;
        });
        params = res.data || {};
        // 初始化违规商品
        params.commoditySimpleVOList.map(({ id, barcode, commodityCode, commodityName }) => ({
          id,
          barcode,
          commodityCode,
          commodityName
        }));
        this.goodsList = params.commoditySimpleVOList || [];
        this.brandId = params.brandId;
        console.log('params', params);
      }
      this.recordDialogOptions.find((i) => i.prop === 'channel').options = dict('DISTRIBUTOR_WEBSITEWHIT_CHANNEL', {
        params: {
          distributorId: params.distributorId,
          status: 'PASS'
        }
      });
      if (!this.$refs.submitForm) await this.$nextTick();
      params.formType = this.type;
      this.$refs.submitForm.setData(params);
      this.getStatisticsData(params);
    },
    // 设置品牌id
    setBrandId(val = '') {
      this.brandId = val;
    },
    // 重置当前分销商违规统计数据
    resetStatisticsData() {
      this.statisticsData = [];
    },
    // 获取当前分销商违规统计数据
    getStatisticsData() {
      const { distributorId, websiteWhitelistId } = this.$refs.submitForm.getFormData();
      if (!distributorId || !websiteWhitelistId) return;
      const data = { distributorId, websiteWhitelistId };
      violateRecordIllegaStatistics(data).then((res) => {
        this.statisticsData = res.data || [];
      });
    },
    onSubmit({ form, callback }) {
      const request = this.type === 'add' ? violateRecordCreate : violateRecordUpdate;
      request(form)
        .then(() => {
          this.$emit('onSubmit');
          this.dialogVisible = false;
          this.$message.success('操作成功');
        })
        .finally(callback);
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep {
  .el-input,
  .el-textarea,
  .el-select {
    width: 320px !important;
  }
}
.jump-distributor {
  position: absolute;
  right: 165px;
  top: 125px;
  z-index: 1;
}
.statistics-data {
  margin-left: -66px;
  span {
    &:not(:last-child) {
      margin-right: 4px;
    }
  }
}
</style>
