import Vue from 'vue';
import { parseTime } from '@/utils';
import dict from '@/components/Common/dicts';
import { validateMoney, validateUrl } from '@/common/validator/index.js';

// 权限配置
export const Authority = {
  view: '/distributor-management/violationRecord/list:view',
  edit: '/distributor-management/violationRecord/list:edit'
};

export const FilterFormOptions = [
  {
    prop: 'mobile',
    component: 'input',
    label: '注册账号'
  },
  {
    prop: 'platformShopName',
    component: 'input',
    label: '违规第三方店铺名称'
  },
  {
    prop: 'commodityName',
    component: 'input',
    label: '违规商品'
  },
  {
    prop: 'channel',
    component: 'select',
    options: Vue.prototype.$dict['soyoungzg_channel'],
    label: '平台名称'
  },
  {
    prop: 'daterangeTime',
    label: '违规时间',
    component: 'dateRange',
    type: 'daterange',
    props: ['violateBeginDate', 'violateEndDate']
  }
];

export const BarOptions = [
  {
    id: 'addRecord',
    label: '新增违规记录',
    authority: Authority.edit
  }
];

export const TableOptions = [
  {
    prop: 'mobile',
    label: '注册账号'
  },
  {
    prop: 'channelName',
    label: '平台名称'
  },
  {
    prop: 'platformShopName',
    label: '违规店铺',
    isCustom: true
  },
  {
    prop: 'violateDate',
    label: '违规时间',
    formatter: (r, c, v) => (v ? parseTime(v, '{y}-{m}-{d}') : '--')
  },
  {
    prop: 'commodityName',
    label: '违规商品名称',
    isCustom: true
  },
  {
    prop: 'guidePrice',
    label: '控价价格（元/件）'
  },
  {
    prop: 'salePrice',
    label: '违规价格（元/件）'
  },
  {
    prop: 'isFreeze',
    label: '是否冻结账号',
    formatter: (r, c, v) => (v === '0' ? '否' : '是')
  },
  {
    prop: 'isFine',
    label: '是否罚款',
    formatter: (r, c, v) => (v === '0' ? '否' : '是')
  },
  {
    prop: 'violateNumber',
    label: '违规次数'
  }
];

export const TableBarOptions = [
  {
    id: 'editRecord',
    label: '编辑',
    authority: Authority.edit,
    isWrap: true,
    button: {
      type: 'text'
    }
  },
  {
    id: 'viewRecord',
    label: '查看',
    authority: Authority.view,
    isWrap: true,
    button: {
      type: 'text'
    }
  },
  {
    id: 'deleteRecord',
    label: '删除',
    authority: Authority.edit,
    isWrap: true,
    button: {
      type: 'text'
    }
  }
];

const shopOptions = [];
export const RecordDialogOptions = [
  {
    label: '注册账号',
    prop: 'mobile'
  },
  {
    prop: 'channel',
    label: '平台名称',
    component: 'select',
    async change(value) {
      const options = value
        ? await dict('DISTRIBUTOR_WEBSITEWHITE_SHOP', {
            params: {
              distributorId: this.form.distributorId,
              platform: value,
              status: 'PASS'
            }
          })
        : [];

      shopOptions.splice(0, shopOptions.length, ...options);
      if (!options.find((i) => i.value === this.form.websiteWhitelistId)) {
        this.setFormItemVal('websiteWhitelistId', '');
        this.setFormItemVal('platformShopName', '');
        const ref = this.$parent.$parent;
        ref && ref.resetStatisticsData();
      }
    },
    options: [],
    placeholder: '请选择',
    rules: [{ required: true, message: '请选择对应渠道', trigger: 'change' }]
  },
  {
    component: 'select',
    prop: 'websiteWhitelistId',
    label: '违规店铺名称',
    change(value) {
      const ref = this.$parent.$parent;
      if (value) {
        const item = shopOptions.find((i) => i.value === value);
        item && this.setFormItemVal('platformShopName', item.label);
        ref && ref.getStatisticsData();
      } else {
        this.setFormItemVal('platformShopName', '');
        ref && ref.resetStatisticsData();
      }
    },
    options: shopOptions,
    placeholder: '需先选择平台名称',
    rules: [{ required: true, message: '请选择违规店铺名称', trigger: 'change' }]
  },
  {
    component: 'input',
    prop: 'websiteCommodityUrl',
    label: '违规商品链接',
    type: 'textarea',
    autosize: { minRows: 3, maxRows: 6 },
    placeholder: '请输入违规商品链接',
    rules: [
      {
        trigger: 'blur',
        validator: validateUrl,
        message: '输入的值格式不对'
      }
    ]
  },
  {
    prop: 'brandId',
    label: '违规品牌',
    component: 'select',
    options: dict('BRAND_LIST_ALL'),
    rules: [{ required: true, message: '请选择违规品牌', trigger: 'change' }],
    change(value) {
      const ref = this.$parent.$parent;
      ref && ref.setBrandId(value);
    }
  },
  {
    prop: 'attachment',
    label: '违规截图凭证',
    component: 'upload',
    limit: 1,
    accept: '.jpg,.jpeg,.png',
    _size: { company: 'MB', size: 5 },
    _tip: '支持JPG、PNG格式，数量：1张，最大5MB',
    rules: [{ required: true, message: '请选择违规截图凭证', trigger: 'change' }]
  },
  {
    prop: 'commoditySimpleVOList',
    label: '违规商品',
    width: '220px',
    rules: [{ required: true, message: '请选择违规商品', trigger: 'change' }]
  },
  {
    component: 'input',
    type: 'number',
    prop: 'guidePrice',
    label: '控价价格',
    prepend: '￥',
    width: '220px',
    placeholder: '请输入控价价格',
    rules: [
      { required: true, message: '请输入控价价格', trigger: 'blur' },
      {
        trigger: 'blur',
        validator: validateMoney,
        message: '输入的值不能小于0且最多支持两位小数'
      }
    ]
  },
  {
    component: 'input',
    type: 'number',
    prop: 'salePrice',
    label: '破价价格',
    prepend: '￥',
    width: '220px',
    placeholder: '请输入破价价格',
    rules: [
      { required: true, message: '请输入破价价格', trigger: 'blur' },
      {
        trigger: 'blur',
        validator: validateMoney,
        message: '输入的值不能小于0且最多支持两位小数'
      }
    ]
  },
  {
    prop: 'violateDate',
    label: '违规时间',
    component: 'dateRange',
    type: 'date',
    clearable: false,
    pickerOptions: {
      disabledDate(date) {
        return date.getTime() > Date.now();
      }
    },
    placeholder: '请选择违规时间',
    rules: [{ required: true, message: '请选择违规时间', trigger: 'change' }]
  },
  {
    component: 'select',
    prop: 'illegalList',
    label: '处罚方式',
    multiple: true,
    clearable: true,
    collapseTags: true,
    options: window.$vue.$dict['violate_record_relation_illegal'],
    rules: [{ required: true, message: '请选择处罚方式', trigger: 'change' }]
  },
  {
    component: 'input',
    prop: 'operateRemarks',
    label: '处罚执行说明',
    type: 'textarea',
    autosize: { minRows: 3, maxRows: 6 },
    placeholder: '',
    visible: (data) => {
      return data.formType === 'view' && data.executeStatus === '1';
    }
  },
  {
    prop: 'operateImageList',
    label: '处罚执行图片',
    component: 'upload',
    limit: 5,
    accept: '.jpg,.jpeg,.png',
    _size: { company: 'MB', size: 5 },
    visible: (data) => {
      return data.formType === 'view' && data.executeStatus === '1';
    }
  },
  {
    component: 'input',
    prop: 'operateRemarks',
    label: '撤销处罚执说明',
    type: 'textarea',
    autosize: { minRows: 3, maxRows: 6 },
    placeholder: '',
    visible: (data) => {
      return data.formType === 'view' && data.izComplaints === '1';
    }
  },
  {
    prop: 'operateImageList',
    label: '撤销处罚图片',
    component: 'upload',
    limit: 5,
    accept: '.jpg,.jpeg,.png',
    _size: { company: 'MB', size: 5 },
    _tip: '支持JPG、PNG格式，数量：1张，最大5MB',
    visible: (data) => {
      return data.formType === 'view' && data.izComplaints === '1';
    }
  },
  {
    label: '',
    prop: 'footerTips'
  }
];
