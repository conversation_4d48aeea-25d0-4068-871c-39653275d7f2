<template>
  <div class="page-container">
    <sy-normal-table v-bind="table" ref="table" v-el-horizontal-scroll />
    <selectData type="distributor" ref="select_distributor" :defaultParams="{ status: 'PASS' }" @onCommit="selectDistributor"></selectData>
    <violationRecordDialog ref="violationRecordDialog" @onSubmit="refresh"></violationRecordDialog>
    <!-- 撤销处罚弹款 -->
    <dialogPenaltyCancel ref="dialogPenaltyCancel" @onSubmit="doRecall" :dialogPenaltyCancelVisible.sync="dialogPenaltyCancelVisible" v-if="dialogPenaltyCancelVisible" />
    <!-- 提交处罚弹框 -->
    <dialogPenaltySubmit ref="dialogPenaltySubmit" @onSubmit="doSubmit" :dialogPenaltySubmitVisible.sync="dialogPenaltySubmitVisible" v-if="dialogPenaltySubmitVisible" />
  </div>
</template>

<script>
import SelectData from '@/components/SelectData';
import violationRecordDialog from './dialog.vue';
import { violateRecordExportExcel, violateRecordList, violateRecordDelete, violateRecordRevokeIllega, violateRecordSubmitVouchers } from '@/api/distributorManagement/violationRecord/index.js';
import { customerList } from '@/api/common';
import { organization_tree } from '@/api/setting/divide-group';
import dict from '@/components/Common/dicts';
import download from '@/utils/download';
import { parseTime } from '@/utils';
import DialogPenaltyCancel from './components/dialog-penalty-cancel';
import DialogPenaltySubmit from './components/dialog-penalty-submit';

export default {
  name: 'distributor-management-violationRecord-list',
  components: {
    violationRecordDialog,
    SelectData,
    DialogPenaltyCancel,
    DialogPenaltySubmit
  },
  data() {
    return {
      csGroupOptions: [],
      exportLoading: false,
      dialogPenaltyCancelVisible: false,
      dialogPenaltySubmitVisible: false,
      revokeIllegaId: '', // 删除处罚的ID
      submitIllegaId: '' // 删除处罚的ID
    };
  },
  computed: {
    table() {
      const that = this;
      return {
        initSearch: false,
        filters: [
          {
            tag: 'el-input',
            prop: 'distributorId',
            label: '分销商ID',
            bind: {
              placeholder: '请输入'
            }
          },
          {
            tag: 'el-input',
            prop: 'platformShopName',
            label: '违规店铺名称',
            bind: {
              placeholder: '请输入'
            }
          },
          {
            tag: 'el-input',
            prop: 'barcode',
            label: '条形码',
            bind: {
              placeholder: '请输入'
            }
          },
          {
            tag: 'sy-select',
            prop: 'brandIds',
            label: '违规品牌',
            bind: {
              filterable: true,
              multiple: true,
              placeholder: '可多选',
              options: async () => await dict('BRAND_LIST_ALL'),
              flashOptions: true
            }
          },
          {
            tag: 'sy-select',
            prop: 'csIds',
            label: '专属顾问',
            bind: {
              placeholder: '请选择专属顾问',
              filterable: true,
              multiple: true,
              clearable: true,
              showEye: true,
              options: async () => {
                const res = await customerList();
                return res.data;
              },
              flashOptions: true,
              optionsProps: {
                label: 'name',
                value: 'id'
              }
            }
          },
          {
            tag: 'el-cascader',
            prop: 'csOrganizationIds',
            label: '专属顾问分组',
            bind: {
              placeholder: '可多选',
              options: that.csGroupOptions,
              flashOptions: true,
              collapseTags: true,
              props: {
                emitPath: false,
                multiple: true,
                expandTrigger: 'hover',
                value: 'id',
                label: 'name'
              }
            }
          },
          {
            tag: 'sy-date-picker',
            prop: ['violateBeginDate', 'violateEndDate'],
            label: '违规时间',
            bind: {
              bind: {
                'start-placeholder': '开始时间',
                'end-placeholder': '结束时间',
                type: 'daterange',
                valueFormat: 'timestamp',
                'default-time': ['00:00:00', '23:59:59']
              }
            }
          },
          {
            label: '处罚方式',
            tag: 'sy-select',
            prop: 'illegalList',
            bind: {
              placeholder: '请选择处罚方式',
              multiple: true,
              filterable: true,
              options: window.$vue.$dict['violate_record_relation_illegal'],
              flashOptions: true
            }
          }
        ],
        btns: [
          {
            text: '新增违规记录',
            type: 'primary',
            code: '/distributor-management/violationRecord/list:edit',
            call: () => that.addRecord()
          },
          {
            text: '导出',
            type: 'primary',
            code: '/distributor-management/violationRecord/list:export',
            confirm: '确认导出数据？',
            bind: {
              loading: that.exportLoading
            },
            call: ({ filtersValue }) => that.onExport(filtersValue)
          }
        ],
        columns() {
          return [
            {
              label: '分销商信息',
              width: 220,
              itemBind: {
                fixed: 'left'
              },
              render: (h, { row }) => (
                <ul>
                  <li>分销商ID：{row.distributorId}</li>
                  <li>店铺名称：{row.shopName}</li>
                  <li>登录手机号：{row.mobile}</li>
                </ul>
              )
            },
            {
              label: '专属顾问',
              width: 150,
              render: (h, { row }) => (
                <ul>
                  <li>{row.csName}</li>
                  <li>{row.organizationName}</li>
                </ul>
              )
            },
            {
              prop: 'channelName',
              label: '平台名称',
              width: 120
            },
            {
              prop: 'platformShopName',
              label: '违规店铺',
              type: 'btns',
              btns({ row }) {
                return [
                  {
                    text: row.platformShopName,
                    type: 'text',
                    code: '/distributor-management/:detail',
                    call: () =>
                      that.$router.push({
                        path: `/distributor-management/distributor/detail/${row.distributorId}`,
                        query: { activeName: 'whiteList' }
                      })
                  }
                ];
              }
            },
            {
              prop: 'violateDate',
              label: '违规时间',
              type: 'time',
              width: 90,
              format: 'yyyy-MM-dd'
            },
            {
              prop: 'websiteCommodityUrl',
              label: '违规商品',
              type: 'render',
              itemBind: {
                minWidth: 200
              },
              render: (h, { row }) => (
                <div v-frag>
                  {row.commoditySimpleVOList && row.commoditySimpleVOList.length
                    ? row.commoditySimpleVOList.map((item) =>
                        row.websiteCommodityUrl ? (
                          <p>
                            <a href={row.websiteCommodityUrl} target="_blank">
                              {item.commodityName}
                            </a>
                          </p>
                        ) : (
                          <p>{item.commodityName}</p>
                        )
                      )
                    : '-'}
                </div>
              )
            },
            {
              prop: 'brandName',
              label: '违规品牌',
              width: 120
            },
            {
              prop: 'guidePrice',
              label: '控价价格'
            },
            {
              prop: 'salePrice',
              label: '违规价格'
            },
            {
              prop: 'illegalList',
              label: '处罚方式',
              type: 'array'
            },
            {
              prop: 'izComplaints',
              label: '申诉状态',
              type: 'map',
              map: [
                { label: '未申诉', value: '0' },
                { label: '申诉成功', value: '1' }
              ]
            },
            {
              prop: 'executeStatusName',
              label: '处罚执行状态'
            },
            {
              prop: 'createInfo',
              label: '创建信息',
              width: 90,
              render: (h, { row }) => (
                <ul>
                  <li>{row.createByName}</li>
                  <li>{parseTime(row.createDate, '{y}-{m}-{d} {h}:{i}')}</li>
                </ul>
              )
            },
            {
              label: '操作',
              type: 'btns',
              width: 80,
              btns({ row }) {
                return [
                  {
                    text: '查看',
                    type: 'text',
                    code: '/distributor-management/violationRecord/list:view',
                    call: () => that.viewRecord(row)
                  },
                  {
                    hide: row.status === 'REJECT',
                    text: '编辑',
                    type: 'text',
                    code: '/distributor-management/violationRecord/list:edit',
                    call: () => that.editRecord(row)
                  },
                  {
                    hide: !(row.izComplaints === '0' && row.executeStatus === '0'),
                    text: '删除',
                    type: 'text',
                    code: 'distributor-management-violationRecord-list-del',
                    confirm: '请确认分销商控价违规申诉成功，对应违规记录的处罚都被撤销。',
                    call: () => that.delRecord(row)
                  },
                  {
                    hide: !(row.izComplaints === '0' && row.executeStatus === '0'),
                    text: '提交处罚凭证',
                    type: 'text',
                    code: 'distributor-management-violationRecord-list-submit',
                    call: () => that.submitVouchers(row)
                  },
                  {
                    hide: row.status === 'REJECT' || row.executeStatus === '1',
                    text: '撤销处罚',
                    type: 'text',
                    code: '/distributor-management/violationRecord/list:cancel',
                    call: () => that.withdrawAppeal(row)
                  }
                ];
              }
            }
          ];
        },
        async search({ filtersValue, pageFilter }) {
          const res = await violateRecordList({
            data: { ...filtersValue },
            ...pageFilter
          });
          return {
            list: res.data.list || [],
            total: res.data.total || 0
          };
        }
      };
    }
  },
  mounted() {
    this.getOrganization();
  },
  activated() {
    this.refresh();
  },
  methods: {
    // 专属顾问分组
    getOrganization() {
      organization_tree({}).then((res) => {
        this.csGroupOptions = res?.data ?? [];
      });
    },
    // 刷新表格
    refresh() {
      const ref = this.$refs.table;
      ref && ref.handlerSearch();
    },
    // 跟进提交
    dialogCallback() {
      this.$refs.cte.onSubmit();
    },
    // 取消选择弹框
    onCancelCheck() {
      this.$refs.cte.$refs.dialog.setVisible(false);
    },
    // 新增违规记录
    addRecord() {
      this.$refs.select_distributor.open();
    },
    // 选择违规分销商
    async selectDistributor([row]) {
      this.$refs.violationRecordDialog.openDialog('add', row);
    },
    // 编辑违规记录
    editRecord(row) {
      this.$refs.violationRecordDialog.openDialog('edit', row);
    },
    // 查看违规记录
    viewRecord(row) {
      this.$refs.violationRecordDialog.openDialog('view', row);
    },
    // 删除违规记录
    delRecord({ id }) {
      violateRecordDelete({ id }).then((res) => {
        if (res.code === '0') {
          this.$message.success('操作成功！');
          this.refresh();
        }
      });
    },
    // 撤销处罚
    withdrawAppeal({ id }) {
      this.dialogPenaltyCancelVisible = true;
      this.revokeIllegaId = id;
    },
    // 撤销处罚提交
    doRecall(form) {
      violateRecordRevokeIllega({ ...form, id: this.revokeIllegaId }).then((res) => {
        if (res.code === '0') {
          this.$message.success('操作成功！');
          this.refresh();
        }
      });
    },
    // 提交处罚凭证
    submitVouchers({ id }) {
      this.dialogPenaltySubmitVisible = true;
      this.submitIllegaId = id;
    },
    doSubmit(form) {
      violateRecordSubmitVouchers({ ...form, id: this.submitIllegaId }).then((res) => {
        if (res.code === '0') {
          this.$message.success('操作成功！');
          this.refresh();
        }
      });
    },
    // 导出
    onExport(data) {
      this.exportLoading = true;
      const params = {
        ...data
      };
      violateRecordExportExcel(params)
        .then((res) => {
          download(res, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', `违规记录-${parseTime(Date.now(), '{y}-{m}-{d}-{h}:{i}:{s}')}.xlsx`);
        })
        .finally(() => {
          this.exportLoading = false;
        });
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep {
  a {
    color: var(--color-primary);
  }
  .columns-platformShopName,
  .columns-commodityName {
    .el-button {
      white-space: normal;
      word-wrap: break-word;
      line-height: 150%;
      text-align: left;
    }
  }
  .columns-operation {
    .cell {
      display: flex;
      flex-direction: column;
      .el-button {
        margin: 5px 0;
        text-align: left;
      }
    }
  }
}
</style>
