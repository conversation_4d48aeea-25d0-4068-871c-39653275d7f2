<template>
  <div class="app-container">
    <div class="table-container">
      <Instructions :showTitle="true" :colNum="3">
        <template slot="title">
          <p>店铺名称:{{ sumData.shopName }}</p>
          <p>登录手机号:{{ sumData.mobile }}</p>
          <p>返利使用金额：￥{{ sumData.amount }}</p>
        </template>
        <template slot="col1">
          <div class="content__row">
            <p class="content__label">专属顾问：</p>
            <p>{{ sumData.customerServiceName }}</p>
          </div>
        </template>
        <template slot="col2">
          <div class="content__row">
            <p class="content__label">统计时间：</p>
            <p>{{ timeObj ? timeObj : '全部' }}</p>
          </div>
        </template>
        <template slot="col3"> <div class="content__row"></div></template>
      </Instructions>
      <form @submit.prevent="onSearch" class="commo-search-container commo-search-flex commo-search-margin-bottom10">
        <div class="commo-search-item">
          <span class="commo-search-item-label">使用类型:</span>
          <div class="commo-search-item-content">
            <el-select v-model="initFilter.accountUseType" filterable placeholder="请选择" @change="accountUseTypeChange">
              <el-option v-for="item in _dict_REBATE_USE_TYPE" :key="item.value" :label="item.label" :value="item.value"> </el-option>
            </el-select>
          </div>
        </div>
        <div class="commo-search-item">
          <span class="commo-search-item-label">订单号:</span>
          <div class="commo-search-item-content">
            <el-input clearable v-model="initFilter.orderNo" :disabled="initFilter.accountUseType === 'CREDIT_BACK_EXPIRATION'"></el-input>
          </div>
        </div>
        <div class="commo-search-btns">
          <el-button native-type="submit" size="small" type="primary">查询</el-button>
          <el-button @click="onReset" size="small">重置</el-button>
          <Authority auth="/brand-rebate/rebate-data/:export">
            <el-button :loading="exportLoading" @click="onExport" size="small">导出</el-button>
          </Authority>
        </div>
      </form>
      <el-table :data="list" element-loading-text="加载中" fit highlight-current-row ref="multipleTable" v-loading.body="listLoading">
        <el-table-column align="center" label="使用类型" prop="accountUseTypeName"> </el-table-column>
        <el-table-column align="center" label="订单编号" prop="orderNo">
          <template slot-scope="scope">{{ scope.row.orderNo || '无' }} </template>
        </el-table-column>
        <el-table-column align="center" label="返利使用金额">
          <template slot-scope="scope"> ￥{{ Math.abs(scope.row.amount) }} </template>
        </el-table-column>
        <el-table-column align="center" label="使用时间">
          <template slot-scope="scope">
            {{ scope.row.usedDate | parseTime('{y}.{m}.{d} {h}:{i}:{s}') }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作" fixed="right">
          <template slot-scope="scope">
            <router-link :to="`/order/inquiry/detail/${scope.row.orderId}`" class="link">
              <Authority auth="/brand-rebate/rebate-data/:view">
                <el-button type="text">订单详情</el-button>
              </Authority>
            </router-link>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination :current-page="pageNo" :disabled="listLoading" :page-size="pageSize" :page-sizes="[10, 20, 30, 40, 50, 100]" :total="total" @current-change="handleCurrentChange" @size-change="handleSizeChange" background layout="total, sizes, prev, pager, next, jumper"></el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import { listDistributorUseDetail, exportDistributorUseDetail, getSumDistributorRebateData } from '@/api/brand-rebate/rebate-data';
import { customerList } from '@/api/distributorManagement/distributor/list';
import pick from 'lodash/pick';
import pickBy from 'lodash/pickBy';
import download from '@/utils/download';
import { parseTime } from '@/utils';
import cloneDeep from 'lodash/cloneDeep';
import dict from '@/components/Common/dicts';

export default {
  name: 'rebate-data-distributor_use_detail',
  data() {
    const filter = {
      distributorId: this.$route.params.distributorId,
      orderNo: '', // 订单号
      accountUseType: 'PURCHASE_SUB' // 使用类型
    };
    const sumData = {
      period: '',
      amount: 0
    };
    return {
      exportLoading: false,
      saveLoading: false,
      formLabelWidth: '80px',
      dialogFormVisible: false,
      formUserType: false,
      filter,
      initFilter: cloneDeep(filter),
      sumData,
      customerServices: [],
      list: [],
      listLoading: true,
      pageNo: 1,
      pageSize: 10,
      total: 0
    };
  },
  computed: {
    // 列表数据过滤
    data() {
      const obj = pickBy(this.initFilter, (val) => !!val);
      obj.startTime = this.dateTime.startTime;
      obj.endTime = this.dateTime.endTime;
      return obj;
    },
    timeObj() {
      if (this.dateTime?.startTime) {
        return `${parseTime(this.dateTime.startTime, '{y}-{m}-{d}')}至${parseTime(this.dateTime.endTime, '{y}-{m}-{d}')}`;
      } else {
        return null;
      }
    }
  },
  async created() {
    this._dict_REBATE_USE_TYPE = await dict('REBATE_USE_TYPE');
  },
  mounted() {
    this.dateTime = JSON.parse(sessionStorage.getItem('distributorDateTime'));
    if (!this.dateTime) {
      this.dateTime = {};
    }
    this.init();
    this.fetchSumData();
    this.fetchCustomerList();
  },
  methods: {
    init() {
      this.fetchData();
    },
    // 获取专属顾问列表
    fetchCustomerList() {
      const listQuery = {};
      customerList(listQuery).then((response) => {
        this.customerServices = response.data;
      });
    },
    fetchSumData() {
      const queryDate = {
        distributorId: this.initFilter.distributorId,
        startTime: this.dateTime.startTime,
        endTime: this.dateTime.endTime,
        queryType: 'USED'
      };
      getSumDistributorRebateData(queryDate).then((response) => {
        if (response.data) {
          this.sumData = response.data;
        }
      });
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.pageNo = 1;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.pageNo = val;
      this.fetchData();
    },
    // 获取列表数据
    fetchData() {
      this.listLoading = true;
      const listQuery = pick(this, ['pageNo', 'pageSize', 'data']);
      listDistributorUseDetail(listQuery)
        .then((response) => {
          const { list = [], total = 0 } = response.data || {};
          this.list = list;
          this.total = total;
        })
        .finally(() => {
          this.listLoading = false;
        });
    },
    // 查询
    onSearch() {
      this.pageNo = 1;
      this.pageSize = 10;
      this.fetchData();
    },
    // 重置
    onReset() {
      this.pageNo = 1;
      this.pageSize = 10;
      this.initFilter = cloneDeep(this.filter);
      this.fetchData();
    },
    onExport() {
      this.exportLoading = true;
      exportDistributorUseDetail(this.data)
        .then((res) => {
          download(res, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', `分销商返利使用明细-${parseTime(Date.now(), '{y}-{m}-{d}-{h}:{i}:{s}')}.xlsx`);
        })
        .finally(() => {
          this.exportLoading = false;
        });
    },
    accountUseTypeChange(v) {
      if (v === 'CREDIT_BACK_EXPIRATION') {
        this.initFilter.orderNo = '';
      }
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.table-container-top {
  margin-bottom: 10px;
}

.filter-list .filter-item {
  min-width: 25%;
  .el-select {
    width: 100%;
  }
}
</style>
