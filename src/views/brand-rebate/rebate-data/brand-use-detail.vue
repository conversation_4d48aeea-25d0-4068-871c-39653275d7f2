<template>
  <div class="app-container">
    <div class="table-container">
      <Instructions :showTitle="true" :colNum="1">
        <template slot="title">
          <p>品牌名称:{{ sumData.brandName }}</p>
          <p>返利使用金额：￥{{ sumData.amount }}</p>
        </template>
        <template slot="col1">
          <div class="content__row">
            <p class="content__label">统计时间：</p>
            <p>{{ timeObj ? timeObj : '全部' }}</p>
          </div>
        </template>
      </Instructions>
      <form @submit.prevent="onSearch" class="commo-search-container commo-search-flex commo-search-margin-bottom10">
        <div class="commo-search-item">
          <span class="commo-search-item-label">店铺名称:</span>
          <div class="commo-search-item-content">
            <el-input clearable v-model.trim="initFilter.shopName"></el-input>
          </div>
        </div>
        <div class="commo-search-item">
          <span class="commo-search-item-label">登录手机号:</span>
          <div class="commo-search-item-content">
            <el-input clearable v-model.trim="initFilter.mobile"></el-input>
          </div>
        </div>
        <div class="commo-search-item">
          <span class="commo-search-item-label">专属顾问:</span>
          <div class="commo-search-item-content">
            <el-select v-model="initFilter.customerServiceId" placeholder="请选择" clearable class="customerServiceId">
              <el-option v-for="item in customerServices" :key="item.id" :label="item.name" :value="item.id"> </el-option>
            </el-select>
          </div>
        </div>
        <div class="commo-search-item">
          <span class="commo-search-item-label">订单号:</span>
          <div class="commo-search-item-content">
            <el-input clearable v-model.trim="initFilter.orderNo"></el-input>
          </div>
        </div>
        <div class="commo-search-btns">
          <el-button native-type="submit" size="small" type="primary">查询</el-button>
          <el-button @click="onReset" size="small">重置</el-button>
          <Authority auth="/brand-rebate/rebate-data/:export">
            <el-button :loading="exportLoading" @click="onExport" size="small">导出</el-button>
          </Authority>
        </div>
      </form>
      <el-table :data="list" element-loading-text="加载中" fit highlight-current-row ref="multipleTable" v-loading.body="listLoading">
        <el-table-column align="center" label="订单编号" prop="orderNo"> </el-table-column>
        <el-table-column align="center" label="活动返利使用金额">
          <template slot-scope="scope"> ￥{{ scope.row.amount }} </template>
        </el-table-column>
        <el-table-column align="center" label="使用时间">
          <template slot-scope="scope">
            {{ scope.row.usedDate | parseTime('{y}.{m}.{d} {h}:{i}:{s}') }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="使用店铺" prop="shopName"> </el-table-column>
        <el-table-column align="center" label="登录手机号" prop="mobile"> </el-table-column>

        <el-table-column align="center" label="专属顾问">
          <template slot-scope="scope">
            {{ scope.row.customerServiceName || '/' }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作" fixed="right">
          <template slot-scope="scope">
            <router-link :to="`/order/inquiry/detail/${scope.row.orderId}`" class="link">
              <Authority auth="/brand-rebate/rebate-data/:view">
                <el-button type="text">订单详情</el-button>
              </Authority>
            </router-link>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination :current-page="pageNo" :disabled="listLoading" :page-size="pageSize" :page-sizes="[10, 20, 30, 40, 50, 100]" :total="total" @current-change="handleCurrentChange" @size-change="handleSizeChange" background layout="total, sizes, prev, pager, next, jumper"></el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import { listBrandUseDetail, exportBrandUseDetail, getSumBrandRebateData } from '@/api/brand-rebate/rebate-data';
import { customerList } from '@/api/distributorManagement/distributor/list';
import pick from 'lodash/pick';
import pickBy from 'lodash/pickBy';
import download from '@/utils/download';
import { parseTime } from '@/utils';
import cloneDeep from 'lodash/cloneDeep';
export default {
  name: 'brand-use-detail',
  data() {
    const filter = {
      shopName: '', // 店铺名称
      mobile: '', // 登录手机号
      customerServiceId: '', // 专属顾问ID
      orderNo: '', // 订单号
      brandId: this.$route.params.brandId
    };
    const sumData = {
      brandName: '',
      amount: 0,
      customerServiceName: ''
    };
    return {
      exportLoading: false,
      saveLoading: false,
      formLabelWidth: '80px',
      dialogFormVisible: false,
      formUserType: false,
      filter,
      initFilter: cloneDeep(filter),
      sumData,
      brandList: [], // 品牌列表选项
      brandCategory: [],
      customerServices: [],
      list: [],
      listLoading: true,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      dateTime: {}
    };
  },
  computed: {
    // 列表数据过滤
    data() {
      const obj = pickBy(this.initFilter, (val) => !!val);
      this.dateTime.startTime ? (obj.startTime = this.dateTime.startTime) : '';
      this.dateTime.endTime ? (obj.endTime = this.dateTime.endTime) : '';
      return obj;
    },
    timeObj() {
      if (this.dateTime.startTime) {
        return `${parseTime(this.dateTime.startTime, '{y}-{m}-{d}')}至${parseTime(this.dateTime.endTime, '{y}-{m}-{d}')}`;
      } else {
        return null;
      }
    }
  },
  mounted() {
    this.dateTime = JSON.parse(sessionStorage.getItem('brandDateTime'));
    if (!this.dateTime) {
      this.dateTime = {};
    }
    this.init();
    this.fetchSumData();
    this.fetchCustomerList();
  },
  methods: {
    init() {
      this.fetchData();
    },
    // 获取专属顾问列表
    fetchCustomerList() {
      const listQuery = {};
      customerList(listQuery).then((response) => {
        this.customerServices = response.data;
      });
    },
    fetchSumData() {
      const queryDate = {
        queryType: 'USED',
        brandId: this.initFilter.brandId,
        startTime: this.dateTime.startTime,
        endTime: this.dateTime.endTime
      };
      getSumBrandRebateData(queryDate).then((response) => {
        if (response.data) {
          this.sumData = response.data;
        }
      });
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.pageNo = 1;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.pageNo = val;
      this.fetchData();
    },
    // 获取列表数据
    fetchData() {
      this.listLoading = true;
      const listQuery = pick(this, ['pageNo', 'pageSize', 'data']);
      listBrandUseDetail(listQuery)
        .then((response) => {
          const { list = [], total = 0 } = response.data || {};
          this.list = list;
          this.total = total;
        })
        .finally(() => {
          this.listLoading = false;
        });
    },
    // 查询
    onSearch() {
      this.pageNo = 1;
      this.pageSize = 10;
      this.fetchData();
    },
    // 重置
    onReset() {
      this.pageNo = 1;
      this.pageSize = 10;
      this.initFilter = cloneDeep(this.filter);
      this.fetchData();
    },
    onExport() {
      this.exportLoading = true;
      exportBrandUseDetail(this.data)
        .then((res) => {
          download(res, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', `品牌返利使用明细-${parseTime(Date.now(), '{y}-{m}-{d}-{h}:{i}:{s}')}.xlsx`);
        })
        .finally(() => {
          this.exportLoading = false;
        });
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.table-container-top {
  margin-bottom: 10px;
}

.filter-list .filter-item {
  min-width: 25%;
  .el-select {
    width: 100%;
  }
}
</style>
