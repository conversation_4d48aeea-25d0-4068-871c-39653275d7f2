<template>
  <div class="app-container">
    <div class="table-container">
      <Instructions :showTitle="true" :colNum="0">
        <template slot="title">
          <p>商品名称：{{ sumData.commodityName }} 商品ID：{{ sumData.commodityId }} 活动返利发放金额：￥{{ sumData.activityVirtualCredit }}</p>
        </template>
      </Instructions>
      <form @submit.prevent="onSearch" class="commo-search-container commo-search-flex commo-search-margin-bottom10">
        <div class="commo-search-item">
          <span class="commo-search-item-label">时间:</span>
          <div class="commo-search-item-content">
            <el-date-picker v-model="filter.period" type="month" placeholder="请选择日期"> </el-date-picker>
          </div>
        </div>
        <div class="commo-search-btns">
          <el-button native-type="submit" size="small" type="primary">查询</el-button>
          <el-button @click="onReset" size="small">重置</el-button>
          <Authority auth="/brand-rebate/rebate-data/:export">
            <el-button :loading="exportLoading" @click="onExport" size="small">导出</el-button>
          </Authority>
        </div>
      </form>
      <el-table :data="list" border element-loading-text="加载中" fit highlight-current-row ref="multipleTable" v-loading.body="listLoading">
        <el-table-column align="center" label="活动时间" prop="periodDesc"></el-table-column>
        <el-table-column align="center" label="活动返利发放金额" prop="activityVirtualCredit">
          <template slot-scope="scope">
            <p v-if="scope.row.activityVirtualCredit">¥{{ scope.row.activityVirtualCredit }}</p>
            <span v-else>¥0</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="发放分销商人数" prop="distributorNum">
          <template slot-scope="scope">
            <p class="link" v-if="scope.row.distributorNum" @click="jump(scope.row, 'distributorNum')">
              {{ scope.row.distributorNum }}
            </p>
            <span v-else>0</span>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination :current-page="pageNo" :disabled="listLoading" :page-size="pageSize" :page-sizes="[10, 20, 30, 40, 50, 100]" :total="total" @current-change="handleCurrentChange" @size-change="handleSizeChange" background layout="total, sizes, prev, pager, next, jumper"></el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import { listIssueByCommodity, exportIssueByCommodity, getIssueByCommoditySum } from '@/api/brand-rebate/rebate-data'; // 接口地址
import pickBy from 'lodash/pickBy'; // 返回一个新对象，值由真值组成
import pick from 'lodash/pick';
import cloneDeep from 'lodash/cloneDeep'; // 对象深拷贝
import download from '@/utils/download';
import { parseTime } from '@/utils';
export default {
  name: 'DistributorSingerDetail',
  data() {
    const initFilter = {
      period: ''
    };
    const sumData = {
      activityVirtualCredit: 0,
      commodityId: '',
      commodityName: ''
    };
    return {
      pageNo: 1,
      pageSize: 10,
      total: 0,
      id: this.$route.query.id, // 商品Id
      listLoading: true,
      sumData,
      initFilter, // 搜索条件初始值加入到Data
      filter: cloneDeep(initFilter), // 搜索条件的值
      list: [], // 列表
      exportLoading: false,
      statusOptions: [], // 订单状态
      statusOptionsLoading: false,
      brandOptions: [],
      brandCategoryOptions: []
    };
  },
  computed: {
    // 表单接口传入的参数
    data() {
      const obj = {};
      if (this.filter.period) {
        obj.period = parseTime(this.filter.period.getTime(), '{y}{m}');
      }
      obj.commodityId = this.$route.query.id;
      return pickBy(obj, (val) => !!val);
    }
  },
  mounted() {
    this.fetchData();
    this.getIssueByCommoditySum();
  },
  methods: {
    // 页面跳转
    jump(row, type) {
      let path = '';
      // 发放分销商人数
      if (type === 'distributorNum') {
        path = `/brand-rebate/rebate-data/distributor-item-detail?id=${this.sumData.commodityId}&time=${row.period}`;
      }
      this.$router.push({
        path
      });
    },
    // 重置
    onReset() {
      this.filter = cloneDeep(this.initFilter);
      this.fetchData();
    },
    // 搜索
    onSearch() {
      this.fetchData();
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.pageNo = 1;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.pageNo = val;
      this.fetchData();
    },
    // 获取列表信息
    fetchData() {
      this.listLoading = true;
      const listQuery = pick(this, ['pageNo', 'pageSize', 'data']);

      listIssueByCommodity(listQuery)
        .then((response) => {
          const { list = [], total = 0 } = response.data || {};
          this.list = list;
          this.total = total;
        })
        .finally(() => {
          this.listLoading = false;
        });
    },
    // 数据汇总
    getIssueByCommoditySum() {
      const listQuery = {
        month: this.filter.month,
        year: this.filter.year,
        commodityId: this.id
      };
      getIssueByCommoditySum(listQuery).then((res) => {
        if (res.data) {
          this.sumData = res.data;
        }
      });
    },
    onExport() {
      this.exportLoading = true;
      exportIssueByCommodity(this.data)
        .then((res) => {
          download(res, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', `活动返利发放明细-${parseTime(Date.now(), '{y}-{m}-{d}-{h}:{i}:{s}')}.xlsx`);
        })
        .finally(() => {
          this.exportLoading = false;
        });
    }
  }
};
</script>

<style lang="scss" scoped>
.link {
  cursor: pointer;
  color: var(--color-primary);
}
</style>
