<template>
  <div class="app-container">
    <div class="table-container">
      <Instructions :showTitle="true" :colNum="3">
        <template slot="title">
          <p>店铺名称:{{ sumData.shopName }}</p>
          <p>登录手机号:{{ sumData.mobile }}</p>
          <p>返利充值金额：￥{{ sumData.amount ? (sumData.amount > 0 ? '+' : '-') + Math.abs(sumData.amount) : 0 }}</p>
        </template>
        <template slot="col1">
          <div class="content__row">
            <p class="content__label">专属顾问：</p>
            <p>{{ sumData.customerServiceName }}</p>
          </div>
        </template>
        <template slot="col2">
          <div class="content__row">
            <p class="content__label">统计时间：</p>
            <p>{{ timeObj ? timeObj : '全部' }}</p>
          </div>
        </template>
        <template slot="col3"> <div class="content__row"></div></template>
      </Instructions>
      <form @submit.prevent="onSearch" class="commo-search-container commo-search-flex commo-search-margin-bottom10">
        <div class="commo-search-item">
          <span class="commo-search-item-label">单号:</span>
          <div class="commo-search-item-content">
            <el-input clearable v-model="initFilter.tradeNo"></el-input>
          </div>
        </div>
        <div class="commo-search-btns">
          <el-button native-type="submit" size="small" type="primary">查询</el-button>
          <el-button @click="onReset" size="small">重置</el-button>
          <Authority auth="/brand-rebate/rebate-data/:export">
            <el-button :loading="exportLoading" @click="onExport" size="small">导出</el-button>
          </Authority>
        </div>
      </form>
      <el-table :data="list" element-loading-text="加载中" fit highlight-current-row ref="multipleTable" v-loading.body="listLoading">
        <el-table-column align="center" label="单号" prop="tradeNo"> </el-table-column>
        <el-table-column align="center" label="充值金额">
          <template slot-scope="scope"> ￥{{ scope.row.rechargeVirtualCredit ? (scope.row.rechargeVirtualCredit > 0 ? '+' : '-') + Math.abs(scope.row.rechargeVirtualCredit) : 0 }} </template>
        </el-table-column>
        <el-table-column align="center" label="充值时间">
          <template slot-scope="scope">
            {{ scope.row.rechargeDate | parseTime('{y}.{m}.{d} {h}:{i}:{s}') }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="备注" prop="remark"></el-table-column>
        <el-table-column align="center" label="操作人" prop="updateByName"> </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination :current-page="pageNo" :disabled="listLoading" :page-size="pageSize" :page-sizes="[10, 20, 30, 40, 50, 100]" :total="total" @current-change="handleCurrentChange" @size-change="handleSizeChange" background layout="total, sizes, prev, pager, next, jumper"></el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import { listDistributorRechargeDetail, getSumDistributorRebateData, exportDistributorRechargeDetail } from '@/api/brand-rebate/rebate-data';
import pick from 'lodash/pick';
import pickBy from 'lodash/pickBy';
import download from '@/utils/download';
import { parseTime } from '@/utils';
import cloneDeep from 'lodash/cloneDeep';
export default {
  name: 'distributor-recharge-detail',
  data() {
    const filter = {
      distributorId: this.$route.params.distributorId,
      shopName: '', // 店铺名称
      mobile: '', // 登录手机号
      customerServiceId: '', // 专属顾问ID
      tradeNo: '' // 单号
    };
    const sumData = {
      period: '',
      amount: 0
    };
    return {
      exportLoading: false,
      saveLoading: false,
      formLabelWidth: '80px',
      dialogFormVisible: false,
      formUserType: false,
      filter,
      initFilter: cloneDeep(filter),
      sumData,
      list: [],
      listLoading: true,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      dateTime: {}
    };
  },
  computed: {
    // 列表数据过滤
    data() {
      const obj = pickBy(this.initFilter, (val) => !!val);
      obj.startTime = this.dateTime.startTime;
      obj.endTime = this.dateTime.endTime;
      return obj;
    },
    timeObj() {
      if (this.dateTime.startTime) {
        return `${parseTime(this.dateTime.startTime, '{y}-{m}-{d}')}至${parseTime(this.dateTime.endTime, '{y}-{m}-{d}')}`;
      } else {
        return null;
      }
    }
  },
  mounted() {
    this.dateTime = JSON.parse(sessionStorage.getItem('distributorDateTime'));
    if (!this.dateTime) {
      this.dateTime = {};
    }
    this.init();
    this.fetchSumData();
  },
  methods: {
    init() {
      this.fetchData();
    },
    fetchSumData() {
      const queryDate = {
        distributorId: this.initFilter.distributorId,
        startTime: this.dateTime.startTime,
        endTime: this.dateTime.endTime,
        queryType: 'RECHARGE'
      };
      getSumDistributorRebateData(queryDate).then((response) => {
        if (response.data) {
          this.sumData = response.data;
        }
      });
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.pageNo = 1;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.pageNo = val;
      this.fetchData();
    },
    // 获取列表数据
    fetchData() {
      this.listLoading = true;
      const listQuery = pick(this, ['pageNo', 'pageSize', 'data']);
      listDistributorRechargeDetail(listQuery)
        .then((response) => {
          const { list = [], total = 0 } = response.data || {};
          this.list = list;
          this.total = total;
        })
        .finally(() => {
          this.listLoading = false;
        });
    },
    // 查询
    onSearch() {
      this.pageNo = 1;
      this.pageSize = 10;
      this.fetchData();
    },
    // 重置
    onReset() {
      this.pageNo = 1;
      this.pageSize = 10;
      this.initFilter = cloneDeep(this.filter);
      this.fetchData();
    },
    onExport() {
      this.exportLoading = true;
      exportDistributorRechargeDetail(this.data)
        .then((res) => {
          download(res, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', `分销商返利充值明细-${parseTime(Date.now(), '{y}-{m}-{d}-{h}:{i}:{s}')}.xlsx`);
        })
        .finally(() => {
          this.exportLoading = false;
        });
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.table-container-top {
  margin-bottom: 10px;
}

.filter-list .filter-item {
  min-width: 25%;
  .el-select {
    width: 100%;
  }
}
</style>
