<template>
  <div class="app-container">
    <div class="table-container">
      <Instructions :showTitle="true" :colNum="0">
        <template slot="title">
          <p>
            <template v-if="initFilter.type === 'MONTH'"> {{ initFilter.period.substring(0, 4) }}年{{ initFilter.period.substring(4, 6) }}月 </template>
            <template v-else> {{ initFilter.period.substring(0, 4) }}年第{{ initFilter.period.substring(5, 6) }}季度 </template>
          </p>
          <p>返利发放金额：￥{{ sumData.amount }}</p>
        </template>
      </Instructions>
      <form @submit.prevent="onSearch" class="commo-search-container commo-search-flex commo-search-margin-bottom10">
        <div class="commo-search-item">
          <span class="commo-search-item-label">店铺名称:</span>
          <div class="commo-search-item-content">
            <el-input clearable v-model.trim="initFilter.shopName" size="small"></el-input>
          </div>
        </div>
        <div class="commo-search-item">
          <span class="commo-search-item-label">登录手机号:</span>
          <div class="commo-search-item-content">
            <el-input clearable v-model.trim="initFilter.mobile" size="small"></el-input>
          </div>
        </div>
        <div class="commo-search-item">
          <span class="commo-search-item-label">专属顾问:</span>
          <div class="commo-search-item-content">
            <el-select v-model="initFilter.customerServiceId" placeholder="请选择" clearable class="customerServiceId" size="small">
              <el-option v-for="item in customerServices" :key="item.id" :label="item.name" :value="item.id"> </el-option>
            </el-select>
          </div>
        </div>
        <div class="commo-search-item">
          <span class="commo-search-item-label">业务分组:</span>
          <div class="commo-search-item-content">
            <el-select v-model="initFilter.brandCategoryId" placeholder="全部" size="small">
              <el-option v-for="item in brandCategory" :key="item.value" :label="item.label" :value="item.value"> </el-option>
            </el-select>
          </div>
        </div>
        <div class="commo-search-item">
          <span class="commo-search-item-label">品牌名称:</span>
          <div class="commo-search-item-content">
            <el-select v-model="initFilter.brandId" placeholder="全部" size="small">
              <el-option v-for="item in brandList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
            </el-select>
          </div>
        </div>
        <div class="commo-search-item">
          <span class="commo-search-item-label">采货类型:</span>
          <div class="commo-search-item-content">
            <el-select v-model="initFilter.purchaseType" placeholder="全部" clearable size="small">
              <el-option key="PURCHASE" label="采销" value="PURCHASE"> </el-option>
              <el-option key="DROP_SHIPPING" label="一件代发" value="DROP_SHIPPING"> </el-option>
            </el-select>
          </div>
        </div>
        <div class="commo-search-btns">
          <el-button native-type="submit" size="small" type="primary">查询</el-button>
          <el-button @click="onReset" size="small">重置</el-button>
          <Authority auth="/brand-rebate/rebate-data/:export">
            <el-button :loading="exportLoading" @click="onExport" size="small">导出</el-button>
          </Authority>
        </div>
      </form>
      <el-table :data="list" element-loading-text="加载中" fit highlight-current-row ref="multipleTable" v-loading.body="listLoading">
        <el-table-column align="center" label="店铺名称" prop="shopName"> </el-table-column>
        <el-table-column align="center" label="登录手机号" prop="mobile"> </el-table-column>
        <el-table-column align="center" label="专属顾问">
          <template slot-scope="scope">
            {{ scope.row.customerServiceName || '/' }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="活动返利发放金额">
          <template slot-scope="scope"> ￥{{ scope.row.activityVirtualCredit }} </template>
        </el-table-column>
        <el-table-column align="center" label="返利品牌" prop="brandName"> </el-table-column>
        <el-table-column align="center" label="采货类型" prop="purchaseTypeName"> </el-table-column>
        <el-table-column align="center" label="业务分组" prop="brandCategoryName"> </el-table-column>
        <el-table-column align="center" label="发放时间">
          <template slot-scope="scope">
            {{ scope.row.virtualCreditDate | parseTime('{y}.{m}.{d} {h}:{i}:{s}') }}
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination :current-page="pageNo" :disabled="listLoading" :page-size="pageSize" :page-sizes="[10, 20, 30, 40, 50, 100]" :total="total" @current-change="handleCurrentChange" @size-change="handleSizeChange" background layout="total, sizes, prev, pager, next, jumper"></el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import { listRebateIssueDetail, exportRebateIssueDetail, getSumRebateDetailData } from '@/api/brand-rebate/rebate-data';
import { customerList } from '@/api/distributorManagement/distributor/list';
import { listAll, brandCategoryOptions } from '@/api/setting/commodity/brand';
import pick from 'lodash/pick';
import pickBy from 'lodash/pickBy';
import download from '@/utils/download';
import { parseTime } from '@/utils';
import cloneDeep from 'lodash/cloneDeep';

export default {
  name: 'rebate-issue-detail',
  data() {
    const filter = {
      shopName: '', // 店铺名称
      mobile: '', // 登录手机号
      customerServiceId: '', // 专属顾问ID
      brandCategoryId: '', // 业务分组id
      brandId: '', // 品牌ID
      type: this.$route.query.timeType,
      period: this.$route.params.period,
      purchaseType: this.$route.query.purchaseType // 采购方式
    };
    const sumData = {
      period: '',
      amount: 0
    };
    return {
      exportLoading: false,
      saveLoading: false,
      formLabelWidth: '80px',
      dialogFormVisible: false,
      formUserType: false,
      filter,
      initFilter: cloneDeep(filter),
      sumData,
      brandList: [], // 品牌列表选项
      brandCategory: [],
      customerServices: [],
      list: [],
      listLoading: true,
      pageNo: 1,
      pageSize: 10,
      total: 0
    };
  },
  computed: {
    // 列表数据过滤
    data() {
      return pickBy(this.initFilter, (val) => !!val);
    }
  },
  mounted() {
    this.initFilter.type = this.$route.query.timeType;
    this.initFilter.period = this.$route.params.period;
    this.initFilter.purchaseType = this.$route.query.purchaseType;

    this.init();
    this.fetchSumData();
    this.fetchBrand();
    this.fetchCustomerList();
    this.fetchBrandCategory();
  },
  methods: {
    init() {
      this.fetchData();
    },
    // 获取所有品牌
    fetchBrand() {
      listAll({}).then((rs) => {
        const res = rs.data.map((item) => ({
          value: item.id,
          label: item.name
        }));
        this.brandList = res;
      });
    },
    // 获取专属顾问列表
    fetchCustomerList() {
      const listQuery = {};
      customerList(listQuery).then((response) => {
        this.customerServices = response.data;
      });
    }, // 获取所有业务分组
    fetchBrandCategory() {
      brandCategoryOptions({}).then((rs) => {
        const res = rs.data.map((item) => ({
          value: item.id,
          label: item.name
        }));
        res.unshift({ value: '', label: '全部' });
        this.brandCategory = res;
      });
    },
    fetchSumData() {
      const queryDate = {
        period: this.initFilter.period,
        type: this.initFilter.type,
        queryType: 'ISSUE'
      };
      getSumRebateDetailData(queryDate).then((response) => {
        if (response.data) {
          this.sumData = response.data;
        }
      });
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.pageNo = 1;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.pageNo = val;
      this.fetchData();
    },
    // 获取列表数据
    fetchData() {
      this.listLoading = true;
      const listQuery = pick(this, ['pageNo', 'pageSize', 'data']);
      listRebateIssueDetail(listQuery)
        .then((response) => {
          const { list = [], total = 0 } = response.data || {};
          this.list = list;
          this.total = total;
        })
        .finally(() => {
          this.listLoading = false;
        });
    },
    // 查询
    onSearch() {
      this.pageNo = 1;
      this.pageSize = 10;
      this.fetchData();
    },
    // 重置
    onReset() {
      this.pageNo = 1;
      this.pageSize = 10;
      this.initFilter = cloneDeep(this.filter);
      this.fetchData();
    },
    onExport() {
      this.exportLoading = true;
      exportRebateIssueDetail(this.data)
        .then((res) => {
          download(res, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', `返利发放明细-${parseTime(Date.now(), '{y}-{m}-{d}-{h}:{i}:{s}')}.xlsx`);
        })
        .finally(() => {
          this.exportLoading = false;
        });
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
@import 'src/styles/goods-table.scss';
.table-container-top {
  margin-bottom: 10px;
}

.filter-list .filter-item {
  min-width: 25%;
  .el-select {
    width: 100%;
  }
}
</style>
