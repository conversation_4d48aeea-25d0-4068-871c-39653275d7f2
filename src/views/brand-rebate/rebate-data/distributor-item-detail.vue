 <template>
  <div class="app-container">
    <div class="table-container">
      <Instructions :showTitle="true" :colNum="0">
        <template slot="title">
          <div class="title_box">
            <p>商品名称：{{ sumData.commodityName }} 商品ID：{{ sumData.commodityId }}</p>
            <p>活动时间：{{ sumData.periodDesc }} 活动发放总金额：￥{{ sumData.activityVirtualCredit }} 发放总人数：{{ sumData.distributorNum }}人</p>
          </div>
        </template>
      </Instructions>
      <form @submit.prevent="onSearch" class="commo-search-container commo-search-flex commo-search-margin-bottom10">
        <div class="commo-search-item">
          <span class="commo-search-item-label">店铺名称:</span>
          <div class="commo-search-item-content">
            <el-input clearable v-model="filter.shopName"></el-input>
          </div>
        </div>
        <div class="commo-search-item">
          <span class="commo-search-item-label">登录手机号:</span>
          <div class="commo-search-item-content">
            <el-input clearable v-model="filter.mobile"></el-input>
          </div>
        </div>
        <div class="commo-search-btns">
          <el-button native-type="submit" size="small" type="primary">查询</el-button>
          <el-button @click="onReset" size="small">重置</el-button>
          <Authority auth="/brand-rebate/rebate-data/:export">
            <el-button :loading="exportLoading" @click="onExport" size="small">导出</el-button>
          </Authority>
        </div>
      </form>
      <el-table :data="list" border element-loading-text="加载中" fit highlight-current-row ref="multipleTable" v-loading.body="listLoading">
        <el-table-column align="center" label="分销商名称" prop="shopName"></el-table-column>
        <el-table-column align="center" label="登录手机号" prop="mobile"></el-table-column>
        <el-table-column align="center" label="活动返利发放金额" prop="activityVirtualCredit">
          <template slot-scope="scope">
            <p v-if="scope.row.activityVirtualCredit">¥{{ scope.row.activityVirtualCredit }}</p>
            <span v-else>¥0</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="采购总金额（可返利）">
          <template slot-scope="scope">
            <p v-if="scope.row.totalAmount">¥{{ scope.row.totalAmount }}</p>
            <span v-else>¥0</span>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination :current-page="pageNo" :disabled="listLoading" :page-size="pageSize" :page-sizes="[10, 20, 30, 40, 50, 100]" :total="total" @current-change="handleCurrentChange" @size-change="handleSizeChange" background layout="total, sizes, prev, pager, next, jumper"></el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import { listIssueByCommodityAndDistributor, exportIssueByCommodityAndDistributor, getIssueByCommodityAndDistributorSum } from '@/api/brand-rebate/rebate-data'; // 接口地址
import pickBy from 'lodash/pickBy'; // 返回一个新对象，值由真值组成
import pick from 'lodash/pick';
import cloneDeep from 'lodash/cloneDeep'; // 对象深拷贝
import download from '@/utils/download';
import { parseTime } from '@/utils';
export default {
  name: 'BrandData',
  data() {
    const initFilter = {
      shopName: '',
      mobile: ''
    };
    const sumData = {
      periodDesc: '',
      commodityId: '',
      commodityName: '',
      activityVirtualCredit: 0,
      distributorNum: 0
    };
    return {
      pageNo: 1,
      pageSize: 10,
      total: 0,
      sumData,
      listLoading: true,
      initFilter, // 搜索条件初始值加入到Data
      filter: cloneDeep(initFilter), // 搜索条件的值
      list: [], // 列表
      exportLoading: false,
      statusOptions: [], // 订单状态
      statusOptionsLoading: false,
      brandOptions: [],
      brandCategoryOptions: [],
      time: this.$route.query.time
    };
  },
  computed: {
    data() {
      const obj = {};
      obj.shopName = this.filter.shopName;
      obj.mobile = this.filter.mobile;
      obj.period = this.$route.query.time;
      obj.commodityId = this.$route.query.id;
      return pickBy(obj, (val) => !!val);
    }
  },
  mounted() {
    this.fetchData();
    this.getIssueByCommodityAndDistributorSum();
  },
  methods: {
    // 重置
    onReset() {
      this.filter = cloneDeep(this.initFilter);
      this.fetchData();
    },
    // 搜索
    onSearch() {
      this.fetchData();
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.pageNo = 1;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.pageNo = val;
      this.fetchData();
    },
    // 获取列表信息
    fetchData() {
      this.listLoading = true;
      const listQuery = pick(this, ['pageNo', 'pageSize', 'data']);
      listIssueByCommodityAndDistributor(listQuery)
        .then((response) => {
          const { list = [], total = 0 } = response.data || {};
          this.list = list;
          this.total = total;
        })
        .finally(() => {
          this.listLoading = false;
        });
    },
    // 数据汇总
    getIssueByCommodityAndDistributorSum() {
      const sendData = {
        shopName: this.filter.shopName,
        mobile: this.filter.mobile,
        period: this.$route.query.time,
        commodityId: this.$route.query.id
      };
      getIssueByCommodityAndDistributorSum(sendData).then((res) => {
        if (res.code === '0' && res.data) {
          this.sumData = res.data;
        }
      });
    },
    onExport() {
      this.exportLoading = true;
      exportIssueByCommodityAndDistributor(this.data)
        .then((res) => {
          download(res, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', `单品分销商发放明细-${parseTime(Date.now(), '{y}-{m}-{d}-{h}:{i}:{s}')}.xlsx`);
        })
        .finally(() => {
          this.exportLoading = false;
        });
    }
  }
};
</script>

<style lang="scss" scoped>
.title_box {
  width: 100%;
  display: flex;
  flex-direction: column;
}
</style>
