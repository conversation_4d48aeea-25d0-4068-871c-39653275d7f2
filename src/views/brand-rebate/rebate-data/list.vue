<template>
  <div class="app-container">
    <div class="table-container">
      <Instructions :showTitle="true" :colNum="1" v-loading="detailLoading">
        <template slot="title">
          <p>返利发放总金额：<strong>{{ getMoney(detail.activityVirtualCredit) }}</strong></p>
          <p>返利使用总金额：<strong>{{ getMoney(detail.usedVirtualCredit) }}</strong></p>
          <p>返利总余额：<strong>{{ getMoney(detail.virtualCredit) }}</strong></p>
        </template>
        <template slot="col1">
          <div class="content__row">
            <p class="content__label">一件代发返利发放总金额：</p>
            <p class="color-danger">
              {{ getMoney(detail.dropShippingVirtualCredit) }}
            </p>
          </div>
          <div class="content__row">
            <p class="content__label">采销返利发放金额：</p>
            <p class="color-danger">
              {{ getMoney(detail.purchaseVirtualCredit) }}
            </p>
          </div>
          <div class="content__row">
            <p class="content__label">返利发放总金额：</p>
            <p>
              包含了返利活动发放的金额+手动充值的金额（正数和负数都计算在内）；
            </p>
          </div>
          <div class="content__row">
            <p class="content__label">返利使用总金额：</p>
            <p>是指订单使用返利抵扣的总金额+失效的总金额；</p>
          </div>
          <div class="content__row">
            <p class="content__label">返利总余额：</p>
            <p>是指平台里所有分销商总返利余额的总和；</p>
          </div>
        </template>
      </Instructions>
      <el-tabs class="custom-border-tabs" v-model="activeName">
        <el-tab-pane label="品牌月度返利数据" name="monthlyData">
          <MonthlyData v-if="activeName === 'monthlyData'"></MonthlyData>
        </el-tab-pane>
        <el-tab-pane label="品牌季度返利数据" name="quarterData">
          <QuarterData v-if="activeName === 'quarterData'"></QuarterData>
        </el-tab-pane>
        <el-tab-pane label="单品返利数据" name="singerData">
          <SingerData v-if="activeName === 'singerData'"></SingerData>
        </el-tab-pane>
        <el-tab-pane label="分销商总返利数据" name="distributorData">
          <DistributorData
            v-if="activeName === 'distributorData'"
          ></DistributorData>
        </el-tab-pane>
        <el-tab-pane label="品牌总返利数据" name="brandData">
          <BrandData v-if="activeName === 'brandData'"></BrandData>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import { getSummary } from '@/api/brand-rebate/rebate-data'; // 接口地址
import SingerData from '@/components/views/brand-rebate/rebate-data/SingerData';
import MonthlyData from '@/components/views/brand-rebate/rebate-data/MonthlyData';
import QuarterData from '@/components/views/brand-rebate/rebate-data/QuarterData';
import DistributorData from '@/components/views/brand-rebate/rebate-data/DistributorData';
import BrandData from '@/components/views/brand-rebate/rebate-data/BrandData';
export default {
  components: {
    SingerData,
    MonthlyData,
    QuarterData,
    DistributorData,
    BrandData
  },
  name: 'rebate-data',
  data() {
    return {
      activeName: 'monthlyData',
      detail: {},
      detailLoading: true
    };
  },
  mounted() {
    this.fetchDataDetail();
  },
  methods: {
    // 金钱格式化
    getMoney(data) {
      return data === 0 || data ? '¥' + data : '/';
    },
    // 获取列表上的汇总数据
    fetchDataDetail() {
      this.detailLoading = true;
      getSummary()
        .then((response) => {
          this.detail = response.data || {};
        })
        .finally(() => {
          this.detailLoading = false;
        });
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
</style>
