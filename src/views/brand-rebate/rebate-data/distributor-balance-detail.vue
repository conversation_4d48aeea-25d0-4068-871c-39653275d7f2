<!-- 分销商返利余额明细 -->
<template>
  <div class="app-container">
    <div class="table-container">
      <filter-form :options="FilterFormOptions" :basicData="basicData" @query="onSubmit_searchForm" :exportOptions="exportOptions" ref="filterForm"></filter-form>
      <el-table :data="list" element-loading-text="加载中" fit highlight-current-row ref="multipleTable" v-loading.body="listLoading">
        <el-table-column align="center" label="分销商名称" prop="shopName"> </el-table-column>
        <el-table-column align="center" label="分销商ID" prop="distributorId"> </el-table-column>
        <el-table-column align="center" label="登录手机号" prop="loginMobile"> </el-table-column>
        <el-table-column align="center" label="专属顾问">
          <template slot-scope="scope">
            {{ scope.row.customerName || '/' }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="返利余额">
          <template slot-scope="scope"> ￥{{ scope.row.remainVirtualAmount }} </template>
        </el-table-column>
        <el-table-column align="center" label="返利即将到期金额" width="200">
          <template slot="header" slot-scope="scope">
            <div style="padding-right: 0">
              返利即将到期金额
              <el-tooltip class="item" effect="dark" placement="top">
                <div slot="content" class="header-tooltip-sort">返利即将到期金额：筛选时间内，返利要失效的总金额。</div>
                <i class="el-icon-question"></i>
              </el-tooltip>
            </div>
          </template>
          <template slot-scope="scope">
            <TableMoney :money="scope.row.aboutExpireVirtualAmount" @click="link(scope.row)"></TableMoney>
          </template>
        </el-table-column>
        <el-table-column align="center" label="返利最新到期时间">
          <template slot-scope="scope">
            {{ scope.row.maxExpireDate | parseTime('{y}-{m}-{d} {h}:{i}:{s}') }}
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination :current-page="pageNo" :disabled="listLoading" :page-size="pageSize" :page-sizes="[10, 20, 30, 40, 50, 100]" :total="total" @current-change="handleCurrentChange" @size-change="handleSizeChange" background layout="total, sizes, prev, pager, next, jumper"></el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import { listByDistributorVirtualCredit, exportExcelDistributorVirtualCredit } from '@/api/brand-rebate/rebate-data';
import pickBy from 'lodash/pickBy';
import dict from '@/components/Common/dicts';
import FilterForm from '@/components/Form/FilterForm';
import TableMoney from '@/components/Table/Money';
export default {
  name: 'rebate-data-distributor_balance_detail',
  components: { FilterForm, TableMoney },
  data() {
    return {
      distributorId: this.$route.query.distributorId, // 商品Id
      list: [],
      listLoading: false,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      basicData: {
        betweenExpiredDate: '7'
      }
    };
  },
  computed: {
    exportOptions() {
      const that = this;
      return {
        auth: 'distributor-rebate-grant-detail:export',
        name: '分销商返利余额明细',
        request() {
          const params = that.getParams();
          return exportExcelDistributorVirtualCredit(params);
        }
      };
    },
    FilterFormOptions() {
      return [
        { prop: 'shopName', label: '分销商名称', component: 'input' },
        { prop: 'distributorId', label: '分销商ID', component: 'input' },
        { prop: 'mobile', label: '登录手机号', component: 'input' },
        {
          prop: 'expireVirtual',
          label: '返利到期时间',
          component: 'dateRange',
          type: 'daterange',
          props: ['expireVirtualBeginDate', 'expireVirtualEndDate']
        },
        {
          prop: 'csIds',
          label: '专属顾问',
          component: 'select',
          multiple: true,
          collapseTags: true,
          options: dict('COMMON_EXPAND_ADVISER')
        },
        {
          prop: 'betweenExpiredDate',
          label: '距离到期日期',
          component: 'select',
          options: dict('REBATE_INVALID_DATE')
        }
      ];
    }
  },
  mounted() {
    // /soyoung-zg/#/brand-rebate/rebate-data/distributor-balance-detail?csIds=" + cs + "&betweenExpiredDate=" + day + "#/"
    const query = this.$route.query;
    Object.assign(this.basicData, query);
    this.$refs.filterForm.clear();
    this.fetchData();
  },

  methods: {
    link(row) {
      const { betweenExpiredDate } = this.getParams();
      this.$router.push({
        path: '/brand-rebate/rebate-data/distributor-rebate-grant-detail',
        query: {
          distributorId: row.distributorId || this.distributorId,
          betweenExpiredDate
        }
      });
    },
    // 列表查询
    onSubmit_searchForm() {
      this.pageNo = 1;
      this.fetchData();
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.pageNo = 1;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.pageNo = val;
      this.fetchData();
    },
    // 获取列表参数
    getParams() {
      const formData = this.$refs.filterForm.getParams();
      const params = pickBy(formData, (val) => !!val);
      return params;
    },
    // 获取列表数据
    fetchData() {
      this.listLoading = true;
      const listQuery = {
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        data: this.getParams()
      };
      listByDistributorVirtualCredit(listQuery)
        .then((response) => {
          const { list = [], total = 0 } = response.data || {};
          this.list = list;
          this.total = total;
        })
        .finally(() => {
          this.listLoading = false;
        });
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
</style>
