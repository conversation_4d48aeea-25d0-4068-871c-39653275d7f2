<template>
  <div class="app-container">
    <div class="table-container">
      <Instructions :showTitle="true" :colNum="0">
        <template slot="title">
          <p>品牌名称:{{ sumData.brandName }}</p>
          <p>活动返利发放金额：￥{{ sumData.totalAmount }}</p>
        </template>
        <!-- <template slot="col1">
          <div class="content__row">
            <p class="content__label">统计时间：</p>
            <p>{{ timeObj ? timeObj : '全部' }}</p>
          </div>
        </template> -->
      </Instructions>
      <form @submit.prevent="onSearch" class="commo-search-container commo-search-flex commo-search-margin-bottom10">
        <div class="commo-search-item">
          <span class="commo-search-item-label">店铺名称:</span>
          <div class="commo-search-item-content">
            <el-input clearable v-model.trim="initFilter.shopName"></el-input>
          </div>
        </div>
        <div class="commo-search-item">
          <span class="commo-search-item-label">登录手机号:</span>
          <div class="commo-search-item-content">
            <el-input clearable v-model.trim="initFilter.mobile"></el-input>
          </div>
        </div>
        <div class="commo-search-item">
          <span class="commo-search-item-label">专属顾问:</span>
          <div class="commo-search-item-content">
            <el-select v-model="initFilter.customerServiceIds" placeholder="请选择(可复选)" clearable multiple class="customerServiceId">
              <el-option v-for="item in customerServices" :key="item.id" :label="item.name + item.mobile" :value="item.id"> </el-option>
            </el-select>
          </div>
        </div>
        <div class="commo-search-item">
          <span class="commo-search-item-label">采货类型:</span>
          <div class="commo-search-item-content">
            <el-select v-model="initFilter.purchaseType" placeholder="全部" clearable>
              <el-option key="PURCHASE" label="采销" value="PURCHASE"> </el-option>
              <el-option key="DROP_SHIPPING" label="一件代发" value="DROP_SHIPPING"> </el-option>
            </el-select>
          </div>
        </div>
        <div class="commo-search-item">
          <span class="commo-search-item-label">活动类型:</span>
          <div class="commo-search-item-content">
            <el-select v-model="initFilter.activityType" placeholder="全部" clearable>
              <el-option :key="idx" :label="item.label" :loading="customerServiceVOLoading" :value="item.value" v-for="(item, idx) in activityType"></el-option>
            </el-select>
          </div>
        </div>
        <div class="commo-search-item">
          <span class="commo-search-item-label">商品名称:</span>
          <div class="commo-search-item-content">
            <el-input clearable v-model.trim="initFilter.commodityName"></el-input>
          </div>
        </div>
        <div class="commo-search-item">
          <span class="commo-search-item-label">商品ID:</span>
          <div class="commo-search-item-content">
            <el-input clearable v-model.trim="initFilter.commodityId"></el-input>
          </div>
        </div>
        <div class="commo-search-item">
          <span class="commo-search-item-label">发放时间:</span>
          <div class="content">
            <el-date-picker size="small" v-model="initFilter.formAuditDate" type="datetimerange" align="right" start-placeholder="开始日期" end-placeholder="结束日期" :default-time="['00:00:00', '23:59:59']"></el-date-picker>
          </div>
        </div>
        <div class="commo-search-btns">
          <el-button native-type="submit" size="small" type="primary">查询</el-button>
          <el-button @click="onReset" size="small">重置</el-button>
          <Authority auth="/brand-rebate/rebate-data/:export">
            <el-button :loading="exportLoading" @click="onExport" size="small">导出</el-button>
          </Authority>
        </div>
      </form>
      <div class="data-sum">
        <span> 汇总：品牌月度总返利：￥{{ sumData.monthAmount }}</span>
        <span>品牌季度总返利：￥{{ sumData.quarterAmount }}</span>
        <span>单品总返利：￥{{ sumData.commodityAmount }}</span>
      </div>
      <el-table :data="list" element-loading-text="加载中" fit highlight-current-row ref="multipleTable" v-loading.body="listLoading">
        <el-table-column align="center" label="店铺名称" prop="shopName"> </el-table-column>
        <el-table-column align="center" label="登录手机号" prop="mobile"> </el-table-column>
        <el-table-column align="center" label="专属顾问">
          <template slot-scope="scope">
            {{ scope.row.customerServiceName || '/' }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="活动返利发放总金额">
          <template slot-scope="scope"> ￥{{ scope.row.activityVirtualCredit }} </template>
        </el-table-column>
        <el-table-column align="center" label="活动时间" prop="periodDesc"> </el-table-column>
        <el-table-column align="center" label="活动类型" prop="activityTypeName"> </el-table-column>
        <el-table-column align="center" label="采货类型" prop="purchaseTypeName"> </el-table-column>
        <el-table-column align="center" label="发放时间">
          <template slot-scope="scope">
            {{ scope.row.virtualCreditDate | parseTime('{y}.{m}.{d} {h}:{i}:{s}') }}
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination :current-page="pageNo" :disabled="listLoading" :page-size="pageSize" :page-sizes="[10, 20, 30, 40, 50, 100]" :total="total" @current-change="handleCurrentChange" @size-change="handleSizeChange" background layout="total, sizes, prev, pager, next, jumper"></el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import { listIssueByBrand, getIssueByBrandSum, exportBrandIssueDetail } from '@/api/brand-rebate/rebate-data';
import { customerList } from '@/api/distributorManagement/distributor/list';
import { fetchChannelOptions } from '@/api/brand-rebate/rebate-data';
import pick from 'lodash/pick';
import pickBy from 'lodash/pickBy';
import download from '@/utils/download';
import { parseTime } from '@/utils';
import cloneDeep from 'lodash/cloneDeep';
export default {
  name: 'brand-issue-detail',
  data() {
    const filter = {
      shopName: '', // 店铺名称
      mobile: '', // 登录手机号
      customerServiceIds: '', // 专属顾问ID
      purchaseType: this.$route.query.purchaseType, // 采购方式
      activityType: '',
      commodityName: '',
      commodityId: ''
    };
    const sumData = {
      brandName: '',
      totalAmount: 0,
      monthAmount: 0,
      quarterAmount: 0,
      commodityAmount: 0
    };
    return {
      exportLoading: false,
      saveLoading: false,
      formLabelWidth: '80px',
      dialogFormVisible: false,
      formUserType: false,
      filter,
      initFilter: cloneDeep(filter),
      sumData,
      brandList: [], // 品牌列表选项
      brandCategory: [],
      customerServices: [],
      list: [],
      listLoading: true,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      activityType: [],
      statisticalTime: null
    };
  },
  computed: {
    // 列表数据过滤
    data() {
      const obj = pickBy(this.initFilter, (val) => !!val);
      obj.brandId = this.$route.params.brandId;
      if (Array.isArray(this.statisticalTime) && this.statisticalTime.length > 0) {
        // 时间条件
        const [startTime, endTime] = this.statisticalTime;
        obj.startTime = startTime.getTime();
        obj.endTime = endTime.getTime();
      }
      return obj;
    }
  },
  mounted() {
    this.initFilter.purchaseType = this.$route.query.purchaseType;

    this.fetchData();
    this.fetchSumData();
    this.fetchCustomerList();
    this.fetchChannelOptions();
  },
  methods: {
    // 获取专属顾问列表
    fetchCustomerList() {
      const listQuery = {};
      customerList(listQuery).then((response) => {
        this.customerServices = response.data;
      });
    },
    // 获取活动类型
    fetchChannelOptions() {
      this.customerServiceVOLoading = true;
      fetchChannelOptions({})
        .then((res) => {
          if (res.code === '0' && res.data) {
            this.activityType = res.data;
          }
        })
        .finally(() => {
          this.customerServiceVOLoading = false;
        });
    },
    fetchSumData() {
      const sendData = this.data;
      getIssueByBrandSum(sendData).then((response) => {
        if (response.data) {
          this.sumData = response.data;
        }
      });
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.pageNo = 1;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.pageNo = val;
      this.fetchData();
    },
    // 获取列表数据
    fetchData() {
      this.listLoading = true;
      const listQuery = pick(this, ['pageNo', 'pageSize', 'data']);
      listIssueByBrand(listQuery)
        .then((response) => {
          const { list = [], total = 0 } = response.data || {};
          this.list = list;
          this.total = total;
        })
        .finally(() => {
          this.listLoading = false;
        });
    },
    // 查询
    onSearch() {
      this.pageNo = 1;
      this.pageSize = 10;
      this.fetchData();
      this.fetchSumData();
    },
    // 重置
    onReset() {
      this.pageNo = 1;
      this.pageSize = 10;
      this.statisticalTime = null;
      this.initFilter = cloneDeep(this.filter);
      this.fetchData();
      this.fetchSumData();
    },
    onExport() {
      this.exportLoading = true;
      exportBrandIssueDetail(this.data)
        .then((res) => {
          download(res, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', `品牌返利发放明细-${parseTime(Date.now(), '{y}-{m}-{d}-{h}:{i}:{s}')}.xlsx`);
        })
        .finally(() => {
          this.exportLoading = false;
        });
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.aggregate_data {
  width: 100%;
  display: flex;
  justify-content: flex-end;
}
.table-container-top {
  margin-bottom: 10px;
}

.filter-list .filter-item {
  min-width: 25%;
  .el-select {
    width: 100%;
  }
}
.data-sum {
  width: 100%;
  display: flex;
  justify-content: flex-end;
  background: #eee;
  padding: 20px;
  & > span {
    padding-right: 20px;
  }
}
</style>
