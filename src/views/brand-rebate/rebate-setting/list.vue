<template>
  <div class="rebate-list">
    <div class="content">
      <el-form :inline="true" class="commo-search-container commo-search-flex commo-search-margin-bottom10">
        <el-form-item label="品牌">
          <el-select filterable multiple placeholder="请选择" v-model="sendData.data.brandIds">
            <el-option :key="item.id" :label="item.name" :value="item.id" v-for="item in searObj.options"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="采货类型">
          <el-select placeholder="请选择" v-model="sendData.data.purchaseType">
            <el-option :key="item.value" :label="item.label" :value="item.value" v-for="item in searObj.purchaseTypeOptions"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button @click="onSubmit" type="primary">查询</el-button>
          <el-button @click="clearForm">清空</el-button>
        </el-form-item>
        <el-form-item>
          <div>
            <Authority auth="/brand-rebate/rebate-setting/:create">
              <router-link to="/brand-rebate/rebate-setting/create">
                <el-button icon="el-icon-plus" size="small" type="primary">新增</el-button>
              </router-link>
            </Authority>
            <Authority auth="/brand-rebate/rebate-setting/:edit">
              <el-button @click="setSingle" icon="el-icon-plus" class="add-mk" size="small" type="primary">设置单笔返点门槛</el-button>
            </Authority>
          </div>
        </el-form-item>
      </el-form>

      <el-table :data="tableData" style="width: 100%">
        <el-table-column label="返利品牌" prop="brandName" width="auto"></el-table-column>
        <el-table-column label="采货类型" prop="purchaseTypeName" width="auto"></el-table-column>
        <el-table-column label="月度返利规则" width="auto">
          <template slot-scope="scope">
            <div v-for="(item, index) in parseList(scope.row.monthThreshold)" :key="index">
              {{ item }}
            </div>
            <div v-if="parseList(scope.row.monthThreshold).length === 0">/</div>
          </template>
        </el-table-column>
        <el-table-column label="季度返利规则" width="auto">
          <template slot-scope="scope">
            <div v-for="(item, index) in parseList(scope.row.quarterThreshold)" :key="index">
              {{ item }}
            </div>
            <div v-if="parseList(scope.row.quarterThreshold).length === 0">/</div>
          </template>
        </el-table-column>
        <el-table-column label="更新时间" width="auto">
          <template slot-scope="scope"> {{ parseInt(scope.row.updateDate) | parseTime('{y}-{m}-{d} {h}:{i}:{s}') }}</template>
        </el-table-column>
        <el-table-column label="月度活动时间" width="auto">
          <template slot-scope="scope">
            <div v-for="(item, index) in parseList(scope.row.monthPeriod)" :key="index">
              {{ item }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="110">
          <template slot-scope="scope">
            <router-link :to="`/brand-rebate/rebate-setting/detail/${scope.row.id}`">
              <Authority auth="/brand-rebate/rebate-setting/:view">
                <el-button type="text">查看</el-button>
              </Authority>
            </router-link>
            <Authority auth="/brand-rebate/rebate-setting/:edit">
              <router-link :to="`/brand-rebate/rebate-setting/edit/${scope.row.id}`">
                <el-button type="text">编辑</el-button>
              </router-link>
            </Authority>
            <Authority auth="/brand-rebate/rebate-setting/period-list/:edit">
              <router-link :to="`/brand-rebate/rebate-setting/period-list/${scope.row.id}`">
                <el-button type="text">活动时间</el-button>
              </router-link>
            </Authority>
            <Authority auth="/brand-rebate/rebate-setting/rule-list/:edit">
              <router-link :to="`/brand-rebate/rebate-setting/rule-list/${scope.row.id}?brandName=${scope.row.brandName}`">
                <el-button type="text">规则修改记录</el-button>
              </router-link>
            </Authority>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination :current-page="sendData.pageNo" :page-size="sendData.pageSize" :page-sizes="[10, 20, 30, 40, 50, 100]" :total="total" @current-change="handleCurrentChange" @size-change="handleSizeChange" background layout="total, sizes, prev, pager, next, jumper"></el-pagination>
      </div>
      <el-dialog :before-close="handleClose" :visible.sync="dialogFormVisible" center title="设置单笔订单返点门槛" :close-on-click-modal="false">
        <div class="dialog-div">
          <span>门槛金额</span>
          <el-input autocomplete="off" v-model.trim="typeValue"></el-input>
          <span>（元）</span>
        </div>
        <p class="dialog-text">（单笔订单金额大于等于{{ typeValue || '未设置' }}元才能参与返利计算）</p>
        <div class="dialog-footer" slot="footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button @click="singleSure" type="primary">确 定</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import { disable, onceCreate, onceUpdate, onceGet } from '@/api/activity/rebate';
import { listActivity, listBrand } from '@/api/brand-rebate/rebate-setting';
import { validateMoney2 } from '@/utils/validate';
export default {
  name: 'rebate-brand-setting-list',
  data() {
    return {
      searObj: {
        options: [],
        purchaseTypeOptions: [
          { id: '', label: '全部', value: '' },
          { id: 'PURCHASE', label: '采销', value: 'PURCHASE' },
          { id: 'DROP_SHIPPING', label: '一件代发', value: 'DROP_SHIPPING' }
        ]
      },
      sendData: {
        data: {
          purchaseType: '',
          brandIds: [],
          creditBackType: 'BRAND'
        },
        pageNo: 1,
        pageSize: 10
      },
      tableData: [],
      total: 0,
      dialogFormVisible: false,
      typeValue: '',
      VIRTUAL_CREDIT_ID: ''
    };
  },
  activated() {
    this.init();
  },
  methods: {
    parseList(data) {
      if (!data) {
        return [];
      }
      const dataList = data.split('\n');
      return dataList;
    },
    init() {
      // 获取品牌列表
      listBrand({}).then((res) => {
        this.searObj.options = res.data;
      });
      this.getList();
    },
    getList() {
      listActivity(this.sendData).then((res) => {
        this.tableData = res.data.list;
        this.total = res.data.total;
      });
    },
    onSubmit() {
      this.sendData.pageNo = 1;
      this.getList();
    },
    clearForm() {
      this.sendData.pageNo = 1;
      this.sendData.data.purchaseType = '';
      this.sendData.data.brandIds = [];
      this.getList();
    },
    handleSizeChange(val) {
      this.sendData.pageSize = val;
      this.sendData.pageNo = 1;
      this.getList();
    },
    handleCurrentChange(val) {
      this.sendData.pageNo = val;
      this.getList();
    },
    /**
     * 设置单笔返点门槛
     */
    setSingle() {
      onceGet('VIRTUAL_CREDIT').then((res) => {
        if (res.data) {
          this.VIRTUAL_CREDIT_ID = res.data.id;
          this.typeValue = res.data.typeValue;
        } else {
          this.VIRTUAL_CREDIT_ID = '';
        }
      });
      this.dialogFormVisible = true;
    },
    handleClose() {
      this.typeValue = '';
      this.dialogFormVisible = false;
    },
    singleSure() {
      if (this.typeValue && validateMoney2(this.typeValue)) {
        if (this.VIRTUAL_CREDIT_ID) {
          onceUpdate({
            id: this.VIRTUAL_CREDIT_ID,
            type: 'VIRTUAL_CREDIT',
            typeValue: this.typeValue
          }).then((res) => {
            if (res.code === '0') {
              this.$message({
                message: '操作成功',
                type: 'success'
              });
              this.getList();
            }
          });
        } else {
          onceCreate({
            type: 'VIRTUAL_CREDIT',
            typeValue: this.typeValue
          }).then((res) => {
            if (res.code === '0') {
              this.$message({
                message: '操作成功',
                type: 'success'
              });
            }
          });
        }
        this.dialogFormVisible = false;
      } else {
        this.$message({
          message: '单笔订单返点门槛格式为保留2位小数的数字',
          type: 'warning'
        });
      }
    },
    stopClick(id) {
      this.$confirm('确认停用吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          disable(id).then((res) => {
            this.$message({
              type: 'success',
              message: '操作成功！'
            });
            this.getList();
          });
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
    }
  }
};
</script>
<style lang="scss" scoped>
.rebate-list {

  .content {
    background: #fff;
    padding: 20px;

    .dialog-div {
      display: flex;
      align-items: center;
      height: 60px;

      span {
        width: 100px;
        text-align: center;
      }
    }

    .dialog-text {
      text-align: center;
      margin-bottom: 0px;
    }
  }
}
.add-mk {
  margin-left: 10px;
}
</style>
