<template>
  <div>
    <div class="label-txt" v-if="!isCreate">{{ txt }}</div>
    <el-table :data="tableData" style="width: 100%">
      <el-table-column label="阶梯" width="80" type="index"> </el-table-column>
      <el-table-column label="累计购物金额" align="center">
        <template slot="header" slot-scope="scope">
          <div class="commo-asterisk header-box">累计购物金额</div>
        </template>
        <template slot-scope="scope">
          <div class="table-cell">
            <div class="input-box">
              <el-input
                class="input-box--item"
                type="number"
                v-model.number="scope.row.minAmount"
                placeholder="请输入金额下限"
                :disabled="isDetail"
                ><template slot="append">元</template></el-input
              >-
              <el-input
                class="input-box--item"
                type="number"
                v-model.number="scope.row.maxAmount"
                placeholder="请输入金额上限"
                :disabled="isDetail"
                ><template slot="append">元</template></el-input
              >
            </div>
            <div class="table__error" v-if="scope.row.errorTxtAmount">
              {{ scope.row.errorTxtAmount }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="返点比例" align="center" width="300">
        <template slot="header" slot-scope="scope">
          <div class="commo-asterisk header-box">返点比例</div>
        </template>
        <template slot-scope="scope">
          <div class="wrap__box">
            <el-input
              type="number"
              v-model.number="scope.row.backRate"
              :disabled="isDetail"
              class="back-rate"
              ><template slot="append">%</template></el-input
            >
            <div class="table__error" v-if="scope.row.errorTxtBackRate">
              {{ scope.row.errorTxtBackRate }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right" width="100">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="danger"
            :disabled="isDetail"
            @click="handleDelete(scope.$index, scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-button :disabled="isDetail" @click="addData" type="text"
      >添加阶梯</el-button
    >
  </div>
</template>

<script>
import round from 'lodash/round';
export default {
  data() {
    return {
      tableData: []
    };
  },
  activated() {
  },
  computed: {
    txt() {
      return this.isQuarter
        ? '季度奖励规则的修改，针对没有结束的季度可以生效，已经结束的季度修改不生效'
        : '月度奖励规则的修改，针对没有结束的月度可以生效，已经结束的月度修改不生效';
    }
  },
  props: {
    isCreate: Boolean, // 是否是新增
    isDetail: Boolean, // 是否是详情
    isEdit: Boolean, // 是否编辑
    isQuarter: Boolean // 是否是季度
  },
  methods: {
    handleDelete(index) {
      this.tableData.splice(index, 1);
    },
    addData() {
      const itemmaxAmount = this.tableData[this.tableData.length - 1];
      if (itemmaxAmount && itemmaxAmount.maxAmount > 0) {
        this.tableData.push(this.getDataItem(itemmaxAmount.maxAmount));
        return;
      }
      this.tableData.push(this.getDataItem());
    },
    getDataItem(num) {
      const obj = {
        backRate: '',
        maxAmount: '',
        minAmount: ''
      };
      if (num > 0) {
        obj.minAmount = num;
      }
      return obj;
    },
    // 外部调用 初始值
    setList(tableData) {
      this.tableData = tableData.map((item) => ({
        backRate: (item.backRate * 100).toFixed(2),
        maxAmount: item.maxAmount,
        minAmount: item.minAmount
      }));
    },
    // 外部调用 传出值
    getList() {
      let fa = false;
      const tableData = this.tableData;
      if (tableData.length === 0) {
        return [];
      }

      tableData.forEach((item, i) => {
        if (this.isCheck(item.maxAmount) || this.isCheck(item.minAmount)) {
          item.errorTxtAmount = '输入错误不能为空或负数、最大不能超过8位数';
          fa = true;
          return;
        } else {
          item.errorTxtAmount = null;
        }
        if (item.minAmount >= item.maxAmount) {
          item.errorTxtAmount = '输入错误金额下限不能大于或等于金额上限';
          fa = true;
          return;
        } else {
          item.errorTxtAmount = null;
        }
        if (this.isCheck(item.backRate)) {
          item.errorTxtBackRate = '输入错误不能为空或负数';
          fa = true;
          return;
        } else {
          item.errorTxtBackRate = null;
        }
        if (this.isOverlap(item, tableData[i + 1])) {
          item.errorTxtAmount = `输入错误,阶梯上限大于阶梯${i + 2}阶梯下限`;
          fa = true;
        }
        if (i > 0 && item.minAmount !== tableData[i - 1].maxAmount) {
          item.errorTxtAmount = `输入错误,阶梯值不连续`;
          fa = true;
        }
        if (i > 0 && round(item.backRate) < round(tableData[i - 1].backRate)) {
          item.errorTxtBackRate = `输入错误，阶梯${i + 1}返点比例必须大于上一个阶梯`;
          fa = true;
        }
      });
      this.tableData = [...tableData];
      if (!fa) {
        return tableData.map((item) => ({
          backRate: item.backRate * 0.01,
          maxAmount: item.maxAmount,
          minAmount: item.minAmount
        }));
      } else {
        return false;
      }
    },
    // 单个数值校验
    isCheck(val) {
      if (!val && val !== 0) return true;
      if (val < 0) return true;
      if (val >= 100000000) return true; // 最大不能超过8位数值
      return false;
    },
    // 阶梯区间校验
    isOverlap(q1, q2) {
      if (!q2) return false;
      if (q1.maxAmount > q2.minAmount) return true;
      return false;
    }
  }
};
</script>

<style lang="scss" scoped>
.input-box {
  display: flex;
  align-items: center;
  .input-box--item {
    flex: 1;
  }
}
.wrap__box {
  display: flex;
  flex-direction: column;
}
.table__error {
  position: absolute;
  color: var(--color-danger);
  font-size: 12px;
  line-height: 1;
  padding-top: 4px;
  text-align: left;
  bottom: -4px;
}
.label-txt {
  color: red;
  margin-bottom: 15px;
}
.header-box {
  display: flex;
  align-items: center;
  line-height: normal;
}
.back-rate {
  height: 36px;
}
::v-deep .el-table__row {
  .cell {
    position: relative;
    height: 60px;
    display: flex;
    align-items: center;
    overflow: inherit;
  }
}
</style>

