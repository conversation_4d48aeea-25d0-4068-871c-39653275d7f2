<template>
  <div class="rebate-components">
    <div class="content">
      <el-form :model="form" :rules="rules" label-width="120px" ref="form">
        <div class="form-title">返利信息设置</div>
        <el-form-item label="返利品牌" prop="brandCategoryId">
          <el-select v-model="form.brandCategoryId" placeholder="请选择" filterable @change="brandCategoryChange" :disabled="isDetail || isEdit">
            <el-option v-for="item in optionsListBrandCategory" :key="item.id" :label="item.name" :value="item.id"> </el-option>
          </el-select>
          <el-select v-model="form.brandId" placeholder="请选择" :disabled="isDetail || isEdit" filterable>
            <el-option v-for="item in optionsListBrand" :key="item.id" :label="item.name" :value="item.id"> </el-option>
          </el-select>
          <div v-if="!isDetail && !isEdit">
            找不到品牌，请点击
            <router-link to="/brand/list">
              <el-button type="text"> 品牌管理 </el-button>
            </router-link>
            |
            <el-button type="text" @click="refreshBrand"> 刷新 </el-button>
            去管理
          </div>
        </el-form-item>
        <el-form-item label="采货类型" prop="purchaseType">
          <el-radio-group :disabled="isDetail || isEdit" v-model="form.purchaseType">
            <el-radio :key="item.id" :label="item.id" v-for="item in options">{{ item.name }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <!-- <el-form-item label="返利文案" prop="description" class="item-box">
          <el-input
            :disabled="isDetail"
            type="textarea"
            v-model="form.description"
          ></el-input>
          <div class="tip">
            返利文案主要用于介绍该品牌的返利情况和计算方式（建议在100字以内）
          </div>
        </el-form-item> -->
        <div class="pagestip">返利规则的设置：月度返利和季度返利的规则必须至少要填写一个，才能保存哦！</div>
        <el-form-item label="月度返利规则" class="item-box"> <myTable ref="MONTH" :isCreate="!id ? true : false" :isDetail="isDetail" :isEdit="isEdit" /></el-form-item>
        <el-form-item label="季度返利规则" class="item-box"><myTable ref="QUARTER" :isCreate="!id ? true : false" isQuarter :isDetail="isDetail" :isEdit="isEdit" /> </el-form-item>
        <el-form-item v-if="!isDetail">
          <Authority auth="/brand-rebate/rebate-setting/:edit">
            <el-button :loading="saveLoading" @click="onSubmit" type="primary">提交</el-button>
          </Authority>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>
<script>
import { listBrandCategory, create, update, getDetail } from '@/api/brand-rebate/rebate-setting';
import { fn_debounce } from '@/utils/enhance';
import myTable from './myTable';
export default {
  name: 'rebate-setting-form',
  components: {
    myTable
  },
  data() {
    const validateBrand = (_, value, callback) => {
      if (!this.form.brandCategoryId) {
        callback(`请选择业务分组`);
        return;
      }
      if (!this.form.brandId) {
        callback(`请选择品牌`);
        return;
      }
      callback();
    };
    return {
      dataOrigin: {}, // 编辑的时候第一次进来保存的数据
      options: [
        { id: 'PURCHASE', name: '采销' },
        { id: 'DROP_SHIPPING', name: '一件代发' }
      ],
      optionsListBrandCategory: [], // 所有业务分组
      optionsListBrand: [], // 分类下的所有品牌
      form: {
        purchaseType: 'PURCHASE', // 采购方式
        description: '', // 文案
        brandCategoryId: '', // 业务分组id
        brandId: '' // 品牌id
      },
      rules: {
        purchaseType: [{ required: true, message: '请选择采货类型', trigger: 'change' }],
        brandCategoryId: [
          { required: true, message: '请选择品牌', trigger: 'change' },
          { validator: validateBrand, trigger: 'change' }
        ],
        brandId: [{ required: true, message: '请选择活动品牌', trigger: 'change' }],
        description: [{ required: true, message: '请输入返利文案', trigger: 'blur' }]
      },
      saveLoading: false
    };
  },
  props: {
    id: String,
    isDetail: {
      type: Boolean,
      default: false
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  created() {
    this.listBrandCategory();
  },
  watch: {},
  methods: {
    brandCategoryChange(id) {
      this.optionsListBrand = this.optionsListBrandCategory.find((item) => item.id === id).brandList;
      this.form.brandId = '';
    },
    // 刷新品牌
    refreshBrand() {
      this.listBrandCategory();
    },
    // 获取业务分组
    listBrandCategory() {
      listBrandCategory()
        .then((res) => {
          this.optionsListBrandCategory = res.data.filter((item) => item.name !== '水羊国际');
        })
        .then(() => {
          this.initData();
        });
    },
    initData() {
      if (this.id) {
        this.getDetail();
      }
    },
    getDetail() {
      getDetail(this.id).then(({ data }) => {
        const form = this.form;
        form.brandCategoryId = data.brandCategoryId;
        this.optionsListBrand = this.optionsListBrandCategory.find((item) => item.id === data.brandCategoryId).brandList;
        form.brandId = data.brandId;
        form.description = data.description;
        form.purchaseType = data.purchaseType;
        this.form = form;
        this.dataOrigin = { ...data };
        if (data.monthThresholdList) {
          this.$refs['MONTH'].setList(data.monthThresholdList);
        }
        if (data.quarterThresholdList) {
          this.$refs['QUARTER'].setList(data.quarterThresholdList);
        }
      });
    },
    onSubmit: fn_debounce(function () {
      const monthThresholdList = this.$refs['MONTH'].getList(); // 月度
      const quarterThresholdList = this.$refs['QUARTER'].getList(); // 季度
      if (monthThresholdList.length === 0 && quarterThresholdList.length === 0) {
        this.$message.error('月度返利和季度返利的规则必须至少要填写一个');
        return;
      }
      // 当组件内部的校验没有通过的时候这里返回false
      if (!monthThresholdList) return;
      if (!quarterThresholdList) return;
      const originMonthThresholdList = this.dataOrigin.monthThresholdList || [];
      const originQuarterThresholdList = this.dataOrigin.quarterThresholdList || [];
      // 需要判断季度返利/月度返利每一次
      // 假如是编辑的时候需要判断数据和编辑刚进来的时候的dataOrigin比较是否一致，要是一样就不允许提交
      if (this.id && monthThresholdList.length === originMonthThresholdList.length && originQuarterThresholdList.length === quarterThresholdList.length) {
        let isSame = true;
        monthThresholdList.forEach((v, i) => {
          if (v.minAmount !== originMonthThresholdList[i].minAmount || v.maxAmount !== originMonthThresholdList[i].maxAmount || v.backRate !== originMonthThresholdList[i].backRate) {
            isSame = false;
            return;
          }
        });
        if (isSame) {
          quarterThresholdList.forEach((v, i) => {
            if (v.minAmount !== originQuarterThresholdList[i].minAmount || v.maxAmount !== originQuarterThresholdList[i].maxAmount || v.backRate !== originQuarterThresholdList[i].backRate) {
              isSame = false;
              return;
            }
          });
        }
        if (isSame) {
          this.$message.error('未对当前页面做修改,请勿重复保存');
          return;
        }
      }
      this.$refs['form'].validate((valid) => {
        if (valid) {
          const request = this.id ? update : create;
          const sendData = {
            brandCategoryId: this.form.brandCategoryId,
            brandId: this.form.brandId,
            description: this.form.description,
            purchaseType: this.form.purchaseType,
            monthThresholdList,
            quarterThresholdList,
            creditBackType: 'BRAND'
          };
          if (this.id) {
            sendData.id = this.id;
          }
          this.saveLoading = true;
          request(sendData)
            .then((response) => {
              this.$message.success('操作成功');
              // this.$router.push({
              //   path: '/brand-rebate/rebate-setting/list'
              // });
              this.$back();
            })
            .finally(() => {
              this.saveLoading = false;
            });
        } else {
          return false;
        }
      });
    }),
    checkValidateInteger(rule, value, callback) {
      if (/^\+?[0-9]\d*$/.test(value)) {
        callback();
      } else {
        callback(new Error('请填写整数'));
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.rebate-components {
  padding: 20px;
  .content {
    background: #fff;
    padding: 18px 30px;
    ::v-deep .el-form {
      .form-title {
        height: 30px;
        border-bottom: 1px solid #000;
        margin-bottom: 10px;
      }
      .el-radio-group {
        .el-radio {
          width: 33%;
          margin-left: 0px;
        }
      }
      .del-btn {
        float: right;
      }
      .minAmount-input {
        width: 50%;
        display: inline-block;
      }
    }
  }
}
.tip {
  color: #666;
  font-size: 12px;
}
.item-box {
  width: 1000px;
}
.pagestip {
  padding: 10px 20px;
  background-color: rgba(64, 158, 255, 0.19);
  border: 1px solid var(--color-primary);
  color: #000;
  font-size: 14px;
  font-weight: 600;
  align-items: center;
  width: 600px;
}
</style>
