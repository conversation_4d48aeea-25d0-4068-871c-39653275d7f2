<template>
  <div class="app-container">
    <div class="table-container">
      <!-- tabs -->
      <el-form>
        <el-tabs v-model="currentTab" type="card" @tab-click="clickTab">
          <el-tab-pane v-for="tabs in tabList" :key="tabs.name" :label="tabs.title" :name="tabs.name"></el-tab-pane>
        </el-tabs>

        <el-form-item>
          <div>
            <Authority auth="/brand-rebate/rebate-setting/:edit">
              <el-button @click="openAddPeriod" icon="el-icon-plus" size="small" type="primary">添加活动时间</el-button>
            </Authority>
          </div>
        </el-form-item>
      </el-form>
      <el-table :data="list" v-loading.body="listLoading">
        <el-table-column label="序号" type="index" :index="indexMethod"> </el-table-column>
        <el-table-column align="center" label="活动时间" prop="periodDesc"></el-table-column>
        <el-table-column align="center" label="状态" prop="statusName"> </el-table-column>
        <el-table-column align="center" label="操作" fixed="right" width="150px">
          <template slot-scope="scope">
            <Authority auth="/brand-rebate/rebate-setting/:edit">
              <el-button type="text" v-if="scope.row.status === 'FUTURE'" @click="onDelete(scope.row.id)">删除</el-button>
            </Authority>
            <Authority auth="/brand-rebate/rebate-setting/:edit">
              <el-button type="text" v-if="scope.row.status === 'USE'" @click="onDisable(scope.row.id)">停用</el-button>
            </Authority>
            <Authority auth="/brand-rebate/rebate-setting/:edit">
              <el-button v-if="scope.row.status === 'STOPPED'" @click="onEnable(scope.row.id)" type="text">启用</el-button>
            </Authority>
            <el-button v-if="scope.row.status === 'END'" type="text">/</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination :current-page="pageNo" :page-size="pageSize" :disabled="listLoading" :page-sizes="[10, 20, 30, 40, 50, 100]" :total="total" @current-change="handleCurrentChange" @size-change="handleSizeChange" background layout="total, sizes, prev, pager, next, jumper"></el-pagination>
      </div>
      <el-dialog :before-close="handleClose" :visible.sync="dialogFormVisible" center title="添加活动时间" :close-on-click-modal="false">
        <div class="dialog-div">
          <div>
            <span class="commo-search-item-label">活动时间:</span>
            <el-date-picker v-if="timeType === 'MONTH'" v-model="addPeriodCmd.month" type="month" placeholder="选择月"> </el-date-picker>
            <el-date-picker v-if="timeType === 'QUARTER'" v-model="addPeriodCmd.year" type="year" placeholder="选择年"> </el-date-picker>
            <el-select v-if="timeType === 'QUARTER'" v-model="addPeriodCmd.quarter" placeholder="请选择季度" clearable>
              <el-option v-for="item in quarterOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </div>
        </div>
        <p class="dialog-text">添加的活动时间不能与已有的时间重复，并且不能添加历史时间</p>
        <div class="dialog-footer" slot="footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button @click="handleSure" type="primary">确 定</el-button>
        </div>
      </el-dialog>
    </div>
    <router-view />
  </div>
</template>
<script>
import { parseTime } from '@/utils';
import cloneDeep from 'lodash/cloneDeep';
import pick from 'lodash/pick';
import pickBy from 'lodash/pickBy';
import { listActivityPeriod, createActivityPeriod, disableActivityPeriod, enableActivityPeriod, deleteActivityPeriod } from '@/api/brand-rebate/rebate-setting';
export default {
  name: 'period-list',
  data() {
    const initFilter = {
      activityId: this.$route.params.id,
      timeType: ''
    };
    const addPeriodCmd = {
      activityId: this.$route.params.id,
      timeType: '',
      period: '', // 采货方式,
      month: '',
      year: '',
      quarter: ''
    };
    return {
      tabList: [
        {
          title: '月度返利时间',
          name: '1'
        },
        {
          title: '季度返利时间',
          name: '2'
        }
      ],
      quarterOptions: [
        {
          value: '01',
          label: '第一季度'
        },
        {
          value: '02',
          label: '第二季度'
        },
        {
          value: '03',
          label: '第三季度'
        },
        {
          value: '04',
          label: '第四季度'
        }
      ],
      currentTab: '1',
      timeType: 'MONTH',
      filterMore: false,
      listLoading: false,
      exportLoading: false,
      initFilter,
      addPeriodCmd,
      filter: cloneDeep(initFilter),
      pageNo: 1,
      pageSize: 10,
      total: 0,
      list: [],
      dialogFormVisible: false
    };
  },
  mounted() {
    const currentTab = sessionStorage.getItem('quarter') || '';
    if (currentTab) {
      this.currentTab = '2';
      this.timeType = 'QUARTER';
      sessionStorage.removeItem('quarter');
    } else {
      this.currentTab = '1';
      this.timeType = 'MONTH';
    }
    this.fetchData();
  },
  computed: {
    data() {
      return pickBy(this.filter, (val) => !!val);
    }
  },
  methods: {
    parseTime,
    onSearch() {
      this.fetchData();
    },
    // 序号技术
    indexMethod(index) {
      return index + 1 + (this.pageNo - 1) * this.pageSize;
    },
    onReset() {
      this.timeType = this.currentTab === '1' ? 'MONTH' : 'QUARTER';
      this.initFilter.timeType = this.timeType;
      this.filter = cloneDeep(this.initFilter);
      this.fetchData();
    },
    clickTab() {
      this.onReset();
    },
    handleCurrentChange(val) {
      this.pageNo = val;
      this.fetchData();
    },
    handleSizeChange(val) {
      this.pageNo = 1;
      this.pageSize = val;
      this.fetchData();
    },

    // 启用
    onEnable(id) {
      this.$confirm('确认是否启用?', '确认提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          enableActivityPeriod(id)
            .then((response) => {
              this.fetchData();
            })
            .finally(() => {
              this.listLoading = false;
            });
          this.$message({
            type: 'success',
            message: '启用成功！'
          });
        })
        .catch(() => {});
    },

    // 删除
    onDelete(id) {
      this.$confirm('确认是否删除?', '确认提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          deleteActivityPeriod(id)
            .then((response) => {
              this.fetchData();
            })
            .finally(() => {
              this.listLoading = false;
            });
          this.$message({
            type: 'success',
            message: '删除成功！'
          });
        })
        .catch(() => {});
    },
    // 停用
    onDisable(id) {
      this.$confirm('确认是否禁用?', '确认提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          disableActivityPeriod(id)
            .then((response) => {
              this.fetchData();
            })
            .finally(() => {
              this.listLoading = false;
            });
          this.$message({
            type: 'success',
            message: '禁用成功！'
          });
        })
        .catch(() => {});
    },

    /**
     * 查询活动时间
     */
    fetchData() {
      this.listLoading = true;
      const listQuery = pick(this, ['pageNo', 'pageSize', 'data']);
      listQuery.data = {
        activityId: this.filter.activityId,
        type: this.timeType
      };
      listActivityPeriod(listQuery)
        .then((response) => {
          const { list = [], total = 0 } = response.data || {};
          this.list = list;
          this.total = total;
        })
        .finally(() => {
          this.listLoading = false;
        });
    },

    openAddPeriod() {
      this.dialogFormVisible = true;
    },
    handleClose() {
      this.addPeriodCmd.year = '';
      this.addPeriodCmd.month = '';
      this.addPeriodCmd.quarter = '';
      this.addPeriodCmd.period = '';
      this.dialogFormVisible = false;
    },
    /**
     * 新增活动时间
     */
    handleSure() {
      let period = this.addPeriodCmd.period;
      if (this.timeType === 'MONTH') {
        period = parseTime(this.addPeriodCmd.month.getTime(), '{y}{m}');
      } else {
        const year = parseTime(this.addPeriodCmd.year.getTime(), '{y}');
        period = year + this.addPeriodCmd.quarter;
      }
      const createCreditBackPeriodCmd = {
        activityId: this.addPeriodCmd.activityId,
        type: this.timeType,
        period: period,
        creditBackType: 'BRAND'
      };
      createActivityPeriod({
        ...createCreditBackPeriodCmd
      }).then((res) => {
        if (res.code === '0') {
          this.$message({
            message: '操作成功',
            type: 'success'
          });
          this.dialogFormVisible = false;
          this.fetchData();
        }
      });
    }
  }
};
</script>


<style lang='scss' scoped>
.filter-list {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;

  .filter-item {
    font-size: 14px;
    box-sizing: border-box;
    display: flex;
    margin-bottom: 16px;
    padding: 0 24px;
    width: 33.333%;
    min-width: 334px;
    text-align: right;
    align-items: center;

    .label {
      padding-right: 16px;
      white-space: nowrap;
      display: inline-block;
      line-height: 32px;
    }

    .content {
      display: flex;
      flex: 1 1;
      align-items: center;
    }

    &.btns-open {
      width: 100%;
    }
  }
}

.create-btn {
  margin-left: 10px;
  vertical-align: middle;
  margin-right: 10px;
}
</style>
