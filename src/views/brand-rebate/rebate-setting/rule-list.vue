<template>
  <div class="app-container">
    <div class="table-container">
      <Instructions :showTitle="true" :colNum="0">
        <template slot="title">
          <p>返利品牌：{{ brandName }}</p>
        </template>
      </Instructions>
      <!-- tabs -->
      <el-form>
        <el-tabs v-model="currentTab" type="card" @tab-click="clickTab">
          <el-tab-pane v-for="tabs in tabList" :key="tabs.name" :label="tabs.title" :name="tabs.name"></el-tab-pane>
        </el-tabs>
      </el-form>
      <el-table :data="list" v-loading.body="listLoading">
        <el-table-column label="修改时间" width="auto">
          <template slot-scope="scope">
            {{
            parseInt(scope.row.updateDate)
            | parseTime('{y}-{m}-{d} {h}:{i}:{s}')
            }}</template>
        </el-table-column>
        <el-table-column align="center" label="修改人" prop="updateByName">
        </el-table-column>
        <el-table-column label="修改后规则" width="auto">
          <template slot-scope="{row}">
            <div v-if="!row.updateAfter">/</div>
            <div v-else>
              <div v-for="(item, index) in parseList(row.updateAfter)" :key="index">
                {{ item }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" label="修改类型" prop="updateTypeName">
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination :current-page="pageNo" :page-size="pageSize" :disabled="listLoading"
          :page-sizes="[10, 20, 30, 40,50,100]" :total="total" @current-change="handleCurrentChange"
          @size-change="handleSizeChange" background layout="total, sizes, prev, pager, next, jumper"></el-pagination>
      </div>
    </div>
    <router-view />
  </div>
</template>
<script>
  import { parseTime } from '@/utils';
  import cloneDeep from 'lodash/cloneDeep';
  import pick from 'lodash/pick';
  import pickBy from 'lodash/pickBy';
  import { listRule } from '@/api/brand-rebate/rebate-setting';

  export default {
    name: 'rule-list',
    data() {
      const initFilter = {
        activityId: this.$route.params.id,
        timeType: ''
      };

      return {
        brandName: this.$route.query.brandName,
        tabList: [
          {
            title: '月度返利规则记录',
            name: '1'
          },
          {
            title: '季度返利规则记录',
            name: '2'
          }
        ],
        currentTab: '1',
        timeType: 'MONTH',
        filterMore: false,
        listLoading: false,
        exportLoading: false,
        initFilter,
        filter: cloneDeep(initFilter),
        pageNo: 1,
        pageSize: 10,
        total: 0,
        list: [],
        dialogFormVisible: false
      };
    },
    mounted() {
      const currentTab = sessionStorage.getItem('quarter') || '';
      if (currentTab) {
        this.currentTab = '2';
        this.timeType = 'QUARTER';
        sessionStorage.removeItem('quarter');
      } else {
        this.currentTab = '1';
        this.timeType = 'MONTH';
      }
      this.fetchData();
    },
    computed: {
      data() {
        return pickBy(this.filter, (val) => !!val);
      }
    },
    methods: {
      parseTime,
      onSearch() {
        this.fetchData();
      },

      parseList(data) {
        if (!data) {
          return [];
        }
        const dataList = data.split('\n');
        return dataList;
      },

      onReset() {
        this.timeType = this.currentTab === '1' ? 'MONTH' : 'QUARTER';
        this.initFilter.timeType = this.timeType;
        this.filter = cloneDeep(this.initFilter);
        this.fetchData();
      },
      clickTab() {
        this.onReset();
      },
      handleCurrentChange(val) {
        this.pageNo = val;
        this.fetchData();
      },
      handleSizeChange(val) {
        this.pageNo = 1;
        this.pageSize = val;
        this.fetchData();
      },

      /**
       * 查询活动规则修改记录
       */
      fetchData() {
        this.listLoading = true;
        const listQuery = pick(this, ['pageNo', 'pageSize', 'data']);
        listQuery.data = {
          activityId: this.filter.activityId,
          type: this.timeType
        };
        listRule(listQuery)
          .then((response) => {
            const { list = [], total = 0 } = response.data || {};
            this.list = list;
            this.total = total;
          })
          .finally(() => {
            this.listLoading = false;
          });
      }
    }
  };
</script>


<style lang='scss' scoped>
  .filter-list {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start;

    .filter-item {
      font-size: 14px;
      box-sizing: border-box;
      display: flex;
      margin-bottom: 16px;
      padding: 0 24px;
      width: 33.333%;
      min-width: 334px;
      text-align: right;
      align-items: center;

      .label {
        padding-right: 16px;
        white-space: nowrap;
        display: inline-block;
        line-height: 32px;
      }

      .content {
        display: flex;
        flex: 1 1;
        align-items: center;
      }

      &.btns-open {
        width: 100%;
      }
    }
  }

  .create-btn {
    margin-left: 10px;
    vertical-align: middle;
    margin-right: 10px;
  }
</style>
