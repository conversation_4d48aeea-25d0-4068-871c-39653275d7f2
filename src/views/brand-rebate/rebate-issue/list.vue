<template>
  <div class="app-container">
    <div class="table-container">
      <el-tabs class="custom-border-tabs" v-model="activeName">
        <el-tab-pane label="月度返利发放" name="month-rebate-issue">
          <RebateIssueList
            :activeName="activeName"
            :timeType="'MONTH'"
            v-if="activeName === 'month-rebate-issue'"
            :ref="activeName"
          ></RebateIssueList>
        </el-tab-pane>
        <el-tab-pane label="季度返利发放" name="quarter-rebate-issue">
          <RebateIssueList
            :activeName="activeName"
            :timeType="'QUARTER'"
            v-if="activeName === 'quarter-rebate-issue'"
            :ref="activeName"
          ></RebateIssueList>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script>
import RebateIssueList from './components/RebateIssueList';
export default {
  name: 'rebate-brand-issue-list',
  components: {
    RebateIssueList
  },
  data() {
    return {
      activeName: 'month-rebate-issue'
    };
  },
  activated() {
    this.$refs[this.activeName] && this.$refs[this.activeName].fetchData();
  },
  methods: {}
};
</script>

<style lang="scss" scoped>
::v-deep.el-tabs--card > .el-tabs__header {
  background: #fff;
}
::v-deep.el-tabs__header {
  margin: 0;
}
</style>
