<template>
  <div>
    <div class="table-container">
      <Instructions :showTitle="true" :colNum="0">
        <!-- 标题 -->
        <template slot="title">
          <p>返利品牌：{{ activityBrief.brandName }}</p>
          <p>
            <template v-if="timeType === 'MONTH'"> 活动时间：{{ activityBrief.period.substring(0, 4) }}年{{ activityBrief.period.substring(4, 6) }}月 </template>
            <template v-else> 活动时间：{{ activityBrief.period.substring(0, 4) }}年第{{ activityBrief.period.substring(5, 6) }}季度 </template>
          </p>
          <p>采货类型:{{ activityBrief.purchaseTypeName }}</p>
        </template>
      </Instructions>
      <form @submit.prevent="onSearch" class="commo-search-container commo-search-flex commo-search-margin-bottom10">
        <div class="commo-search-item">
          <span class="commo-search-item-label">商品名称:</span>
          <div class="commo-search-item-content">
            <el-input clearable v-model="initFilter.commodityName"></el-input>
          </div>
        </div>
        <div class="commo-search-item">
          <span class="commo-search-item-label">商品规格编码:</span>
          <div class="commo-search-item-content">
            <el-input clearable v-model="initFilter.specCode"></el-input>
          </div>
        </div>
        <div class="commo-search-item">
          <span class="commo-search-item-label">商品规格标识:</span>
          <div class="commo-search-item-content">
            <el-input clearable v-model="initFilter.skuId"></el-input>
          </div>
        </div>
        <div class="commo-search-item">
          <span class="commo-search-item-label">是否参与返利:</span>
          <div class="commo-search-item-content">
            <el-select v-model="initFilter.isEnable" placeholder="全部" clearable>
              <el-option key="1" label="参与返利" value="1"> </el-option>
              <el-option key="0" label="不参与返利" value="0"> </el-option>
            </el-select>
          </div>
        </div>
        <div class="commo-search-btns">
          <el-button native-type="submit" size="small" type="primary">查询</el-button>
          <el-button @click="onReset" size="small">重置</el-button>
        </div>
      </form>
      <el-table :data="list" element-loading-text="加载中" fit highlight-current-row ref="multipleTable" v-loading.body="listLoading">
        <el-table-column align="center" label="商品信息" width="300px">
          <template slot-scope="scope">
            <div class="commodity-box">
              <img class="imgUrl" :src="scope.row.thumbnailUrl" alt />
              <div class="commodity-name">
                <p class="commodity-name--name">
                  {{ scope.row.name }}
                </p>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" label="商品ID" prop="commodityId"> </el-table-column>
        <el-table-column align="center" label="是否参与返利">
          <template slot-scope="scope">
            <template v-if="scope.row.isEnable === '1'"> 参与 </template>
            <template v-else>
              <p>不参与</p>
              <p>{{ scope.row.updateDate | parseTime('{y}.{m}.{d}') }}</p>
            </template>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination :current-page="pageNo" :disabled="listLoading" :page-size="pageSize" :page-sizes="[10, 20, 30, 40, 50, 100]" :total="total" @current-change="handleCurrentChange" @size-change="handleSizeChange" background layout="total, sizes, prev, pager, next, jumper"></el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import { listRebateCommodity, getRebateActivityBrief } from '@/api/brand-rebate/rebate-issue';
import pick from 'lodash/pick';
import pickBy from 'lodash/pickBy';
import cloneDeep from 'lodash/cloneDeep';
export default {
  name: 'issue-commodity',
  props: {
    timeType: {
      type: String,
      default: 'MONTH'
    }
  },
  data() {
    const filter = {
      commodityName: '', // 商品名称
      specCode: '', // 规格编码
      skuId: '', // 规格标识
      isEnable: '', // 是否返利
      activitySnapshootId: this.$route.query.activitySnapshootId,
      creditBackType: 'BRAND'
    };
    const activityBrief = {
      brandId: '',
      brandName: '',
      id: '',
      period: '',
      purchaseType: '',
      purchaseTypeName: '',
      thresholdAmount: 0,
      type: '',
      updateDate: null
    };
    return {
      exportLoading: false,
      saveLoading: false,
      formLabelWidth: '80px',
      dialogFormVisible: false,
      formUserType: false,
      filter,
      initFilter: cloneDeep(filter),
      activityBrief,
      list: [],
      listLoading: true,
      pageNo: 1,
      pageSize: 10,
      total: 0
    };
  },
  computed: {
    // 列表数据过滤
    data() {
      return pickBy(this.initFilter, (val) => !!val);
    }
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      this.fetchData();
      getRebateActivityBrief(this.initFilter.activitySnapshootId).then((res) => {
        this.activityBrief = res.data;
      });
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.pageNo = 1;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.pageNo = val;
      this.fetchData();
    },
    // 获取列表数据
    fetchData() {
      this.listLoading = true;
      const listQuery = pick(this, ['pageNo', 'pageSize', 'data']);
      listRebateCommodity(listQuery)
        .then((response) => {
          const { list = [], total = 0 } = response.data || {};
          this.list = list;
          this.total = total;
        })
        .finally(() => {
          this.listLoading = false;
        });
    },
    // 查询
    onSearch() {
      this.pageNo = 1;
      this.pageSize = 10;
      this.fetchData();
    },
    // 重置
    onReset() {
      this.pageNo = 1;
      this.pageSize = 10;
      this.initFilter = cloneDeep(this.filter);
      this.fetchData();
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
@import 'src/styles/goods-table.scss';
.table-container-top {
  margin-bottom: 10px;
}
.box {
  padding: 20px;
  .row {
    position: relative;
    display: flex;
    .item {
      flex-basis: 33.33%;
      color: #333;
      font-size: 12px;
      text-align: center;
      .amount {
        cursor: pointer;
        display: block;
        font-size: 24px;
        margin: 6px 0 10px;
        color: var(--color-primary);
        text-decoration: none;
      }
      .amount-x {
        color: #f44;
      }
      .amount-yesterday {
        color: #999;
      }
    }
  }
}
.filter-list .filter-item {
  min-width: 25%;
  .el-select {
    width: 100%;
  }
}
.commodity-box {
  display: flex;
  align-items: center;
  padding-left: 30px;
  .commodity-name {
    margin-left: 10px;
    &--name {
      margin-top: 0;
      display: -webkit-box;
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      width: 190px;
      line-height: 18px;
      min-height: 17px;
      max-height: 34;
      margin-bottom: 0px;
      text-align: left;
    }
    &--global {
      width: 40px;
      height: 18px;
      color: #fff;
      background: red;
      border-radius: 2px;
      text-align: center;
      line-height: 18px;
      font-size: 14px;
      display: inline-block;
      margin-right: 6px;
    }
    &--rebate {
      color: #d7092f;

      background: #ffe8e8;
    }
  }
}
.imgUrl {
  width: 70px;
  height: 70px;
}
</style>
