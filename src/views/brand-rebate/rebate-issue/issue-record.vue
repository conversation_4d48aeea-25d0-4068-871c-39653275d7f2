<!-- 发放记录 -->
<template>
  <div class="app-container">
    <div class="table-container">
      <Instructions :showTitle="true" :colNum="2" v-loading="detailLoading">
        <template slot="title">
          <p>返利品牌：{{ detail.brandName || '/' }}</p>
          <p v-if="detail.type === 'MONTH'">活动时间：{{ detail.period && detail.period.substring(0, 4) }}年{{ detail.period && detail.period.substring(4, 6) }}月</p>
          <p v-else>活动时间：{{ detail.period && detail.period.substring(0, 4) }}年第{{ detail.period && detail.period.substring(5, 6) }}季度</p>
          <p>采货类型：{{ detail.purchaseTypeName || '/' }}</p>
        </template>
        <template slot="col1">
          <div class="content__row">
            <p class="content__label">已发放分销商数：</p>
            <p>{{ detail.totalAuditDistributorQuantity }}个</p>
          </div>
        </template>
        <template slot="col2">
          <div class="content__row">
            <p class="content__label">返利发放总金额：</p>
            <p>¥{{ detail.totalIssuedActualVirtualCredit }}</p>
          </div>
        </template>
      </Instructions>
      <form @submit.prevent="onSearch" class="commo-search-container commo-search-flex commo-search-margin-bottom10">
        <div class="commo-search-item">
          <span class="commo-search-item-label">店铺名称:</span>
          <div class="commo-search-item-content">
            <el-input clearable v-model="filter.shopName"></el-input>
          </div>
        </div>
        <div class="commo-search-item">
          <span class="commo-search-item-label">登录手机号:</span>
          <div class="commo-search-item-content">
            <el-input clearable v-model="filter.mobile"></el-input>
          </div>
        </div>
        <div class="commo-search-item">
          <span class="commo-search-item-label">专属顾问:</span>
          <div class="commo-search-item-content">
            <el-select filterable clearable size="small" style="width: 100%" v-model="filter.customerServiceId">
              <el-option :key="idx" :label="item.name + item.mobile" :loading="customerServiceVOLoading" :value="item.id" v-for="(item, idx) in customerServiceVO"></el-option>
            </el-select>
          </div>
        </div>
        <div class="commo-search-btns">
          <el-button native-type="submit" size="small" type="primary">查询</el-button>
          <el-button @click="onReset" size="small">重置</el-button>
          <Authority auth="/brand-rebate/rebate-issue/:export">
            <el-button :loading="exportLoading" @click="onExport" size="small">导出</el-button>
          </Authority>
        </div>
      </form>
      <el-table height="600" :data="list" element-loading-text="加载中" fit highlight-current-row ref="multipleTable" v-loading.body="listLoading">
        <el-table-column align="center" label="店铺名称" prop="shopName"></el-table-column>
        <el-table-column align="center" label="登录手机号" prop="mobile"></el-table-column>
        <el-table-column align="center" label="采货金额（元）参与返利" prop="totalVirtualCredit"></el-table-column>
        <el-table-column align="center" label="返利比例（%）" prop="backRate"></el-table-column>
        <el-table-column align="center" label="返利发放金额（元）" prop="actualVirtualCredit"></el-table-column>
        <el-table-column align="center" label="参与返利的总订单数" prop="totalQuantity"></el-table-column>
        <el-table-column align="center" label="专属顾问" prop="customerServiceVO.name"></el-table-column>
        <el-table-column align="center" label="操作">
          <template slot-scope="scope">
            <Authority auth="/brand-rebate/rebate-issue/purchase-details/:view">
              <el-button @click="jump(scope.row)" size="small" type="text">采购明细</el-button>
            </Authority>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination :current-page="pageNo" :disabled="listLoading" :page-size="pageSize" :page-sizes="[10, 20, 30, 40, 50, 100]" :total="total" @current-change="handleCurrentChange" @size-change="handleSizeChange" background layout="total, sizes, prev, pager, next, jumper"></el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import { listAuditIssuePage, getIssueDetail, auditIssueExportExcel } from '@/api/brand-rebate/rebate-issue'; // 接口地址
import { customerList } from '@/api/distributorManagement/distributor/list';
import pickBy from 'lodash/pickBy'; // 返回一个新对象，值由真值组成
import cloneDeep from 'lodash/cloneDeep'; // 对象深拷贝
import download from '@/utils/download';
import { parseTime } from '@/utils';
export default {
  name: 'issue-record',
  data() {
    const initFilter = {
      // 搜索条件初始值
      shopName: '', // 店铺名称
      mobile: '', // 登录手机号
      customerServiceId: '', // 专属顾问
      activitySnapshootId: ''
    };
    return {
      detail: {},
      detailLoading: true,
      listLoading: true,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      list: [], // 列表
      initFilter, // 搜索条件初始值加入到Data
      filter: cloneDeep(initFilter), // 搜索条件的值
      customerServiceVO: [], // 专属运营
      customerServiceVOLoading: true,
      exportLoading: false,
      form: {
        name: '',
        radio: ''
      }
    };
  },
  computed: {
    // 表单接口传入的参数
    interfaceData() {
      const listQuery = pickBy(this.filter, (val) => !!val);
      listQuery.status = 'ISSUED';
      return listQuery;
    }
  },
  mounted() {
    this.filter.activitySnapshootId = JSON.parse(sessionStorage.getItem('audit-issue')).id;
    // 页面加载事 自动运行
    this.fetchDataDetail();
    this.fetchData();
    this.fetchCustomerList();
  },
  methods: {
    // 获取列表上的汇总数据
    fetchDataDetail() {
      this.detailLoading = true;
      getIssueDetail(this.filter.activitySnapshootId)
        .then((response) => {
          this.detail = response.data || {};
        })
        .finally(() => {
          this.detailLoading = false;
        });
    },
    // 获取专属顾问列表
    fetchCustomerList() {
      this.customerServiceVOLoading = true;
      const listQuery = {};
      customerList(listQuery)
        .then((response) => {
          this.customerServiceVO = response.data;
        })
        .finally(() => {
          this.customerServiceVOLoading = false;
        });
    },
    // 跳转其他页面
    jump(row) {
      sessionStorage.setItem(
        'adjust-order',
        JSON.stringify({
          id: row.id
        })
      );
      const path = '/brand-rebate/rebate-issue/audit-issue/purchase-details';
      this.$router.push({
        path
      });
    },
    // 重置
    onReset() {
      this.pageNo = 1;
      this.pageSize = 10;
      this.filter = cloneDeep(this.initFilter);
      this.filter.activitySnapshootId = JSON.parse(sessionStorage.getItem('audit-issue')).id;
      this.fetchData();
    },
    // 搜索
    onSearch() {
      this.pageNo = 1;
      this.pageSize = 10;
      this.fetchData();
    },
    onExport() {
      this.exportLoading = true;
      auditIssueExportExcel(this.interfaceData)
        .then((res) => {
          download(res, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', `审核发放-${parseTime(Date.now(), '{y}-{m}-{d}-{h}:{i}:{s}')}.xlsx`);
        })
        .finally(() => {
          this.exportLoading = false;
        });
    },

    // 获取列表信息
    fetchData() {
      this.listLoading = true;
      const listQuery = {
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        data: this.interfaceData
      };
      listAuditIssuePage(listQuery)
        .then((response) => {
          const { list = [], total = 0 } = response.data || {};
          this.list = list;
          this.total = total;
        })
        .finally(() => {
          this.listLoading = false;
        });
    },
    // 分页控制pageSize
    handleSizeChange(val) {
      this.pageSize = val;
      this.pageNo = 1;
      this.fetchData();
    },
    // 分页控制pageNo
    handleCurrentChange(val) {
      this.pageNo = val;
      this.fetchData();
    }
  }
};
</script>

<style lang="scss" scoped>
.tips {
  margin-left: 70px;
}
.btn-all-issue {
  margin-bottom: 10px;
}
</style>
