<template>
  <div>
    <div>
      <form @submit.prevent="onSearch" class="commo-search-container commo-search-flex commo-search-margin-bottom10">
        <div class="commo-search-item">
          <span class="commo-search-item-label">品牌名称:</span>
          <div class="commo-search-item-content">
            <el-select size="small" v-model="initFilter.brandIds" placeholder="请选择" clearable multiple class="brandIds">
              <el-option v-for="item in brandList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
            </el-select>
          </div>
        </div>
        <div class="commo-search-item">
          <span class="commo-search-item-label">发放状态:</span>
          <div class="commo-search-item-content">
            <el-select size="small" v-model="initFilter.issueStatus" placeholder="全部" clearable>
              <el-option key="0" label="未完成" value="0"> </el-option>
              <el-option key="1" label="已完成" value="1"> </el-option>
            </el-select>
          </div>
        </div>
        <div class="commo-search-item">
          <span class="commo-search-item-label">采货类型:</span>
          <div class="commo-search-item-content">
            <el-select size="small" v-model="initFilter.purchaseType" placeholder="全部" clearable>
              <el-option key="PURCHASE" label="采销" value="PURCHASE"> </el-option>
              <el-option key="DROP_SHIPPING" label="一件代发" value="DROP_SHIPPING"> </el-option>
            </el-select>
          </div>
        </div>
        <div class="commo-search-item" v-if="timeType === 'MONTH'">
          <span class="commo-search-item-label">时间:</span>
          <div class="commo-search-item-content">
            <el-date-picker size="small" style="width: 147px !important" v-model="initFilter.startPeriod" type="month" placeholder="开始月份"> </el-date-picker>
            <el-date-picker size="small" style="width: 147px !important" v-model="initFilter.endPeriod" type="month" placeholder="结束月份"> </el-date-picker>
          </div>
        </div>
        <div class="commo-search-item" v-else>
          <span class="commo-search-item-label">时间:</span>
          <div>
            <el-date-picker size="small" style="width: 147px !important" v-model="initFilter.year" type="year" placeholder="选择年"> </el-date-picker>
            <el-select size="small" style="width: 147px !important" v-model="initFilter.quarter" placeholder="全部" clearable>
              <el-option v-for="item in quarterOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </div>
        </div>
        <div class="commo-search-btns">
          <el-button native-type="submit" size="small" type="primary">查询</el-button>
          <el-button @click="onReset" size="small">重置</el-button>
        </div>
      </form>
      <el-table :data="list" element-loading-text="加载中" fit highlight-current-row ref="multipleTable" v-loading.body="listLoading">
        <el-table-column align="center" label="返利品牌" prop="brandName"></el-table-column>
        <el-table-column align="center" label="活动时间">
          <template slot-scope="scope">
            <template v-if="timeType === 'MONTH'"> {{ scope.row.period.substring(0, 4) }}年 {{ scope.row.period.substring(4, 6) }}月 </template>
            <template v-else> {{ scope.row.period.substring(0, 4) }}年 {{ scope.row.period.substring(5, 6) }}季度 </template>
          </template>
        </el-table-column>
        <el-table-column align="center" label="采货类型" prop="purchaseTypeName"></el-table-column>
        <el-table-column align="center" label="返利规则">
          <template slot-scope="scope">
            <p v-for="(item, index) in scope.row.ruleText" :key="index">{{ index + 1 }}、累计购物在￥{{ item.minAmount }}-{{ item.maxAmount }},返利{{ item.backRate * 100 }}%；</p>
          </template>
        </el-table-column>
        <el-table-column align="center" label="审核完成分销商数" prop="totalQuantity">
          <template slot-scope="scope"> {{ scope.row.totalQuantity }}个 </template>
        </el-table-column>
        <el-table-column align="center" label="分销商发放数据">
          <template slot-scope="scope">
            <p v-if="scope.row.totalFinishedQuantity >= 0">已发放：{{ scope.row.totalFinishedQuantity }} 个</p>
            <p v-if="scope.row.totalUnfinishedQuantity >= 0">未发放：{{ scope.row.totalUnfinishedQuantity }} 个</p>
          </template>
        </el-table-column>
        <el-table-column align="center" label="已发放金额" prop="totalIssueAmount">
          <template slot-scope="scope"> ￥{{ scope.row.totalIssueAmount }} </template>
        </el-table-column>
        <el-table-column align="center" label="发放状态" prop="issueStatusName"></el-table-column>
        <el-table-column align="center" label="操作" fixed="right" width="120px">
          <template slot-scope="scope">
            <div>
              <div v-if="scope.row.issueStatus === '0'">
                <Authority auth="/brand-rebate/rebate-issue/list/:edit">
                  <el-button type="text" @click="jump(scope.row)">审核发放</el-button>
                </Authority>
                <Authority auth="/brand-rebate/rebate-issue/issue-commodity/:view">
                  <router-link :to="`/brand-rebate/rebate-issue/issue-commodity?activitySnapshootId=${scope.row.id}`" class="link">
                    <el-button type="text">返利商品</el-button>
                  </router-link>
                </Authority>
              </div>
            </div>
            <div>
              <div v-if="scope.row.issueStatus !== '0'">
                <el-button type="text" @click="jumpIssueRecord(scope.row)">发放记录</el-button>
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination :current-page="pageNo" :disabled="listLoading" :page-size="pageSize" :page-sizes="[10, 20, 30, 40, 50, 100]" :total="total" @current-change="handleCurrentChange" @size-change="handleSizeChange" background layout="total, sizes, prev, pager, next, jumper"></el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import { listRebateIssuePage } from '@/api/brand-rebate/rebate-issue';
import { listAll } from '@/api/setting/commodity/brand';
import pick from 'lodash/pick';
import pickBy from 'lodash/pickBy';
import { parseTime } from '@/utils';
import cloneDeep from 'lodash/cloneDeep';
export default {
  name: 'RebateIssueList',
  components: {},
  props: {
    timeType: {
      type: String,
      default: 'MONTH'
    }
  },
  data() {
    const filter = {
      brandIds: [],
      issueStatus: '', // 发放状态
      purchaseType: '', // 采货方式,
      month: '',
      year: '',
      quarter: '',
      startPeriod: '',
      endPeriod: ''
    };
    return {
      exportLoading: false,
      saveLoading: false,
      formLabelWidth: '80px',
      dialogFormVisible: false,
      formUserType: false,
      brandList: [], // 品牌列表选项
      timeList: null, // 活动时间
      filter,
      initFilter: cloneDeep(filter),
      list: [],
      listLoading: true,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      distributorAccount: {}, // 分销商账户对象
      // 商品上架状态
      quarterOptions: [
        {
          value: '01',
          label: '第一季度'
        },
        {
          value: '02',
          label: '第二季度'
        },
        {
          value: '03',
          label: '第三季度'
        },
        {
          value: '04',
          label: '第四季度'
        }
      ]
    };
  },
  computed: {
    // 列表数据过滤
    data() {
      const obj = pickBy(this.initFilter, (val) => !!val);
      obj.type = this.timeType;
      if (this.timeType === 'MONTH' && this.initFilter.startPeriod) {
        obj.startPeriod = parseTime(this.initFilter.startPeriod.getTime(), '{y}{m}');
      }
      if (this.timeType === 'MONTH' && this.initFilter.endPeriod) {
        obj.endPeriod = parseTime(this.initFilter.endPeriod.getTime(), '{y}{m}');
      }
      if (this.timeType === 'QUARTER' && this.initFilter.year) {
        obj.year = parseTime(this.initFilter.year.getTime(), '{y}');
      }
      if (this.timeType === 'QUARTER' && this.initFilter.quarter) {
        obj.month = this.initFilter.quarter;
      }
      if (obj.year && obj.month) {
        obj.period = obj.year + obj.month;
      }
      return obj;
    }
  },
  created() {
    this.init();
    this.fetchBrand(); // 获取所有品牌
  },
  methods: {
    // 跳转到审核发放
    jump(row) {
      sessionStorage.setItem(
        'audit-issue',
        JSON.stringify({
          id: row.id
        })
      );
      const path = '/brand-rebate/rebate-issue/audit-issue/list';
      this.$router.push({
        path
      });
    },
    // 发放记录
    jumpIssueRecord(row) {
      sessionStorage.setItem(
        'audit-issue',
        JSON.stringify({
          id: row.id
        })
      );
      const path = '/brand-rebate/rebate-issue/issue-record';
      this.$router.push({
        path
      });
    },
    init() {
      this.onReset();
    },
    // 获取所有品牌
    fetchBrand() {
      listAll({}).then((rs) => {
        const res = rs.data.map((item) => ({
          value: item.id,
          label: item.name
        }));
        this.brandList = res;
      });
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.pageNo = 1;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.pageNo = val;
      this.fetchData();
    },
    // 获取列表数据
    fetchData() {
      this.listLoading = true;
      const listQuery = pick(this, ['pageNo', 'pageSize', 'data']);
      listRebateIssuePage(listQuery)
        .then((response) => {
          const { list = [], total = 0 } = response.data || {};
          this.list = list;
          this.total = total;
        })
        .finally(() => {
          this.listLoading = false;
        });
    },
    // 查询
    onSearch() {
      if (this.initFilter.endPeriod < this.initFilter.startPeriod) {
        this.$message.error('时间选择有误，结束月份大于开始月份');
        return;
      }
      this.pageNo = 1;
      this.pageSize = 10;
      this.fetchData();
    },
    // 重置
    onReset() {
      this.pageNo = 1;
      this.pageSize = 10;
      this.initFilter = cloneDeep(this.filter);
      this.fetchData();
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
@import 'src/styles/goods-table.scss';
.table-container-top {
  margin-bottom: 10px;
}
.box {
  padding: 20px;
  .row {
    position: relative;
    display: flex;
    .item {
      flex-basis: 33.33%;
      color: #333;
      font-size: 12px;
      text-align: center;
      .amount {
        cursor: pointer;
        display: block;
        font-size: 24px;
        margin: 6px 0 10px;
        color: var(--color-primary);
        text-decoration: none;
      }
      .amount-x {
        color: #f44;
      }
      .amount-yesterday {
        color: #999;
      }
    }
  }
}
.filter-list .filter-item {
  min-width: 25%;
  .el-select {
    width: 100%;
  }
}
</style>
