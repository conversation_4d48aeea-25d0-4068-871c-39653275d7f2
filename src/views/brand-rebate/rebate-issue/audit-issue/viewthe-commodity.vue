<template>
  <div class="app-container">
    <div class="table-container">
      <Instructions :showTitle="true" :colNum="0">
        <template slot="title">
          <p>返利品牌：{{ detail.brandName }}</p>
          <p>
            <template v-if="detail.period">
              <template v-if="detail.type === 'MONTH'">
                活动时间：{{ detail.period.substring(0, 4) }}年{{
                  detail.period.substring(4, 6)
                }}月
              </template>
              <template v-else>
                活动时间：{{ detail.period.substring(0, 4) }}年第{{
                  detail.period.substring(5, 6)
                }}季度
              </template>
            </template>
          </p>
          <p>采货类型:{{ detail.purchaseTypeName }}</p>
          <p>订单编号：{{ detail.orderNo }}</p>
        </template>
      </Instructions>
      <el-table
        :data="list"
        element-loading-text="加载中"
        fit
        highlight-current-row
        ref="multipleTable"
        v-loading.body="listLoading"
      >
        <el-table-column align="center" label="商品信息" width="300px">
          <template slot-scope="scope">
            <div class="commodity-box">
              <img class="imgUrl" :src="scope.row.imgUrl" alt />
              <div class="commodity-name">
                <p class="commodity-name--name commo-ellipsis-2">
                  {{ scope.row.commodityName }}
                </p>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          label="商品规格编码"
          prop="commodityCode"
        ></el-table-column>
        <el-table-column
          align="center"
          label="单价"
          prop="price"
        ></el-table-column>
        <el-table-column
          align="center"
          label="购买数量"
          prop="quantity"
        ></el-table-column>
        <el-table-column
          align="center"
          label="支付金额"
          prop="payAmount"
        ></el-table-column>
        <el-table-column
          align="center"
          label="退款金额"
          prop="actualRefundAmount"
        ></el-table-column>
        <el-table-column align="center" label="人工扣除金额">
          <template slot-scope="scope">
            <p>{{ scope.row.adjustAmount || '0' }}</p>
            <p v-if="scope.row.isOverlayCommodity == '0'">
              (已参与单品返利,不支持品牌返利)
            </p>
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          label="实收金额（扣除退款)"
          prop="actualPayAmount"
        ></el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          :current-page="pageNo"
          :disabled="listLoading"
          :page-size="pageSize"
          :page-sizes="[10, 20, 30, 40, 50, 100]"
          :total="total"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
          background
          layout="total, sizes, prev, pager, next, jumper"
        ></el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getSnapshotOrder,
  creditBackOrderDetailList
} from '@/api/brand-rebate/rebate-audit'; // 接口地址
export default {
  name: 'viewthe-commodity',
  data() {
    return {
      id: this.$route.params.id, // id
      orderId: '', // 订单id
      activitySnapshootId: this.$route.query.activitySnapshootId, // 活动id
      detailLoading: false,
      listLoading: false,
      list: [],
      total: 0,
      pageNo: 1,
      pageSize: 20,
      detail: {}
    };
  },
  mounted() {
    const { id } = this.$route.params;
    const { orderId, activitySnapshootId } = this.$route.query;
    this.orderId = orderId;
    this.activitySnapshootId = activitySnapshootId;
    this.id = id;
    // 页面加载事 自动运行
    this.fetchData();
    this.fetchDataDetail();
  },
  methods: {
    // 获取列表上的汇总数据
    fetchDataDetail() {
      this.detailLoading = true;
      getSnapshotOrder(this.orderId, this.activitySnapshootId)
        .then((response) => {
          this.detail = response.data || {};
        })
        .finally(() => {
          this.detailLoading = false;
        });
    },
    // 获取列表信息
    fetchData() {
      this.listLoading = true;
      const listQuery = {
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        data: { creditBackOrderId: this.id }
      };
      creditBackOrderDetailList(listQuery)
        .then((response) => {
          const { list = [], total = 0 } = response.data || {};
          this.list = list;
          this.total = total;
        })
        .finally(() => {
          this.listLoading = false;
        });
    },
    // 分页控制pageSize
    handleSizeChange(val) {
      this.pageSize = val;
      this.pageNo = 1;
      this.fetchData();
    },
    // 分页控制pageNo
    handleCurrentChange(val) {
      this.pageNo = val;
      this.fetchData();
    }
  }
};
</script>

<style lang="scss" scoped>
.commodity-box {
  display: flex;
  .imgUrl {
    width: 55px;
    height: 55px;
    margin-right: 10px;
  }
}
</style>

