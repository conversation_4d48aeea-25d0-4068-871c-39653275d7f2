.box {
  padding: 50px;
  width: 100%;
  height: 100%;
  background-color: #fff;
}

.filter-list {
  margin-bottom: 10px;
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;

  .filter-item {
    font-size: 14px;
    box-sizing: border-box;
    display: flex;
    margin-bottom: 16px;
    padding: 0 24px;
    width: 33.333%;
    min-width: 334px;
    text-align: right;

    .label {
      padding-right: 16px;
      white-space: nowrap;
      display: inline-block;
      line-height: 32px;
    }

    .content {
      display: flex;
      flex: 1 1;
      align-items: center;
    }

    &.btns-open {
      display: block;
      width: 100%;
    }
  }
}

.thumbImg {
  width: 50px;
  height: 50px;
}

.clear-both {
  clear: both;
}

.btns-list {
  margin-top: 16px;
  margin-bottom: 16px;
}

.divide {
  margin: 0 6px;
  color: rgba(0, 0, 0, 0.65);
}

.time-tool {
  position: relative;
  padding: 0 2px;
  font-size: 12px;
  background-color: #67c23a;
  color: #fff;
  border-radius: 8px;

  &.takedown {
    background-color: #e6a23c;
  }

  .icon {
    position: absolute;
    top: -4px;
    right: -8px;
    color: var(--color-danger);
    font-size: 14px;
  }
}

.select-time-wrap {
  position: relative;
  display: inline-block;

  .time-btn {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 2;
  }

  ::v-deep .selectTime .el-input__inner {
    height: 32px;
    line-height: 32px;
  }

  .el-date-editor.el-input,
  .el-date-editor.el-input__inner {
    width: 80px;
  }
}

.el-button + .select-time-wrap,
.select-time-wrap + .el-button {
  margin-left: 10px;
}

.hide-goods {
  &:hover {
    .cancel {
      display: inline-block;
    }

    .hided {
      display: none;
    }
  }

  .cancel {
    display: none;
    color: var(--color-danger);
  }
}
::v-deep.el-select {
  width: 100%;
}

.list-sort--box {
  display: flex;
  .zhiding {
    color: #666;
    font-size: 36px;
    line-height: 33px;
    cursor: pointer;
    &:hover {
      color: #5F3BCE;
    }
  }
}
.header-tooltip-sort {
  width: 200px;
  text-align: justify;
  font-size: 11px;
}

::v-deep .el-table th > .cell {
  display: flex;
  justify-content: center;
  align-items: center;
}
