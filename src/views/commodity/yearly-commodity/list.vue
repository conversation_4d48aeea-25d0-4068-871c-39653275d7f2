<template>
  <div class="page-container" v-el-horizontal-scroll>
    <sy-normal-table ref="table" v-bind="table" />
    <Dialog :callback="refreshTable" ref="dialogRemark" type="COMMODITYMANAGE_REMARK"> </Dialog>
  </div>
</template>

<script>
import { list, goodsType, updateSaleStatus, exportByCondition } from '@/api/commodity/list';
import dict from '@/components/Common/dicts';
import { listAllBrandName } from '@/api/brand/brand-info';
import Dialog from '@/components/Dialog/index.vue';
import download from '@/utils/download';
import { parseTime } from '@/utils';
import cloneDeep from 'lodash/cloneDeep';
export default {
  name: 'commodity-yearly-list',
  components: { Dialog },
  data() {
    return {
      exportLoading: false
    };
  },
  computed: {
    table() {
      const that = this;
      return {
        initSearch: false,
        tip: [{ text: '说明：' }, { text: '1、维护在年框商品池管理当中的商品，仅可以通过年框推品清单推送给分销商使用，下单价格读取分销商【年框政策】台账当中的价格' }, { text: '2、一旦被引用至年框推品清单，那么商品的条形码不允许更改' }],
        filters: [
          {
            tag: 'el-input',
            prop: 'name',
            label: '商品名称',
            bind: { placeholder: '请输入商品名称' }
          },
          {
            tag: 'el-input',
            prop: 'id',
            label: '商品ID',
            bind: {
              placeholder: '请输入商品ID'
            }
          },
          {
            tag: 'sy-select',
            prop: 'categoryIds',
            label: '商品分组',
            bind: {
              placeholder: '请选择商品分组',
              multiple: true,
              filterable: true,
              clearable: true,
              showEye: true,
              flashOptions: true,
              options: async () => {
                const res = await goodsType();
                return res.data;
              },
              optionsProps: {
                label: 'name',
                value: 'id'
              }
            }
          },
          {
            tag: 'sy-select',
            prop: 'type',
            label: '商品类型',
            bind: {
              placeholder: '请选择商品类型',
              clearable: true,
              flashOptions: true,
              options: async () => await dict('COMMONODITY_TYPE')
            }
          },
          {
            tag: 'el-input',
            prop: 'specCodes',
            str2Arr: true,
            label: '商品规格编码',
            bind: { placeholder: '请输入商品规格编码,多个用逗号隔开' }
          },
          {
            tag: 'el-input',
            prop: 'skuId',
            label: '规格标识',
            bind: { placeholder: '请输入' }
          },
          {
            tag: 'sy-select',
            prop: 'brandIds',
            label: '商品品牌',
            bind: {
              placeholder: '请选择商品品牌',
              multiple: true,
              filterable: true,
              clearable: true,
              showEye: true,
              flashOptions: true,
              options: async () => {
                const res = await listAllBrandName();
                return res.data;
              },
              optionsProps: {
                label: 'name',
                value: 'id'
              }
            }
          },
          {
            tag: 'sy-select',
            prop: 'purchaseType',
            label: '采货类型',
            bind: {
              placeholder: '请选择采货类型',
              options: [
                { value: 'PURCHASE', label: '采销' },
                { value: 'DROP_SHIPPING', label: '一件代发' }
              ]
            }
          },
          {
            tag: 'sy-date-picker',
            prop: ['createBeginDate', 'createEndDate'],
            label: '创建时间',
            bind: {
              bind: {
                'start-placeholder': '开始日期',
                'end-placeholder': '结束日期',
                type: 'daterange',
                valueFormat: that.$utils.FORMAT.DATE_TIME,
                defaultTime: ['00:00:00', '23:59:59']
              }
            }
          },
          {
            tag: 'sy-select',
            prop: 'status',
            label: '商品状态',
            bind: {
              placeholder: '请选择商品状态',
              options: [
                { value: '1', label: '上架' },
                { value: '3', label: '下架' }
              ]
            }
          }
        ],
        btns: [
          {
            text: '导出',
            type: 'primary',
            confirm: '确定导出吗？',
            code: 'commodity-yearly-export',
            bind: {
              loading: that.exportLoading
            },
            call: ({ filtersValue }) => that.onExport(filtersValue)
          }
        ],
        columns() {
          return [
            {
              label: '商品名称',
              prop: 'name',
              multiLine: 4
            },
            {
              label: '商品ID',
              prop: 'id',
              width: 180
            },
            {
              label: '商品条形码',
              width: 120,
              render: (h, { row }) => (row.skuBriefs?.length ? row.skuBriefs[0].specCode : '-')
            },
            {
              label: '缩略图',
              prop: 'thumbnailUrl',
              type: 'image',
              width: 80
            },
            {
              label: '商品状态',
              prop: 'statusName',
              width: 130
            },
            {
              label: '库存',
              prop: 'stock',
              width: 130
            },
            {
              label: '销量',
              prop: 'salesVolume',
              width: 130
            },
            {
              label: '备注',
              prop: 'remarks',
              multiLine: 3
            },
            {
              label: '创建时间',
              prop: 'createDate',
              type: 'time',
              format: 'yyyy-MM-dd hh:mm:ss',
              width: 180
            },
            {
              label: '操作',
              width: 200,
              type: 'btns',
              itemBind: {
                fixed: 'right'
              },
              btns({ row }) {
                return [
                  {
                    text: '查看',
                    type: 'text',
                    code: 'commodity-yearly-detail',
                    call: () => {
                      that.$router.push('/commodity/yearly/detail/' + row.id);
                    }
                  },
                  {
                    text: '编辑',
                    type: 'text',
                    code: 'commodity-yearly-edit',
                    call: () => {
                      that.$router.push('/commodity/yearly/edit/' + row.id);
                    }
                  },
                  {
                    text: '修改备注',
                    type: 'text',
                    code: 'commodity-yearly-edit',
                    call: () => {
                      that.$refs['dialogRemark'].setVisible(true, row);
                    }
                  },
                  {
                    hide: row.status === '3',
                    text: '下架',
                    type: 'text',
                    code: 'commodity-yearly-edit',
                    confirm: '确定下架该商品吗？',
                    call: () => that.updateCommodityStatus('down', row.id)
                  },
                  {
                    hide: row.status === '1',
                    text: '上架',
                    type: 'text',
                    code: 'commodity-yearly-edit',
                    confirm: '确定上架该商品吗？',
                    call: () => that.updateCommodityStatus('up', row.id)
                  }
                ];
              }
            }
          ];
        },
        async search({ filtersValue, pageFilter }) {
          const params = {
            data: {
              ...filtersValue,
              commodityClass: 'DISTRIBUTION_YEARLY_POLICY'
            },
            ...pageFilter
          };
          const res = await list(params);
          return {
            list: res.data.list,
            total: res.data.total
          };
        }
      };
    }
  },
  activated() {
    this.refreshTable();
  },
  methods: {
    // 刷新表格
    refreshTable() {
      const ref = this.$refs.table;
      ref && ref.handlerSearch();
    },
    // 上下架
    updateCommodityStatus(type, id) {
      updateSaleStatus(type, [id]).then((res) => {
        if (res.success) {
          this.$message.success('操作成功');
          this.refreshTable();
        }
      });
    },
    // 导出商品
    onExport(filtersValue) {
      this.exportLoading = true;
      const params = cloneDeep(filtersValue);
      const specCodes = params?.specCodes;
      if (specCodes && specCodes.includes(',')) {
        params.specCodes = specCodes.split(',').filter((code) => !!code);
      }
      const listQuery = {
        ...params,
        commodityClass: 'DISTRIBUTION_YEARLY_POLICY'
      };
      exportByCondition(listQuery)
        .then((res) => {
          download(res, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', `年框商品池-${parseTime(Date.now(), '{y}-{m}-{d}-{h}:{i}:{s}')}.xlsx`);
        })
        .finally(() => {
          this.exportLoading = false;
        });
    }
  }
};
</script>

<style scoped lang="scss">
::v-deep .sy-table.el-table .table-img img {
  object-fit: cover;
  width: 50px;
  height: 50px;
}
</style>
