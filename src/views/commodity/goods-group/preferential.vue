<!-- 商品详情-优惠信息 -->
<template>
  <div class="preferential-box">
    <div
      class="preferential-row"
      v-if="coupons.activityContent && coupons.activityContent.length > 0"
    >
      <div class="preferential-row--title">优惠</div>
      <div
        class="preferential-row--div"
        @click="moreList(coupons.activityType)"
      >
        <div class="coupon-warp">
          <div
            v-for="(item, index) in coupons.activityContent.slice(0, 3)"
            :key="index"
            class="coupons-box"
          >
            <div class="coupons-box--rig">{{ item }}</div>
          </div>
          <div>{{ coupons.activityContent.length >= 4 ? '...' : '' }}</div>
        </div>
      </div>
      <span class="el-icon-arrow-right"></span>
    </div>
    <div
      class="preferential-row"
      v-if="
        moneyOff &&
        moneyOff.activityContent &&
        moneyOff.activityContent.length > 0
      "
    >
      <div class="preferential-row--title">满减</div>
      <div class="preferential-row--div">
        <div class="content-warp">
          <div
            v-for="(item, index) in moneyOff.activityContent"
            :key="index"
            class="content"
          >
            <span class="text-black">{{ item }}</span>
          </div>
        </div>
      </div>
    </div>
    <div
      class="preferential-row"
      v-if="
        rateOff && rateOff.activityContent && rateOff.activityContent.length > 0
      "
    >
      <div class="preferential-row--title">满折</div>
      <div class="preferential-row--div">
        <div class="content-warp">
          <div
            v-for="(item, index) in rateOff.activityContent"
            :key="index"
            class="content"
          >
            <span class="text-black">{{ item }}</span>
          </div>
        </div>
      </div>
    </div>
    <template
      v-if="giftOff.activityContent && giftOff.activityContent.length > 0"
    >
      <div class="preferential-row">
        <div class="preferential-row--title">满赠</div>
        <div class="preferential-row--div" @click="openGiftOff(giftOff)">
          <div class="content-warp">
            <div
              v-for="(item, index) in giftOff.activityContent.slice(0, 2)"
              :key="index"
              class="content"
            >
              <span class="text-black">{{ item }}</span>
            </div>
            {{ giftOff.activityContent.length >= 3 ? '...' : '' }}
          </div>
        </div>
        <span class="el-icon-arrow-right"></span>
      </div>
    </template>
  </div>
</template>

<script>
export default {
  data() {
    return {
      cops: 'https://oss.syounggroup.com/static/file/soyoung-zg/aliyun/soyoung-zg/mobile-terminal/goods-coupons-ico.png',
      popShowCouponList: false,
      popShowGiftOff: false,
      coupons: {}, // 优惠券
      moneyOff: {}, // 满减
      rateOff: {}, // 满折
      giftOff: {}, // 赠品
      couponList: [] // 弹出框优惠券列表
    };
  },

  components: {},
  props: {
    activityRuleBriefVOS: {
      type: Array,
      default: () => []
    }
  },
  watch: {},
  created() {
    this.getPageData();
  },

  methods: {
    getPageData() {
      const dataList = this.activityRuleBriefVOS;
      if (dataList && dataList.length > 0) {
        let coupons = {};
        let moneyOff = {};
        let rateOff = {};
        let giftOff = {};
        for (let i = 0; i < dataList.length; i++) {
          const item = dataList[i];
          if (item.activityType === 'COUPON') {
            if (coupons.length >= 4) {
              continue;
            }
            coupons = item;
          }
          switch (item.activityType) {
            case 'MONEY_OFF':
              moneyOff = item;
              break;
            case 'RATE_OFF':
              rateOff = item;
              break;
            case 'GIFT_OFF':
              giftOff = item;
              break;
            default:
              break;
          }
        }

        this.coupons = coupons;
        this.moneyOff = moneyOff;
        this.rateOff = rateOff;
        this.giftOff = giftOff;
      }
    },
    async moreList() {
      this.$message.info('预览不能进行操作哦');
    },
    hidePop() {
      this.popShowCouponList = false;
    },
    async tapCoupon(item) {},
    openGiftOff() {
      this.$emit('openGiftOff', this.giftOff);
    }
  }
};
</script>
<style lang='scss' scoped>
.preferential-box {
  background: #fff;
  border-radius: 8px;
  margin-bottom: 16px;
  padding-bottom: 7.5px;
}
.preferential-row {
  display: flex;
  align-items: center;
  padding: 0 16px 0 15px;
  position: relative;
  font-size: 13px;
  &--div {
    display: flex;
    flex: 1;
    align-items: center;
  }
  &--title {
    width: 74px;
    flex-shrink: 0;
  }
  .content-warp {
    display: flex;
    flex-wrap: wrap;
    .content {
      margin-right: 4.5px;
    }
  }
}

.goods-type-fl {
  background: rgba(215, 9, 47, 0.1);
  border-radius: 2px;
  color: #d7092f;
  font-size: 9px;
  width: 28px;
  height: 16px;
  text-align: center;
  line-height: 16px;
  display: inline-block;
  margin-right: 5px;
}
.preferential-row-tip {
  height: 18px !important;
  padding-bottom: 7.5px;
}
.subject {
  height: 10%;
  font-size: 14px;
  color: #222;
  text-align: center;
}
.subject .title-text {
  font-size: 15px;
  line-height: 47.5px;
}
.subject .close-warp {
  display: flex;
  position: absolute;
  right: 10px;
  top: 7.5px;
  width: 35px;
  height: 35px;
  -webkit-justify-content: center;
  justify-content: center;
  -webkit-align-items: center;
  align-items: center;
}
.subject .close-warp .close {
  width: 15px;
  height: 15px;
  background-image: url('https://oss.syounggroup.com/static/file/soyoung-zg/aliyun/newretail/mobile/images/common/close-square.png');
  background-size: cover;
}
.coupons-box {
  position: relative;
  display: flex;
  align-items: center;
  margin-left: 5px;
  font-size: 11px;
  color: #d7092f;
  border: 1px solid #d7092f;
  height: 20px;
  line-height: 34.5px;
  background: #fbe6ea;
  &--rig {
    padding: 0 7.5px;
  }
}
.coupon-warp {
  display: flex;
  align-items: center;
}
</style>