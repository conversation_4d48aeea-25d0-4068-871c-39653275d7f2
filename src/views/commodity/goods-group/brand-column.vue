<!-- 商品详情-品牌信息展示组件 -->
<template>
  <div class="brand-column-wrap" v-if="brandVO || goodsList.length > 0">
    <div class="brand-column-box" v-if="brandVO" @tap="linkBrand(brandVO.jumpData)">
      <img class="brand-column-box--img" :src="brandVO.logoUrl" />
      <div class="brand-column-box--rigbox">
        <div class="brand-column-box--div">
          <div class="flex1">
            <div class="brand-column-box--div--name">{{ brandVO.name }}</div>
            <div class="brand-column-box--div--tag" v-if="brandVO.isEnableChannel === '1'">可授权</div>
          </div>
          <span class="el-icon-arrow-right"></span>
        </div>
        <div class="brand-column-box--subtitle">{{ brandVO.slogan }}</div>
      </div>
    </div>
    <div class="brand-recommended-box">
      <div class="brand-goods" v-for="item in goodsList" :key="item.id" @click="link(item.id)">
        <div class="brand-goods-top">
          <img :src="item.thumbnailUrl" class="brand-goods-img" />
        </div>
        <div class="brand-goods-name">{{ item.name }}</div>
        <div class="brand-goods-info">
          <div class="brand-goods-info--price">
            ¥<span class="price">{{ item.price }}</span>
          </div>
          <div class="brand-goods-info--profits">毛利率{{ setGrossProfitRate(item.grossProfitRate) }}%</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { commodityListRecommendById, zgBrandGetBrandById } from '@/api/commodity/list';
export default {
  data() {
    return {
      brandVO: null,
      goodsList: [] // 推荐商品
    };
  },
  props: {
    interfaceData: {
      type: Object,
      default: () => {}
    }
  },
  components: {},

  watch: {},
  created() {
    this.getPageData();
  },

  methods: {
    no() {
      this.$message.info('预览不能进行操作哦');
    },
    getPageData() {
      const { brandVO, id } = this.interfaceData;
      if (brandVO) {
        zgBrandGetBrandById(brandVO.id).then((res) => {
          if (res.success) {
            this.brandVO = res.data;
          }
        });
      }
      commodityListRecommendById(id).then((res) => {
        this.goodsList = res.data.slice(0, 3);
      });
    },
    link() {
      this.no();
    },
    // 修改毛利率
    setGrossProfitRate(grossProfitRate) {
      return (grossProfitRate * 100).toFixed();
    },
    // 进入品牌 微页面
    linkBrand() {
      this.no();
    }
  }
};
</script>
<style lang="scss" scoped>
.brand-column-wrap {
  border-radius: 8px;
  background: #fff;
  margin-bottom: 17px;
}
.brand-column-box {
  display: flex;
  padding: 15px;
  &--img {
    flex-shrink: 0;
    width: 40px;
    height: 40px;
    border-radius: 4px;
    background: #fff;
    margin-right: 6px;
  }
  &--rigbox {
    flex: 1;
  }
  &--div {
    display: flex;
    align-items: center;
    &--name {
      font-weight: 600;
      color: #000000;
      font-size: 17px;
      margin-right: 6px;
    }
    &--tag {
      background: rgba(215, 9, 47, 0.1);
      border-radius: 2px;
      color: #d7092f;
      font-size: 14px;
      width: 36px;
      height: 16px;
      text-align: center;
      line-height: 16px;
      display: inline-block;
      margin-right: 5px;
      margin-right: 6px;
    }
    &--type {
      width: 62px;
      border-radius: 4px;
      border: 1px solid #9a00cc;
      color: #9a00cc;
      font-size: 10px;
      text-align: center;
    }
  }
  &--subtitle {
    font-size: 14px;
    font-weight: 600;
    color: #000000;
    text-align: left;
  }
}
.flex1 {
  display: flex;
  flex: 1;
  align-items: center;
}
.brand-recommended-box {
  background: #fff;
  padding: 15px 15px 13px 15px;
  border-radius: 8px;
  display: flex;
  position: relative;
  &::before {
    content: '';
    width: 325px;
    height: 2px;
    background: #f6f6f6;
    position: absolute;
    top: 0;
    left: 15px;
  }
}
.brand-goods {
  flex: 1;
  text-align: center;
  &-top {
    width: 80px;
    height: 80px;
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 9px;
  }
  &-img {
    width: 100%;
    height: 100%;
  }
  &-name {
    font-size: 13px;
    color: #333333;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    word-break: break-all;
  }
  &-info {
    &--price {
      font-size: 13px;
      color: #d7092f;
      margin-right: 5px;
      .price {
        font-size: 17px;
      }
    }
    &--profits {
      font-size: 10px;
      color: #999999;
    }
  }
}
</style>
