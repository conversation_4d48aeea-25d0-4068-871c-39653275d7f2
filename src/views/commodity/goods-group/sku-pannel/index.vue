<template>
  <div class="goods-sku-wrap goods-num">
    <div class="box">
      <div class="goods">
        <img :src="newGoods.thumbnailUrl" />
        <div class="change-num">
          <div v-if="defaultCalculate" class="price">
            ￥{{ price }}
            <!-- {{skuObj.price || (newGoods.minPrice === newGoods.maxPrice ? newGoods.maxPrice : newGoods.minPrice + '~' + newGoods.maxPrice)}} -->
          </div>
          <slot v-else name="price"></slot>
          <div v-if="(showStock && skuObj.stock) || skuObj.stock === 0">
            库存{{
              skuObj.stock === undefined ? newGoods.stock_number : skuObj.stock
            }}件
            <text>(起订量：{{ skuObj.miniOrderQuantity || '不限' }}件)</text>
          </div>
          <div
            v-if="
              newGoods &&
              newGoods.specificationVOList &&
              newGoods.specificationVOList.length > 0 &&
              !skuId
            "
          >
            请选择规格属性
          </div>
          <div>活动大促价￥{{ controlPrice }}</div>
          <div>建议零售价￥{{ retailPrice }}</div>
        </div>
      </div>
      <div
        v-for="(item, pIndex) in newGoods.specificationVOList"
        :key="pIndex"
        class="sku-wrap"
      >
        <div class="title">{{ item.name }}</div>
        <div class="sku-list">
          <div
            v-for="(skuItem, index) in item.specificationItemVOList"
            :key="index"
            :class="'com-goods ' + (skuItem.checked ? 'checked' : '')"
            :data-index="index"
            :data-p-index="pIndex"
            :data-name="item.name"
            :style="
              'background:' +
              (skuItem.checked ? '#d7092f' : '#FFFFFF') +
              '; color: ' +
              (skuItem.checked ? '#FFFFFF' : '#999999') +
              '; border: ' +
              (skuItem.checked ? '1px solid #d7092f' : '1px solid #999999') +
              ';'
            "
            @click="chooseSku"
          >
            {{ skuItem.name }}
          </div>
        </div>
      </div>
      <div class="tool-btns">
        <div v-if="skuObj.stock === 0" class="expire">库存不足</div>
        <button
          class="confirmNum"
          @click="confirmNum"
          v-else
          style="background: #d7092f"
        >
          确定
        </button>
      </div>
    </div>
  </div>
</template>



<script>
export default {
  data() {
    return {
      skuId: '',
      skuObj: {},
      quantity: 1,
      choosedSkuObj: {},
      newGoods: {
        specificationItemVOList: []
      }, // 将props传进来的值进行处理得到的
      theme: ''
    };
  },

  components: {},
  props: {
    goods: {
      type: Object,
      default: () => ({})
    },
    // 是否显示数量修改器,默认显示
    showStepper: {
      type: Boolean,
      default: true
    },
    // 是否显示库存,默认显示
    showStock: {
      type: Boolean,
      default: true
    },
    // 是否采用默认的价格计算
    defaultCalculate: {
      type: Boolean,
      default: true
    }
  },
  watch: {
    goods: {
      handler: function (newValue) {
        if (!newValue) {
          return;
        }
        this.skuId = '';
        this.skuObj = {};
        this.quantity = 1;
        this.choosedSkuObj = {};
        this.newGoods = {};
        // 当商品只有一个SKU的时候，默认为选中状态
        if (newValue && newValue.skuVOS && newValue.skuVOS.length === 1) {
          if (
            newValue.specificationVOList &&
            newValue.specificationVOList.length > 0
          ) {
            // 如果有规格，就默认选中第一个
            newValue.specificationVOList[0].specificationItemVOList[0].checked = true;
          }
          this.skuId = newValue.skuVOS[0].id;
          this.skuObj = newValue.skuVOS[0];
          this.quantity = newValue.skuVOS[0].miniOrderQuantity || 1;
        }
        this.newGoods = JSON.parse(JSON.stringify(newValue));
      },
      immediate: true,
      deep: true
    }
  },
  computed: {
    price() {
      const skuObj = this.skuObj;
      const newGoods = this.newGoods;
      return (
        skuObj.price ??
        (newGoods.minPrice === newGoods.maxPrice
          ? newGoods.maxPrice
          : newGoods.minPrice + '~' + newGoods.maxPrice)
      );
    },

    retailPrice() {
      const skuObj = this.skuObj;
      const newGoods = this.newGoods;
      return (
        skuObj.retailPrice ??
        (newGoods.minRetailPrice === newGoods.maxRetailPrice
          ? newGoods.maxRetailPrice
          : newGoods.minRetailPrice + '~' + newGoods.maxRetailPrice)
      );
    },

    controlPrice() {
      const skuObj = this.skuObj;
      const newGoods = this.newGoods;
      return (
        skuObj.minControlPrice ??
        (newGoods.minControlPrice === newGoods.maxControlPrice
          ? newGoods.minControlPrice
          : newGoods.minControlPrice + '~' + newGoods.maxControlPrice)
      );
    },

    // 是否在限购时间范围内
    restrictionTime() {
      const newGoods = this.newGoods;
      const curDate = new Date();
      const beginDate = new Date(newGoods.restrictionStartDate);
      const endDate = new Date(newGoods.restrictionEndDate);

      if (curDate > beginDate && curDate < endDate) {
        return true;
      }

      return false;
    }
  },
  options: {
    multipleSlots: true // 在组件定义时的选项中启用多slot支持
  },
  created: function () {},
  beforeMount: function () {},
  mounted: function () {},
  moved: function () {},
  destroyed: function () {},
  methods: {
    isDuringDate(beginDateStr, endDateStr) {
      const curDate = new Date();
      const beginDate = new Date(beginDateStr);
      const endDate = new Date(endDateStr);

      if (curDate > beginDate && curDate < endDate) {
        return true;
      }

      return false;
    },

    async confirmNum() {
      this.$emit('confirmNum');
    },

    changeGoodsNum(e) {
      this.quantity = e;
    },
    chooseSku: function (e) {
      const { index, pIndex } = e.target.dataset;
      const goods = this.newGoods;
      const { specificationItemVOList } = goods.specificationVOList[pIndex];

      for (const specificationItemVO of specificationItemVOList) {
        specificationItemVO.checked = false;
      }

      specificationItemVOList[index].checked = true;

      if (pIndex === 0) {
        // 点击的是第一级SKU
        goods.thumbnailUrl =
          specificationItemVOList[index].showImg ||
          goods.defaultThumbnailUrl ||
          goods.thumbnailUrl;
      }
      this.newGoods = { ...goods };
      const choosedSkuObj = this.choosedSkuObj;
      goods.specificationVOList;
      choosedSkuObj[goods.specificationVOList[pIndex].level - 1] =
        specificationItemVOList[index].name;
      this.choosedSkuObj = { ...choosedSkuObj };
      const levelNumber = goods.specificationVOList.length; // sku级数
      if (Object.keys(this.choosedSkuObj).length === levelNumber) {
        const idx = goods.skuVOS.findIndex((item) => {
          return (
            item.firstLevel === this.choosedSkuObj[0] &&
            (item.secondLevel
              ? item.secondLevel === this.choosedSkuObj[1]
              : true) &&
            (item.threeLevel ? item.threeLevel === this.choosedSkuObj[2] : true)
          );
        });

        if (goods.skuVOS[idx].thumbnailUrl) {
          goods.thumbnailUrl = goods.skuVOS[idx].thumbnailUrl;
        }
        this.newGoods = goods;
        this.skuId = goods.skuVOS[idx].id;
        this.skuObj = {
          ...goods.skuVOS[idx],
          firstLevelName: goods.specificationVOList[0]
            ? goods.specificationVOList[0].name
            : null,
          secondLevelName: goods.specificationVOList[1]
            ? goods.specificationVOList[1].name
            : null,
          threeLevelName: goods.specificationVOList[2]
            ? goods.specificationVOList[2].name
            : null
        };
        this.quantity = goods.skuVOS[idx].miniOrderQuantity || 1;
        this.$emit('skuChange', {
          detail: {
            skuObj: this.skuObj,
            quantity: this.quantity
          }
        });
      }
    }
  }
};
</script>
<style>
.button {
  border: 1px solid #999;
  color: #666;
  text-decoration: none;
  text-align: center;
  display: block;
  border-radius: 4px;
  line-height: 37.5px;
  box-sizing: border-box;
  -webkit-appearance: none;
  appearance: none;
  background: 0 0;
  padding: 0 12px;
  margin: 0;
  height: 37.5px;
  white-space: nowrap;
  position: relative;
  text-overflow: ellipsis;
  font-size: 15px;
  cursor: pointer;
}
.button.small {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 13.5px;
}
.button.button-light {
  border-color: #a0a0a0;
  color: #666;
}
.button.disabled {
  background-color: #a9a9a9;
  border: 1px solid #a9a9a9;
  color: #fff;
  cursor: none;
}
.button.button-fill {
  color: #fff;
  background: #d7092f;
  border: none;
}
.button.button-fill.disabled {
  cursor: none;
  color: #fff;
  background-color: #d8d8d8;
  border: 1px solid #d8d8d8;
}
.button.button-primary {
  color: #d7092f;
  background-color: #fff;
  border: 1px solid #d7092f;
}
.button.button-fill.button-primary {
  color: #fff;
  background-color: #d7092f;
  border: 1px solid #d7092f;
}
.button.button-warn {
  color: #fff;
  background-color: #f5ad35;
  border: 1px solid #f5ad35;
}
.goods-sku-wrap.bg {
  position: fixed;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  overflow: hidden;
  z-index: 20;
  display: block;
}
.goods-sku-wrap.goods-num {
  bottom: 0;
  width: 100%;
  background-color: #fff;
  z-index: 999;
}
.goods-sku-wrap .box {
  background-color: #fff;
  font-size: 14px;
}
.goods-sku-wrap .goods {
  display: flex;
  align-items: flex-end;
  margin-top: 10px;
  padding: 10px 10px 10px 20px;
  color: #424242;
}
.goods-sku-wrap .goods img {
  margin-right: 10px;
  width: 87.5px;
  height: 87.5px;
  -webkit-flex-shrink: 0;
  flex-shrink: 0;
}
.goods-sku-wrap .goods .change-num {
  display: flex;
  flex-direction: column;
  margin-left: 10px;
}
.goods-sku-wrap .goods .change-num .price {
  display: block;
  color: #d7092f;
  font-size: 17px;
}
.goods-sku-wrap .sku-wrap {
  padding: 10px 20px;
}
.goods-sku-wrap .sku-wrap .title {
  font-size: 14px;
  color: #222;
  margin-bottom: 10px;
  text-align: left;
}
.goods-sku-wrap .sku-wrap .sku-list {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}
.goods-sku-wrap .sku-wrap .sku-list .com-goods {
  margin-right: 10px;
  margin-bottom: 5px;
  padding: 7.5px 8px 7.5px 9px;
  border: 1px solid #999;
  border-radius: 4px;
  background-color: #fff;
  color: #999;
  font-size: 14px;
}
.goods-sku-wrap .sku-wrap .sku-list .com-goods.disable {
  background: #f4f4f4;
}
.goods-sku-wrap .sku-wrap .sku-list .com-goods.checked {
  background-color: #fff;
  color: #d7092f;
  border: 1px solid #d7092f;
}
.goods-sku-wrap .num {
  padding: 10px 20px;
  font-size: 14px;
}
.goods-sku-wrap .num .name {
  -webkit-flex-grow: 1;
  flex-grow: 1;
}
.goods-sku-wrap .num .restrictionNum {
  color: #d7092f;
  font-size: 14px;
  margin-left: 10px;
}
.goods-sku-wrap .num .change-num {
  margin-top: 10px;
  display: flex;
  text-align: center;
  width: 100%;
}
.goods-sku-wrap .num .change-num .wrap {
  border: 1px solid #cbcbcb;
}
.goods-sku-wrap .num .change-num .input {
  width: 50px;
  border-radius: 0;
  background: #fff;
  border-left: 1px solid #cbcbcb;
  border-right: 1px solid #cbcbcb;
}
.goods-sku-wrap .num .change-num .plus {
  width: 32px;
  background: #fff;
}
.goods-sku-wrap .num .change-num .minus {
  width: 32px;
  background: #fff;
}
.goods-sku-wrap .tool-btns {
  padding: 5px;
  text-align: center;
  border-top: 1px solid #f2f5f8;
  font-size: 16px;
}
.goods-sku-wrap .tool-btns .confirmNum {
  margin: 10px auto;
  width: 90%;
  color: #fff;
  height: 45px;
  line-height: 45px;
  display: block;
  border: none;
}
.goods-sku-wrap .tool-btns .expire {
  background-color: #c8c9cb !important;
  color: #fff !important;
  margin: 10px;
  height: 45px;
  line-height: 45px;
  border-radius: 5px;
  display: block;
}
.goods {
  text-align: left;
}
</style>