<!-- 商详基础信息展示 -->
<template>
  <div class="goods-info-wrap">
    <div class="info-row info-row-flex-end">
      <div class="price-box">
        <span class="price-box--tag">¥</span>
        <!-- <span class="price-box--tag--price">{{ showPrice }}</span> -->
        <price-show-view
          min-font-size="16"
          max-font-size="30"
          :price="showPrice"
        />
      </div>
      <div class="price-box-income" v-if="!isSample">
        赚￥{{ discountAmount }}
      </div>
      <div
        v-if="interfaceData.materialZip"
        class="material-btn"
        @tap="showDownload"
      >
        <span class="el-icon-download"></span>
        <span>素材下载</span>
      </div>
    </div>
    <div class="info-row">
      <div class="level-txet">建议零售价：¥{{ showRetailPrice }}</div>
    </div>
    <div class="info-row info-row-space-between">
      <div class="level-txet">活动大促价：¥{{ showControlPrice }}</div>
      <span v-if="isSample" class="gross-profit-text"
        >预估批发价：{{ interfaceData.minPrice }}</span
      >
      <div v-else class="gross-profit-text">毛利率：{{ grossProfitRate }}%</div>
    </div>
    <div class="title-row">
      <span v-if="isLimitNew" class="goods-type"> 新人专享 </span>
      <span v-if="interfaceData.type === 'GLOBAL'" class="goods-type-ht">
        海淘
      </span>
      <div class="goods-type-fl" v-if="isRebate">返利</div>
      {{ goodsName }}
    </div>
    <div class="title-row--subtitle" v-if="interfaceData.subhead">
      {{ interfaceData.subhead }}
    </div>
    <div class="info-row">
      <div class="level-txet">
        <div v-if="interfaceData.expressFee !== 0 && !isSample">
          <div v-if="shippingThreshold === 0">
            <span>包邮</span>
          </div>
          <div v-else-if="shippingThreshold > 0">
            <span
              >邮费{{ interfaceData.expressFee }}元，满{{
                shippingThreshold
              }}元包邮</span
            >
          </div>
          <div v-else>
            <span>邮费{{ interfaceData.expressFee }}元</span>
          </div>
        </div>
        <div v-else>包邮</div>
      </div>
    </div>
    <div
      v-if="isShowDownload"
      class="close-btn iconfont icon-danchuangguanbiicon"
      @tap="hideMaterialDownload"
    ></div>
  </div>
</template>

<script>
import priceShowView from './price-show-view';
export default {
  data() {
    return {
      inputEmail: '',
      currentMterial: 0,
      isShowDownload: false, // 下载弹出层
      isRebate: false, // 是否返利
      goodsName: '', // 商品名称
      showPrice: '0', // 价格
      grossProfitRate: '0', // 毛利率
      showRetailPrice: '0', // 建议零售价
      showControlPrice: '0', // 活动大促价
      discountAmount: '0', // 赚的钱
      isLimitNew: false, //  是否新人专享
      commodityExtensionVO: {}
    };
  },
  props: {
    interfaceData: {
      type: Object,
      default: () => {}
    },
    isSample: {
      type: Boolean,
      default: () => false
    },
    isSpecialGoods: {
      type: Boolean,
      default: () => false
    },
    interfaceSampleData: {
      type: Object,
      default: () => {}
    },
    interfaceSpecialSupplyData: {
      type: Object,
      default: () => {}
    },
    shippingThreshold: {
      type: Number,
      default: () => 0
    }
  },
  components: { priceShowView },
  watch: {},
  created() {
    const {
      grossProfitRate,
      minControlPrice,
      maxControlPrice,
      minRetailPrice,
      maxRetailPrice,
      name,
      id,
      discountAmount,
      commodityExtensionVO
    } = this.interfaceData;
    this.id = id;
    this.commodityExtensionVO = commodityExtensionVO;
    this.goodsName = name;
    this.grossProfitRate = this.setGrossProfitRate(grossProfitRate);
    this.showPrice = this.getShowPrice();
    this.showRetailPrice = this.getPriceText(minRetailPrice, maxRetailPrice);
    this.showControlPrice = this.getPriceText(minControlPrice, maxControlPrice);
    this.discountAmount = discountAmount;
  },

  methods: {
    //  素材tab切换
    onClickItemMterial(e) {
      this.currentMterial = e.currentIndex;
    },
    // 素材下载按钮
    showDownload() {
      this.isShowDownload = true;
    },
    // 关闭素材下载弹框
    hideMaterialDownload() {
      this.isShowDownload = false;
    },
    // 修改毛利率
    setGrossProfitRate(grossProfitRate) {
      return (grossProfitRate * 100).toFixed();
    },
    getShowPrice() {
      const { minPrice, maxPrice } = this.interfaceData;
      return this.getPriceText(minPrice, maxPrice);
    },
    // 显示区间
    getPriceText(minPrice, maxPrice) {
      const priceTxt =
        minPrice === maxPrice ? maxPrice : minPrice + '-' + maxPrice;
      return priceTxt;
    },
    inputEmailchange(e) {
      const { value = '' } = e.detail;
      this.inputEmail = value;
    }
  }
};
</script>
<style lang='scss' scoped>
.goods-info-wrap {
  padding: 17px 0;
  background: #fff;
  border-radius: 2px 2px 16px 16px;
  margin-bottom: 16px;
}
.info-row {
  display: flex;
  position: relative;
  padding: 0 15px;
  &-flex-end {
    align-items: flex-end;
  }
  &-space-between {
    justify-content: space-between;
  }
}

.price-box {
  font-size: 30px;
  color: #d7092f;
  &--tag {
    font-size: 20px;
  }
  &-income {
    font-size: 14px;
    color: #333333;
    margin-left: 10px;
  }
}
.material-btn {
  width: 79px;
  height: 29px;
  border-radius: 50px 0 0 50px;
  position: absolute;
  right: 0;
  top: 10px;
  font-size: 12px;
  color: #fff;
  background: #fa5630;
  display: flex;
  align-items: center;
  justify-content: center;
}
.level-txet {
  color: #999999;
  font-size: 13px;
}
.gross-profit-text {
  color: #fa5630;
  font-size: 15px;
}
.title-row {
  padding: 0 15px;
  margin: 16px 0 12px 0;
  font-weight: 600;
  text-align: left;
  &--subtitle {
    padding: 0 15px;
    font-size: 13px;
    color: #333333;
    margin-bottom: 12px;
    text-align: left;
  }
}
.goods-type {
  display: inline-block;
  line-height: 17px;
  width: 31px;
  height: 17px;
  color: #fff;
  background: #cf0a2c;
  border-radius: 4px;
  font-size: 12px;
  text-align: center;
  margin-right: 3px;
}
.goods-type-ht {
  width: 67px;
  height: 17px;
  color: #9a00cc;
  background: rgba(154, 0, 204, 0.1);
  border-radius: 4px;
  font-size: 12px;
  margin-right: 10px;
  padding-left: 3px;
  line-height: 17px;
  text-align: center;
}

.goods-type-fl {
  background: rgba(215, 9, 47, 0.1);
  border-radius: 4px;
  color: #d7092f;
  font-size: 12px;
  width: 42px;
  height: 20px;
  text-align: center;
  line-height: 20px;
  display: inline-block;
  margin-right: 10px;
}
.material-popup {
  border-radius: 10px;
}
.material-popup .close-btn {
  position: absolute;
  bottom: -25px;
  width: 30px;
  height: 30px;
  left: 50%;
  margin-left: -15px;
}
.material-box {
  width: 290px;
  height: 225px;
  position: relative;
}
.material-box .site {
  width: 250px;
  height: 115.2px;
  font-size: 16px;
  text-align: center;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
}
.material-box .site .cont {
  width: 100%;
  text-align: center;
}
.material-box .site .cont .ts {
  -webkit-flex-shrink: 0;
  flex-shrink: 0;
}
.material-box .site .cont .text {
  color: #007aff;
  text-decoration: underline;
  word-wrap: break-word;
  display: inline-block;
  text-align: justify;
  word-break: break-all;
  width: 100%;
}
.material-box .site-email {
  padding: 20px 20px;
}
.material-box .site-email .input {
  background: #f6f6f6;
  text-align: left;
  height: 50px;
  line-height: 50px;
  border-radius: 3px;
  padding: 0 16px;
}
.material-box .site-email .input-mobile {
  background: #fff;
  width: 230px;
  height: 40px;
  border-radius: 3px;
  border: 1px solid #979797;
  padding: 0 10px;
}
.material-box .site-email .tip {
  text-align: left;
  font-size: 13px;
}
.material-box .site-email .tip-mobile-title {
  text-align: center;
  font-size: 15px;
  color: #222;
}
.material-box .site-email .tip-mobile {
  text-align: center;
  font-size: 10px;
  color: #666;
  margin-top: 2px;
  margin-bottom: 20px;
}
.material-box .btn {
  width: 250px;
  height: 40px;
  line-height: 40px;
  border-radius: 10px;
  background: #d7092f;
  color: #fff;
  font-size: 15px;
}
</style>