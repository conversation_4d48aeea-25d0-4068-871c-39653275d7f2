<!-- 商品详情-商品参数 -->
<template>
  <div class="preferential-box">
    <div class="preferential-box-title">
      <div class="preferential-box-title-txt">{{ title }}</div>
    </div>
    <template v-if="!goodsDetail">
      <div
        class="preferential-row"
        v-for="item of paramRelationList"
        :key="item.id"
      >
        <div class="preferential-row--title">{{ item.paramName }}</div>
        <div class="preferential-row--div">{{ item.paramValue }}</div>
      </div>
      <div
        class="preferential-row preferential-row--tip"
        v-if="paramRelationList.length === 0"
      >
        当前商品，没有更多参数
      </div>
    </template>
    <template v-if="goodsDetail">
      <img
        v-for="(item, index) in detailImageUrls"
        class="detail-image"
        :key="index"
        :src="item"
      />
    </template>
  </div>
</template>

<script>
export default {
  data() {
    return {};
  },
  props: {
    goodsDetail: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: ''
    },
    // 商品参数属性信息  paramName (string, optional): 属性名 , paramValue (string, optional): 属性详情值
    paramRelationList: {
      type: Array,
      default: () => []
    },
    // 详情图片
    detailImageUrls: {
      type: Array,
      default: () => []
    }
  },
  components: {},

  computed: {},

  methods: {}
};
</script>
<style lang='scss' scoped>
.preferential-box-title {
  font-size: 15px;
  font-weight: 600;
  color: #333333;
  padding: 15px;
  position: relative;
  &-txt {
    margin-left: 7.5px;
  }
  &::before {
    position: absolute;
    content: '';
    left: 15px;
    top: 50%;
    margin-top: -7.5px;
    width: 4px;
    background: #d7092f;
    height: 15px;
    border-radius: 100px;
  }
}
.preferential-box {
  background: #fff;
  border-radius: 8px;
  margin-bottom: 16px;
  padding-bottom: 7.5px;
}
.preferential-row {
  display: flex;
  align-items: center;
  padding: 0 16px 0 15px;
  position: relative;
  font-size: 13px;
  color: #333;
  &--div {
    display: flex;
    flex: 1;
    align-items: center;
  }
  &--title {
    width: 74px;
    flex-shrink: 0;
    color: #999999;
  }
  &--tip {
    color: #999999;
  }
}
.preferential-row-tip {
  height: 18px !important;
  padding-bottom: 7.5px;
}
.detail-image {
  width: 100%;
  display: block;
}
</style>
