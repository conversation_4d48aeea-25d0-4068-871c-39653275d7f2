<!-- 商品详情-仓储信息 -->
<template>
  <div class="preferential-box">
    <div class="preferential-row">
      <div class="preferential-row--title">库存</div>
      <div class="preferential-row--div">{{ stock }}件</div>
      <div>起订量{{ miniOrderQuantity }}件</div>
    </div>
    <div class="preferential-row" @click="openSkuPannel" v-if="isSkuShow">
      <div class="preferential-row--title">规格</div>
      <div class="preferential-row--div">
        {{ skuName ? '已选：' + skuName : '请选择规格' }}
      </div>
      <span class="el-icon-arrow-right"></span>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      stock: 0,
      miniOrderQuantity: 1 // 最小起订量
    };
  },
  props: {
    // 当前SKU
    currentSkuObj: {
      type: Object,
      default: () => {}
    },
    interfaceData: {
      type: Object,
      default: () => {}
    },
    // 是否展示规格名称  如果没有规格名称 则不展示
    isSkuShow: {
      type: Boolean,
      default: () => false
    }
  },
  components: {},
  created() {
    this.getPageData();
  },

  computed: {
    skuName() {
      if (Object.keys(this.currentSkuObj).length > 0) {
        const {
          firstLevelName,
          secondLevelName,
          threeLevelName,
          firstLevel,
          secondLevel,
          threeLevel
        } = this.currentSkuObj;
        let txt = '';
        if (firstLevelName) {
          txt += `${firstLevelName}:${firstLevel};`;
        }
        if (secondLevelName) {
          txt += `${secondLevelName}:${secondLevel};`;
        }
        if (threeLevelName) {
          txt += `${threeLevelName}:${threeLevel};`;
        }
        return txt;
      }
      return '';
    }
  },
  methods: {
    openSkuPannel() {
      this.$emit('openSkuPannel');
      // this.$message.info('预览不能进行操作哦');
    },
    getPageData() {
      const { stock, skuVOlist } = this.interfaceData;

      const miniOrderQuantity = Math.min.apply(
        Math,
        skuVOlist.map((o) => o.miniOrderQuantity)
      );
      if (miniOrderQuantity > 1) this.miniOrderQuantity = miniOrderQuantity;
      this.stock = stock;
    }
  }
};
</script>
<style lang='scss' scoped>
.preferential-box {
  background: #fff;
  border-radius: 8px;
  margin-bottom: 16px;
  padding-bottom: 7.5px;
}
.preferential-row {
  display: flex;
  align-items: center;
  padding: 0 16px 0 15px;
  position: relative;
  font-size: 13px;
  &--div {
    display: flex;
    flex: 1;
    align-items: center;
  }
  &--title {
    width: 74px;
    flex-shrink: 0;
  }
}

.goods-type-fl {
  background: rgba(215, 9, 47, 0.1);
  border-radius: 4px;
  color: #d7092f;
  font-size: 9px;
  width: 28px;
  height: 16px;
  text-align: center;
  line-height: 16px;
  display: inline-block;
  margin-right: 5px;
}
.preferential-row-tip {
  height: 18px !important;
  padding-bottom: 7.5px;
}
</style>