<!-- 价格展示组件 -->
<template>
  <span :style="{ fontSize: priceFontSize(price) + 'px' }">{{ price }}</span>
</template>

<script>
export default {
  data() {
    return {};
  },
  props: {
    price: [String, Number],
    maxlength: [Number],
    minFontSize: [String, Number], // 最小字体
    maxFontSize: [String, Number] // 最大字体
  },
  components: {},

  computed: {},


  methods: {
    priceFontSize(price) {
      const a = String(price);
      if (a.length > (this.maxlength || 7)) {
        return this.minFontSize;
      }
      return this.maxFontSize;
    }
  }
};
</script>
<style lang='scss' scoped>
</style>
