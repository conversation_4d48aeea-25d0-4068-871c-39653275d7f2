<template>
  <div class="app-container">
    <div class="table-container">
      <Authority auth="/commodity/productLabel/:edit">
        <el-button type="primary" icon="el-icon-plus" class="add-btn" size="small" @click="add">新增商品标签</el-button>
      </Authority>
      <Instructions :showTitle="true" :colNum="0">
        <template slot="title">
          <div>
            <p>1、新品是按照商品的创建时间来确定的，取近30天新创建的商品，越早创建的排在越后面（不能删除）；</p>
            <p>2、新品属性只能有一个标签使用；</p>
            <p>3、商品关联了多个标签的话，会按照标签的显示顺序优先限制优先级别高的标签；</p>
          </div>
        </template>
      </Instructions>
      <el-table :data="list" v-loading.body="listLoading" element-loading-text="加载中" fit highlight-current-row>
        <el-table-column label="序号" type="index" width="50"> </el-table-column>
        <el-table-column align="center" label="标签名称" prop="name"></el-table-column>
        <el-table-column align="center" label="优先级排序" width="150">
          <template slot-scope="scope">
            <el-button @click="sort(scope.row.id, 'TOTOP')" type="text" :loading="TOTOP" v-if="scope.$index !== 0">置顶</el-button>
            <el-button @click="sort(scope.row.id, 'UP')" type="text" :loading="UP" v-if="scope.$index !== 0">上移</el-button>
            <el-button @click="sort(scope.row.id, 'DOWN')" type="text" :loading="DOWN" v-if="scope.$index !== list.length - 1">下移</el-button>
          </template>
        </el-table-column>

        <el-table-column align="center" label="已关联商品数" prop="relationCommodityNum"></el-table-column>
        <el-table-column align="center" label="是否新品" prop="isNewProduct">
          <template slot-scope="scope">
            <el-button type="text" size="small">
              <el-switch v-model="scope.row.isNewProduct" @change="setNewProductLabel(scope.row)"> </el-switch>
            </el-button>
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作" fixed="right" width="150px">
          <template slot-scope="scope">
            <el-button type="text" size="small">
              <Authority auth="/commodity/productLabel/:edit">
                <el-button @click="handleDelete(scope.row.id, scope.$index)" type="text">删除</el-button>
              </Authority>
            </el-button>
            <el-button type="text" size="small">
              <Authority auth="/commodity/productLabel/:edit">
                <el-button type="text" @click="update(scope.row)">编辑</el-button>
              </Authority>
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-dialog :title="isEdit ? '修改商品标签' : '新增商品标签'" :visible.sync="dialogFormVisible" width="400px">
      <el-form :model="form" ref="ruleForm" class="el-form">
        <el-form-item label="标签名称" prop="name">
          <el-input class="dialog-input" v-model.trim="form.name" autocomplete="off" maxlength="6"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible = false" size="small">取 消</el-button>
        <el-button type="primary" @click="preserve" :loading="loading" size="small">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { list, handleDelete, changeSort, enableNewProductLabel, disableNewProductLabel, createCommodityLabel, updateCommodityLabel } from '@/api/commodity/product.js';
import pick from 'lodash/pick';
import pickBy from 'lodash/pickBy';
export default {
  name: 'commodity-productLabel',
  data() {
    return {
      TOTOP: false,
      UP: false,
      DOWN: false,
      list: [],
      listLoading: true,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      dialogFormVisible: false,
      form: {
        name: '',
        id: '',
        sort: ''
      },
      isEdit: false,
      loading: false
    };
  },
  computed: {
    // 过滤
    data() {
      const self = this;
      return pickBy(self.filter, (val) => !!val);
    }
  },
  activated() {
    const self = this;
    self.fetchData();
  },
  methods: {
    // 添加
    add() {
      this.form.name = '';
      this.form.id = '';
      this.form.sort = '';
      this.isEdit = false;
      this.dialogFormVisible = true;
    },
    update(row) {
      this.form.name = row.name;
      this.form.id = row.id;
      this.form.sort = row.sort;
      this.isEdit = true;
      this.dialogFormVisible = true;
    },
    preserve() {
      const self = this;
      const keyword = this.form.name;
      if (!keyword) {
        this.$message({
          type: 'warning',
          message: '请输入标签名'
        });
        return;
      }

      if (this.isEdit) {
        const data = this.form;
        updateCommodityLabel(data)
          .then((response) => {
            self.$message.success(response.msg);
          })
          .finally(() => {
            self.fetchData();
            this.dialogFormVisible = false;
          });
      } else {
        const data = {
          name: keyword
        };
        createCommodityLabel(data)
          .then((response) => {
            self.$message.success(response.msg);
          })
          .finally(() => {
            self.fetchData();
            this.dialogFormVisible = false;
          });
      }
    },
    fetchData() {
      const self = this;
      self.listLoading = true;
      const listQuery = pick(self, ['pageNo', 'pageSize', 'data']);
      list(listQuery)
        .then((response) => {
          const lists = response.data;
          response.data.forEach((v, i) => {
            v.isNewProduct === '0' ? (lists[i].isNewProduct = false) : (lists[i].isNewProduct = true);
          });
          self.list = lists;
          self.total = response.data.total;
        })
        .finally(() => {
          self.listLoading = false;
        });
    },
    // 排序
    sort(id, type) {
      const self = this;
      self[type] = true;
      const data = {
        id,
        type
      };
      changeSort(data)
        .then((response) => {
          self.$message.success(response.msg);
        })
        .finally(() => {
          self[type] = false;
          self.fetchData();
        });
    },
    setNewProductLabel(row) {
      const self = this;
      if (!row.isNewProduct) {
        disableNewProductLabel(row.id)
          .then((response) => {
            self.$message.success(response.msg);
          })
          .finally(() => {
            self.fetchData();
          });
      } else {
        enableNewProductLabel(row.id)
          .then((response) => {
            self.$message.success(response.msg);
          })
          .finally(() => {
            self.fetchData();
          });
      }
    },
    // 删除
    handleDelete(val, index) {
      const self = this;
      this.$confirm('确认删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          handleDelete(val)
            .then((response) => {
              self.$message.success(response.msg);
            })
            .finally(() => {
              self.fetchData();
              self.listLoading = false;
            });
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });
        });
    }
  }
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.tips {
  padding: 30px 0;
}
.add-btn {
  margin-bottom: 10px;
}
.link {
  color: var(--color-primary);
  &:hover {
    color: var(--color-primary);
  }
}
</style>
