<template>
  <div>
    <div class="recompose-box" v-if="isInitial">
      <div class="info">
        <div>
          <span>活动时间：{{ obj.startDate | parseTime('{y}-{m}-{d} {h}:{i}:{s}') }}至{{ obj.endDate | parseTime('{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </div>
        <div>
          <span>活动备注：{{ obj.remarks }}</span>
        </div>
      </div>
      <div>
        <el-button @click="recompose" size="mini" type="primary">修改</el-button>
        <br />
        <el-button @click="remove" size="mini" type="primary">删除</el-button>
      </div>
    </div>
    <div v-else>
      <el-button @click="open" size="mini" type="primary">添加</el-button>
    </div>
    <el-dialog :before-close="() => (dialogVisible = false)" :visible.sync="dialogVisible" title="活动属性" width="750px">
      <el-form :model="form" :rules="rules" class="form" label-width="100px" ref="form" size="mini">
        <el-form-item label="活动时间" prop="activityDate">
          <el-date-picker :default-time="defaultDate" end-placeholder="结束日期" range-separator="至" start-placeholder="开始日期" type="datetimerange" v-model="form.activityDate"></el-date-picker>
        </el-form-item>
        <el-form-item class="textarea" label="活动备注" prop="remarks">
          <el-input :rows="3" type="textarea" v-model="form.remarks"></el-input>
        </el-form-item>
        <el-form-item class="space" label="商品名称" prop="name">
          <el-input type="text" v-model.trim="form.name"></el-input>
        </el-form-item>
        <el-form-item label="商品缩略图" prop="thumbnailUrl">
          <ImageManagement :maxSize="100 * 1024" v-model="form.thumbnailUrl"></ImageManagement>
          <div class="tip">宽高：500*500，大小：最大100k，数量：1张</div>
        </el-form-item>
        <el-form-item label="商品橱窗图" prop="shopWindowUrls">
          <ImageManagement :limit="10" :maxSize="300 * 1024" data-type="array" v-model="form.shopWindowUrls"></ImageManagement>
          <div class="tip">宽高：800*800，大小：最大300k，数量：最多10张</div>
        </el-form-item>
        <el-form-item label="商品价格" v-if="sku.specificationVOList && sku.specificationVOList.length > 0">
          <table border="0" class="stock-table" rules="none">
            <thead>
              <tr>
                <th :key="i" v-for="(v, i) of sku.specificationVOList">
                  {{ v.name }}
                </th>
                <th>价格</th>
                <th>规格编码</th>
              </tr>
            </thead>
            <tbody>
              <tr :key="i" v-for="(v, i) of getRows(filterSpecs)">
                <td :key="j" :rowspan="m.rowspan" v-for="(m, j) of filterRowSpan(i)">
                  {{ v[m.name] }}
                </td>
                <td>
                  <input :value="getSkuValue(v, 'price')" @input="onSkuInput($event, v, 'price')" class="spec-code-input" type="text" />
                </td>
                <td>
                  <input :value="getSkuValue(v, 'specCode')" @input="onSkuInput($event, v, 'specCode')" class="spec-code-input" type="text" />
                </td>
              </tr>
            </tbody>
          </table>
        </el-form-item>
        <div v-else>
          <el-form-item label="商品价格" prop="radius">
            <el-input class="inputbox" v-model.trim="singleSKU.price"></el-input>
          </el-form-item>
          <el-form-item label="规格编码" prop="radius">
            <el-input class="inputbox" v-model.trim="singleSKU.specCode"></el-input>
          </el-form-item>
        </div>
        <el-form-item label="运费设置" prop="expressFee">
          <el-radio-group v-model="isExpressFee">
            <el-radio :label="0">包邮</el-radio>
            <el-radio :label="1">
              <span>不包邮</span>
              <span class="expressFee" v-if="isExpressFee === 1">
                <el-input placeholder="请输入运费金额" size="small" v-model.trim="form.expressFee"></el-input>
                <span>元</span>
              </span>
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <!-- <el-form-item label="会员权益" prop="checkList">
          积分抵现
          <span>（营销活动期间建议不参与）</span>
          <el-radio-group v-model="form.creditDeduction" :disabled="type === 'CARD'">
            <el-radio label="1">启用</el-radio>
            <el-radio label="0">不启用</el-radio>
          </el-radio-group>
          <br />会员折扣
          <span>（营销活动期间建议不参与）</span>
          <el-radio-group v-model="form.discount" :disabled="type === 'CARD'">
            <el-radio label="1">启用</el-radio>
            <el-radio label="0">不启用</el-radio>
          </el-radio-group>
        </el-form-item>-->
        <el-form-item label="参加返利" prop="expressFee">
          <el-radio-group v-model="form.creditBack">
            <el-radio :label="'1'">是</el-radio>
            <el-radio :label="'0'">否</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <span class="dialog-footer" slot="footer">
        <el-button @click="dialogVisible = false" size="small">取 消</el-button>
        <el-button @click="onSubmit" size="small" type="primary">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getCommodityActivityParam, listSpec, createCommodityActivityParam, updateCommodityActivityParam, deleteCommodityActivityParam } from '@/api/common/commodity';
import ImageManagement from '@/components/ImageManagement';
import { parseDefaultTime } from '@/utils';
import cloneDeep from 'lodash/cloneDeep';
import get from 'lodash/get';
import set from 'lodash/set';
import { validateFloatNum } from '@/utils/validate';
export default {
  name: '',
  props: {
    id: String,
    type: String
  },
  components: { ImageManagement },
  data() {
    const initForm = {
      name: '',
      // creditDeduction: '',
      // discount: '',
      remarks: '',
      activityDate: [],
      shopWindowUrls: [],
      thumbnailUrl: '',
      expressFee: '',
      creditBack: ''
    };
    return {
      isExpressFee: '',
      singleSKU: { price: 0, specCode: '' },
      sku: {},
      defaultDate: ['00:00:00', '23:59:59'],
      submitLoading: false,
      dialogVisible: false,
      initForm,
      form: cloneDeep(initForm),
      isInitial: false,
      obj: {},
      rules: {
        activityDate: [{ required: true, message: '活动时间必填', trigger: 'change' }]
      }
    };
  },
  created() {
    this.getCommodityActivityParam();
    this.getListSpec();
  },
  computed: {
    default_val() {
      const data = {};
      this.sku.skuVOS.forEach((sku) => {
        const { firstLevel, secondLevel, threeLevel, ...rest } = sku;
        const str = [firstLevel, secondLevel, threeLevel].filter((val) => !!val).join('.');
        set(data, str, rest);
      });
      if (this.sku.specificationVOList.length) {
        return data;
      } else {
        // 如果没有规格
        return { ...this.sku.skuVOS[0] };
      }
    },
    filterSpecs() {
      const filterList = [];
      if (!this.sku.specificationVOList) return;
      this.sku.specificationVOList.forEach((item) => {
        const { specificationItemVOList, name } = item;
        const fList = specificationItemVOList.filter((item) => item && item.name);
        if (!fList.length) {
          return;
        }
        filterList.push({ name, list: fList });
      });
      return filterList;
    },
    data() {
      const data = {};
      const {
        name,
        // creditDeduction,
        // discount,
        remarks,
        shopWindowUrls,
        thumbnailUrl,
        activityDate,
        expressFee,
        creditBack
      } = this.form;
      const [startDate, endDate] = activityDate;
      data.startDate = parseDefaultTime(startDate);
      data.endDate = parseDefaultTime(endDate);
      // data.creditDeduction = creditDeduction;
      // data.discount = discount;
      data.creditBack = creditBack;
      data.name = name;
      data.shopWindowUrls = shopWindowUrls;
      data.thumbnailUrl = thumbnailUrl;
      data.remarks = remarks;
      data.expressFee = this.isExpressFee === '' ? '' : !this.isExpressFee ? 0 : expressFee;
      data.id = this.id;
      return data;
    }
  },
  watch: {},
  methods: {
    showFilter(val, key) {
      const id = get(this.default_val, `${this.filterSpecs.map((item) => val[item.name]).join('.')}.id`);
      const [filterobj] = this.obj.skuParams.filter((v) => v.id === id);
      return filterobj[key];
    },
    getSkuValue(val, key) {
      return get(this.default_val, `${this.filterSpecs.map((item) => val[item.name]).join('.')}.${key}`);
    },
    onSkuInput(e, sku, key) {
      set(this.default_val, `${this.filterSpecs.map((item) => sku[item.name]).join('.')}.${key}`, e.target.value);
    },
    getRows(specifications = []) {
      function loop(list, key, deep, parentList) {
        const res = [];
        parentList.forEach((parent) => {
          list.forEach((val) => {
            res.push({
              ...parent,
              [key]: val.name
            });
          });
        });
        return res;
      }
      if (!specifications.length) {
        return [];
      }
      const [first, ...rest] = specifications;
      return [
        first.list.map((val) => ({
          [first.name]: val.name
        })),
        ...rest
      ].reduce((acc, cur, index) => {
        return loop(cur.list, cur.name, index, acc);
      });
    },
    filterRowSpan(index) {
      let rowspan = 1;
      const list = [];
      for (let i = this.filterSpecs.length - 1; i >= 0; i--) {
        const item = this.filterSpecs[i];
        if (index % rowspan === 0) {
          list.unshift({
            ...item,
            rowspan
          });
        }
        rowspan *= item.list.length;
      }
      return list;
    },
    // 查询sku
    getListSpec() {
      listSpec(this.id).then((res) => {
        this.sku = res.data;
        if (res.data.specificationVOList.length === 0) {
          const { id, price, specCode } = { ...this.sku.skuVOS[0] };
          this.singleSKU = {
            id,
            price,
            specCode
          };
        }
      });
    },
    getCommodityActivityParam() {
      getCommodityActivityParam(this.id).then((res) => {
        const { data } = res;
        if (data) {
          this.obj = data;
          this.isInitial = true;
        } else {
          this.obj = {};
          this.isInitial = false;
        }
      });
    },
    validate() {
      return this.$refs.form.validate().then(() => this.data);
    },
    // 判断是否为对象
    isObject(obj) {
      return Object.prototype.toString.call(obj) === '[object Object]';
    },
    getSkuParams() {
      const skuParams = [];
      if (this.sku.specificationVOList.length > 0) {
        // 获取嵌套对象属性的值
        const getValue = (data) => {
          for (const key of Object.keys(data)) {
            if (data[key].hasOwnProperty('id') && data[key].hasOwnProperty('price')) {
              skuParams.push({
                id: data[key].id,
                price: data[key].price * 1,
                specCode: data[key].specCode
              });
            }
            if (this.isObject(data[key]) || Array.isArray(data[key])) {
              const result = getValue(data[key]);
              if (typeof result !== 'undefined') {
                return result;
              }
            }
          }
        };
        getValue(this.default_val);
      } else {
        // 没有 规格属性
        skuParams.push(this.singleSKU);
      }
      return skuParams;
    },
    onSubmit() {
      this.validate().then((formData) => {
        formData.skuParams = this.getSkuParams();
        const verify = formData.skuParams.every((item) => {
          return item.price !== '' && !isNaN(item.price) && item.price >= 0 && !!item.specCode && item.specCode !== '';
        });
        if (!verify) {
          this.$message.error('输入有误！价格必须是数字、不能为空、不能小于0、编码不能为空');
          return;
        }
        if (this.isExpressFee === '1' && (formData.expressFee === '' || !validateFloatNum(formData.expressFee))) {
          this.$message.error('输入有误！邮费必须是数字且最多保留2位小数、不能为空、不能小于0');
          return;
        }
        const res = this.isInitial ? updateCommodityActivityParam : createCommodityActivityParam;
        res(formData).then((res) => {
          this.$message.success('操作成功');
          this.dialogVisible = false;
          this.getCommodityActivityParam();
          this.getListSpec();
        });
      });
    },
    open() {
      this.dialogVisible = true;
    },
    remove() {
      this.$confirm('确认删除该商品活动属性吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          deleteCommodityActivityParam(this.id).then((response) => {
            this.$message({
              type: 'success',
              message: '删除成功！'
            });
            this.getCommodityActivityParam();
            this.getListSpec();
            this.form = cloneDeep(this.initForm);
            this.isExpressFee = '';
          });
        })
        .catch(() => {});
    },
    recompose() {
      // 修改
      const {
        // creditDeduction = '',
        // discount = '',
        creditBack = '',
        expressFee = '',
        name = '',
        remarks = '',
        shopWindowUrls,
        skuParams,
        thumbnailUrl,
        startDate,
        endDate
      } = this.obj;

      if (this.sku.specificationVOList.length === 0) {
        this.singleSKU = skuParams[0];
      } else {
        const getValue = (data, k) => {
          for (const key of Object.keys(data)) {
            if (data[key].hasOwnProperty('id') && data[key].hasOwnProperty('price')) {
              if (data[key]['id'] === k['id']) {
                data[key]['price'] = k['price'];
                data[key]['specCode'] = k['specCode'];
              }
            }
            if (this.isObject(data[key])) {
              const result = getValue(data[key], k);
              if (typeof result !== 'undefined') {
                return result;
              }
            }
          }
        };
        // 替换初始SKU
        skuParams.forEach((k, i) => {
          getValue(this.default_val, k);
        });
      }
      const form = {
        // creditDeduction,
        // discount,
        creditBack,
        expressFee,
        name,
        remarks,
        shopWindowUrls,
        thumbnailUrl,
        activityDate: [startDate, endDate]
      };
      this.isExpressFee = !expressFee && expressFee !== 0 ? '' : expressFee > 0 ? 1 : 0;
      this.form = form;
      this.dialogVisible = true;
    }
  }
};
</script>

<style lang="scss"  scoped>
@import '../variables';
$border: 1px solid #e5e5e5;
.inputbox {
  width: 200px;
}
.imgurl {
  width: 60px;
  height: 60px;
  margin-right: 10px;
}
.recompose-box {
  display: flex;
  .info {
    border: 1px solid #e5e5e5;
    padding: 0 10px;
    margin-right: 15px;
    border-radius: 5px;
    color: #666;
  }
}
::v-deep {
  .imgbox .list,
  .box-card {
    width: 108px;
    height: 108px;
  }
}
.stock-table {
  width: 100%;
  border: 1px solid #e5e5e5;
  text-align: left;
  & > thead {
    background-color: #f8f8f8;
    // border-bottom: $border;
    th {
      font-weight: 400;
    }
  }
  & > tfoot {
    border-top: $border;
  }
  td,
  th {
    padding: 16px 10px;
  }
  td {
    @include input;
    input[type='text'].spec-code-input {
      width: 130px;
    }
    border-top: $border;
    & + td {
      border-left: $border;
    }
  }
  .links {
    > * + * {
      margin-left: 2px;
    }
  }
  .btns {
    margin-left: 8px;
  }
}
.expressFee {
  color: #606266;
  cursor: auto;
  margin-left: 8px;

  ::v-deep .el-input {
    width: 150px;
  }
}
</style>
