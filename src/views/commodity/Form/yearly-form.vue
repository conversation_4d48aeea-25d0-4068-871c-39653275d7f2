<template>
  <div v-frag>
    <div class="chapter">
      <el-form :model="form" :rules="rules" class="form" label-width="150px" ref="form">
        <Anchor :labelList="labelList">
          <template :slot="labelList[0].key">
            <div class="section">
              <div class="divider">
                <span class="text">{{ labelList[0].name }}</span>
              </div>
              <div class="content">
                <el-form-item label="采货类型" prop="purchaseType" ref="purchaseType">
                  <el-select :disabled="isUpdate || isDetail" clearable v-model="form.purchaseType">
                    <el-option :key="item.id" :label="item.name" :value="item.id" v-for="item in purchaseTypeOptions"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="商品类型" prop="type" ref="type">
                  <el-select disabled clearable v-model="form.type">
                    <el-option :key="idx" :label="item.label" :loading="typeOptionsLoading" :value="item.value" v-for="(item, idx) in typeOptions"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="商品名称" prop="name" ref="name">
                  <el-input :disabled="isDetail" v-model.trim="form.name"></el-input>
                </el-form-item>
                <el-form-item label="商品品牌" prop="brandId" ref="brandId">
                  <el-select ref="brandIdSelect" disabled :value="form.brandId" filterable placeholder="请选择" @change="brandIdChange">
                    <el-option v-for="item in brandList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
                  </el-select>
                </el-form-item>
              </div>
            </div>
          </template>
          <template :slot="labelList[1].key">
            <div class="section">
              <div class="divider">
                <span class="text">{{ labelList[1].name }}</span>
              </div>
              <!-- 没有参与活动的和复制的可以编辑规格 ,编辑和查看不能编辑规格-->
              <div class="content">
                <CommodityStock
                  :id="id"
                  :purchaseType="form.purchaseType"
                  :brandInfo="currentBrandInfo"
                  :columns="skuColumns"
                  :isCopy="isCopy"
                  :isAdd="isAdd"
                  :isDetail="isDetail"
                  :isSpecEditable="isCopy || (!joinActivity && !id)"
                  :isSpecVisible="isSpecVisible"
                  :isUpdate="isUpdate"
                  :restrictionNum="form.restrictionNum"
                  ref="commodityStock"
                  @assignFormData="assignFormData"
                  :SKUNumbers.sync="SKUNumbers"
                  commodity-type="yearly"
                />
                <el-form-item class="join-activity-tips" v-if="joinActivity"> 当前商品参与{{ joinActivity }}活动，活动期间不能删除规格 </el-form-item>
              </div>
            </div>
          </template>
          <template :slot="labelList[2].key">
            <div class="section media">
              <div class="divider">
                <span class="text">{{ labelList[2].name }}</span>
              </div>
              <div class="content">
                <el-form-item label="已关联的SKU条码：">
                  <span style="margin-right: 10px">{{ tradeCenterCommodityExt.detailSpecCode || '暂无关联' }}</span>
                  <el-button size="mini" :disabled="isDetail" type="primary" @click="openSpecCode('detailSpecCode')">更换条码</el-button>
                  <el-button size="mini" type="text" @click="removeSpecCode('detailSpecCode')" :disabled="isDetail">删除关联</el-button>
                </el-form-item>
                <el-form-item label="商品缩略图" prop="thumbnailUrl" ref="thumbnailUrl">
                  <ImageManagement :maxSize="100 * 1024" v-model="form.thumbnailUrl" :disabled="isDetail" @input="validateField('thumbnailUrl')"></ImageManagement>
                  <div class="tip">宽高：500*500，大小：最大100k，数量：1张</div>
                </el-form-item>
              </div>
            </div>
          </template>
          <template :slot="labelList[3].key">
            <div class="section">
              <div class="divider">
                <span class="text">{{ labelList[3].name }}</span>
              </div>
              <div class="content">
                <el-form-item label="限购设置"> <el-switch :disabled="isDetail" v-model="isPurchase"> </el-switch><span class="content-tag">开启后表示在限购时间段内该商品购买会进行限购，不在限购时间内不限购</span> </el-form-item>
                <div class="restrictions" v-if="isPurchase">
                  <el-form-item label="限购时间" required>
                    <el-date-picker :default-time="['00:00:00', '23:59:59']" :disabled="isDetail" align="right" end-placeholder="结束日期" start-placeholder="开始日期" type="daterange" v-model="form.restrictionNumTime"></el-date-picker>
                  </el-form-item>
                  <div class="purchase-box">
                    <div class="purchase-box-label commo-asterisk">限购条件</div>
                    <el-checkbox-group v-model="restrictionNumCheckList">
                      <div>
                        <el-checkbox label="restrictionNum">
                          <span class="label">条件一：购买总数限制</span>
                        </el-checkbox>
                        <el-input :disabled="isDetail" v-model.number="form.restrictionNum" type="number"><template slot="append">件</template></el-input>
                      </div>
                      <div>
                        <el-checkbox label="singleRestrictionNum">
                          <span class="label">条件二：单次购买总数限制</span>
                        </el-checkbox>
                        <el-input :disabled="isDetail" v-model.number="form.singleRestrictionNum" type="number">
                          <template slot="append">件</template>
                        </el-input>
                      </div>
                    </el-checkbox-group>

                    <div class="explain">
                      限购说明：限购是在限定的时间段内针对设置的限购方式来进行限购。<br />
                      1、条件一是指用户可以购买这个商品一共可以买多少件；<br />
                      2、条件二是指用户单次购买这个商品可以购买多少件；<br />
                      3、条件一和条件二可以只选择一个进行设置，也可以两个都勾选进行设置；<br />
                      4、条件一和条件二都启用的话，设置的数量：条件一必须大于等于条件二；
                    </div>
                  </div>
                </div>
                <el-form-item label="备注" prop="remarks">
                  <el-input :autosize="{ minRows: 2 }" type="textarea" v-model.trim="form.remarks" placeholder="请输入备注"></el-input>
                </el-form-item>
                <el-form-item label="运费设置" prop="freeShipping">
                  <el-radio-group :disabled="isDetail" v-model="form.freeShipping">
                    <el-radio :label="0">包邮</el-radio>
                    <el-radio :label="1">
                      <span>不包邮</span>
                      <span class="expressFee" style="margin-left: 10px" v-if="form.freeShipping === 1">
                        <el-input placeholder="请输入运费金额" size="small" v-model.trim="form.expressFee" style="margin-right: 4px"></el-input>
                        <span>元</span>
                      </span>
                    </el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="状态" prop="status">
                  <el-radio-group :disabled="isDetail" v-model="form.status">
                    <el-radio label="1">上架</el-radio>
                    <el-radio label="3">下架</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="是否隐藏">
                  <el-radio-group disabled v-model="form.isHidden">
                    <el-radio label="0">否</el-radio>
                    <el-radio label="1">是</el-radio>
                  </el-radio-group>
                  <span>（隐藏后不会在分类列表展示，也无法被搜索到）</span>
                </el-form-item>
                <el-form-item label="是否参与返利">
                  <el-radio-group disabled v-model="form.creditBack">
                    <el-radio label="0">否</el-radio>
                    <el-radio label="1">是</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="是否可使用返利">
                  <el-radio-group disabled v-model="form.creditDeduction">
                    <el-radio label="0">否</el-radio>
                    <el-radio label="1">是</el-radio>
                  </el-radio-group>
                </el-form-item>
              </div>
            </div>
          </template>
        </Anchor>
        <div class="footer-wrap" v-if="!isDetail">
          <div class="footer">
            <el-button :loading="submitLoading" @click="onSubmit" type="primary">提交</el-button>
          </div>
        </div>
      </el-form>
    </div>
    <FXSelectGoods ref="FXSelectGoodsRadio" :brandInfo="currentBrandInfo" @save="FXSelectGoodsRadioSave" configType="Radio" />
  </div>
</template>

<script>
import { add, getByCouponId, getById, listCommodityParams, update, validationByCommodityId, commodityGetRelationActivityInfo } from '@/api/commodity';
import { listCustoms } from '@/api/common/dict';
import dict from '@/components/Common/dicts';
import Process from '@/utils/Process';
import pick from 'lodash/pick';
import ImageManagement from '@/components/ImageManagement/index.vue';
import CommodityStock from '@/components/CommodityStock/index.vue';
import { parseDefaultTime } from '@/utils';
import FXSelectGoods from '@/components/FXSelectGoods/index.vue';
import Anchor from '@/components/Anchor/index.vue';
import pickBy from 'lodash/pickBy';
import { brandListByCondition } from '@/api/setting/commodity/brand'; // 返回一个新对象，值由真值组成
export default {
  name: 'commodity-form',
  data() {
    return {
      SpecCodeType: '', // SKU条码类型 detailSpecCode paramSpecCode
      tradeCenterCommodityExt: {
        detailSpecCode: '', // 商品详情对应的规格编码（用于商品关联到分销商品池信息）
        paramSpecCode: '' // 商品参数对应的规格编码（用于商品关联到主数据信息）
      }, // 分销-商品条码关联信息
      labelList: [
        { name: '基础信息', key: 'info' },
        { name: '价格库存', key: 'price-of-inventory' },
        { name: '商品详情', key: 'details' },
        { name: '其他信息', key: 'other' }
      ],
      SKUNumbers: 0, // 已选择sku 数量
      purchaseTypeOptions: [
        { id: 'PURCHASE', name: '采销' },
        { id: 'DROP_SHIPPING', name: '一件代发' }
      ], // 采货类型
      isPurchase: false, // 是否控制限购
      joinActivity: '',
      uploadLoading: false,
      form: {
        purchaseType: '', // 采货类型
        isSet: '0', // 是否套装
        setItemList: [], // 套装子商品列表
        type: 'ACTUAL', // 商品类型 默认：大贸
        name: '', // 名称
        brandId: '', // 品牌id
        commodityParam: {}, // 商品属性
        thumbnailUrl: '', // 商品缩略图
        restrictionStartDate: '', // 限购时间开始
        restrictionEndDate: '', // 限购时间结束
        restrictionNum: '', // 限购 购买总数限制
        singleRestrictionNum: '', // 单次购买总数限制
        remarks: '',
        status: '1',
        expressFee: '',
        isHidden: '1',
        creditBack: '1', // 是否参与返利
        creditDeduction: '0', // 是否使用返利
        freeShipping: 0
      },
      originBrandList: [], // 品牌接口返回数据列表
      typeOptionsLoading: false, // 商品类型数据字典加载
      typeOptions: [], // 商品类型
      customsListLoading: true,
      customsList: [],
      pickKeys: ['isSet', 'setItemList', 'type', 'name', 'brandId', 'remarks', 'thumbnailUrl', 'restrictionStartDate', 'restrictionEndDate', 'status', 'isHidden', 'creditBack', 'creditDeduction', 'purchaseType'],
      commodityParams: [], // 商品参数
      commodityParamsLoading: true,
      submitLoading: false,
      dataLoaidng: false,
      featureListVisible: false,
      shareCheckList: [],
      restrictionNumCheckList: [], // 限购条件
      previousBrandId: '',
      isUsed: true
    };
  },
  props: {
    id: null,
    isAdd: Boolean,
    isCopy: Boolean,
    isEdit: Boolean,
    isDetail: {
      type: Boolean,
      default: false
    }
  },

  watch: {
    'form.type'(type) {
      // 当为卡券商品时，套装为不足套
      if (type === 'CARD') {
        this.form.isSet = '0';
        this.form.setItemList = [];
      }
    }
  },
  created() {},
  activated() {
    const { query } = this.$route;
    if (this.isAdd && query.specCode) {
      // 添加商品时，外部传入值
      Object.assign(
        this.form,
        pickBy(this.$route.query, (v, k) => this.form.hasOwnProperty(k))
      );
    }
    this.commodityParamsLoading = true;
    listCommodityParams()
      .then((res) => {
        this.commodityParams = res.data;
        this.$nextTick(() => {
          this.$refs.form.clearValidate();
        });
      })
      .finally(() => {
        this.commodityParamsLoading = false;
      });
  },
  mounted() {
    // 如果
    if (this.isUpdate) {
      validationByCommodityId(this.id).then((res) => {
        if (res.data) {
          this.joinActivity = res.data;
        }
      });
    }
    const stacks = new Process();
    this.fetchCommodityType();
    listCustoms()
      .then((res) => {
        this.customsList = res.data;
      })
      .finally(() => {
        this.customsListLoading = false;
      });

    const p1 = listCommodityParams()
      .then((res) => {
        this.commodityParams = res.data;
      })
      .finally(() => {
        this.commodityParamsLoading = false;
      });
    stacks.push(p1);

    if (this.id) {
      this.dataLoaidng = true;
      const p3 = getById(this.id)
        .then((res) => {
          const commodityParam = {};
          const tradeCenterCommodity = res.data?.tradeCenterCommodity || {};
          this.tradeCenterCommodityExt = { ...this.tradeCenterCommodityExt, ...res.data?.tradeCenterCommodityExt };
          const {
            commodityParamRelationList,
            commodityMultimediaVO,
            //   marketPrice,
            skuVOlist,
            specificationVOList,
            expressFee,
            isHidden,
            creditBack,
            creditDeduction,
            isSet,
            type,
            commodityExtensionVO,
            restrictionStartDate,
            restrictionEndDate,
            restrictionNum,
            singleRestrictionNum
          } = tradeCenterCommodity;
          commodityParamRelationList.forEach(({ paramId, paramValue }) => {
            commodityParam[paramId] = paramValue;
          });

          commodityMultimediaVO.forEach(({ type, jsonValue }) => {
            let value;
            try {
              value = JSON.parse(jsonValue);
            } catch (e) {
              // console.error(e);
              jsonValue && (value = jsonValue);
            }
            if (!value) {
              return;
            }
          });
          this.form = {
            ...this.form,
            commodityParam,
            expressFee,
            isHidden,
            creditBack,
            creditDeduction,
            skuVOlist,
            singleRestrictionNum: singleRestrictionNum > 0 ? singleRestrictionNum : '',
            restrictionNum: restrictionNum > 0 ? restrictionNum : '',
            freeShipping: expressFee ? 1 : 0,
            ...pick(tradeCenterCommodity, [...this.pickKeys]),
            restrictionNumTime: '' // 限购时间
          };
          // 限购时间
          if (restrictionStartDate) {
            this.isPurchase = true;
            const arr = [];
            if (singleRestrictionNum > 0) {
              arr.push('singleRestrictionNum');
            }
            if (restrictionNum > 0) {
              arr.push('restrictionNum');
            }
            this.restrictionNumCheckList = arr;
            this.form.restrictionNumTime = [new Date(restrictionStartDate), new Date(restrictionEndDate)];
          }

          if (commodityExtensionVO) {
            if (isSet === '1') {
              this.form.isSet = '1';
              this.form.setItemList = commodityExtensionVO.setItemList;
            }
            if (type === 'CARD') {
              this.form.cardId = commodityExtensionVO.cardId;
              this.fetchCoupon(commodityExtensionVO.cardId);
            }
            this.form.creditBack = commodityExtensionVO.creditBack;
            this.form.creditDeduction = commodityExtensionVO.creditDeduction;
            this.form.purchaseType = commodityExtensionVO.purchaseType || '';
          }
          // 初始化规格和sku信息
          this.$refs.commodityStock.setData({
            skuVOlist,
            specificationVOList
          });
        })
        .finally(() => {
          this.dataLoaidng = false;
        });
      stacks.push(p3);
    }
    // 清空初始化的校验信息
    stacks.all().then(() => {
      this.$nextTick(() => {
        this.$refs.form.clearValidate();
      });
    });
    this.fetchBrand();
  },
  computed: {
    skuColumns() {
      const list = [
        {
          label: '商品条码', // 列名
          prop: 'specCode', // 列属性
          editable: false, // 是否支持编辑
          width: '150px'
        },
        {
          label: '规格标识', // 列名
          prop: 'id', // 列属性
          editable: false, // 是否支持编辑
          width: '150px'
        },
        {
          label: '价格（通过年框推品清单读取客户年框政策价格）',
          prop: 'price',
          editable: false
        },
        {
          label: '库存',
          prop: 'stock',
          editable: true
        },
        {
          label: '起订量',
          prop: 'miniOrderQuantity',
          editable: true
        }
      ];
      if (this.isAdd || this.isCopy) {
        return list.filter((item) => item.prop !== 'salesVolume');
      }
      return list;
    },
    isUpdate() {
      return this.id && !this.isCopy && !this.isDetail;
    },
    isSpecVisible() {
      if (this.form.type === 'CARD') {
        return false;
      }
      if ((this.form.type === 'ACTUAL' || this.form.type === 'GLOBAL') && this.form.isSet === '1') {
        return false;
      }
      return true;
    },
    rules() {
      const rules = {
        type: [
          {
            required: true,
            message: '请选择商品类型',
            trigger: 'change'
          }
        ],
        name: [
          {
            required: true,
            message: '请输入商品名称',
            trigger: 'change'
          }
        ],
        brandId: [
          {
            required: true,
            message: '请选择商品品牌',
            trigger: 'change'
          }
        ],
        purchaseType: [
          {
            required: true,
            message: '请选择采货类型',
            trigger: 'change'
          }
        ],
        thumbnailUrl: [
          {
            required: true,
            message: '请选择商品缩略图',
            trigger: 'change'
          }
        ]
      };
      if (this.form.type === 'ACTUAL' || this.form.type === 'GLOBAL') {
        rules['isSet'] = {
          required: true,
          message: '必填信息',
          trigger: 'change'
        };
      }
      if (this.form.isSet === '1') {
        rules['setItemList'] = {
          required: true,
          message: '请选择适用商品',
          trigger: 'change'
        };
      }
      if (this.form.restrictionNum || this.form.restrictionNum === 0) {
        rules['restrictionNumTime'] = {
          required: true,
          message: '请选择限购时间',
          trigger: 'change'
        };
      }
      this.commodityParams.forEach(({ required, id, name }) => {
        if (required === '1') {
          rules[`commodityParam.${id}`] = {
            required: true,
            message: `请填写${name}`,
            trigger: 'change'
          };
        }
      });
      return rules;
    },
    brandList() {
      const { isUpdate, originBrandList, currentBrandInfo } = this;
      const arr = isUpdate ? originBrandList.filter((item) => item?.orderBrandType === currentBrandInfo?.orderBrandType) : originBrandList;
      return arr.map((item) => ({
        id: item.id,
        name: item.name
      }));
    },
    currentBrandInfo() {
      const { brandId } = this.form;
      if (!brandId) {
        return null;
      }
      return this.originBrandList.find((item) => item.id === this.form.brandId);
    }
  },
  methods: {
    // 获取品牌列表
    fetchBrand() {
      const params = {
        filterFlag: '1'
      };
      brandListByCondition(params).then((res) => {
        this.originBrandList = res.data || [];
      });
    },
    // 获取商品类型
    fetchCommodityType() {
      // 订单状态列表
      dict('COMMONODITY_TYPE').then((res) => {
        this.typeOptions = res;
      });
    },
    // 通过id查询优惠券信息
    fetchCoupon(id) {
      getByCouponId(id).then((response) => {
        const couponItem = response.data;
        // 满减活动-有门槛
        if (response.data.couponType === 'MONEY_OFF' && response.data.useThreshold) {
          couponItem.couponContent = `满${response.data.useThreshold}减${response.data.discountContent}元`;
        }
        // 满减活动-无门槛
        if (response.data.couponType === 'MONEY_OFF' && !response.data.useThreshold) {
          couponItem.couponContent = `减${response.data.discountContent}元`;
        }
        // 满折活动-有门槛
        if (response.data.couponType === 'RATE_OFF' && response.data.useThreshold) {
          couponItem.couponContent = `满${response.data.useThreshold}元${response.data.discountContent}折`;
        }
        // 满折活动-无门槛
        if (response.data.couponType === 'RATE_OFF' && !response.data.useThreshold) {
          couponItem.couponContent = `${response.data.discountContent}折`;
        }
        this.form.couponList.push(couponItem);
      });
    },
    getCommodityParams() {
      const { commodityParam } = this.form;
      const params = this.commodityParams.map(({ id }) => ({
        paramId: id,
        paramValue: commodityParam[id]
      }));
      return { paramRelationCmdList: params };
    },
    validate() {
      const p1 = this.$refs.commodityStock.validate();
      const p2 = this.$refs.form.validate();
      return Promise.all([p1, p2])
        .then(([obj]) => {
          // 默认填充最低控价
          if (obj?.skuVOCmd?.updSkuList?.length) {
            obj.skuVOCmd.updSkuList.forEach((item) => {
              item.minControlPrice = 99999;
            });
          }
          return obj;
        })
        .catch((err) => {
          if (this.isObject(err) && Object.keys(err).length) {
            const o = Object.values(err)[0][0] || null;
            if (o && o.message) {
              this.scrollView(err);
              this.$message.error(o.message);
            }
          }
          if (typeof err === 'string') {
            // 跳转到sku组件
            this.scrollView({ commodityStock: {} });
          }
          return Promise.reject(err || '有必填项未填写');
        });
    },
    // 判断是否为对象
    isObject(obj) {
      return Object.prototype.toString.call(obj) === '[object Object]';
    },
    onSubmit() {
      this.submitLoading = true;
      let validateResult = true;
      if ((this.form.type === 'ACTUAL' || this.form.type === 'GLOBAL') && this.form.isSet === '1') {
        const validateResultArr = this.$refs['itemCommodity'].validateArr;
        validateResultArr.forEach((item) => {
          if (!item.validateResult) {
            validateResult = false;
          }
        });
      }

      // 校验套装子商品里面的数据是否符合要求
      if (!validateResult) {
        this.$message.error('数据有误，请检查之后再提交');
        this.submitLoading = false;
        return false;
      }
      this.validate()
        .then((obj) => {
          const { expressFee, freeShipping } = this.form;
          const conmmodityExtension = this.isUpdate ? 'updateCommodityExtensionCmd' : 'createCommodityExtensionCmd';
          const reqData = {
            ...pick(this.form, this.pickKeys),
            ...this.getCommodityParams(),
            expressFee: freeShipping && expressFee,
            ...obj
          };
          // reqData.showMaterial = reqData.showMaterial ? '1' : 0;
          let req = add;
          if (this.isUpdate) {
            req = update;
            reqData.id = this.id;
          }
          reqData[conmmodityExtension] = {
            creditDeduction: '0',
            discount: '0'
          };
          if (this.form.type === 'ACTUAL' || (this.form.type === 'GLOBAL' && this.form.isSet === '1')) {
            reqData[conmmodityExtension].setItemList = this.form.setItemList;
          } else {
            reqData[conmmodityExtension].setItemList = [];
          }
          if (this.form.type === 'CARD') {
            reqData[conmmodityExtension].cardId = this.form.couponList[0].id;
          }
          reqData[conmmodityExtension].creditBack = this.form.creditBack;
          reqData[conmmodityExtension].creditDeduction = this.form.creditDeduction;
          reqData[conmmodityExtension].purchaseType = this.form.purchaseType;
          delete reqData.creditBack;
          delete reqData.creditDeduction;
          if (this.isPurchase) {
            // 是否控制限购
            if (this.form.restrictionNumTime.length === 0) {
              const errorTxt = '已开启限购控制，请选择限购时间';
              this.$message.error(errorTxt);
              return Promise.reject(errorTxt);
            }
            reqData.restrictionStartDate = parseDefaultTime(this.form.restrictionNumTime[0]);
            reqData.restrictionEndDate = parseDefaultTime(this.form.restrictionNumTime[1]);
            if (this.restrictionNumCheckList.length === 0) {
              const errorTxt = '开启限购至少选一种限购条件';
              this.$message.error(errorTxt);
              return Promise.reject(errorTxt);
            }
            if (this.restrictionNumCheckList.findIndex((key) => key === 'singleRestrictionNum') !== -1) {
              const singleRestrictionNum = this.form['singleRestrictionNum'];
              if (singleRestrictionNum <= 0 || !this.isInteger(singleRestrictionNum)) {
                const errorTxt = '单次购买总数限制必须大于0件且必须是整数';
                this.$message.error(errorTxt);
                return Promise.reject(errorTxt);
              }
              reqData.singleRestrictionNum = singleRestrictionNum;
            } else {
              reqData.singleRestrictionNum = 0;
            }

            if (this.restrictionNumCheckList.findIndex((key) => key === 'restrictionNum') !== -1) {
              const restrictionNum = this.form['restrictionNum'];
              if (restrictionNum <= 0 || !this.isInteger(restrictionNum)) {
                const errorTxt = '购买总数限制必须大于0件且必须是整数';
                this.$message.error(errorTxt);
                return Promise.reject(errorTxt);
              }
              reqData.restrictionNum = restrictionNum;
            } else {
              reqData.restrictionNum = 0;
            }
          } else {
            reqData.restrictionStartDate = 0;
            reqData.restrictionEndDate = 0;
            reqData.singleRestrictionNum = 0;
            reqData.restrictionNum = 0;
          }
          const queryData = {
            tradeCenterCommodity: reqData,
            tradeCenterCommodityExt: this.tradeCenterCommodityExt
          };
          if (this.isUpdate && reqData?.skuVOCmd?.updSkuList?.length > 0) {
            this.isUsed = false;
            this.getRelation(reqData, queryData, req);
          }
          if (!this.isUsed) return;
          return req(queryData);
        })
        .then((res) => {
          if (res.code === '0') {
            this.$message.success('提交成功');
            this.$back('/commodity/yearly/list');
          }
        })
        .catch((e) => {
          // 这里不需要 全局已经有提示了
          if (e && typeof e === 'string') {
            this.$message.error(e);
          }
        })
        .finally(() => {
          this.submitLoading = false;
        });
    },
    // 判断是否被积分兑换活动引用
    getRelation({ skuVOCmd, id }, queryData, request) {
      const skuList = new Map(skuVOCmd?.updSkuList.map(({ id, price }) => [id, Number(price)]));
      const obj = {
        commodityId: id,
        skuList: Array.from(
          this.form.skuVOlist
            .filter(({ id }) => skuList.has(id))
            .map(({ id, price }) => ({
              price: Number(price) !== skuList.get(id) ? skuList.get(id) : undefined,
              skuId: id
            }))
        ).filter(({ price }) => price !== undefined)
      };
      if (!obj.skuList.length) {
        this.isUsed = true;
        return;
      }
      commodityGetRelationActivityInfo(obj).then((res) => {
        if (!res.success) return;
        const data = res?.data || [];
        if (!data.length) {
          this.isUsed = true;
          this.submitRequest(request, queryData);
          return;
        }
        const msg = data.find((item) => item.bizType === 'CreditActivityExchange')?.content || '';
        if (!msg) return;
        this.$confirm(`<span style="white-space: pre-wrap">${msg}</span>`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          dangerouslyUseHTMLString: true,
          type: 'warning'
        })
          .then(() => {
            this.isUsed = true;
            this.submitRequest(request, queryData);
          })
          .catch(() => {
            this.isUsed = false;
          });
      });
    },
    submitRequest(req, queryData) {
      req(queryData).then(() => {
        this.$message.success('提交成功');
        this.$back('/commodity/list');
      });
    },
    isInteger(obj) {
      // 判断是否是整数
      return parseInt(obj, 10) === obj;
    },
    // sku 合并到主数据
    assignFormData(skuOBJ) {
      // 合并当前商品信息
      //  -采货类型、商品分组：需要让运营自己选择，不能进行匹配；
      //  -商品名称：默认取【第一个SKU的商品名称】；
      //  -商品品牌：为选择的品牌；
      //  -商品详情：取【第一个SKU的商品详情】，如果没有就不取；
      !this.form.name && (this.form.name = skuOBJ.commodityName);
      !this.form.brandId && (this.form.brandId = skuOBJ.brandId);
      if (!this.form.type) {
        let key = '';
        switch (skuOBJ.commodityType) {
          case 'GENERAL_TRADE':
            // 大贸
            key = 'ACTUAL';
            break;
          case 'CROSS_BORDER_TRADE':
            // 海淘
            key = 'GLOBAL';
            break;
          case 'GENERAL_AND_CROSS_BORDER_TRADE':
            // 大贸海淘  如果是【大贸、大贸海淘】都显示为【大贸】
            key = 'ACTUAL';
            break;
        }
        this.form.type = key;
      }
      if (!this.tradeCenterCommodityExt.detailSpecCode) {
        // 如果已经关联则不覆盖
        this.MergeDetails(skuOBJ);
      }
      if (!this.tradeCenterCommodityExt.paramSpecCode) {
        // 如果已经关联则不覆盖
        this.MergeCommodityParams(skuOBJ);
      }
    },
    // 对部分表单字段进行校验的方法
    validateField(type) {
      this.$refs['form'].validateField(type);
    },
    // 滚动到固定地方
    scrollView(object) {
      if (!object) return;
      for (const i in object) {
        let dom = this.$refs[i];
        if (Object.prototype.toString.call(dom) !== '[object Object]') {
          dom = dom[0];
        }
        dom.$el.scrollIntoView({
          block: 'center',
          behavior: 'smooth'
        });
        break;
      }
    },
    // 删除关联
    removeSpecCode(type) {
      const text = type === 'detailSpecCode' ? '是否删除【商品详情】sku关联？' : '是否删除【商品参数】sku关联？';
      this.$confirm(text, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          // 提交数据
          this.$message.success('删除成功，需要保存后生效！');
          this.tradeCenterCommodityExt[type] = '';
        })
        .catch(() => {
          this.$message.info('已取消删除!');
        });
    },
    openSpecCode(type) {
      if (!this.form.purchaseType) {
        this.$message.error('请先选择采货类型');
        return;
      }
      if (!this.form.brandId) {
        this.$message.error('请先选择商品品牌');
        return;
      }
      this.SpecCodeType = type;
      this.$refs.FXSelectGoodsRadio.open();
    },
    // 合并详情
    MergeDetails(skuOBJ, type) {
      const isCover = type === 'cover'; // 是否覆盖原数据
      if ((!this.form.thumbnailUrl && skuOBJ['logoUrl']) || isCover) {
        this.form.thumbnailUrl = skuOBJ['logoUrl'] || '';
      }
      this.tradeCenterCommodityExt['detailSpecCode'] = skuOBJ.barcode;
    },
    // 合并商品参数
    MergeCommodityParams(skuOBJ, type) {
      const isCover = type === 'cover'; // 是否覆盖原数据
      this.commodityParams.map((item) => {
        if (item.commodityCenterFieldRelated === '1' && item.commodityCenterFeildVO) {
          // 检查已关联主数据的  覆盖原数据
          const ParamsKey = item.commodityCenterFeildVO?.fieldName;
          const { commodityExtendInfoVO = {} } = skuOBJ;
          const val = commodityExtendInfoVO[ParamsKey] ?? '';
          this.$set(this.form.commodityParam, [item.id], val);
        } else {
          if (isCover && !this.form.commodityParam[item.id]) {
            this.$set(this.form.commodityParam, [item.id], '');
          }
        }
      });
      this.tradeCenterCommodityExt['paramSpecCode'] = skuOBJ.barcode;
    },
    // 更换条码
    FXSelectGoodsRadioSave(skuOBJ) {
      const k = this.SpecCodeType;
      if (k === 'detailSpecCode') {
        // 替换详情数据
        this.MergeDetails(skuOBJ, 'cover');
      } else if (k === 'paramSpecCode') {
        // 替换商品参数
        this.MergeCommodityParams(skuOBJ, 'cover');
      }
    },
    // 品牌改变
    brandIdChange(val) {
      const { stockData = {}, skuVOlist = [], specifications = [] } = this.$refs.commodityStock;
      const { isAdd, currentBrandInfo } = this;
      const changeBrandInfo = this.originBrandList.find((item) => item.id === val);
      const currentOrderBrandType = currentBrandInfo?.orderBrandType;
      const changeOrderBrandType = changeBrandInfo?.orderBrandType;
      const emptyFlag = this.hasNonEmptyValue(stockData);
      if (isAdd && currentOrderBrandType && changeOrderBrandType && changeOrderBrandType !== currentOrderBrandType && (skuVOlist.length || specifications.length || emptyFlag)) {
        this.$confirm('您修改的品牌会导致清空下面已选择的商品，请确认是否清空？')
          .then(() => {
            this.$refs.brandIdSelect.blur();
            this.form.brandId = val;
            this.resetFormValue();
          })
          .catch(() => {
            this.$refs.brandIdSelect.blur();
          });
      } else {
        this.form.brandId = val;
      }
    },
    // 判断对象中是否不包含空值
    hasNonEmptyValue(obj) {
      const values = Object.values(obj);
      return values.some((value) => value !== null && value !== undefined && value !== '');
    },
    // 品牌改变清除表单值
    resetFormValue() {
      this.form.name = '';
      this.form.thumbnailUrl = '';
      this.tradeCenterCommodityExt.paramSpecCode = '';
      this.tradeCenterCommodityExt.detailSpecCode = '';
      // 初始化规格和sku信息
      this.$refs.commodityStock.setData({
        skuVOlist: [],
        specificationVOList: []
      });
    }
  },
  components: {
    Anchor,
    CommodityStock,
    ImageManagement,
    FXSelectGoods
  }
};
</script>

<style lang="scss" scoped>
@import './styles';
</style>
