.divider {
  position: relative;
  display: table;
  margin: 24px 0;
  white-space: nowrap;
  text-align: center;
  width: 100%;
  padding-right: 110px;

  &::before,
  &::after {
    position: relative;
    display: table-cell;
    width: 50%;
    border-top: 1px solid var(--border-color-lighter);
    content: '';
    transform: translateY(50%);
  }

  .text {
    font-weight: 500;
    color: var(--color-text-primary);
    font-size: 16px;
    padding: 0 24px;
  }
}
.link {
  cursor: pointer;
  color: var(--color-primary);
}

.form {
  .add-coupon {
    width: 500px;
    margin-left: 150px;
    margin-bottom: 20px;
  }
  ::v-deep {
    .el-form-item {
      .tip {
        color: #868D9F;
        font-size: 12px;
        line-height: 16px;
        padding-top: 4px;
      }

      &.is-error .tip {
        display: none;
      }
    }

    .el-upload-list > li {
      vertical-align: bottom;
    }
  }

  .el-checkbox-group {
    .el-checkbox {
      margin: 0 10px 0 0;
    }
  }
}

.chapter {
  background-color: #fff;
  padding: 24px 0 24px 32px;

  .section {
    & > .content {
      padding: 16px 110px 16px 0;
      .content-tag {
        font-size: 12px;
        color: #868D9F;
        margin-left: 20px;
      }
    }

    &.media {
      ::v-deep .el-form-item__content {
        width: auto;
      }
    }
  }

  .section + .section {
    margin-top: 48px;
  }
}

.shipping {
  ::v-deep .el-radio {
    display: block;
    line-height: 36px;
    margin-left: 0;
  }

  .expressFee {
    color: #606266;
    cursor: auto;
    margin-left: 8px;
    ::v-deep .el-input {
      width: 150px;
    }
  }
}

.footer {
  position: fixed;
  width: 100%;
  left: 0;
  bottom: 0;
  background-color: #fff;
  padding: 8px 16px;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.15);
  text-align: right;
  z-index: 1;
}

.app-container {
  padding-bottom: 80px;
}

.content-form-item {
  margin-top: 16px;
}

.showMaterial {
  ::v-deep {
    .el-form-item__content {
      width: 750px;
    }

    &.el-form-item.is-error {
      .inner-textarea {
        .el-textarea__inner {
          border-color: #dcdfe6;

          &:hover {
            border-color: #c0c4cc;
          }

          &:focus {
            border-color: var(--color-primary);
          }
        }
      }
    }

    .el-checkbox {
      display: block;
    }

    .el-checkbox + .el-checkbox {
      margin-left: 0;
    }
  }
}

.required::before {
  content: '*';
  color: var(--color-danger);
  margin-right: 4px;
}

.member-detail {
  font-size: 14px;
}

.hideOrShow {
  display: inline-block;
}

::v-deep .el-checkbox-group {
  font-size: 14px;
}

.article {
  span {
    display: block;
  }

  .short-inline {
    width: 160px;
    display: inline-block;
    clear: left;
  }
}

.brand {
  .el-radio {
    margin-bottom: 10px;
    &:first-child {
      margin-left: 10px;
    }
  }
}

.upload {
  width: 146px;
  position: relative;
  .spec-close {
    width: 10px;
    height: 10px;
    color: #999;
    position: absolute;
    top: 5px;
    right: 10px;
    cursor: pointer;
  }
  .upload-btn {
    text-align: center;
    background-color: #fff;
    border: 1px dashed #e5e5e5;
    padding: 37px 0;
    color: #666;
    text-align: center;
    .upload-text,
    .upload-icon {
      color: #666;
      word-break: break-all;
      width: 100%;
      line-height: 16px;
    }
    ::v-deep .el-upload {
      display: block;
    }
    .suggested-size {
      color: rgba(0, 0, 0, 0.35);
      margin-top: 3px;
    }
  }
}

.join-activity-tips {
  color: #999;
}
.icon-style {
  font-size: 34px;
  .upload-text {
    display: block;
    text-align: center;
  }
}

.restrictions {
  ::v-deep .el-form-item {
    display: inline-block;
    .el-form-item__content {
      width: 300px;
    }
  }
}

.purchase-box {
  margin-left: 150px;
  position: relative;
  .purchase-box-label {
    position: absolute;
    left: -150px;
    top: 0;
    text-align: right;
    float: left;
    font-size: 14px;
    color: #606266;
    line-height: 40px;
    padding: 0 12px 0 0;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    width: 150px;
    font-weight: 700;
  }
  ::v-deep .el-checkbox-group .el-checkbox {
    margin-bottom: 20px;
  }
  ::v-deep .el-input-group {
    width: 143px;
  }
  .label {
    width: 180px;
    display: inline-block;
  }

  .explain {
    font-size: 12px;
    color: #666;
    margin-bottom: 20px;
    line-height: 1.6;
  }
}
::v-deep .el-select {
  width: 300px;
}
.select-goods-row {
  &-top {
    margin-bottom: 10px;
    &-tip {
      margin-left: 30px;
      margin-right: 10px;
    }
  }
}

.chapter,
.form {
  height: 100%;
  padding-bottom: 0;

  ::-webkit-scrollbar {
    display: none;
  }
}
