<template>
  <div v-frag>
    <div class="chapter">
      <el-form :model="form" :rules="rules" class="form" label-width="150px" ref="form">
        <Anchor :labelList="labelList">
          <template :slot="labelList[0].key">
            <div class="section">
              <div class="divider">
                <span class="text">{{ labelList[0].name }}</span>
              </div>
              <div class="content">
                <el-form-item label="采货类型" prop="purchaseType" ref="purchaseType">
                  <el-select :disabled="isUpdate || isDetail" clearable v-model="form.purchaseType">
                    <el-option :key="item.id" :label="item.name" :value="item.id" v-for="item in purchaseTypeOptions"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="选择上架商品" v-if="!isUpdate && !isDetail">
                  <div class="select-goods-row">
                    <div class="select-goods-row-top">
                      <el-button type="primary" @click="openFXSelectGoodsBatch">选择商品</el-button>
                      <span class="select-goods-row-top-tip">找不到商品，前往品牌档案</span>
                      <router-link class="link-type" to="/brand/list">
                        <el-button type="text">维护商品信息</el-button>
                      </router-link>
                    </div>
                    <div>已选择：{{ SKUNumbers }}个SKU， 可在价格库存的地方进行查看</div>
                  </div>
                </el-form-item>
                <el-form-item label="子商品" prop="setItemList" v-if="form.isSet === '1'" ref="setItemList">
                  <add-item-commodity @updateTable="updateCurrentTable" ref="itemCommodity" v-model="form.setItemList"></add-item-commodity>
                </el-form-item>
                <el-form-item label="优惠券" prop="couponList" v-if="form.type === 'CARD'" ref="couponList">
                  <add-coupon :multiple="false" :source="'commodity'" editable v-model="form.couponList"></add-coupon>
                </el-form-item>
                <el-form-item label="商品类型" prop="type" ref="type">
                  <el-select disabled clearable v-model="form.type">
                    <el-option :key="idx" :label="item.label" :loading="typeOptionsLoading" :value="item.value" v-for="(item, idx) in typeOptions"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="商品名称" prop="name" ref="name">
                  <el-input :disabled="isDetail" v-model.trim="form.name"></el-input>
                  <div class="tip">商品展示名称</div>
                </el-form-item>
                <el-form-item label="商品副标题">
                  <el-input :disabled="isDetail" v-model.trim="form.subhead" maxlength="50"></el-input>
                  <div class="tip">限制50个字以内</div>
                </el-form-item>
                <el-form-item class="brand" label="商品品牌" prop="brandId" ref="brandId">
                  <el-select ref="brandIdSelect" :disabled="isDetail" :value="form.brandId" filterable placeholder="请选择" @change="brandIdChange">
                    <el-option v-for="item in brandList" :key="item.id" :label="item.name" :value="item.id"> </el-option>
                  </el-select>
                  <span style="margin-left: 12px" v-if="currentBrandInfo">该品牌为【{{ currentBrandInfo.orderBrandTypeName }}】，下面的商品只能选择该品牌类型的商品</span>
                  <div>
                    <router-link class="link-type" to="/brand/list">品牌维护</router-link>
                  </div>
                </el-form-item>
                <el-form-item class="shipping" label="是否展示品牌" prop="showBrand" ref="showBrand">
                  <el-radio-group :disabled="isDetail" v-model="form.showBrand">
                    <el-radio label="0">
                      商详不展示
                      <span class="tip">(注：配置为展示，商详可以展示品牌入口)</span>
                    </el-radio>
                    <el-radio label="1">商详展示</el-radio>
                  </el-radio-group>
                </el-form-item>

                <el-form-item label="商品分组" prop="categoryIdList" ref="categoryIdList">
                  <el-select :disabled="isDetail" filterable multiple placeholder="请选择" v-model="form.categoryIdList">
                    <el-option :key="category.id" :label="category.name" :value="category.id" v-for="category in categories"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item>
                  <el-button @click="refreshCategories" type="text">刷新</el-button>|
                  <router-link class="link-type" to="/setting/commodity/category">新建分组</router-link>
                </el-form-item>
                <el-form-item label="分享描述" prop="subtitle">
                  <el-input :autosize="{ minRows: 2 }" :disabled="isDetail" type="textarea" v-model.trim="form.subtitle"></el-input>
                  <div class="tip">微信分享给好友时使用</div>
                </el-form-item>
              </div>
            </div>
          </template>
          <!-- 商品参数 -->
          <template :slot="labelList[1].key">
            <div class="section">
              <div class="divider">
                <span class="text">{{ labelList[1].name }}</span>
              </div>
              <div class="content">
                <el-form-item label="已关联的SKU条码："
                  ><span>{{ tradeCenterCommodityExt.paramSpecCode || '--' }}</span> <el-button :disabled="isDetail" size="mini" type="primary" @click="openSpecCode('paramSpecCode')">更换条码</el-button>
                  <el-button v-if="tradeCenterCommodityExt.paramSpecCode" :disabled="isDetail" size="mini" type="text" @click="removeSpecCode('paramSpecCode')">删除关联</el-button></el-form-item
                >
                <el-form-item :key="param.id" :label="param.name" :prop="'commodityParam.' + param.id" :ref="'commodityParam.' + param.id" v-for="param in commodityParams">
                  <el-input :disabled="isDetail" v-model.trim="form.commodityParam[param.id]"></el-input>
                </el-form-item>
                <el-form-item v-if="!isDetail">
                  <router-link class="link-type" to="/setting/commodity/param">
                    <el-button type="primary">商品参数维护</el-button>
                  </router-link>
                </el-form-item>
              </div>
            </div>
          </template>
          <template :slot="labelList[2].key">
            <div class="section">
              <div class="divider">
                <span class="text">{{ labelList[2].name }}</span>
              </div>
              <!-- 没有参与活动的和复制的可以编辑规格 ,编辑和查看不能编辑规格-->
              <div class="content">
                <CommodityStock
                  :id="id"
                  :purchaseType="form.purchaseType"
                  :brandInfo="currentBrandInfo"
                  :columns="skuColumns"
                  :isCopy="isCopy"
                  :isAdd="isAdd"
                  :isDetail="isDetail"
                  :isSpecEditable="isCopy || (!joinActivity && !id)"
                  :isSpecVisible="isSpecVisible"
                  :isUpdate="isUpdate"
                  :restrictionNum="form.restrictionNum"
                  ref="commodityStock"
                  @assignFormData="assignFormData"
                  :SKUNumbers.sync="SKUNumbers"
                />

                <el-form-item class="content-form-item" label="税费" prop="type" required v-if="form.type === 'GLOBAL'">
                  <el-radio-group disabled v-model="form.createCommodityGlobalParamCmd.isNeedDuty">
                    <el-radio label="1">商品价格包含税费</el-radio>
                    <el-radio label="0">税费单独填写</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item class="join-activity-tips" v-if="joinActivity"> 当前商品参与{{ joinActivity }}活动，活动期间不能删除规格 </el-form-item>
              </div>
            </div>
          </template>
          <template :slot="labelList[3].key">
            <div class="section media">
              <div class="divider">
                <span class="text">{{ labelList[3].name }}</span>
              </div>
              <div class="content">
                <el-form-item label="已关联的SKU条码："
                  ><span>{{ tradeCenterCommodityExt.detailSpecCode || '--' }}</span> <el-button size="mini" :disabled="isDetail" type="primary" @click="openSpecCode('detailSpecCode')">更换条码</el-button>
                  <el-button size="mini" type="text" @click="removeSpecCode('detailSpecCode')" :disabled="isDetail">删除关联</el-button></el-form-item
                >
                <el-form-item label="商品缩略图" prop="thumbnailUrl" ref="thumbnailUrl">
                  <ImageManagement :maxSize="100 * 1024" v-model="form.thumbnailUrl" :disabled="isDetail" @input="validateField('thumbnailUrl')"></ImageManagement>
                  <div class="tip">宽高：500*500，大小：最大100k，数量：1张</div>
                </el-form-item>
                <el-form-item label="商品主视频" prop="video" ref="video">
                  <VideoUpload v-model="form.video" :disabled="isDetail"></VideoUpload>
                  <div class="tip">目前仅支持在手机端播放，建议时长9-30秒，建议视频宽高比16:9</div>
                </el-form-item>
                <el-form-item label="视频封面" prop="videoCover" v-if="form.video" ref="videoCover">
                  <ImageManagement :maxSize="300 * 1024" v-model="form.videoCover" :disabled="isDetail"></ImageManagement>
                  <div class="tip">宽高：750*750，大小：最大300k，数量：1张</div>
                </el-form-item>
                <el-form-item label="商品橱窗图" prop="shopwindowList" ref="shopwindowList">
                  <ImageManagement :limit="10" :maxSize="300 * 1024" data-type="array" v-model="form.shopwindowList" :disabled="isDetail"></ImageManagement>
                  <div class="tip">宽高：800*800，大小：最大300k，数量：10张</div>
                </el-form-item>
                <el-form-item label="商品详情图" prop="detailList">
                  <ImageManagement :maxSize="500 * 1024" data-type="array" v-model="form.detailList" :disabled="isDetail"></ImageManagement>
                  <div class="tip">商详图宽750，高不限制，建议不超过500k，大小：最大500k , 数量：任意张</div>
                </el-form-item>
                <el-form-item label="素材下载" prop="zipFile">
                  <div class="upload">
                    <i @click="clearzipFile" class="el-icon-circle-close spec-close" v-if="form.zipFile"></i>
                    <Upload :before-upload="onBeforeUpload" :on-change="onUploadChange" :disabled="isDetail" :on-success="onUploadSuccess" :show-file-list="false" accept=".zip" class="upload-btn" element-loading-spinner="el-icon-loading" v-loading="uploadLoading">
                      <div v-if="!form.zipFile">
                        <SvgIcon :className="'upload-icon'" iconClass="hao"></SvgIcon>
                        <span class="upload-text">添加素材</span>
                      </div>
                      <div v-else>
                        <svg aria-hidden="true" class="icon icon-style" style="width: 40px; height: 40px">
                          <use xlink:href="#icon-filezip" />
                        </svg>
                        <div class="upload-text">{{ zipFileName }}</div>
                      </div>
                    </Upload>
                  </div>
                  <div class="tip">（注：zip格式，名称为商品名称，用于客户下载）</div>
                  <div class="link" @click="downLoadMaterial" v-if="form.zipFile">素材下载</div>
                </el-form-item>
              </div>
            </div>
          </template>
          <template :slot="labelList[4].key">
            <div class="section">
              <div class="divider">
                <span class="text">{{ labelList[4].name }}</span>
              </div>
              <div class="content">
                <el-form-item label="限购设置"> <el-switch :disabled="isDetail" v-model="isPurchase"> </el-switch><span class="content-tag">开启后表示在限购时间段内该商品购买会进行限购，不在限购时间内不限购</span> </el-form-item>
                <div class="restrictions" v-if="isPurchase">
                  <el-form-item label="限购时间" required>
                    <el-date-picker :default-time="['00:00:00', '23:59:59']" :disabled="isDetail" align="right" end-placeholder="结束日期" start-placeholder="开始日期" type="daterange" v-model="form.restrictionNumTime"></el-date-picker>
                  </el-form-item>
                  <div class="purchase-box">
                    <div class="purchase-box-label commo-asterisk">限购条件</div>
                    <el-checkbox-group v-model="restrictionNumCheckList">
                      <div>
                        <el-checkbox label="restrictionNum">
                          <span class="label">条件一：购买总数限制</span>
                        </el-checkbox>
                        <el-input :disabled="isDetail" v-model.number="form.restrictionNum" type="number"><template slot="append">件</template></el-input>
                      </div>
                      <div>
                        <el-checkbox label="singleRestrictionNum">
                          <span class="label">条件二：单次购买总数限制</span>
                        </el-checkbox>
                        <el-input :disabled="isDetail" v-model.number="form.singleRestrictionNum" type="number">
                          <template slot="append">件</template>
                        </el-input>
                      </div>
                    </el-checkbox-group>

                    <div class="explain">
                      限购说明：限购是在限定的时间段内针对设置的限购方式来进行限购。<br />
                      1、条件一是指用户可以购买这个商品一共可以买多少件；<br />
                      2、条件二是指用户单次购买这个商品可以购买多少件；<br />
                      3、条件一和条件二可以只选择一个进行设置，也可以两个都勾选进行设置；<br />
                      4、条件一和条件二都启用的话，设置的数量：条件一必须大于等于条件二；
                    </div>
                  </div>
                </div>
                <el-form-item label="备注" prop="remarks">
                  <el-input :autosize="{ minRows: 2 }" type="textarea" v-model.trim="form.remarks"></el-input>
                </el-form-item>
                <el-form-item class="shipping" label="运费设置" prop="freeShipping">
                  <el-radio-group :disabled="isDetail" v-model="form.freeShipping">
                    <el-radio :label="0">包邮</el-radio>
                    <el-radio :label="1">
                      <span>不包邮</span>
                      <span class="expressFee" v-if="form.freeShipping === 1">
                        <el-input placeholder="请输入运费金额" size="small" v-model.trim="form.expressFee"></el-input>
                        <span>元</span>
                      </span>
                    </el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item class="shipping" label="销量是否前端展示" prop="showSalesVolume">
                  <el-radio-group :disabled="isDetail" v-model="form.showSalesVolume">
                    <el-radio label="0">不显示</el-radio>
                    <el-radio label="1">显示</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="通关口岸" prop="createCommodityGlobalParamCmd.customs" required v-if="form.type === 'GLOBAL'">
                  <el-select :disabled="isDetail" :loading="customsListLoading" filterable v-model="form.createCommodityGlobalParamCmd.customs">
                    <el-option :key="customs.value" :label="customs.label" :value="customs.value" v-for="customs in customsList"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="状态" prop="status">
                  <el-radio-group :disabled="isDetail" v-model="form.status">
                    <el-radio border label="0">待上架</el-radio>
                    <el-radio border label="1">上架</el-radio>
                    <el-radio border label="3">下架</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="是否隐藏">
                  <el-radio-group :disabled="isDetail" class="hideOrShow" v-model="form.isHidden">
                    <el-radio label="0">否</el-radio>
                    <el-radio label="1">是</el-radio>
                  </el-radio-group>
                  <span>（隐藏后不会在分类列表展示，也无法被搜索到）</span>
                </el-form-item>
                <el-form-item label="热卖排行">
                  <el-radio-group :disabled="isDetail" class="hideOrShow" v-model="form.showTopSales">
                    <el-radio label="0">不显示</el-radio>
                    <el-radio label="1">显示</el-radio>
                  </el-radio-group>
                  <span>（热卖前20名的商品才会展示）</span>
                </el-form-item>
                <el-form-item label="商品标签">
                  <el-checkbox-group :disabled="isDetail" v-model="form.commodityLabelIds">
                    <el-checkbox v-for="item in commodityLabelOptions" :label="item.id" :key="item.id">{{ item.name }}</el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
                <el-form-item label="推荐商品">
                  <el-radio-group :disabled="isDetail" class="hideOrShow" v-model="form.isRecommend">
                    <el-radio label="0">不显示</el-radio>
                    <el-radio label="1">显示</el-radio> </el-radio-group
                  ><span>（最多可以选择3个商品）</span>
                  <AddCommodity :operation="isDetail ? 'detail' : ''" v-if="form.isRecommend === '1'" v-model="goodsList" commodityType="recommendCommodity"> </AddCommodity>
                </el-form-item>
                <el-form-item label="是否参与返利">
                  <el-radio-group :disabled="isDetail" class="hideOrShow" v-model="form.creditBack">
                    <el-radio label="0">否</el-radio>
                    <el-radio label="1">是</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="是否可使用返利">
                  <el-radio-group :disabled="isDetail" class="hideOrShow" v-model="form.creditDeduction">
                    <el-radio label="0">否</el-radio>
                    <el-radio label="1">是</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="活动属性信息" v-if="id && !isCopy && !isDetail">
                  <activity-attribute :id="id" :type="form.type" />
                </el-form-item>
              </div>
            </div>
          </template>
        </Anchor>
        <div class="footer-wrap" v-if="!isDetail">
          <div class="footer">
            <el-button :loading="submitLoading" @click="onSubmit" type="primary">提交</el-button>
          </div>
        </div>
      </el-form>
    </div>
    <FXSelectGoods ref="FXSelectGoods" :brandInfo="currentBrandInfo" @save="FXSelectGoodsSave" />
    <FXSelectGoods ref="FXSelectGoodsRadio" :brandInfo="currentBrandInfo" @save="FXSelectGoodsRadioSave" configType="Radio" />
  </div>
</template>

<script>
import { add, getByCouponId, getById, getCommodityLabel, listCategories, listCommodityParams, update, validationByCommodityId, commodityGetRelationActivityInfo } from '@/api/commodity';
import { listCustoms } from '@/api/common/dict';
import dict from '@/components/Common/dicts';
import Process from '@/utils/Process';
import pick from 'lodash/pick';
import ImageManagement from '@/components/ImageManagement';
import VideoUpload from '@/components/Upload/VideoUpload';
import CommodityStock from '@/components/CommodityStock';
import AddCoupon from '@/components/AddCoupon';
import SvgIcon from '@/components/SvgIcon';
import AddItemCommodity from '@/components/AddItemCommodity';
import AddCommodity from '@/components/AddCommodity';
import ActivityAttribute from '../ActivityAttribute';
import Upload from '@/components/Upload';
import { parseDefaultTime } from '@/utils';
import FXSelectGoods from '@/components/FXSelectGoods/index.vue';
import Anchor from '@/components/Anchor/index.vue';
import pickBy from 'lodash/pickBy';
import { brandListByCondition } from '@/api/setting/commodity/brand'; // 返回一个新对象，值由真值组成
export default {
  name: 'commodity-form',
  data() {
    return {
      SpecCodeType: '', // SKU条码类型 detailSpecCode paramSpecCode
      tradeCenterCommodityExt: {
        detailSpecCode: '', // 商品详情对应的规格编码（用于商品关联到分销商品池信息）
        paramSpecCode: '' // 商品参数对应的规格编码（用于商品关联到主数据信息）
      }, // 分销-商品条码关联信息
      labelList: [
        { name: '基础信息', key: 'info' },
        { name: '商品参数', key: 'parameters' },
        { name: '价格库存', key: 'price-of-inventory' },
        { name: '商品详情', key: 'details' },
        { name: '其他信息', key: 'other' }
      ],
      SKUNumbers: 0, // 已选择sku 数量
      goodsList: [],
      purchaseTypeOptions: [
        { id: 'PURCHASE', name: '采销' },
        { id: 'DROP_SHIPPING', name: '一件代发' }
      ], // 采货类型
      zipFileName: '', // 上传的zip文件名
      isPurchase: false, // 是否控制限购
      joinActivity: '',
      uploadLoading: false,
      form: {
        purchaseType: '', // 采货类型
        isSet: '0', // 是否套装
        setItemList: [], // 套装子商品列表
        couponList: [], // 优惠券列表
        type: 'ACTUAL', // 商品类型 默认：大贸
        name: '', // 名称
        subhead: '', // 商品副标题
        brandId: '', // 品牌id
        showBrand: '0', // 控制是否显示品牌
        categoryIdList: [], // 分组
        subtitle: '', // 分享描述
        commodityParam: {}, // 商品属性
        thumbnailUrl: '', // 商品缩略图
        shopwindowList: [], // 商品橱窗图
        detailList: [], // 商品介绍图
        restrictionStartDate: '', // 限购时间开始
        restrictionEndDate: '', // 限购时间结束
        restrictionNum: '', // 限购 购买总数限制
        singleRestrictionNum: '', // 单次购买总数限制
        remarks: '',
        status: '0',
        commodityLabelIds: [], // 商品标签
        expressFee: '',
        isHidden: '0',
        showTopSales: '0', // 热卖排行
        creditBack: '0', // 是否参与返利
        creditDeduction: '1', // 是否使用返利
        isRecommend: '0', // 是否显示推荐商品
        // 海淘属性
        createCommodityGlobalParamCmd: {
          // 海关口岸
          customs: 'SHANGHAI_ZS',
          // 是否需要税费，0-否，1-是
          isNeedDuty: '1',
          // 原产地
          origin: ''
        },
        // 商详是否展示销量, 0-不显示，1-显示
        showSalesVolume: '0',
        freeShipping: 0,
        // showMaterialList: [], // 素材图
        video: '',
        videoCover: '', // 视频封面
        zipFile: '' // 素材zip
      },
      originBrandList: [], // 品牌接口返回数据列表
      commodityLabelOptions: [], // 商品标签列表
      typeOptionsLoading: false, // 商品类型数据字典加载
      typeOptions: [], // 商品类型
      customsListLoading: true,
      customsList: [],
      pickKeys: [
        'isSet',
        'setItemList',
        'couponList',
        'type',
        'name',
        'subhead',
        'commodityLabelIds',
        'recommendCommodityIds',
        'brandId',
        'showBrand',
        'remarks',
        'subtitle',
        'thumbnailUrl',
        'restrictionStartDate',
        'restrictionEndDate',
        'status',
        'isHidden',
        'showTopSales',
        'creditBack',
        'creditDeduction',
        'isRecommend',
        'categoryIdList',
        'showSalesVolume',
        'purchaseType'
      ],
      commodityParams: [], // 商品参数
      commodityParamsLoading: true,
      categories: [],
      categoriesLoading: true,
      commodityLabelLoading: true,
      submitLoading: false,
      dataLoaidng: false,
      featureListVisible: false,
      shareCheckList: [],
      restrictionNumCheckList: [], // 限购条件
      previousBrandId: '',
      isUsed: true
    };
  },
  props: {
    id: null,
    isAdd: Boolean,
    isCopy: Boolean,
    isEdit: Boolean,
    isDetail: {
      type: Boolean,
      default: false
    }
  },

  watch: {
    'form.type'(type) {
      // 当为卡券商品时，套装为不足套
      if (type === 'CARD') {
        this.form.isSet = '0';
        this.form.setItemList = [];
      }
    },
    goodsList(list) {
      let recommendCommodityIds = [];
      if (this.form.isRecommend === '1') {
        recommendCommodityIds = list.map((item) => {
          return item.id;
        });
        this.form.recommendCommodityIds = recommendCommodityIds;
      }
    }
  },
  created() {},
  activated() {
    const { query } = this.$route;
    if (this.isAdd && query.specCode) {
      // 添加商品时，外部传入值
      Object.assign(
        this.form,
        pickBy(this.$route.query, (v, k) => this.form.hasOwnProperty(k))
      );
    }
    this.commodityParamsLoading = true;
    listCommodityParams()
      .then((res) => {
        this.commodityParams = res.data;
        this.$nextTick(() => {
          this.$refs.form.clearValidate();
        });
      })
      .finally(() => {
        this.commodityParamsLoading = false;
      });
  },
  mounted() {
    // 如果
    if (this.isUpdate) {
      validationByCommodityId(this.id).then((res) => {
        if (res.data) {
          this.joinActivity = res.data;
        }
      });
    }
    const stacks = new Process();
    this.fetchCommodityType();
    listCustoms()
      .then((res) => {
        this.customsList = res.data;
      })
      .finally(() => {
        this.customsListLoading = false;
      });

    const p1 = listCommodityParams()
      .then((res) => {
        this.commodityParams = res.data;
      })
      .finally(() => {
        this.commodityParamsLoading = false;
      });
    stacks.push(p1);

    const p2 = listCategories()
      .then((res) => {
        this.categories = res.data;
      })
      .finally(() => {
        this.categoriesLoading = false;
      });

    stacks.push(p2);
    if (this.id) {
      this.dataLoaidng = true;
      const p3 = getById(this.id)
        .then((res) => {
          const commodityParam = {};
          const tradeCenterCommodity = res.data?.tradeCenterCommodity || {};
          this.tradeCenterCommodityExt = { ...this.tradeCenterCommodityExt, ...res.data?.tradeCenterCommodityExt };
          const {
            commodityParamRelationList,
            commodityMultimediaVO,
            //   marketPrice,
            skuVOlist,
            specificationVOList,
            expressFee,
            isHidden,
            showTopSales,
            creditBack,
            creditDeduction,
            isRecommend,
            isSet,
            type,
            commodityLabelIds = [],
            commodityGlobalParam,
            // recommentReason = '',
            commodityExtensionVO,
            restrictionStartDate,
            restrictionEndDate,
            restrictionNum,
            singleRestrictionNum
          } = tradeCenterCommodity;
          commodityParamRelationList.forEach(({ paramId, paramValue }) => {
            commodityParam[paramId] = paramValue;
          });

          let shopwindowList = [];
          let detailList = [];
          // let showMaterialList = [];
          let video = '';
          let videoCover = '';
          let zipFile = '';
          commodityMultimediaVO.forEach(({ type, jsonValue }) => {
            let value;
            try {
              value = JSON.parse(jsonValue);
            } catch (e) {
              // console.error(e);
              jsonValue && (value = jsonValue);
            }
            if (!value) {
              return;
            }
            if (type === 'VIDEO') {
              video = value;
              return;
            }
            if (type === 'VIDEO_COVER') {
              videoCover = value;
              return;
            }
            if (type === 'MATERIAL_ZIP') {
              zipFile = value.url || '';
              this.zipFileName = value.name || '';
              return;
            }
            if (type === 'SHOPWINDOW') {
              shopwindowList = value;
              return;
            }
            if (type === 'DETAILS') {
              detailList = value;
              return;
            }
            if (type === 'MATERIAL') {
              // showMaterialList = value;
            }
          });
          const { createCommodityGlobalParamCmd } = this.form;
          this.form = {
            ...this.form,
            commodityParam,
            shopwindowList,
            detailList,
            video,
            videoCover,
            zipFile,
            expressFee,
            isHidden,
            commodityLabelIds,
            showTopSales,
            creditBack,
            creditDeduction,
            isRecommend,
            skuVOlist,
            singleRestrictionNum: singleRestrictionNum > 0 ? singleRestrictionNum : '',
            restrictionNum: restrictionNum > 0 ? restrictionNum : '',
            freeShipping: expressFee ? 1 : 0,
            ...pick(tradeCenterCommodity, [...this.pickKeys]),
            ...(type === 'GLOBAL' && {
              createCommodityGlobalParamCmd: {
                ...createCommodityGlobalParamCmd,
                ...commodityGlobalParam
              }
            }),
            restrictionNumTime: '' // 限购时间
          };
          // 限购时间
          if (restrictionStartDate) {
            this.isPurchase = true;
            const arr = [];
            if (singleRestrictionNum > 0) {
              arr.push('singleRestrictionNum');
            }
            if (restrictionNum > 0) {
              arr.push('restrictionNum');
            }
            this.restrictionNumCheckList = arr;
            this.form.restrictionNumTime = [new Date(restrictionStartDate), new Date(restrictionEndDate)];
          }

          if (commodityExtensionVO) {
            if (isSet === '1') {
              this.form.isSet = '1';
              this.form.setItemList = commodityExtensionVO.setItemList;
            }
            if (type === 'CARD') {
              this.form.cardId = commodityExtensionVO.cardId;
              this.fetchCoupon(commodityExtensionVO.cardId);
            }
            this.form.showTopSales = commodityExtensionVO.showTopSales;
            this.form.isRecommend = commodityExtensionVO.isRecommend;
            this.goodsList = commodityExtensionVO.recommendCommodityList || [];
            this.form.creditBack = commodityExtensionVO.creditBack;
            this.form.creditDeduction = commodityExtensionVO.creditDeduction;
            this.form.purchaseType = commodityExtensionVO.purchaseType || '';
          }
          // 初始化规格和sku信息
          this.$refs.commodityStock.setData({
            skuVOlist,
            specificationVOList
          });
        })
        .finally(() => {
          this.dataLoaidng = false;
        });
      stacks.push(p3);
    }
    // 清空初始化的校验信息
    stacks.all().then(() => {
      this.$nextTick(() => {
        this.$refs.form.clearValidate();
      });
    });
    this.fetchBrand();
    this.fetchCommodityLabelOptions();
  },
  computed: {
    skuColumns() {
      const list = [
        {
          label: '商品条码', // 列名
          prop: 'specCode', // 列属性
          // editable: true, // 是否支持编辑
          defaultValue: this.$route.query.specCode, // 是否支持编辑
          width: '150px'
        },
        {
          label: '规格标识', // 列名
          prop: 'id', // 列属性
          editable: false, // 是否支持编辑
          width: '150px'
        },
        {
          label: '批发价(元)',
          prop: 'price',
          editable: true,
          batchSet: true
        },
        {
          label: '建议零售价(元)',
          prop: 'retailPrice',
          editable: true,
          batchSet: true // 是否支持批量设置
        },
        {
          label: '活动大促价(元)',
          prop: 'minControlPrice',
          editable: true,
          batchSet: true // 是否支持批量设置
        },
        {
          label: '库存',
          prop: 'stock',
          editable: true,
          batchSet: false // 是否支持批量设置
        },
        {
          label: '虚拟销量',
          prop: 'salesVolume',
          editable: true,
          batchSet: false // 是否支持批量设置
        },
        {
          label: '起订量',
          prop: 'miniOrderQuantity',
          editable: true,
          batchSet: true // 是否支持批量设置
        }
      ];
      if (this.isAdd || this.isCopy) {
        return list.filter((item) => item.prop !== 'salesVolume');
      }
      return list;
    },
    isUpdate() {
      return this.id && !this.isCopy && !this.isDetail;
    },
    isSpecVisible() {
      if (this.form.type === 'CARD') {
        return false;
      }
      if ((this.form.type === 'ACTUAL' || this.form.type === 'GLOBAL') && this.form.isSet === '1') {
        return false;
      }
      return true;
    },
    rules() {
      const rules = {
        type: [
          {
            required: true,
            message: '请选择商品类型',
            trigger: 'change'
          }
        ],
        name: [
          {
            required: true,
            message: '请输入商品名称',
            trigger: 'change'
          }
        ],
        brandId: [
          {
            required: true,
            message: '请选择商品品牌',
            trigger: 'change'
          }
        ],
        showBrand: [
          {
            required: true,
            message: '请选择是否展示品牌',
            trigger: 'change'
          }
        ],
        categoryIdList: [
          {
            required: true,
            message: '请选择商品品类',
            trigger: 'change'
          }
        ],
        purchaseType: [
          {
            required: true,
            message: '请选择采货类型',
            trigger: 'change'
          }
        ],
        thumbnailUrl: [
          {
            required: true,
            message: '请选择商品缩略图',
            trigger: 'change'
          }
        ],
        videoCover: [
          {
            required: true,
            message: '请上传视频封面',
            trigger: 'change'
          }
        ]
      };
      if (this.form.type === 'ACTUAL' || this.form.type === 'GLOBAL') {
        rules['isSet'] = {
          required: true,
          message: '必填信息',
          trigger: 'change'
        };
      }
      if (this.form.isSet === '1') {
        rules['setItemList'] = {
          required: true,
          message: '请选择适用商品',
          trigger: 'change'
        };
      }
      if (this.form.type === 'CARD') {
        rules['couponList'] = {
          required: true,
          message: '请选择优惠券',
          trigger: 'change'
        };
      }
      if (this.form.restrictionNum || this.form.restrictionNum === 0) {
        rules['restrictionNumTime'] = {
          required: true,
          message: '请选择限购时间',
          trigger: 'change'
        };
      }
      this.commodityParams.forEach(({ required, id, name }) => {
        if (required === '1') {
          rules[`commodityParam.${id}`] = {
            required: true,
            message: `请填写${name}`,
            trigger: 'change'
          };
        }
      });
      return rules;
    },
    brandList() {
      const { isUpdate, originBrandList, currentBrandInfo } = this;
      const arr = isUpdate ? originBrandList.filter((item) => item?.orderBrandType === currentBrandInfo?.orderBrandType) : originBrandList;
      return arr.map((item) => ({
        id: item.id,
        name: item.name
      }));
    },
    currentBrandInfo() {
      const { brandId } = this.form;
      if (!brandId) {
        return null;
      }
      return this.originBrandList.find((item) => item.id === this.form.brandId);
    }
  },
  methods: {
    downLoadMaterial() {
      window.open(this.form.zipFile);
    },
    // 刷新商品分组列表
    refreshCategories() {
      listCategories()
        .then((res) => {
          this.categories = res.data;
        })
        .finally(() => {
          this.categoriesLoading = false;
        });
    },
    // 获取商品标签
    fetchCommodityLabelOptions() {
      getCommodityLabel()
        .then((res) => {
          this.commodityLabelOptions = res.data;
        })
        .finally(() => {
          this.commodityLabelLoading = false;
        });
    },
    clearzipFile() {
      this.form.zipFile = '';
    },
    onBeforeUpload(file) {
      // console.log('file', file);
    },
    onUploadChange(file) {
      this.zipFileName = file.name;
      this.uploadLoading = file.status === 'uploading' || file.status === 'ready';
    },
    onUploadSuccess(res) {
      this.form.zipFile = res.data;
    },
    // 获取品牌列表
    fetchBrand() {
      const params = {
        filterFlag: '1'
      };
      brandListByCondition(params).then((res) => {
        this.originBrandList = res.data || [];
      });
    },
    // 用来接收添加商品组件的实时展示在页面上的列表
    updateCurrentTable(setItemList) {
      this.form.setItemList = [...setItemList];
    },
    // 获取商品类型
    fetchCommodityType() {
      // 订单状态列表
      dict('COMMONODITY_TYPE').then((res) => {
        this.typeOptions = res;
      });
    },
    // 通过id查询优惠券信息
    fetchCoupon(id) {
      getByCouponId(id).then((response) => {
        const couponItem = response.data;
        // 满减活动-有门槛
        if (response.data.couponType === 'MONEY_OFF' && response.data.useThreshold) {
          couponItem.couponContent = `满${response.data.useThreshold}减${response.data.discountContent}元`;
        }
        // 满减活动-无门槛
        if (response.data.couponType === 'MONEY_OFF' && !response.data.useThreshold) {
          couponItem.couponContent = `减${response.data.discountContent}元`;
        }
        // 满折活动-有门槛
        if (response.data.couponType === 'RATE_OFF' && response.data.useThreshold) {
          couponItem.couponContent = `满${response.data.useThreshold}元${response.data.discountContent}折`;
        }
        // 满折活动-无门槛
        if (response.data.couponType === 'RATE_OFF' && !response.data.useThreshold) {
          couponItem.couponContent = `${response.data.discountContent}折`;
        }
        this.form.couponList.push(couponItem);
      });
    },
    hideFeatureList() {
      this.featureListVisible = false;
    },
    // featureListSelected(data) {
    //   this.featureListObj = data;
    // },
    getCommodityParams() {
      const { commodityParam } = this.form;
      const params = this.commodityParams.map(({ id }) => ({
        paramId: id,
        paramValue: commodityParam[id]
      }));
      return { paramRelationCmdList: params };
    },
    getCommodityMultimedia() {
      const multimediaCmdList = [];
      const {
        shopwindowList,
        detailList,
        video,
        // showMaterialList,
        videoCover,
        zipFile
      } = this.form;
      if (shopwindowList.length) {
        multimediaCmdList.push({
          type: 'SHOPWINDOW',
          jsonValue: JSON.stringify(shopwindowList)
        });
      }
      if (detailList.length) {
        multimediaCmdList.push({
          type: 'DETAILS',
          jsonValue: JSON.stringify(detailList)
        });
      }
      if (video) {
        multimediaCmdList.push({
          type: 'VIDEO',
          jsonValue: JSON.stringify(video)
        });
      }
      if (videoCover) {
        multimediaCmdList.push({
          type: 'VIDEO_COVER',
          jsonValue: JSON.stringify(videoCover)
        });
      }
      if (zipFile) {
        multimediaCmdList.push({
          type: 'MATERIAL_ZIP',
          jsonValue: JSON.stringify({ name: this.zipFileName, url: zipFile })
        });
      }
      return { multimediaCmdList };
    },
    validate() {
      const p1 = this.$refs.commodityStock.validate();
      const p2 = this.$refs.form.validate();
      return Promise.all([p1, p2])
        .then(([obj]) => obj)
        .catch((err) => {
          if (this.isObject(err) && Object.keys(err).length) {
            const o = Object.values(err)[0][0] || null;
            if (o && o.message) {
              this.scrollView(err);
              this.$message.error(o.message);
            }
          }
          if (typeof err === 'string') {
            // 跳转到sku组件
            this.scrollView({ commodityStock: {} });
          }
          return Promise.reject(err || '有必填项未填写');
        });
    },
    // 判断是否为对象
    isObject(obj) {
      return Object.prototype.toString.call(obj) === '[object Object]';
    },
    onSubmit() {
      this.submitLoading = true;
      let validateResult = true;
      if ((this.form.type === 'ACTUAL' || this.form.type === 'GLOBAL') && this.form.isSet === '1') {
        const validateResultArr = this.$refs['itemCommodity'].validateArr;
        validateResultArr.forEach((item) => {
          if (!item.validateResult) {
            validateResult = false;
          }
        });
      }

      // 校验套装子商品里面的数据是否符合要求
      if (!validateResult) {
        this.$message.error('数据有误，请检查之后再提交');
        this.submitLoading = false;
        return false;
      }
      // 判断推荐商品是否显示，如果显示，最多可以选择3个商品
      if (this.form.isRecommend === '1' && this.goodsList.length > 3) {
        this.$message.error('推荐商品最多只可以选择3个！');
        this.submitLoading = false;
        return false;
      }
      this.validate()
        .then((obj) => {
          const {
            expressFee,
            freeShipping,
            type,
            createCommodityGlobalParamCmd
            // member
          } = this.form;
          const globalParamKey = this.isUpdate ? 'updateCommodityGlobalParamCmd' : 'createCommodityGlobalParamCmd';
          const conmmodityExtension = this.isUpdate ? 'updateCommodityExtensionCmd' : 'createCommodityExtensionCmd';
          const reqData = {
            ...pick(this.form, this.pickKeys),
            ...(type === 'GLOBAL' && {
              [globalParamKey]: createCommodityGlobalParamCmd
            }),
            ...this.getCommodityParams(),
            ...this.getCommodityMultimedia(),
            expressFee: freeShipping && expressFee,
            ...obj
          };
          // reqData.showMaterial = reqData.showMaterial ? '1' : 0;
          let req = add;
          if (this.isUpdate) {
            req = update;
            reqData.id = this.id;
          }
          reqData[conmmodityExtension] = {
            creditDeduction: '0',
            discount: '0'
          };
          if (this.form.type === 'ACTUAL' || (this.form.type === 'GLOBAL' && this.form.isSet === '1')) {
            reqData[conmmodityExtension].setItemList = this.form.setItemList;
          } else {
            reqData[conmmodityExtension].setItemList = [];
          }
          if (this.form.type === 'CARD') {
            reqData[conmmodityExtension].cardId = this.form.couponList[0].id;
          }
          reqData[conmmodityExtension].creditBack = this.form.creditBack;
          reqData[conmmodityExtension].creditDeduction = this.form.creditDeduction;
          reqData[conmmodityExtension].showTopSales = this.form.showTopSales;
          reqData[conmmodityExtension].isRecommend = this.form.isRecommend;
          reqData[conmmodityExtension].recommendCommodityIds = this.form.recommendCommodityIds;
          reqData[conmmodityExtension].purchaseType = this.form.purchaseType;
          delete reqData.showTopSales;
          delete reqData.isRecommend;
          delete reqData.recommendCommodityIds;
          delete reqData.creditBack;
          delete reqData.creditDeduction;
          if (this.isPurchase) {
            // 是否控制限购
            if (this.form.restrictionNumTime.length === 0) {
              const errorTxt = '已开启限购控制，请选择限购时间';
              this.$message.error(errorTxt);
              return Promise.reject(errorTxt);
            }
            reqData.restrictionStartDate = parseDefaultTime(this.form.restrictionNumTime[0]);
            reqData.restrictionEndDate = parseDefaultTime(this.form.restrictionNumTime[1]);
            if (this.restrictionNumCheckList.length === 0) {
              const errorTxt = '开启限购至少选一种限购条件';
              this.$message.error(errorTxt);
              return Promise.reject(errorTxt);
            }
            if (this.restrictionNumCheckList.findIndex((key) => key === 'singleRestrictionNum') !== -1) {
              const singleRestrictionNum = this.form['singleRestrictionNum'];
              if (singleRestrictionNum <= 0 || !this.isInteger(singleRestrictionNum)) {
                const errorTxt = '单次购买总数限制必须大于0件且必须是整数';
                this.$message.error(errorTxt);
                return Promise.reject(errorTxt);
              }
              reqData.singleRestrictionNum = singleRestrictionNum;
            } else {
              reqData.singleRestrictionNum = 0;
            }

            if (this.restrictionNumCheckList.findIndex((key) => key === 'restrictionNum') !== -1) {
              const restrictionNum = this.form['restrictionNum'];
              if (restrictionNum <= 0 || !this.isInteger(restrictionNum)) {
                const errorTxt = '购买总数限制必须大于0件且必须是整数';
                this.$message.error(errorTxt);
                return Promise.reject(errorTxt);
              }
              reqData.restrictionNum = restrictionNum;
            } else {
              reqData.restrictionNum = 0;
            }
          } else {
            reqData.restrictionStartDate = 0;
            reqData.restrictionEndDate = 0;
            reqData.singleRestrictionNum = 0;
            reqData.restrictionNum = 0;
          }
          const queryData = {
            tradeCenterCommodity: reqData,
            tradeCenterCommodityExt: this.tradeCenterCommodityExt
          };
          if (this.isUpdate && reqData?.skuVOCmd?.updSkuList?.length > 0) {
            this.isUsed = false;
            this.getRelation(reqData, queryData, req);
          }
          if(!this.isUsed) return;
          return req(queryData);
        })
        .then((res) => {
          if(res.code === '0') {
            this.$message.success('提交成功');
            this.$back('/commodity/list');
          }
        })
        .catch((e) => {
          // 这里不需要 全局已经有提示了
          if (e && typeof e === 'string') {
            this.$message.error(e);
          }
        })
        .finally(() => {
          this.submitLoading = false;
        });
    },
    // 判断是否被积分兑换活动引用
    getRelation({skuVOCmd, id}, queryData, request) {
      const skuList = new Map(skuVOCmd?.updSkuList.map(({ id, price }) => [id, Number(price)]));
      const obj = {
        commodityId: id,
        skuList: Array.from(
          this.form.skuVOlist.filter(({ id }) => skuList.has(id)).map(({ id, price }) => ({
            price: Number(price) !== skuList.get(id) ? skuList.get(id) : undefined,
            skuId: id
          }))
        ).filter(({ price }) => price !== undefined)
      }
      if (!obj.skuList.length) {
        this.isUsed = true;
        return;
      }
      commodityGetRelationActivityInfo(obj).then((res) => {
        if(!res.success) return;
        const data = res?.data || [];
        if (!data.length) {
          this.isUsed = true;
          this.submitRequest(request, queryData);
          return;
        }
        const msg = data.find(item => item.bizType === 'CreditActivityExchange')?.content || '';
        if (!msg) return;
        this.$confirm(`<span style="white-space: pre-wrap">${msg}</span>`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          dangerouslyUseHTMLString: true,
          type: 'warning'
        }).then(() => {
          this.isUsed = true;
          this.submitRequest(request, queryData);
        }).catch(() => {
          this.isUsed = false;
        });
      });
    },
    submitRequest(req, queryData) {
      req(queryData).then(() => {
        this.$message.success('提交成功');
        this.$back('/commodity/list');
      });
    },
    isInteger(obj) {
      // 判断是否是整数
      return parseInt(obj, 10) === obj;
    },
    openFXSelectGoodsBatch() {
      if (!this.form.purchaseType) {
        this.$message.error('请先选择采货类型');
        return;
      }
      this.$refs.FXSelectGoods.open();
    },
    // sku 合并到主数据
    assignFormData(skuOBJ) {
      // 合并当前商品信息
      //  -采货类型、商品分组：需要让运营自己选择，不能进行匹配；
      //  -商品名称：默认取【第一个SKU的商品名称】；
      //  -商品品牌：为选择的品牌；
      //  -商品详情：取【第一个SKU的商品详情】，如果没有就不取；
      !this.form.name && (this.form.name = skuOBJ.commodityName);
      !this.form.brandId && (this.form.brandId = skuOBJ.brandId);
      if (!this.form.type) {
        let key = '';
        switch (skuOBJ.commodityType) {
          case 'GENERAL_TRADE':
            // 大贸
            key = 'ACTUAL';
            break;
          case 'CROSS_BORDER_TRADE':
            // 海淘
            key = 'GLOBAL';
            break;
          case 'GENERAL_AND_CROSS_BORDER_TRADE':
            // 大贸海淘  如果是【大贸、大贸海淘】都显示为【大贸】
            key = 'ACTUAL';
            break;
        }
        this.form.type = key;
      }
      if (!this.tradeCenterCommodityExt.detailSpecCode) {
        // 如果已经关联则不覆盖
        this.MergeDetails(skuOBJ);
      }
      if (!this.tradeCenterCommodityExt.paramSpecCode) {
        // 如果已经关联则不覆盖
        this.MergeCommodityParams(skuOBJ);
      }
    },
    // 批量增加规格
    FXSelectGoodsSave(arr) {
      const [skuOBJ] = arr;
      if (skuOBJ) {
        // 合并默认数据
        this.assignFormData(skuOBJ);
      }
      const whitelist = [];
      const deWeight = arr.map((item, i) => {
        let name = item.spec;
        if (!whitelist.includes(name)) {
          whitelist.push(name);
        } else {
          name = `【规格值${i}重复！请修改】${item.spec}`;
          whitelist.push(name);
        }

        return {
          ...item,
          name
        };
      }); // 先把重复的去重
      const specificationVOList = [
        {
          level: '1',
          name: '规格',
          specificationItemVOList: deWeight.map((item) => {
            return {
              ...item,
              sort: 1,
              specId: item.barcode // 规格Id
            };
          }) // 规格值对象
        }
      ]; // 商品规格名信息
      const skuVOlist = deWeight.map((item, i) => {
        return {
          specCode: item.barcode,
          firstLevel: item.name,
          sort: 1,
          retailPrice: item.retailPrice ?? '',
          minControlPrice: item.minControlPrice ?? '',
          price: this.filtrationPrice(item)
        };
      }); // 商品规格值信息
      // 初始化规格和sku信息
      this.$refs.commodityStock.setData({
        skuVOlist,
        specificationVOList
      });
    },
    // 价格过滤 批发价（元） 需要根据commodityType和采货类型来确定显示什么价格
    filtrationPrice(item) {
      const purchaseType = this.form.purchaseType; // 采货类型、 采销、一件代发   { id: 'PURCHASE', name: '采销' }, { id: 'DROP_SHIPPING', name: '一件代发' }
      let key = '';
      switch (item.commodityType) {
        case 'GENERAL_TRADE':
          // 大贸
          key = purchaseType === 'PURCHASE' ? 'purchaseSupplyPriceTrade' : 'dropShippingSupplyPriceTrade';
          break;
        case 'CROSS_BORDER_TRADE':
          // 海淘
          key = purchaseType === 'PURCHASE' ? 'purchaseSupplyPriceOverseas' : 'dropShippingSupplyPriceOverseas';
          break;
        case 'GENERAL_AND_CROSS_BORDER_TRADE':
          // 大贸海淘  如果是【大贸、大贸海淘】都显示为【大贸】
          key = purchaseType === 'PURCHASE' ? 'purchaseSupplyPriceTrade' : 'dropShippingSupplyPriceTrade';
          break;
      }
      return key ? item[key] ?? '' : '';
    },
    // 对部分表单字段进行校验的方法
    validateField(type) {
      this.$refs['form'].validateField(type);
    },
    // 滚动到固定地方
    scrollView(object) {
      if (!object) return;
      for (const i in object) {
        let dom = this.$refs[i];
        if (Object.prototype.toString.call(dom) !== '[object Object]') {
          dom = dom[0];
        }
        dom.$el.scrollIntoView({
          block: 'center',
          behavior: 'smooth'
        });
        break;
      }
    },
    // 删除关联
    removeSpecCode(type) {
      const text = type === 'detailSpecCode' ? '是否删除【商品详情】sku关联？' : '是否删除【商品参数】sku关联？';
      this.$confirm(text, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          // 提交数据
          this.$message.success('删除成功，需要保存后生效！');
          this.tradeCenterCommodityExt[type] = '';
        })
        .catch(() => {
          this.$message.info('已取消删除!');
        });
    },
    openSpecCode(type) {
      if (!this.form.purchaseType) {
        this.$message.error('请先选择采货类型');
        return;
      }
      if (!this.form.brandId) {
        this.$message.error('请先选择商品品牌');
        return;
      }
      this.SpecCodeType = type;
      this.$refs.FXSelectGoodsRadio.open();
    },
    // 合并详情
    MergeDetails(skuOBJ, type) {
      const isCover = type === 'cover'; // 是否覆盖原数据
      if ((!this.form.thumbnailUrl && skuOBJ['logoUrl']) || isCover) {
        this.form.thumbnailUrl = skuOBJ['logoUrl'] || '';
      }
      if ((!this.form.zipFile && skuOBJ['material']) || isCover) {
        this.form.zipFile = skuOBJ['material'];
      }
      if (skuOBJ?.commodityDetailVO?.video || isCover) {
        // 商品主视频
        this.form.video = skuOBJ.commodityDetailVO?.video ?? '';
      }
      if (skuOBJ?.commodityDetailVO?.windowPictureList || isCover) {
        // 商品橱窗图
        this.form.shopwindowList = skuOBJ.commodityDetailVO?.windowPictureList ?? '';
      }
      if (skuOBJ?.commodityDetailVO?.detailPictureList || isCover) {
        // 商品详情图
        this.form.detailList = skuOBJ.commodityDetailVO?.detailPictureList ?? '';
      }
      this.tradeCenterCommodityExt['detailSpecCode'] = skuOBJ.barcode;
    },
    // 合并商品参数
    MergeCommodityParams(skuOBJ, type) {
      const isCover = type === 'cover'; // 是否覆盖原数据
      this.commodityParams.map((item) => {
        if (item.commodityCenterFieldRelated === '1' && item.commodityCenterFeildVO) {
          // 检查已关联主数据的  覆盖原数据
          const ParamsKey = item.commodityCenterFeildVO?.fieldName;
          const { commodityExtendInfoVO = {} } = skuOBJ;
          const val = commodityExtendInfoVO[ParamsKey] ?? '';
          this.$set(this.form.commodityParam, [item.id], val);
        } else {
          if (isCover && !this.form.commodityParam[item.id]) {
            this.$set(this.form.commodityParam, [item.id], '');
          }
        }
      });
      this.tradeCenterCommodityExt['paramSpecCode'] = skuOBJ.barcode;
    },
    // 更换条码
    FXSelectGoodsRadioSave(skuOBJ) {
      const k = this.SpecCodeType;
      if (k === 'detailSpecCode') {
        // 替换详情数据
        this.MergeDetails(skuOBJ, 'cover');
      } else if (k === 'paramSpecCode') {
        // 替换商品参数
        this.MergeCommodityParams(skuOBJ, 'cover');
      }
    },
    // 品牌改变
    brandIdChange(val) {
      const { stockData = {}, skuVOlist = [], specifications = [] } = this.$refs.commodityStock;
      const { isAdd, currentBrandInfo } = this;
      const changeBrandInfo = this.originBrandList.find((item) => item.id === val);
      const currentOrderBrandType = currentBrandInfo?.orderBrandType;
      const changeOrderBrandType = changeBrandInfo?.orderBrandType;
      const emptyFlag = this.hasNonEmptyValue(stockData);
      if (isAdd && currentOrderBrandType && changeOrderBrandType && changeOrderBrandType !== currentOrderBrandType && (skuVOlist.length || specifications.length || emptyFlag)) {
        this.$confirm('您修改的品牌会导致清空下面已选择的商品，请确认是否清空？')
          .then(() => {
            this.$refs.brandIdSelect.blur();
            this.form.brandId = val;
            this.resetFormValue();
          })
          .catch(() => {
            this.$refs.brandIdSelect.blur();
          });
      } else {
        this.form.brandId = val;
      }
    },
    // 判断对象中是否不包含空值
    hasNonEmptyValue(obj) {
      const values = Object.values(obj);
      return values.some((value) => value !== null && value !== undefined && value !== '');
    },
    // 品牌改变清除表单值
    resetFormValue() {
      this.form.name = '';
      this.form.thumbnailUrl = '';
      this.form.video = '';
      this.form.videoCover = '';
      this.form.shopwindowList = [];
      this.form.detailList = [];
      this.form.zipFile = '';
      this.tradeCenterCommodityExt.paramSpecCode = '';
      this.tradeCenterCommodityExt.detailSpecCode = '';
      // 初始化规格和sku信息
      this.$refs.commodityStock.setData({
        skuVOlist: [],
        specificationVOList: []
      });
    }
  },
  components: {
    Anchor,
    CommodityStock,
    VideoUpload,
    // FeatureList,
    ImageManagement,
    AddCoupon,
    AddItemCommodity,
    AddCommodity,
    ActivityAttribute,
    Upload,
    SvgIcon,
    FXSelectGoods
  }
};
</script>

<style lang="scss" scoped>
@import './styles';
</style>
