<!-- 批量导入商品 -->
<template>
  <div class="table-container import">
    <import-page name="商品" ref="importPage" :fileSizeLimit="5" :fileRowNumLimit="500" :uploadRequest="uploadRequest" :downloadFailRequest="downloadFailRequest" :templateUrl="templateUrl" @uploadFileSuccess="uploadFileSuccess" @onSubmit="onSubmit">
      <BatchCreate ref="batchCreate" class="preview-table" type="Table" :tableColumns="importOptions" :isUpdate="false" @deleteRow="deleteRow">
        <template slot="tip" slot-scope="{ scope }">
          <el-tooltip class="item" effect="dark" :content="scope.row.errorMsg || '成功'" placement="top-end">
            <span :class="scope.row.isSuccess === '0' ? 'el-icon-warning' : 'el-icon-success'"></span>
          </el-tooltip>
        </template>
      </BatchCreate>
    </import-page>
  </div>
</template>

<script>
import importPage from '@/components/Common/Import/importPage.vue';
import BatchCreate from '@/components/Common/BatchCreate';
import { zgCommodityCreateInBatch, zgCommodityValidInBatch } from '@/api/commodity';

export default {
  components: { importPage, BatchCreate },
  data() {
    return {
      loading: false,
      uploadRequest: '/soyoungzg/api/zgCommodity/batchImport',
      downloadFailRequest: '/soyoungzg/api/zgCommodity/exportError',
      // 导入模板地址
      templateUrl: 'https://oss.syounggroup.com/static/file/soyoung-zg/template/分销平台商品批量导入模板.xlsx',
      formStates: [],
      formData: [],
      importOptions: [
        {
          key: 'specCode',
          label: '商品条码',
          rule: [{ required: true }],
          elOptions: {
            width: '120px'
          }
        },
        {
          key: 'name',
          label: '商品名称',
          rule: [{ required: true }],
          elOptions: {
            width: '120px'
          }
        },
        {
          key: 'brandName',
          label: '商品品牌',
          rule: [{ required: true }],
          elOptions: {
            width: '120px'
          }
        },
        {
          key: 'purchaseType',
          label: '采货类型',
          rule: [{ required: true }],
          elOptions: {
            width: '120px'
          }
        },

        {
          key: 'price',
          label: '批发价',
          rule: [{ required: true }],
          elOptions: {
            width: '120px'
          }
        },
        {
          key: 'retailPrice',
          label: '建议零售价',
          rule: [{ required: true }],
          elOptions: {
            width: '120px'
          }
        },
        {
          key: 'minControlPrice',
          label: '活动大促价',
          rule: [{ required: true }],
          elOptions: {
            width: '120px'
          }
        },
        {
          key: 'stock',
          label: '库存',
          rule: [{ required: true }],
          elOptions: {
            width: '120px'
          }
        },
        {
          key: 'expressFeeStr',
          label: '运费设置',
          rule: [{ required: true }],
          elOptions: {
            width: '200px'
          }
        },
        {
          key: 'category',
          label: '商品分组',
          rule: [{ required: true }],
          elOptions: {
            width: '120px'
          }
        },
        {
          key: 'miniOrderQuantity',
          label: '起订量',
          rule: [{ required: true }],
          elOptions: {
            width: '120px'
          }
        },
        {
          key: 'status',
          label: '上架状态',
          rule: [{ required: true }],
          elOptions: {
            width: '120px'
          }
        },
        {
          key: 'isHidden',
          label: '是否隐藏',
          rule: [{ required: true }],
          elOptions: {
            width: '120px'
          }
        }
      ]
    };
  },
  computed: {},
  mounted() {},
  methods: {
    // 文件上传成功
    uploadFileSuccess(data) {
      this.formData = data;
      this.$refs.batchCreate.init(data);
    },
    // 删除行
    deleteRow() {
      const tableData = this.$refs.batchCreate.tableData;
      this.zgCommodityValidInBatch(tableData).then((res) => {
        if (res.code === '0') {
          this.$refs.importPage.updateData(res.data); // 覆盖失败条数
          this.uploadFileSuccess(res.data.dataDetails); // 覆盖表格
        }
      });
    },
    // 校验
    zgCommodityValidInBatch(tableData) {
      return zgCommodityValidInBatch(tableData).then((res) => {
        return res;
      });
    },
    // 保存
    onSubmit(callback) {
      const tableData = this.$refs.batchCreate.tableData;
      if (!tableData.length) {
        this.$message({
          message: '当前无能导入的数据',
          type: 'warning'
        });
        callback();
        return;
      }

      if (tableData.some((i) => i.isSuccess === '0')) {
        this.$message({
          message: '请删除错误信息再导入',
          type: 'warning'
        });
        callback();
        return;
      }
      this.zgCommodityValidInBatch(tableData)
        .then((res) => {
          this.$refs.importPage.handleSuccess(res);
          if (this.formData.length === 0) {
            this.$message({
              message: '当前无能导入的数据',
              type: 'warning'
            });
            callback && callback();
            return;
          }
          if (this.formData.some((i) => i.isSuccess === '0')) {
            this.$message({
              message: '请删除错误信息再导入',
              type: 'warning'
            });
            callback && callback();
            return;
          }
          const tip = `是否确认保存${this.formData.length}条有效数据`;
          this.$confirm(tip, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
            .then(() => {
              // 提交数据
              zgCommodityCreateInBatch(this.formData)
                .then((res) => {
                  this.$message({
                    type: 'success',
                    message: `导入成功`
                  });
                  this.cancel();
                })
                .finally(() => {
                  callback();
                });
            })
            .catch(() => {
              this.$message({
                type: 'info',
                message: '已取消导入'
              });
              callback();
            });
        })
        .catch(() => {
          callback();
        });
    },
    cancel() {
      this.$back({ path: '/commodity/list' });
    }
  }
};
</script>

<style lang='scss' scoped>
.import {
  height: 100%;
}
.el-icon-warning {
  color: var(--color-warning);
  font-size: 20px;
  vertical-align: middle;
}
.el-icon-success {
  color: var(--color-success);
  font-size: 20px;
  vertical-align: middle;
}
</style>
