<template>
  <div>
    <filter-form :options="_filterFormOptions" @query="query" ref="filterForm"></filter-form>

    <table-exhibition @query="query" :options="_tableOptions" :table="table" ref="table" v-loading="loading" style="margin-top: 20px"></table-exhibition>
  </div>
</template>

<script>
import FilterForm from '@/components/Form/FilterForm';
import TableExhibition from '@/components/Table/TableExhibition';
import { FilterFormOptions, TableOptions } from './config';
import { pushProductCensusList } from '@/api/commodity/pushProduct.js';

export default {
  // 私人推品清单
  name: 'pushProductCensus_private',
  components: { FilterForm, TableExhibition },
  data() {
    return {
      type: 'PRIVATE',
      loading: false,
      table: {}
    };
  },
  created() {
    this.init();
  },
  activated() {
    this.query();
  },
  methods: {
    init() {
      this._filterFormOptions = FilterFormOptions;
      this._tableOptions = TableOptions;
    },
    query({ data } = {}) {
      const params = {
        ...this.$refs.table.getParams(),
        ...(data ? { data, pageNo: 1 } : {data: this.$refs.filterForm.getParams()})
      };
      params.data.type = this.type;

      this.loading = true;
      pushProductCensusList(params)
        .then((res) => {
          const { pageSize, pageNo } = params;
          const { total, list } = res.data;
          list.forEach((i, index) => {
            i._pushStatisticsIndex = total - pageSize * (pageNo - 1) - index;
          });
          
          this.table = res.data;
        })
        .finally(() => {
          this.loading = false;
        });
    }
  }
};
</script>

<style lang='scss' scoped>
</style>