import dict from '@/components/Common/dicts';
import { checkGroup } from '@/components/Common/SelectGroup';

export const FilterFormOptions = [
  {
    component: 'input',
    label: '清单名称',
    prop: 'name'
  },
  {
    component: 'select',
    label: '所属团队',
    prop: 'groupId',
    options: dict('DISTRIBUTOR_TEAM_DS'),
    placeholder: '请选择所属团队',
    visible: () => !checkGroup()
  }
];

// 列表展示内容
export const TableOptions = [
  {
    prop: '_pushStatisticsIndex',
    label: '推品清单编号',
    width: '120',
  },
  {
    prop: 'name',
    label: '清单'
  },
  {
    prop: 'groupName',
    label: '所属团队'
  },
  {
    prop: 'pushMemberNum',
    label: '推送人数'
  },
  {
    prop: 'openMemberNum',
    label: '打开人数'
  },
  {
    prop: 'addCartMemberNum',
    label: '加购人数'
  },
  {
    prop: 'purchaseMemberNum',
    label: '下单人数'
  },
  {
    prop: 'openNum',
    label: '打开次数'
  },
  {
    prop: 'purchaseNum',
    label: '下单次数'
  }
];
