<template>
  <div class="table-container table">
    <el-tabs v-model="activeName" class="custom-border-tabs">
      <el-tab-pane v-for="item of tabs" :key="item.name" :label="item.label" :name="item.name"></el-tab-pane>
    </el-tabs>
    <keep-alive>
      <component :is="activeName" :key="activeName"></component>
    </keep-alive>
  </div>
</template>

<script>
import Common from './component/Common';
import Private from './component/Private';

export default {
  name: 'commodity-pushProductCensus-list',
  components: { Common, Private },
  data() {
    return {
      activeName: 'Common',
      tabs: [
        { label: '公共推品统计', name: 'Common' },
        { label: '私人推品统计', name: 'Private' }
      ]
    };
  },
  computed: {},
  created() {},
  methods: {}
};
</script>

<style lang="scss" scoped>
</style>
