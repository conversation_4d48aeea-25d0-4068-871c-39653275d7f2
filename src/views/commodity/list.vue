<template>
  <div class="app-container">
    <div class="table-container">
      <el-tabs @tab-click="onTabClick" class="tabs custom-border-tabs" v-model="activeName">
        <el-tab-pane label="全部" name></el-tab-pane>
        <el-tab-pane label="待上架" name="1"></el-tab-pane>
        <el-tab-pane label="上架中" name="2"></el-tab-pane>
        <el-tab-pane label="已下架" name="3"></el-tab-pane>
      </el-tabs>

      <form @submit.prevent="onSearch" class="commo-search-container commo-search-flex commo-search-margin-bottom10">
        <div class="commo-search-item">
          <span class="commo-search-item-label">商品名称:</span>
          <div class="commo-search-item-content">
            <el-input clearable size="small" v-model.trim="filter.name"></el-input>
          </div>
        </div>
        <div class="commo-search-item">
          <span class="commo-search-item-label">商品ID:</span>
          <div class="commo-search-item-content">
            <el-input clearable size="small" v-model.trim="filter.id"></el-input>
          </div>
        </div>
        <div class="commo-search-item">
          <span class="commo-search-item-label">商品分组:</span>
          <div class="commo-search-item-content">
            <el-select clearable size="small" v-model="filter.categoryIds" filterable multiple>
              <el-option :key="item.value" :label="item.name" :value="item.id" v-for="item in typeOptions"></el-option>
            </el-select>
          </div>
        </div>
        <div class="commo-search-item">
          <span class="commo-search-item-label">商品类型:</span>
          <div class="commo-search-item-content">
            <el-select clearable size="small" v-model="filter.type">
              <el-option :key="idx" :label="item.label" :value="item.value" v-for="(item, idx) in commodityTypeList"></el-option>
            </el-select>
          </div>
        </div>
        <div class="commo-search-item">
          <span class="commo-search-item-label">是否隐藏:</span>
          <div class="commo-search-item-content">
            <el-select clearable size="small" v-model="filter.isHidden">
              <el-option :key="idx" :label="item.label" :value="item.value" v-for="(item, idx) in isHiddenOption"></el-option>
            </el-select>
          </div>
        </div>
        <div class="commo-search-item">
          <span class="commo-search-item-label">商品规格编码:</span>
          <div class="commo-search-item-content">
            <el-input clearable size="small" v-model.trim="filter.specCode"></el-input>
          </div>
        </div>
        <div class="commo-search-item">
          <span class="commo-search-item-label">通关口岸:</span>
          <div class="commo-search-item-content">
            <el-select :loading="customsListLoading" clearable size="small" v-model="filter.customs">
              <el-option :key="item.value" :label="item.label" :value="item.value" v-for="item in customsList"></el-option>
            </el-select>
          </div>
        </div>
        <div class="commo-search-item">
          <span class="commo-search-item-label">总销量:</span>
          <div class="commo-search-item-content">
            <el-input clearable maxlength="100000000" v-int class="commo-search-item-content--rangeinput" size="small" v-model="filter.minSalesVolume"></el-input>
            <span class="divide">-</span>
            <el-input clearable maxlength="100000000" v-int class="commo-search-item-content--rangeinput" size="small" v-model="filter.maxSalesVolume"></el-input>
          </div>
        </div>
        <div class="commo-search-item">
          <span class="commo-search-item-label">是否返利:</span>
          <div class="commo-search-item-content">
            <el-select clearable size="small" v-model="filter.creditBack">
              <el-option :key="item.value" :label="item.label" :value="item.value" v-for="item in creditBackOptions"></el-option>
            </el-select>
          </div>
        </div>
        <div class="commo-search-item">
          <span class="commo-search-item-label">规格标识:</span>
          <div class="commo-search-item-content">
            <el-input clearable size="small" v-model.trim="filter.skuId"></el-input>
          </div>
        </div>
        <div class="commo-search-item">
          <span class="commo-search-item-label">商品品牌:</span>
          <div class="commo-search-item-content">
            <el-select clearable size="small" style="width: 100%" v-model="filter.brandIds" filterable multiple>
              <el-option v-for="item in brandList" :key="item.value" :label="item.label" :value="item.value"></el-option>
            </el-select>
          </div>
        </div>
        <div class="commo-search-item">
          <span class="commo-search-item-label">采货类型:</span>
          <div class="commo-search-item-content">
            <el-select clearable size="small" v-model="filter.purchaseType">
              <el-option :key="item.id" :label="item.name" :value="item.id" v-for="item in purchaseTypeOptions"></el-option>
            </el-select>
          </div>
        </div>
        <div class="commo-search-item">
          <span class="commo-search-item-label">上架时间:</span>
          <div class="commo-search-item-content">
            <el-date-picker
              size="small"
              class="commo-search-item-content--picker"
              v-model="onlineTime"
              type="daterange"
              align="right"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :default-time="['00:00:00', '23:59:59']"
              value-format="timestamp"
              :picker-options="mixPickerOptions"
              @change="changeOnlineTime"
            ></el-date-picker>
          </div>
        </div>
        <div class="commo-search-item">
          <span class="commo-search-item-label">创建时间:</span>
          <div class="commo-search-item-content">
            <el-date-picker
              size="small"
              class="commo-search-item-content--picker"
              v-model="createDate"
              type="daterange"
              align="right"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              :default-time="['00:00:00', '23:59:59']"
              value-format="timestamp"
              :picker-options="mixPickerOptions"
              @change="changeCreateDate"
            ></el-date-picker>
          </div>
        </div>
        <div class="commo-search-item">
          <span class="commo-search-item-label">商品标签:</span>
          <div class="commo-search-item-content">
            <el-select filterable clearable size="small" v-model="filter.commodityLabelIds" multiple>
              <el-option :key="item.id" :label="item.name" :value="item.id" v-for="item in commodityLabel"></el-option>
            </el-select>
          </div>
        </div>
        <div class="commo-search-item">
          <span class="commo-search-item-label">返点使用:</span>
          <div class="commo-search-item-content">
            <el-select clearable size="small" v-model="filter.creditDeduction">
              <el-option :key="item.value" :label="item.label" :value="item.value" v-for="item in creditDeductionOptions"></el-option>
            </el-select>
          </div>
        </div>
        <div class="commo-search-btns">
          <el-button native-type="submit" size="small" type="primary">查询</el-button>
          <el-button @click="onResetClick" size="small">重置</el-button>
          <Authority auth="/commodity/list/:export">
            <el-button @click="onExport" size="small">导出</el-button>
          </Authority>
        </div>
      </form>
      <div class="btn-linebox">
        <Authority auth="/commodity/list/:add">
          <router-link :to="{ path: '/commodity/add' }">
            <el-button size="small" type="primary" class="btn-linebox__add"> + 新增 </el-button>
          </router-link>
        </Authority>
        <Authority auth="/commodity/list/:import">
          <router-link :to="{ path: '/commodity/import-commodity' }">
            <el-button size="small" type="primary"> 导入 </el-button>
          </router-link>
        </Authority>
      </div>
      <el-table :data="list" :sort-orders="sortArray" @selection-change="handleSelectionChange" @sort-change="sortChange" element-loading-text="加载中" highlight-current-row ref="multipleTable" v-loading="listLoading" v-el-horizontal-scroll>
        <el-table-column type="selection" width="55" fixed="left"></el-table-column>
        <el-table-column align="center" label="商品名称" prop="name" fixed="left" width="130"></el-table-column>
        <el-table-column align="center" label="商品ID" prop="id" sortable="custom" width="100"></el-table-column>
        <el-table-column align="center" label="缩略图" prop="thumbnailUrl" width="80">
          <template slot-scope="scope">
            <img :src="scope.row.thumbnailUrl || require('@/assets/default-image2.png')" class="thumbImg" />
          </template>
        </el-table-column>
        <el-table-column align="center" label="商品类型" prop="type" width="80">
          <template slot-scope="scope">
            {{ getCommodityType(scope.row.type) }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="商品标签" prop="commodityLabelNames" width="100">
          <template slot-scope="{ row }">
            {{ row.commodityLabelNames ? row.commodityLabelNames.join(' 、 ') : '' }}
          </template>
        </el-table-column>

        <el-table-column label="商品状态" align="center" width="80">
          <template slot-scope="scope">
            {{ scope.row.status | filtrationStatus }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="是否隐藏" prop="isHidden" width="80">
          <template slot-scope="scope">
            <p v-if="scope.row.isHidden === '1'">是</p>
            <p v-if="scope.row.isHidden === '0'">否</p>
          </template>
        </el-table-column>
        <el-table-column align="center" label="是否返利" prop="creditBack">
          <template slot-scope="scope">
            <p v-if="scope.row.creditBack === '1'">是</p>
            <p v-if="scope.row.creditBack === '0'">否</p>
          </template>
        </el-table-column>
        <el-table-column align="center" label="库存" prop="stock" sortable="custom"></el-table-column>
        <el-table-column align="center" label="销量" prop="salesVolume" sortable="custom"></el-table-column>
        <el-table-column align="center" label="备注" prop="remarks" width="140px">
          <template slot-scope="scope" width="130px">
            <CommoEllipsis :key="scope.row.id" :isShowView="false" :contentWidth="110" :lineClamp="2" effect="light" :text="scope.row.remarks">
              <div class="list-note">
                {{ scope.row.remarks }}
              </div>
            </CommoEllipsis>
          </template>
        </el-table-column>
        <el-table-column align="center" label="排序" width="150" sortable="custom" prop="sort">
          <!-- eslint-disable-next-line -->
          <template slot="header" slot-scope="scope">
            <div style="padding-right: 0">
              排序
              <el-tooltip class="item" effect="dark" placement="top">
                <div slot="content" class="header-tooltip-sort">商品排序：<br />1、排序规则：序号越大的排在越前面，如果序号相同的话，那么按照添加时间，时间越新添加的排在越前面；【最大数值为99999】<br />2、排序显示：前端售卖的商品列表显示会按照这个顺序来显示上架中的商品；</div>
                <i class="el-icon-question"></i>
              </el-tooltip>
            </div>
          </template>
          <template slot-scope="scope">
            <div class="list-sort--box">
              <el-input v-model.number="scope.row.sort" @blur="sortInput($event, scope.row.id)" type="number"></el-input>
              <SvgIcon iconClass="zhiding1" :className="'zhiding'" @click="sortTop(scope.row.id)" v-if="scope.$index !== 0"></SvgIcon>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" label="最新上架时间" prop="onlineTime" width="110">
          <div slot-scope="scope">
            {{ scope.row.onlineTime | parseTime }}
          </div>
        </el-table-column>
        <el-table-column align="center" label="创建时间" prop="createDate" width="110">
          <div slot-scope="scope">
            {{ scope.row.createDate | parseTime }}
          </div>
        </el-table-column>
        <el-table-column align="center" label="操作" width="180" fixed="right">
          <template slot-scope="scope">
            <el-button class="hide-goods" size="small" type="text" v-if="scope.row.isHidden === '1'" key="1">
              <span class="hided">已隐藏</span>
              <span @click="cancelHide(scope.row)" class="cancel">不隐藏</span>
            </el-button>
            <Authority auth="/commodity/list/:view" key="2">
              <el-button size="small" type="text">
                <router-link :to="{ path: '/commodity/detail/' + scope.row.id }">查看</router-link>
              </el-button>
            </Authority>
            <Authority auth="commodity-list-edit" key="3">
              <el-button size="small" type="text">
                <router-link :to="{ path: '/commodity/edit/' + scope.row.id }">编辑</router-link>
              </el-button>
            </Authority>
            <Authority auth="/commodity/list/:copy" key="4">
              <el-button size="small" type="text">
                <router-link :to="{ path: '/commodity/copy/' + scope.row.id }">复制</router-link>
              </el-button>
            </Authority>
            <Authority auth="commodity-list-edit" key="5">
              <el-button v-show="scope.row.status === '3'" @click="moveToAbandon(scope.row)" size="small" type="text"> 移到回收站 </el-button>
            </Authority>
            <div style="display: flex; justify-content: space-around">
              <Authority auth="commodity-list-edit">
                <dialog-commodity-stock :id="scope.row.id" @updatePrice="updatePrice" />
              </Authority>

              <el-button type="text" @click="$refs.dialog_remark.setVisible(true, scope.row)" size="small">修改备注</el-button>
            </div>
            <div style="display: flex; justify-content: space-around">
              <promotion :id="scope.row.id" :key="scope.row.id" v-if="scope.row.isHidden === '0'" promType="goodsDetail"></promotion>
              <HiddenProductPromotion v-else :commodityId="scope.row.id" :key="scope.row.id"></HiddenProductPromotion>
              <GoodPreview :id="scope.row.id" />
            </div>
            <div :class="{ takedown: status === '1' }" class="time-tool" v-if="scope.row.autoShelveType">
              <span>{{ scope.row.autoShelveDate | parseDefaultTime() }}</span>
              <span v-if="scope.row.autoShelveType === 'OFF'">下架</span>
              <span v-else>上架</span>
              <i @click="cancelAutoShelve(scope.row)" class="icon el-icon-error"></i>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div class="batch-operation">
        <BatchOperation pageName="commodity_manage" style="position: fixed; bottom: 0; z-index: 999"></BatchOperation>
      </div>
      <div class="pagination" style="position: relative">
        <el-pagination
          style="margin-bottom: 30px"
          :current-page="pageNo"
          :disabled="listLoading"
          :page-size="pageSize"
          :page-sizes="[10, 20, 30, 40, 50, 100]"
          :total="total"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
          background
          layout="total, sizes, prev, pager, next, jumper"
        ></el-pagination>
      </div>

      <Dialog :callback="fetchData" ref="dialog_remark" type="COMMODITYMANAGE_REMARK"> </Dialog>
      <ChooseCategory :commodityIdList="multipleSelection" :dialogVisible="visible" @clearCommodityList="clearCommodityList" @dialogVisible="dialogVisible"></ChooseCategory>
    </div>
  </div>
</template>

<script>
import dict from '@/components/Common/dicts';
import { disableCommodity } from '@/api/commodity';
import download from '@/utils/download';
import Promotion from '@/components/Promotion';
import HiddenProductPromotion from '@/components/HiddenProductPromotion';
import pickBy from 'lodash/pickBy';
import pick from 'lodash/pick';
import mapValues from 'lodash/mapValues';
import { listCustoms } from '@/api/common/dict';
import { parseTime } from '@/utils';
import SvgIcon from '@/components/SvgIcon';
import ChooseCategory from '@/components/ChooseCategory';
import DialogCommodityStock from '@/components/DialogCommodityStock';
import GoodPreview from './GoodPreview';
import BatchOperation from '@/components/BatchOperation';
import Dialog from '@/components/Dialog/index.vue';
import CommoEllipsis from '@/components/CommoEllipsis';
import debounce from 'lodash/debounce';
import { listAllBrandName } from '@/api/brand/brand-info';
import { list, deleteByIds, updateSaleStatus, goodsType, exportByCondition, changeSort, changeSortTOTOP } from '@/api/commodity/list';
import { batchHiddenCommodity, batchCancelHiddenCommodity, cancelHiddenCommodity, autoOnShelve, autoOffShelve, cancelAutoOnShelve, cancelAutoOffShelve, batchDisableCommodity, getCommodityLabel } from '@/api/commodity';
export default {
  components: {
    promotion: Promotion,
    HiddenProductPromotion: HiddenProductPromotion,
    ChooseCategory,
    SvgIcon,
    DialogCommodityStock,
    GoodPreview,
    BatchOperation,
    Dialog,
    CommoEllipsis
  },
  computed: {
    // 过滤
    data() {
      const { minSalesVolume, maxSalesVolume, ...rest } = this.filter;
      return {
        ...pickBy(rest, (val) => !!val),
        ...mapValues(
          pickBy({ minSalesVolume, maxSalesVolume }, (val) => val !== ''),
          (val) => parseInt(val, 10)
        ),
        status: this.status
      };
    }
  },
  name: 'commodity-list',
  filters: {
    filtrationStatus(statusKey) {
      // 0-待上架，1-上架，2-定时上架，3-下架 ,
      const statusObj = {
        0: '待上架',
        1: '上架',
        2: '定时上架',
        3: '下架'
      };
      return statusObj[statusKey] || statusKey;
    }
  },
  data() {
    //  const { getter: getCommodityType } = new Dict(COMMODITY_TYPE);
    const initFilter = {
      name: '',
      id: '',
      specCode: '',
      categoryIds: [],
      creditBack: '',
      isHidden: '',
      type: '',
      customs: '',
      minSalesVolume: '',
      maxSalesVolume: '',
      skuId: '',
      brandIds: [],
      purchaseType: '',
      isEnable: '1',
      onlineBeginTime: '',
      onlineEndTime: '',
      createBeginDate: '',
      createEndDate: '',
      commodityLabelIds: [], // 商品标签
      creditDeduction: '', // 返点使用
      commodityClass: 'NORMAL'
    };
    return {
      creditDeductionOptions: [
        { label: '可以使用', value: '1' },
        { label: '不可以使用', value: '0' }
      ],
      pickerOptions: {
        disabledDate(date) {
          return date.getTime() + 86400000 < Date.now();
        }
      },
      isHiddenOption: [
        { label: '是', value: '1' },
        { label: '否', value: '0' }
      ],
      commodityTypeList: [],
      purchaseTypeOptions: [
        { id: 'PURCHASE', name: '采销' },
        { id: 'DROP_SHIPPING', name: '一件代发' }
      ], // 采货类型
      activeName: '',
      initFilter: initFilter,
      filter: { ...initFilter },
      filters: [],
      list: [],
      multipleSelection: [],
      pageNo: 1,
      pageSize: 10,
      status: '',
      listLoading: false,
      total: 0,
      sortArray: ['ascending', 'descending'],
      typeOptions: [],
      creditBackOptions: [
        { value: '1', label: '是' },
        { value: '0', label: '否' }
      ],
      orderBy: '',
      customsListLoading: true,
      customsList: [],
      downTimeVal: '',
      upTimeVal: '',
      ref: 'up',
      exportLoading: false,
      visible: false, // 改分组弹窗是否展示
      brandList: [],
      onlineTime: [],
      createDate: [], // 创建时间
      commodityLabel: []
    };
  },
  activated() {
    this.fetchBrand(); // 获取所有品牌
    this.fetchCommodityType();
    this.getCommodityLabel();
    this.fetchGoodsType();
    this.fetchData();
    listCustoms()
      .then((res) => {
        this.customsList = res.data;
      })
      .finally(() => {
        this.customsListLoading = false;
      });
  },
  methods: {
    getCommodityType(value) {
      let key = '';
      switch (value) {
        case 'ACTUAL':
          // 大贸
          key = '大贸';
          break;
        case 'GLOBAL':
          // 海淘
          key = '海淘';
          break;
      }
      return key;
    },
    // 上架时间筛选
    changeOnlineTime(onlineTime) {
      this.filter.onlineBeginTime = onlineTime ? onlineTime[0] : '';
      this.filter.onlineEndTime = onlineTime ? onlineTime[1] : '';
    },
    // 创建时间筛选
    changeCreateDate(createDate) {
      this.filter.createBeginDate = createDate ? createDate[0] : '';
      this.filter.createEndDate = createDate ? createDate[1] : '';
    },
    // 移到回收站
    moveToAbandon(data) {
      this.$confirm(`确认该商品移到回收站吗？`, '确认提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          disableCommodity(data.id).then((response) => {
            if (response.code === '0') {
              this.$message.success('操作成功');
              this.fetchData();
            }
          });
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '取消操作'
          });
        });
    },
    // 批量移到回收站
    getRecovery() {
      if (this.multipleSelection.length < 1) {
        this.$message.error('您未选中任何数据哦~');
        return;
      }
      this.$confirm(`确认该商品移到回收站吗？`, '确认提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          const list = this.multipleSelection;
          const ids = [];
          list.map((item) => {
            ids.push(item.id);
          });
          batchDisableCommodity(ids).then((response) => {
            if (response.code === '0') {
              this.$message.success('操作成功');
              this.fetchData();
            }
          });
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '取消操作'
          });
        });
    },
    sortInput($event, id) {
      if ($event.target.value === '') {
        this.$message.error('请输入正整数');
        this.fetchData();
        return;
      }
      let value = Number($event.target.value);
      if (value > 99999) {
        value = 99999;
      }
      if (value >= 0 && parseInt(value, 10) === value) {
        this.sort(id, value);
      } else {
        this.$message.error('请输入正整数');
        this.fetchData();
      }
    },
    // 排序
    sort(id, sort) {
      changeSort({
        commodityId: id,
        sort
      })
        .then((response) => {
          this.$message.success(response.msg);
        })
        .finally(() => {
          // this.fetchData();
        });
    },
    sortTop(id) {
      changeSortTOTOP(id)
        .then((response) => {
          this.$message.success('置顶成功');
        })
        .finally(() => {
          this.fetchData();
        });
    },
    // 清除选中的多选项商品
    clearCommodityList(val) {
      if (val.length === 0) {
        this.fetchData();
      }
    },
    // 改分组组件是否展示
    dialogVisible(value) {
      this.visible = value;
    },
    // 改分组
    changeCategory() {
      if (this.multipleSelection.length < 1) {
        this.$message.error('您未选中任何数据哦~');
        return;
      } else {
        this.visible = true;
      }
    },
    // 导出商品
    onExport: debounce(function (event) {
      this.exportLoading = true;
      const listQuery = {
        ...this.data,
        ...(this.orderBy && { orderBy: this.orderBy })
      };
      exportByCondition(listQuery)
        .then((res) => {
          download(res, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', `商品列表-${parseTime(Date.now(), '{y}-{m}-{d}-{h}:{i}:{s}')}.xlsx`);
        })
        .finally(() => {
          this.exportLoading = false;
        });
    }, 200),
    // 获取商品类型
    fetchCommodityType() {
      // 订单状态列表
      dict('COMMONODITY_TYPE').then((res) => {
        this.commodityTypeList = res;
      });
    },
    onTabClick(tab, event) {
      const tabNames = ['', '0', '1', '3'];
      const pIndex = tabNames.indexOf(this.status);
      if (pIndex !== -1) {
        this.$set(this.filters, pIndex, this.filter);
      }
      this.status = tabNames[tab.index];
      if (this.filters[tab.index]) {
        this.filter = this.filters[tab.index];
      } else {
        this.filter = { ...this.initFilter };
        this.$set(this.filters, tab.index, this.filter);
      }

      this.pageNo = 1;
      this.pageSize = 10;
      this.fetchData();
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    fetchGoodsType() {
      goodsType().then((response) => {
        this.typeOptions = response.data;
      });
    },
    getCommodityLabel() {
      getCommodityLabel().then((response) => {
        this.commodityLabel = response.data;
      });
    },
    fetchData() {
      this.listLoading = true;
      const listQuery = {
        ...pick(this, ['pageNo', 'pageSize', 'data']),
        ...(this.orderBy && { orderBy: this.orderBy })
      };
      list(listQuery)
        .then((response) => {
          this.$nextTick(() => {
            const lists = response.data.list;
            this.list = lists;
            this.total = response.data.total;
          });
        })
        .finally(() => {
          this.listLoading = false;
        });
    },
    sortChange(data) {
      const { prop, order } = data;
      const sortKey = {
        stock: 'stock',
        salesVolume: 'sales_volume',
        id: 'id',
        sort: 'sort'
      }[prop];
      const orderKey = {
        ascending: 'asc',
        descending: 'desc'
      }[order];
      this.orderBy = orderKey && sortKey ? `${sortKey} ${orderKey}` : '';
      this.fetchData();
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.pageNo = 1;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.pageNo = val;
      this.fetchData();
    },
    onSearch() {
      const self = this;
      self.pageNo = 1;
      self.fetchData();
    },
    onResetClick() {
      const self = this;
      this.filter = { ...this.initFilter };
      this.pageNo = 1;
      this.pageSize = 10;
      this.orderBy = '';
      this.onlineTime = [];
      this.createDate = [];
      this.$refs['multipleTable'].clearSort();
      self.fetchData();
    },
    handleClickDel() {
      const self = this;
      if (self.multipleSelection.length < 1) {
        self.$message.error('您未选中任何数据');
        return;
      }
      self
        .$confirm('此操作将永久删除该商品, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        .then(() => {
          const ids = [];
          for (const item of self.multipleSelection) {
            ids.push(item.id);
          }
          deleteByIds(ids)
            .then((res) => {
              if (res.code === '0') {
                self.$message({
                  message: '数据删除成功~',
                  type: 'success'
                });
                self.fetchData();
                self.multipleSelection = [];
              }
            })
            .finally(() => {});
        })
        .catch(() => {});
    },
    // 点击定时上下架
    upOrDown(type, callback = () => {}) {
      const self = this;
      if (self.multipleSelection.length < 1) {
        self.$message.error('您未选中任何数据哦~');
        return;
      }
      // 清一下前面选择的日期，避免后面日期选择器的change事件不会触发
      this.downTimeVal = '';
      this.upTimeVal = '';

      callback();
      // const ref = type === 'up' ? 'upTime' : 'downTime';
      // self.$refs[ref].focus();
    },
    // 定时上下架
    changeUpOrDownTime(type, timeVal) {
      const self = this;
      let msg = '上架';
      // let timeVal = self.upTimeVal;
      let interfaceName = autoOnShelve;
      if (type !== 'up') {
        msg = '下架';
        // timeVal = self.downTimeVal;
        interfaceName = autoOffShelve;
      }
      if (timeVal.getTime() < Date.now()) {
        self
          .$confirm(`您选择的时间小于当前时间，请您重新选择！！！`, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'error'
          })
          .then(() => {})
          .catch(() => {});
        return;
      }
      self
        .$confirm(`您确定要将选中的商品定时${msg}?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        .then(() => {
          const ids = [];
          for (const item of self.multipleSelection) {
            ids.push(item.id);
          }
          const postData = {
            autoShelveDate: timeVal,
            ids
          };
          interfaceName(postData).then((e) => {
            if (e.code === '0') {
              self.$message({
                message: '操作成功',
                type: 'success'
              });
              this.fetchData();
            } else {
              self.$confirm(e.msg, '提示', {
                confirmButtonText: '确定',
                type: 'warning'
              });
            }
          });
        })
        .catch(() => {});
    },
    // 取消定时上架（下架）
    cancelAutoShelve(row) {
      const self = this;
      let msg = '上架';
      let interfaceName = cancelAutoOnShelve;
      if (row.autoShelveType === 'OFF') {
        msg = '下架';
        interfaceName = cancelAutoOffShelve;
      }
      self
        .$confirm(`您确定要将商品${row.name}取消定时${msg}吗?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        .then(() => {
          interfaceName(row.id).then((e) => {
            if (e.code === '0') {
              self.$message({
                message: '操作成功',
                type: 'success'
              });
              this.fetchData();
            } else {
              self.$confirm(e.msg, '提示', {
                confirmButtonText: '确定',
                type: 'warning'
              });
            }
          });
        })
        .catch(() => {});
    },
    // 商品取消隐藏
    cancelHide(row) {
      const self = this;
      self
        .$confirm(`您确定要将商品${name}取消隐藏吗?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        .then(() => {
          cancelHiddenCommodity(row.id).then((e) => {
            if (e.code === '0') {
              self.$message({
                message: '操作成功',
                type: 'success'
              });
              self.fetchData();
            } else {
              self.$confirm(e.msg, '提示', {
                confirmButtonText: '确定',
                type: 'warning'
              });
            }
          });
        })
        .catch(() => {});
    },
    changeStatus(type) {
      const self = this;
      if (self.multipleSelection.length < 1) {
        self.$message.error('您未选中任何数据哦~');
        return;
      }

      const operations = {
        up: { label: '上架', handle: updateSaleStatus },
        down: { label: '下架', handle: updateSaleStatus },
        wait: { label: '待上架', handle: updateSaleStatus },
        hide: {
          label: '隐藏',
          handle: (type, ids) => batchHiddenCommodity(ids)
        },
        show: {
          label: '取消隐藏',
          handle: (type, ids) => batchCancelHiddenCommodity(ids)
        }
      };
      const { label, handle } = operations[type];

      self
        .$confirm(`此操作将导致选中的全部商品${label}, 是否继续?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        .then(() => {
          const ids = [];
          for (const item of self.multipleSelection) {
            ids.push(item.id);
          }
          handle(type, ids)
            .then((e) => {
              if (e.code === '0') {
                self.$message({
                  message: '操作成功',
                  type: 'success'
                });
                this.fetchData();
              } else {
                self.$confirm(e.msg, '提示', {
                  confirmButtonText: '确定',
                  type: 'warning'
                });
              }
            })
            .catch((e) => {
              // code为720的时候是部分商品不能上架，但是部分商品可以上架成功，需要重新刷新页面更新状态
              if (e && e.data && e.data.code && e.data.code === '720') {
                this.fetchData();
              }
            });
        })
        .catch(() => {});
    },
    // 获取所有品牌
    fetchBrand() {
      listAllBrandName().then((rs) => {
        const res = rs.data.map((item) => ({
          value: item.id,
          label: item.name
        }));
        this.brandList = res;
      });
    },
    updatePrice(data) {
      this.fetchData();
    }
  }
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
@import './styles';
.el-table {
  width: 99.9% !important;
  max-width: 99.9% !important;
}
.list-note {
  width: 250px;
  letter-spacing: 1px;
}
.btn-linebox {
  margin-bottom: 10px;
  &__add {
    margin-right: 10px;
  }
}
</style>
