<template>
  <div><sy-normal-table v-bind="table" ref="table" /></div>
</template>
<script>
import dict from '@/components/Common/dicts';
import { parseTime } from '@/utils';
import { distributorYearlyPolicyList } from '@/api/yearly-policy-management';

export default {
  name: 'SelectYearly',
  props: {
    scope: {
      type: Object,
      default() {
        return {};
      }
    }
  },
  computed: {
    table() {
      const that = this;
      return {
        filters() {
          return [
            {
              tag: 'el-input',
              label: '分销商ID',
              prop: 'distributorIds',
              bind: {
                placeholder: '请输入分销商ID'
              }
            },
            {
              tag: 'el-input',
              label: '分销商名称',
              prop: 'shopName',
              bind: {
                placeholder: '请输入分销商名称'
              }
            },
            {
              tag: 'sy-select',
              prop: 'csIds',
              label: '专属顾问名称',
              bind: {
                filterable: true,
                multiple: true,
                placeholder: '可多选',
                options: async () => await dict('COMMON_EXPAND_ADVISER'),
                flashOptions: true
              }
            },
            {
              tag: 'el-input',
              label: '年框政策编号',
              prop: 'policyCode',
              bind: {
                placeholder: '请输入'
              }
            }
          ];
        },
        columns() {
          return [
            {
              label: '年框政策编号',
              prop: 'policyCode',
              width: 180
            },
            {
              label: '分销商信息',
              type: 'render',
              render(h, { row }) {
                return (
                  <ul>
                    <li>分销商ID：{row.distributorId} </li>
                    <li>店铺名称：{row.shopName}</li>
                    <li>登录手机号：{row.loginMobile}</li>
                    <li>专属顾问：{row.csName}</li>
                  </ul>
                );
              }
            },
            {
              label: '关联年框合同信息',
              itemBind: {
                minWidth: 130
              },
              render: (h, { row }) => (
                <div v-frag>
                  {(row.contractInfos.length > 0 &&
                    row.contractInfos.map(({ contractId, contractName }, index) => [
                      <p key={`${index}-1`}>{index > 0 ? `合同${index + 1}：` : ''}</p>,
                      <p key={`${index}-2`}>合同ID：{contractId}</p>,
                      <p key={`${index}-3`}>飞书合同编号：{contractId || '-'}</p>,
                      <p key={`${index}-4`}>合同名称：{contractName}</p>,
                      <p key={`${index}-5`}>甲方：{row.ownCompanyName || '-'}</p>,
                      <p key={`${index}-6`}>乙方：{row.companyName || '-'}</p>
                    ])) ||
                    '-'}
                </div>
              )
            },
            {
              label: '年框品牌',
              prop: 'brandName'
            },
            {
              label: '政策状态',
              prop: 'statusName'
            },
            {
              label: '生效时间',
              width: 125,
              render: (h, { row }) => (
                <div>
                  {parseTime(row.startDate, '{y}/{m}/{d}')}-{parseTime(row.endDate, '{y}/{m}/{d}')}
                </div>
              )
            },
            {
              label: '对应渠道主数据',
              render: (h, { row }) => (
                <div v-frag>
                  <p>
                    SPA：{row.sapChannelCode}-{row.sapChannelCodeName}
                  </p>
                  <p>
                    OMS：{row.omsChannelId}-{row.omsChannelName}
                  </p>
                </div>
              )
            },
            {
              label: '操作',
              type: 'btns',
              btns({ row }) {
                return [
                  {
                    text: '选择',
                    type: 'text',
                    call() {
                      that.$emit('onChecked', row);
                    }
                  }
                ];
              }
            }
          ];
        },
        async search({ filtersValue, pageFilter }) {
          const { pageNo, pageSize } = pageFilter;
          const { distributorIds = '' } = filtersValue;
          const data = { ...filtersValue, policyPlatformOwners: ['SYZG'] };
          if (distributorIds) {
            data.distributorIds = [distributorIds];
          }
          const res = await distributorYearlyPolicyList({
            data,
            pageNo,
            pageSize
          });
          const list = res.data?.list || [];
          const total = res.data?.total || 0;
          return {
            list,
            total
          };
        }
      };
    }
  },
  watch: {
    scope: {
      handler() {
        this.refresh();
      },
      immediate: true
    }
  },
  methods: {
    refresh() {
      const ref = this.$refs.table;
      ref && ref.handlerSearch();
    }
  }
};
</script>
