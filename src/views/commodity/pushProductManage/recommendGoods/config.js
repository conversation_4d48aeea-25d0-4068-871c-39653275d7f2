// 推荐商品配置
import { getDictionary } from '@/api/common/dictionary.js';

export default {
  Form: {
    items: [
      {
        key: 'id',
        type: 'input',
        label: '商品ID',
        placeholder: '请输入'
      },
      {
        key: 'name',
        type: 'input',
        label: '商品名称',
        placeholder: '请输入'
      },
      {
        key: 'skuId',
        type: 'input',
        label: '规格标识',
        placeholder: '请输入'
      },
      {
        key: 'specCode',
        type: 'input',
        label: '规格编码',
        placeholder: '请输入'
      },
      {
        key: 'brandIds',
        type: 'select',
        multiple: true,
        selectOptions: getDictionary.bind(null, 'BRAND_PREFER'),
        label: '品牌'
      },
      {
        key: 'commodityLabelIds',
        type: 'select',
        multiple: true,
        selectOptions: getDictionary.bind(null, 'COMMODITY_LABEL'),
        label: '商品标签'
      },
      {
        key: 'isHidden',
        type: 'select',
        selectOptions: getDictionary.bind(null, 'ISFLAG'),
        label: '是否为隐藏商品'
      },
      {
        key: 'creditBack',
        type: 'select',
        selectOptions: getDictionary.bind(null, 'ISFLAG'),
        label: '是否支持返利'
      },
      {
        key: 'purchaseType',
        type: 'select',
        selectOptions: getDictionary.bind(null, 'DISTRIBUTOR_PURCHASE_TYPE'),
        label: '采货类型'
      },
      {
        key: 'categoryIds',
        type: 'select',
        multiple: true,
        selectOptions: getDictionary.bind(null, 'COMMODITY_CATEGORY'),
        label: '商品分组'
      },
    ],
    template: {},
    interface: () => {}
  },
  Table: {
    isSelect: true,
    items: [
      {
        key: 'name',
        label: '商品名称'
      },
      {
        key: 'skuDetail',
        isSlot: true,
        label: 'SKU信息'
      },
      {
        key: 'price',
        label: '价格'
      },
      {
        key: 'commodityId',
        label: '商品ID'
      },
      {
        key: 'stock',
        label: '库存'
      },
      {
        key: 'restrictionNum',
        label: '限购',
        handle(v) {
          return v === 0 ? '不限购' : v;
        }
      },
      {
        key: 'miniOrderQuantity',
        label: '起订'
      },
      {
        key: 'brandName',
        label: '品牌'
      },
      {
        key: 'purchaseType',
        label: '采货类型',
        handle(v) {
          const types = {
            PURCHASE: '采销',
            DROP_SHIPPING: '一件代发'
          };
          return types[v];
        }
      },
      {
        key: 'creditBack',
        label: '是否支持返利',
        handle(v) {
          return v === '0' ? '否' : '是';
        }
      },
      {
        key: 'isHidden',
        label: '是否隐藏',
        handle(v) {
          return v === '0' ? '否' : '是';
        }
      },
      {
        key: 'commodityCategoryNames',
        label: '商品分组',
        handle(v) {
          Array.isArray(v) || (v = []);
          const str = v.reduce((cur, pre) => {
            return (cur += ',' + pre);
          }, '');
          return str.slice(1);
        }
      },
      {
        key: 'commodityLabelNames',
        label: '商品标签',
        handle(v) {
          Array.isArray(v) || (v = []);
          const str = v.reduce((cur, pre) => {
            return (cur += ',' + pre);
          }, '');
          return str.slice(1);
        }
      }
    ],
    operates: [
      {
        label: '添加',
        click(row) {
          const multipleSelection = this.$parent.selectData;
          if (!multipleSelection.find(i => i.id === row.id)) {
            this.handleSelectionChange([...this.multipleSelection, row])
          }
          const selectTableRow = this.$parent;
          selectTableRow.$emit(
            'onCommit',
            selectTableRow.isMultiple
              ? [...selectTableRow.selectData]
              : [row]
          );
        }
      }
    ]
  },
  Pagination: {}
};
