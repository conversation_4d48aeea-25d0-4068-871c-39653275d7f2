<template>
  <operate :type="operateType" :params="$route.query" :options="options"></operate>
</template>

<script>
import operate from './operate.vue';
import cloneDeep from 'lodash/cloneDeep'; // 对象深拷贝
import * as options from './config';

/* 推品清单创建 */
export default {
  name: 'copy',
  components: { operate },
  data() {
    return {
      operateType: 'copy',
      options: {}
    };
  },
  computed: {
    isYearly() {
      return this.$route.query.type === 'YEARLY';
    }
  },
  created() {
    const operate = this.isYearly ? options.yearlyOperate : options.operate;
    this.options = cloneDeep(operate);
    this.options.Form.operateType = 'copy';
  }
};
</script>

<style></style>
