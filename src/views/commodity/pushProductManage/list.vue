<template>
  <div class="page-container">
    <sy-tabs v-model="tabType" v-bind="tabs" class="custom-border-tabs" @change="tabChange" />
    <sy-normal-table v-el-horizontal-scroll v-bind="table" ref="table" />
  </div>
</template>

<script>
import { pushProductManageList, pushProductManageDisable, pushProductManageEnable, pushProductManageDelete, pushProductManagePublic, pushProductManageToPublic, pushProductManageShow, pushProductManageHide } from '@/api/commodity/pushProduct.js';
import dict from '@/components/Common/dicts';
import { checkGroup } from '@/components/Common/SelectGroup';
export default {
  name: 'commodity-pushProductManage-list',
  data() {
    return {
      tabType: 'COMMON',
      tabs: {
        options: [
          {
            label: '公共推品清单',
            value: 'COMMON'
          },
          {
            label: '个人推品清单',
            value: 'PRIVATE'
          },
          {
            label: '年框推品清单',
            value: 'YEARLY'
          }
        ]
      }
    };
  },
  computed: {
    table() {
      const that = this;
      return {
        initSearch: false,
        filters() {
          return [
            {
              tag: 'el-input',
              prop: 'name',
              label: '清单名称',
              bind: {
                placeholder: '请输入清单名称'
              }
            },
            {
              tag: 'sy-date-picker',
              prop: ['updateBeginDate', 'updateEndDate'],
              label: '更新时间',
              bind: {
                bind: {
                  'start-placeholder': '开始日期',
                  'end-placeholder': '结束日期',
                  type: 'daterange',
                  valueFormat: that.$utils.FORMAT.DATE_TIME,
                  defaultTime: ['00:00:00', '23:59:59']
                }
              }
            },
            {
              hide: that.tabType !== 'COMMON',
              tag: 'sy-select',
              prop: 'userIds',
              label: '创建人',
              bind: {
                filterable: true,
                multiple: true,
                flashOptions: true,
                placeholder: '请选择',
                options: async () => dict('COMMON_STAFF_LIST')
              }
            },
            {
              hide: checkGroup(),
              prop: 'groupId',
              tag: 'sy-select',
              label: '所属团队',
              bind: {
                placeholder: '请选择所属团队',
                flashOptions: true,
                options: async () => dict('DISTRIBUTOR_TEAM_DS')
              }
            }
          ];
        },
        btns: [
          {
            text: '创建推品清单',
            type: 'primary',
            code: '/commodity/pushProductManage/list:add',
            call: () => that.createPushList()
          }
        ],
        columns() {
          return [
            {
              label: '推品清单编号',
              prop: '_taskReleaseNumber',
              width: 100
            },
            {
              label: '创建人',
              prop: 'createByName',
              width: 80
            },
            {
              label: '所属团队',
              prop: 'groupId',
              type: 'map',
              map: [
                { label: '水羊直供', value: '1' },
                { label: '水羊国际', value: '3' }
              ]
            },
            {
              prop: 'name',
              label: '清单名称',
              multiLine: 3,
              itemBind: {
                minWidth: 120
              }
            },
            {
              prop: 'description',
              label: '描述',
              multiLine: 3,
              itemBind: {
                minWidth: 120
              }
            },
            {
              prop: 'isHasHiddenCommodity',
              label: '是否包含隐藏商品',
              type: 'map',
              map: [
                { label: '否', value: '0' },
                { label: '是', value: '1' }
              ]
            },
            {
              prop: 'totalCommodityNum',
              label: '商品种类'
            },
            {
              prop: 'totalCommodityQuantity',
              label: '商品件数'
            },
            {
              prop: 'totalAmount',
              label: '商品总金额'
            },
            {
              prop: 'pushType',
              label: '推送用户',
              type: 'map',
              width: 100,
              map: [
                { label: '全部分销商', value: 'ALL' },
                { label: '部分分销商', value: 'PART' }
              ]
            },
            {
              prop: 'updateDate',
              label: '更新时间',
              type: 'time',
              width: 90,
              format: 'yyyy-MM-dd hh:mm:ss'
            },
            {
              prop: 'isPush',
              label: '是否已推送',
              type: 'map',
              map: [
                { label: '未推送', value: '0' },
                { label: '已推送', value: '1' }
              ]
            },
            {
              prop: 'isEnable',
              label: '是否启用',
              type: 'render',
              render: (h, { row }) => {
                return <el-switch value={row.isEnable} inactive-value="0" active-value="1" onChange={() => that.enableTask(row)} />;
              }
            },
            {
              prop: 'izHide',
              label: '是否隐藏',
              type: 'render',
              render: (h, { row }) => {
                return <el-switch value={row.izHide} inactive-value="0" active-value="1" onChange={() => that.hideleTask(row)} />;
              }
            },
            {
              label: '操作',
              type: 'btns',
              width: 180,
              itemBind: {
                fixed: 'right'
              },
              btns({ row }) {
                return [
                  {
                    text: '查看',
                    type: 'text',
                    code: '/commodity/pushProductManage/list:view',
                    call: () => that.viewPushList(row)
                  },
                  {
                    text: '编辑',
                    type: 'text',
                    code: '/commodity/pushProductManage/list:edit',
                    call: () => that.editPushList(row)
                  },
                  {
                    text: '复制',
                    type: 'text',
                    code: '/commodity/pushProductManage/list:copy',
                    call: () => that.copyPushList(row)
                  },
                  {
                    text: '删除',
                    type: 'text',
                    code: '/commodity/pushProductManage/list:edit',
                    confirm: '确定删除吗？',
                    hide: !(row.isPush === '0' && row.isEnable === '0'),
                    call: () => that.deletePushList(row)
                  },
                  {
                    text: '推广',
                    type: 'text',
                    call: () => that.promotePushList(row)
                  },
                  {
                    text: '转为公共',
                    type: 'text',
                    code: '/commodity/pushProductManage/list:edit',
                    hide: row.type !== 'PRIVATE',
                    confirm: '确认转为公共吗?',
                    call: () => that.privateToPublic(row)
                  },
                  {
                    text: '推送',
                    type: 'text',
                    hide: !(row.isPush === '0' && row.isEnable === '1'),
                    confirm: '确认推送吗?',
                    call: () => that.movePushList(row)
                  }
                ];
              }
            }
          ];
        },
        async search({ filtersValue, pageFilter }) {
          // 如果不是公共推品清单，需要删除创建人条件
          const type = that.tabType;
          if (type !== 'COMMON') {
            delete filtersValue.userIds;
          }
          const res = await pushProductManageList({
            data: {
              ...filtersValue,
              type
            },
            ...pageFilter
          });
          const { list, total = 0 } = res.data;
          return {
            list: list.map((item, index) => ({
              ...item,
              _taskReleaseNumber: total - pageFilter.pageSize * (pageFilter.pageNo - 1) - index
            })),
            total
          };
        }
      };
    }
  },
  activated() {
    this.refreshTable();
  },
  methods: {
    tabChange() {
      const ref = this.$refs.table;
      ref && ref.clearFilters();
    },
    // 刷新表格
    refreshTable() {
      const ref = this.$refs.table;
      ref && ref.handlerSearch();
    },
    // 开启/关闭任务
    enableTask({ id, isEnable }) {
      const flag = isEnable === '1';
      const enableName = flag ? '禁用' : '启用';
      this.$confirm(`确认${enableName}吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          (flag ? pushProductManageDisable : pushProductManageEnable)({
            id
          }).then((res) => {
            if (res.code === '0') {
              this.$message.success(res.msg);
              this.refreshTable();
            }
          });
        })
        .catch(() => {
          this.$message.info('已取消操作');
        });
    },
    // 是否在前端展示推品清单
    hideleTask({ id, izHide, isPush, isEnable }) {
      if (isPush === '0' || isEnable === '0') return this.$message.warning('推品清单已启用及已推送状态下才能操作');
      const flag = izHide === '1';
      const enableName = flag ? '显示' : '隐藏';
      this.$confirm(`确认${enableName}吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          const formData = new FormData();
          formData.append('id', id);
          (flag ? pushProductManageShow : pushProductManageHide)(formData).then((res) => {
            if (res.code === '0') {
              this.$message.success(res.msg);
              this.refreshTable();
            }
          });
        })
        .catch(() => {
          this.$message.info('已取消操作');
        });
    },
    // 创建推品清单
    createPushList() {
      const type = this.tabType;
      const query = { type };
      if (type === 'YEARLY') {
        query.pushType = 'PART';
      }
      this.$router.push({
        path: '/commodity/pushProductManage/add',
        query
      });
    },
    // 编辑推品清单
    editPushList({ id, isPush }) {
      const type = this.tabType;
      this.$router.push({
        path: '/commodity/pushProductManage/edit',
        query: {
          id,
          isPush,
          type
        }
      });
    },
    // 查看推品清单
    viewPushList({ id }) {
      const type = this.tabType;
      this.$router.push({
        path: '/commodity/pushProductManage/detail',
        query: {
          id,
          type
        }
      });
    },
    // 复制推品清单
    copyPushList({ id }) {
      const type = this.tabType;
      this.$router.push({
        path: '/commodity/pushProductManage/copy',
        query: {
          id,
          type
        }
      });
    },
    // 推送
    movePushList({ id }) {
      pushProductManagePublic({ id }).then((res) => {
        if (res.code === '0') {
          this.$message.success(res.msg);
          this.refreshTable();
        }
      });
    },
    // 推广
    promotePushList() {
      this.$message.warning('请前往【企业微信-推品清单】进行使用');
    },
    // 删除推品清单
    deletePushList({ id }) {
      pushProductManageDelete({ id }).then((res) => {
        if (res.code === '0') {
          this.$message.success(res.msg);
          this.refreshTable();
        }
      });
    },
    // 私人转公共
    privateToPublic({ id }) {
      pushProductManageToPublic({ id }).then((res) => {
        if (res.code === '0') {
          this.$message.success(res.msg);
          this.refreshTable();
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep .columns-operation .el-button {
  margin: 4px !important;
}
</style>
