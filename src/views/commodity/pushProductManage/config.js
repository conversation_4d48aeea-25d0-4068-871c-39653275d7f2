import { parseTime, parseEndTime } from '@/utils';
import { getDictionary } from '@/api/common/dictionary.js';
import { pushProductManageCreate, pushProductManageUpdate, pushProductManageDelete, pushProductManageToPublic, pushProductManagePublic } from '@/api/commodity/pushProduct.js';

// 公共退品清单配置项
export const list = {
  Form: {
    items: [
      {
        key: 'name',
        type: 'input',
        label: '清单名称'
      },
      {
        key: 'updateDate',
        type: 'datePicker',
        dateType: 'daterange',
        change(options) {
          if (this.templateData[options.key] && Array.isArray(this.templateData[options.key])) {
            const [v1, v2] = this.templateData[options.key];
            console.log('v2', parseEndTime(v2));
            this.templateData.updateBeginDate = v1.getTime();
            this.templateData.updateEndDate = parseEndTime(v2).getTime();
          } else {
            this.templateData.updateBeginDate = '';
            this.templateData.updateEndDate = '';
          }
        },
        label: '更新时间'
      },
      {
        key: 'userIds',
        type: 'select',
        multiple: true,
        selectOptions: getDictionary.bind(null, 'STAFF'),
        label: '创建人'
      }
    ],
    template: {
      updateBeginDate: '',
      updateEndDate: ''
    },
    submitFormDatas: ['updateBeginDate', 'updateEndDate', 'name', 'userIds'],
    interface: () => {}
  },
  Operate: {
    items: [
      {
        authority: '/commodity/pushProductManage/list:add',
        label: '创建推品清单',
        type: 'Button',
        event_click() {
          this.$router.push({
            path: '/commodity/pushProductManage/add',
            query: {
              type: 'COMMON'
            }
          });
        }
      }
    ]
  },
  Table: {
    items: [
      {
        key: '_taskReleaseNumber',
        label: '推品清单编号'
      },
      {
        key: 'createByName',
        label: '创建人'
      },
      {
        key: 'name',
        label: '清单名称'
      },
      {
        key: 'description',
        label: '描述'
      },
      {
        key: 'isHasHiddenCommodity',
        label: '是否包含隐藏商品',
        handle(v) {
          return v === '0' ? '否' : '是';
        }
      },
      {
        key: 'totalCommodityNum',
        label: '商品种类'
      },
      {
        key: 'totalCommodityQuantity',
        label: '商品件数'
      },
      {
        key: 'totalAmount',
        label: '商品总金额'
      },
      {
        key: 'pushType',
        label: '推送用户',
        handle(v) {
          return v === 'ALL' ? '全部分销商' : '部分分销商';
        }
      },
      {
        key: 'updateDate',
        label: '更新时间',
        handle(v) {
          return parseTime(v, '{y}-{m}-{d} {h}:{i}:{s}');
        }
      },
      {
        key: 'isPush',
        label: '是否已推送',
        handle(v) {
          const vlaues = {
            0: '未推送',
            1: '已推送'
          };
          return vlaues[v];
        }
      },
      {
        key: 'isEnable',
        label: '是否启用',
        isSlot: true
      }
    ],
    operates: [
      {
        authority: '/commodity/pushProductManage/list:edit',
        label: '编辑',
        click(data) {
          this.$router.push({
            path: '/commodity/pushProductManage/edit',
            query: {
              id: data.id,
              isPush: data.isPush
            }
          });
        }
      },
      {
        authority: '/commodity/pushProductManage/list:view',
        label: '查看',
        click(data) {
          this.$router.push({
            path: '/commodity/pushProductManage/detail',
            query: {
              id: data.id
            }
          });
        }
      },
      {
        label: '推广',
        click() {
          this.$message.warning('请前往【企业微信-推品清单】进行使用');
        }
      },
      {
        authority: '/commodity/pushProductManage/list:copy',
        label: '复制',
        click(data) {
          this.$router.push({
            path: '/commodity/pushProductManage/copy',
            query: {
              id: data.id
            }
          });
        }
      },
      {
        label: '推送',
        loadCondition(row) {
          return row.isPush === '0' && row.isEnable === '1';
        },
        click(row) {
          const info = '操作';
          this.$confirm(`确认${info}吗?`, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
            .then(() => {
              pushProductManagePublic({ id: row.id })
                .then((res) => {
                  this.$message({
                    type: 'success',
                    message: `${info}成功！`
                  });
                })
                .catch(() => {
                  this.$message({
                    type: 'warning',
                    message: `操作失败！`
                  });
                })
                .finally(() => {
                  this.$refs.pagination.getResponse();
                });
            })
            .catch(() => {
              this.$message({
                type: 'info',
                message: `已取消${info}`
              });
            });
        }
      },
      {
        authority: '/commodity/pushProductManage/list:edit',
        label: '删除',
        loadCondition(row) {
          return row.isPush === '0' && row.isEnable === '0';
        },
        click(row) {
          this.$confirm('确认删除吗?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
            .then(() => {
              pushProductManageDelete({ id: row.id })
                .then((res) => {
                  this.$message.success(res.msg);
                })
                .finally(() => {
                  this.$refs.pagination.getResponse();
                });
            })
            .catch(() => {
              this.$message({
                type: 'info',
                message: '已取消删除'
              });
            });
        }
      }
    ]
  },
  Pagination: {}
};

// 私人退品清单配置项
export const privateList = {
  Form: {
    items: [
      {
        key: 'name',
        type: 'input',
        label: '清单名称'
      },
      {
        key: 'updateDate',
        type: 'datePicker',
        dateType: 'daterange',
        change(options) {
          if (this.templateData[options.key] && Array.isArray(this.templateData[options.key])) {
            const [v1, v2] = this.templateData[options.key];
            this.templateData.updateBeginDate = v1.getTime();
            this.templateData.updateEndDate = parseEndTime(v2).getTime();
          } else {
            this.templateData.updateBeginDate = '';
            this.templateData.updateEndDate = '';
          }
        },
        label: '更新时间'
      },
      {
        key: 'userIds',
        type: 'select',
        multiple: true,
        selectOptions: getDictionary.bind(null, 'STAFF'),
        label: '创建人'
      }
    ],
    template: {
      updateBeginDate: '',
      updateEndDate: ''
    },
    submitFormDatas: ['updateBeginDate', 'updateEndDate', 'name', 'userIds'],
    interface: () => {}
  },
  Operate: {
    items: [
      {
        authority: '/commodity/pushProductManage/list:add',
        label: '创建推品清单',
        type: 'Button',
        event_click() {
          this.$router.push({
            path: '/commodity/pushProductManage/add',
            query: {
              type: 'PRIVATE'
            }
          });
        }
      }
    ]
  },
  Table: {
    items: [
      {
        key: '_taskReleaseNumber',
        label: '推品清单编号'
      },
      {
        key: 'createByName',
        label: '创建人'
      },
      {
        key: 'name',
        label: '清单名称'
      },
      {
        key: 'description',
        label: '描述'
      },
      {
        key: 'isHasHiddenCommodity',
        label: '是否包含隐藏商品',
        handle(v) {
          return v === '0' ? '否' : '是';
        }
      },
      {
        key: 'totalCommodityNum',
        label: '商品种类'
      },
      {
        key: 'totalCommodityQuantity',
        label: '商品件数'
      },
      {
        key: 'totalAmount',
        label: '商品总金额'
      },
      {
        key: 'pushType',
        label: '推送用户',
        handle(v) {
          return v === 'ALL' ? '全部分销商' : '部分分销商';
        }
      },
      {
        key: 'updateDate',
        label: '更新时间',
        handle(v) {
          return parseTime(v, '{y}-{m}-{d} {h}:{i}:{s}');
        }
      },
      {
        key: 'isPush',
        label: '是否已推送',
        handle(v) {
          const vlaues = {
            0: '未推送',
            1: '已推送'
          };
          return vlaues[v];
        }
      },
      {
        key: 'isEnable',
        label: '是否启用',
        isSlot: true
      }
    ],
    operates: [
      {
        authority: '/commodity/pushProductManage/list:edit',
        label: '编辑',
        click(data) {
          this.$router.push({
            path: '/commodity/pushProductManage/edit',
            query: {
              id: data.id,
              isPush: data.isPush
            }
          });
        }
      },
      {
        authority: '/commodity/pushProductManage/list:view',
        label: '查看',
        click(data) {
          this.$router.push({
            path: '/commodity/pushProductManage/detail',
            query: {
              id: data.id
            }
          });
        }
      },
      {
        label: '推广',
        click() {
          this.$message.warning('请前往【企业微信-推品清单】进行使用');
        }
      },
      {
        authority: '/commodity/pushProductManage/list:copy',
        label: '复制',
        click(data) {
          this.$router.push({
            path: '/commodity/pushProductManage/copy',
            query: {
              id: data.id
            }
          });
        }
      },
      {
        label: '推送',
        loadCondition(row) {
          return row.isPush === '0' && row.isEnable === '1';
        },
        click(row) {
          const info = '操作';
          this.$confirm(`确认${info}吗?`, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
            .then(() => {
              pushProductManagePublic({ id: row.id })
                .then((res) => {
                  this.$message({
                    type: 'success',
                    message: `${info}成功！`
                  });
                })
                .catch(() => {
                  this.$message({
                    type: 'warning',
                    message: `操作失败！`
                  });
                })
                .finally(() => {
                  this.$refs.pagination.getResponse();
                });
            })
            .catch(() => {
              this.$message({
                type: 'info',
                message: `已取消${info}`
              });
            });
        }
      },
      {
        authority: '/commodity/pushProductManage/list:edit',
        label: '转为公共',
        click(row) {
          const info = '操作';
          this.$confirm(`确认${info}吗?`, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
            .then(() => {
              pushProductManageToPublic({ id: row.id })
                .then((res) => {
                  this.$message({
                    type: 'success',
                    message: `${info}成功！`
                  });
                })
                .catch(() => {
                  this.$message({
                    type: 'warning',
                    message: `操作失败！`
                  });
                })
                .finally(() => {
                  this.$refs.pagination.getResponse();
                });
            })
            .catch(() => {
              this.$message({
                type: 'info',
                message: `已取消${info}`
              });
            });
        }
      },
      {
        authority: '/commodity/pushProductManage/list:edit',
        label: '删除',
        loadCondition(row) {
          return row.isPush === '0' && row.isEnable === '0';
        },
        click(row) {
          this.$confirm('确认删除吗?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
            .then(() => {
              pushProductManageDelete({ id: row.id })
                .then((res) => {
                  this.$message.success(res.msg);
                })
                .finally(() => {
                  this.$refs.pagination.getResponse();
                });
            })
            .catch(() => {
              this.$message({
                type: 'info',
                message: '已取消删除'
              });
            });
        }
      }
    ],
    operateWidth: 80
  },
  Pagination: {}
};

const operateApis = {
  copy: pushProductManageCreate,
  add: pushProductManageCreate,
  edit_0: pushProductManageUpdate,
  edit_1: pushProductManageUpdate
};
// 操作清单配置（创建，查看，编辑，复制）
export const operate = {
  Form: {
    operateType: 'add',
    items: [
      {
        key: 'name',
        type: 'input',
        style: {
          width: '200px'
        },
        maxlength: 15,
        placeholder: '15个字以内',
        label: '清单名称'
      },
      {
        key: 'description',
        type: 'input',
        style: {
          width: '200px'
        },
        inputType: 'textarea',
        maxlength: 50,
        rows: 3,
        placeholder: '50个字以内',
        label: '清单描述'
      },
      {
        key: 'pushType',
        style: {
          width: '200px'
        },
        type: 'select',
        disabled: false,
        observers: ['distributorIds'],
        selectOptions: getDictionary.bind(null, 'PUSHPRODUCT_DISTRIBUTOR_TYPE'),
        change() {},
        label: '分销商'
      },
      {
        key: 'distributorIds',
        type: 'table',
        'max-height': 300,
        isLoadLabel: false,
        visible: false,
        change({ key }) {
          this.$refs.form.validateField(key);
        },
        observerListener(options) {
          options.visible = this.templateData.pushType === 'PART';

          this.options.rules[options.key][0].required = options.visible;
        },
        Operate: {
          items: [
            {
              type: 'Text',
              label: {},
              handle(options) {
                this.$nextTick(() => {
                  options.label = this.rootComponent.$refs.form.templateData;
                });
                return `已选${options.label.distributorIds ? options.label.distributorIds.length : 0}条记录`;
              }
            },
            {
              label: '选择分销商',
              event_click() {
                // this.rootComponent 当前页面组件对象
                this.rootComponent.dialogType = 'distributor';
                this.rootComponent.options.Dialog.title = '选择分销商';
                this.rootComponent.options.Dialog.width = '75%';
                this.rootComponent.$refs.dialog.setVisible(true, {
                  multipleSelection: this.rootComponent.$refs.form.templateData.distributorIds
                });
              },
              type: 'Button'
            },
            {
              label: '删除全部已选',
              event_click() {
                // this.rootComponent 当前页面组件pushProductManageOperate对象
                this.rootComponent.$refs.form.templateData.distributorIds = [];
              },
              type: 'Button'
            }
          ]
        },
        Table: {
          items: [
            {
              key: 'shopName',
              label: '分销商名称'
            },
            {
              key: 'applyMobile',
              label: '登录手机号'
            },
            {
              key: 'customerServiceVO',
              label: '专属顾问',
              handle(v = {}) {
                return v.name || '-';
              }
            }
          ],
          operates: [
            {
              label: '删除',
              loadCondition() {
                return !['detail', 'edit_1'].includes(this.formComponent.options.operateType);
              },
              click(row) {
                const distributorIds = this.formComponent.templateData.distributorIds;
                const index = distributorIds.findIndex((i) => i.id === row.id);
                if (index !== -1) {
                  distributorIds.splice(index, 1);
                }
              }
            }
          ]
        },
        Pagination: {}
      },
      {
        key: 'commodityList',
        type: 'slot',
        isLoadLabel: false,
        'max-height': 300,
        change({ key }) {
          this.$refs.form.validateField(key);
        },
        Operate: {
          items: [
            {
              type: 'Text',
              label: {},
              handle(options) {
                this.$nextTick(() => {
                  options.label = this.rootComponent.$refs.form.templateData;
                });
                return `已选${options.label.commodityList ? options.label.commodityList.length : 0}条记录`;
              }
            },
            {
              label: '添加推荐商品',
              event_click() {
                // this.rootComponent 当前页面组件pushProductManageOperate对象
                this.rootComponent.dialogType = 'commodity';
                this.rootComponent.options.Dialog.title = '选择推荐商品';
                this.rootComponent.options.Dialog.width = '75%';
                this.rootComponent.$refs.dialog.setVisible(true, {
                  multipleSelection: this.rootComponent.$refs.form.templateData.commodityList
                });
              },
              type: 'Button'
            },
            {
              label: '删除全部已选',
              event_click() {
                // this.rootComponent 当前页面组件pushProductManageOperate对象
                this.rootComponent.$refs.form.templateData.commodityList = [];

                this.rootComponent.handleDistributorId();
              },
              type: 'Button'
            }
          ]
        },
        Table: {
          items: [
            {
              key: 'name',
              label: '商品名称'
            },
            {
              key: 'skuDetail',
              isSlot: true,
              label: 'SKU信息'
            },
            {
              key: 'price',
              label: '价格'
            },
            {
              key: 'quantity',
              isSlot: true,
              width: 150,
              label: '件数'
            },
            {
              key: 'stock',
              label: '库存'
            },
            {
              key: 'restrictionNum',
              label: '限购',
              handle(v) {
                return !v || v === 0 ? '不限购' : v;
              }
            },
            {
              key: 'miniOrderQuantity',
              label: '起订'
            },
            {
              key: 'brandName',
              label: '品牌'
            },
            {
              key: 'purchaseType',
              label: '采货类型',
              handle(v) {
                const types = {
                  PURCHASE: '采销',
                  DROP_SHIPPING: '一件代发'
                };
                return types[v];
              }
            },
            {
              key: 'creditBack',
              label: '是否支持返利',
              handle(v) {
                return v === '0' ? '否' : '是';
              }
            },
            {
              key: 'isHidden',
              label: '是否隐藏',
              handle(v) {
                return v === '0' ? '否' : '是';
              }
            },
            {
              key: 'commodityLabelNames',
              label: '商品标签',
              handle(v) {
                Array.isArray(v) || (v = []);
                const str = v.reduce((cur, pre) => {
                  return (cur += ',' + pre);
                }, '');
                return str.slice(1);
              }
            }
          ],
          operates: [
            {
              label: '删除',
              loadCondition() {
                return !['detail'].includes(this.formComponent.options.operateType);
              },
              click(row) {
                const commodityList = this.formComponent.templateData.commodityList;
                const index = commodityList.findIndex((i) => i.id === row.id);
                if (index !== -1) {
                  commodityList.splice(index, 1);
                }

                this.formComponent.$parent.handleDistributorId();
              }
            }
          ]
        },
        Pagination: {}
      },
      {
        key: 'pushTypeTip',
        isLoadLabel: false,
        type: 'slot'
      },
    ],
    template: {
      id: '',
      groupId: '',
      type: '',
      commodityList: [],
      distributorIds: []
    },
    rules: {
      name: [{ required: true, message: '请输入清单名称', trigger: 'change' }],
      commodityList: [
        {
          required: true,
          message: '请选择推荐商品',
          trigger: 'change'
        }
      ],
      pushType: [{ required: true, message: '请选择分销商范围', trigger: 'change' }],
      distributorIds: [
        {
          required: false,
          message: '指定用户范围请选择分销商',
          trigger: 'change'
        }
      ]
    },
    // submitFormDatas: ['type', 'pushType', 'id', 'description', 'createRecommendPlanDetailCmds','updateRecommendPlanDetailCmds', 'commodityList'],
    interface(formData) {
      return new Promise((res, req) => {
        operateApis[this.options.operateType]({
          ...formData,
          [this.options.operateType.indexOf('edit') !== -1 ? 'updateRecommendPlanDetailCmds' : 'createRecommendPlanDetailCmds']: formData.commodityList.map(({ id, quantity, commodityId }) => ({
            commodityId,
            quantity,
            skuId: id
          })),
          distributorIds: formData.distributorIds.map((i) => i.id)
        })
          .then((res2) => {
            res({ success: true });

            if (res2.success) {
              this.$back({
                path: '/commodity/pushProductManage/list'
              });
            }
          })
          .catch(() => {
            req();
          });
      });
    },
    onCancel() {}
  },
  Dialog: {
    title: '选择推荐商品',
    width: '75%'
  }
};
// 年框
export const yearlyOperate = {
  Form: {
    operateType: 'add',
    items: [
      {
        key: 'name',
        type: 'input',
        style: {
          width: '200px'
        },
        maxlength: 15,
        placeholder: '15个字以内',
        label: '清单名称'
      },
      {
        key: 'description',
        type: 'input',
        style: {
          width: '200px'
        },
        inputType: 'textarea',
        maxlength: 50,
        rows: 3,
        placeholder: '50个字以内',
        label: '清单描述'
      },
      {
        key: 'pushType',
        style: {
          width: '200px'
        },
        type: 'select',
        disabled: true,
        observers: ['distributorIds'],
        selectOptions: getDictionary.bind(null, 'PUSHPRODUCT_DISTRIBUTOR_TYPE'),
        change() {},
        label: '分销商'
      },
      {
        key: 'distributorIds',
        type: 'table',
        'max-height': 300,
        isLoadLabel: false,
        visible: false,
        change({ key }) {
          this.$refs.form.validateField(key);
        },
        observerListener(options) {
          options.visible = this.templateData.pushType === 'PART';

          this.options.rules[options.key][0].required = options.visible;
        },
        Operate: {
          items: [
            {
              type: 'Text',
              label: {},
              handle(options) {
                this.$nextTick(() => {
                  options.label = this.rootComponent.$refs.form.templateData;
                });
                return `已选${options.label.distributorIds ? options.label.distributorIds.length : 0}条记录`;
              }
            },
            {
              label: '选择分销商',
              event_click() {
                // this.rootComponent 当前页面组件对象
                this.rootComponent.dialogType = 'distributor';
                this.rootComponent.options.Dialog.title = '选择分销商';
                this.rootComponent.options.Dialog.width = '75%';
                this.rootComponent.$refs.dialog.setVisible(true, {
                  multipleSelection: this.rootComponent.$refs.form.templateData.distributorIds
                });
              },
              type: 'Button'
            },
            {
              label: '删除全部已选',
              event_click() {
                // this.rootComponent 当前页面组件pushProductManageOperate对象
                this.rootComponent.$refs.form.templateData.distributorIds = [];
              },
              type: 'Button'
            }
          ]
        },
        Table: {
          items: [
            {
              key: 'shopName',
              label: '分销商名称'
            },
            {
              key: 'applyMobile',
              label: '登录手机号'
            },
            {
              key: 'csName',
              label: '专属顾问',
            }
          ],
          operates: [
            {
              label: '删除',
              loadCondition() {
                return !['detail', 'edit_1'].includes(this.formComponent.options.operateType);
              },
              click(row) {
                const distributorIds = this.formComponent.templateData.distributorIds;
                const index = distributorIds.findIndex((i) => i.id === row.id);
                if (index !== -1) {
                  distributorIds.splice(index, 1);
                  this.formComponent.$parent.yealiyPolicyId = '';
                }
              }
            }
          ]
        },
        Pagination: {}
      },
      {
        key: 'commodityList',
        type: 'slot',
        isLoadLabel: false,
        'max-height': 300,
        change({ key }) {
          this.$refs.form.validateField(key);
        },
        Operate: {
          items: [
            {
              type: 'Text',
              label: {},
              handle(options) {
                this.$nextTick(() => {
                  options.label = this.rootComponent.$refs.form.templateData;
                });
                return `已选${options.label.commodityList ? options.label.commodityList.length : 0}条记录`;
              }
            },
            {
              label: '添加推荐商品',
              event_click() {
                // this.rootComponent 当前页面组件pushProductManageOperate对象
               if(!this.rootComponent.yealiyPolicyId){
                 return this.$message.error('请先选择分销商');
               }
                this.rootComponent.dialogType = 'commodity';
                this.rootComponent.options.Dialog.title = '选择推荐商品';
                this.rootComponent.options.Dialog.width = '75%';
                this.rootComponent.$refs.dialog.setVisible(true, {
                  multipleSelection: this.rootComponent.$refs.form.templateData.commodityList
                });
              },
              type: 'Button'
            },
            {
              label: '删除全部已选',
              event_click() {
                // this.rootComponent 当前页面组件pushProductManageOperate对象
                this.rootComponent.$refs.form.templateData.commodityList = [];

                this.rootComponent.handleDistributorId();
              },
              type: 'Button'
            }
          ]
        },
        Table: {
          items: [
            {
              key: 'name',
              label: '商品名称'
            },
            {
              key: 'skuDetail',
              isSlot: true,
              label: 'SKU信息'
            },
            {
              key: 'price',
              label: '价格'
            },
            {
              key: 'quantity',
              isSlot: true,
              width: 150,
              label: '件数'
            },
            {
              key: 'stock',
              label: '库存'
            },
            {
              key: 'restrictionNum',
              label: '限购',
              handle(v) {
                return !v || v === 0 ? '不限购' : v;
              }
            },
            {
              key: 'miniOrderQuantity',
              label: '起订'
            },
            {
              key: 'brandName',
              label: '品牌'
            },
            {
              key: 'purchaseType',
              label: '采货类型',
              handle(v) {
                const types = {
                  PURCHASE: '采销',
                  DROP_SHIPPING: '一件代发'
                };
                return types[v];
              }
            },
            {
              key: 'creditBack',
              label: '是否支持返利',
              handle(v) {
                return v === '0' ? '否' : '是';
              }
            },
            {
              key: 'isHidden',
              label: '是否隐藏',
              handle(v) {
                return v === '0' ? '否' : '是';
              }
            },
            {
              key: 'commodityLabelNames',
              label: '商品标签',
              handle(v) {
                Array.isArray(v) || (v = []);
                const str = v.reduce((cur, pre) => {
                  return (cur += ',' + pre);
                }, '');
                return str.slice(1);
              }
            }
          ],
          operates: [
            {
              label: '删除',
              loadCondition() {
                return !['detail'].includes(this.formComponent.options.operateType);
              },
              click(row) {
                const commodityList = this.formComponent.templateData.commodityList;
                const index = commodityList.findIndex((i) => i.id === row.id);
                if (index !== -1) {
                  commodityList.splice(index, 1);
                }

                this.formComponent.$parent.handleDistributorId();
              }
            }
          ]
        },
        Pagination: {}
      },
      {
        key: 'pushTypeTip',
        isLoadLabel: false,
        type: 'slot'
      },
    ],
    template: {
      id: '',
      groupId: '',
      type: '',
      commodityList: [],
      distributorIds: [],
      activityId: ''
    },
    rules: {
      name: [{ required: true, message: '请输入清单名称', trigger: 'change' }],
      commodityList: [
        {
          required: true,
          message: '请选择推荐商品',
          trigger: 'change'
        }
      ],
      pushType: [{ required: true, message: '请选择分销商范围', trigger: 'change' }],
      distributorIds: [
        {
          required: false,
          message: '指定用户范围请选择分销商',
          trigger: 'change'
        }
      ]
    },
    // submitFormDatas: ['type', 'pushType', 'id', 'description', 'createRecommendPlanDetailCmds','updateRecommendPlanDetailCmds', 'commodityList'],
    interface(formData) {
      return new Promise((res, req) => {
        operateApis[this.options.operateType]({
          ...formData,
          [this.options.operateType.indexOf('edit') !== -1 ? 'updateRecommendPlanDetailCmds' : 'createRecommendPlanDetailCmds']: formData.commodityList.map(({ id, quantity, commodityId }) => ({
            commodityId,
            quantity,
            skuId: id
          })),
          distributorIds: formData.distributorIds.map((i) => i.id)
        })
          .then((res2) => {
            res({ success: true });

            if (res2.success) {
              this.$back({
                path: '/commodity/pushProductManage/list'
              });
            }
          })
          .catch(() => {
            req();
          });
      });
    },
    onCancel() {}
  },
  Dialog: {
    title: '选择推荐商品',
    width: '75%'
  }
};
