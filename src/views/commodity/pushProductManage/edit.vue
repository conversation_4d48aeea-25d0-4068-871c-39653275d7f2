<template>
  <operate
    :type="operateType"
    :params="$route.query"
    :options="options"
  ></operate>
</template>

<script>
import operate from './operate.vue';
import cloneDeep from 'lodash/cloneDeep'; // 对象深拷贝
import * as options from './config';

/* 推品清单创建 */
export default {
  name: 'edit',
  components: { operate },
  data() {
    return {
      operateType: 'edit',
      options: {}
    };
  },
  computed: {
    isYearly() {
      return this.$route.query.type === 'YEARLY';
    }
  },
  created() {
    this.initOptions();
  },
  methods: {
    initOptions() {
      const isPush = this.$route.query.isPush;

      const operate = this.isYearly ? options.yearlyOperate : options.operate;
      this.options = cloneDeep(operate);
      this.options.Form.operateType = 'edit_' + isPush;

      if (isPush !== '1') return;

      // 已推送 分销商不能修改
      this.options.Form.items.forEach((i) => {
        const { Operate, key } = i;

        const disabled = ['pushType', 'distributorIds'].includes(key);
        i.disabled = disabled

        Operate && Operate.items.forEach((i) => (i.disabled = disabled));
      });
    }
  }
};
</script>

<style></style>
