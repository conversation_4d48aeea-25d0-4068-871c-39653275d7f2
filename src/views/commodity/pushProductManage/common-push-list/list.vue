<template>
  <div class="table">
    <!-- 搜索表单 -->
    <filter-form :options="_filterFormOptions" @query="onSubmit_searchForm" ref="filterForm"></filter-form>

    <!-- 操作栏 -->
    <action-bar size="small" class="action-bar" v-bind="_actionBarOptions" @actionBarClick="({ id }) => this[id]()"> </action-bar>

    <!-- 列表展示 -->
    <list-exhibition ref="listExhibition" class="table" @getData="getData" v-bind="_tableOptions" v-el-horizontal-scroll>
      <!-- 开启/关闭任务 -->
      <template slot="table_cell_enable" slot-scope="{ scope }">
        <div :class="'table_cell_enable ' + (scope.row.isEnable === '1' ? 'active' : '')" @click="enableTask(scope.row)"></div>
      </template>
      <!-- 是否在前端展示推品清单 -->
      <template slot="table_cell_hide" slot-scope="{ scope }">
        <div :class="'table_cell_enable ' + (scope.row.izHide === '1' ? 'active' : '')" @click="hideleTask(scope.row)"></div>
      </template>
      <!-- 操作栏 -->
      <el-table-column label="操作" fixed="right" width="100">
        <template slot-scope="scope">
          <action-bar v-bind="_tableActionOptions" :record="scope.row" @actionBarClick="({ id }, row) => _self[id](row)"></action-bar>
        </template>
      </el-table-column>
    </list-exhibition>
  </div>
</template>

<script>
import FilterForm from '@/components/Form/FilterForm';
import ActionBar from '@/components/Common/ActionBar';
import ListExhibition from '@/components/Common/ListExhibition';
import { FilterFormOptions, ActionBars, Table, TableActionBars } from './config';
import { pushProductManageList, pushProductManageDisable, pushProductManageEnable, pushProductManageDelete, pushProductManagePublic, pushProductManageShow, pushProductManageHide } from '@/api/commodity/pushProduct.js';
import pickBy from 'lodash/pickBy';
import { getGroupLabel } from '@/components/Common/SelectGroup';
import dict from '@/components/Common/dicts';

export default {
  name: 'common-push-list',
  components: { FilterForm, ActionBar, ListExhibition },
  data() {
    return {};
  },
  created() {
    this.init();
  },
  mounted() {
    this.onSubmit_searchForm();
  },
  methods: {
    init() {
      // 筛选条件：创建人根据团队变化
      const userIds = FilterFormOptions.find((i) => i.prop === 'userIds');
      if (userIds) userIds.options = dict(`COMMON_${getGroupLabel()}STAFF_LIST`);

      this._filterFormOptions = FilterFormOptions;
      this._actionBarOptions = ActionBars;
      this._tableOptions = Table;
      this._tableActionOptions = TableActionBars;
    },
    // 列表查询
    onSubmit_searchForm() {
      this.$refs.listExhibition.refreshData();
    },
    // 列表获取数据
    getData({ pageNo, pageSize, callback }) {
      const formData = this.$refs.filterForm.getParams();
      const params = pickBy(formData, (val) => !!val);

      let data = [];
      return pushProductManageList({
        data: {
          ...params,
          type: 'COMMON'
        },
        pageNo,
        pageSize
      })
        .then((res) => {
          data = res.data;
          const { list, total } = data;

          list.forEach((i, index) => {
            // 设置任务编号
            i._taskReleaseNumber = total - pageSize * (pageNo - 1) - index;
          });
        })
        .finally(() => {
          callback(data);
        });
    },
    // 开启/关闭任务
    enableTask({ id, isEnable }) {
      const flag = isEnable === '1';
      const enableName = flag ? '禁用' : '启用';
      this.$confirm(`确认${enableName}吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          (flag ? pushProductManageDisable : pushProductManageEnable)({
            id
          }).then((res) => {
            if (res.code === '0') {
              this.$message.success(res.msg);
              this.onSubmit_searchForm();
            }
          });
        })
        .catch(() => {
          this.$message.info('已取消操作');
        });
    },
    // 是否在前端展示推品清单
    hideleTask({ id, izHide, isPush, isEnable }) {
      if(isPush === '0' || isEnable === '0') return this.$message.warning('推品清单已启用及已推送状态下才能操作');
      const flag = izHide === '1';
      const enableName = flag ? '显示' : '隐藏';
      this.$confirm(`确认${enableName}吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          const formData = new FormData();
          formData.append('id', id);
          (flag ? pushProductManageShow : pushProductManageHide)(formData).then((res) => {
            if (res.code === '0') {
              this.$message.success(res.msg);
              this.onSubmit_searchForm();
            }
          });
        })
        .catch(() => {
          this.$message.info('已取消操作');
        });
    },
    // 创建推品清单
    createPushList() {
      this.$router.push({
        path: '/commodity/pushProductManage/add',
        query: {
          type: 'COMMON'
        }
      });
    },
    // 编辑推品清单
    editPushList({ id, isPush }) {
      this.$router.push({
        path: '/commodity/pushProductManage/edit',
        query: {
          id,
          isPush
        }
      });
    },
    // 查看推品清单
    viewPushList({ id }) {
      this.$router.push({
        path: '/commodity/pushProductManage/detail',
        query: {
          id
        }
      });
    },
    // 复制推品清单
    copyPushList({ id }) {
      this.$router.push({
        path: '/commodity/pushProductManage/copy',
        query: {
          id
        }
      });
    },
    // 推送
    movePushList({ id }) {
      this.$confirm(`确认推送吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          pushProductManagePublic({ id }).then((res) => {
            if (res.code === '0') {
              this.$message.success(res.msg);
              this.onSubmit_searchForm();
            }
          });
        })
        .catch(() => {
          this.$message.info('已取消操作');
        });
    },
    // 推广
    promotePushList() {
      this.$message.warning('请前往【企业微信-推品清单】进行使用');
    },
    // 删除推品清单
    deletePushList({ id }) {
      this.$confirm('确认删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          pushProductManageDelete({ id }).then((res) => {
            if (res.code === '0') {
              this.$message.success(res.msg);
              this.onSubmit_searchForm();
            }
          });
        })
        .catch(() => {
          this.$message.info('已取消操作');
        });
    }
  }
};
</script>

<style lang='scss' scoped>
.table {
  .table_cell_enable {
    width: 50px;
    height: 25px;
    padding: 1px;
    box-sizing: content-box;
    border-radius: 25px;
    background-color: gray;
    display: inline-block;
    text-align: left;
    cursor: pointer;

    &::before {
      content: '';
      display: inline-block;
      width: 25px;
      height: 25px;
      border-radius: 50%;
      background-color: #fff;
    }
    &.active {
      text-align: right;
      background-color: var(--main-color);
    }
  }
}
.action-bar {
  margin: 10px 0;
}
</style>
