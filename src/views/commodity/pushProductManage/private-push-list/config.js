import { parseTime } from '@/utils';
import dict from '@/components/Common/dicts';
import { checkGroup } from '@/components/Common/SelectGroup';

// 权限配置
export const Authority = {
  view: '/commodity/pushProductManage/list:view',
  edit: '/commodity/pushProductManage/list:edit',
  create: '/commodity/pushProductManage/list:add',
  copy: '/commodity/pushProductManage/list:copy'
};

export const FilterFormOptions = [
  {
    prop: 'name',
    component: 'input',
    label: '清单名称'
  },
  {
    prop: 'updateDate',
    label: '更新时间',
    component: 'dateRange',
    type: 'daterange',
    props: ['updateBeginDate', 'updateEndDate']
  },
  {
    prop: 'userIds',
    component: 'select',
    multiple: true,
    label: '创建人',
    options: dict('COMMON_STAFF_LIST'),
    placeholder: '请选择创建人',
    visible: () => !checkGroup()
  },
  {
    prop: 'groupId',
    component: 'select',
    label: '所属团队',
    options: dict('DISTRIBUTOR_TEAM_DS'),
    placeholder: '请选择所属团队',
    visible: () => !checkGroup()
  }
];

// 列表操作
export const ActionBars = {
  itemOptions: [
    {
      id: 'createPushList',
      label: '创建推品清单',
      authority: Authority.create,
      button: {
        type: 'primary'
      }
    }
  ]
};

// 列表展示内容
export const Table = {
  items: [
    {
      key: '_taskReleaseNumber',
      label: '推品清单编号'
    },
    {
      key: 'createByName',
      label: '创建人'
    },
    {
      key: 'groupId',
      label: '所属团队',
      handle: async (v) => {
        const optons = await dict.findLabelByValue({
          type: 'DISTRIBUTOR_TEAM',
          value: v ?? '1'
        });
        return optons?.label || {};
      }
    },
    {
      key: 'name',
      label: '清单名称'
    },
    {
      key: 'description',
      label: '描述'
    },
    {
      key: 'isHasHiddenCommodity',
      label: '是否包含隐藏商品',
      handle: (v) => (v === '0' ? '否' : '是')
    },
    {
      key: 'totalCommodityNum',
      label: '商品种类'
    },
    {
      key: 'totalCommodityQuantity',
      label: '商品件数'
    },
    {
      key: 'totalAmount',
      label: '商品总金额'
    },
    {
      key: 'pushType',
      label: '推送用户',
      handle: (v) => (v === 'ALL' ? '全部分销商' : '部分分销商')
    },
    {
      key: 'updateDate',
      label: '更新时间',
      handle: (v) => parseTime(v)
    },
    {
      key: 'isPush',
      label: '是否已推送',
      handle(v) {
        const vlaues = {
          0: '未推送',
          1: '已推送'
        };
        return vlaues[v];
      }
    },
    {
      key: 'enable',
      label: '是否启用',
      isSlot: true
    },
    {
      key: 'hide',
      label: '是否隐藏',
      isSlot: true
    }
  ]
};

// 列表操作
export const TableActionBars = {
  itemOptions: [
    {
      id: 'viewPushList',
      authority: Authority.view,
      label: '查看',
      isWrap: true,
      button: {
        type: 'text'
      }
    },
    {
      id: 'editPushList',
      authority: Authority.edit,
      label: '编辑',
      isWrap: true,
      button: {
        type: 'text'
      }
    },
    {
      id: 'copyPushList',
      authority: Authority.copy,
      label: '复制',
      isWrap: true,
      button: {
        type: 'text'
      }
    },
    {
      id: 'deletePushList',
      authority: Authority.edit,
      label: '删除',
      loadCondition: (row) => row.isPush === '0' && row.isEnable === '0',
      isWrap: true,
      button: {
        type: 'text'
      }
    },
    {
      id: 'promotePushList',
      label: '推广',
      isWrap: true,
      button: {
        type: 'text'
      }
    },
    {
      id: 'movePushList',
      label: '推送',
      loadCondition: (row) => row.isPush === '0' && row.isEnable === '1',
      isWrap: true,
      button: {
        type: 'text'
      }
    },
    {
      id: 'privateToPublic',
      authority: Authority.edit,
      label: '转为公共',
      isWrap: true,
      button: {
        type: 'text'
      }
    }
  ]
};
