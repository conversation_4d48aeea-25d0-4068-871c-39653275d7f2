<template>
  <div class="page-container">
    <instructions show-title is-hide v-if="isYearly">
      <template slot="title">
        <ul class="tip-text">
          <li>使用说明：</li>
          <li>1. 年框推品清单主要用于按照年框合同签约价格并参与年框政策设置的销售政策推送给分销商；</li>
          <li>2. 只有通过年框推品下单才会计算年框政策当中的随单政策（如下单随返和配赠），通过私人推品清单或者常规采购车不会计算年框政策当中的销售规则</li>
        </ul>
      </template>
    </instructions>
    <Form :options="options.Form" ref="form" class="form" :externalData="externalData" :keyEnterOnSubmit="onCommit">
      <div>
        <footer-bar v-if="type !== 'detail'">
          <el-button @click="onCancel" size="small">取消</el-button>
          <button-hoc @click="onCommit" size="small" type="primary" :loading="initLoading">保存</button-hoc>
        </footer-bar>
      </div>
      <template slot="commodityList" slot-scope="{ formData, options }">
        <FormTable :options="options" :form="formData">
          <template slot="table_cell_quantity" slot-scope="{ scope }">
            <el-input-number v-if="type !== 'detail'" v-model="scope.row.quantity" size="small" controls-position="right" :min="scope.row.miniOrderQuantity" label="件数"></el-input-number>
            <template v-else>
              {{ scope.row.quantity }}
            </template>
          </template>

          <template slot="table_cell_skuDetail" slot-scope="{ scope }">
            <div>{{ scope.row.skuLevelNames || '\\' }}</div>
            <div>标识：{{ scope.row.id }}</div>
          </template>
        </FormTable>
      </template>
      <template slot="pushTypeTip">
        <span v-show="this.commodityIsHidden" style="color: #ff0000">选择的商品里面包含了【隐藏】商品，所以需要选择指定用户进行推送</span>
      </template>
    </Form>

    <!-- 弹出框 -->
    <Dialog :callback="dialogCallback" class="dialog" ref="dialog" :dialogOptions="options.Dialog">
      <template slot-scope="{ scope }">
        <template v-if="dialogType === 'distributor'">
          <!-- 选择分销商 -->
          <SelectYearly v-if="isYearly" @onChecked="checkYearlyDistributor" :scope="scope"></SelectYearly>
          <SelectDistributor v-else @onChecked="checkDistributor" @onCancelCheck="onCancelCheck" :scope="scope" :dataParams="{ status: 'PASS', groupId: externalData.groupId, isSyncMerchant: '1' }"></SelectDistributor>
        </template>
        <template v-else>
          <!-- 选择推荐商品 -->
          <SelectCommodity :is-yearly="isYearly" :yealiyPolicyId="yealiyPolicyId" @onChecked="checkCommodity" @onCancelCheck="onCancelCheck" :scope="scope"></SelectCommodity>
        </template>
      </template>
    </Dialog>
  </div>
</template>

<script>
import SelectCommodity from './recommendGoods';
import SelectDistributor from './distributors';
import Form from '@/components/Dialog/Form';
import FormTable from '@/components/Form/FormTable';
import Dialog from '@/components/Dialog/index.vue';
import { pushProductManageGet } from '@/api/commodity/pushProduct.js';
import SelectYearly from './select-yearly/index.vue';

export default {
  name: 'pushProductManageOperate',
  components: {
    SelectYearly,
    Form,
    FormTable,
    SelectCommodity,
    SelectDistributor,
    Dialog
  },
  data() {
    return {
      initLoading: false,
      dialogType: 'distributors',
      externalData: {},
      commodityIsHidden: false,
      yealiyPolicyId: ''
    };
  },
  props: ['type', 'params', 'options'],
  computed: {
    isYearly() {
      return this.externalData.type === 'YEARLY';
    }
  },
  provide() {
    return {
      rootComponent: this
    };
  },
  mounted() {
    if (['detail', 'copy', 'edit'].includes(this.type)) {
      // id一样即使用缓存
      if (this.externalData.id === this.params.id) return;

      const tableItems = this.options.Form.items.find((i) => i.key === 'commodityList').Table.items;
      this.externalData = {
        commodityList: [],
        distributorIds: []
      };
      pushProductManageGet(this.params).then((res) => {
        const commodityList = res.data.recommendPlanDetails
          .filter((i) => i.skuCommodityVO)
          .map((i, index) => ({
            ...i.skuCommodityVO,
            quantity: i.quantity,
            ...tableItems.reduce((cur, { handle, key }) => {
              if (handle) {
                cur[key + '_handle'] = handle.apply(this, [i.skuCommodityVO[key], i.skuCommodityVO, index]);
              }
              return cur;
            }, {})
          }));

        this.externalData.commodityList.push(...commodityList);

        let distributorIds = [];
        const pushType = res.data.pushType;
        if (pushType !== 'ALL') {
          const tableItems2 = this.options.Form.items.find((i) => i.key === 'distributorIds').Table.items;
          distributorIds = res.data.distributors.map((i, index) => ({
            ...i,
            ...tableItems2.reduce((cur, { handle, key }) => {
              if (handle) {
                cur[key + '_handle'] = handle.apply(this, [i[key], i, index]);
              }
              return cur;
            }, {})
          }));
        }
        // 年框商品清单
        const { type, activityId } = res.data;
        if (type === 'YEARLY') {
          distributorIds.forEach((i) => {
            i.csName = i.customerServiceVO?.name ?? '';
          });
          this.yealiyPolicyId = activityId;
          this.$refs.form.templateData.activityId = activityId;
        }
        this.externalData = Object.assign({}, this.externalData, {
          ...res.data,
          distributorIds
        });

        if (this.type !== 'detail') {
          this.handleDistributorId();
        }
      });
    } else {
      this.externalData = {
        ...this.params
      };
    }
  },
  methods: {
    onCancel() {
      this.$back({
        path: '/commodity/pushProductManage/list'
      });
    },
    // 保存
    onCommit() {
      this.$refs.form.onSubmit();
    },
    dialogCallback() {},
    // 取消选择弹框
    onCancelCheck() {
      this.$refs.dialog.setVisible(false);
    },
    // 选择分销商
    checkDistributor(rows) {
      this.$refs.form.templateData.distributorIds = rows;
      this.$refs.dialog.setVisible(false);
    },
    // 选择分销商-年框
    checkYearlyDistributor(row) {
      this.yealiyPolicyId = row.id;
      this.$refs.form.templateData.distributorIds = [
        {
          csName: row.csName,
          shopName: row.shopName,
          id: row.distributorId,
          applyMobile: row.loginMobile
        }
      ];
      this.$refs.form.templateData.activityId = row.id;
      this.$refs.dialog.setVisible(false);
    },
    // 选择商品
    checkCommodity(rows) {
      // 编辑时，清单如果1.已推送2.分销商为全部 那么不能选择含有隐藏的商品
      if (this.$route.query.isPush === '1' && this.$refs.form.templateData.pushType === 'ALL' && rows.find((i) => i.isHidden === '1')) {
        this.$message.warning('不能选择隐藏商品');
        return;
      }

      // 赋予件数
      this.$refs.form.templateData.commodityList = rows.map((i) => {
        // 之前已经存在件数就不改变
        const commodity = this.$refs.form.templateData.commodityList.find((x) => x.id === i.id);
        return {
          ...i,
          quantity: commodity ? commodity.quantity : i.miniOrderQuantity || 1
        };
      });

      this.handleDistributorId();

      this.$refs.dialog.setVisible(false);
    },
    // 选择隐藏商品分销商必须为部分
    handleDistributorId() {
      if (this.type === 'detail' || this.$route.query.isPush === '1') return;
      this.commodityIsHidden = false;
      this.$refs.form.templateData.commodityList.some((i) => {
        if (i.isHidden === '1') {
          this.commodityIsHidden = true;
        }
        return this.commodityIsHidden;
      });
      if (this.isYearly || this.commodityIsHidden) {
        this.$refs.form.templateData.pushType = 'PART';
      }
      const pushTypeOptions = this.options.Form.items.find((i) => i.key === 'pushType');
      pushTypeOptions.disabled = this.isYearly ? true : this.commodityIsHidden;
      const distributorIdsOptions = this.options.Form.items.find((i) => i.key === 'distributorIds');
      distributorIdsOptions.observerListener.apply(this.$refs.form, [distributorIdsOptions]);
    }
  }
};
</script>

<style lang="scss" scoped>
.tip-text {
  li {
    line-height: 150%;
  }
}
.page-container {
  ::v-deep {
    .el-form {
      flex-direction: column;
      .el-form-item {
        width: 100%;
        .el-input-number {
          width: 100%;
        }
      }
    }
    .dialog {
      .el-dialog__body {
        max-height: none;
      }

      .el-form {
        flex-direction: row;
        .el-form-item {
          width: 33%;
        }
      }
    }
  }
}
</style>
