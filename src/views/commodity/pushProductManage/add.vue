<template>
  <operate :type="operateType" :params="$route.query" :options="options" ref="operate"></operate>
</template>

<script>
import operate from './operate.vue';
import cloneDeep from 'lodash/cloneDeep'; // 对象深拷贝
import * as options from './config';
import { selectGroup } from '@/components/Common/SelectGroup';

/* 推品清单创建 */
export default {
  name: 'add',
  components: { operate },
  data() {
    return {
      operateType: 'add',
      options: {}
    };
  },
  created() {
    const operate = this.isYearly ? options.yearlyOperate : options.operate;
    this.options = cloneDeep(operate);
    this.options.Form.operateType = 'add';
  },
  computed: {
    isYearly() {
      return this.$route.query.type === 'YEARLY';
    }
  },
  mounted() {
    selectGroup((groupId) => {
      if (groupId) {
        this.$refs.operate.externalData = {
          ...this.$route.query,
          groupId
        };
      } else {
        this.cancal();
      }
    }).apply(this);
  },
  methods: {
    cancal() {
      this.$back({
        path: '/commodity/pushProductManage/list'
      });
    }
  }
};
</script>

<style></style>
