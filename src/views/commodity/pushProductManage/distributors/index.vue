<template>
  <div class="table">
    <SelectTableRow
      @onCommit="checkOnCommit"
      @onCancel="checkOnCancel"
      :isMultiple="isMultiple"
      ref="selectTableRow"
    >
      <template slot="table">
        <CommonTableExhibition
          :options="options"
          @handleSelectionChange="
            (rows) => {
              $refs.selectTableRow.handleSelectionChange(rows);
            }
          "
          @getTableData="getTableData"
          ref="cte"
        >
        </CommonTableExhibition>
      </template>
    </SelectTableRow>
  </div>
</template>

<script>
import CommonTableExhibition from '@/components/Common/CommonTableExhibition.vue';
import SelectTableRow from '@/components/Common/SelectTableRow.vue';
import options from './config';

import { list } from '@/api/distributorManagement/distributor/list.js';

/* 分销商选择 */
export default {
  name: 'distributors',
  components: { CommonTableExhibition, SelectTableRow },
  data() {
    return {};
  },
  computed: {
    options() {
      const o = {
        ...options,
        ...{
          Table: {
            ...options.Table,
            isSelect: this.isSelect
          }
        }
      };
      return o;
    }
  },
  props: {
    scope: {
      type: Object,
      default() {
        return {};
      }
    },
    // 单选传 false， 默认多选
    isMultiple: {
      type: Boolean,
      default: true
    },
    dataParams: {
      type: Object,
      default() {
        return {};
      }
    },
    // 是否显示 table selection
    isSelect: {
      type: Boolean,
      default: true
    },
    // 指定接口
    httpRequest: Function
  },
  watch: {
    scope: {
      handler() {
        this.$nextTick(() => {
          this.init();
        });
      },
      immediate: true
    }
  },
  methods: {
    init() {
      this.$nextTick(() => {
        this.$refs.selectTableRow.beforeMultipleSelection = [
          ...(this.scope.multipleSelection || [])
        ];
        // 重置之前存在的选择缓存
        this.$refs.selectTableRow.multipleSelection = [];
        // 重置查询
        this.$refs.cte.onReset();
      });
    },
    getTableData({ data, pageNo, pageSize, callback }) {
      let data2;
      const rep = this.httpRequest ? this.httpRequest : list;
      return rep({
        data: {
          ...data,
          ...this.dataParams,
          status: 'PASS'
        },
        pageNo,
        pageSize
      })
        .then((res) => {
          data2 = res.data;
        })
        .finally(() => {
          callback(data2);

          const str = this.$refs.selectTableRow;
          str.multipleSelection.forEach((i) => {
            if (!str.beforeMultipleSelection.find((x) => x.id === i.id)) {
              str.beforeMultipleSelection.push(i);
            }
          });
          const ms = this.$refs.selectTableRow.beforeMultipleSelection; // 获取已经选择的
          const tableData = this.$refs.cte.tableData;
          const filterData = tableData.filter((i) =>
            ms.find((x) => x.id === i.id)
          );
          // 记录已经选择的数据项
          this.$refs.cte.multipleSelection.splice(
            0,
            tableData.length,
            ...filterData
          );
          // 触发element-ui的选择
          this.$nextTick(() => {
            this.toggleRowSelections(filterData, true);
          });
        });
    },
    toggleRowSelections(data, select) {
      data.forEach((i) => {
        this.$refs.cte.$refs.multipleTable.toggleRowSelection(i, select);
      });
    },
    checkOnCancel() {
      this.$emit('onCancelCheck');
    },
    // 添加推荐商品
    checkOnCommit(multipleSelection) {
      this.$emit('onChecked', multipleSelection);
    }
  }
};
</script>

<style lang="scss" scoped>
.table {
  height: 70vh;
  .form {
    ::v-deep {
      .el-form {
        display: flex;
        flex-wrap: wrap;
      }
      .el-form-item {
        width: 33%;
        display: flex;
        height: 32px;
        margin-bottom: 16px;

        .el-form-item__label {
          padding-right: 16px;
          white-space: nowrap;
          display: inline-block;
          line-height: 32px;
          font-family: 'PingFangSC-Regular';
          color: rgb(0, 0, 0);
          font-weight: normal;
        }
        .el-form-item__content {
          flex: 1;
          margin-left: 0px !important;
          & > div {
            width: 100%;
          }
        }
      }
    }
  }
  .dialog {
    ::v-deep {
      .el-dialog {
        .el-dialog__header {
          text-align: center;
        }
      }
    }
  }

  &-table {
    margin-top: 10px;
  }
}
</style>
