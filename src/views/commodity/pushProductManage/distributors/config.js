// 推荐商品配置
import { getDictionary } from '@/api/common/dictionary.js';

export default {
  Form: {
    items: [
      {
        key: 'purchaseType',
        type: 'select',
        selectOptions: getDictionary.bind(null, 'DISTRIBUTOR_PURCHASE_TYPE'),
        label: '客户类型'
      },
      {
        key: 'shopType',
        type: 'select',
        selectOptions: getDictionary.bind(null, 'DISTIBUTOR_SHOP_TYPE'),
        label: '分销商类型'
      },
      {
        key: 'shopName',
        type: 'input',
        label: '店铺名称'
      },
      {
        key: 'platformShopId',
        type: 'input',
        label: '店铺ID'
      },
      {
        key: 'applyMobile',
        type: 'input',
        label: '手机号'
      },
      {
        key: 'csIds',
        type: 'select',
        selectOptions: getDictionary.bind(null, 'ADVISER_EXPAND'),
        label: '专属顾问'
      }
    ],
    template: {},
    interface: () => {}
  },
  Table: {
    isSelect: true,
    items: [
      {
        key: 'shopName',
        label: '店铺名称'
      },
      {
        key: 'platformShopId',
        label: '店铺ID'
      },
      {
        key: 'applyMobile',
        label: '登录手机号'
      },
      {
        key: 'sourceName',
        label: '分销商类型'
      },
      {
        key: 'statusName',
        label: '状态'
      },
      {
        key: 'customerServiceVO',
        label: '专属顾问',
        handle(v = {}) {
          return v.name || '-';
        }
      }
    ],
    operates: [
      {
        label: '添加',
        click(row) {
          const multipleSelection = this.$parent.selectData;
          if (!multipleSelection.find((i) => i.id === row.id)) {
            this.handleSelectionChange([...this.multipleSelection, row]);
          }
          const selectTableRow = this.$parent;
          selectTableRow.$emit('onCommit', selectTableRow.isMultiple ? [...selectTableRow.selectData] : [row]);
        }
      }
    ]
  },
  Pagination: {}
};
