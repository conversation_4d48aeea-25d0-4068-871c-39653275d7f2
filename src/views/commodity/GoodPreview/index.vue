<!-- 商品预览组件 -->
<template>
  <div class="good-preview">
    <el-button size="small" type="text" @click="openPreview"> 预览</el-button>
    <el-dialog title="商品预览" width="390px" append-to-body :visible.sync="dialogVisible" @close="() => (dialogVisible = false)">
      <div v-if="loaded" class="good-preview-dialog-box">
        <div class="goods-swiper-wrap">
          <el-carousel indicator-position="none">
            <el-carousel-item class="goods-swiper-item" v-if="videoUrl" :key="'-1'" :data-index="-1">
              <VideoUpload v-model="videoUrl"></VideoUpload>
            </el-carousel-item>
            <el-carousel-item v-for="(item, index) in list" :key="index">
              <img :src="item" class="goods-swiper-item-image" />
            </el-carousel-item>
          </el-carousel>
          <div class="indicator-dots" v-if="carouselTotal >= 1">{{ carouselCurrent }}/{{ carouselTotal }}</div>
        </div>
        <goodsBasicInformation :interfaceData="interfaceData" />
        <preferential :activityRuleBriefVOS="activityRuleBriefVOS" @openGiftOff="openGiftOff" />
        <warehousing
          :currentSkuObj="currentSkuObj"
          :interfaceData="interfaceData"
          :isSkuShow="isSkuShow"
          @openSkuPannel="
            () => {
              showGoodsEdit = true;
              ismark = true;
            }
          "
        />
        <brandColumn :interfaceData="interfaceData" />
        <productParameters title="商品参数" :paramRelationList="commodityParamRelationVOList" />
        <productParameters title="商品详情" goodsDetail :detailImageUrls="detailImageUrls" />
      </div>
      <!-- 弹出层--选择加入采购车数量 -->
      <div v-show="showGoodsEdit" class="show-sku-pop">
        <div class="subject">
          <span class="title-text"></span>
          <div
            class="close-warp"
            @click="
              () => {
                showGoodsEdit = false;
                ismark = false;
              }
            "
          >
            <span class="close"></span>
          </div>
        </div>
        <skuPannel :goods="interfaceData" @confirmNum="skuPannelConfirmNum" @skuChange="skuChange"></skuPannel>
      </div>
      <div class="gift-off-pop-box" v-show="popShowGiftOff">
        <div class="subject">
          <span class="title-text">满赠</span>
          <div
            class="close-warp"
            @click="
              () => {
                popShowGiftOff = false;
                ismark = false;
              }
            "
          >
            <span class="close"></span>
          </div>
        </div>
        <div class="gift-off-box" v-for="item in giftOff.giftRuleBriefVOList" :key="item.id">
          <div class="gift-off-time">
            活动时间：{{ item.startDate | parseDefaultTime }}
            {{ item.endDate | parseDefaultTime }}
          </div>
          <div v-for="(gs, index) in item.activityGiftVOList" :key="index" class="lists-item">
            <div>{{ gs.activityContent }}</div>
            <div class="zp">赠：{{ gs.giftBriefVO.name }}</div>
          </div>
        </div>
        <div
          class="s-popup-btn"
          @click="
            () => {
              popShowGiftOff = false;
              ismark = false;
            }
          "
        >
          确定
        </div>
      </div>
      <div class="mark" v-show="ismark"></div>
    </el-dialog>
  </div>
</template>

<script>
import { commodityDetail, activityListActivityRuleForCommodity } from '@/api/commodity/list';
import VideoUpload from '@/components/Upload/VideoUpload';
import goodsBasicInformation from '../goods-group/goods-basic-information';
import productParameters from '../goods-group/product-parameters';
import preferential from '../goods-group/preferential';
import warehousing from '../goods-group/warehousing';
import skuPannel from '../goods-group/sku-pannel';
import brandColumn from '../goods-group/brand-column';

export default {
  name: 'GoodPreview',
  data() {
    // 活动数据
    const activityData = {
      activityRuleBriefVOS: [], // 营销活动规则 ,
      cashBackForCommodityVO: null, // 首单返现活动信息，包括抵扣规则 ,
      seckillRuleBriefVO: null, //  限时抢购规则（null表示限时抢购批次未进行） ,
      shippingThreshold: null // 包邮门槛（null表示不包邮，0表示运费0元，其他表示 满XX包邮） ,
    };
    return {
      loaded: false,
      interfaceData: {},
      carouselCurrent: 1,
      dialogVisible: false,
      list: [], // 轮播
      detailImageUrls: [], // 详情图片
      videoUrl: '', // 视频地址
      videoCover: '', // 视频封面
      commodityParamRelationVOList: [],
      ...activityData,
      showGoodsEdit: false,
      currentSkuObj: {},
      popShowGiftOff: false,
      giftOff: {},
      ismark: false,
      isSkuShow: false // 是否展示规格名称  如果没有规格名称 则不展示
    };
  },
  props: ['id'],
  components: {
    VideoUpload,
    goodsBasicInformation,
    productParameters,
    preferential,
    warehousing,
    skuPannel,
    brandColumn
  },

  computed: {
    carouselTotal() {
      let num = 0;
      if (this.videoUrl) {
        num = num + 1;
      }
      return this.list.length + num;
    }
  },

  methods: {
    getCommodityDetail() {
      return commodityDetail(this.id).then((res) => {
        if (res.data.commodityMultimediaVO && res.data.commodityMultimediaVO.length > 0) {
          res.data.shopWindowUrls = [];
          res.data.commodityMultimediaVO.map((item) => {
            if (item.type === 'SHOPWINDOW') {
              // 轮播图片
              res.data.shopWindowUrls = JSON.parse(item.jsonValue);
            }
            if (item.type === 'DETAILS') {
              // 详情图片
              res.data.detailImageUrls = JSON.parse(item.jsonValue);
            }
            if (item.type === 'VIDEO') {
              // 视频
              res.data.videoUrl = JSON.parse(item.jsonValue);
            }
            if (item.type === 'VIDEO_COVER') {
              // 视频图片
              res.data.videoCover = JSON.parse(item.jsonValue);
            }
            if (item.type === 'MATERIAL_ZIP') {
              // 素材下载zip包
              res.data.materialZip = JSON.parse(item.jsonValue);
            }
          });
        }
        console.dir(res.data);
        const { shopWindowUrls = [], videoUrl = '', videoCover = '', detailImageUrls = [], commodityParamRelationVOList = [], skuVOlist = [] } = res.data;
        this.interfaceData = res.data;
        this.list = shopWindowUrls;
        this.videoUrl = videoUrl;
        this.videoCover = videoCover;
        this.detailImageUrls = detailImageUrls;
        this.commodityParamRelationVOList = commodityParamRelationVOList;
        this.isSkuShow = Boolean(skuVOlist.length >= 1 && (skuVOlist[0].name || skuVOlist[0].firstLevel));
      });
    },
    // 获取活动列表
    getActivityList() {
      return activityListActivityRuleForCommodity(this.id).then((res) => {
        if (res.success) {
          const { activityRuleBriefVOS, cashBackForCommodityVO, seckillRuleBriefVO, shippingThreshold } = res.data;
          this.activityRuleBriefVOS = activityRuleBriefVOS;
          this.cashBackForCommodityVO = cashBackForCommodityVO;
          this.seckillRuleBriefVO = seckillRuleBriefVO;
          this.shippingThreshold = shippingThreshold;
        }
      });
    },
    openPreview() {
      this.dialogVisible = true;
      this.loaded = false;
      Promise.all([this.getCommodityDetail(), this.getActivityList()]).then((res) => {
        this.loaded = true;
      });
    },
    skuPannelConfirmNum(e) {
      this.showGoodsEdit = false;
      this.ismark = false;
    },
    openGiftOff(e) {
      this.popShowGiftOff = true;
      this.ismark = true;
      this.giftOff = e;
    },
    // sku 选择
    skuChange(e) {
      const { skuObj } = e.detail;
      this.currentSkuObj = skuObj;
    }
  }
};
</script>
<style lang='scss' scoped>
.good-preview {
  position: relative;

  ::v-deep .el-carousel__container {
    height: 370px;
  }
}
::v-deep .el-dialog__body {
  padding: 0;
  background: #f2f5f8;
}
.el-carousel__item h3 {
  color: #475669;
  font-size: 18px;
  opacity: 0.75;
  line-height: 300px;
  margin: 0;
}

.el-carousel__item:nth-child(2n) {
  background-color: #99a9bf;
}

.el-carousel__item:nth-child(2n + 1) {
  background-color: #d3dce6;
}
.goods-swiper-item-image {
  width: 100%;
  height: 100%;
}
.goods-swiper-wrap {
  position: relative;
  background-image: url('https://oss.syounggroup.com/static/file/soyoung-zg/aliyun/soyoung-zg/mobile-terminal/default-image.png');
  background-position: center center;
  background-size: cover;
}
.goods-swiper-item-image {
  width: 100%;
  height: 100%;
}
.indicator-dots {
  position: absolute;
  font-size: 12px;
  right: 15px;
  bottom: 14px;
  padding: 0 7px;
  height: 20px;
  line-height: 20px;
  border-radius: 20px;
  color: #ffffff;
  opacity: 0.4;
  z-index: 60;
  background: #000000;
  min-width: 32px;
  box-sizing: border-box;
  text-align: center;
}
.good-preview-dialog-box {
  max-height: 700px;
  overflow: auto;
  position: relative;
}
.show-sku-pop {
  background: #fff;
  position: absolute;
  left: 0;
  bottom: 0;
  max-height: 450px;
  overflow: auto;
  width: 100%;
  z-index: 10;
}
.subject {
  display: flex;
  height: 10%;
  font-size: 14px;
  color: #222;
  text-align: center;
  .title-text {
    font-size: 15px;
    line-height: 47.5px;
    padding-left: 10px;
  }
  .close-warp {
    display: flex;
    position: absolute;
    right: 10px;
    top: 7.5px;
    width: 35px;
    height: 35px;
    justify-content: center;
    align-items: center;
  }
  .close-warp .close {
    width: 15px;
    height: 15px;
    background-image: url('https://oss.syounggroup.com/static/file/soyoung-zg/aliyun/newretail/mobile/images/common/close-square.png');
    background-size: cover;
  }
}
.mark {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.4);
  z-index: 9;
}
.gift-off-box {
  padding: 10px 15px;
  font-size: 13px;
  .lists-item {
    margin-bottom: 10px;
    .zp {
      color: #999999;
      margin-bottom: 2px;
    }
  }
}
.gift-off-pop-box {
  position: absolute;
  bottom: 0;
  left: 0;
  background: #fff;
  z-index: 10;
  width: 100%;
  text-align: left;
}

.s-popup-btn {
  width: 345px;
  height: 36px;
  line-height: 36px;
  background: #d7092f;
  border-radius: 18px;
  font-size: 15px;
  text-align: center;
  color: #ffffff;
  margin: 10px auto;
}
.gift-off-time {
  font-size: 22rpx;
  color: #999999;
  margin-bottom: 5rpx;
}
</style>
