<template>
  <div class="page-container">
    <el-form :model="form" :rules="rules" class="form-container" ref="form" label-width="120px" label-position="left" size="small">
      <el-form-item label-width="0">
        <ul class="form-tips">
          <li>说明：</li>
          <li>1、该流程用于为年框客户申请合同外权益；</li>
          <li>2、品牌成本中心和需求部门，请选择品牌预算部门，可咨询财务BP后再选择。</li>
        </ul>
      </el-form-item>
      <el-form-item label="驳回原因" v-if="oaRejectReason && isEdit">
        <div class="reject">{{ oaRejectReason }}</div>
      </el-form-item>
      <el-form-item label="选择分销商" prop="customerAccount">
        <choosing-distributor :editable="!isDetail" v-model="form.customerAccount" />
      </el-form-item>
      <el-form-item label="选择需求部门" prop="requireDept" class="form-item-box">
        <p v-if="isDetail">{{ info.requireDeptName || '--' }}</p>
        <sy-org-cascader v-else-if="companyCodes.length" departmentType="HR" optionValue="hrDepartmentCode" v-model="form.requireDept" :companyCodes="['SYGF', 'SYGJ']" :bind="{ clearable: true, filterable: true, disabled: isDetail }" />
      </el-form-item>
      <el-form-item label="选择品牌成品中心" prop="costCenter">
        <p v-if="isDetail">{{ info.costCenterName || '--' }}</p>
        <el-select v-else v-model="form.costCenter" filterable placeholder="请选择" :disabled="isDetail">
          <el-option v-for="item in costCenterList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="申请政策" prop="applyTypeList">
        <div v-if="isDetail">
          <div v-for="item in form.applyTypeList" :key="item">
            <span class="radio-box__title">{{ item === 'VIRTUAL_CREDIT' ? '返利' : '返现金' }}</span>
            <span>{{ item === 'VIRTUAL_CREDIT' ? form.creditValue : form.rebateCashValue }}</span>
            <span>元</span>
          </div>
        </div>
        <el-checkbox-group v-else v-model="form.applyTypeList" :disabled="isDetail">
          <el-checkbox label="VIRTUAL_CREDIT">
            <div class="radio-box">
              <span class="radio-box__title">返利</span>
              <el-input v-model.trim="form.creditValue" :disabled="isDetail">
                <template slot="append">元</template>
              </el-input>
            </div>
          </el-checkbox>
          <br />
          <el-checkbox label="REBATE_CASH" style="margin-top: 10px">
            <div class="radio-box">
              <span class="radio-box__title">返现金</span>
              <el-input v-model.trim="form.rebateCashValue" :disabled="isDetail">
                <template slot="append">元</template>
              </el-input>
            </div>
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>

      <el-form-item label="政策描述" prop="remarks">
        <p v-if="isDetail">{{ form.remarks }}</p>
        <el-input v-else style="width: 400px" type="textarea" :autosize="{ minRows: 6, maxRows: 20 }" placeholder="请输入内容" v-model="form.remarks" maxlength="500" show-word-limit> </el-input>
      </el-form-item>
      <el-form-item label="计划使用时间" prop="timeData">
        <p v-if="isDetail">{{ info.startDate | parseDefaultTime }} 至 {{ info.endDate | parseDefaultTime }}</p>
        <el-date-picker v-else :picker-options="pickerOptions" :default-time="['00:00:00', '23:59:59']" end-placeholder="结束日期" range-separator="至" start-placeholder="开始日期" type="datetimerange" value-format="timestamp" v-model.trim="form.timeData"></el-date-picker>
      </el-form-item>
    </el-form>
    <footer-bar>
      <div class="footer-btn-box">
        <el-button @click="onCancel">取消</el-button>
        <button-hoc @click="submitForm" type="primary" :loading="loading" :disabled="isDetail">提交并发起OA</button-hoc>
      </div>
    </footer-bar>
  </div>
</template>

<script>
import ChoosingDistributor from '../components/ChoosingDistributor';
import { parseDefaultTime } from '@/utils';
import { distributorPolicyApplyExternalListUserCompany, distributorPolicyApplyExternalListCostCenter, distributorPolicyApplyExternalCreate, distributorPolicyApplyExternalGet, distributorPolicyApplyExternalUpdate } from '@/api/yearly-policy-management';
export default {
  components: {
    ChoosingDistributor
  },
  name: 'policy-list-form',
  props: {
    id: String,
    isAdd: {
      type: Boolean,
      default: false
    },
    isEdit: {
      type: Boolean,
      default: false
    },
    isDetail: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      form: {
        customerAccount: null, // 返利账户
        remarks: '',
        timeData: '',
        requireDept: [], // 需求部门
        creditValue: '', // 返利金额
        rebateCashValue: '', // 返现金额
        applyTypeList: [],
        costCenter: ''
      },
      rules: {
        customerAccount: [{ required: true, message: '请选择返利账户', trigger: 'blur' }],
        costCenter: [{ required: true, message: '请选择品牌成本中心', trigger: 'blur' }],
        requireDept: [{ required: true, message: '请选择需求部门', trigger: 'blur' }],
        timeData: [{ required: true, message: '请选择计划使用时间', trigger: 'blur' }],
        creditValue: [{ required: true, message: '请输入返利金额', trigger: 'blur' }],
        rebateCashValue: [{ required: true, message: '请输入返现金额', trigger: 'blur' }],
        remarks: [{ required: true, message: '请输入政策描述', trigger: 'blur' }],
        applyTypeList: [{ required: true, message: '请选择申请政策', trigger: 'blur' }]
      },
      companyCodes: [],
      costCenterList: [], // 成本中心列表
      loading: false,
      oaRejectReason: '', // 驳回原因和节点
      creditID: '',
      rebateID: '',
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7; // 禁用今天之前的日期
        }
      },
      info: {} // 详情信息
    };
  },
  computed: {
    formData() {
      const { customerAccount, remarks, timeData, requireDept, creditValue, rebateCashValue, costCenter, applyTypeList } = this.form;

      const detailExternalCmdList = applyTypeList.map((item) => {
        const typeValue = item === 'VIRTUAL_CREDIT' ? creditValue : rebateCashValue;
        const id = item === 'VIRTUAL_CREDIT' ? this.creditID : this.rebateID;
        return {
          applyType: item,
          typeValue: typeValue,
          id
        };
      });
      let [startDate, endDate] = timeData;
      startDate = parseDefaultTime(startDate);
      endDate = parseDefaultTime(endDate);
      const customerAccountId = customerAccount.id;
      const policyPlatformOwnerType = customerAccount.policyPlatformOwnerType;
      return {
        customerAccountId,
        remarks,
        startDate,
        endDate,
        requireDept,
        costCenter,
        detailExternalCmdList,
        policyPlatformOwnerType
      };
    }
  },
  methods: {
    initData() {
      this.getUserCompany();
      this.getCostCenter();
      this.getDetail();
    },
    submitForm() {
      this.$refs.form.validate((valid) => {
        if (!valid) {
          return;
        }
        let msg = '';
        const hasEmptyTypeValue = this.formData.detailExternalCmdList.some((item) => {
          if (!item.typeValue) {
            msg = item.applyType === 'VIRTUAL_CREDIT' ? '请输入返利金额' : '请输入返现金额';
            return true;
          }
          return false;
        });
        if (hasEmptyTypeValue) {
          this.$message.error(msg);
          return;
        }
        const params = {
          ...this.formData
        };
        this.loading = true;
        const request = this.isEdit ? distributorPolicyApplyExternalUpdate : distributorPolicyApplyExternalCreate;
        this.isEdit && (params.id = this.id);
        request(params)
          .then((res) => {
            this.$message.success('操作成功');
            this.$back({ path: '/yearly-policy-management/policy-list' });
          })
          .finally(() => {
            this.loading = false;
          });
      });
    },
    getDetail() {
      if (this.isAdd) return;
      // Object.assign
      distributorPolicyApplyExternalGet(this.id).then((res) => {
        const info = res?.data ?? {};
        this.info = info;
        const { oaRejectReason, requireDeptList = [], costCenter = '', remarks = '', startDate, endDate, policyPlatformOwnerType, customerAccountId, detailExternalVOList = [], shopName = '', sapChannelName = '', brandName } = info;
        this.form.requireDept = requireDeptList;
        this.form.costCenter = costCenter;
        this.form.remarks = remarks;
        this.form.timeData = [startDate, endDate];
        const customerAccount = {
          policyPlatformOwnerType,
          id: customerAccountId,
          distributorName: shopName,
          distributorPartyRelationName: sapChannelName,
          subtypeValueName: brandName
        };
        this.form.customerAccount = customerAccount;
        const applyTypeList = detailExternalVOList.map((item) => item.applyType);
        this.form.applyTypeList = applyTypeList;
        detailExternalVOList.forEach((item) => {
          if (item.applyType === 'VIRTUAL_CREDIT') {
            this.form.creditValue = item.typeValue;
            this.creditID = item.id;
          } else {
            this.form.rebateCashValue = item.typeValue;
            this.rebateID = item.id;
          }
        });
        this.oaRejectReason = oaRejectReason;
      });
    },
    onCancel() {
      this.$back({
        path: `/yearly-policy-management/policy-list`
      });
    },
    // 选择分销商返利账户
    selectDistributor(value) {
      this.form.customerAccount = value;
    },
    // 获取用户所属公司
    getUserCompany() {
      distributorPolicyApplyExternalListUserCompany().then((res) => {
        this.companyCodes = res.data && [res.data];
      });
    },
    // 获取成本中心数据
    getCostCenter() {
      distributorPolicyApplyExternalListCostCenter().then((res) => {
        const list = res.data || [];
        this.costCenterList = list.map((item) => ({
          label: item.costCenterName,
          value: item.costCenterCode
        }));
      });
    }
  },
  mounted() {
    this.initData();
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
::v-deep {
  .btn-box {
    span {
      padding-right: 10px;
    }
    .remove {
      margin-left: 0;
    }
  }
  .el-input--medium {
    width: 300px;
  }
  .el-radio-group {
    & {
      display: flex;
      flex-direction: column;
      padding-top: 1px;
    }
    .el-radio {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
      .el-date-editor {
        width: 400px;
      }
      &__label {
        line-height: 30px;
      }
    }
    .el-input {
      width: 300px;
    }
    .el-input-group__append {
      background-color: #f7f8fa;
      color: #868d9f;
      border: 1px solid #e6e8eb;
      border-left-color: transparent;
    }
  }
}

.footer-btn-box {
  width: 100%;
  text-align: right;
}
.radio-box {
  display: flex;
  align-items: center;
  &__title {
    width: 60px;
  }
}
.form-tips {
  font-size: 12px;
  padding: 12px;
  background-color: var(--background-color-base);
  color: var(--color-info);
  line-height: 150%;
}
.form-item-box {
  ::v-deep .el-input__inner {
    width: 400px;
  }
}
.reject {
  color: var(--color-danger);
}
</style>
