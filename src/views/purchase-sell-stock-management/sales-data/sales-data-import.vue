<template>
  <div class="table-container import">
    <instructions :showTitle="true" :colNum="0">
      <template slot="title">
        分销商店铺名称：{{ distributorShopName }} <span class="m-l-24">分销商ID：{{ distributorId }}</span>
      </template>
    </instructions>
    <el-row :gutter="24" type="flex" align="middle" class="import-step">
      <el-col>第一步：下载模板 </el-col>
      <el-col><i class="el-icon-right color-primary"></i></el-col>
      <el-col>第二步：完善【存货数据】填写，然后导入 </el-col>
    </el-row>
    <el-form ref="ruleForm" :model="ruleForm" size="mini" :rules="rules" label-width="130px">
      <el-timeline>
        <el-timeline-item timestamp="第一步 选择需要导入的时间周期" placement="top">
          <el-form-item label="数据录入开始日期：">
            <span>{{ startDate | parseTime('{y}-{m}-{d}') }}</span>
          </el-form-item>
          <el-form-item label="数据录入截止日期：" prop="endEntryDate">
            <div class="jump-box">
              <el-date-picker v-model="ruleForm.endEntryDate" type="date" placeholder="请选择数据录入截止日期" :picker-options="pickerOptions"> </el-date-picker>
            </div>
          </el-form-item>
          <el-form-item label="下载客户模板：">
            <button-hoc type="primary" @click="exportInventoryData">下载《分销商存货数据》</button-hoc>
            <p class="color-info">该模板会自动统计数据录入截止日期前客户系统的当前存货</p>
          </el-form-item>
        </el-timeline-item>
        <el-timeline-item timestamp="第二步 完成存货数据填写，然后导入" placement="top">
          <p class="p-t-8">导入填写完的分销商存货数据模板</p>
          <import-page
            source="salesData"
            name="存货数据"
            ref="importPage"
            @uploadFileSuccess="uploadFileSuccess"
            @onRemove="uploadFileRemove"
            :isDisabledUpload="isDisabledUpload"
            :fileRowNumLimit="5000"
            :uploadRequest="uploadRequest"
            :downloadFailRequest="downloadFailRequest"
            :showInstructions="false"
            :showFooter="false"
            showFileList
          />
          <div v-if="isShowTable">
            <el-table :data="abnormalData" style="width: 100%" v-el-horizontal-scroll size="mini">
              <el-table-column type="index" label="序号" width="50"></el-table-column>
              <el-table-column prop="commodityName" label="商品名称" min-width="200"></el-table-column>
              <el-table-column prop="commodityBarcode" label="商品条码"></el-table-column>
              <el-table-column prop="inventoryNum" label="录入存货销量"></el-table-column>
              <el-table-column prop="saleNum" label="计算出来商品销量"></el-table-column>
              <el-table-column prop="remark" label="*填写原因（100字以内）" min-width="300">
                <template slot-scope="scope">
                  <el-input v-model.trim="scope.row.remark" type="text" maxlength="100" placeholder="请输入"></el-input>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-timeline-item>
      </el-timeline>
      <div class="footer-wrap">
        <div class="footer">
          <button-hoc type="primary" @click="onSubmit">确定</button-hoc>
          <el-button @click="mixCancel">取消</el-button>
        </div>
      </div>
    </el-form>
  </div>
</template>

<script>
import importPage from '@/components/Common/Import/importPage.vue';
import download from '@/utils/download';
import { parseTime } from '@/utils';
import { psiSaleEntryExportDistributorCommodityInventory, psiSaleEntryGetLastSaleEntryByShopId, psiSaleEntrySaveSaleEntryDetaill } from '@/api/purchase-sell-stock-management/sales-data';
import salesDataEntry from '@/mixins/sales-data-entry';
export default {
  name: 'purchase-sell-stock-sales-data-import',
  mixins: [salesDataEntry],
  components: {
    importPage
  },
  data() {
    return {
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now(); // 可选历史天、可选当前天、不可选未来天
        }
      },
      startDate: null, // 数据录入开始时间
      exportLoading: false, // 导出模板的loading
      baseUploadRequest: '/soyoungzg/api/psiSaleEntry/importSaleEntryDetail', // 上传请求地址
      downloadFailRequest: '/soyoungzg/api/psiSaleEntry/exportFailedRecord', // 下载导入失败记录请求地址
      ruleForm: {
        endEntryDate: ''
      },
      rules: {
        endEntryDate: [{ required: true, message: '必填信息', trigger: 'change' }]
      },
      dataDetails: [],
      isShowTable: false // 是否显示表格
    };
  },
  computed: {
    uploadRequest() {
      return `${this.baseUploadRequest}?distributorId=${this.distributorId}&endEntryDate=${parseTime(this.ruleForm.endEntryDate, '{y}-{m}-{d}')}`;
    },
    isDisabledUpload() {
      return !this.ruleForm.endEntryDate;
    },
    // 正常数据
    normalData() {
      return this.dataDetails.filter((item) => item.saleNum >= 0);
    },
    // 异常数据
    abnormalData() {
      return this.dataDetails.filter((item) => item.saleNum < 0);
    }
  },
  mounted() {
    this.getLastSaleEntryByShopId();
  },
  activated() {
    this.getLastSaleEntryByShopId();
  },
  methods: {
    // 获取数据录入开始日期
    getLastSaleEntryByShopId() {
      psiSaleEntryGetLastSaleEntryByShopId(this.distributorId).then((res) => {
        if (res && res.success) {
          this.startDate = res.data;
        }
      });
    },
    // 分销商存货数据
    exportInventoryData() {
      const { endEntryDate } = this.ruleForm;
      if (!endEntryDate) {
        return this.$message.warning('请先选择数据录入截止日期');
      }
      this.exportLoading = true;
      const data = {
        distributorId: this.distributorId,
        endEntryDate: parseTime(endEntryDate, '{y}-{m}-{d}')
      };
      psiSaleEntryExportDistributorCommodityInventory(data)
        .then((res) => {
          try {
            const resObj = JSON.parse(new TextDecoder('utf-8').decode(new Uint8Array(res)));
            if (resObj.success) {
              this.$message.success(resObj.msg);
            } else {
              this.$message.error(resObj.msg || '系统错误');
            }
          } catch (error) {
            // 获取指标名称
            download(res, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', `${this.distributorShopName}存货数据下载-${parseTime(Date.now(), '{y}-{m}-{d}-{h}:{i}:{s}')}.xlsx`);
          }
        })
        .finally(() => {
          this.exportLoading = false;
        });
    },
    uploadFileSuccess(data) {
      this.dataDetails = data.map((item) => ({ ...item, remark: '' }));
      const ref = this.$refs.importPage;
      const { importDataFCount = 0, saleNumLessThanZero = 0 } = ref;
      this.isShowTable = importDataFCount === 0 && saleNumLessThanZero > 0;
    },
    uploadFileRemove() {
      this.isShowTable = false;
      this.dataDetails = [];
    },
    // 保存
    onSubmit() {
      this.$refs.ruleForm.validate((valid) => {
        if (!valid) {
          return false;
        }
        const { failNum = 0, fileUrl = '', fileName = '' } = this.$refs.importPage.getUploadRes() || {};
        if (failNum > 0) {
          this.$message.error('请先修正导入模板问题数据');
          return false;
        }
        if (!fileUrl) {
          this.$message.error('请上传文件');
          return false;
        }
        const { ruleForm, distributorId, normalData, abnormalData } = this;
        // 判断abnormalData中的remark是否为空
        const isRemarkEmpty = abnormalData.some((item) => !item.remark);
        if (isRemarkEmpty) {
          this.$message.error('请填写异常数据的原因');
          return false;
        }
        const dataDetails = normalData.concat(abnormalData);
        const { endEntryDate } = ruleForm;
        const params = {
          fileName,
          fileUrl,
          distributorId,
          dataDetails,
          endEntryDate: parseTime(endEntryDate, '{y}-{m}-{d}')
        };
        psiSaleEntrySaveSaleEntryDetaill(params).then((res) => {
          if (res.code === '0') {
            this.$message.success('导入成功');
          } else {
            this.$message.error(res.msg || '文件导入失败');
          }
          this.mixCancel();
        });
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.import {
  display: flex;
  flex-direction: column;
  &-step {
    margin-bottom: 24px;
    text-align: center;
    & > div:nth-of-type(2n) {
      font-size: 48px;
    }
  }
  ::v-deep .import-operate {
    margin-top: 18px;
    & > div:first-child {
      margin: 0;
      width: 130px;
    }
    .import-operate__tip {
      width: 100%;
      padding-left: 130px;
      margin-top: 6px;
    }
  }
}
</style>
