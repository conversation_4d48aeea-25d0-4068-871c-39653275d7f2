<template>
  <div class="app-container">
    <div class="table-container">
      <form @submit.prevent="onSearch" class="filter-list">
        <div class="filter-left">
          <el-button
            @click="add"
            class="add-btn"
            icon="el-icon-plus"
            size="small"
            type="primary"
            >新增客服</el-button
          >
        </div>
        <div class="filter-item">
          <span class="label">客服昵称:</span>
          <div class="content">
            <el-input clearable v-model="filter.staffName"></el-input>
          </div>
        </div>
        <div class="filter-item">
          <span class="label">在线状态:</span>
          <div class="content">
            <el-select clearable v-model="filter.status">
              <el-option
                :key="value"
                :label="label"
                :value="value"
                v-for="{ value, label } of statusOptions"
              ></el-option>
            </el-select>
          </div>
        </div>
        <div class="filter-item">
          <el-button native-type="submit" size="small" type="primary"
            >查询</el-button
          >
          <el-button @click="onReset" size="small">重置</el-button>
        </div>
      </form>
      <el-table
        :data="list"
        element-loading-text="加载中"
        fit
        highlight-current-row
        ref="multipleTable"
        v-loading.body="listLoading"
      >
        <el-table-column
          align="center"
          label="员工名称"
          prop="name"
        ></el-table-column>
        <el-table-column align="center" label="分组">
          <template slot-scope="{ row }">
            {{ row.belongGroupName ? row.belongGroupName : '未分组' }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="在线状态">
          <template slot-scope="{ row, $index }">
            <el-select
              :class="[row.status, 'status-select']"
              size="small"
              :value="row.status"
              @change="
                (val) => {
                  changeStatus(val, $index, row);
                }
              "
            >
              <el-option
                v-for="{ label, value } in statusOptions"
                :key="value"
                :label="label"
                :value="value"
              >
                <div class="options">
                  <span :class="[value, 'options-pre']"></span>
                  <span>{{ label }}</span>
                </div>
              </el-option>
            </el-select>
          </template>
        </el-table-column>
        <el-table-column align="center" label="接待中会话">
          <template slot-scope="{ row }">
            <span
              :class="row.todayOnChatSessionNumber > 0 ? 'highlight' : ''"
              >{{ row.todayOnChatSessionNumber }}</span
            >
          </template>
        </el-table-column>
        <el-table-column
          align="center"
          label="今日接入会话"
          prop="todayChatSessionNumber"
        ></el-table-column>
        <el-table-column
          align="center"
          label="最大接待人数"
          prop="assignQuota"
        ></el-table-column>
        <el-table-column align="center" label="操作" fixed="right">
          <template slot-scope="scope">
            <el-button @click="removeList(scope.row)" size="small" type="text"
              >删除</el-button
            >
            <el-button @click="edit(scope.row)" size="small" type="text"
              >编辑</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          :current-page="pageNo"
          :disabled="listLoading"
          :page-size="pageSize"
          :page-sizes="[10, 20, 30, 40, 50, 100]"
          :total="total"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
          background
          layout="total, sizes, prev, pager, next, jumper"
        ></el-pagination>
      </div>
    </div>
    <el-dialog
      :before-close="() => (dialogVisible = false)"
      :title="currentRow !== null ? '编辑客服' : '新增客服'"
      :visible.sync="dialogVisible"
      width="441px"
    >
      <div class="dialog-item">
        <div class="lable">员工姓名</div>
        <el-select
          :disabled="currentRow !== null"
          clearable
          filterable
          v-model="name"
          value-key="staffId"
        >
          <el-option
            :key="inx"
            :label="item.name"
            :value="item"
            v-for="(item, inx) of staffList"
          ></el-option>
        </el-select>
      </div>
      <div class="dialog-item">
        <div class="lable">客服分组</div>
        <el-select
          clearable
          filterable
          v-model="belongGroupId"
          value-key="staffId"
        >
          <el-option
            :key="inx"
            :label="item.belongGroupName"
            :value="item.belongGroupId"
            v-for="(item, inx) of customerList"
          ></el-option>
        </el-select>
        <el-button @click="groupAssert" type="text">分组维护</el-button>
      </div>
      <div class="dialog-item">
        <div class="lable">最大接待人数</div>
        <el-input class="input" type="number" v-model="assignQuota"></el-input
        >人
        <span class="tag">0-1000人</span>
      </div>
      <span class="dialog-footer" slot="footer">
        <el-button @click="dialogVisible = false" size="small">取 消</el-button>
        <el-button
          @click="currentRow ? updateStaff() : addStaff()"
          size="small"
          type="primary"
          >确 定</el-button
        >
      </span>
    </el-dialog>
    <ChatForwardDialog
      :visible.sync="forwardVisible"
      :dataList="[serviceId]"
      isSupervisor
      title="当前客服有未结束的会话。若切换为离线状态，需要将未结束的会话将转接给其他客服。"
      @success="success"
    />
  </div>
</template>
<script>
import {
  list,
  deleteById,
  staffListPage,
  customerListPage,
  create,
  update,
  fetchStatusOptions,
  updateCsQuota,
  changeStatus
} from '@/api/chat/staff-list';
import pickBy from 'lodash/pickBy';
import ChatForwardDialog from '@/components/ChatForwardDialog';
import pick from 'lodash/pick';
import cloneDeep from 'lodash/cloneDeep';
// import Promotion from '@/components/Promotion';
export default {
  name: 'chat-staff',
  components: {
    ChatForwardDialog
    // Promotion
  },
  data() {
    const initFilter = {
      staffName: '',
      status: ''
    };
    return {
      serviceId: '', // 转接的时候被转接的客服ID
      forwardVisible: false,
      dialogVisible: false,
      initFilter,
      filter: cloneDeep(initFilter),
      list: [],
      currentRow: null, // 当前编辑的数据
      listLoading: true,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      name: '',
      belongGroupId: '',
      assignQuota: '', // 最大接待人数
      grouping: '',
      staffList: [],
      customerList: [], // 客服分组
      statusOptions: []
    };
  },
  computed: {
    // 过滤
    data() {
      return pickBy(this.filter, (val) => !!val);
    }
  },
  mounted() {
    this.fetchData();
    this.fetchStatusOptions();
  },
  watch: {},
  methods: {
    // 转接会话成功
    success(ids) {
      changeStatus('change2Offline', ids[0]).then((res) => {
        this.fetchData();
      });
    },
    changeStatus(e, idx, row) {
      // 切换到离线状态的时候假如有未结束会话需要先转接
      if (e === 'OFFLINE' && row.todayOnChatSessionNumber > 0) {
        this.serviceId = row.id;
        this.forwardVisible = true;
        return;
      }
      // 切换到上线或者忙碌状态可以直接切换
      const status =
        e === 'BUSY'
          ? 'change2Busy'
          : e === 'OFFLINE'
          ? 'change2Offline'
          : 'change2Online';
      changeStatus(status, row.id).then((res) => {
        const list = [...this.list];
        list[idx].status = e;
        this.list = [...list];
      });
    },
    // 分组维护，跳转至分组列表
    groupAssert() {
      this.dialogVisible = false;
      this.$router.push('/chat/chat-group');
    },
    fetchStatusOptions() {
      fetchStatusOptions().then((res) => {
        this.statusOptions = res.data.map(({ label, value }) => ({
          label,
          value
        }));
      });
    },
    staffListPage() {
      const data = {
        pageNo: 1,
        pageSize: 1000
      };
      staffListPage(data).then((res) => {
        const list = res.data.list.map((item) => ({
          name: item.name,
          staffId: item.id
        }));
        this.staffList = list;
      });
    },
    // 获取客服分组数据
    customerListPage() {
      customerListPage().then((res) => {
        const list = res.data.map((item) => ({
          belongGroupName: item.groupName,
          belongGroupId: item.id
        }));
        this.customerList = list;
      });
    },
    // 点击新增按钮
    add() {
      this.dialogVisible = true;
      this.currentRow = null;
      this.name = '';
      this.belongGroupId = '';
      this.assignQuota = '';
      this.staffListPage();
      this.customerListPage();
    },
    // 点击编辑按钮
    edit(row) {
      this.staffListPage();
      this.customerListPage();
      this.dialogVisible = true;
      this.currentRow = row;
      const { id, name, belongGroupId, assignQuota } = row;
      this.name = { staffId: id, name };
      this.belongGroupId = belongGroupId;
      this.assignQuota = assignQuota;
    },
    // 添加客服
    addStaff() {
      const { staffId, name } = this.name;
      if (name === '') {
        this.$message({
          type: 'info',
          message: '请选中要添加的人员'
        });
        return;
      }
      if (!this.isassignQuota(this.assignQuota)) return;
      create({
        staffId,
        name,
        belongGroupId: this.belongGroupId
      })
        .then((res) => {
          return updateCsQuota({
            csId: staffId,
            assignQuota: this.assignQuota
          });
        })
        .then((res) => {
          this.$message({
            type: 'success',
            message: '添加成功！'
          });
          this.dialogVisible = false;
          this.fetchData();
        });
    },
    isassignQuota(num) {
      if (num !== '' && num >= 0 && num <= 1000) return true;
      this.$message.error('请在0-1000区间设置最大接待人数');
      return false;
    },
    // 编辑
    updateStaff() {
      const { id, status } = this.currentRow;
      if (!this.isassignQuota(this.assignQuota)) return;
      update({
        belongGroupId: this.belongGroupId,
        id,
        status
      })
        .then((res) => {
          return updateCsQuota({ csId: id, assignQuota: this.assignQuota });
        })
        .then((res) => {
          this.$message({
            type: 'success',
            message: '修改成功！'
          });
          this.dialogVisible = false;
          this.fetchData();
        });
    },
    // 删除
    removeList(row) {
      this.$confirm('确认删除该客服账号？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          deleteById(row.id).then((response) => {
            this.$message({
              type: 'success',
              message: '删除成功!'
            });
            this.fetchData();
          });
        })
        .catch(() => {});
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.pageNo = 1;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.pageNo = val;
      this.fetchData();
    },

    fetchData() {
      this.listLoading = true;
      const listQuery = pick(this, ['pageNo', 'pageSize', 'data']);
      list(listQuery)
        .then((response) => {
          const { list = [], total = 0 } = response.data || {};
          this.list = list;
          this.total = total;
        })
        .finally(() => {
          this.listLoading = false;
        });
    },
    onSearch() {
      this.pageNo = 1;
      this.pageSize = 10;
      this.fetchData();
    },
    onReset() {
      this.pageNo = 1;
      this.pageSize = 10;
      this.filter = cloneDeep(this.initFilter);
      this.fetchData();
    }
  }
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
.highlight {
  color: #67c23a;
}
.options {
  display: flex;
  align-items: center;
  .options-pre {
    width: 4px;
    height: 12px;
    margin-right: 4px;
    &.BUSY {
      background: #e6a23c;
    }
    &.OFFLINE {
      background: #f56c6c;
    }
    &.ONLINE {
      background: #67c23a;
    }
  }
}

.status-select {
  width: 100px;
  float: right;
  flex: none;
  ::v-deep .el-input--small .el-input__inner {
    color: #fff;
  }
  ::v-deep .el-input--small .el-input__icon {
    color: #fff;
  }
  &.BUSY {
    ::v-deep .el-input__inner {
      border: 1px solid #e6a23c;
    }
    ::v-deep .el-input--small .el-input__inner {
      background: #e6a23c;
    }
  }
  &.OFFLINE {
    ::v-deep .el-input__inner {
      border: 1px solid #f56c6c;
    }
    ::v-deep .el-input--small .el-input__inner {
      background: #f56c6c;
    }
  }
  &.ONLINE {
    ::v-deep .el-input__inner {
      border: 1px solid #67c23a;
    }
    ::v-deep .el-input--small .el-input__inner {
      background: #67c23a;
    }
  }
}
.filter-list {
  margin-top: 10px;
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  .filter-left {
    flex: 1;
    .add-btn {
      margin-left: 22px;
    }
  }
  .filter-item {
    font-size: 14px;
    box-sizing: border-box;
    display: flex;
    margin-bottom: 16px;
    padding: 0 24px;
    text-align: right;
    .label {
      padding-right: 16px;
      white-space: nowrap;
      display: inline-block;
      line-height: 32px;
    }
    .content {
      .el-select {
        width: 100%;
      }
      .el-cascader--medium,
      .el-range-editor--medium.el-input__inner {
        width: 100%;
      }
    }
    &.btns-open {
      display: block;
      width: 100%;
    }
  }
}
.dialog-item {
  display: flex;
  line-height: 44px;
  .lable {
    margin-right: 15px;
    width: 100px;
  }
  .add-btn {
    margin-left: 15px;
  }
  .input {
    width: 200px;
  }
  .tag {
    margin-left: 5px;
    color: #999;
    font-size: 12px;
  }
  ::v-deep input::-webkit-outer-spin-button,
  ::v-deep input::-webkit-inner-spin-button {
    -webkit-appearance: none;
  }
  ::v-deep input[type='number'] {
    -moz-appearance: textfield;
  }
}
</style>
