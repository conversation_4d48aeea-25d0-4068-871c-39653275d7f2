<template>
  <div class="app-container">
    <div class="table-container">
      <div class="tools">
        <el-button type="primary" size="small" @click="onAddClick"
          >新建分组</el-button
        >
      </div>
      <el-table
        :data="list"
        style="width: 100%"
        class="table-wrap"
        v-loading="loading"
      >
        <el-table-column label="分组名称" prop="groupName"></el-table-column>
        <el-table-column label="数量" prop="groupNumber"></el-table-column>
        <el-table-column label="组员" required>
          <template slot-scope="{ row }">{{
            row.customerServiceVOList | filterNames
          }}</template>
        </el-table-column>
        <el-table-column label="操作" fixed="right">
          <template slot-scope="{ row }">
            <div>
              <a @click.prevent="onEditClick(row.id)" class="link-type">编辑</a>
              <a @click.prevent="fetchDeleteGroup(row.id)" class="link-type"
                >删除</a
              >
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          background
          layout="total, sizes, prev, pager, next, jumper"
          :page-sizes="pageSizes"
          @size-change="onSizeChange"
          :total="total"
          :current-page="pageNo"
          @current-change="onPageChange"
        ></el-pagination>
      </div>
    </div>
    <ChatGroupDialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      :groupData="groupData"
      :rules="rules"
      :submitLoaidng="submitLoaidng"
      :loading="groupLoading"
      @submit="fetchCreateOrUpdateGroup"
    ></ChatGroupDialog>
  </div>
</template>

<script>
import {
  fetchGroupListPage,
  fetchChatGroup,
  fetchUpdateGroup,
  fetchDeleteGroup,
  fetchCreateGroup
} from '@/api/chat';
import ChatGroupDialog from '@/components/ChatGroupDialog';
import pick from 'lodash/pick';
class GroupData {
  constructor(options = {}) {
    this.$options = options;
    const { groupName = '', csIdList = [], id } = options;
    this.groupName = groupName;
    this.csIdList = csIdList;
    this.id = id;
  }
  static withRequest(data) {
    const { customerServiceVOList = [], groupName, id } = data;
    return new GroupData({
      csIdList: customerServiceVOList.map(item => item.id),
      id,
      groupName
    });
  }
  get req() {
    return pick(this, ['id', 'csIdList', 'groupName']);
  }
}
export default {
  name: 'chat-group',
  beforeMount() {
    this.fetchListPage();
  },
  methods: {
    onSizeChange(pageSize) {
      this.pageSize = pageSize;
      this.fetchListPage();
    },

    onPageChange(pageNo) {
      this.pageNo = pageNo;
      this.fetchListPage();
    },

    onAddClick() {
      this.dialogVisible = true;
      this.dialogTitle = '新建分组';
      if (this.groupData.id) {
        this.groupData = new GroupData();
      }
    },

    onEditClick(groupId) {
      this.dialogVisible = true;
      this.dialogTitle = '编辑分组';
      this.fetchGroup(groupId);
    },

    fetchListPage() {
      this.loading = true;
      fetchGroupListPage({
        ...pick(this, ['pageNo', 'pageSize']),
        data: {}
      })
        .then(({ data }) => {
          const { list, total } = data;
          this.list = list;
          this.total = total;
        })
        .finally(() => {
          this.loading = false;
        });
    },

    fetchGroup(id) {
      this.groupLoading = true;
      fetchChatGroup(id)
        .then(({ data }) => {
          this.groupData = GroupData.withRequest(data);
        })
        .finally(() => {
          this.groupLoading = false;
        });
    },

    fetchCreateOrUpdateGroup() {
      this.submitLoaidng = true;
      const group = this.groupData;
      const fetchData = group.id ? fetchUpdateGroup : fetchCreateGroup;
      fetchData(group.req)
        .then(res => {
          this.dialogVisible = false;
          this.$message.success(group.id ? '更新成功' : '创建成功');
          this.fetchListPage();
        })
        .finally(() => {
          this.submitLoaidng = false;
        });
    },

    fetchDeleteGroup(id) {
      this.$confirm('确认删除该分组？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        fetchDeleteGroup(id).then(response => {
          this.$message({
            type: 'success',
            message: '删除成功!'
          });
          this.fetchListPage();
        });
      });
    }
  },
  filters: {
    filterNames(list = []) {
      return list.map(item => item.name).join('、');
    }
  },
  components: {
    ChatGroupDialog
  },
  data() {
    return {
      loading: true,
      list: [],
      pageNo: 1,
      total: 1,
      pageSize: 10,
      pageSizes: Object.freeze([10, 30, 50, 100]),
      dialogVisible: false,
      dialogTitle: '',
      groupData: new GroupData(),
      groupLoading: false,
      submitLoaidng: false,
      rules: {
        groupName: [
          { required: true, message: '请填写组名', trigger: 'change' }
        ]
      }
    };
  }
};
</script>

<style lang="scss" scoped>
.table-wrap {
  margin-top: 16px;
}
</style>
