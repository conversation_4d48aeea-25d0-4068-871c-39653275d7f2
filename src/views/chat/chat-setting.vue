<template>
  <div class="app-container">
    <div class="table-container">
      <el-form
        ref="form"
        :model="form"
        label-width="120px"
        size="mini"
        class="form"
      >
        <el-form-item label="渠道关联规则：" prop="composing">
          <div class="channel-box">
            <div class="channel">
              <span class="t1">小程序商城</span>
              <span>
                关联分组：
                <el-tag>全部分组</el-tag>
              </span>
            </div>
            <div class="channel">
              <span class="t1">pc商家端</span>
              <span>
                关联分组：
                <el-tag>全部分组</el-tag>
              </span>
            </div>
          </div>
        </el-form-item>
        <el-form-item label="排队列表规则：" prop="composing">
          <el-radio v-model="form.fifo" label="0"
            >新进入排队的用户优先分配</el-radio
          >
          <br />
          <el-radio v-model="form.fifo" label="1"
            >等待时间长的用户优先分配</el-radio
          >
        </el-form-item>
        <el-form-item label="分组分配规则：" prop="composing">
          <el-radio v-model="form.csGroupFair" label="1"
            >各分组优先级相同</el-radio
          >
          <span class="remark">在全部客服中进行分配</span>
          <br />
          <el-radio v-model="form.csGroupFair" label="0"
            >各分组优先级不同</el-radio
          >
          <span class="remark">先分配给优先级高的分组</span>
          <div class="tip" v-if="form.csGroupFair === '0'">
            <span
              >(拖拽下面的分组可调换顺序,已经删除的分组不会显示在下面,新建的分组自动排在后面)</span
            >
            <div class="channellist hader">
              <div class="item">优先级</div>
              <div class="item">分组名称</div>
            </div>
            <draggable
              :options="{ draggable: '.move' }"
              v-model="form.csGroups"
              @start="drag = true"
              @end="drag = false"
              style="
                width: 300px;
                overflow: auto;
                border: 1px solid #eee;
                border-bottom: 0;
              "
            >
              <div
                class="channellist move"
                v-for="(item, index) of form.csGroups"
                :key="index"
              >
                <div class="item">{{ index + 1 }}</div>
                <div class="item">{{ item.groupName }}</div>
              </div>
            </draggable>
          </div>
        </el-form-item>
        <el-form-item label="客服分配规则：" prop="composing">
          <el-radio v-model="form.assignRule" label="EQUALITY"
            >人人平等模式</el-radio
          >
          <span class="remark">当前接待量，越小优先</span>
          <br />
          <el-radio v-model="form.assignRule" label="QUOTA_ALLOWANCE"
            >接待余量模式</el-radio
          >
          <span class="remark">当前接待量/最大接待量， 越小优先</span>
        </el-form-item>
        <el-form-item label="禁用小程序商城的用户咨询：" label-width="200px">
          <el-switch v-model="switchFlag"></el-switch>
          <span class="tip"
            >开启禁用后，只能使用微信小程序后台接待小程序商城的用户咨询</span
          >
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            size="small"
            @click="onSubmit"
            :loading="loading"
            >保存</el-button
          >
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
import draggable from 'vuedraggable';
import findIndex from 'lodash/findIndex';
import {
  create,
  update,
  getRule,
  customerlistGroupVO,
  SET_MINI_MESSAGE_TRANSFER,
  GET_MINI_MESSAGE_TRANSFER
} from '@/api/chat/chat-setting';
export default {
  name: 'chat-setting',
  components: { draggable },
  data() {
    return {
      switchFlag: false,
      channellist: [],
      cityOptions: ['售前', '售后'],
      channelConfigList: [],
      form: {
        fifo: '1',
        csGroupFair: '1',
        assignRule: 'EQUALITY',
        csGroups: []
      },
      drag: false,
      loading: false,
      id: ''
    };
  },
  watch: {},
  mounted() {
    this.fetchData();
    this.getMiniMessage();
  },
  methods: {
    getMiniMessage() {
      GET_MINI_MESSAGE_TRANSFER().then((res) => {
        this.switchFlag = res.data === '1';
      });
    },
    fetchData() {
      Promise.all([getRule(), customerlistGroupVO()]).then(([q1, q2]) => {
        const { data } = q1;
        this.channelConfigList = [...q2.data] || [];
        if (data) {
          const { assignRule, csGroupFair, csGroups, fifo, id } = data;
          this.id = id;
          this.form = {
            fifo,
            csGroupFair,
            assignRule,
            csGroups: this.filtrationList(csGroups, this.channelConfigList)
          };
        } else {
          this.form.csGroups = [...this.channelConfigList];
        }
      });
    },
    filtrationList(idList, csList) {
      const list = idList.reduce((acc, id) => {
        if (id === '0') {
          return acc.concat({ groupName: '未分组', id });
        }
        const idx = findIndex(csList, { id });
        if (idx === -1) return acc;
        const [item] = csList.splice(idx, 1);
        return acc.concat(item);
      }, []);
      return list.concat(csList);
    },
    onSubmit() {
      this.loading = true;
      const data = {
        ...this.form,
        csGroups: this.form.csGroups.map((item) => item.id)
      };
      const req = this.id ? update : create;
      if (this.id) data.id = this.id;
      SET_MINI_MESSAGE_TRANSFER({
        moduleType: 'MINI_MESSAGE_TRANSFER',
        switchFlag: this.switchFlag ? '1' : '0'
      }).then((res) => {
        this.getMiniMessage();
      });
      req(data)
        .then((res) => {
          this.$message({ message: '保存成功！', type: 'success' });
          this.fetchData();
        })
        .finally(() => {
          this.loading = false;
        });
    }
  }
};
</script>

<style lang="scss" scoped>
.el-icon-edit {
  color: var(--color-primary);
  cursor: pointer;
}
.channel-box {
  .channel {
    display: flex;
    margin-bottom: 10px;
    .tag {
      margin-right: 10px;
    }
    .t1 {
      width: 100px;
    }
  }
}
.channellist {
  display: flex;
  &.hader {
    background: #eee;
    width: 300px;
  }
  &.move {
    border-bottom: 1px solid #eee;
    line-height: 29px;
    .item {
      cursor: move;
    }
  }
  .item {
    flex: 1;
    text-align: center;
  }
}
.remark {
  margin-left: 25px;
  font-size: 12px;
  color: #999;
}
.tip {
  color: #666;
}
</style>
