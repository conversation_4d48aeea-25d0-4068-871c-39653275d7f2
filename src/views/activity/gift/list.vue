<template>
  <div class="app-container wrap">
    <div class="table-container">
      <form @submit.prevent="onSearch" class="commo-search-container commo-search-flex commo-search-margin-bottom10">
        <div class="commo-search-item">
          <span class="commo-search-item-label">赠品名称:</span>
          <div class="commo-search-item-content">
            <el-input v-model.trim="name" size="small"></el-input>
          </div>
        </div>
        <div class="commo-search-item">
          <el-button native-type="submit" type="primary" size="small">查询</el-button>
          <el-button @click="onReset" size="small">重置</el-button>
        </div>
      </form>
      <div class="create">
        <router-link class="link" :to="'/activity/gift/create'">
          <Authority auth="/activity/create/gift">
            <el-button @click="onSearch" type="primary" size="small">新增</el-button>
          </Authority>
        </router-link>
      </div>
      <el-table ref="multipleTable" :data="list" v-loading="listLoading" element-loading-text="加载中" fit highlight-current-row>
        <el-table-column align="center" label="赠品缩略图">
          <template slot-scope="scope">
            <img :src="scope.row.skuCommodityVO.thumbnailUrl" alt v-if="scope.row.skuCommodityVO" class="goods-img" />
          </template>
        </el-table-column>
        <el-table-column align="center" label="赠品名称" prop="name"></el-table-column>
        <el-table-column align="center" label="已发放" prop="issueNum"></el-table-column>
        <el-table-column align="center" label="剩余库存" prop="stock"></el-table-column>
        <el-table-column align="center" label="限领次数">
          <template slot-scope="scope">{{ scope.row.limitNum | parseLimitNum }}</template>
        </el-table-column>
        <el-table-column align="center" label="是否启用">
          <template slot-scope="scope">
            <el-switch :value="scope.row.isEnable | isEnableFormatter" @change="switchEnable(scope.row, scope.$index)"></el-switch>
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作" fixed="right" width="150px">
          <template slot-scope="scope">
            <el-button type="text" size="small">
              <router-link class="link" :to="'/activity/gift/detail/' + scope.row.id">
                <Authority auth="/activity/view/gift">
                  <el-button type="text">查看</el-button>
                </Authority>
              </router-link>
            </el-button>
            <el-button type="text" size="small">
              <router-link class="link" :to="'/activity/gift/edit/' + scope.row.id">
                <Authority auth="/activity/edit/gift">
                  <el-button type="text">编辑</el-button>
                </Authority>
              </router-link>
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="pageNo" :page-sizes="[10, 20, 30, 40, 50, 100]" :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper" :total="total" :disabled="listLoading"></el-pagination>
      </div>
    </div>
  </div>
</template>
<script>
import { list, disable, enable } from '@/api/activity/gift/list';
export default {
  name: 'activity-gift-list',
  data() {
    return {
      name: '',
      list: [],
      listLoading: true,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      currentId: '',
      activityIdResult: []
    };
  },
  filters: {
    isEnableFormatter(isEnable) {
      let result = isEnable;
      if (isEnable === '1') {
        result = true;
      } else if (isEnable === '0') {
        result = false;
      }
      return result;
    },
    parseLimitNum(limitNum) {
      let result = limitNum;
      if (limitNum === 0) {
        result = '不限次数';
      }
      return result;
    }
  },
  mounted() {
    this.fetchData();
  },
  activated() {
    this.fetchData();
  },
  methods: {
    switchEnable(row, index) {
      const list = [...this.list];
      if (row.isEnable === '1' || row.isEnable === true) {
        disable(row.id).then(() => {
          list[index].isEnable = false;
        });
      } else {
        enable(row.id).then(() => {
          list[index].isEnable = true;
        });
      }
      this.list = list;
    },
    handleDelete(id) {
      this.dialogVisible = true;
      this.currentId = id;
    },
    fetchData() {
      this.listLoading = true;
      const { pageNo, pageSize, name } = this;
      list({ pageNo, pageSize, data: { name } })
        .then((response) => {
          if (response.data) {
            this.list = response.data.list;
            this.total = response.data.total;
          }
        })
        .finally(() => {
          this.listLoading = false;
        });
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.pageNo = 1;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.pageNo = val;
      this.fetchData();
    },
    onSearch() {
      this.pageNo = 1;
      this.pageSize = 10;
      this.fetchData();
    },
    onReset() {
      this.pageNo = 1;
      this.pageSize = 10;
      this.name = '';
      this.fetchData();
    }
  }
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
@import 'src/styles/goods-table.scss';
.create {
  margin-bottom: 8px;
}
.filter-item .link {
  margin-left: 10px;
}
.goods-img {
  width: 60px;
  height: 60px;
}
</style>
