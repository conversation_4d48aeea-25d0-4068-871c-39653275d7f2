<template>
  <div class="app-container" v-loading="loading">
    <div class="table-container">
      <div class="title">{{ title }}</div>
      <div class="content">
        <el-form
          :model="ruleForm"
          :rules="rules"
          ref="ruleForm"
          label-width="150px"
          class="demo-ruleForm"
        >
          <el-form-item label="赠品名称" prop="name">
            <el-input v-model.trim="ruleForm.name" :disabled="isDetail"></el-input>
          </el-form-item>
          <div class="add-commodity">
            <el-form-item label="适用商品">
              <div v-if="isEdit">
                <add-gift
                  :commodityType="'addGift'"
                  :operation="'edit'"
                  v-model="ruleForm.commodityList"
                  @updateTable="updateCurrentTable"
                  :multiple="false"
                  isgift
                ></add-gift>
              </div>
              <div v-if="isDetail">
                <add-gift
                  :commodityType="'addGift'"
                  v-model="ruleForm.commodityList"
                  @updateTable="updateCurrentTable"
                  :operation="'detail'"
                  :multiple="false"
                  isgift
                ></add-gift>
              </div>
              <div v-if="!isDetail && !isEdit">
                <add-gift
                  :commodityType="'addGift'"
                  v-model="ruleForm.commodityList"
                  @updateTable="updateCurrentTable"
                  :multiple="false"
                  isgift
                ></add-gift>
              </div>
              <div class="validate-tips" v-if="showValidate">请选择商品</div>
            </el-form-item>
          </div>
          <el-form-item label="赠品库存" prop="stock">
            <el-input
              v-model.number="ruleForm.stock"
              :disabled="isDetail"
              type="number"
            ></el-input>
          </el-form-item>
          <div class="choice-container">
            <el-form-item label="每人限领次数" prop="limitNumChoice">
              <el-radio-group
                v-model="ruleForm.limitNumChoice"
                :disabled="isDetail"
              >
                <el-radio label="0">不限次数</el-radio>
                <el-radio label="1">限领</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item class="limit-num">
              <el-input
                v-model.number="limitNum"
                :disabled="isDetail || ruleForm.limitNumChoice !== '1'"
                type="number"
              ></el-input>
              <span class="time">件/人</span>
            </el-form-item>
          </div>
          <el-form-item v-if="!isDetail">
            <el-button
              type="primary"
              @click="submitForm('ruleForm')"
              :loading="saveLoading"
              >确认</el-button
            >
            <router-link class="link" :to="'/activity/gift'">
              <el-button>取消</el-button>
            </router-link>
          </el-form-item>
          <el-form-item v-if="isDetail">
            <router-link class="link" :to="'/activity/gift'">
              <el-button>返回</el-button>
            </router-link>
          </el-form-item>
        </el-form>
      </div>
    </div>
  </div>
</template>

<script>
import { create, update, getById } from '@/api/activity/gift/list';
import AddGift from '@/components/AddCommodity';
import { validateInteger, validateMaxNum } from '@/common/validator';
export default {
  name: 'Gift',
  props: {
    id: String,
    isEdit: {
      type: Boolean,
      default: false
    },
    isDetail: {
      type: Boolean,
      default: false
    }
  },
  created() {
    this.initData();
  },
  components: {
    'add-gift': AddGift
  },
  data() {
    const validateLimitNum = (_, value, callback) => {
      if (value === '1') {
        if (!this.limitNum && this.limitNum !== 0) {
          callback(new Error('请输入限领次数'));
          return;
        }
        const val = this.limitNum - 0;
        if (typeof val !== 'number') {
          return callback(new Error('请输入数字'));
        }
        if (this.limitNum > 100000000) {
          callback('数据大于1个亿，请重新输入');
          return;
        }
        if (this.limitNum < 1) {
          callback('数据小于1,请重新输入');
          return;
        }
        let str = this.limitNum;
        if (typeof str === 'number') {
          str = str.toString();
        }
        const reg = /^([1-9]\d*|[0]{1,1})$/;
        if (!reg.test(str.trim())) {
          callback('请输入整数');
          return;
        }
      }
      callback();
    };
    return {
      title: '新增赠品',
      loading: false,
      saveLoading: false,
      listEdit: {}, // 编辑的时候从get接口获取数据
      ruleForm: {
        name: '',
        stock: null,
        commodityList: [], // 适用商品列表
        limitNumChoice: '' // 每人限领次数的选择
      },
      limitNum: '', // 每人限领次数的输入框的值
      rules: {
        name: [
          { required: true, message: '请输入活动名称', trigger: 'blur' },
          { max: 30, message: '名称长度不能超过30', trigger: 'blur' }
        ],
        stock: [
          { required: true, message: '请输入赠品库存', trigger: 'blur' },
          { validator: validateInteger, trigger: 'blur' },
          { validator: validateMaxNum, trigger: 'blur' }
        ],
        limitNumChoice: [
          { required: true, message: '请选择限领次数', trigger: 'change' },
          { validator: validateLimitNum, trigger: 'change' }
        ]
        // commodityList: [
        //   { required: true, message: '请选择适用商品', trigger: 'change' }
        // ]
      },
      showValidate: false // 控制选择商品的校验的提示的显示和隐藏
    };
  },
  methods: {
    // 用来接收添加商品组件的实时展示在页面上的列表
    updateCurrentTable(commodityList) {
      this.ruleForm.commodityList = [...commodityList];
      this.ruleForm.commodityList.length === 0
        ? (this.showValidate = true)
        : (this.showValidate = false);
    },
    initData() {
      if (this.id) {
        this.isDetail ? (this.title = '赠品详情') : (this.title = '修改赠品');
        this.fetchCommodity();
      }
    },
    fetchCommodity() {
      this.loading = true;
      return getById(this.id)
        .then((response) => {
          const { name, stock, limitNum } = { ...response.data };
          const ruleForm = {
            name,
            stock,
            commodityList: [{ ...response.data }]
          };
          if (limitNum === 0) {
            ruleForm.limitNumChoice = '0';
          } else {
            ruleForm.limitNumChoice = '1';
            this.limitNum = limitNum;
          }
          this.ruleForm = ruleForm;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (!valid) {
          this.ruleForm.commodityList.length === 0
            ? (this.showValidate = true)
            : (this.showValidate = false);
          return false;
        }

        if (this.ruleForm.commodityList.length === 0) {
          this.showValidate = true;
          return false;
        } else {
          this.showValidate = false;
        }

        const { commodityList, name, stock } = { ...this.ruleForm };
        const postData = {
          commodityId: commodityList[0].skuCommodityVO.commodityId,
          name,
          skuId: commodityList[0].skuCommodityVO.id,
          stock: stock
        };
        this.ruleForm.limitNumChoice === '1'
          ? (postData.limitNum = this.limitNum)
          : (postData.limitNum = 0);
        if (this.isEdit) {
          postData.id = this.id;
        }
        const request = this.isEdit ? update : create;
        this.saveLoading = true;
        request(postData)
          .then((response) => {
            this.$message.success('保存成功');
            this.$back({ path: '/activity/gift' });
          })
          .finally(() => {
            this.saveLoading = false;
          });
      });
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.app-container {
  padding: 20px;
  .table-container {
    background: #fff;
    padding: 0;
    .title {
      font-size: 16px;
      font-weight: 500;
      border-bottom: 1px solid #e8e8e8;
      padding: 18px 30px;
      font-size: 16px;
      user-select: none;
    }
    .content {
      padding: 18px 30px;
      .line {
        text-align: center;
      }
      .tips {
        color: #606266;
        font-size: 12px;
      }
      .rule-detail {
        ::v-deep .el-input {
          width: 90%;
        }
      }
    }
    .choose-time {
      ::v-deep .el-input {
        width: 100%;
      }
    }

    .add-commodity {
      .validate-tips {
        color: var(--color-danger);
        font-size: 12px;
        line-height: 1;
      }

      ::v-deep .el-form-item__label::before {
        content: '*';
        color: var(--color-danger);
        margin-right: 4px;
      }
    }

    .choice-container {
      display: flex;
      .limit-num {
        ::v-deep .el-form-item__content {
          margin-left: 10px !important;
          display: flex;
          .time {
            width: 50px;
            margin-left: 10px;
          }
        }
      }
    }
  }
}
</style>
