::v-deep {
  .box-card {
    width: 60px;
    height: 60px;
    margin-right: 0;
    margin-bottom: 0;
  }
  .imgbox .list {
    width: 60px;
    margin-right: 0;
    margin-bottom: 0;
    .card .card__body i {
      font-size: 15px;
    }
  }
  .img-wrap {
    height: 60px;
  }
  .default_image-box--back {
    font-size: 15px;
  }
  .el-table.el-table--small td.table-cell {
    .cell {
      padding: 0 !important;
    }
  }
  .el-table__fixed-right-patch {
    visibility: hidden;
  }
}
.form-item-box {
  margin: 10px 0;
  display: flex;
  justify-content: center;
  align-items: center;
}
.goods-img {
  width: 60px;
  height: 60px;
}
.remarks {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  -webkit-line-clamp: 3;
}
.table-colums-wrap {
  display: flex;
  flex-direction: column;
}
.table-colums-custom {
  display: flex;
  flex-direction: column;
  height: 140px;
  border-bottom: 1px solid #ebeef5;
  padding: 16px 8px;
  &:last-child {
    border-bottom: none;
  }
  &.table-colums-custom-small {
    height: 110px;
  }
}
.sku-price {
  font-size: 12px;
  color: #de3509;
  > span {
    margin-right: 10px;
  }
}
