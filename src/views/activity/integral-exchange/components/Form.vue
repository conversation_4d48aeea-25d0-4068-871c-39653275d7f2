<template>
  <div>
    <el-form :model="form" :rules="rules" label-width="120px" ref="form" :disabled="isDetail">
      <div class="app-container">
        <div class="part part-activity">
          <div class="content">
            <el-form-item label="活动名称：" prop="name">
              <el-input v-model.trim="form.name" maxlength="20" show-word-limit placeholder="请输入活动名称"></el-input>
            </el-form-item>
            <el-form-item label="活动兑换时间：" prop="timeData">
              <el-date-picker :picker-options="pickerOptions" :default-time="['00:00:00', '23:59:59']" end-placeholder="结束时间" range-separator="至" start-placeholder="开始时间" type="datetimerange" value-format="timestamp" v-model.trim="form.timeData" @change="handleDateChange"></el-date-picker>
            </el-form-item>
            <el-form-item label="兑换积分类型：" prop="subtype">
              <el-radio-group v-model="form.subtype" @change="handleSubType" :disabled="isEdit || isDetail">
                <el-radio label="NORMAL">大贸积分</el-radio>
                <el-radio label="GLOBAL">海淘积分</el-radio>
              </el-radio-group>
              <el-form-item label-width="0">
                <div class="integral-tip">
                  <p><i class="el-icon-warning-outline"></i> {{ tip }}</p>
                </div>
              </el-form-item>
            </el-form-item>
            <el-form-item label="活动类型：" prop="subExtType">
              <el-radio-group v-model="form.subExtType" :disabled="isEdit || isDetail" @change="handleSubExtType">
                <p class="radio-block">
                  <el-radio label="DELIVERY_FEE_COUPON">优惠券活动-运费券</el-radio>
                </p>
                <p class="radio-block">
                  <el-radio label="VIRTUAL_COUPON" style="margin-right: 10px">优惠券活动-核销卡券</el-radio>
                </p>
                <p class="radio-block">
                  <el-radio label="COMMODITY_ACTIVITY">商品活动</el-radio>
                </p>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="是否限购：" prop="izLimitNum">
              <el-radio-group v-model="form.izLimitNum" @change="handleIzLimitNum">
                <p class="radio-block">
                  <el-radio label="0">不限购</el-radio>
                </p>
                <p class="radio-block">
                  <el-radio label="1">限购</el-radio>
                  <span class="form-span">活动期间，每人单件商品最多可限制兑换</span>
                  <el-form-item label prop="limitNum" class="form-item-margin">
                    <el-input v-model="form.limitNum" placeholder="请输入正整数" v-int style="width: 200px" :disabled="form.izLimitNum !== '1'" oninput="if(value <= 0) value=''">
                      <template slot="append">件</template>
                    </el-input>
                  </el-form-item>
                </p>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="活动使用人群：" prop="customerInfo.customerType">
              <el-radio-group v-model="form.customerInfo.customerType">
                <el-radio label="ALL">所有分销商</el-radio>
                <el-radio label="SUB_DISTRIBUTOR">部分分销商</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="部分分销商选择：" prop="customerInfo.customerSubtype" v-if="form.customerInfo.customerType === 'SUB_DISTRIBUTOR'">
              <el-radio-group v-model="form.customerInfo.customerSubtype">
                <p class="radio-block">
                  <el-radio label="SELECT">选择分销商</el-radio>
                  <selectDistributor v-bind="importOptions" ref="selectDistributors" @onSuccess="sure" v-show="form.customerInfo.customerSubtype === 'SELECT'"></selectDistributor>
                </p>
                <p class="radio-block">
                  <el-radio label="FILE_IMPORT">导入分销商</el-radio>
                  <importDistributors v-bind="importOptions" ref="importDistributors" @onSuccess="onSuccess" v-show="form.customerInfo.customerSubtype === 'FILE_IMPORT'"></importDistributors>
                </p>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="活动备注：">
              <el-input class="my-textarea" type="textarea" :autosize="{ minRows: 4, maxRows: 6 }" placeholder="用于运营侧备注说明" maxlength="200" show-word-limit v-model="form.remarks"> </el-input>
            </el-form-item>
            <el-form-item label="运费设置：" prop="deliveryFeeInfo.izDeliveryFee" v-if="form.subtype === 'NORMAL' && form.subExtType === 'COMMODITY_ACTIVITY'">
              <el-radio-group v-model="form.deliveryFeeInfo.izDeliveryFee">
                <div class="radio-block-basic">
                  <el-radio label="1">包邮</el-radio>
                </div>
                <div class="radio-block-basic">
                  <el-radio label="0">自定义运费（每单收取固定运费）</el-radio>
                  <template v-if="form.deliveryFeeInfo.izDeliveryFee === '0'">
                    <p class="form-item">
                      <el-form-item label prop="deliveryFeeInfo.normalDeliveryFee" class="form-item-margin">
                        <span class="form-span">自营品牌-运费</span>
                        <el-input v-model="form.deliveryFeeInfo.normalDeliveryFee" placeholder="请输入" v-money style="width: 200px"></el-input>
                        <span>元</span>
                      </el-form-item>
                    </p>
                    <p class="form-item">
                      <el-form-item label prop="deliveryFeeInfo.internationDeliveryFee" class="form-item-margin">
                        <span class="form-span">国际品牌-运费</span>
                        <el-input v-model="form.deliveryFeeInfo.internationDeliveryFee" placeholder="请输入" v-money style="width: 200px"></el-input>
                        <span>元</span>
                      </el-form-item>
                    </p>
                  </template>
                </div>
              </el-radio-group>
            </el-form-item>
            <template v-if="form.subExtType === 'COMMODITY_ACTIVITY'">
              <el-form-item label="积分计算方式：" prop="izPointMultiple">
                <el-radio-group v-model="form.izPointMultiple" @change="handleMultiple">
                  <el-radio label="1">所需积分=商品商城价*兑换倍率</el-radio>
                  <el-radio label="0">自定义积分</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="兑换倍率设置：" prop="pointMultiple" v-if="form.izPointMultiple === '1'">
                <el-input v-model="form.pointMultiple" placeholder="支持两位小数" v-money @input="inventoryChange" style="width: 200px">
                  <template slot="append">倍</template>
                </el-input>
              </el-form-item>
            </template>
            <el-form-item :label="form.subExtType === 'COMMODITY_ACTIVITY' ? '活动商品选择：' : '券选择：'" prop="commodityRelationList">
              <!-- 选择优惠券 -->
              <AddCouponAdvanced v-bind="couponOptions" v-model.trim="form.commodityRelationList" @removeTable="remove" @updateTable="clearValidate('commodityRelationList', $event)" v-if="form.subExtType !== 'COMMODITY_ACTIVITY'"></AddCouponAdvanced>
              <!-- 选择海淘商品 -->
              <AddOutsideCommodity
                v-bind="outsideCommodityOptions"
                v-model.trim="form.commodityRelationList"
                @removeTable="remove"
                @updateIdentifying="updateIdentifying"
                @save="clearValidate('commodityRelationList', $event)"
                v-if="form.subtype === 'GLOBAL' && form.subExtType === 'COMMODITY_ACTIVITY'"
              ></AddOutsideCommodity>
              <!-- 选择大贸商品 -->
              <AddCommodity
                v-bind="commodityOptions"
                v-model="form.commodityRelationList"
                @removeTable="remove"
                @updateEnable="updateEnable"
                @updateIdentifying="updateIdentifying"
                @updateTable="clearValidate('commodityRelationList', $event)"
                v-if="form.subtype === 'NORMAL' && form.subExtType === 'COMMODITY_ACTIVITY'"
              ></AddCommodity>
            </el-form-item>
          </div>
        </div>
      </div>
    </el-form>
    <div class="part footer">
      <el-button @click="onCancel">取消</el-button>
      <el-button :loading="loading" @click="onSubmit" type="primary" v-if="!isDetail">保存</el-button>
    </div>
  </div>
</template>

<script>
import { creditActivityExchangeCreate, creditActivityExchangeGetForPC, creditActivityExchangeUpdate } from '@/api/activity/integral';
import importDistributors from '@/components/ImportDistributors';
import selectDistributor from '@/components/selectDistributor';
import AddCouponAdvanced from './AddCouponAdvanced';
import AddOutsideCommodity from './AddOutsideCommodity';
import AddCommodity from './AddCommodity';
import pickBy from 'lodash/pickBy';
import omit from 'lodash/omit';
export default {
  name: 'integral-exchange-form',
  components: {
    AddCommodity,
    importDistributors,
    selectDistributor,
    AddCouponAdvanced,
    AddOutsideCommodity
  },
  provide() {
    return {
      form: this // 孙组件必要时刻可以读取信息，以及调用父组件方法
    };
  },
  props: {
    id: String,
    status: String,
    isAdd: {
      type: Boolean,
      default: false
    },
    isCopy: {
      type: Boolean,
      default: false
    },
    isEdit: {
      type: Boolean,
      default: false
    },
    isDetail: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      direction: 'twoPeople',
      loading: false,
      form: {
        name: '', // 活动名称
        timeData: [], // 活动时间
        oldTimeData: [],
        subtype: 'NORMAL', // 兑换积分类型
        subExtType: 'DELIVERY_FEE_COUPON', // 活动类型
        izLimitNum: '0', // 是否限购
        limitNum: '', // 兑换限制
        izPointMultiple: '1', // 积分计算方式
        pointMultiple: '', // 积分倍数
        remarks: '', // 备注
        customerInfo: {
          customerType: 'ALL', // 适用客户类型
          customerSubtype: 'SELECT', // 部分分销商选择leix
          subtype: 'CUSTOMER_TYPE_DEFINE', // 关联分销商方式
          dataList: [], // 关联分销商数据
          fileBusiSeq: '' // 文件导入流水号
        },
        deliveryFeeInfo: {
          izDeliveryFee: '1', // 是否包邮
          normalDeliveryFee: '', // 自营品牌-运费
          internationDeliveryFee: '' // 国际品牌-运费
        },
        commodityRelationList: [] // 活动商品/券
      },
      rules: {
        name: [{ required: true, message: '请输入活动名称', trigger: 'blur' }],
        timeData: [{ required: true, message: '请选择活动时间', trigger: 'change' }],
        subtype: [{ required: true, message: '请选择兑换积分类型', trigger: 'change' }],
        subExtType: [{ required: true, message: '请选择活动类型', trigger: 'change' }],
        izLimitNum: [{ required: true, message: '请选择是否限购', trigger: 'change' }],
        limitNum: [
          {
            required: true,
            validator: (_, value, callback) => {
              if (this.form.izLimitNum === '1') {
                value ? callback() : callback('必选信息');
              } else {
                callback();
              }
            },
            trigger: 'blur'
          }
        ],
        izPointMultiple: [{ required: true, message: '请选择积分计算方式', trigger: 'change' }],
        pointMultiple: [
          {
            required: true,
            validator: (_, value, callback) => {
              if (!value) {
                callback('请输入积分倍数');
              } else if (Number(value) === 0) {
                callback('积分倍数不能为0');
              } else {
                callback();
              }
            },
            trigger: ['blur', 'change']
          }
        ],
        'customerInfo.customerType': [{ required: true, message: '请选择活动使用人群', trigger: 'change' }],
        'customerInfo.customerSubtype': [
          { required: true, message: '必选信息', trigger: 'change' },
          {
            validator: (_, value, callback) => {
              const target = value === 'SELECT' ? '请选择分销商' : '请导入分销商';
              const condition = value === 'SELECT' ? this?.form?.customerInfo?.dataList?.length : this?.form?.customerInfo?.fileUrl;
              if (!condition) {
                callback(target);
              } else {
                callback();
              }
            },
            trigger: 'change'
          }
        ],
        'deliveryFeeInfo.izDeliveryFee': [{ required: true, message: '必选信息', trigger: 'change' }],
        'deliveryFeeInfo.normalDeliveryFee': [{ required: true, message: '必填信息', trigger: 'blur' }],
        'deliveryFeeInfo.internationDeliveryFee': [{ required: true, message: '必填信息', trigger: 'blur' }],
        commodityRelationList: [{ required: true, message: '必选信息', trigger: 'change' }],
        bizImgUrl: [{ required: true, message: '请上传活动主图', trigger: 'change' }],
        bizName: [{ required: true, message: '必选信息', trigger: 'blur' }],
        point: [
          {
            required: true,
            validator: (_, value, callback) => {
              if (!value || Number(value) <= 0) {
                callback('请输入正确的兑换积分');
              } else {
                callback();
              }
            },
            trigger: 'blur'
          }
        ],
        stock: [{ required: true, message: '必选信息', trigger: 'blur' }]
      },
      subtypeMap: {
        CUSTOMER_TYPE_DEFINE: 'csIds',
        CUSTOMER_TYPE_CS_ORG: 'organizationIds',
        CUSTOMER_TYPE_ACTIVE_DEGREE: 'activeDegreeIds',
        CUSTOMER_TYPE_PURCHASE_CHANNEL: 'purchaseChannelIds',
        CUSTOMER_TYPE_DISTRIBUTOR_GRADE: 'distributorGradeIds',
        CUSTOMER_TYPE_CUSTOMER_ATTRIBUTE: 'customerAttributeIds'
      }
    };
  },
  computed: {
    importOptions() {
      return {
        source: 'integralExchange',
        relationData: this.form.customerInfo
      };
    },
    // 优惠券
    couponOptions() {
      return {
        editable: true,
        isAdd: this.isAdd,
        isDetail: this.isDetail,
        isCopy: this.isCopy,
        showContent: true,
        isShowCoupon: false,
        isPayGift: false,
        couponType: this.form.subExtType === 'DELIVERY_FEE_COUPON' ? 'DELIVERY_FEE' : 'VERIFICATION_CARD',
        rules: this.rules,
        source: 'integralExchange'
      };
    },
    // 海淘商品
    outsideCommodityOptions() {
      return {
        editable: true,
        isAdd: this.isAdd,
        isDetail: this.isDetail,
        isCopy: this.isCopy,
        isShowCommodity: false,
        rules: this.rules,
        source: 'integralExchange',
        bizTagNum: this.form.bizTagNum || 0,
        izPointMultiple: this.form.izPointMultiple,
        pointMultiple: this.form.pointMultiple
      };
    },
    // 大贸商品
    commodityOptions() {
      return {
        isAdd: this.isAdd,
        isDetail: this.isDetail,
        isCopy: this.isCopy,
        labelTitle: '选择商品',
        source: 'integralExchange',
        izPointMultiple: this.form.izPointMultiple,
        pointMultiple: this.form.pointMultiple,
        bizTagNum: this.form.bizTagNum || 0,
        rules: this.rules,
        isShowCommodity: false
      };
    },
    tip() {
      return this.form.subtype === 'NORMAL' ? '大贸积分兑换链路：走线上可以进行兑换使用' : '海淘积分兑换链路：走的是线下人工进行使用，然后在兑换订单中进行人工核销；';
    },
    pickerOptions() {
      return {
        ...this.mixPickerOptions,
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7;
        }
      };
    }
  },
  methods: {
    // 移除校验提示
    clearValidate(formProp) {
      this.$refs['form'].clearValidate(formProp);
    },
    initData() {
      this.id && this.getData();
    },
    // 获取活动详情
    getData() {
      creditActivityExchangeGetForPC(this.id).then((res) => {
        const { data = {} } = res;
        const { customerInfo = {} } = data;
        const key = this.subtypeMap[customerInfo.subtype];
        if (key) {
          customerInfo[key] = customerInfo?.dataList || [];
        }
        const { isCopy } = this;
        // 表单数据赋值
        Object.assign(this.form, data || {}, {
          timeData: [data.startDate, data.endDate],
          oldTimeData: [data.startDate, data.endDate],
          commodityRelationList:
            data?.commodityRelationList.map((item) => {
              const { bizId, bizImgUrl, commodityRelationDetailList } = item;
              const imgUrl = bizImgUrl;
              const commodityRelationDetailListModified = commodityRelationDetailList.map((i) => {
                const { leaveStock = '', bizId = null, bizTag = '', usableStock = 0, usedStock = 0 } = i;
                return {
                  ...i,
                  leaveStock,
                  id: bizId,
                  bizTag: isCopy ? '' : bizTag,
                  usableStock: isCopy ? 0 : usableStock,
                  usedStock: isCopy ? 0 : usedStock
                };
              });
              return {
                ...item,
                id: bizId,
                imgUrl,
                commodityRelationDetailList: commodityRelationDetailListModified
              };
            }) || [],
          customerInfo: {
            ...customerInfo,
            customerNum: isCopy ? 0 : customerInfo.customerNum,
            fileUrl: isCopy ? '' : customerInfo.fileUrl
          }
        });
      });
    },
    // 获取参数
    getParams() {
      // 复制时去除ID
      if (this.isCopy) {
        this.form.activityId = null;
      }
      const { timeData, subExtType, subtype, customerInfo } = this.form;
      const [startDate, endDate] = timeData;
      // 商品/券
      const commodityRelationList = this.form.commodityRelationList.map((item) => {
        const { id, imgUrl, commodityRelationDetailList, bizName, mainId = null, barcode = null } = item;
        const dataType = subExtType === 'COMMODITY_ACTIVITY' ? 'GOODS' : 'COUPON';
        const bizImgUrl = imgUrl;
        const commodityRelationDetailListModified = commodityRelationDetailList.map((i) => {
          const { skuBarcode = null, id = null, point = '', leaveStock = '', itemId = null, izEnable = null, bizTag = '' } = i;
          const bizCode = subtype === 'NORMAL' ? skuBarcode : barcode;
          const bizId = subtype === 'NORMAL' ? id : null;
          return {
            leaveStock: izEnable === '0' && !leaveStock ? 0 : leaveStock,
            bizCode,
            bizId,
            itemId: this.isCopy ? null : itemId,
            izEnable,
            bizTag,
            point: izEnable === '0' && !point ? 0 : point
          };
        });
        return {
          bizId: id,
          dataType,
          bizName,
          bizImgUrl,
          mainId: this.isCopy ? null : mainId,
          commodityRelationDetailList: commodityRelationDetailListModified
        };
      });
      let params = pickBy(
        {
          ...this.form,
          customerInfo: {
            ...customerInfo,
            fileUrl: null
          },
          startDate,
          endDate,
          commodityRelationList
        },
        (val) => !!val
      );
      // 排除不需要的字段
      const omitKey = ['timeData', 'oldTimeData', 'customerInfo.csIds', 'customerInfo.activeDegreeIds', 'customerInfo.distributorGradeIds', 'customerInfo.organizationIds', 'customerInfo.purchaseChannelIds'];
      params = omit(params, omitKey);
      return params;
    },
    // 封装验证函数
    submitForm(formUser) {
      return new Promise((resolve, reject) => {
        this.$refs[formUser].validate((valid, object) => {
          if (valid) {
            resolve();
          } else {
            reject(object);
          }
        });
      });
    },
    // 提交
    onSubmit() {
      this.loading = true;
      Promise.all([this.submitForm('form')])
        .then(() => {
          const params = this.getParams();
          const request = this.id && !this.isCopy ? creditActivityExchangeUpdate : creditActivityExchangeCreate;
          request(params)
            .then((res) => {
              if (res.code === '0') {
                this.$message.success(res.msg);
                this.$back({ path: '/activity/integral-exchange/list' });
              }
            })
            .catch((err) => {
              console.log(err);
            })
            .finally(() => {
              this.loading = false;
            });
        })
        .catch((object) => {
          console.log('error submit!!', object);
          this.loading = false;
        });
    },
    // 取消
    onCancel() {
      this.$back();
    },
    // 积分计算方式切换
    handleMultiple() {
      this.setPoitn();
    },
    // 兑换积分类型切换
    handleSubType(val) {
      if (this.form.subExtType !== 'COMMODITY_ACTIVITY') return;
      this.form.commodityRelationList = [];
    },
    // 活动类型切换
    handleSubExtType() {
      this.form.commodityRelationList = [];
    },
    // 限购类型切换
    handleIzLimitNum(v) {
      if (v === '0') {
        this.clearValidate('limitNum');
      }
    },
    // 导入分销商成功回调
    onSuccess(data) {
      Object.assign(this.form.customerInfo, {
        fileBusiSeq: data.busiSeq,
        fileUrl: data.fileUrl
      });
      this.clearValidate('customerInfo.customerSubtype');
    },
    // 选择分销商成功回调
    sure(data) {
      // 处理选择分销商客户类型数据
      const type = data.subtype;
      const dataList = type ? data[this.subtypeMap[type]] || [] : [];
      Object.assign(this.form.customerInfo, data, {
        subtype: type,
        dataList
      });
      this.clearValidate('customerInfo.customerSubtype');
    },
    inventoryChange() {
      this.setPoitn();
    },
    setPoitn() {
      if (this.form.subExtType === 'COMMODITY_ACTIVITY' && this.form.izPointMultiple === '1' && this.form.commodityRelationList.length) {
        const pointMultiple = this.form.pointMultiple;
        const detailList = this.form.commodityRelationList.flatMap((item) => item.commodityRelationDetailList);
        detailList.forEach((i) => {
          i.point = Math.ceil(i.skuPrice * pointMultiple);
        });
      }
    },
    // 移除所选商品/券
    remove({ index, key }) {
      if (key === 'all') {
        this.form.commodityRelationList = [];
      } else {
        this.form.commodityRelationList.splice(index, 1);
      }
    },
    // 价格有变动时设置大贸商品全部启用
    updateEnable() {
      this.form.commodityRelationList.forEach((item) => {
        item.commodityRelationDetailList.forEach((detail) => {
          detail.izEnable = '1';
        });
      });
      // this.$message.success('启用成功');
    },
    // 清除商品更新标识
    updateIdentifying() {
      this.form.commodityRelationList.forEach((item) => {
        item.commodityRelationDetailList.forEach((detail) => {
          detail.bizTag = '';
        });
      });
      // this.$message.success('清除成功');
    },
    handleDateChange(v) {
      const {
        isEdit,
        isAdd,
        isCopy,
        status,
        form: { timeData, oldTimeData }
      } = this;
      const now = new Date();
      const thisDate = new Date(now.getFullYear(), now.getMonth(), now.getDate()).getTime();
      if (isAdd || isCopy) {
        if (!v) return;
        const [startDate] = v;
        if (startDate < thisDate) {
          timeData[0] = thisDate;
          this.$message.warning('所选日期不能小于当前日期');
        }
        return;
      }
      if (!isEdit || (status !== 'USE' && status !== 'STOPPED')) return;
      if (!timeData) return;
      const [startDate] = timeData;
      const [oldStartDate] = oldTimeData;
      if (startDate !== oldStartDate) {
        timeData[0] = oldStartDate;
        this.$message.warning('当前活动开始时间不能修改');
      }
    }
  },
  mounted() {
    this.initData();
  }
};
</script>

<style lang="scss" scoped>
.el-input,
.el-textarea,
.el-date-editor--datetimerange {
  width: 400px;
  margin-right: 10px;
}
.part {
  background-color: #fff;
  & > .title-h3 {
    border-bottom: none;
    padding: 30px 30px 0;
  }
  & > .title {
    font-weight: bolder;
    border-bottom: 1px solid #e8e8e8;
    padding: 18px 35px;
    font-size: 15px;
    user-select: none;
  }
  & > .content {
    padding: 30px 15px;
  }
  & ~ .part {
    margin-top: 20px;
  }
  &.footer {
    margin-top: 0;
    padding: 10px 46px;
  }
  &.part-activity {
    margin-bottom: 10px;
  }
}
.custom-form-item {
  margin-top: 10px;
  width: 768px;
  ::v-deep {
    .el-input {
      width: 768px !important;
    }
  }
}

.radio-block {
  display: flex;
  align-items: center;
  margin-top: 10px;
}

.radio-block-basic {
  margin-top: 10px;
}

.form-span {
  font-size: 12px;
  margin-right: 10px;
}

.form-item {
  margin-top: 15px;
}

.integral-tip {
  display: inline-block;
  padding: 0 20px;
  font-size: 12px;
  background: rgba(105, 105, 105, 0.1);
  // color: #9EA4B2;
}
.radio-block-flex {
  display: flex;
  align-items: baseline;
  margin-top: 10px;
}

.checkbox-list {
  max-width: 900px;
  display: flex;
  flex-wrap: wrap;

  > p {
    width: 220px;
    margin-bottom: 10px;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
  }
}
.integral-icon {
  font-size: 14px;
}

::v-deep {
  .el-input-number .el-input__inner {
    text-align: left;
  }
  .el-checkbox:last-of-type {
    margin-right: 10px;
  }
  .form-item-margin {
    margin-bottom: 0;
  }
  .my-textarea {
    .el-input__count {
      background: transparent;
      bottom: -26px;
    }
  }
}
</style>
