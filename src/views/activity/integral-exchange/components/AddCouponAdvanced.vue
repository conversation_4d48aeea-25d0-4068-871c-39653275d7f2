<template>
  <div class="add-coupon">
    <AddCoupon v-bind="$attrs" :couponList="couponList" @updateTable="updateTable" />
    <el-table border fit ref="tableBox" max-height="600" style="margin-top: 10px" :data="couponList" :row-style="{ height: '70px' }" :cell-style="{ padding: '0px' }">
      <el-table-column label="操作" width="70" align="center" v-if="!isDetail">
        <template slot-scope="scope">
          <el-button type="text" @click="deleteRow(scope.$index)">删除</el-button>
        </template>
      </el-table-column>
      <el-table-column label="优惠券信息" min-width="100">
        <template slot-scope="{ row }">
          <p>优惠券ID：{{ row.id || '--' }}</p>
          <p>优惠券名称：{{ row.name || '--' }}</p>
        </template>
      </el-table-column>
      <el-table-column prop="content" label="优惠券内容" min-width="60"></el-table-column>
      <el-table-column label="优惠券剩余库存" min-width="60">
        <template slot-scope="{ row }">
          <div>{{ row.detailTotalStock }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="statusName" label="优惠券状态" min-width="60"></el-table-column>
      <el-table-column label="兑换主图" :render-header="addRedStar" width="120">
        <template slot-scope="scope">
          <el-form-item :prop="'commodityRelationList.' + scope.$index + '.imgUrl'" :rules="rules.bizImgUrl">
            <div class="form-item-box">
              <ImageManagement :maxSize="5000 * 1024" v-model="scope.row.imgUrl" :disabled="isDetail" @input="clearValidate('commodityRelationList.' + scope.$index + '.imgUrl', $event)"></ImageManagement>
            </div>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="兑换名称" :render-header="addRedStar" min-width="120">
        <template slot-scope="scope">
          <el-form-item :prop="'commodityRelationList.' + scope.$index + '.bizName'" :rules="rules.bizName">
            <el-input v-model="scope.row.bizName" clearable type="textarea" autosize maxlength="30" placeholder="最多30个字"> </el-input>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="兑换库存" :render-header="addRedStar" class-name="table-cell" min-width="140">
        <template slot-scope="scope">
          <div class="table-colums-wrap">
            <div class="table-colums-custom table-colums-custom-small" v-for="(item, index) in scope.row.commodityRelationDetailList" :key="index" ref="inventory">
              <el-form-item
                v-if="isAdd || isCopy"
                :prop="'commodityRelationList.' + scope.$index + '.commodityRelationDetailList.' + index + '.leaveStock'"
                :rules="[
                  {
                    required: true,
                    validator: (_, value, callback) => {
                      if (!value) {
                        callback('请输入库存数量');
                      } else if (value > scope.row.detailTotalStock) {
                        callback('兑换库存不能大于剩余库存');
                      } else {
                        callback();
                      }
                    },
                    trigger: 'blur'
                  }
                ]"
              >
                <el-input style="width: 100%" v-model="item.leaveStock" clearable v-int @input="inventoryChange(item, $event)" placeholder="请输入" />
              </el-form-item>
              <el-form-item
                v-else
                style="margin: 0; flex: 1"
                :prop="'commodityRelationList.' + scope.$index + '.commodityRelationDetailList.' + index + '.leaveStock'"
                :rules="[
                  {
                    validator: (_, value, callback) => {
                      if (value && value > scope.row.detailTotalStock) {
                        callback('兑换库存不能大于剩余库存');
                      } else {
                        callback();
                      }
                    },
                    trigger: 'blur'
                  }
                ]"
              >
                <el-input style="width: 100%" v-model.trim="item.leaveStock" clearable placeholder="剩余库存调整为" />
              </el-form-item>
              <template v-if="!(isAdd || isCopy)">
                <p>剩余库存：{{ item.usableStock }}</p>
                <p>库存使用：{{ item.usedStock }}</p>
              </template>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="兑换所需积分" :render-header="addRedStar" class-name="table-cell" min-width="120">
        <template slot-scope="scope">
          <div class="table-colums-wrap">
            <div class="table-colums-custom table-colums-custom-small" v-for="(item, index) in scope.row.commodityRelationDetailList" :key="index">
              <el-form-item :prop="'commodityRelationList.' + scope.$index + '.commodityRelationDetailList.' + index + '.point'" :rules="rules.point">
                <el-input v-model="item.point" clearable v-money:[direction] placeholder="请输入"> </el-input>
              </el-form-item>
            </div>
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import AddCoupon from '@/components/AddCoupon/indexActivity';
import ImageManagement from '@/components/ImageManagement';
export default {
  name: 'add-coupon-advanced',
  model: {
    prop: 'couponList',
    event: 'updateTable'
  },
  inject: ['form'],
  components: {
    AddCoupon,
    ImageManagement
  },
  props: {
    couponList: Array,
    isAdd: {
      type: Boolean,
      default: false
    },
    isDetail: {
      type: Boolean,
      default: false
    },
    isCopy: {
      type: Boolean,
      default: false
    },
    rules: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  created() {},
  filters: {},
  computed: {},
  watch: {},
  data() {
    return {
      direction: 'twoPeople'
    };
  },
  methods: {
    // 表头生成必填校验红星样式
    addRedStar(h, { column }) {
      return [h('span', { style: 'color: red' }, '*'), h('span', ' ' + column.label)];
    },
    // 库存变化数量限制
    inventoryChange(item, value) {
      if (value) {
        value = value.replace(/[^\d]/g, '');
        if (Number(value) <= 0) {
          value = '';
        }
        if (Number(value) >= item.detailTotalStock) {
          throw new Error('兑换库存不能大于剩余库存');
        }
      }
      item.leaveStock = value;
    },
    // 数据改变
    updateTable(list) {
      this.$emit('updateTable', list);
    },
    // 删除行
    deleteRow(index) {
      this.$confirm(`确认删除优惠券？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$emit('removeTable', {
            index,
            key: 'one'
          });
        })
        .catch(() => {});
    },
    clearValidate(formProp) {
      this?.form?.$refs?.['form']?.clearValidate(formProp);
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
@import './styles';
</style>
