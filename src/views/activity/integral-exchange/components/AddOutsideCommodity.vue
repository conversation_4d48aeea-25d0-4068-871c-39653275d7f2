<template>
  <div class="add-outside-commodity">
    <AddCommodity v-bind="$attrs" :multipleSelectionData="multipleSelectionData" :izPointMultiple="izPointMultiple" @save="save" />
    <div class="sku-price" v-if="bizTagNum > 0 && !isCopy">
      <span>下面有{{ bizTagNum }}个SKU价格有变动</span>
      <el-button type="primary" size="mini" @click="updateIdentifying">清除商品更新标识</el-button>
    </div>
    <el-table border fit ref="tableBox" :max-height="multipleSelectionData.length > 10 ? 600 : 'initial'" style="margin-top: 10px" :data="multipleSelectionData" :row-style="{ height: '70px' }" :cell-style="{ padding: '0px' }">
      <el-table-column label="操作" width="70" align="center" v-if="!isDetail">
        <template slot-scope="scope">
          <el-button type="text" @click="deleteRow(scope.$index)">删除</el-button>
        </template>
      </el-table-column>
      <el-table-column label="商品信息" min-width="100">
        <template slot-scope="{ row }">
          <p>商品条码：{{ row.barcode || '--' }}</p>
          <p>
            <el-tooltip effect="dark" :content="row.name" placement="top-start" :disabled="!isShowTooltip">
              <span class="remarks" @mouseenter="visibilityChange">商品名称：{{ row.name || '--' }}</span>
            </el-tooltip>
          </p>
        </template>
      </el-table-column>
      <el-table-column label="商品缩略图" width="100">
        <template slot-scope="{ row }">
          <div class="form-item-box">
            <img :src="row.thumbnailUrl" alt class="goods-img" v-if="row.thumbnailUrl" />
            <span v-else>--</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="statusName" label="商品状态" width="80"></el-table-column>
      <el-table-column label="兑换主图" :render-header="addRedStar" width="100">
        <template slot-scope="scope">
          <el-form-item :prop="'commodityRelationList.' + scope.$index + '.imgUrl'" :rules="rules.bizImgUrl">
            <div class="form-item-box">
              <ImageManagement :maxSize="5000 * 1024" @input="clearValidate('commodityRelationList.' + scope.$index + '.imgUrl', $event)" v-model="scope.row.imgUrl" :disabled="isDetail"></ImageManagement>
            </div>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="兑换名称" :render-header="addRedStar" min-width="120">
        <template slot-scope="scope">
          <el-form-item :prop="'commodityRelationList.' + scope.$index + '.bizName'" :rules="rules.bizName">
            <el-input v-model="scope.row.bizName" clearable type="textarea" autosize maxlength="30" placeholder="最多30个字"> </el-input>
          </el-form-item>
        </template>
      </el-table-column>
      <el-table-column label="海淘商城价（RSP）" width="140">
        <template slot-scope="{ row }">
          <div class="table-colums-wrap">
            <div class="table-colums-custom" v-for="(item, index) in row.commodityRelationDetailList" :key="index">
              <span>￥{{ item.skuPrice }}</span>
              <p v-if="item.bizTag && !isCopy">
                <el-tag type="danger" effect="dark">{{ item.bizTagName }}</el-tag>
              </p>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="兑换库存" :render-header="addRedStar" class-name="table-cell" min-width="140">
        <template slot-scope="scope">
          <div class="table-colums-wrap">
            <div class="table-colums-custom table-colums-custom-small" v-for="(item, index) in scope.row.commodityRelationDetailList" :key="index" ref="inventory">
              <el-form-item v-if="isAdd || isCopy" style="margin: 0" :prop="'commodityRelationList.' + scope.$index + '.commodityRelationDetailList.' + index + '.leaveStock'" :rules="rules.stock">
                <el-input v-model="item.leaveStock" clearable v-int @input="inventoryChange(item, $event)" placeholder="请输入" />
              </el-form-item>
              <el-form-item v-else :prop="'commodityRelationList.' + scope.$index + '.commodityRelationDetailList.' + index + '.leaveStock'">
                <el-input v-model.trim="item.leaveStock" clearable placeholder="剩余库存调整为" />
              </el-form-item>
              <template v-if="!(isAdd || isCopy)">
                <p>剩余库存：{{ item.usableStock }}</p>
                <p>库存使用：{{ item.usedStock }}</p>
              </template>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="兑换所需积分" :render-header="addRedStar" class-name="table-cell" width="120">
        <template slot-scope="scope">
          <div class="table-colums-wrap">
            <div class="table-colums-custom table-colums-custom-small" v-for="(item, index) in scope.row.commodityRelationDetailList" :key="index">
              <el-form-item :prop="'commodityRelationList.' + scope.$index + '.commodityRelationDetailList.' + index + '.point'" :rules="rules.point" v-if="izPointMultiple === '0'">
                <el-input v-model="item.point" clearable v-money:[direction] placeholder="请输入"> </el-input>
              </el-form-item>
              <span v-else>{{ item.point || 0 }}分</span>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="是否禁用" min-width="70" class-name="table-cell">
        <template slot-scope="scope">
          <div class="table-colums-wrap">
            <div class="table-colums-custom" v-for="(item, index) in scope.row.commodityRelationDetailList" :key="index">
              <span>{{ item.izEnable === '1' ? '否' : '已禁用' }}</span>
            </div>
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import AddCommodity from '@/components/AddCommodityMultiplex';
import ImageManagement from '@/components/ImageManagement';
export default {
  name: 'add-outside-commodity',
  model: {
    prop: 'multipleSelectionData',
    event: 'save'
  },
  inject: ['form'],
  components: {
    AddCommodity,
    ImageManagement
  },
  props: {
    multipleSelectionData: Array,
    // 积分计算方式
    izPointMultiple: String,
    // 商品价格变动数量
    bizTagNum: Number,
    isAdd: {
      type: Boolean,
      default: false
    },
    isDetail: {
      type: Boolean,
      default: false
    },
    isCopy: {
      type: Boolean,
      default: false
    },
    rules: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  created() {},
  filters: {},
  computed: {},
  watch: {},
  data() {
    return {
      direction: 'twoPeople',
      isShowTooltip: false
    };
  },
  methods: {
    // 表头生成必填校验红星样式
    addRedStar(h, { column }) {
      return [h('span', { style: 'color: red' }, '*'), h('span', ' ' + column.label)];
    },
    // 库存变化数量限制
    inventoryChange(item, value) {
      if (value) {
        value = value.replace(/[^\d]/g, '');
        if (Number(value) <= 0) {
          value = '';
        }
      }
      item.leaveStock = value;
    },
    // 数据改变
    save(list) {
      this.$emit('save', list);
    },
    // 更新标识
    updateIdentifying() {
      this.$emit('updateIdentifying');
    },
    // 删除行
    deleteRow(index) {
      this.$confirm(`确认删除商品？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.$emit('removeTable', {
            index,
            key: 'one'
          });
        })
        .catch(() => {});
    },
    // 是否提示toolTip
    visibilityChange(e) {
      const ev = e.target;
      if (ev.scrollWidth > ev.offsetWidth || ev.scrollHeight > ev.offsetHeight) {
        this.isShowTooltip = true;
      } else {
        // 否则为不溢出
        this.isShowTooltip = false;
      }
    },
    clearValidate(formProp) {
      this?.form?.$refs?.['form']?.clearValidate(formProp);
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
@import './styles';
</style>
