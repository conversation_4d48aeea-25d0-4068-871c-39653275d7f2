<template>
  <div class="table-container">
    <div class="horizontalScroll">
      <!-- 列表展示 -->
      <sy-normal-table v-bind="table" ref="table" />
    </div>
  </div>
</template>
<script>
import { creditActivityExchangeList, activityExchangeChangeStatus, activityExchangeDelete, creditActivityExchangeExport } from '@/api/activity/integral';
import { parseTime } from '@/utils';
import download from '@/utils/download';
import dataDeal from '@/utils/dataDeal';

export default {
  name: 'activity-integral-exchange-list',
  components: {},
  data() {
    return {
      dialogVisible: false,
      exportLoading: false
    };
  },
  computed: {
    table() {
      const that = this;
      return {
        syTableBind: {
          horizontalScroll: {
            boxSelector: '.horizontalScroll'
          }
        },
        filters: [
          {
            tag: 'el-input',
            prop: 'name',
            label: '活动名称：',
            bind: {
              placeholder: '请输入内容'
            }
          },
          {
            tag: 'el-input',
            prop: 'id',
            label: '活动ID：',
            bind: {
              placeholder: '请输入内容'
            }
          },
          {
            tag: 'sy-select',
            prop: 'subExtType',
            label: '活动类型：',
            defaultValue: '',
            bind: {
              filterable: true,
              placeholder: '',
              options: window.$vue.$dict['credit_activity_exchange_sub_ext_type'],
              flashOptions: true
            }
          },
          {
            tag: 'sy-select',
            prop: 'izEnable',
            label: '是否启用：',
            defaultValue: '',
            bind: {
              filterable: true,
              placeholder: '',
              options: window.$vue.$dict['soyoungzg_common_whether'],
              flashOptions: true
            }
          },
          {
            tag: 'sy-select',
            prop: 'status',
            label: '活动状态：',
            defaultValue: 'ALL',
            bind: {
              filterable: true,
              placeholder: '',
              options: window.$vue.$dict['zg_marketing_activity_status'],
              flashOptions: true
            }
          },
          {
            tag: 'sy-date-picker',
            prop: ['startDate', 'endDate'],
            label: '活动时间：',
            bind: {
              bind: {
                'start-placeholder': '开始时间',
                'end-placeholder': '结束时间',
                type: 'daterange',
                valueFormat: 'timestamp',
                'default-time': ['00:00:00', '23:59:59']
              }
            }
          },
          {
            tag: 'sy-select',
            prop: 'subtype',
            label: '积分类型：',
            defaultValue: '',
            bind: {
              filterable: true,
              placeholder: '',
              options: window.$vue.$dict['credit_activity_exchange_subtype'],
              flashOptions: true
            }
          }
        ],
        btns: [
          {
            text: '+ 新增',
            type: 'primary',
            code: 'activity-integral-exchange-add',
            isJudgeShow: false,
            call: () => {
              that.$router.push({ path: '/activity/integral-exchange/add' });
            }
          },
          {
            text: '积分兑换记录',
            type: 'primary',
            code: 'activity-integral-exchange-record',
            isJudgeShow: false,
            call() {
              that.$router.push({ path: '/activity/integral-exchange-records/list' });
            }
          },
          {
            text: '导出',
            type: 'primary',
            code: 'activity-integral-exchange-export',
            isJudgeShow: false,
            bind: {
              loading: that.exportLoading
            },
            call: ({ filtersValue, tableData }) => that.onExport(filtersValue, tableData)
          }
        ],
        columns() {
          return [
            {
              prop: 'id',
              label: '活动ID',
              width: 120,
              render: (h, { row }) => (
                <div v-frag>
                  <p>{row.id || '-'}</p>
                  {row.bizTag && (
                    <p>
                      <el-tag type="danger" effect="plain">
                        {row.bizTagName}
                      </el-tag>
                    </p>
                  )}
                </div>
              )
            },
            {
              label: '活动名称',
              prop: 'name',
              multiLine: 3,
              itemBind: {
                minWidth: 120
              }
            },
            {
              label: '活动备注',
              prop: 'remarks',
              multiLine: 2
            },
            {
              label: '活动类型',
              width: 100,
              multiLine: 2,
              prop: 'subExtTypeName'
            },
            {
              label: '积分类型',
              prop: 'subtypeName',
              width: 90
            },
            {
              label: '活动时间',
              width: 150,
              render: (h, { row }) => (
                <div v-frag>
                  <p>{parseTime(row.startDate, '{y}-{m}-{d} {h}:{i}:{s}')}至</p>
                  <p>{parseTime(row.endDate, '{y}-{m}-{d} {h}:{i}:{s}')}</p>
                </div>
              )
            },
            {
              label: '活动状态',
              prop: 'statusName'
            },
            {
              label: '积分兑换数量（分）',
              width: 140,
              render: (h, { row }) => <div v-frag>{dataDeal.formaNumber(row.totalPoint || 0, { type: 'thousands' })}</div>
            },
            {
              label: '积分兑换人数',
              width: 110,
              render: (h, { row }) => <div v-frag>{dataDeal.formaNumber(row.totalDistributorNum || 0, { type: 'thousands' })}</div>
            },
            {
              label: '兑换订单数（笔）',
              width: 120,
              render: (h, { row }) => (
                <div v-frag>
                  <el-button type="text" onClick={() => that.$router.push({ path: '/activity/integral-exchange-records/list', query: { activityId: row.id, activityName: row.name } })}>
                    {dataDeal.formaNumber(row.totalOrderNum || 0, { type: 'thousands' })}
                  </el-button>
                </div>
              )
            },
            {
              label: '创建人/创建时间',
              width: 140,
              render: (h, { row }) => (
                <div v-frag>
                  <p>{row.createBy}</p>
                  <p>{parseTime(row.createDate, '{y}-{m}-{d} {h}:{i}')}</p>
                </div>
              )
            },
            {
              label: '是否启用',
              prop: 'purchaseAmountScore',
              width: 100,
              tips: {
                text: '活动只有启用后才会对外可用，停\n' + '用的活动对外不可用；'
              },
              render: (h, { row }) => (
                <div v-frag>
                  <el-switch active-value="1" inactive-value="0" value={row.izEnable} disabled={row.status === 'END'} on-change={() => that.changeEnable(row)}></el-switch>
                </div>
              )
            },
            {
              label: '操作',
              type: 'btns',
              width: 100,
              itemBind: {
                fixed: 'right'
              },
              render: (h, { row }) => (
                <div v-frag>
                  {row.status !== 'END' && (
                    <Authority auth="activity-integral-exchange-edit">
                      <el-button type="text" on-click={() => that.createEdit(row)}>
                        编辑
                      </el-button>
                    </Authority>
                  )}
                  <router-link to={'/activity/integral-exchange/detail/' + row.id} class="link">
                    <Authority auth="activity-integral-exchange-detail">
                      <el-button type="text">查看</el-button>
                    </Authority>
                  </router-link>
                  <router-link to={'/activity/integral-exchange/copy/' + row.id} class="link">
                    <Authority auth="activity-integral-exchange-copy">
                      <el-button type="text">复制</el-button>
                    </Authority>
                  </router-link>
                  {row.status === 'STAGING' && (
                    <Authority auth="activity-integral-exchange-remove">
                      <el-button type="text" on-click={() => that.deleteActivity(row)}>
                        删除
                      </el-button>
                    </Authority>
                  )}
                  {row.status !== 'STAGING' && (
                    <Authority auth="activity-integral-exchange-record">
                      <el-button type="text" onClick={() => that.$router.push({ path: '/activity/integral-exchange-records/list', query: { activityId: row.id, activityName: row.name } })}>
                        兑换记录
                      </el-button>
                    </Authority>
                  )}
                </div>
              )
            }
          ];
        },
        async search({ filtersValue, pageFilter }) {
          const { pageNo, pageSize } = pageFilter;
          const data = await creditActivityExchangeList({
            data: filtersValue,
            pageNo,
            pageSize
          });
          const list = data.data?.list || [];
          const total = data.data?.total || 0;
          return {
            list,
            total
          };
        }
      };
    }
  },
  activated() {
    this.refreshTableList();
  },
  methods: {
    // 刷新表格数据
    refreshTableList() {
      const ref = this.$refs.table;
      ref && ref.handlerSearch();
    },
    // 编辑
    createEdit(row) {
      this.$router.push(`/activity/integral-exchange/edit/${row.status}/${row.id}`);
    },
    // 积分活动状态变更
    changeEnable(item) {
      const { id, izEnable } = item;
      const flag = izEnable === '1';
      const enableName = flag ? '禁用' : '启用';
      this.$confirm(`确认${enableName}吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          activityExchangeChangeStatus({ id, status: flag ? '0' : '1' }).then((res) => {
            if (res.code === '0') {
              this.$message.success(res.msg);
              this.refreshTableList();
            }
          });
        })
        .catch(() => {
          this.$message.info('已取消操作');
        });
    },
    // 删除活动
    deleteActivity(row) {
      this.$confirm('确认删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          activityExchangeDelete({ activityId: row.id }).then((res) => {
            if (res.code === '0') {
              this.$message.success(res.msg);
              this.refreshTableList();
            }
          });
        })
        .catch(() => {
          this.$message.info('已取消操作');
        });
    },
    // 导出
    onExport(filtersValue, tableData) {
      if (!tableData.length) return this.$message.warning('暂无可导出数据');
      this.exportLoading = true;
      creditActivityExchangeExport(filtersValue)
        .then((res) => {
          try {
            const resObj = JSON.parse(new TextDecoder('utf-8').decode(new Uint8Array(res)));
            if (resObj.success) {
              this.$message.success(resObj.msg);
            } else {
              this.$message.error(resObj.msg || '系统错误');
            }
          } catch (error) {
            download(res, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', `积分兑换列表数据导出-${parseTime(Date.now(), '{y}-{m}-{d}-{h}:{i}:{s}')}.xlsx`);
          }
        })
        .finally(() => {
          this.exportLoading = false;
        });
    }
  }
};
</script>

<style scoped lang="scss">
::v-deep {
  .cell {
    .el-button {
      margin: 2px;
      text-align: center;
    }
  }
}
.commo-dialog-content {
  display: flex;
  justify-content: space-around;
}
.horizontalScroll {
  height: calc(100vh - 150px);
  overflow-y: auto;
}
</style>
