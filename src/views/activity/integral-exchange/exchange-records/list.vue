<template>
  <div class="table-container">
    <div class="horizontalScroll">
      <!-- 列表展示 -->
      <sy-normal-table v-bind="table" ref="table" />
    </div>
    <!-- 退款 -->
    <el-dialog
      title="退款确认"
      :visible.sync="refundDialogVisible"
      width="40%"
      @closed="onCancelRefund">
      <el-form :model="refundForm" ref="refundForm" label-width="75px">
        <el-form-item label-width="0">
          <instructions show-title is-hide>
            <template slot="title">
              <ul class="tips-text">
                <li>退款：只能进行全额退款，不能修改退款的数量和金额，所以请谨慎操作；确认退款后，客户端兑换订单状态更改为【已使用】。</li>
              </ul>
            </template>
          </instructions>
        </el-form-item>
        <el-form-item label="兑换单号：">
          <span>{{ rowObj.orderNo }}</span>
        </el-form-item>
        <el-form-item label="积分活动：">
          <span>{{ rowObj.subExtTypeName }}</span>
        </el-form-item>
        <el-form-item label="使用积分：">
          <span>{{ rowObj.payCreditAccountTypeName }}：{{ rowObj.payPoint }}分</span>
        </el-form-item>
        <el-form-item label="支付金额：">
          <span>￥{{ rowObj.payAmount }}</span>
        </el-form-item>
        <el-form-item label="退款备注：" prop="remarks">
          <el-input
            type="textarea"
            placeholder="请输入内容"
            :autosize="{ minRows: 4, maxRows: 6}"
            v-model="refundForm.remarks"
            maxlength="100"
            show-word-limit>
          </el-input>
        </el-form-item>
        <el-form-item style="text-align: right" class="form-footer">
          <el-button @click="onCancelRefund">取消</el-button>
          <button-hoc type="primary" @click="onSubmitRefund('refundForm')">确定</button-hoc>
        </el-form-item>
      </el-form>
    </el-dialog>
    <!-- 人工核销 -->
    <el-dialog
      title="人工核销"
      :visible.sync="dialogVisible"
      width="40%"
      @closed="onCancelRecord">
      <el-form :model="recordForm" ref="recordForm" label-width="75px">
        <el-form-item label-width="0">
          <instructions show-title is-hide>
            <template slot="title">
              <ul class="tips-text">
                <li>人工核销：需要确保线下已经核销完成，选择人工核销，客户端兑换订单状态将会改为【已使用】。</li>
              </ul>
            </template>
          </instructions>
        </el-form-item>
        <el-form-item label="核销备注：" prop="remarks">
          <el-input
            type="textarea"
            placeholder="请输入内容"
            :autosize="{ minRows: 4, maxRows: 6}"
            v-model="recordForm.remarks"
            maxlength="100"
            show-word-limit>
          </el-input>
        </el-form-item>
        <el-form-item style="text-align: right" class="form-footer">
          <el-button @click="onCancelRecord">取消</el-button>
          <button-hoc type="primary" @click="onSubmitRecord('recordForm')">确定</button-hoc>
        </el-form-item>
      </el-form>
    </el-dialog>
    <!-- 批量人工核销 -->
    <el-dialog title="批量人工核销" :visible.sync="visible" @closed="onCancel">
      <el-form :model="form" :rules="rules" ref="form">
        <el-form-item>
          <instructions show-title is-hide>
            <template slot="title">
              <ul class="tips-text">
                <li>批量核销说明：</li>
                <li>1、兑换订单只能整笔订单进行核销，不能拆分核销；</li>
                <li>2、兑换订单只能核销【待使用】状态的订单；</li>
              </ul>
            </template>
          </instructions>
        </el-form-item>
        <el-form-item label="批量导入核销清单：" prop="data">
          <template>
            <upload-excel v-bind="importOptions" ref="uploadExcel" v-model="form.data" @input="($event) => onInput($event, 'data')"></upload-excel>
          </template>
          <template>
            <div class="tip">
              <span v-if="form.data && (form.data.success || form.data.fail)">
                <strong>处理结果</strong> 成功<span class="success">{{ form.data.success }}</span>条，失败<span class="fail">{{
      form.data.fail }}</span>条
              </span>
              <button-hoc style="padding: 0" type="text" @click="downLoadFailRecord"
                v-if="form.data && form.data.fail">下载失败记录 <svg-icon icon-class="cloud-rain"></svg-icon></button-hoc>
            </div>
          </template>
        </el-form-item>
        <el-form-item style="text-align: right" class="form-footer">
          <el-button @click="onCancel">取消</el-button>
          <button-hoc type="primary" @click="onSubmit('form')">确定</button-hoc>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>
<script>
import { creditOrderList, creditOrderRefund, creditOrderVerification, creditOrderExport, staffListAll } from '@/api/activity/integral';
import { decryptCipher } from '@/api/common/list'; 
import UploadExcel from '@/components/UploadExcel';
import dict from '@/components/Common/dicts';
import { parseTime } from '@/utils';
import download from '@/utils/download';
import dataDeal from '@/utils/dataDeal';

export default {
  name: 'integral-exchange-records-list',
  components: {
    UploadExcel
  },
  data() {
    return {
      dialogVisible: false, // 人工核销弹窗
      exportLoading: false, // 导出
      visible: false, // 批量人工核销弹窗
      refundDialogVisible: false, // 退款弹窗
      isShowTooltip: false,
      rowObj: {},
      staffList: [],
      recordForm: {
        remarks: '',
        id: ''
      },
      refundForm: {
        remarks: '',
        id: ''
      },
      form: {
        data: ''
      },
      rules: {
        data: [
          { 
            validator: (rule, value = '', callback) => {
              const data = value;
              if (data) {
                if (data.code && data.code !== '0') {
                  callback(data.msg || '上传文件存在错误数据不能导入');
                } else {
                  if(data.fail >= 1) {
                    callback(new Error('有失败数据，请修改后重新提交'));
                  } else {
                    callback();
                  }
                }
              } else {
                callback('请上传导入模板！');
              }
            },
            trigger: ['blur', 'change'] 
          }
        ]
      },
    }
  },
  computed: {
    importOptions() {
      return {
        title: '积分兑换订单手工核销',
        name: '积分兑换订单手工核销',
        tip: '文件只支持上传大小为:5M以内,条数1000条以内;文件格式支持:xls,xlsx格式',
        options: {
          _size: { company: 'MB', size: 5 },
        },
        accept: '.xls,.xlsx',
        limit: 1,
        action: process.env.VUE_APP_BASE_URL + '/soyoungzg/api/creditOrder/batchVerification',
        templateUrl: process.env.VUE_APP_MUSHUROOMFILEURL + '/static/file/soyoung-zg/template/积分兑换订单手工核销模板.xlsx'
      };
    },
    table() {
      const that = this;
      return {
        initSearch: false,
        syTableBind: {
          horizontalScroll: {
            boxSelector: '.horizontalScroll'
          }
        },
        tableBind: {
          border: true
        },
        filters: [
          {
            tag: 'el-input',
            prop: 'distributorName',
            label: '分销商名称：',
            bind: {
              placeholder: '请输入内容',
            }
          },
          {
            tag: 'el-input',
            prop: 'distributorIds',
            label: '分销商ID：',
            bind: {
              placeholder: '支持逗号分隔查询多条',
            }
          },
          {
            tag: 'sy-select',
            prop: 'csIds',
            label: '专属顾问：',
            bind: {
              filterable: true,
              multiple: true,
              placeholder: '可多选',
              options: async () => {
                const data = await dict('COMMON_EXPAND_ADVISER');
                return data;
              },
              flashOptions: true,
            }
          },
          {
            tag: 'sy-select',
            prop: 'subExtType',
            label: '活动类型：',
            bind: {
              filterable: true,
              placeholder: '',
              options: window.$vue.$dict['credit_activity_exchange_sub_ext_type'],
              flashOptions: true,
            }
          },
          {
            tag: 'el-input',
            prop: 'orderNo',
            label: '兑换单号：',
            bind: {
              placeholder: '请输入兑换单号',
            }
          },
          {
            tag: 'sy-select',
            prop: 'subType',
            label: '积分类型：',
            bind: {
              filterable: true,
              placeholder: '',
              options: window.$vue.$dict['credit_activity_exchange_subtype'],
              flashOptions: true,
            }
          },
          {
            tag: 'sy-select',
            prop: 'statusList',
            label: '订单状态：',
            bind: {
              filterable: true,
              multiple: true,
              placeholder: '可多选',
              options: window.$vue.$dict['credit_order_status'],
              flashOptions: true,
            }
          },
          {
            tag: 'sy-select',
            prop: 'verificationWay',
            label: '核销方式：',
            bind: {
              filterable: true,
              placeholder: '',
              options: window.$vue.$dict['credit_order_verification_way'],
              flashOptions: true,
            }
          },
          {
            tag: 'sy-select',
            prop: 'verificationByList',
            label: '核销人员：',
            bind: {
              filterable: true,
              multiple: true,
              placeholder: '可多选',
              options: that.staffList,
              flashOptions: true,
            }
          },
          {
            tag: 'sy-date-picker',
            prop: ['drawDateBegin', 'drawDateEnd'],
            label: '兑换时间：',
            bind: {
              bind: {
                'start-placeholder': '开始时间',
                'end-placeholder': '结束时间',
                type: 'daterange',
                valueFormat: 'timestamp',
                'default-time': ['00:00:00', '23:59:59']
              }
            }
          },
          {
            tag: 'sy-date-picker',
            prop: ['finishDateBegin', 'finishDateEnd'],
            label: '完成时间：',
            bind: {
              bind: {
                'start-placeholder': '开始时间',
                'end-placeholder': '结束时间',
                type: 'daterange',
                valueFormat: 'timestamp',
                'default-time': ['00:00:00', '23:59:59']
              }
            }
          },
          {
            tag: 'el-input',
            prop: 'activityId',
            label: '活动ID：',
            bind: {
              placeholder: '请输入内容',
            }
          },
          {
            tag: 'el-input',
            prop: 'activityName',
            label: '活动名称：',
            bind: {
              placeholder: '请输入内容',
            }
          },
        ],
        btns: [
          {
            text: '批量核销',
            type: 'primary',
            code: 'activity-integral-exchange-records-batch',
            isJudgeShow: false,
            call: () => {
              that.visible = true;
            }
          },
          {
            text: '导出',
            type: 'primary',
            code: 'activity-integral-exchange-export',
            isJudgeShow: false,
            bind: {
              loading: that.exportLoading
            },
            call: ({ filtersValue, tableData }) => that.onExport(filtersValue, tableData)
          }
        ],
        columns() {
          return [
            {
              prop: 'orderNo',
              label: '兑换订单号',
              multiLine: 3,
              itemBind: {
                fixed: 'left'
              }
            },
            {
              label: '兑换活动类型',
              width: '130',
              render: (h, { row }) => (
                <div v-frag>
                  <p>{ row.subExtTypeName || '-' }</p>
                </div>
              )
            },
            {
              label: '兑换商品/券信息',
              width: '200',
              render: (h, { row }) => (
                <div v-frag>
                  <p>{row.subExtType !== 'COMMODITY_ACTIVITY' ? '优惠券ID：' : '商品ID：'}{ row.bizId || '-' }</p>
                  { row.subExtType === 'COMMODITY_ACTIVITY' && (<p>商品条码：{ row.bizDetailCode || '-' }</p>) }
                  { (row.subExtType === 'COMMODITY_ACTIVITY' && row.payCreditAccountType === 'NORMAL') && (<p>规格：{ row.specContent || '-' }</p>) }
                  <p>{row.subExtType !== 'COMMODITY_ACTIVITY' ? '优惠券名称：' : '商品名称：'}{ row.bizName || '-' }</p>
                  <p>兑换单价：{ row.price }分</p>
                </div>
              )
            },
            {
              label: '兑换时间',
              width: '140',
              render: (h, { row }) => (
                <div v-frag>
                  <p>{parseTime(row.createDate, '{y}-{m}-{d} {h}:{i}:{s}')}</p>
                </div>
              )
            },
            {
              label: '兑换数量',
              render: (h, { row }) => (
                <div v-frag>
                  {dataDeal.formaNumber(row.quantity || 0, { type: 'thousands' })}
                </div>
              )
            },
            {
              label: '支付积分',
              render: (h, { row }) => (
                <div v-frag>
                  {dataDeal.formaNumber(row.payPoint || 0, { type: 'thousands' })}
                </div>
              )
            },
            {
              label: '支付积分类型',
              prop: 'payCreditAccountTypeName'
            },
            {
              label: '支付金额',
              render: (h, { row }) => (
                <div v-frag>
                  ￥{dataDeal.formaNumber(row.payAmount || 0, { type: 'thousands' })}
                </div>
              )
            },
            {
              label: '分销商信息',
              width: '130',
              render: (h, { row }) => (
                <div v-frag>
                  <p>分销商ID：{ row.distributorId || '-' }</p>
                  <p>分销商名称：{ row.distributionName || '-' }</p>
                  <p>专属顾问：{ row.customerServiceName }</p>
                </div>
              )
            },
            {
              label: '订单状态',
              width: '115',
              render: (h, { row }) => (
                <div v-frag>
                  <p>{ row.statusName || '-' }</p>
                  { (row.status === 'REFUND_FAILED' && row.remarks) && (<p>（{row.remarks}）</p>) }
                </div>
              )
            },
            {
              label: '参与活动信息',
              width: '130',
              render: (h, { row }) => (
                <div v-frag>
                  <p>活动ID：{ row.activityId || '-' }</p>
                  <p>活动名称：{ row.activityName || '-' }</p>
                </div>
              )
            },
            {
              label: '核销信息',
              width: '160',
              render: (h, { row }) => {
                // WAIT_USE: 待使用；USED: 已使用；REFUNDED: 已退款；WAIT_CONFIRM: 待合作方确认；CONFIRMED: 合作方已确认；REJECT: 合作方确认驳回；WAIT_DELIVERY：待发货；DELIVERED：已发货；FINISHED：已完成；WAIT_PAY：待付款；WAIT_CONFIRM_PAID：付款待确认；CANCELED：已取消；REFUND_FAILED：退款失败；

                const show = row.status === 'CONFIRMED' || row.status === 'REJECT' || row.status === 'USED' || row.status === 'REFUNDED' || row.status === 'DELIVERED' || (row.status === 'WAIT_CONFIRM' && row.subExtType === 'COMMODITY_ACTIVITY')
                // 核销方式/退款方式
                const verificationWayName = row.status === 'DELIVERED' || row.status === 'USED' || row.status === 'REFUNDED' || row.status === 'REJECT' || row.status === 'CONFIRMED' ? (<p>{ row.verificationWayName }</p>) : null
                // 操作人
                const verificationByName = (row.status === 'CONFIRMED' && row.subExtType !== 'COMMODITY_ACTIVITY' && row.payCreditAccountType === 'GLOBAL') || ['USED', 'REJECT', 'REFUNDED'].includes(row.status) ? (<p>操作人：{ row.verificationByName }</p>) : null
                 // 使用时间、退款时间、发货时间
                const dateText = {
                  CONFIRMED: '使用时间',
                  USED: '使用时间',
                  REJECT: '退款时间',
                  REFUNDED: '退款时间',
                  DELIVERED: '发货时间'
                }[row.status]
                const year = parseTime(row.finishDate, '{y}-{m}-{d}')
                const time = parseTime(row.finishDate, '{h}:{i}:{s}')
                const timeData = (row.status === 'CONFIRMED' || row.status === 'USED' || row.status === 'REJECT' || row.status === 'REFUNDED' || row.status === 'DELIVERED') ? (
                  <div>
                    { dateText }：{ year }
                    <br/>
                    { time }
                  </div>
                ) : null
                // 订单号
                const orderNo = row.status === 'DELIVERED' && row.payCreditAccountType === 'NORMAL' && row.subExtType === 'COMMODITY_ACTIVITY' ? (<el-button type="text" style="white-space: break-spaces;text-align: left;line-height: 16px;" onClick={() => that.jumpOrderDetails(row.dataId)}>
                    订单号：{ row.orderNo }
                  </el-button>) : null
                // 兑换码
                const exchangeCode = (row.status === 'WAIT_CONFIRM' || row.status === 'CONFIRMED') && row.payCreditAccountType === 'GLOBAL' && row.subExtType === 'COMMODITY_ACTIVITY' ? (<p>兑换码：{ row.orderNo }</p>) : null
                // 备注
                const verificationRemarks = row.status === 'REJECT' || row.status === 'USED' || (row.status === 'CONFIRMED' && row.subExtType !== 'DELIVERY_FEE_COUPON') || (row.status === 'REFUNDED' && row.subExtType === 'VIRTUAL_COUPON') ? (<p>{row.verificationRemarks && <el-tooltip effect="dark" content={row.verificationRemarks} placement="top-start" disabled={!that.isShowTooltip}>
                  <span class="remarks" on-mouseenter={that.visibilityChange()}>备注：{row.verificationRemarks}</span>
                </el-tooltip> || '备注：-'}</p>) : null

                return <div v-frag>{ show ? (<div>{ verificationWayName }{ verificationByName }{ timeData }{ exchangeCode }{ orderNo }{ verificationRemarks }</div>) : '-' }</div>
              }
            },
            {
              label: '收货地址',
              width: '130',
              render: (h, { row }) => {
                const show = row.address &&(row.status === 'REFUND_FAILED' || row.status === 'WAIT_PAY' || row.status === 'CANCELED' || row.status === 'WAIT_CONFIRM_PAID' || row.status === 'WAIT_DELIVERY' || row.status === 'DELIVERED' || (row.status === 'REFUNDED' && row.subExtType === 'COMMODITY_ACTIVITY') || ((row.status === 'WAIT_CONFIRM' || row.status === 'CONFIRMED' || row.status === 'REJECT') && row.subExtType === 'COMMODITY_ACTIVITY'))
                // 收货人
                const consignee = show ? (<p>{ row?.address?.consignee }</p>) : null
                // 收货人手机号
                const mobile = show ? (row?.address?.mobile && (<p>
                  { row?.address?.mobile }
                  <Authority auth="activity-integral-exchange-records-decrypt">
                    <el-button
                      class="crypto_block_input-btn"
                      type="text"
                      on-click={() => that.toView(row)}
                      size="mini"
                      disabled={row.viewDisabled}>查看</el-button>
                  </Authority>
                </p>)) : null
                // 收货地址
                const provinceName = row?.address?.provinceName ?? ''
                const cityName = row?.address?.cityName ?? ''
                const zoneName = row?.address?.zoneName ?? ''
                const addressDetail = row?.address?.addressDetail ?? ''
                const address = show ? (<p>{ `${provinceName}${cityName}${zoneName}${addressDetail}` }</p>) : null
                return <div v-frag>{ show ? (<div>{ consignee }{ mobile }{ address }</div>) : '-' }</div>
              }
            },
            {
              label: '操作',
              type: 'btns',
              width: 120,
              itemBind: {
                fixed: 'right',
                align: 'center'
              },
              btns({ row }) {
                const isArtificial = row.status === 'WAIT_USE' || row.status === 'WAIT_CONFIRM' || row.status === 'REFUND_FAILED'
                const isRefund = row.status === 'WAIT_DELIVERY' || isArtificial
                return [
                  {
                    text: '退款',
                    type: 'text',
                    code: 'activity-integral-exchange-records-refund',
                    isJudgeShow: false,
                    hide: !isRefund,
                    async call() {
                      that.rowObj = row;
                      that.refundForm.id = row.id;
                      that.refundDialogVisible = true;
                    }
                  },
                  {
                    text: '人工核销',
                    type: 'text',
                    code: 'activity-integral-exchange-records-artificial',
                    isJudgeShow: false,
                    hide: !isArtificial,
                    call() {
                      that.rowObj = row;
                      that.recordForm.id = row.id;
                      that.dialogVisible = true;
                    }
                  },
                  {
                    text: '-',
                    type: 'text',
                    hide: isRefund || isArtificial,
                    call() {}
                  },
                ];
              }
            }
          ];
        },
        async search({ filtersValue, pageFilter }) {
          const { pageNo, pageSize } = pageFilter;
          const data = await creditOrderList({
            data: filtersValue,
            pageNo,
            pageSize
          });
          const list = (data.data?.list || []).map(item => ({ ...item, viewDisabled: false }));
          const total = data.data?.total || 0;
          return {
            list,
            total
          };
        }
      };
    },
  },
  created() {
    this.fetchDataList();
  },
  async mounted() {
    this.initFilter();
  },
  methods: {
    // 初始化过滤条件
    initFilter() {
      const { activityId = '', activityName = '', orderNo = '' } = this.$route.query;
      if (activityId) {
        this.$refs.table.setFiltersValue(activityId, 'activityId');
      }
      if (activityName) {
        this.$refs.table.setFiltersValue(activityName, 'activityName');
      }
      if (orderNo) {
        this.$refs.table.setFiltersValue(orderNo, 'orderNo');
      }
      // this.$router.push({ query: {} });
      this.refreshTableList();
    },
    // 刷新表格数据
    refreshTableList() {
      const ref = this.$refs.table;
      ref && ref.handlerSearch();
    },
    parseCustomerThreshold(val) {
      const value = val && val.join('、');
      return value;
    },
    // 获取核销人员
    fetchDataList() {
      staffListAll({})
        .then((response) => {
          this.staffList = response.data.map((item) => {
            return {
              label: item.name,
              value: item.userId
            };
          });
        })
    },
    // 解密
    toView(row) {
      const params = {
        cipherFieldList: [
          {
            cipherText: row?.address?.mobileCipher || '',
            fieldName: 'mobile'
          },
          {
            cipherText: row?.address?.addressDetailCipher || '',
            fieldName: 'addressDetail'
          }
        ]
      }
      decryptCipher(params).then(res => {
        row.viewDisabled = true;
        row.address.mobile = res.data.mobile;
        row.address.addressDetail = res.data.addressDetail;
      });
    },
    // 查看订单详情
    jumpOrderDetails(id) {
      this.$router.push(`/order/inquiry/detail/${id}`);
    },
    // 是否提示toolTip
    visibilityChange() {
      return (e) => {
        const ev = e.target;
        if (ev.scrollWidth > ev.offsetWidth || ev.scrollHeight > ev.offsetHeight) {
          this.isShowTooltip =true;
        } else {
          // 否则为不溢出
          this.isShowTooltip = false;
        }
      }
    },
    // 导出
    onExport(filtersValue, tableData) {
      if(!tableData.length) return this.$message.warning('暂无可导出数据');
      this.exportLoading = true;
      creditOrderExport(filtersValue)
        .then((res) => {
          try {
            const resObj = JSON.parse(new TextDecoder('utf-8').decode(new Uint8Array(res)));
            if (resObj.success) {
              this.$message.success(resObj.msg);
            } else {
              this.$message.error(resObj.msg || '系统错误');
            }
          } catch (error) {
            download(res, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', `积分兑换记录列表数据导出-${parseTime(Date.now(), '{y}-{m}-{d}-{h}:{i}:{s}')}.xlsx`);
          }
        })
        .finally(() => {
          this.exportLoading = false;
        });
    },
    onSubmit(ruleForm) {
      this.$refs[ruleForm].validate((valid) => {
        if (!valid) {
          return;
        }
        this.$message.success('批量核销成功');
        this.refreshTableList();
        this.onCancel();
      });
    },
    // 表单数据更新
    onInput(val, prop) {
      this.$set(this.form, prop, val);
      this.$refs.form.validateField(prop);
    },
    onCancel() {
      this.$nextTick(() => {
        this.$refs.uploadExcel.setValue();
      })
      this.$refs.form.resetFields();
      this.visible = false;
    },
    // 下载失败记录
    downLoadFailRecord() {
      window.location.href = this.form.data.url;
    },
    // 人工核销
    onSubmitRecord() {
      creditOrderVerification({
        id: this.rowObj.id, 
        ...this.recordForm
      }).then(res => {
        if(res.code === '0') {
          this.$message.success('核销成功');
          this.refreshTableList();
          this.onCancelRecord();
        }
      });
    },
    // 关闭人工审核弹框
    onCancelRecord() {
      this.$refs.recordForm.resetFields();
      this.dialogVisible = false;
    },
    // 退款
    onSubmitRefund() {
      creditOrderRefund({
        id: this.rowObj.id, 
        ...this.refundForm
      }).then(res => {
       if(res.code === '0') {
        this.$message.success('退款成功');
        this.refreshTableList();
        this.onCancelRefund();
       }
      });
    },
    // 关闭退款弹框
    onCancelRefund() {
      this.$refs.refundForm.resetFields();
      this.refundDialogVisible = false;
    }
  }
};
</script>

<style scoped lang="scss">
::v-deep {
  .cell {
    .el-button {
      margin: 2px;
      text-align: center;
    }
  }
  .remarks {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    -webkit-line-clamp: 1;
  }
}
.horizontalScroll {
  height: calc(100vh - 150px);
  overflow-y: auto;
}
.tips-text {
  line-height: 20px;
}
.success {
  margin: 0 5px;
  color: var(--color-success)
}
.fail {
  margin: 0 5px;
  color: var(--color-danger);
}
</style>
