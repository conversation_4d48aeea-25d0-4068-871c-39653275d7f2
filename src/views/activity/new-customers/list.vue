<template>
  <div class="app-container">
    <div class="table-container">
      <el-form :inline="true" class="commo-search-container commo-search-flex commo-search-margin-bottom10">
        <div class="commo-search-item">
          <span class="commo-search-item-label">活动名称:</span>
          <div class="commo-search-item-content">
            <el-input size="small" v-model.trim="sendData.data.name"></el-input>
          </div>
        </div>
        <div class="commo-search-item">
          <span class="commo-search-item-label">状态:</span>
          <div class="commo-search-item-content">
            <el-select size="small" placeholder="请选择" v-model="sendData.data.status">
              <el-option :key="item.id" :label="item.name" :value="item.id" v-for="item in searObj.statusOptions"></el-option>
            </el-select>
          </div>
        </div>
        <div class="commo-search-item">
          <span class="commo-search-item-label">权益类型:</span>
          <div class="commo-search-item-content">
            <el-select size="small" placeholder="请选择" v-model="sendData.data.giftType">
              <el-option :key="item.value" :label="item.label" :value="item.value" v-for="item in giftTypeOptions"></el-option>
            </el-select>
          </div>
        </div>
        <div class="commo-search-btns">
          <el-button size="small" @click="onSubmit" type="primary">查询</el-button>
          <el-button size="small" @click="clearForm">清空</el-button>
        </div>
      </el-form>
      <div style="margin-bottom: 20px">
        <router-link to="/activity/new-customers/create">
          <Authority auth="/activity/create/new-customers">
            <el-button icon="el-icon-plus" size="small" type="primary">新增</el-button>
          </Authority>
        </router-link>
      </div>
      <el-table :data="tableData" center style="width: 100%">
        <el-table-column align="center" label="活动名称" prop="name" width="auto"></el-table-column>
        <el-table-column align="center" label="活动有效期" prop="date" width="auto">
          <template slot-scope="scope">{{ scope.row.beginDate | parseTime('{y}-{m}-{d} {h}:{i}:{s}') }}~{{ scope.row.endDate | parseTime('{y}-{m}-{d} {h}:{i}:{s}') }}</template>
        </el-table-column>
        <el-table-column align="center" label="享受权益时长（天）" prop="validDays" width="auto"></el-table-column>
        <el-table-column align="center" label="权益" prop="giftTypeName" width="auto"></el-table-column>
        <el-table-column align="center" label="状态" prop="statusName" width="auto"></el-table-column>
        <el-table-column align="center" label="更新人&更新时间" width="auto">
          <template slot-scope="scope">
            {{ scope.row.updateByName }}
            <br />
            {{ parseInt(scope.row.updateDate) | parseTime('{y}-{m}-{d} {h}:{i}:{s}') }}
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="120">
          <template slot-scope="scope">
            <el-button @click="disable(scope.row.id)" type="text" v-if="scope.row.status === 'NOT_START' || scope.row.status === 'USE'">停用</el-button>
            <router-link :to="'/activity/new-customers/edit/' + scope.row.id" v-if="scope.row.status === 'NOT_START' || scope.row.status === 'USE'">
              <Authority auth="/activity/edit/new-customers">
                <el-button type="text">编辑</el-button>
              </Authority>
            </router-link>
            <router-link :to="'/activity/new-customers/detail/' + scope.row.id">
              <Authority auth="/activity/view/new-customers">
                <el-button type="text">查看</el-button>
              </Authority>
            </router-link>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination :current-page="sendData.pageNo" :page-size="sendData.pageSize" :page-sizes="[10, 20, 30, 40, 50, 100]" :total="total" @current-change="handleCurrentChange" @size-change="handleSizeChange" background layout="total, sizes, prev, pager, next, jumper"></el-pagination>
      </div>
    </div>
  </div>
</template>
<script>
import { list, disable } from '@/api/activity/new-customers';
import dict from '@/components/Common/dicts';
const searchData = function () {
  return {
    giftType: '',
    name: '',
    status: ''
  };
};
export default {
  name: 'activity-new_customers-list',
  data() {
    return {
      giftTypeOptions: [],
      searObj: {
        statusOptions: [
          { id: 'NOT_START', name: '未开始' },
          { id: 'USE', name: '活动进行中' },
          { id: 'END', name: '活动已结束' },
          { id: 'STOPPED', name: '活动已停用' }
        ]
      },
      sendData: {
        data: searchData(),
        pageNo: 1,
        pageSize: 10
      },
      tableData: [],
      total: 0
    };
  },
  mounted() {
    this.initData();
  },
  activated() {
    this.initData();
  },
  methods: {
    initData() {
      this.getList();
      dict('ACTIVITY_GIFT_TYPE').then((res) => {
        this.giftTypeOptions = res;
      });
    },
    async getList() {
      const { data: res } = await list(this.sendData);
      this.tableData = res.list;
      this.total = res.total;
    },
    onSubmit() {
      this.sendData.pageSize = 10;
      this.sendData.pageNo = 1;
      this.getList();
    },
    clearForm() {
      this.sendData.data = searchData();
      this.onSubmit();
    },
    handleSizeChange(val) {
      this.sendData.pageSize = val;
      this.sendData.pageNo = 1;
      this.getList();
    },
    handleCurrentChange(val) {
      this.sendData.pageNo = val;
      this.getList();
    },
    disable(id) {
      this.$confirm('确认停用吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          disable(id).then((res) => {
            if (res.code === '0') {
              this.$message({
                type: 'success',
                message: '操作成功！'
              });
              this.getList();
            }
          });
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
    }
  }
};
</script>
<style lang="scss" scoped></style>
