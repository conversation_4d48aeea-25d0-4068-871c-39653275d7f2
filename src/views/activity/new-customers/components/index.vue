<template>
  <div class="table-container">
    <el-form :model="form" :rules="rules" label-width="120px" ref="form" :disabled="isDetail">
      <formTitle>基本信息</formTitle>
      <!-- 活动名称 -->
      <el-form-item label="活动名称" prop="name">
        <el-input v-model.trim="form.name"></el-input>
      </el-form-item>

      <!-- 活动时间 -->
      <el-form-item label="活动时间" prop="timeData">
        <el-date-picker :picker-options="mixPickerOptions" :default-time="['00:00:00', '23:59:59']" end-placeholder="结束日期" range-separator="至" start-placeholder="开始日期" type="datetimerange" value-format="timestamp" v-model.trim="form.timeData"></el-date-picker>
        <span class="form-span new-customers-tip">(指活动有效期)</span>
      </el-form-item>
      <formTitle>活动资格</formTitle>
      <!-- 参与用户 -->
      <el-form-item label="参与用户" prop="attendType">
        <el-radio-group v-model.trim="form.attendType" :disabled="isEdit">
          <el-radio label="AUDIT">新入驻用户</el-radio>
          <el-radio label="FIRST_ORDER">新动销用户</el-radio>
        </el-radio-group>
        <span class="new-customers-tip">(注：此处动销指的是客户首单非样品)</span>
      </el-form-item>
      <!-- 享受权益的时长 -->
      <el-form-item label="享受权益的时长" prop="validDays">
        {{ attendTypeText }}
        <el-input v-model.trim="form.validDays" v-int>
          <template slot="append">天</template>
        </el-input>
        <span class="form-span new-customers-tip">{{ validDayTip }}</span>
      </el-form-item>
      <formTitle>活动权益</formTitle>
      <!-- 权益类型 -->
      <el-form-item label="权益类型" prop="giftType">
        <el-radio-group v-model.trim="form.giftType" :disabled="isEdit">
          <el-radio label="FREE_DELIVERY">包邮</el-radio>
          <el-radio label="COUPON">返券</el-radio>
        </el-radio-group>
        <template v-if="isSelectCoupon">
          <el-form-item label="优惠券类型" prop="isCyclic">
            <el-radio-group v-model.trim="form.isCyclic">
              <el-radio label="1">循环满赠-组合优惠券</el-radio>
              <el-radio label="0">普通优惠券</el-radio>
            </el-radio-group>
            <div class="form-item-flex">
              <!-- 循环赠送条件 每满-->
              <el-form-item prop="thresholdAmount" label="每满" label-width="70px">
                <el-input v-model.trim="form.thresholdAmount" v-money>
                  <template slot="append">元</template>
                </el-input>
              </el-form-item>
              <el-form-item prop="couponAmount" label="赠送" label-width="70px">
                <el-input v-model.trim="form.couponAmount" v-money>
                  <template slot="append">元</template>
                </el-input>
              </el-form-item>
            </div>
          </el-form-item>
          <!--赠送券上限-->
          <el-form-item label="赠送券上限" prop="drawLimit">
            <el-radio-group v-model.trim="form.drawLimit">
              <el-radio v-for="i in _COMMON_ISFLAG" :key="i.value" :label="i.value">{{ i.label }}</el-radio>
            </el-radio-group>
            <div class="form-item-flex">
              <!-- 赠送券上限 金额上限-->
              <el-form-item prop="drawLimitAmount" label="金额上限" label-width="90px" v-if="form.drawLimit === '1'">
                <el-input v-model.trim="form.drawLimitAmount" v-money>
                  <template slot="append">元</template>
                </el-input>
                <span class="new-customers-tip">(发放优惠券金额的上限，超过部分不予发放)</span>
              </el-form-item>
            </div>
          </el-form-item>
          <!-- 选择优惠券 -->
          <el-form-item label prop="couponList">
            <div class="notice">{{ noticeContent }}</div>
            <p v-if="!isSelectCyclicCoupon">实付满{{ amountTip }}，赠送以下所有优惠券（不支持循环满赠）</p>

            <AddCoupon :editable="!isDetail" v-model.trim="form.couponList" :showContent="true" :isPayGift="false" 
            :disabled="isSelectCyclicCoupon ? true : false" :couponType="isSelectCyclicCoupon ? 'MONEY_OFF' : null"></AddCoupon>
          </el-form-item>
        </template>
      </el-form-item>
      <formTitle>活动商品</formTitle>
      <el-form-item label="参与商品" prop="commodityType">
        <el-radio-group v-model.trim="form.commodityType">
          <el-radio label="ALL">全部商品</el-radio>
          <el-radio label="GROUP_INCLUDE">指定分组</el-radio>
          <el-radio label="SUB_PART">指定商品</el-radio>
          <el-radio label="SUB_PART_BRAND">指定品牌</el-radio>
        </el-radio-group>
        <el-form-item label="请选择指定品牌" prop="includeBrandList" v-if="form.commodityType === 'SUB_PART_BRAND'" class="custom-form-item">
          <el-select v-model="form.includeBrandList" placeholder="请选择品牌（可多选）" clearable multiple filterable>
            <el-option v-for="item in brandList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
          </el-select>
        </el-form-item>
        <!-- 选择品牌分组 -->
        <el-form-item label="请选择品牌分组" prop="brandCategoryIds" v-if="isSelectBrandGroup" style="margin-top: 10px">
          <el-select v-model.trim="form.brandCategoryIds" multiple filterable placeholder="请选择">
            <el-option v-for="i in _BRAND_CLASS" :key="i.value" :label="i.label" :value="i.value"> </el-option>
          </el-select>
        </el-form-item>
        <!-- 选择商品 -->
        <el-form-item label prop="commodityList" v-if="isSelectCommodity">
          <AddCommodity :operation="isOperation" v-model.trim="form.commodityList"></AddCommodity>
        </el-form-item>
      </el-form-item>
      <el-form-item label="从参与商品剔除" prop="excludeCommodityType">
        <el-radio-group v-model.trim="form.excludeCommodityType" :disabled="form.commodityType === 'SUB_PART'">
          <el-radio label="NOT">不剔除</el-radio>
          <el-radio label="SUB_NOT_PART">剔除</el-radio>
        </el-radio-group>
        <el-form-item label="剔除品牌" prop="excludeBrandList" v-if="isSelectExcludeBrand" class="custom-form-item">
          <el-select v-model="form.excludeBrandList" placeholder="请选择品牌（可多选）" clearable multiple filterable>
            <el-option v-for="item in brandList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
          </el-select>
        </el-form-item>
        <!-- 选择商品 -->
        <el-form-item label="剔除商品" prop="excludeCommodityList" :rules="[{ required: form.commodityType === 'SUB_PART_BRAND', message: '请选择指定剔除商品', trigger: 'blur' }]" v-if="isSelectExcludeCommodity">
          <AddCommodity :operation="isOperation" v-model.trim="form.excludeCommodityList"></AddCommodity>
        </el-form-item>
      </el-form-item>

      <el-form-item v-if="!isDetail">
        <el-button :loading="loading" @click="onSubmit('form')" type="primary">提交</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import { getNewcustomerActivity, create, update } from '@/api/activity/new-customers';
import AddCommodity from '@/components/AddCommodity';
import AddCoupon from '@/components/AddCoupon/indexActivity';
import { validateInteger } from '@/utils/validate';
import dict from '@/components/Common/dicts';
import pick from 'lodash/pick';
import pickBy from 'lodash/pickBy';
import omit from 'lodash/omit';
import { listAllBrandName } from '@/api/brand/brand-info';

export default {
  name: 'new-customers-components',
  components: {
    AddCommodity,
    AddCoupon
  },
  props: {
    id: String,
    isDetail: {
      type: Boolean,
      default: false
    },
    isEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      form: {
        name: '', // 活动名称
        timeData: '', // 活动时间
        giftType: 'FREE_DELIVERY', // 权益类型 包邮 FREE_DELIVERY, 返券 COUPON
        attendType: 'AUDIT', // 有效期
        validDays: '', // 有效期天
        commodityType: 'ALL', // 活动商品类型
        excludeCommodityType: 'NOT', // 从参与商品剔除   不剔除 NOT ,指定商品SUB_NOT_PART
        brandCategoryIds: [], // 品牌分组
        commodityList: [], // 商品选择
        excludeCommodityList: [], // 剔除指定商品ID集合
        ...this.couponsInitData(),
        includeBrandList: [], // 指定品牌ID集合
        excludeBrandList: [] // 剔除品牌ID集合
      },
      rules: {
        name: [{ required: true, message: '请输入活动名称', trigger: 'blur' }],
        timeData: [{ required: true, message: '请选择活动时间', trigger: 'change' }],
        giftType: [{ required: true, message: '请选择权益类型', trigger: 'change' }],
        attendType: [{ required: true, message: '请选择参与用户', trigger: 'change' }],
        validDays: [
          { required: true, message: '请填写有效期天数', trigger: 'blur' },
          {
            validator: validateInteger,
            trigger: 'blur'
          }
        ],
        commodityType: [{ required: true, message: '请选择活动商品', trigger: 'change' }],
        excludeCommodityType: [{ required: true, message: '请选择从参与商品剔除', trigger: 'change' }],
        brandCategoryIds: [{ required: true, message: '请选择品牌商品分组', trigger: 'blur' }],
        commodityList: [{ required: true, message: '请选择指定商品', trigger: 'blur' }],
        includeBrandList: [{ required: true, message: '请选择指定品牌', trigger: 'blur' }],
        isCyclic: [{ required: true, message: '请选择优惠券赠送模式', trigger: 'blur' }],
        drawLimit: [{ required: true, message: '请选择是否有赠送上限', trigger: 'blur' }],
        thresholdAmount: [
          { required: true, message: '请输入金额', trigger: 'blur' },
          {
            validator: (_, value, callback) => {
              value > 0 ? callback() : callback('赠送金额不能小于0');
            },
            trigger: 'blur'
          }
        ],
        drawLimitAmount: [
          { required: true, message: '请输入上限金额', trigger: 'blur' },
          {
            validator: (_, value, callback) => {
              this.loading && Number(value) < Number(this.form.couponAmount) ? callback('上限金额不能小于赠送金额') : callback();
            },
            trigger: 'blur'
          }
        ],
        couponList: [{ required: true, message: '请选择优惠券', trigger: 'blur' }],
        couponAmount: [
          { required: true, message: '请输入赠送优惠券金额', trigger: 'blur' },
          {
            validator: (_, value, callback) => {
              value > 0 ? callback() : callback('赠送金额不能小于0');
            },
            trigger: 'blur'
          }
        ]
      },
      loading: false,
      couponsKeys: Object.keys(this.couponsInitData()),
      brandList: []
    };
  },
  computed: {
    isOperation() {
      return this.isEdit || this.isDetail ? (this.isEdit ? 'edit' : 'detail') : 'create';
    },
    // 是否选择返券
    isSelectCoupon() {
      return this.form.giftType === 'COUPON';
    },
    // 活动商品 -> 选择分组
    isSelectBrandGroup() {
      const flag = ['GROUP_INCLUDE'].includes(this.form.commodityType);
      return flag;
    },
    // 活动商品 -> 选择商品
    isSelectCommodity() {
      const flag = ['SUB_PART', 'SUB_NOT_PART'].includes(this.form.commodityType);
      return flag;
    },
    // 活动商品 -> 从参与商品剔除
    isSelectExcludeCommodity() {
      return this.form.excludeCommodityType === 'SUB_NOT_PART';
    },
    isSelectExcludeBrand() {
      return this.form.excludeCommodityType === 'SUB_NOT_PART' && this.form.commodityType !== 'SUB_PART_BRAND';
    },
    // 是否选择循环满赠
    isSelectCyclicCoupon() {
      return this.form.isCyclic === '1';
    },
    // 是否选择设置赠送券上限
    isSelectGiftLimit() {
      return this.form.drawLimit === '1';
    },
    // 优惠券说明
    noticeContent() {
      const { isCyclic = '1' } = this.form;
      const contents = [
        '普通优惠券主要固定送选择的所有的优惠券，仅赠送一次（不支持循环满赠）；赠送时间：达到可以发放门槛后的第二天进行发放。',
        '组合优惠券主要用于可以按照优惠券的数值大小来进行递减组合发放，发放上限根据实付金额来进行限制；赠送时间：第二天赠送前一天满足门槛的优惠券，没有达到发放的额度会往后累计到下次一起发放。'
      ];
      return contents[isCyclic];
    },
    // 有效期说明
    validDayTip() {
      const flag = this.form.attendType === 'FIRST_ORDER';
      return flag ? '(指用户动销日起XX天内参与活动)' : '(指新人注册并审核通过后xx天内参与活动)';
    },
    // 入驻、动销
    attendTypeText() {
      const { attendType = 'AUDIT' } = this.form;
      const t = {
        AUDIT: '入驻之后',
        FIRST_ORDER: '动销之后'
      };
      return t[attendType];
    },
    amountTip() {
      const num = this.form.thresholdAmount || '-';
      return ` ${num} 元`;
    }
  },
  watch: {
    'form.commodityType'(val) {
      if (val === 'SUB_PART') {
        this.form.excludeCommodityType = 'NOT';
      }
    }
  },
  created() {
    this.fetchBrand();
    this.getDict();
  },
  mounted() {
    this.initData();
  },
  methods: {
    // 获取品牌
    fetchBrand() {
      listAllBrandName().then((rs) => {
        this.brandList = rs.data.map((item) => ({
          value: item.id,
          label: item.name
        }));
      });
    },
    initData() {
      this.id && this.getData();
    },
    // 返券初始数据
    couponsInitData() {
      return {
        thresholdAmount: '', // 订单门槛金额
        couponList: [], // 优惠券列表
        couponAmount: '', // 赠送优惠券金额
        isCyclic: '1', // 优惠券赠送方式 循环/普通
        drawLimit: '1', // 优惠券是否有赠送上限
        drawLimitAmount: '' // 优惠券赠送上限金额
      };
    },
    getDict() {
      const dicts = ['BRAND_CLASS', 'COMMON_ISFLAG'];
      dicts.forEach((i) => {
        this[`_${i}`] = [];
        dict(i).then((res) => {
          this[`_${i}`].push(...res);
        });
      });
    },
    getData() {
      getNewcustomerActivity(this.id).then((res) => {
        const { data = {} } = res;
        const { beginDate, endDate } = data;
        // 表单数据复制
        Object.assign(this.form, pick(res.data || {}, Object.keys(this.form)), { timeData: [beginDate, endDate] });
        // 过滤值
        const { includeBrandList = [], excludeBrandList = [] } = this.form;
        if (includeBrandList.length) {
          this.form.includeBrandList = includeBrandList.map(({ id }) => id);
          console.log('this.form.includeBrandList', this.form.includeBrandList);
        }
        if (excludeBrandList.length) {
          this.form.excludeBrandList = excludeBrandList.map(({ id }) => id);
        }
      });
    },
    // 按逻辑剔除不相干数据
    beforeRuleOut() {
      const giftType = this.form.giftType; // 权益类型, 包邮 FREE_DELIVERY, 返券 COUPON
      const commodityType = this.form.commodityType; // 包邮商品类型：全部商品包邮-ALL，指定商品包邮-SUB_PART， 指定分组包邮 GROUP_INCLUDE
      const excludeCommodityType = this.form.excludeCommodityType; // 从参与商品剔除   不剔除 NOT ,指定商品SUB_NOT_PART
      if (giftType === 'FREE_DELIVERY') {
        // 包邮时清空返券填写的信息
        this.form = { ...this.form, ...this.couponsInitData() };
      }
      switch (commodityType) {
        case 'ALL':
          this.form.commodityList = [];
          this.form.brandCategoryIds = [];
          this.form.includeBrandList = [];
          break;
        case 'SUB_PART':
          this.form.brandCategoryIds = [];
          this.form.excludeBrandList = [];
          this.form.includeBrandList = [];
          break;
        case 'GROUP_INCLUDE':
          this.form.commodityList = [];
          this.form.includeBrandList = [];
          break;
        case 'SUB_PART_BRAND':
          this.form.brandCategoryIds = [];
          this.form.excludeBrandList = [];
          break;
      }
      if (excludeCommodityType === 'NOT') {
        this.form.excludeCommodityList = [];
        this.form.excludeBrandList = [];
      }
    },
    // 获取参数
    getParams() {
      this.beforeRuleOut();
      const { commodityList, excludeCommodityList, couponList, timeData, includeBrandList, excludeBrandList } = this.form;
      const [beginDate, endDate] = timeData;
      // 商品id集合
      const commodityIds = commodityList.map(({ id }) => id);
      // 剔除商品id集合
      const excludeCommodityIds = excludeCommodityList.map(({ id }) => id);
      // 优惠券id集合
      const couponIds = couponList.map(({ id }) => id);

      let params = pickBy(
        {
          ...this.form,
          beginDate,
          endDate,
          commodityIds,
          excludeCommodityIds,
          brandIds: includeBrandList,
          excludeBrandIds: excludeBrandList,
          couponIds,
          id: this.id
        },
        (val) => !!val
      );

      // 排除不需要的字段
      const omitKey = ['commodityList', 'couponList', 'timeData', 'excludeCommodityList', 'excludeBrandList', 'includeBrandList'];
      if (!this.isSelectCoupon) {
        omitKey.push(...this.couponsKeys, 'couponIds');
      }
      params = omit(params, omitKey);
      return params;
    },
    onSubmit(formName) {
      this.loading = true;
      this.$refs[formName].validate((valid) => {
        if (!valid) {
          this.loading = false;
          return false;
        }
        // 判断剔除品牌和商品是否有选择
        const { excludeCommodityType, excludeBrandList, excludeCommodityList } = this.form;
        if (excludeCommodityType === 'SUB_NOT_PART' && !excludeBrandList.length && !excludeCommodityList.length) {
          this.$message.error('剔除品牌与剔除商品最少要选择一个');
          this.loading = false;
          return false;
        }
        const params = this.getParams();
        const request = this.id ? update : create;
        request(params)
          .then((res) => {
            if (res.code === '0') {
              this.$message.success(res.msg);
              this.$back({ path: '/activity/new-customers/list' });
            }
          })
          .catch((err) => {
            const errMsg = err?.data?.msg || '系统错误';
            this.$alert(errMsg, {
              confirmButtonText: '确定',
              type: 'warning'
            });
          })
          .finally(() => {
            this.loading = false;
          });
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.new-customers-components {
  padding: 20px;
  .content {
    background: #fff;
    padding: 18px 30px;
    ::v-deep .el-form {
      .el-input {
        width: 400px;
      }
      .price {
        margin-left: 20px;
      }
      .form-span {
        margin-left: 20px;
      }
      .item-inline {
        .el-form-item {
          display: inline-block;
        }
      }
    }
  }
  .form-content {
    .notice {
      padding: 0 15px;
      border-radius: 3px;
      background: #ebeef5;
    }
    .cyclic {
      margin-bottom: 15px;
      .cyclic-label {
        margin-right: 10px;
      }
    }
  }
}

::v-deep .el-form {
  .el-input,
  .el-date-editor--datetimerange {
    width: 400px;
    margin-right: 10px;
  }
}
.notice {
  padding: 0 15px;
  border-radius: 3px;
  background: #ebeef5;
  font-size: 12px;
  line-height: 36px;
  margin-top: 25px;
}

.form-item-flex {
  display: flex;
  margin-top: 10px;
  margin-bottom: 20px;
  ::v-deep {
    .el-input,
    .el-date-editor--datetimerange {
      width: 200px;
    }
  }
}
.new-customers-tip {
  color: #666;
  font-size: 12px;
  line-height: 1.6;
  padding-left: 10px;
}
.custom-form-item {
  margin-top: 10px;
  width: 768px;
  ::v-deep {
    .el-input {
      width: 768px !important;
    }
  }
}
</style>
