<template>
  <el-form :model="ruleForm" :rules="rules" ref="ruleForm" class="form-container" label-width="120px">
    <div class="app-container" v-loading="loading">
      <div class="part">
        <div class="title">基本信息</div>
        <div class="content">
          <el-form-item label="优惠券类型" prop="couponType">
            <el-radio-group v-model="ruleForm.couponType" :disabled="isDetail || isEdit">
              <el-radio label="MONEY_OFF">满减券</el-radio>
              <el-radio label="RATE_OFF">折扣券</el-radio>
              <el-radio label="SAMPLE">样品券</el-radio>
              <el-radio label="DELIVERY_FEE">运费劵</el-radio>
              <el-radio label="VERIFICATION_CARD">核销卡券</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="优惠券名称" prop="name">
            <el-input v-model.trim="ruleForm.name" :disabled="isDetail" maxlength="45" show-word-limit></el-input>
          </el-form-item>
          <el-form-item label="发放总量" prop="issueNum" v-if="!showAvailableStock">
            <el-input v-model.trim="ruleForm.issueNum" :disabled="isDetail" @input="handleIssueNumInput"></el-input>
            <span class="tips">最大为8位正整数</span>
          </el-form-item>
          <el-form-item label="发放总量" prop="availableStock" v-if="showAvailableStock">
            <div>总数量：{{ ruleForm.issueNum }}张，剩余：{{ availableStockObj.remainStock }}张</div>
            <div>
              <span>调整当前库存为:</span>
              <el-input v-model.trim="ruleForm.availableStock" :disabled="isDetail" @input="handleAvailableStockInput"></el-input>
              <span class="tips">最大为8位正整数</span>
            </div>
            <div>调整后剩余库存：{{ availableStockObj.availableStock }}张，总数量:{{ availableStockObj.totalNum }}张</div>
          </el-form-item>
          <template v-if="ruleForm.couponType !== 'SAMPLE'">
            <el-form-item label="适用商品" prop="dataType" v-if="ruleForm.couponType !== 'VERIFICATION_CARD'">
              <el-radio-group v-model="ruleForm.dataType" :disabled="isDetail">
                <el-radio label="ALL">全部商品可用</el-radio>
                <br />
                <el-radio label="SUB_PART">指定商品可用</el-radio>
                <br />
                <el-form-item v-show="ruleForm.dataType === 'SUB_PART'">
                  <div v-if="isDetail === false">
                    <add-commodity :commodityList="commodityList" @updateTable="updateCurrentTable" :operation="'edit'"></add-commodity>
                  </div>
                  <div v-if="isDetail === true">
                    <add-commodity :operation="'detail'" :commodityList="commodityList" @updateTable="updateCurrentTable"></add-commodity>
                  </div>
                  <div class="validate-tips" v-if="showValidate">请选择商品</div>
                </el-form-item>
                <br />
                <el-radio label="SUB_NOT_PART">指定商品不可用</el-radio>
                <br />
                <el-form-item v-show="ruleForm.dataType === 'SUB_NOT_PART'">
                  <div v-if="isDetail === false">
                    <add-commodity :operation="'edit'" :commodityList="commodityList" @updateTable="updateCurrentTable"></add-commodity>
                  </div>
                  <div v-if="isDetail === true">
                    <add-commodity :operation="'detail'" :commodityList="commodityList" @updateTable="updateCurrentTable"></add-commodity>
                  </div>
                  <div class="validate-tips" v-if="showValidate">请选择商品</div>
                </el-form-item>
                <el-radio label="SUB_COMMODITY_CATEGORY_NOT_PART" v-show="ruleForm.couponType === 'DELIVERY_FEE'">指定商品分组不可用</el-radio>
                <el-form-item prop="commodityCategoryList" v-if="ruleForm.dataType === 'SUB_COMMODITY_CATEGORY_NOT_PART' && ruleForm.couponType === 'DELIVERY_FEE'" class="goods-category">
                  <el-select filterable multiple placeholder="请选择商品分组(可多选)" :disabled="isDetail" v-model="ruleForm.commodityCategoryList" :loading="categoriesLoading">
                    <el-option :key="category.id" :label="category.name" :value="category.id" v-for="category in categories"></el-option>
                  </el-select>
                </el-form-item>
              </el-radio-group>
            </el-form-item>
            <el-form-item v-if="ruleForm.couponType === 'MONEY_OFF' || ruleForm.couponType === 'RATE_OFF'" label="使用门槛" prop="useThreshold">
              <el-radio-group v-model="useThresholdChecked" :disabled="isDetail">
                <el-radio label="0">无使用门槛</el-radio>
                <br />
                <el-radio label="1">订单满</el-radio>
                <el-input :disabled="useThresholdChecked === '0' || isDetail" v-model.trim="ruleForm.useThreshold" class="my-input-class" @input="handleUseThreshold"></el-input>
                <span class="my-span">元</span>
              </el-radio-group>
            </el-form-item>
            <el-form-item :label="discountContentObj.title" prop="discountContent" v-if="discountContentObj.show">
              <el-input v-model.trim="ruleForm.discountContent" class="my-input-class my-discountContent" :disabled="isDetail || disableDiscountContent || izEdit === '0'" @input="handleDiscountPrice"></el-input>
              <span class="my-span">元</span>
              <div class="tips">{{ discountContentObj.tips }}</div>
            </el-form-item>
            <div v-if="ruleForm.couponType === 'RATE_OFF'">
              <el-form-item label="优惠内容" prop="discountContent">
                <span class="my-span">打</span>
                <el-input :disabled="isDetail" v-model.trim="ruleForm.discountContent" class="my-input-class my-discountContent" @input="handleDiscountContent"></el-input>
                <span class="my-span">折</span>
              </el-form-item>
              <el-form-item label prop="discountLimit">
                <el-checkbox v-model.trim="discountLimitChecked" :disabled="isDetail">最多优惠</el-checkbox>
                <el-input :disabled="!discountLimitChecked || isDetail" v-model.trim="ruleForm.discountLimit" class="my-input-class my-discountContent" @input="handleDiscountLimit"></el-input>
                <span class="my-span">元</span>
              </el-form-item>
            </div>
            <el-form-item label="是否展示商详" prop="isShow" v-if="ruleForm.couponType === 'MONEY_OFF' || ruleForm.couponType === 'RATE_OFF'">
              <el-radio-group v-model="ruleForm.isShow" :disabled="isDetail">
                <el-radio label="0">不展示在商详</el-radio>
                <br />
                <el-radio label="1">展示在商详</el-radio>
              </el-radio-group>
            </el-form-item>
          </template>
          <el-form-item label="活动时间" prop="activityDate">
            <el-date-picker
              :disabled="isDetail"
              v-model="ruleForm.activityDate"
              type="datetimerange"
              align="right"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              style="margin-left: 10px; width: 500px"
              :picker-options="mixPickerOptions"
              :default-time="['00:00:00', '23:59:59']"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="使用时间" prop="validDate">
            <el-radio label="FIXED" v-model="ruleForm.validType" :disabled="isDetail">固定时间</el-radio>
            <el-date-picker
              v-model="ruleForm.validDate"
              type="datetimerange"
              align="right"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              style="margin-left: 10px; width: 500px"
              :disabled="ruleForm.validType === 'FLOAT' || isDetail"
              :picker-options="mixPickerOptions"
              :default-time="['00:00:00', '23:59:59']"
            ></el-date-picker>
          </el-form-item>
          <el-form-item prop="validDays">
            <el-radio label="FLOAT" v-model="ruleForm.validType" :disabled="isDetail">领取后</el-radio>
            <el-input :disabled="ruleForm.validType === 'FIXED' || isDetail" v-model.trim="ruleForm.validDays" class="my-input-class" @input="handleValidDays"></el-input>
            <span class="my-span">天</span>
          </el-form-item>
        </div>
        <!-- 核销卡券领取成功配置区 -->
        <template v-if="ruleForm.couponType === 'VERIFICATION_CARD'">
          <el-form-item label="领取成功提示" prop="drawSucceedTips">
            <el-input class="my-textarea" type="textarea" v-model="ruleForm.drawSucceedTips" :disabled="isDetail" maxlength="200" show-word-limit></el-input>
          </el-form-item>
          <el-form-item label="领取成功跳转" prop="izJump">
            <el-radio-group v-model="ruleForm.izJump" :disabled="isDetail">
              <el-radio label="0">不跳转,显示【查看领取记录】</el-radio>
              <br />
              <el-radio label="1">跳转</el-radio>
            </el-radio-group>
            <div class="jump-desc-wrap" v-if="ruleForm.izJump === '1'">
              <el-form-item label="跳转按钮文案：" prop="jumpBtnText">
                <el-input maxlength="6" show-word-limit :disabled="ruleForm.izJump !== '1' || isDetail" v-model.trim="ruleForm.jumpBtnText"></el-input>
              </el-form-item>
              <el-form-item label="跳转链接配置：" prop="jumpData">
                <jump-input :showLabel="false" v-model="ruleForm.jumpData" :editable="ruleForm.izJump === '1' && !isDetail" />
              </el-form-item>
            </div>
          </el-form-item>
        </template>
        <div class="title">领用限制</div>
        <div class="content">
          <el-form-item label="每人限领次数" prop="limitNum">
            <el-radio-group v-model="limitNumChecked" :disabled="isDetail">
              <el-radio label="0">不限次数</el-radio>
              <br />
              <el-radio label="1">限领</el-radio>
              <el-select :disabled="limitNumChecked === '0' || isDetail" v-model="ruleForm.limitNum" placeholder="请选择" class="my-input-class">
                <el-option label="1" value="1"></el-option>
                <el-option label="2" value="2"></el-option>
                <el-option label="3" value="3"></el-option>
                <el-option label="4" value="4"></el-option>
                <el-option label="5" value="5"></el-option>
                <el-option label="10" value="10"></el-option>
              </el-select>
              <span class="my-span">次</span>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="使用说明" prop="remarks">
            <el-input class="my-textarea" type="textarea" v-model="ruleForm.remarks" :disabled="isDetail" maxlength="1000" show-word-limit></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="submitForm('ruleForm')" :loading="saveLoading" v-if="!isDetail">保存</el-button>
          </el-form-item>
        </div>
      </div>
    </div>
  </el-form>
</template>

<script>
import { create, update, getById } from '@/api/activity/coupon';
import AddCommodity from '@/components/AddCommodity';
import { parseDefaultTime } from '@/utils';
import { validateMoney, validateInteger } from '@/common/validator';
import { listCategories } from '@/api/commodity';
import JumpInput from '@/components/Design/JumpInput';
import pick from 'lodash/pick';
import { LocationUtils } from '@/components/Design/utils';
import { cloneDeep } from 'lodash';
import { handlePrice, handleInteger } from '@/utils/handleVlue';
export default {
  components: {
    'add-commodity': AddCommodity,
    JumpInput
  },
  name: 'my-form',
  props: {
    id: String,
    isEdit: {
      type: Boolean,
      default: false
    },
    isDetail: {
      type: Boolean,
      default: false
    },
    izEdit: {
      type: String,
      default: '1'
    }
  },
  created() {
    this.initData();
  },
  watch: {
    'ruleForm.useThreshold'(value) {
      if (value) {
        this.useThresholdChecked = '1';
      }
    },
    discountLimitChecked() {
      if (!this.discountLimitChecked) {
        this.ruleForm.discountLimit = null;
        this.$refs.ruleForm.clearValidate('discountLimit');
        this.rules.discountLimit = [];
      } else {
        this.rules.discountLimit = [{ required: true, validator: validateMoney, trigger: 'blur' }];
      }
    },
    'ruleForm.discountLimit'(value) {
      if (value) {
        this.discountLimitChecked = true;
      }
    },
    'ruleForm.dataType'(value) {
      if (value === 'ALL') {
        this.commodityList = [];
      }
    },
    useThresholdChecked() {
      if (this.useThresholdChecked === '0') {
        this.$refs.ruleForm.clearValidate('useThreshold');
        this.rules.useThreshold = [{ required: false, trigger: 'blur' }];
        this.ruleForm.useThreshold = '';
      } else {
        this.rules.useThreshold = [
          { required: true, message: '请输入金额数', trigger: 'blur' },
          { validator: validateMoney, trigger: 'blur' }
        ];
      }
    },
    'ruleForm.limitNum'(value) {
      if (value) {
        this.limitNumChecked = '1';
      }
    },
    limitNumChecked() {
      if (this.limitNumChecked === '0') {
        this.rules.limitNum = [{ required: false, trigger: 'blur' }];
        this.ruleForm.limitNum = '';
        this.$nextTick(() => {
          this.$refs.ruleForm.clearValidate('limitNum');
        });
      } else {
        this.rules.limitNum = [{ required: true, message: '请选择限领次数', trigger: 'blur' }];
      }
    },
    'ruleForm.validType'() {
      if (this.ruleForm.validType === 'FIXED') {
        this.$refs.ruleForm.clearValidate('validDays');
        this.rules.validDate = [
          {
            required: true,
            message: '请输入使用时间',
            trigger: 'change'
          }
        ];
        this.rules.validDays = [{ required: false, trigger: 'blur' }];
        this.ruleForm.validDays = '';
      } else {
        this.ruleForm.validDate = null;
        this.rules.validDate = [
          {
            required: false,
            trigger: 'change'
          }
        ];
        this.rules.validDays = [
          { required: true, message: '请输入使用时间', trigger: 'blur' },
          { validator: validateInteger, trigger: 'blur' }
        ];
        this.$nextTick(() => {
          this.$refs.ruleForm.clearValidate('validDate');
        });
      }
    }
  },
  computed: {
    data() {
      const obj = {};
      if (this.id) {
        obj.id = this.id;
      }
      // 公共要传的字段
      obj.name = this.ruleForm.name;
      obj.issueNum = this.ruleForm.issueNum;
      obj.dataType = this.ruleForm.dataType;
      obj.validType = this.ruleForm.validType;
      obj.activityId = this.ruleForm.activityId;
      obj.couponType = this.ruleForm.couponType;
      obj.remarks = this.ruleForm.remarks;
      obj.commodityList = this.ruleForm.dataType === 'ALL' ? [] : this.commodityList;
      obj.useThreshold = this.useThresholdChecked === '1' ? this.ruleForm.useThreshold : 0;
      obj.limitNum = this.limitNumChecked === '1' ? this.ruleForm.limitNum : 0;
      obj.isShow = this.ruleForm.isShow;
      const [activityStartDate, activityEndDate] = this.ruleForm.activityDate;
      obj.activityStartDate = parseDefaultTime(activityStartDate);
      obj.activityEndDate = parseDefaultTime(activityEndDate);
      if (this.ruleForm.validType === 'FIXED' && this.ruleForm.validDate) {
        const [validStartDate, validEndDate] = this.ruleForm.validDate;
        obj.validStartDate = parseDefaultTime(validStartDate);
        obj.validEndDate = parseDefaultTime(validEndDate);
      }
      if (this.ruleForm.validType === 'FLOAT' && this.ruleForm.validDays) {
        obj.validDays = this.ruleForm.validDays;
      }
      // 满减需要传的字段
      if (this.ruleForm.couponType === 'MONEY_OFF') {
        obj.discountContent = this.ruleForm.discountContent;
      }
      // 满折需要传的字段
      if (this.ruleForm.couponType === 'RATE_OFF') {
        obj.discountContent = this.ruleForm.discountContent;
        if (this.discountLimitChecked) {
          obj.discountLimit = this.ruleForm.discountLimit;
        }
      }
      // 样品需要传的字段
      if (this.ruleForm.couponType === 'SAMPLE') {
        obj.dataType = 'ALL';
        obj.useThreshold = 0;
        obj.isShow = 0;
        obj.discountContent = 0;
      }
      // 运费券
      if (this.ruleForm.couponType === 'DELIVERY_FEE') {
        obj.useThreshold = 0;
        obj.isShow = 0;
        if (this.ruleForm.dataType === 'SUB_COMMODITY_CATEGORY_NOT_PART') {
          // 指定商品分组不可用
          obj.commodityCategoryList = this.ruleForm.commodityCategoryList;
        }
        obj.discountContent = this.ruleForm.discountContent;
        if (this.showAvailableStock && this.ruleForm.availableStock && Number(this.ruleForm.availableStock >= 0)) {
          obj.availableStock = Number(this.ruleForm.availableStock);
          delete obj.issueNum;
        }
      }
      // 核销卡券
      if (this.ruleForm.couponType === 'VERIFICATION_CARD') {
        obj.useThreshold = 0;
        obj.isShow = 0;
        obj.discountContent = this.ruleForm.discountContent;
        obj.drawSucceedTips = this.ruleForm.drawSucceedTips;
        obj.izJump = this.ruleForm.izJump;
        if (this.ruleForm.izJump === '1') {
          // 跳转按钮文案
          obj.jumpBtnText = this.ruleForm.jumpBtnText;
          // 跳转链接
          const jumpDesc = {};
          const jumpDatas = cloneDeep(this.ruleForm.jumpData);
          const { location, mini_location } = pick(new LocationUtils(jumpDatas), ['location', 'mini_location']);
          jumpDesc.location = location;
          jumpDesc.mini_location = mini_location;
          jumpDesc.jumpData = jumpDatas;
          obj.jumpDesc = JSON.stringify(jumpDesc);
        }
        if (this.showAvailableStock && this.ruleForm.availableStock && Number(this.ruleForm.availableStock >= 0)) {
          obj.availableStock = Number(this.ruleForm.availableStock);
          delete obj.issueNum;
        }
      }
      return obj;
    },
    discountContentObj() {
      if (this.ruleForm.couponType === 'DELIVERY_FEE') {
        return {
          show: true,
          title: '优惠金额',
          tips: '优惠金额可以用于订单运费抵扣的金额'
        };
      }
      if (this.ruleForm.couponType === 'VERIFICATION_CARD') {
        return {
          show: true,
          title: '价值金额',
          tips: '价值金额是指核销卡券的价值'
        };
      }
      if (this.ruleForm.couponType === 'MONEY_OFF') {
        return {
          show: true,
          title: '优惠内容',
          tips: ''
        };
      }
      return {
        show: false,
        title: '',
        tips: ''
      };
    },
    availableStockObj() {
      // 剩余库存 = 总数量 - 已领取数量
      const remainStock = this.ruleForm.issueNum - this.drawNum;
      // 调整后剩余库存
      const availableStock = this.ruleForm.availableStock || '-- ';
      // 调整后总数量
      const totalNum = this.ruleForm.availableStock ? this.drawNum + Number(this.ruleForm.availableStock) : '-- ';
      return {
        remainStock,
        availableStock,
        totalNum
      };
    },
    // 是否显示调整库存
    showAvailableStock() {
      // 1.编辑状态下 &2.运费券 &3.核销卡券
      return this.isEdit && (this.ruleForm.couponType === 'DELIVERY_FEE' || this.ruleForm.couponType === 'VERIFICATION_CARD');
    },
    // 运费券未开始时可以修改优惠金额
    disableDiscountContent() {
      return this.isEdit && this.ruleForm.couponType === 'DELIVERY_FEE' && this.status !== 'FUTURE';
    }
  },
  data() {
    return {
      categoriesLoading: false,
      categories: [],
      showValidate: false, // 控制选择商品的校验的提示的显示和隐藏
      discountLimitChecked: '',
      useThresholdChecked: '0',
      limitNumChecked: '0',
      commodityList: [], // 适用商品列表 传给组件的值
      drawNum: '',
      status: '',
      ruleForm: {
        availableStock: '',
        jumpData: {}, // 跳转链接
        jumpBtnText: '', // 领取成功跳转按钮文案
        izJump: '', // 是否跳转
        drawSucceedTips: '', // 领取成功提示
        commodityCategoryList: [],
        activityId: '',
        couponType: 'MONEY_OFF',
        discountContent: '',
        validDays: '',
        discountLimit: '',
        name: '',
        issueNum: '',
        dataType: 'ALL',
        useThreshold: '',
        isShow: '0',
        activityDate: '',
        validDate: '',
        validType: 'FIXED',
        limitNum: '',
        remarks: ''
      },
      value: null,
      saveLoading: false,
      loading: false,
      rules: {
        name: [{ required: true, message: '必填信息', trigger: 'blur' }],
        validDays: [{ required: false, message: '必填信息', trigger: 'blur' }],
        couponType: [{ required: true, message: '请选择优惠券类型', trigger: 'blur' }],
        issueNum: [
          { required: true, message: '必填信息', trigger: 'blur' },
          { validator: validateInteger, trigger: 'blur' }
        ],
        dataType: [{ required: true, message: '必填信息', trigger: 'blur' }],
        useThreshold: [{ required: false, message: '请输入金额', trigger: 'blur' }],
        discountContent: [
          { required: true, message: '必填信息', trigger: 'blur' },
          { validator: validateMoney, trigger: 'blur' }
        ],
        discountLimit: [],
        isShow: [{ required: true, message: '必填信息', trigger: 'blur' }],
        remarks: [{ required: true, message: '必填信息', trigger: 'blur' }],
        limitNum: [{ required: false, message: '请选择限领次数', trigger: 'blur' }],
        activityDate: [
          {
            required: true,
            message: '请输入活动时间',
            trigger: 'change'
          }
        ],
        validDate: [
          {
            required: true,
            message: '请输入用券时间',
            trigger: 'change'
          }
        ],
        commodityCategoryList: [{ required: true, message: '请选择商品分组', trigger: 'blur' }],
        drawSucceedTips: [{ required: true, message: '必填信息', trigger: 'blur' }],
        izJump: [{ required: true, message: '必填信息', trigger: 'blur' }],
        jumpBtnText: [{ required: true, message: '必填信息', trigger: 'blur' }],
        jumpData: [
          { required: true, message: '必填信息', trigger: 'blur' },
          { validator: this.validateJumpData, trigger: 'blur' }
        ]
        // availableStock: [{ required: true, message: '必填信息', trigger: 'blur' }]
      }
    };
  },
  methods: {
    validateJumpData(rule, value, callback) {
      if (this.ruleForm.izJump === '1' && (!value || Object.keys(value).length === 0 || value.type.length === 0)) {
        callback(new Error('请选择跳转链接配置'));
      } else {
        callback();
      }
    },
    // 用来接收添加商品组件的实时展示在页面上的列表的
    updateCurrentTable(commodityList) {
      this.commodityList = commodityList;
      this.commodityList.length === 0 ? (this.showValidate = true) : (this.showValidate = false);
    },
    initData() {
      if (this.id) {
        this.fetchCounpDetail();
      }
      this.fetchCategory();
    },
    fetchCounpDetail() {
      this.loading = true;
      return getById(this.id)
        .then((response) => {
          const {
            activityId,
            couponType,
            commodityList,
            validDays,
            discountContent,
            discountLimit,
            name,
            issueNum,
            dataType,
            useThreshold,
            isShow,
            activityStartDate,
            activityEndDate,
            validStartDate,
            validEndDate,
            validType,
            limitNum,
            remarks,
            status,
            commodityCategoryList = [],
            drawSucceedTips = '',
            izJump,
            jumpBtnText = '',
            jumpDesc = '',
            drawNum = 0
          } = response.data;
          this.drawNum = drawNum;
          this.status = status;
          this.commodityList = commodityList || [];
          const activityDate = [new Date(activityStartDate), new Date(activityEndDate)];
          const validDate = [new Date(validStartDate), new Date(validEndDate)];
          let jumpData = {};
          if (jumpDesc) {
            const jumpDescription = JSON.parse(jumpDesc);
            jumpData = { ...jumpDescription.jumpData };
          }
          this.ruleForm = {
            activityId,
            couponType,
            discountContent,
            validDays,
            discountLimit,
            name,
            issueNum,
            dataType,
            useThreshold,
            isShow,
            activityDate,
            validDate,
            validType,
            limitNum,
            remarks,
            commodityCategoryList,
            drawSucceedTips,
            izJump,
            jumpBtnText,
            jumpData
          };
          if (this.ruleForm.validType === 'FIXED') {
            this.ruleForm.validDays = '';
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    submitForm(ruleForm) {
      this.listLoading = true;
      this.$refs[ruleForm].validate((valid, object) => {
        if (!valid) {
          if (this.ruleForm.dataType !== 'ALL') {
            this.commodityList.length === 0 ? (this.showValidate = true) : (this.showValidate = false);
          }
          console.log(object);
          return false;
        }
        if (this.ruleForm.dataType !== 'ALL' && this.ruleForm.dataType !== 'SUB_COMMODITY_CATEGORY_NOT_PART' && this.ruleForm.dataType !== 'VERIFICATION_CARD') {
          if (this.commodityList.length === 0) {
            this.showValidate = true;
            return false;
          } else {
            this.showValidate = false;
          }
        }
        if (this.ruleForm.activityDate && this.ruleForm.activityDate[0] instanceof Date && this.ruleForm.validDate && this.ruleForm.validDate[0] instanceof Date && this.ruleForm.activityDate[0].getTime() > this.ruleForm.validDate[0].getTime()) {
          // 判断使用时间是否大于活动时间
          this.listLoading = false;
          this.$message.error('使用时间不能在活动时间之前');
          return;
        }
        const data = { ...this.data };
        const request = this.isEdit ? update : create;
        this.saveLoading = true;
        request(data)
          .then((response) => {
            this.$message.success('保存成功');
            //  this.$router.push({ path: '/activity/coupon/list' });
            this.$back({ path: '/activity/coupon/list' });
          })
          .finally(() => {
            this.saveLoading = false;
          });
      });
    },
    fetchCategory() {
      this.categoriesLoading = true;
      listCategories()
        .then((res) => {
          this.categories = res.data || [];
        })
        .finally(() => {
          this.categoriesLoading = false;
        });
    },
    handleIssueNumInput(value) {
      this.ruleForm.issueNum = handleInteger(value, 8);
    },
    handleAvailableStockInput(value) {
      this.ruleForm.availableStock = handleInteger(value, 8);
    },
    handleDiscountPrice(value) {
      this.ruleForm.discountContent = handlePrice(value);
    },
    handleUseThreshold(value) {
      this.ruleForm.useThreshold = handlePrice(value);
    },
    handleValidDays(value) {
      this.ruleForm.validDays = handleInteger(value, 8);
    },
    handleDiscountContent(value) {
      this.ruleForm.discountContent = handlePrice(value);
    },
    handleDiscountLimit(value) {
      this.ruleForm.discountLimit = handlePrice(value);
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
$text: rgba(0, 0, 0, 0.65);
.el-form-item {
  margin: 0 0 24px;
}
.el-select,
.el-input,
.el-input__inner,
.el-textarea {
  width: 250px;
}
.form-container {
  padding-bottom: 60px;
  position: relative;
  height: 100%;
  overflow: auto;
}
.type-radio .el-radio {
  margin: 3px 6px 3px 0 !important;
}
.fix-part {
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.3);
  background-color: #fff;
  position: fixed;
  left: 0;
  z-index: 2;
  width: 100%;
  bottom: 0;
  text-align: right;
  .el-form-item {
    padding: 10px 0;
    margin: 0 20px;
  }
}
.part {
  background-color: #fff;
  box-shadow: 0 0 2px rgba(0, 0, 0, 0.2);
  & > .title {
    font-weight: bolder;
    border-bottom: 1px solid #e8e8e8;
    padding: 18px 30px;
    font-size: 16px;
    user-select: none;
  }
  & > .content {
    padding: 18px 30px;
  }
  & ~ .part {
    margin-top: 20px;
  }
}
.add-commodity {
  padding-left: 24px;
}
.el-radio {
  margin-top: 10px;
}
.my-input-class {
  width: 128px;
  margin: 0 10px;
  margin-top: 10px;
}
.my-span {
  width: 50px;
  line-height: 36px;
  font-size: 14px;
  color: #000;
}
.my-discountContent {
  margin-top: 0;
}
.validate-tips {
  color: var(--color-danger);
  font-size: 12px;
  line-height: 1;
  padding-top: 4px;
}
.goods-category {
  margin-top: 10px;
  ::v-deep .el-select {
    width: 500px;
  }
}
.tips {
  color: #999;
  font-size: 12px;
  padding: 0 10px;
}
.jump-desc-wrap {
  width: 700px;
  ::v-deep .img-form-item {
    .img-form-content {
      margin-left: 0;
    }
    .img-form-label {
      text-align: left;
    }
  }
}
::v-deep {
  .my-textarea {
    .el-input__count {
      background: transparent;
      bottom: -26px;
    }
  }
}
</style>
