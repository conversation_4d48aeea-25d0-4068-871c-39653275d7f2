<template>
  <div class="app-container wrap">
    <div class="table-container">
      <div class="commo-search-container commo-search-margin-bottom10">
        <span class="counp-name">优惠券ID：{{ $route.params.id }}</span
        ><span class="counp-name right">优惠券名称：{{ name }}</span>
      </div>
      <div class="commo-search-container commo-search-flex commo-search-margin-bottom10">
        <div class="commo-search-item">
          <span class="commo-search-item-label">分销商ID:</span>
          <div class="commo-search-item-content">
            <el-input v-model="filter.distributorId" placeholder="请输入分销商ID"></el-input>
          </div>
        </div>
        <div class="commo-search-item">
          <span class="commo-search-item-label">手机号:</span>
          <div class="commo-search-item-content">
            <el-input v-model="filter.mobile" placeholder="请输入手机号"></el-input>
          </div>
        </div>
        <div class="commo-search-item">
          <span class="commo-search-item-label">分销商名称:</span>
          <div class="commo-search-item-content">
            <el-input v-model="filter.distributorName" placeholder="请输入分销商名称"></el-input>
          </div>
        </div>
        <div class="commo-search-item">
          <span class="commo-search-item-label">领取时间</span>
          <div class="commo-search-item-content">
            <el-date-picker v-model="filter.date" type="daterange" start-placeholder="开始日期" end-placeholder="结束日期" :default-time="['00:00:00', '23:59:59']" :pickerOptions="mixPickerOptions" />
          </div>
        </div>
        <div class="commo-search-item">
          <span class="commo-search-item-label">领取方式</span>
          <div class="commo-search-item-content">
            <el-select size="small" clearable v-model="filter.drawWay">
              <el-option :label="label" :value="value" v-for="{ label, value } of $dict['marketing_user_draw_way']" :key="value"></el-option>
            </el-select>
          </div>
        </div>
        <div class="commo-search-item">
          <span class="commo-search-item-label">使用状态</span>
          <div class="commo-search-item-content">
            <el-select size="small" clearable v-model="filter.userCouponStatus">
              <el-option :label="label" :value="value" v-for="{ label, value } of $dict['soyoungzg_user_coupon_status']" :key="value"></el-option>
            </el-select>
          </div>
        </div>
        <div class="commo-search-btns">
          <el-button @click="onSearch" type="primary" size="small">查询</el-button>
          <el-button @click="onReset" size="small">重置</el-button>
          <el-button @click="onExport" :loading="exportLoading" size="small">导出</el-button>
        </div>
      </div>
      <el-table ref="multipleTable" :data="list" v-loading.body="listLoading" element-loading-text="加载中" fit highlight-current-row>
        <el-table-column label="分销商ID" prop="distributorId" width="180" align="center"></el-table-column>
        <el-table-column label="分销商名称" prop="distributorName" width="180" align="center"></el-table-column>
        <el-table-column label="手机号" prop="mobile" width="180" align="center"></el-table-column>
        <el-table-column label="领取时间" width="180" align="center">
          <template slot-scope="scope">{{ scope.row.drawDate | parseTime('{y}-{m}-{d} {h}:{i}') }}</template>
        </el-table-column>
        <el-table-column label="领取方式" width="150" align="center">
          <template slot-scope="scope">{{ scope.row.drawWayName || '-' }}</template>
        </el-table-column>
        <el-table-column label="领取备注" align="center">
          <template slot-scope="scope">{{ scope.row.remarks || '-' }}</template>
        </el-table-column>
        <el-table-column label="使用时间" width="180" align="center">
          <template slot-scope="scope">
            <span v-if="scope.row.useDate">{{ scope.row.useDate | parseTime('{y}-{m}-{d} {h}:{i}') }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="使用状态" prop="userCouponStatusName" width="120" align="center"></el-table-column>
        <el-table-column label="订单详情" width="150px" align="center">
          <template slot-scope="scope">
            <el-button type="text" size="small" v-if="scope.row.userCouponStatus === 'USED' && scope.row.verificationWay !== 'MANUAL' && scope.row.verificationWay !== 'MANUAL_RETURN'">
              <router-link class="link" :to="'/order/inquiry/detail/' + scope.row.orderId" v-if="scope.row.couponType !== 'SAMPLE'">
                <el-button type="text">查看详情</el-button>
              </router-link>
              <router-link class="link" :to="'/activity/sample/list?activeName=detail&id=' + scope.row.orderId" v-if="scope.row.couponType === 'SAMPLE'">
                <el-button type="text" v-if="!scope.row.verificationWay">查看订单详情</el-button>
                <span v-else>-</span>
              </router-link>
            </el-button>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" align="center">
          <template slot-scope="scope">
            <div>
              <span v-if="scope.row.verificationWayName">【{{ scope.row.verificationByName }}】{{ scope.row.verificationWayName }}</span>
              <el-button v-else-if="scope.row.userCouponStatus === 'UNUSED' && (scope.row.couponType === 'SAMPLE' || scope.row.couponType === 'VERIFICATION_CARD' || scope.row.couponType === 'DELIVERY_FEE')" type="text" size="small" @click="handleCancel(scope.row)">人工核销</el-button>
              <div v-else>-</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="备注" width="120" align="center" show-overflow-tooltip>
          <template slot-scope="scope">
            <span v-if="scope.row.verificationRemarks">{{ scope.row.verificationRemarks }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="pageNo" :page-sizes="[10, 20, 30, 40, 50, 100]" :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper" :total="total" :disabled="listLoading"></el-pagination>
      </div>
    </div>
    <el-dialog title="人工核销" :visible.sync="dialogVisible" append-to-body :close-on-click-modal="false" width="30%">
      <span>通过线下发放赠品给到客户时，选择人工核销，用户端优惠券状态将会更改为“已使用”</span>
      <p style="margin-top: 10px">
        <el-input type="textarea" placeholder="请输入内容" v-model="verificationRemarks" maxlength="500" show-word-limit :rows="4"> </el-input>
      </p>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitEvent">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import { listUserCouponPage, listByMemberIds, verificationUserCoupon, exportUserCouponExcel } from '@/api/activity/coupon';
import pick from 'lodash/pick';
import pickBy from 'lodash/pickBy';
import cloneDeep from 'lodash/cloneDeep';
import { parseDefaultTime } from '@/utils';
import download from '@/utils/download';
import { parseTime } from '@/utils';
export default {
  components: {},
  data() {
    const initFilter = {
      date: '',
      drawStartDate: '',
      drawEndDate: '',
      mobile: '',
      couponId: this.$route.params.id,
      drawWay: '', // 领取方式
      distributorId: '',
      userCouponStatus: '',
      distributorName: ''
    };
    return {
      initFilter,
      filter: cloneDeep(initFilter),
      date: '',
      list: [],
      listLoading: true,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      dialogVisible: false,
      verificationRemarks: '',
      userCouponId: '',
      name: '',
      exportLoading: false
    };
  },
  watch: {
    'filter.date'(value) {
      if (value) {
        const [drawStartDate, drawEndDate] = value;
        this.filter.drawStartDate = parseDefaultTime(drawStartDate);
        this.filter.drawEndDate = parseDefaultTime(drawEndDate);
      } else {
        this.filter.drawStartDate = '';
        this.filter.drawEndDate = '';
      }
    }
  },
  computed: {
    // 过滤
    data() {
      return pickBy(this.filter, (val) => !!val);
    }
  },
  mounted() {
    const { status, name } = this.$route.query;
    this.name = name;
    if (status) {
      this.filter.userCouponStatus = status;
    }
    this.fetchData();
  },
  methods: {
    fetchData() {
      this.listLoading = true;
      const listQuery = pick(this, ['pageNo', 'pageSize', 'data']);
      listUserCouponPage(listQuery)
        .then((response) => {
          this.list = response.data.list;
          const ids = response.data.list.map(({ userId }) => userId);
          this.total = response.data.total;
          return listByMemberIds(ids);
        })
        .then((res) => {
          const arrs = this.list.map((item) => {
            const data = res.data.find((i) => item.userId === i.memberId);
            return {
              ...item,
              shopName: data ? data.shopName : ''
            };
          });
          this.list = [...arrs];
        })
        .finally(() => {
          this.listLoading = false;
        });
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.pageNo = 1;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.pageNo = val;
      this.fetchData();
    },
    onSearch() {
      this.pageNo = 1;
      this.pageSize = 10;
      this.$nextTick(() => {
        this.fetchData();
      });
    },
    onReset() {
      this.pageNo = 1;
      this.pageSize = 10;
      this.filter = cloneDeep(this.initFilter);
      this.$nextTick(() => {
        this.fetchData();
      });
    },
    // 人工核销
    handleCancel(row) {
      this.verificationRemarks = '';
      this.userCouponId = row.id;
      this.dialogVisible = true;
    },
    // 核销提交
    submitEvent() {
      verificationUserCoupon({
        userCouponId: this.userCouponId,
        verificationRemarks: this.verificationRemarks
      }).then((res) => {
        this.dialogVisible = false;
        this.fetchData();
      });
    },
    onExport() {
      this.exportLoading = true;
      const listQuery = pick(this, ['data']);
      exportUserCouponExcel(listQuery.data)
        .then((res) => {
          download(res, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', `优惠券:${this.name}领取记录-${parseTime(Date.now(), '{y}-{m}-{d}-{h}:{i}:{s}')}.xlsx`);
        })
        .finally(() => {
          this.exportLoading = false;
        });
    }
  }
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
@import 'src/styles/goods-table.scss';
.create {
  margin-bottom: 8px;
}
.filter-item .link {
  margin-left: 10px;
}
.counp-name {
  font-size: 14px;
}
.counp-name.right {
  padding-left: 20px;
}
</style>
