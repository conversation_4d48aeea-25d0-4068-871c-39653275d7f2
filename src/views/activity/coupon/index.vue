<template>
  <div class="app-container">
    <div class="table-container">
      <form @submit.prevent="onSearch" class="commo-search-container commo-search-flex commo-search-margin-bottom10">
        <span class="commo-search-item">
          <label class="commo-search-item-label">优惠券名称</label>
          <div class="commo-search-item-content">
            <el-input size="small" clearable v-model.trim="filter.name" placeholder="请输入优惠券名称"></el-input>
          </div>
        </span>
        <span class="commo-search-item">
          <label class="commo-search-item-label">优惠券ID</label>
          <div class="commo-search-item-content">
            <el-input size="small" clearable v-model.trim="filter.id" placeholder="请输入优惠券ID"></el-input>
          </div>
        </span>
        <span class="commo-search-item">
          <label class="commo-search-item-label">优惠券状态</label>
          <div class="commo-search-item-content">
            <el-select size="small" clearable v-model="filter.status">
              <el-option label="全部" value="ALL"></el-option>
              <el-option label="未开始" value="FUTURE"></el-option>
              <el-option label="使用中" value="USE"></el-option>
              <el-option label="已结束" value="END"></el-option>
            </el-select>
          </div>
        </span>
        <span class="commo-search-item">
          <label class="commo-search-item-label">类型</label>
          <div class="commo-search-item-content">
            <el-select size="small" clearable v-model="filter.couponType">
              <el-option label="全部" value=""></el-option>
              <el-option label="满减券" value="MONEY_OFF"></el-option>
              <el-option label="折扣券" value="RATE_OFF"></el-option>
              <el-option label="样品券" value="SAMPLE"></el-option>
              <el-option label="运费券" value="DELIVERY_FEE"></el-option>
              <el-option label="核销卡券" value="VERIFICATION_CARD"></el-option>
            </el-select>
          </div>
        </span>
        <span class="commo-search-item">
          <label class="commo-search-item-label">创建人</label>
          <div class="commo-search-item-content">
            <el-select size="small" clearable v-model="filter.createBy">
              <el-option :key="idx" :label="item.label" :value="item.value" v-for="(item, idx) in staffList"></el-option>
            </el-select>
          </div>
        </span>
        <span class="commo-search-item">
          <label class="commo-search-item-label">创建时间</label>
          <div class="commo-search-item-content">
            <el-date-picker v-model="createDate" type="daterange" start-placeholder="开始日期" end-placeholder="结束日期" :default-time="['00:00:00', '23:59:59']" value-format="timestamp" :picker-options="mixPickerOptions" />
          </div>
        </span>
        <span class="commo-search-item">
          <label class="commo-search-item-label">创建方式</label>
          <div class="commo-search-item-content">
            <el-select size="small" clearable v-model="filter.izEdit">
              <el-option label="系统创建" value="0"></el-option>
              <el-option label="人工创建" value="1"></el-option>
            </el-select>
          </div>
        </span>
        <div class="commo-search-btns">
          <el-button native-type="submit" size="small" type="primary">查询</el-button>
          <el-button @click="onReset" size="small">重置</el-button>
          <el-button @click="onExport" :loading="exportLoading" size="small">导出</el-button>
        </div>
      </form>
      <router-link to="/activity/coupon/create">
        <Authority auth="/activity/create/coupon">
          <el-button class="add-btn" icon="el-icon-plus" size="small" type="primary">新增</el-button>
        </Authority>
      </router-link>
      <el-table :data="list" element-loading-text="加载中" fit highlight-current-row ref="multipleTable" tooltip-effect="dark" v-loading="listLoading" v-el-horizontal-scroll>
        <el-table-column label="ID" prop="id" width="180" align="center"></el-table-column>
        <el-table-column label="优惠券名称" prop="name" align="center"></el-table-column>
        <el-table-column label="类型" prop="couponTypeName" width="120" align="center"></el-table-column>
        <el-table-column label="优惠内容" prop="couponContent" width="150" align="center"></el-table-column>
        <el-table-column label="已领取/总数量" width="120" align="center">
          <template slot-scope="scope">
            <el-button size="small" type="text">
              <router-link :to="'/activity/coupon/order/' + scope.row.id + `?name=${scope.row.name}`" class="link">
                <el-button type="text">{{ scope.row.drawNum + '/' + scope.row.issueNum }}</el-button>
              </router-link>
            </el-button>
          </template>
        </el-table-column>
        <el-table-column label="已使用" width="100" align="center">
          <template slot-scope="scope">
            <el-button size="small" type="text">
              <router-link :to="'/activity/coupon/order/' + scope.row.id + `?status=USED&name=${scope.row.name}`" class="link">
                <el-button type="text">{{ scope.row.usedNum }}</el-button>
              </router-link>
            </el-button>
          </template>
        </el-table-column>
        <el-table-column label="支付总金额" prop="saleAmount" width="110" align="center">
          <template slot="header">
            <div class="header-container">
              <span>支付总金额</span>
              <el-tooltip effect="dark" content="使用了优惠券的订单支付总金额（不扣除退款订单）" placement="top">
                <i class="el-icon-info tips-icon"></i>
              </el-tooltip>
            </div>
          </template>
          <template slot-scope="scope">
            <span>{{ scope.row.saleAmount }}</span>
          </template>
        </el-table-column>
        <el-table-column label="平均客单价" prop="averageAmount" width="110" align="center">
          <template slot="header">
            <div class="header-container">
              <span>平均客单价</span>
              <el-tooltip effect="dark" content="使用优惠券支付的订单总金额/使用的优惠券总数量（不扣除退款订单）" placement="top">
                <i class="el-icon-info tips-icon"></i>
              </el-tooltip>
            </div>
          </template>
          <template slot-scope="scope">
            <span>{{ scope.row.averageAmount }}</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" prop="statusName" width="110" align="center"></el-table-column>
        <el-table-column label="创建人/时间" width="160" align="center">
          <template slot-scope="scope">
            <p>{{ scope.row.createByName }}</p>
            <p>{{ scope.row.createDate | parseTime }}</p>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120px" align="center">
          <template slot-scope="scope">
            <el-button size="small" type="text">
              <router-link :to="'/activity/coupon/detail/' + scope.row.id" class="link">
                <Authority auth="/activity/view/coupon">
                  <el-button type="text">查看</el-button>
                </Authority>
              </router-link>
              <router-link :to="'/activity/coupon/edit/' + scope.row.id + '?izEdit=' + scope.row.izEdit" class="link">
                <Authority auth="/activity/edit/coupon">
                  <el-button type="text">编辑</el-button>
                </Authority>
              </router-link>
              <el-button type="text" v-if="scope.row.couponType !== 'SAMPLE' && scope.row.izEdit === '1'">
                <promotion :id="scope.row.id" :key="scope.row.id" :promType="'coupon'"></promotion>
              </el-button>
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination :current-page="pageNo" :disabled="listLoading" :page-size="pageSize" :page-sizes="[10, 20, 30, 40, 50, 100]" :total="total" @current-change="handleCurrentChange" @size-change="handleSizeChange" background layout="total, sizes, prev, pager, next, jumper"></el-pagination>
      </div>
    </div>
  </div>
</template>
<script>
import { list, exportCouponExcel } from '@/api/activity/coupon';
import download from '@/utils/download';
import { parseTime } from '@/utils';
import pick from 'lodash/pick';
import pickBy from 'lodash/pickBy';
import cloneDeep from 'lodash/cloneDeep';
import Promotion from '@/components/Promotion';
import dict from '@/components/Common/dicts';
export default {
  name: 'activity-coupon-list',
  components: {
    promotion: Promotion
  },
  data() {
    const initFilter = {
      name: '',
      status: '', // 状态
      couponType: '',
      id: '',
      createBy: '',
      izEdit: ''
    };
    return {
      initFilter,
      filter: cloneDeep(initFilter),
      list: [],
      listLoading: true,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      multipleSelection: [],
      ids: [],
      createDate: [],
      staffList: [],
      exportLoading: false
    };
  },
  computed: {
    // 过滤
    data() {
      const { createDate, filter } = this;
      const data = { ...filter };
      if (createDate.length) {
        data.createStartDate = createDate[0];
        data.createEndDate = createDate[1];
      }
      return pickBy(data, (val) => !!val);
    }
  },
  created() {
    this.initData();
  },
  activated() {
    this.initData();
  },
  methods: {
    initData() {
      const { id = '' } = this.$route.query;
      if (id) {
        this.filter.id = id;
      }
      this.fetchStaffData();
      this.fetchData();
    },
    onSearch() {
      this.pageNo = 1;
      this.pageSize = 10;
      this.$nextTick(() => {
        this.fetchData();
      });
    },
    onReset() {
      this.pageNo = 1;
      this.pageSize = 10;
      this.createDate = [];
      this.filter = cloneDeep(this.initFilter);
      this.$nextTick(() => {
        this.fetchData();
      });
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.pageNo = 1;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.pageNo = val;
      this.fetchData();
    },
    fetchData() {
      this.listLoading = true;
      const listQuery = pick(this, ['pageNo', 'pageSize', 'data']);
      list(listQuery)
        .then((response) => {
          this.list = response.data.list;
          this.total = response.data.total;
        })
        .finally(() => {
          this.listLoading = false;
        });
    },
    fetchStaffData() {
      dict('COMMON_STAFF_LIST').then((res) => {
        this.staffList = res;
      });
    },
    // 导出优惠券列表
    onExport() {
      this.exportLoading = true;
      const listQuery = pick(this, ['data']);
      const postData = listQuery.data;
      exportCouponExcel(postData)
        .then((res) => {
          try {
            const resObj = JSON.parse(new TextDecoder('utf-8').decode(new Uint8Array(res)));
            if (resObj.success) {
              this.$message.success(resObj.msg);
            } else {
              this.$message.error(resObj.msg || '系统错误');
            }
          } catch (error) {
            download(res, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', `优惠券列表-${parseTime(Date.now(), '{y}-{m}-{d}-{h}:{i}:{s}')}.xlsx`);
          }
        })
        .finally(() => {
          this.exportLoading = false;
        });
    }
  }
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
@import './styles';
</style>
