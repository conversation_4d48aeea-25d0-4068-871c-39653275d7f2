.tips {
  padding: 30px 0;
}

.syslog {
  width: 40px;
  height: 40px;
  float: right;
  line-height: 58px;
}
.commodity-picture {
  width: 66px;
  max-height: 66px;
}
.filter-container {
  margin-bottom: 16px;
  .el-input,
  .el-select {
    width: 200px;
  }
  & > span {
    margin-right: 16px;
    display: inline-block;
    margin-bottom: 14px;
    label {
      margin-right: 8px;
      font-weight: normal;
    }
  }
}
.add-btn {
  margin-bottom: 10px;
}
.link {
  color: var(--color-primary);
  &:hover {
    color: var(--color-primary);
  }
}
.filter-name {
  margin-right: 10px;
  color: #616366;
  font-weight: 500;
}

.toggle-btn {
  ::v-deep [class*='el-icon-'] + span {
    margin-left: 0;
  }
}

.tips-icon {
  font-size: 15px;
  margin-left: 3px;
  padding-bottom: 1px;
}
.header-container {
  display: flex;
  align-items: center;
}
