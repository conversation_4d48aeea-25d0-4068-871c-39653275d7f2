<template>
  <div class="app-container wrap">
    <div class="table-container">
      <div class="commo-search-container commo-search-flex commo-search-margin-bottom10">
        <div class="commo-search-item">
          <span class="commo-search-item-label">订单时间</span>
          <div class="commo-search-item-content">
            <el-date-picker size="small" v-model="date" type="datetimerange" start-placeholder="开始日期" end-placeholder="结束日期" :default-time="['00:00:00', '23:59:59']"></el-date-picker>
          </div>
        </div>
        <div class="commo-search-item">
          <span class="commo-search-item-label">订单状态:</span>
          <div class="commo-search-item-content">
            <el-select v-model="filter.orderStatus" size="small" style="width: 100%" clearable>
              <el-option v-for="(item, idx) in statusOptions" :key="idx" :label="item.label" :value="item.value" :loading="statusOptionsLoading"></el-option>
            </el-select>
          </div>
        </div>
        <div class="commo-search-item">
          <span class="commo-search-item-label">订单号:</span>
          <div class="commo-search-item-content">
            <el-input size="small" v-model="filter.orderNo"></el-input>
          </div>
        </div>
        <div class="commo-search-btns">
          <el-button @click="onSearch" type="primary" size="small">查询</el-button>
          <el-button @click="onReset" size="small">重置</el-button>
        </div>
      </div>
      <el-table ref="multipleTable" :data="list" v-loading.body="listLoading" element-loading-text="加载中" fit highlight-current-row>
        <el-table-column align="center" label="订单号" prop="orderNo"></el-table-column>
        <el-table-column align="center" label="活动类型" prop="activityTypeName"></el-table-column>
        <el-table-column align="center" label="会员" prop="mobile"></el-table-column>
        <el-table-column align="center" label="购买时间">
          <template slot-scope="scope">{{ scope.row.buyTime | parseDefaultTime }}</template>
        </el-table-column>
        <el-table-column align="center" label="订单状态" prop="orderStatusName"></el-table-column>
        <el-table-column align="center" label="订单详情" width="150px">
          <template slot-scope="scope">
            <el-button type="text" size="small">
              <router-link class="link" :to="'/order/inquiry/detail/' + scope.row.orderId">
                <el-button type="text">查看详情</el-button>
              </router-link>
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="pageNo" :page-sizes="[10, 20, 30, 40, 50, 100]" :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper" :total="total" :disabled="listLoading"></el-pagination>
      </div>
    </div>
  </div>
</template>
<script>
import { orderList, fetchOrderStatusOptions } from '@/api/activity/fullDiscount';
import pick from 'lodash/pick';
import pickBy from 'lodash/pickBy';
import cloneDeep from 'lodash/cloneDeep';
import { parseDefaultTime } from '@/utils';
export default {
  components: {},
  data() {
    const initFilter = {
      orderStatus: '', // 状态
      orderNo: '' // 订单号
    };
    return {
      date: [],
      initFilter,
      id: this.$route.params.id,
      filter: cloneDeep(initFilter),
      list: [],
      listLoading: true,
      statusOptions: [], // 状态列表
      statusOptionsLoading: true,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      orderBy: 'buyTime desc'
    };
  },
  computed: {
    // 过滤
    data() {
      return pickBy(this.filter, (val) => !!val);
    }
  },
  mounted() {
    this.fetchData();
    this.fetchStatus();
  },
  methods: {
    fetchStatus() {
      // 订单状态列表
      this.statusOptionsLoading = true;
      fetchOrderStatusOptions()
        .then((rs) => {
          const res = rs.data.map((item) => ({
            value: item.value,
            label: item.label
          }));
          this.statusOptions = res;
        })
        .finally(() => {
          this.statusOptionsLoading = false;
        });
    },
    fetchData() {
      this.listLoading = true;
      const listQuery = pick(this, ['orderBy', 'pageNo', 'pageSize', 'data']);
      if (this.date && this.date.length > 0) {
        const [buyBeginTime, buyEndTime] = this.date;
        listQuery.data.buyBeginTime = parseDefaultTime(buyBeginTime);
        listQuery.data.buyEndTime = parseDefaultTime(buyEndTime);
      }
      listQuery.data.activityId = this.id;
      orderList(listQuery)
        .then((response) => {
          this.list = response.data.list;
          this.total = response.data.total;
        })
        .finally(() => {
          this.listLoading = false;
        });
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.pageNo = 1;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.pageNo = val;
      this.fetchData();
    },
    onSearch() {
      this.pageNo = 1;
      this.pageSize = 10;
      this.$nextTick(() => {
        this.fetchData();
      });
    },
    onReset() {
      this.pageNo = 1;
      this.pageSize = 10;
      this.filter = cloneDeep(this.initFilter);
      this.$nextTick(() => {
        this.fetchData();
      });
    }
  }
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
@import 'src/styles/goods-table.scss';
.create {
  margin-bottom: 8px;
}
.filter-item .link {
  margin-left: 10px;
}
</style>
