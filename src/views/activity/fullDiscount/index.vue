<template>
  <div class="app-container">
    <div class="table-container">
      <form @submit.prevent="onSearch" class="commo-search-container commo-search-flex commo-search-margin-bottom10">
        <span class="commo-search-item">
          <label class="commo-search-item-label">活动名称</label>
          <div class="commo-search-item-content">
            <el-input size="small" clearable v-model.trim="filter.name"></el-input>
          </div>
        </span>
        <span class="commo-search-item">
          <label class="commo-search-item-label">状态</label>
          <div class="commo-search-item-content">
            <el-select clearable size="small" v-model="filter.status">
              <el-option :key="idx" :label="item.label" :loading="statusOptionsLoading" :value="item.value" v-for="(item, idx) in statusOptions"></el-option>
            </el-select>
          </div>
        </span>
        <span class="commo-search-item">
          <label class="commo-search-item-label">类型</label>
          <div class="commo-search-item-content">
            <el-select clearable size="small" v-model="filter.moneyOffType">
              <el-option :key="idx" :label="item.label" :loading="typeOptionsLoading" :value="item.value" v-for="(item, idx) in typeOptions"></el-option>
            </el-select>
          </div>
        </span>
        <div class="commo-search-btns">
          <el-button native-type="submit" size="small" type="primary">查询</el-button>
          <el-button @click="onReset" size="small">重置</el-button>
        </div>
      </form>
      <router-link to="/activity/fullDiscount/create">
        <Authority auth="/activity/create/full-discount">
          <el-button class="add-btn" icon="el-icon-plus" size="small" type="primary">新增</el-button>
        </Authority>
      </router-link>
      <el-table :data="list" element-loading-text="加载中" fit highlight-current-row ref="multipleTable" tooltip-effect="dark" v-loading="listLoading">
        <el-table-column align="center" label="活动ID" prop="id"></el-table-column>
        <el-table-column align="center" label="活动名称" prop="name"></el-table-column>
        <el-table-column align="center" label="类型" prop="marketingConfigTypeName"></el-table-column>
        <el-table-column align="center" label="活动时间" width="200">
          <template slot-scope="scope">
            <p v-if="scope.row.startDate">开始：{{ scope.row.startDate | parseTime('{y}-{m}-{d} {h}:{i}:{s}') }}</p>
            <p v-if="scope.row.endDate">结束：{{ scope.row.endDate | parseTime('{y}-{m}-{d} {h}:{i}:{s}') }}</p>
          </template>
        </el-table-column>
        <el-table-column align="center" label="活动详情" prop="usageRuleNames"></el-table-column>
        <el-table-column align="center" label="订单数量">
          <template slot-scope="scope">
            <router-link :to="'/activity/fullDiscount/order/' + scope.row.id" class="link">
              <el-button type="text">{{ findOrderNum(scope.row.id).orderNum }}</el-button>
            </router-link>
          </template>
        </el-table-column>
        <el-table-column align="center" label="实付金额">
          <template slot-scope="scope">
            <p>{{ findOrderNum(scope.row.id).saleAmount }}</p>
          </template>
        </el-table-column>
        <el-table-column align="center" label="客单价">
          <template slot-scope="scope">
            <p>{{ findOrderNum(scope.row.id).averageAmount }}</p>
          </template>
        </el-table-column>
        <el-table-column align="center" label="状态" prop="statusName"></el-table-column>
        <el-table-column align="center" label="是否启用">
          <template slot-scope="scope">
            <el-switch :value="scope.row.isEnable | isEnableFormatter" @change="switchEnable(scope.row, scope.$index)"></el-switch>
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作" width="120px">
          <template slot-scope="scope">
            <el-button size="small" type="text">
              <router-link :to="'/activity/fullDiscount/detail/' + scope.row.id" class="link">
                <Authority auth="/activity/view/full-discount">
                  <el-button type="text">查看</el-button>
                </Authority>
              </router-link>
              <router-link :to="'/activity/fullDiscount/edit/' + scope.row.id" class="link">
                <Authority auth="/activity/edit/full-discount">
                  <el-button type="text">编辑</el-button>
                </Authority>
              </router-link>
              <el-button type="text">
                <promotion :id="scope.row.id" :key="scope.row.id" :promType="'fullDiscount'"></promotion>
              </el-button>
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination :current-page="pageNo" :disabled="listLoading" :page-size="pageSize" :page-sizes="[10, 20, 30, 40, 50, 100]" :total="total" @current-change="handleCurrentChange" @size-change="handleSizeChange" background layout="total, sizes, prev, pager, next, jumper"></el-pagination>
      </div>
    </div>
  </div>
</template>
<script>
import { list, fetchOrderNums, fetchStatusOptions, fetchMoneyTypeOptions, disable, enable } from '@/api/activity/fullDiscount';
import Promotion from '@/components/Promotion';
import pick from 'lodash/pick';
import pickBy from 'lodash/pickBy';
import cloneDeep from 'lodash/cloneDeep';
export default {
  name: 'activity-fullDiscount-list',
  components: {
    promotion: Promotion
  },
  data() {
    const initFilter = {
      name: '',
      moneyOffType: '', // 类型
      status: '' // 状态
    };
    return {
      filter: cloneDeep(initFilter),
      initFilter,
      list: [],
      statusOptions: [], // 状态列表
      statusOptionsLoading: true,
      typeOptions: [], // 类型列表
      typeOptionsLoading: true,
      listLoading: true,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      activityIdResult: []
    };
  },
  filters: {
    isEnableFormatter(isEnable) {
      let result = isEnable;
      if (isEnable === '1') {
        result = true;
      } else if (isEnable === '0') {
        result = false;
      }
      return result;
    }
  },
  computed: {
    // 过滤
    data() {
      return pickBy(this.filter, (val) => !!val);
    }
  },
  created() {
    this.initData();
  },
  activated() {
    this.initData();
  },
  methods: {
    initData() {
      this.fetchData();
      this.fetchStatus();
      this.fetchType();
    },
    fetchStatus() {
      // 状态列表
      this.statusOptionsLoading = true;
      fetchStatusOptions()
        .then((rs) => {
          const res = rs.data.map((item) => ({
            value: item.value,
            label: item.label
          }));
          this.statusOptions = res;
        })
        .finally(() => {
          this.statusOptionsLoading = false;
        });
    },
    fetchType() {
      // 类型列表
      this.typeOptionsLoading = true;
      fetchMoneyTypeOptions()
        .then((rs) => {
          const res = rs.data.map((item) => ({
            value: item.value,
            label: item.label
          }));
          this.typeOptions = res;
        })
        .finally(() => {
          this.typeOptionsLoading = false;
        });
    },
    switchEnable(row, index) {
      const list = [...this.list];
      if (row.isEnable === '1' || row.isEnable === true) {
        disable(row.id).then((rs) => {});
        list[index].isEnable = false;
      } else {
        enable(row.id).then((rs) => {});
        list[index].isEnable = true;
      }
      this.list = list;
    },
    fetchOrderNum(activityList) {
      // 通过activityid获取已完成订单数
      const postData = {
        activityIds: activityList
      };
      fetchOrderNums(postData).then((response) => {
        if (response.data) {
          this.activityIdResult = response.data;
        }
      });
    },
    findOrderNum(activityId) {
      const obj = {};
      // 已完成订单数
      let orderNum = 0;
      // 实付金额
      let saleAmount = '0.00';
      // 客单价
      let averageAmount = '0.00';
      if (this.activityIdResult.length > 0) {
        this.activityIdResult.forEach((v, i) => {
          if (v.activityId === activityId) {
            orderNum = v.orderNum;
            saleAmount = v.saleAmount;
            averageAmount = v.averageAmount;
          }
        });
      }
      obj.orderNum = orderNum;
      obj.saleAmount = saleAmount;
      obj.averageAmount = averageAmount;
      return obj;
    },
    onSearch() {
      this.pageNo = 1;
      this.pageSize = 10;
      this.$nextTick(() => {
        this.fetchData();
      });
    },
    onReset() {
      this.pageNo = 1;
      this.pageSize = 10;
      this.filter = cloneDeep(this.initFilter);
      this.$nextTick(() => {
        this.fetchData();
      });
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.pageNo = 1;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.pageNo = val;
      this.fetchData();
    },
    fetchData() {
      this.listLoading = true;
      const listQuery = pick(this, ['pageNo', 'pageSize', 'orderBy', 'data']);
      list(listQuery)
        .then((response) => {
          this.list = response.data.list;
          this.total = response.data.total;
          if (response.data.list.length > 0) {
            const activityList = [];
            response.data.list.forEach((v, i) => {
              activityList.push(v.id);
            });
            this.fetchOrderNum(activityList);
          } else {
            this.listLoading = false;
          }
        })
        .finally(() => {
          this.listLoading = false;
        });
    }
  }
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
@import './styles';
</style>
