<template>
  <el-form :model="ruleForm" :rules="rules" class="form-container" label-width="120px" ref="ruleForm">
    <div class="app-container" v-loading="loading">
      <div class="part">
        <div class="title">基本信息</div>
        <div class="content">
          <el-form-item label="活动名称" prop="name">
            <el-input :disabled="isDetail" v-model.trim="ruleForm.name"></el-input>
          </el-form-item>
          <el-form-item label="活动时间" prop="activityDate">
            <el-date-picker :disabled="isDetail" align="right" :picker-options="mixPickerOptions" :default-time="['00:00:00', '23:59:59']" end-placeholder="结束日期" start-placeholder="开始日期" style="margin-left: 10px; width: 500px" type="datetimerange" v-model="ruleForm.activityDate"></el-date-picker>
          </el-form-item>
          <el-form-item label="活动类型" prop="marketingConfigType">
            <el-radio-group :disabled="isDetail" v-model="ruleForm.marketingConfigType">
              <el-radio label="MONEY_OFF">满减</el-radio>
              <br />
              <el-radio label="RATE_OFF">满折</el-radio>
              <br />
              <el-radio label="GIFT_OFF">满赠</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="满赠类型" prop="fullGiftType" v-if="ruleForm.marketingConfigType === 'GIFT_OFF'">
            <el-radio-group :disabled="isDetail" v-model="fullGiftType">
              <el-radio label="MIN_AMOUNT">满XX元赠</el-radio>
              <br />
              <el-radio label="COMMODITY_QUANTITY">满XX件赠</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item
            :rules="{
              required: true,
              message: '必填信息',
              trigger: 'blur'
            }"
            label="优惠叠加"
            prop="isOverlay"
            v-if="ruleForm.marketingConfigType !== 'GIFT_OFF'"
          >
            <el-radio-group :disabled="isDetail" v-model="ruleForm.isOverlay">
              <el-radio label="1">叠加优惠（可与优惠券一起使用）</el-radio>
              <br />
              <el-radio label="0">不叠加优惠</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item
            :rules="{
              required: true,
              message: '必填信息',
              trigger: 'blur'
            }"
            label="活动品牌"
            prop="bizChannel"
          >
            <el-radio-group :disabled="isDetail || isEdit" :value="ruleForm.bizChannel" @input="bizChannelChange">
              <el-radio label="NORMAL">自有品牌</el-radio>
              <br />
              <el-radio label="INTERNATION">国际品牌：包含了【国际代理品牌、御强品牌、其他品牌】</el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
        <div class="title">活动规则</div>
        <div class="content">
          <div class="public-remarks">适用商品依赖于上面选择的活动品牌类型</div>
          <el-form-item label="适用商品" prop="dataType">
            <el-radio-group :disabled="isDetail" v-model="ruleForm.dataType" @input="dataTypeChange">
              <el-radio label="ALL">全部商品可用</el-radio>
              <br />
              <el-radio label="SUB_PART">指定商品可用</el-radio>
              <br />
              <el-form-item style="width: 610px" v-show="ruleForm.dataType === 'SUB_PART'">
                <div v-if="isDetail === false">
                  <add-commodity :operation="'edit'" :bizChannel="ruleForm.bizChannel" source="fullDiscount" v-model="commodityList"></add-commodity>
                </div>
                <div v-if="isDetail === true">
                  <add-commodity :operation="'detail'" :bizChannel="ruleForm.bizChannel" source="fullDiscount" v-model="commodityList"></add-commodity>
                </div>
                <div class="validate-tips" v-if="showValidate">请选择适用商品</div>
              </el-form-item>
            </el-radio-group>
          </el-form-item>
          <!-- 活动类型-满减 -->
          <el-form-item class="reduce" label="优惠设置" v-if="ruleForm.marketingConfigType === 'MONEY_OFF'">
            <div :key="item.key" class="wrap" v-for="(item, index) in ruleForm.usageRuleCmdList">
              <div class="item-header">
                {{ index + 1 }}级优惠
                <el-button @click.prevent="removeGrade(item)" type="text" v-if="index !== 0">删除</el-button>
              </div>
              <el-form-item
                :prop="'usageRuleCmdList.' + index + '.ruleData'"
                :rules="[
                  { required: true, message: '必填信息', trigger: 'blur' },
                  { validator: validateMoney, trigger: 'blur' },
                  { validator: validateMaxMoney, trigger: 'blur' }
                ]"
                label="优惠门槛"
              >
                <div>
                  <span class="my-span">满</span>
                  <el-input :disabled="isDetail" class="my-input-class my-discountContent" v-model.trim="item.ruleData"></el-input>
                  <span class="my-span">元</span>
                </div>
              </el-form-item>
              <el-form-item
                :prop="'usageRuleCmdList.' + index + '.resultData'"
                :rules="[
                  { required: true, message: '必填信息', trigger: 'blur' },
                  { validator: validateMoney, trigger: 'blur' },
                  { validator: validateMaxMoney, trigger: 'blur' }
                ]"
                label="优惠内容"
                style="width: 500px"
              >
                <div>
                  <span class="my-span">减</span>
                  <el-input :disabled="isDetail" class="my-input-class my-discountContent" v-model.trim="item.resultData"></el-input>
                  <span class="my-span">元</span>
                </div>
              </el-form-item>
            </div>
          </el-form-item>
          <!-- 活动类型-满折 -->
          <el-form-item class="discount" label="优惠设置" v-if="ruleForm.marketingConfigType === 'RATE_OFF'">
            <div :key="item.key" class="wrap" v-for="(item, index) in ruleForm.usageRuleCmdList">
              <div class="item-header">
                {{ index + 1 }}级优惠
                <el-button @click.prevent="removeGrade(item)" type="text" v-if="index !== 0">删除</el-button>
              </div>
              <el-form-item
                :prop="'usageRuleCmdList.' + index + '.ruleData'"
                :rules="[
                  { required: true, message: '必填信息', trigger: 'blur' },
                  { validator: validateInteger, trigger: 'blur' },
                  { validator: validateMaxMoney, trigger: 'blur' }
                ]"
                label="优惠门槛"
              >
                <div>
                  <span class="my-span">满</span>
                  <el-input :disabled="isDetail" class="my-input-class my-discountContent" v-model.trim="item.ruleData"></el-input>
                  <span class="my-span">件</span>
                </div>
              </el-form-item>
              <el-form-item
                :prop="'usageRuleCmdList.' + index + '.resultData'"
                :rules="[
                  { required: true, message: '必填信息', trigger: 'blur' },
                  { validator: validatediscount1, trigger: 'blur' },
                  { validator: validateMaxMoney, trigger: 'blur' }
                ]"
                label="优惠内容"
                style="width: 500px"
              >
                <div>
                  <span class="my-span">打</span>
                  <el-input :disabled="isDetail" class="my-input-class my-discountContent" v-model.trim="item.resultData"></el-input>
                  <span class="my-span">折</span>
                  <span class="my-span">（如75折，则填写为7.5折）</span>
                </div>
              </el-form-item>
            </div>
          </el-form-item>
          <!-- 活动类型-满赠 -->
          <el-form-item label="满赠方式" prop="usageRuleType" v-if="ruleForm.marketingConfigType === 'GIFT_OFF'">
            <el-radio-group :disabled="isDetail" v-model="ruleForm.usageRuleType">
              <el-radio label="LADDER">阶梯满赠</el-radio>
              <br />
              <el-radio label="CYCLE">循环满赠</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item class="gift" label="优惠设置" v-if="ruleForm.marketingConfigType === 'GIFT_OFF'">
            <div :key="item.key" class="wrap" v-for="(item, index) in ruleForm.usageRuleCmdList3">
              <div class="item-header">
                {{ index + 1 }}级优惠
                <el-button @click.prevent="removeGrade(item)" type="text" v-if="index !== 0">删除</el-button>
              </div>
              <el-form-item
                :prop="'usageRuleCmdList3.' + index + '.ruleData'"
                :rules="[
                  { required: true, message: '必填信息', trigger: 'blur' },
                  { validator: validateMoney, trigger: 'blur' },
                  { validator: validateMaxMoney, trigger: 'blur' }
                ]"
                label="优惠门槛"
                v-if="fullGiftType === 'MIN_AMOUNT'"
              >
                <div v-if="fullGiftType === 'MIN_AMOUNT'">
                  <span class="my-span" v-if="ruleForm.usageRuleType === 'LADDER'">满</span>
                  <span class="my-span" v-if="ruleForm.usageRuleType === 'CYCLE'">每满</span>
                  <el-input :disabled="isDetail" class="my-input-class my-discountContent" v-model.trim="item.ruleData"></el-input>
                  <span class="my-span" v-if="ruleForm.usageRuleType === 'LADDER'">元</span>
                  <span class="my-span" v-if="ruleForm.usageRuleType === 'CYCLE'">元（注：是实付金额，且没有上限，请慎重配置）</span>
                </div>
              </el-form-item>
              <el-form-item
                :prop="'usageRuleCmdList3.' + index + '.ruleData'"
                :rules="[
                  { required: true, message: '必填信息', trigger: 'blur' },
                  { validator: validateInteger, trigger: 'blur' },
                  { validator: validateMaxMoney, trigger: 'blur' }
                ]"
                label="优惠门槛"
                v-if="fullGiftType === 'COMMODITY_QUANTITY'"
              >
                <div v-if="fullGiftType === 'COMMODITY_QUANTITY'">
                  <span class="my-span" v-if="ruleForm.usageRuleType === 'LADDER'">满</span>
                  <span class="my-span" v-if="ruleForm.usageRuleType === 'CYCLE'">每满</span>
                  <el-input :disabled="isDetail" class="my-input-class my-discountContent" v-model.trim="item.ruleData"></el-input>
                  <span class="my-span" v-if="ruleForm.usageRuleType === 'LADDER'">件</span>
                  <span class="my-span" v-if="ruleForm.usageRuleType === 'CYCLE'">件（注：是实付金额，且没有上限，请慎重配置）</span>
                </div>
              </el-form-item>
              <el-form-item
                :prop="'usageRuleCmdList3.' + index + '.resultData'"
                :rules="{
                  required: true,
                  message: '必填信息',
                  trigger: 'blur'
                }"
                label="优惠内容"
                style="width: 610px"
              >
                <div v-if="!isDetail">
                  <add-commodity :commodityType="'gift'" :labelTitle="'选择赠品'" :bizChannel="ruleForm.bizChannel" source="fullDiscount" :multiple="false" v-model="item.resultData"></add-commodity>
                </div>
                <div v-if="isDetail">
                  <add-commodity :commodityType="'gift'" :labelTitle="'选择赠品'" :bizChannel="ruleForm.bizChannel" source="fullDiscount" :multiple="false" :operation="'detail'" v-model="item.resultData"></add-commodity>
                </div>
                <!-- <div class="validate-tips" v-if="showValidate">请选择适用商品</div> -->
              </el-form-item>
            </div>
          </el-form-item>
          <el-form-item v-if="ruleForm.usageRuleType !== 'CYCLE'">
            <div class="setting-add">
              <el-button :disabled="isDetail" @click="addGrade()" type="text">添加等级</el-button>
            </div>
          </el-form-item>
          <el-form-item>
            <el-form-item>
              <el-button :loading="saveLoading" @click="submitForm('ruleForm')" type="primary" v-if="!isDetail">保存</el-button>
            </el-form-item>
          </el-form-item>
        </div>
      </div>
    </div>
  </el-form>
</template>

<script>
import { create, update, getById } from '@/api/activity/fullDiscount';
import AddCommodity from '@/components/AddCommodity';
import { parseDefaultTime } from '@/utils';
import { validateMoney2, isInteger, validatediscount1 } from '@/utils/validate';
export default {
  components: {
    'add-commodity': AddCommodity
  },
  name: 'my-form',
  props: {
    id: String,
    isEdit: {
      type: Boolean,
      default: false
    },
    isDetail: {
      type: Boolean,
      default: false
    }
  },
  created() {
    this.initData();
  },
  computed: {
    data() {
      const obj = {};
      if (this.id) {
        obj.id = this.id;
      }
      // 公共要传的字段
      obj.name = this.ruleForm.name;
      obj.marketingConfigType = this.ruleForm.marketingConfigType;
      obj.usageRuleType = this.ruleForm.usageRuleType;
      obj.isOverlay = this.ruleForm.isOverlay;
      obj.dataType = this.ruleForm.dataType;
      obj.bizChannel = this.ruleForm.bizChannel;
      if (this.ruleForm.dataType === 'SUB_PART') {
        const commodityList = [];
        this.commodityList.forEach(function (item) {
          const { id: commodityId } = item;
          commodityList.push(commodityId);
        });
        obj.commodityList = commodityList;
      }
      const [startDate, endDate] = this.ruleForm.activityDate;
      obj.startDate = parseDefaultTime(startDate);
      obj.endDate = parseDefaultTime(endDate);
      // 满减需要传的字段
      if (this.ruleForm.marketingConfigType === 'MONEY_OFF') {
        const ruleType = 'MIN_AMOUNT';
        const resultType = 'MONEY_OFF';
        const usageRuleCmdList = [];
        this.ruleForm.usageRuleCmdList.forEach((item, index) => {
          const usageRuleItemCmdList = [];
          const usageRuleResultCmdList = [];
          usageRuleItemCmdList[0] = {
            ruleData: item.ruleData,
            ruleType: ruleType
          };
          usageRuleResultCmdList[0] = {
            resultData: item.resultData,
            resultType: resultType
          };
          usageRuleCmdList[index] = {
            usageRuleItemCmdList: usageRuleItemCmdList,
            usageRuleResultCmdList: usageRuleResultCmdList
          };
        });
        obj.usageRuleCmdList = usageRuleCmdList;
      }
      // 满折需要传的字段
      if (this.ruleForm.marketingConfigType === 'RATE_OFF') {
        const ruleType = 'COMMODITY_QUANTITY';
        const resultType = 'RATE_OFF';
        const usageRuleCmdList = [];
        this.ruleForm.usageRuleCmdList.forEach((item, index) => {
          const usageRuleItemCmdList = [];
          const usageRuleResultCmdList = [];
          usageRuleItemCmdList[0] = {
            ruleData: item.ruleData,
            ruleType: ruleType
          };
          usageRuleResultCmdList[0] = {
            resultData: item.resultData,
            resultType: resultType
          };
          usageRuleCmdList[index] = {
            usageRuleItemCmdList: usageRuleItemCmdList,
            usageRuleResultCmdList: usageRuleResultCmdList
          };
        });
        obj.usageRuleCmdList = usageRuleCmdList;
      }
      // 满赠需要传的字段
      if (this.ruleForm.marketingConfigType === 'GIFT_OFF') {
        const ruleType = this.fullGiftType;
        const resultType = 'GIFTS';
        const usageRuleCmdList = [];
        this.ruleForm.usageRuleCmdList3.forEach((item, index) => {
          const usageRuleItemCmdList = [];
          const usageRuleResultCmdList = [];
          usageRuleItemCmdList[0] = {
            ruleData: item.ruleData,
            ruleType: ruleType
          };
          usageRuleResultCmdList[0] = {
            resultData: item.resultData[0].id,
            resultType: resultType
          };
          usageRuleCmdList[index] = {
            usageRuleItemCmdList: usageRuleItemCmdList,
            usageRuleResultCmdList: usageRuleResultCmdList
          };
        });
        obj.usageRuleCmdList = usageRuleCmdList;
      }
      return obj;
    }
  },
  data() {
    return {
      fullGiftType: 'MIN_AMOUNT', // 满赠类型
      showValidate: false, // 控制选择商品的校验的提示的显示和隐藏
      commodityList: [], // 指定商品
      giftList: [], // 赠品
      ruleForm: {
        name: '', // 活动名称
        activityDate: '', // 活动时间
        marketingConfigType: 'MONEY_OFF', // 活动类型
        usageRuleType: 'LADDER', // 活动规则类型
        isOverlay: '0', // 优惠叠加
        bizChannel: '', // 活动品牌
        dataType: 'ALL', // 适用商品
        // 活动规则-满减、满折
        usageRuleCmdList: [
          {
            ruleData: '',
            resultData: ''
          }
        ],
        // 活动规则-满赠
        usageRuleCmdList3: [
          {
            ruleData: '',
            resultData: []
          }
        ]
      },
      saveLoading: false,
      dialogVisible: false,
      loading: false,
      rules: {
        name: [
          { required: true, message: '必填信息', trigger: 'blur' },
          { max: 15, message: '输入不能超过15个字', trigger: 'blur' }
        ],
        activityDate: [{ required: true, message: '必填信息', trigger: 'blur' }],
        marketingConfigType: [{ required: true, message: '请选择优惠券类型', trigger: 'blur' }],
        dataType: [{ required: true, message: '必填信息', trigger: 'blur' }]
      }
    };
  },
  methods: {
    dataTypeChange(val) {
      if (val === 'SUB_PART' && !this.ruleForm.bizChannel) {
        this.ruleForm.dataType = 'ALL';
        this.$message.error('请先选择活动品牌');
      }
    },
    // 大于0的整数
    validateInteger(rule, str, callback) {
      if (str) {
        if (typeof str === 'number') {
          str = str.toString();
        }
        const value = str.trim();
        if (!isInteger(value)) {
          callback('数据有误，请重新输入');
          return;
        }
      }
      callback();
    },
    validateMaxMoney(rule, str, callback) {
      if (str > 100000000) {
        callback('数据大于1个亿，请重新输入');
        return;
      }
      callback();
      return;
    },
    validatediscount1(rule, str, callback) {
      if (str) {
        if (typeof str === 'number') {
          str = str.toString();
        }
        const value = str.trim();
        if (!validatediscount1(value)) {
          callback('数据有误，输入的数据大于0小于10且保留一位小数');
          return;
        }
      }
      callback();
    },
    validateMoney(rule, str, callback) {
      if (str) {
        if (typeof str === 'number') {
          str = str.toString();
        }
        const value = str.trim();
        if (!validateMoney2(value)) {
          callback('数据有误，请重新输入');
          return;
        }
      }
      callback();
    },
    // 删除等级
    removeGrade(item) {
      if (this.ruleForm.marketingConfigType !== 'GIFT_OFF') {
        const index = this.ruleForm.usageRuleCmdList.indexOf(item);
        if (index !== -1) {
          this.ruleForm.usageRuleCmdList.splice(index, 1);
        }
      }
      if (this.ruleForm.marketingConfigType === 'GIFT_OFF') {
        const index = this.ruleForm.usageRuleCmdList3.indexOf(item);
        if (index !== -1) {
          this.ruleForm.usageRuleCmdList3.splice(index, 1);
        }
      }
    },
    // 添加等级
    addGrade() {
      // 添加满减等级或者满折等级
      if (this.ruleForm.marketingConfigType !== 'GIFT_OFF') {
        const obj = {
          ruleData: '',
          resultData: '',
          key: Date.now()
        };
        this.ruleForm.usageRuleCmdList.push(obj);
      }
      // 添加满赠等级
      if (this.ruleForm.marketingConfigType === 'GIFT_OFF') {
        const obj = {
          ruleData: '',
          resultData: [],
          key: Date.now()
        };
        this.ruleForm.usageRuleCmdList3.push(obj);
      }
    },
    initData() {
      if (this.id) {
        this.fetchDetail();
      }
    },
    fetchDetail() {
      this.loading = true;
      return getById(this.id)
        .then((response) => {
          const {
            bizChannel,
            name,
            marketingConfigType,
            isOverlay,
            dataType,
            endDate,
            startDate,
            usageRuleType,
            usageRuleCmdList = [
              {
                ruleData: '',
                resultData: ''
              }
            ],
            usageRuleCmdList3 = [
              {
                ruleData: '',
                resultData: []
              }
            ]
          } = response.data;
          const activityDate = [new Date(startDate), new Date(endDate)];
          const ruleForm = {
            bizChannel,
            name,
            marketingConfigType,
            usageRuleType,
            isOverlay,
            dataType,
            activityDate,
            usageRuleCmdList,
            usageRuleCmdList3
          };
          // 指定商品
          this.commodityList = response.data.commodityList;
          // 满减规则或者满折规则
          if (marketingConfigType !== 'GIFT_OFF') {
            const usageRuleCmdList = [];
            response.data.usageRuleVO.forEach((item, index) => {
              const { usageRuleResultVOs, usageRuleItemVOs } = item;
              const { resultData } = usageRuleResultVOs[0];
              const { ruleData } = usageRuleItemVOs[0];
              const obj = {
                ruleData: ruleData,
                resultData: resultData,
                key: Date.now() + index
              };
              usageRuleCmdList.push(obj);
            });
            ruleForm.usageRuleCmdList = usageRuleCmdList;
          }
          // 满赠规则
          if (marketingConfigType === 'GIFT_OFF') {
            const usageRuleCmdList3 = [];
            response.data.usageRuleVO.forEach((item, index) => {
              const resultData = [];
              const { usageRuleResultVOs, usageRuleItemVOs } = item;
              const { giftDetailVO } = usageRuleResultVOs[0];
              const { ruleData, ruleType } = usageRuleItemVOs[0];
              this.fullGiftType = ruleType;
              resultData.push(giftDetailVO);
              const obj = {
                ruleData: ruleData,
                resultData: resultData,
                key: Date.now() + index
              };
              usageRuleCmdList3.push(obj);
            });
            ruleForm.usageRuleCmdList3 = usageRuleCmdList3;
          }
          this.ruleForm = ruleForm;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    submitForm(ruleForm) {
      this.$refs[ruleForm].validate((valid) => {
        if (!valid) {
          if (this.ruleForm.dataType !== 'ALL') {
            this.commodityList.length === 0 ? (this.showValidate = true) : (this.showValidate = false);
          }
          return false;
        }
        if (this.ruleForm.dataType !== 'ALL') {
          if (this.commodityList.length === 0) {
            this.showValidate = true;
            return false;
          } else {
            this.showValidate = false;
          }
        }
        const data = { ...this.data };
        const request = this.isEdit ? update : create;
        this.saveLoading = true;
        request(data)
          .then(() => {
            this.$message.success('保存成功');
            this.$back({ path: '/activity/fullDiscount/list' });
          })
          .finally(() => {
            this.saveLoading = false;
          });
      });
    },
    // 活动品牌切换
    bizChannelChange(val) {
      const { dataType, usageRuleCmdList3 } = this.ruleForm;
      const giftCommodityList = usageRuleCmdList3.filter((item) => item.resultData.length);
      // 已选择指定商品或赠品
      if ((dataType === 'SUB_PART' && this.commodityList.length) || giftCommodityList.length) {
        this.$confirm('修改活动品牌会导致清空下面已选择的商品和赠品，请确认是否清空？').then(() => {
          this.ruleForm.bizChannel = val;
          this.commodityList = [];
          this.ruleForm.usageRuleCmdList3.map((item) => {
            item.resultData = [];
          });
        });
      } else {
        this.ruleForm.bizChannel = val;
      }
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
$text: rgba(0, 0, 0, 0.65);
.el-form-item {
  margin: 0 0 24px;
}
.el-select,
.el-input,
.el-input__inner,
.el-textarea {
  width: 250px;
}
.form-container {
  padding-bottom: 60px;
  position: relative;
  height: 100%;
  overflow: auto;
}
.type-radio .el-radio {
  margin: 3px 6px 3px 0 !important;
}
.fix-part {
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.3);
  background-color: #fff;
  position: fixed;
  left: 0;
  z-index: 2;
  width: 100%;
  bottom: 0;
  text-align: right;
  .el-form-item {
    padding: 10px 0;
    margin: 0 20px;
  }
}
.part {
  background-color: #fff;
  box-shadow: 0 0 2px rgba(0, 0, 0, 0.2);
  & > .title {
    font-weight: bolder;
    border-bottom: 1px solid #e8e8e8;
    padding: 18px 30px;
    font-size: 16px;
    user-select: none;
  }
  & > .content {
    padding: 18px 30px;
  }
  & ~ .part {
    margin-top: 20px;
  }
}
.add-commodity {
  padding-left: 24px;
}
.el-radio {
  margin-top: 10px;
}
.my-input-class {
  width: 128px;
  margin: 0 10px;
  margin-top: 10px;
}
.my-span {
  width: 50px;
  line-height: 36px;
  font-size: 14px;
  color: #000;
}
.my-discountContent {
  margin-top: 0;
}
.validate-tips {
  color: var(--color-danger);
  font-size: 12px;
  line-height: 1;
  padding-top: 4px;
}
.item-header {
  display: flex;
  justify-content: space-between;
  box-sizing: border-box;
  width: 610px;
  color: #333;
  line-height: 30px;
  font-size: 14px;
  padding: 1px 10px;
  background-color: #f7f7f7;
  margin-bottom: 20px;
}
.setting-add {
  border-top: 1px solid #e8e8e8;
  width: 610px;
}
.public-remarks {
  font-size: 13px;
  color: #666;
}
</style>
