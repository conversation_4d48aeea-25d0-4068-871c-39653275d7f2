<template>
  <div class="page-container">
    <!-- 列表展示 -->
    <sy-normal-table v-bind="table" ref="table" v-el-horizontal-scroll />
    <el-dialog title="请选择活动类型" :visible.sync="dialogVisible" width="15%">
      <div class="commo-dialog-content">
        <div class="commo-dialog-margin">
          <Authority>
            <router-link class="link" :to="'/activity/integral/add/PURCHASE'">
              <el-button type="primary">消费型</el-button>
            </router-link>
          </Authority>
        </div>
        <div class="commo-dialog-margin">
          <Authority>
            <router-link class="link" :to="'/activity/integral/add/TASK'">
              <el-button type="primary">任务型</el-button>
            </router-link>
          </Authority>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { changeStatus, creditActivityList } from '@/api/activity/integral';
import { parseTime } from '@/utils';

export default {
  name: 'integral-list',
  components: {},
  data() {
    return {
      dialogVisible: false
    };
  },
  computed: {
    table() {
      const that = this;
      return {
        filters: [
          {
            tag: 'el-input',
            prop: 'id',
            label: '活动ID',
            bind: {
              placeholder: '请输入内容'
            }
          },
          {
            tag: 'el-input',
            prop: 'name',
            label: '活动名称',
            bind: {
              placeholder: '请输入内容'
            }
          },
          {
            tag: 'sy-select',
            prop: 'status',
            label: '活动状态',
            defaultValue: 'ALL',
            bind: {
              filterable: true,
              placeholder: '',
              options: window.$vue.$dict['zg_marketing_activity_status'],
              flashOptions: true
            }
          },
          {
            tag: 'sy-select',
            prop: 'subtype',
            label: '发放类型',
            bind: {
              filterable: true,
              placeholder: '',
              options: [{ label: '全部', value: '' }, ...window.$vue.$dict['credit_activity_subtype']],
              flashOptions: true
            }
          },
          {
            tag: 'sy-select',
            prop: 'taskType',
            label: '任务类型',
            bind: {
              filterable: true,
              options: [{ label: '全部', value: '' }, ...window.$vue.$dict['credit_activity_task_type']],
              flashOptions: true
            }
          }
        ],
        btns: [
          {
            text: '+ 新增',
            type: 'primary',
            code: 'activity-integral-add',
            isJudgeShow: false,
            call: () => {
              that.createAdd();
            }
          }
        ],
        columns() {
          return [
            {
              prop: 'id',
              label: '活动ID',
              multiLine: 2,
              itemBind: {
                fixed: 'left'
              }
            },
            {
              label: '活动名称',
              prop: 'name',
              multiLine: 2
            },
            {
              label: '任务类型',
              prop: 'taskTypeNameList',
              type: 'tags'
            },
            {
              label: '发放类型',
              prop: 'subtypeName',
              width: 120
            },
            {
              label: '活动时间',
              render: (h, { row }) => (
                <div v-frag>
                  <p>{parseTime(row.startDate, '{y}-{m}-{d} {h}:{i}:{s}')}</p>
                  <p>{parseTime(row.endDate, '{y}-{m}-{d} {h}:{i}:{s}')}</p>
                </div>
              )
            },
            {
              label: '活动状态',
              prop: 'statusName',
              width: 100
            },
            {
              label: '活动参与人群',
              prop: 'customerInfoName',
              width: 120
            },
            {
              label: '启用',
              prop: 'purchaseAmountScore',
              render: (h, { row }) => (
                <div v-frag>
                  <el-switch active-value="1" inactive-value="0" value={row.izEnable} disabled={row.status === 'END'} on-change={() => that.changeEnable(row)}></el-switch>
                </div>
              )
            },
            {
              label: '更新人/更新时间',
              width: '140',
              render: (h, { row }) => (
                <div v-frag>
                  <p>{row.updateBy}</p>
                  <p>{parseTime(row.updateDate, '{y}-{m}-{d} {h}:{i}:{s}')}</p>
                </div>
              )
            },
            {
              label: '操作',
              type: 'btns',
              width: 120,
              itemBind: {
                fixed: 'right'
              },
              btns({ row }) {
                return [
                  {
                    text: '查看',
                    type: 'text',
                    code: 'activity-integral-detail',
                    isJudgeShow: false,
                    call() {
                      const toPath = `/activity/integral/detail/${row.subtype}/${row.id}`;
                      this.$router.push(toPath);
                    }
                  },
                  {
                    text: '编辑',
                    type: 'text',
                    code: 'activity-integral-edit',
                    isJudgeShow: false,
                    hide: row.status === 'END' || row.izEnable === '1',
                    call() {
                      const toPath = `/activity/integral/edit/${row.subtype}/${row.id}`;
                      this.$router.push(toPath);
                    }
                  },
                  {
                    text: '复制',
                    type: 'text',
                    code: 'activity-integral-copy',
                    isJudgeShow: false,
                    call() {
                      const toPath = `/activity/integral/copy/${row.subtype}/${row.id}`;
                      this.$router.push(toPath);
                    }
                  }
                ];
              }
            }
          ];
        },
        async search({ filtersValue, pageFilter }) {
          const { pageNo, pageSize } = pageFilter;
          const data = await creditActivityList({
            data: filtersValue,
            pageNo,
            pageSize
          });
          const list = data.data?.list || [];
          const total = data.data?.total || 0;
          return {
            list,
            total
          };
        }
      };
    }
  },
  methods: {
    // 刷新表格数据
    refreshTableList() {
      const ref = this.$refs.table;
      ref && ref.handlerSearch();
    },
    // 新增
    createAdd() {
      this.dialogVisible = true;
    },
    // 积分活动状态变更
    changeEnable(item) {
      const { id, izEnable } = item;
      const flag = izEnable === '1';
      const enableName = flag ? '禁用' : '启用';
      this.$confirm(`确认${enableName}吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          changeStatus({ id, status: flag ? '0' : '1' }).then((res) => {
            if (res.code === '0') {
              this.$message.success(res.msg);
              this.refreshTableList();
            }
          });
        })
        .catch(() => {
          this.$message.info('已取消操作');
        });
    }
  }
};
</script>

<style scoped lang="scss">
::v-deep {
  .cell {
    .el-button {
      margin: 2px;
      text-align: center;
    }
  }
}
.commo-dialog-content {
  display: flex;
  justify-content: space-around;
}
</style>
