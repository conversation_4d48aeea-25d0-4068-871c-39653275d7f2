<template>
  <el-card shadow="never">
    <div slot="header" class="page-title">{{ title }}</div>
    <div class="mod-header"><h3>基本信息</h3></div>
    <el-form :model="form" :rules="rules" label-width="140px" ref="form" label-position="left" :disabled="isDetail || (!isCopy && izEnable === '1')">
      <el-form-item label="活动名称" prop="name">
        <el-input v-model.trim="form.name" maxlength="128" placeholder="请输入活动名称"></el-input>
      </el-form-item>
      <el-form-item label="活动时间" prop="timeData">
        <el-date-picker :default-time="['00:00:00', '23:59:59']" :picker-options="pickerOptions" end-placeholder="结束日期" range-separator="至" start-placeholder="开始日期" type="datetimerange" value-format="timestamp" v-model.trim="form.timeData" :disabled="izEnable !== '-1' && !isCopy" />
        <ul class="form-tips" v-if="subType === 'PURCHASE'">
          <li>说明：</li>
          <li>1、样品订单不参与消费返积分活动；</li>
          <li>2、积分计算仅计算订单的商品实付金额，不包含邮费以及税费；</li>
          <li>3、相同时间段内的相同类型活动只能参与一次，一个客户最多只能参加一次新用户活动和一次非新用户活动；</li>
        </ul>
        <ul class="form-tips" v-else>
          <li>说明：用户在该时间段内完成对应任务发放积分</li>
        </ul>
      </el-form-item>
      <el-form-item label="活动任务类型" prop="customerThreshold" v-if="subType === 'PURCHASE'">
        <el-checkbox-group v-model="form.customerThreshold" :disabled="izEnable !== '-1' && !isCopy">
          <el-checkbox label="NEW_CUSTOMER">新用户（分销商审核通过30天内的客户）</el-checkbox>
          <el-checkbox label="COMMON_CUSTOMER">非新客用户（分销商审核通过30天以上的客户）</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="活动不参与人群" prop="izExcludeCustomerInfo">
        <el-switch v-model="form.izExcludeCustomerInfo" active-value="1" inactive-value="0"></el-switch>
        <div v-if="form.izExcludeCustomerInfo === '1'" style="margin-top: 12px">
          <el-radio-group v-model="form.excludeCustomerInfo.customerSubtype">
            <p class="radio-block">
              <el-radio label="SELECT">选择分销商</el-radio>
              <selectDistributor source="integral" :relationData="form.excludeCustomerInfo" v-show="form.excludeCustomerInfo.customerSubtype === 'SELECT'" @onSuccess="selectDistributorSuccess" />
            </p>
            <p class="radio-block">
              <el-radio label="FILE_IMPORT">导入分销商</el-radio>
              <importDistributors source="integral" :relationData="form.excludeCustomerInfo" v-show="form.excludeCustomerInfo.customerSubtype === 'FILE_IMPORT'" @onSuccess="importDistributorsSuccess" />
            </p>
          </el-radio-group>
        </div>
      </el-form-item>
      <template v-if="subType === 'TASK'">
        <div class="mod-header"><h3>活动权益</h3></div>
        <div v-for="(item, index) in form.detailCmdVOS" :key="index">
          <el-form-item :label="'任务' + (index + 1)">
            <el-select v-model="item.type" placeholder="请选择" clearable style="width: 300px" :disabled="izEnable !== '-1' && !isCopy">
              <el-option :label="v.label" :value="v.value" v-for="(v, i) in typeList" :key="i" :disabled="isTypeDisabled(v.value)"></el-option>
            </el-select>
            <el-button @click="delTask(index)" type="text" v-if="index !== 0 && !isDetail" style="margin-left: 10px">删除</el-button>
          </el-form-item>
          <el-form-item label="奖励积分数">
            <el-radio-group v-model="item.subtype">
              <div>
                <el-radio label="ALL">全部通用</el-radio>
                <el-input v-model="item.point" placeholder="请输入数值" v-money:[direction] style="width: 200px"></el-input>
                <span class="form-span">分</span>
              </div>
              <div class="radio-block-flex">
                <el-radio label="SPECIFY_GRADE">分等级奖励</el-radio>
                <div class="checkbox-list">
                  <p v-for="(grade, i) in taskGradeDetailCmds" :key="i">
                    <el-checkbox v-model="item.taskGradeDetailCmds[i].grade" :true-label="grade.gradeLabel" :false-label="null" :disabled="item.subtype !== 'SPECIFY_GRADE'">{{ grade.gradeName }}</el-checkbox>
                    <el-input v-model="item.taskGradeDetailCmds[i].point" placeholder="请输入数值" v-money:[direction] :disabled="item.subtype !== 'SPECIFY_GRADE'"></el-input>
                  </p>
                </div>
              </div>
            </el-radio-group>
          </el-form-item>
        </div>
        <el-form-item label-width="0" v-if="izEnable === '-1' || isCopy">
          <el-button size="small" type="primary" @click="createAdd" v-if="form.detailCmdVOS.length < 3 && !isDetail">+添加任务</el-button>
        </el-form-item>
        <el-form-item label-width="0">
          <div class="integral-tip">
            <p>说明：</p>
            <p>1、每项任务完成只发放1次积分，不重复发放；</p>
            <p>2、主体信息任务仅在用户完善的该用户的第一个（0-1）主体信息发放；</p>
            <p>3、添加企业微信之后，需专属顾问在企业端完成绑定发放</p>
            <p>4、积分仅支持小数点后1位；</p>
            <p>5、分销商不重复参与任务发放活动，即已经在任务活动1当中完成过添加企业微信任务，后续平台的该任务活动将不参与积分</p>
          </div>
        </el-form-item>
      </template>
      <template v-else>
        <div class="mod-header"><h3>活动商品</h3></div>
        <el-form-item label="参与商品" prop="commodityType">
          <el-radio-group v-model.trim="form.commodityType" @change="handleCommodityType">
            <el-radio label="ALL">全部商品</el-radio>
            <el-radio label="SUB_COMMODITY">指定商品</el-radio>
            <el-radio label="SUB_BRAND">指定品牌</el-radio>
          </el-radio-group>
          <el-form-item label="请选择指定品牌" prop="includeCmd.brandIds" v-if="form.commodityType === 'SUB_BRAND'">
            <el-select v-model="form.includeCmd.brandIds" placeholder="请选择品牌（可多选）" clearable multiple filterable>
              <el-option v-for="item in brandList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
            </el-select>
          </el-form-item>
          <!-- 选择商品 -->
          <el-form-item label prop="includeCmd.commodityIds" v-if="isSelectCommodity">
            <AddCommodity ref="AddCommodity" :editable="!isDetail" v-model.trim="form.includeCmd.commodityIds"></AddCommodity>
          </el-form-item>
        </el-form-item>
        <el-form-item label="从参与商品剔除" prop="izExcludeCommodity">
          <el-radio-group v-model.trim="form.izExcludeCommodity" :disabled="form.commodityType === 'SUB_COMMODITY'">
            <el-radio label="0">不剔除</el-radio>
            <el-radio label="1">剔除</el-radio>
          </el-radio-group>
          <el-form-item label="剔除品牌" prop="excludeCmd.brandIds" v-if="isSelectExcludeBrand" class="custom-form-item">
            <el-select v-model="form.excludeCmd.brandIds" placeholder="请选择品牌（可多选）" clearable multiple filterable>
              <el-option v-for="item in brandList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
            </el-select>
          </el-form-item>
          <!-- 选择商品 -->
          <el-form-item label="剔除商品" prop="excludeCmd.commodityIds" v-if="isSelectExcludeCommodity">
            <AddCommodity ref="excludeAddCommodity" :editable="!isDetail" v-model.trim="form.excludeCmd.commodityIds"></AddCommodity>
          </el-form-item>
        </el-form-item>
        <div class="mod-header"><h3>权益信息设置</h3></div>
        <div v-if="isMoreActivity" style="margin-bottom: 16px">
          <sy-tabs v-model="activity" v-bind="activityTabs" />
        </div>
        <!--新客-->
        <div v-show="activity === 'NEW_CUSTOMER'">
          <el-form-item label="基础活动资格设置" prop="newCustomerConfig.izBasic">
            <el-switch v-model="form.newCustomerConfig.izBasic" active-value="1" inactive-value="0" />
            <span style="margin-left: 12px">基础活动资格适用于除去【活动不参与人群】外+【特定奖励人群】外，其他的参与人，按照该规则走</span>
          </el-form-item>
          <el-form-item label="" prop="newCustomerConfig.basicConfig.basicType" v-if="form.newCustomerConfig.izBasic === '1'">
            <el-radio-group v-model="form.newCustomerConfig.basicConfig.basicType">
              <el-radio label="BASIC_POINT_ISSUE">基础奖励发放规则</el-radio>
              <p style="height: 12px"></p>
              <el-radio label="MULTIPLE_POINT_ISSUE">多倍积分奖励规则</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="" v-if="form.newCustomerConfig.izBasic === '1' && form.newCustomerConfig.basicConfig.basicType === 'MULTIPLE_POINT_ISSUE'" prop="newCustomerConfig.basicConfig.configPoint">
            <el-input v-model.trim="form.newCustomerConfig.basicConfig.configPoint" placeholder="请输入">
              <template slot="append">倍</template>
            </el-input>
            <span class="input-tips">(数值>1,最多输入小数点后一位)</span>
          </el-form-item>
          <el-form-item label="特定人群奖励资格设置" prop="newCustomerConfig.izSpecial">
            <el-switch v-model="form.newCustomerConfig.izSpecial" active-value="1" inactive-value="0" />
          </el-form-item>
          <template v-if="form.newCustomerConfig.izSpecial === '1'">
            <el-form-item label="选择参与人群" prop="newCustomerConfig.specialConfig.customerInfo.customerSubtype">
              <el-radio-group v-model="form.newCustomerConfig.specialConfig.customerInfo.customerSubtype">
                <p class="radio-block">
                  <el-radio label="SELECT">选择分销商</el-radio>
                  <selectDistributor v-show="form.newCustomerConfig.specialConfig.customerInfo.customerSubtype === 'SELECT'" source="integral" :relationData="form.newCustomerConfig.specialConfig.customerInfo" @onSuccess="selectSpecialDistributorSuccess($event, 'newCustomerConfig')" />
                </p>
                <p class="radio-block">
                  <el-radio label="FILE_IMPORT">导入分销商</el-radio>
                  <importDistributors v-show="form.newCustomerConfig.specialConfig.customerInfo.customerSubtype === 'FILE_IMPORT'" source="integral" :relationData="form.newCustomerConfig.specialConfig.customerInfo" @onSuccess="importSpecialDistributorsSuccess($event, 'newCustomerConfig')" />
                </p>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="选择奖励积分方式" prop="newCustomerConfig.specialConfig.specialType">
              <template v-slot:label>
                <span>选择奖励积分方式</span>
                <el-tooltip effect="dark" placement="top">
                  <div slot="content">
                    方式一：就是圈定的客户全部按照一个奖励规则走；
                    <br />
                    方式二：适用于客户圈定方式为【按采货渠道、按生命周期、按客户等级、按客户层级】三种方式，不支持【自定义客户、导入客户、按客户分组】来设置不同的奖励倍数；
                  </div>
                  <i class="el-icon-question"></i>
                </el-tooltip>
              </template>
              <el-radio-group v-model="form.newCustomerConfig.specialConfig.specialType" @input="specialTypeChange($event, 'newCustomerConfig')">
                <el-radio label="SPECIAL_BASIC_POINT_ISSUE">【基础积分】奖励发放规则</el-radio>
                <p style="height: 12px"></p>
                <el-radio label="SPECIAL_MULTIPLE_POINT_ISSUE_GENERAL">【多倍积分】方式一：按统一奖励规则走，奖励倍数</el-radio>
                <template v-if="form.newCustomerConfig.specialConfig.specialType === 'SPECIAL_MULTIPLE_POINT_ISSUE_GENERAL'">
                  <p style="height: 12px"></p>
                  <el-form-item label="" prop="newCustomerConfig.specialConfig.configPoint">
                    <el-input v-model.trim="form.newCustomerConfig.specialConfig.configPoint" placeholder="请输入">
                      <template slot="append">倍</template>
                    </el-input>
                    <span class="input-tips">(数值>1,最多输入小数点后一位)</span>
                  </el-form-item>
                </template>
                <template v-if="hasNewCustomerSpecialPointList">
                  <p style="height: 12px"></p>
                  <el-radio label="SPECIAL_MULTIPLE_POINT_ISSUE_DEFINE">【多倍积分】方式二：按客户群体设置</el-radio>
                  <el-form-item style="margin-top: 16px" label="" prop="newCustomerConfig.specialConfig.specialPointList" v-if="form.newCustomerConfig.specialConfig.specialType === 'SPECIAL_MULTIPLE_POINT_ISSUE_DEFINE'">
                    <el-table :data="form.newCustomerConfig.specialConfig.specialPointList" class="special-point-list">
                      <el-table-column :label="'人群圈定方式：' + newCustomerSubTypeName">
                        <el-table-column prop="subtype" label="参与人群" width="120">
                          <template slot-scope="{ row }">
                            {{ renderDictSubTypeName(row.subtype, 'newCustomerConfig') }}
                          </template>
                        </el-table-column>
                        <el-table-column prop="point" label="*奖励倍数">
                          <template slot-scope="scope">
                            <el-input-number v-model="scope.row.point" :precision="1" :step="0.1" :min="1" label="请输入倍数"></el-input-number>
                          </template>
                        </el-table-column>
                      </el-table-column>
                    </el-table>
                  </el-form-item>
                </template>
              </el-radio-group>
            </el-form-item>
          </template>
        </div>
        <!--普通用户-->
        <div v-show="activity === 'COMMON_CUSTOMER'">
          <el-form-item label="基础活动资格设置" prop="commonCustomerConfig.izBasic">
            <el-switch v-model="form.commonCustomerConfig.izBasic" active-value="1" inactive-value="0" />
            <span style="margin-left: 12px">基础活动资格适用于除去【活动不参与人群】外+【特定奖励人群】外，其他的参与人，按照该规则走</span>
          </el-form-item>
          <el-form-item label="" prop="commonCustomerConfig.basicConfig.basicType" v-if="form.commonCustomerConfig.izBasic === '1'">
            <el-radio-group v-model="form.commonCustomerConfig.basicConfig.basicType">
              <el-radio label="BASIC_POINT_ISSUE">基础奖励发放规则</el-radio>
              <p style="height: 12px"></p>
              <el-radio label="MULTIPLE_POINT_ISSUE">多倍积分奖励规则</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="" v-if="form.commonCustomerConfig.izBasic === '1' && form.commonCustomerConfig.basicConfig.basicType === 'MULTIPLE_POINT_ISSUE'" prop="commonCustomerConfig.basicConfig.configPoint">
            <el-input v-model.trim="form.commonCustomerConfig.basicConfig.configPoint" placeholder="请输入">
              <template slot="append">倍</template>
            </el-input>
            <span class="input-tips">(数值>1,最多输入小数点后一位)</span>
          </el-form-item>
          <el-form-item label="特定人群奖励资格设置" prop="commonCustomerConfig.izSpecial">
            <el-switch v-model="form.commonCustomerConfig.izSpecial" active-value="1" inactive-value="0" />
          </el-form-item>
          <template v-if="form.commonCustomerConfig.izSpecial === '1'">
            <el-form-item label="选择参与人群" prop="commonCustomerConfig.specialConfig.customerInfo.customerSubtype">
              <el-radio-group v-model="form.commonCustomerConfig.specialConfig.customerInfo.customerSubtype">
                <p class="radio-block">
                  <el-radio label="SELECT">选择分销商</el-radio>
                  <selectDistributor source="integral" :relationData="form.commonCustomerConfig.specialConfig.customerInfo" v-show="form.commonCustomerConfig.specialConfig.customerInfo.customerSubtype === 'SELECT'" @onSuccess="selectSpecialDistributorSuccess($event, 'commonCustomerConfig')" />
                </p>
                <p class="radio-block">
                  <el-radio label="FILE_IMPORT">导入分销商</el-radio>
                  <importDistributors
                    source="integral"
                    :relationData="form.commonCustomerConfig.specialConfig.customerInfo"
                    v-show="form.commonCustomerConfig.specialConfig.customerInfo.customerSubtype === 'FILE_IMPORT'"
                    @onSuccess="importSpecialDistributorsSuccess($event, 'commonCustomerConfig')"
                  />
                </p>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="选择奖励积分方式" prop="commonCustomerConfig.specialConfig.specialType">
              <template v-slot:label>
                <span>选择奖励积分方式</span>
                <el-tooltip effect="dark" placement="top">
                  <div slot="content">
                    方式一：就是圈定的客户全部按照一个奖励规则走；
                    <br />
                    方式二：适用于客户圈定方式为【按采货渠道、按生命周期、按客户等级、按客户层级】三种方式，不支持【自定义客户、导入客户、按客户分组】来设置不同的奖励倍数；
                  </div>
                  <i class="el-icon-question"></i>
                </el-tooltip>
              </template>
              <el-radio-group v-model="form.commonCustomerConfig.specialConfig.specialType" @input="specialTypeChange($event, 'commonCustomerConfig')">
                <p style="height: 12px"></p>
                <el-radio label="SPECIAL_BASIC_POINT_ISSUE">基础积分奖励发放规则</el-radio>
                <p style="height: 12px"></p>
                <el-radio label="SPECIAL_MULTIPLE_POINT_ISSUE_GENERAL">【多倍积分】方式一：按统一奖励规则走，奖励倍数</el-radio>
                <template v-if="form.commonCustomerConfig.specialConfig.specialType === 'SPECIAL_MULTIPLE_POINT_ISSUE_GENERAL'">
                  <p style="height: 12px"></p>
                  <el-form-item label="" prop="commonCustomerConfig.specialConfig.configPoint">
                    <el-input v-model.trim="form.commonCustomerConfig.specialConfig.configPoint" placeholder="请输入">
                      <template slot="append">倍</template>
                    </el-input>
                    <span class="input-tips">(数值>1,最多输入小数点后一位)</span>
                  </el-form-item>
                </template>
                <template v-if="hasCommonCustomerSpecialPointList">
                  <p style="height: 12px"></p>
                  <el-radio label="SPECIAL_MULTIPLE_POINT_ISSUE_DEFINE">【多倍积分】方式二：按客户群体设置</el-radio>
                  <el-form-item style="margin-top: 16px" label="" prop="commonCustomerConfig.specialConfig.specialPointList" v-if="form.commonCustomerConfig.specialConfig.specialType === 'SPECIAL_MULTIPLE_POINT_ISSUE_DEFINE'">
                    <el-table :data="form.commonCustomerConfig.specialConfig.specialPointList" class="special-point-list">
                      <el-table-column :label="'人群圈定方式：' + commonCustomerSubTypeName">
                        <el-table-column prop="subtype" label="参与人群" width="120">
                          <template slot-scope="{ row }">
                            {{ renderDictSubTypeName(row.subtype, 'commonCustomerConfig') }}
                          </template>
                        </el-table-column>
                        <el-table-column prop="point" label="*奖励倍数">
                          <template slot-scope="scope">
                            <el-input-number v-model="scope.row.point" :precision="1" :step="0.1" :min="1" label="请输入倍数"></el-input-number>
                          </template>
                        </el-table-column>
                      </el-table-column>
                    </el-table>
                  </el-form-item>
                </template>
              </el-radio-group>
            </el-form-item>
          </template>
        </div>
      </template>
    </el-form>
    <footer-bar v-if="!isDetail">
      <button-hoc :loading="loading" @click="onSubmit" type="primary">保存</button-hoc>
    </footer-bar>
  </el-card>
</template>

<script>
import { listAllBrandName } from '@/api/brand/brand-info';
import { creditActivityCreate, creditActivityUpdate, getForInfor } from '@/api/activity/integral';
import AddCommodity from '@/components/AddCommodityMultiplex';
import pickBy from 'lodash/pickBy';
import omit from 'lodash/omit';
import selectDistributor from '@/components/selectDistributor/index.vue';
import importDistributors from '@/components/ImportDistributors/index.vue';
import { validateMultiple } from '@/utils/validate';
import { cloneDeep, merge } from 'lodash';
const initExcludeCustomerInfo = {
  customerType: 'SUB_DISTRIBUTOR', // 适用客户类型
  customerSubtype: 'SELECT', // 部分分销商选择
  subtype: 'CUSTOMER_TYPE_DEFINE', // 关联分销商方式
  dataList: [] // 关联分销商数据
};
const initCustomerConfig = {
  izBasic: '0',
  basicConfig: {
    basicType: 'BASIC_POINT_ISSUE',
    configPoint: ''
  },
  izSpecial: '0',
  specialConfig: {
    specialType: 'SPECIAL_BASIC_POINT_ISSUE',
    configPoint: '',
    customerInfo: {
      customerType: 'SUB_DISTRIBUTOR', // 适用客户类型
      customerSubtype: 'SELECT', // 部分分销商选择
      subtype: 'CUSTOMER_TYPE_DEFINE', // 关联分销商方式
      dataList: [], // 关联分销商数据
      fileBusiSeq: '' // 文件导入流水号
    },
    specialPointList: []
  }
};
export default {
  name: 'integral-info',
  components: {
    importDistributors,
    selectDistributor,
    AddCommodity
  },
  props: {
    id: String,
    isAdd: {
      type: Boolean,
      default: false
    },
    isCopy: {
      type: Boolean,
      default: false
    },
    isEdit: {
      type: Boolean,
      default: false
    },
    isDetail: {
      type: Boolean,
      default: false
    },
    // 活动类型
    subType: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      izEnable: '-1',
      subtypeMap: {
        CUSTOMER_TYPE_DEFINE: 'csIds',
        CUSTOMER_TYPE_CS_ORG: 'organizationIds',
        CUSTOMER_TYPE_ACTIVE_DEGREE: 'activeDegreeIds',
        CUSTOMER_TYPE_PURCHASE_CHANNEL: 'purchaseChannelIds',
        CUSTOMER_TYPE_DISTRIBUTOR_GRADE: 'distributorGradeIds',
        CUSTOMER_TYPE_CUSTOMER_ATTRIBUTE: 'customerAttributeIds'
      },
      subtypeNameMap: {
        CUSTOMER_TYPE_ACTIVE_DEGREE: '按生命周期',
        CUSTOMER_TYPE_PURCHASE_CHANNEL: '按采货渠道',
        CUSTOMER_TYPE_DISTRIBUTOR_GRADE: '按分销商等级',
        CUSTOMER_TYPE_CUSTOMER_ATTRIBUTE: '按客户层级'
      },
      subtypeDictMap: {
        CUSTOMER_TYPE_ACTIVE_DEGREE: window.$vue.$dict['soyoungzg_active_degree'],
        CUSTOMER_TYPE_PURCHASE_CHANNEL: window.$vue.$dict['syzg_purchase_channel_type_sub'],
        CUSTOMER_TYPE_DISTRIBUTOR_GRADE: window.$vue.$dict['distributor_grade'],
        CUSTOMER_TYPE_CUSTOMER_ATTRIBUTE: window.$vue.$dict['soyoungzg_distributor_customer_attribute']
      },
      subtypeWhiteList: ['CUSTOMER_TYPE_ACTIVE_DEGREE', 'CUSTOMER_TYPE_PURCHASE_CHANNEL', 'CUSTOMER_TYPE_DISTRIBUTOR_GRADE', 'CUSTOMER_TYPE_CUSTOMER_ATTRIBUTE'],
      activity: '',
      activityTabs: {
        options: [
          { label: '新客活动', value: 'NEW_CUSTOMER' },
          { label: '普通用户活动', value: 'COMMON_CUSTOMER' }
        ]
      },
      direction: 'twoPeople',
      tableData: [
        {
          name: '自建头部',
          province: '1'
        },
        {
          name: '自建腰部',
          province: '2'
        },
        {
          name: '自建腰部',
          province: '2'
        },
        {
          name: '自建腰部',
          province: '2'
        }
      ],
      loading: false,
      brandList: [],
      typeList: [
        {
          label: '完善个人信息，填写微信号',
          value: 'IMPROVE_PERSONAL_WECHAT_INFORMATION'
        },
        {
          label: '添加专属顾问企业微信',
          value: 'ADD_CUSTOMER_SERVICE_QYWX'
        },
        {
          label: '完善主体信息',
          value: 'IMPROVE_MAIN_INFORMATION'
        }
      ],
      taskGradeDetailCmds: [
        {
          gradeName: '普通会员',
          gradeLabel: 'NORMAL'
        },
        {
          gradeName: '新星用户',
          gradeLabel: 'NEW'
        },
        {
          gradeName: '二星用户',
          gradeLabel: 'TWO'
        },
        {
          gradeName: '三星用户',
          gradeLabel: 'THREE'
        },
        {
          gradeName: '四星用户',
          gradeLabel: 'FOUR'
        },
        {
          gradeName: '五星用户',
          gradeLabel: 'FIVE'
        },
        {
          gradeName: '合伙人',
          gradeLabel: 'PARTNER'
        }
      ],
      form: {
        name: '', // 活动名称
        timeData: '', // 活动时间
        customerThreshold: [], // 客户门槛
        commodityType: 'ALL', // 适用商品类型
        types: 'BASIC_POINT_ISSUE', // IMPROVE_PERSONAL_WECHAT_INFORMATION-完善个人信息；ADD_CUSTOMER_SERVICE_QYWX-添加专属顾问企微；IMPROVE_MAIN_INFORMATION-完善主体信息；BASIC_POINT_ISSUE-基础积分发放；MULTIPLE_POINT_ISSUE-多倍积分发放
        point: '', // 积分
        izExcludeCustomerInfo: '0',
        excludeCustomerInfo: cloneDeep(initExcludeCustomerInfo),
        // 特定
        newCustomerConfig: cloneDeep(initCustomerConfig),
        commonCustomerConfig: cloneDeep(initCustomerConfig),
        // 适用商品
        includeCmd: {
          brandIds: [], // 指定品牌ID集合
          commodityIds: [] // 指定商品ID集合
        },
        // 剔除商品
        excludeCmd: {
          brandIds: [], // 剔除品牌ID集合
          commodityIds: [] // 剔除商品ID集合
        },
        izExcludeCommodity: '0', // 从参与商品剔除   不剔除 0 , 剔除 1
        detailCmdVOS: [
          {
            id: '', // 明细id
            point: null, // 积分
            subtype: 'ALL', // 会员等级门槛
            type: '', // IMPROVE_PERSONAL_WECHAT_INFORMATION-完善个人信息；ADD_CUSTOMER_SERVICE_QYWX-添加专属顾问企微；IMPROVE_MAIN_INFORMATION-完善主体信息；BASIC_POINT_ISSUE-基础积分发放；MULTIPLE_POINT_ISSUE-多倍积分发放
            taskGradeDetailCmds: [
              {
                grade: null, // 会员等级
                point: null // 积分
              },
              {
                grade: null,
                point: null
              },
              {
                grade: null,
                point: null
              },
              {
                grade: null,
                point: null
              },
              {
                grade: null,
                point: null
              },
              {
                grade: null,
                point: null
              },
              {
                grade: null,
                point: null
              }
            ] // 任务型活动权益明细
          }
        ] // 活动明细
      },
      rules: {
        name: [{ required: true, message: '请输入活动名称', trigger: 'blur' }],
        timeData: [{ required: true, message: '请选择活动时间', trigger: 'change' }],
        customerThreshold: [{ required: true, message: '请选择活动任务类型', trigger: 'change' }],
        'includeCmd.brandIds': [{ required: true, message: '请选择指定品牌', trigger: 'change' }],
        'includeCmd.commodityIds': [{ required: true, message: '请选择指定商品', trigger: 'blur' }],
        'newCustomerConfig.specialConfig.customerInfo.customerSubtype': [{ required: true, message: '请选择参与人群', trigger: 'change' }],
        'commonCustomerConfig.specialConfig.customerInfo.customerSubtype': [{ required: true, message: '请选择参与人群', trigger: 'change' }],
        'newCustomerConfig.specialConfig.specialType': [{ required: true, message: '选择奖励积分方式', trigger: 'change' }],
        'commonCustomerConfig.specialConfig.specialType': [{ required: true, message: '选择奖励积分方式', trigger: 'change' }],
        commodityType: [{ required: true, message: '请选择参与商品', trigger: 'change' }],
        izExcludeCommodity: [{ required: true, message: '请选择是否剔除参与商品', trigger: 'change' }],
        'newCustomerConfig.basicConfig.configPoint': [
          {
            required: true,
            validator: validateMultiple,
            trigger: 'change'
          }
        ],
        'commonCustomerConfig.basicConfig.configPoint': [
          {
            required: true,
            validator: validateMultiple,
            trigger: 'change'
          }
        ],
        'newCustomerConfig.specialConfig.configPoint': [
          {
            required: true,
            validator: validateMultiple,
            trigger: 'change'
          }
        ],
        'commonCustomerConfig.specialConfig.configPoint': [
          {
            required: true,
            validator: validateMultiple,
            trigger: 'change'
          }
        ],
        point: [
          {
            required: true,
            validator: (_, value, callback) => {
              if (this.form.types === 'MULTIPLE_POINT_ISSUE') {
                if (value <= 1) {
                  callback('请输入大于1的数值');
                } else {
                  callback();
                }
              } else {
                callback();
              }
            },
            trigger: 'change'
          }
        ]
      }
    };
  },
  watch: {
    'form.customerThreshold'(val) {
      if (val.length) {
        this.activity = val[0];
      } else {
        this.activity = '';
      }
    }
  },
  computed: {
    pickerOptions() {
      return {
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7;
        }
      };
    },
    isMoreActivity() {
      return this.form.customerThreshold.length > 1;
    },
    title() {
      const action = this.isAdd ? '新增' : this.isCopy ? '复制' : this.isEdit ? '编辑' : '查看';
      return `${action}活动-${this.subType === 'PURCHASE' ? '消费型' : '任务型'}`;
    },
    tip() {
      return this.subType === 'PURCHASE' ? '用户在该时间段内下单发货即可享受消费送积分活动' : '用户在该时间段内完成对应任务发放积分';
    },
    // 活动商品 -> 从参与商品剔除
    isSelectExcludeCommodity() {
      return this.form.izExcludeCommodity === '1';
    },
    // 活动商品 -> 选择商品
    isSelectCommodity() {
      return ['SUB_COMMODITY', '1'].includes(this.form.commodityType);
    },
    isSelectExcludeBrand() {
      return this.form.izExcludeCommodity === '1' && this.form.commodityType !== 'SUB_BRAND';
    },
    // 控制任务型活动权益不能添加相同任务
    isTypeDisabled() {
      const map = this.form.detailCmdVOS.reduce((acc, item) => {
        acc[item.type] = true;
        return acc;
      }, {});
      return (type) => !!map[type];
    },
    hasNewCustomerSpecialPointList() {
      const { customerInfo = {} } = this.form.newCustomerConfig.specialConfig;
      const { dataList = [], subtype = '', customerSubtype = 'SELECT' } = customerInfo;
      return customerSubtype === 'SELECT' && this.subtypeWhiteList.includes(subtype) && dataList.length > 0;
    },
    hasCommonCustomerSpecialPointList() {
      const { customerInfo = {} } = this.form.commonCustomerConfig.specialConfig;
      const { dataList = [], subtype = '', customerSubtype = 'SELECT' } = customerInfo;
      return customerSubtype === 'SELECT' && this.subtypeWhiteList.includes(subtype) && dataList.length > 0;
    },
    newCustomerSubTypeName() {
      const val = this.form.newCustomerConfig.specialConfig.customerInfo.subtype;
      return this.subtypeNameMap[val] || '';
    },
    commonCustomerSubTypeName() {
      const val = this.form.commonCustomerConfig.specialConfig.customerInfo.subtype;
      return this.subtypeNameMap[val] || '';
    }
  },
  methods: {
    // 翻译字典值
    renderDictSubTypeName(val, key) {
      const customerInfoSubtype = this.form[key].specialConfig.customerInfo.subtype;
      const currentDict = this.subtypeDictMap[customerInfoSubtype] || [];
      return currentDict.find((item) => item.value === val)?.label || '';
    },
    // 初始化按客户群体设置不同奖励倍数列表
    initSpecialPointList(key) {
      const target = this.form[key];
      const { dataList } = target.specialConfig.customerInfo;
      target.specialConfig.specialPointList = dataList.map((item) => {
        return {
          point: 1.0,
          subtype: item
        };
      });
    },
    // 活动不参与人群-选择分销商
    selectDistributorSuccess(data) {
      const type = data.subtype;
      const dataList = type ? data[this.subtypeMap[type]] || [] : [];
      Object.assign(this.form.excludeCustomerInfo, data, {
        subtype: type,
        dataList
      });
      this.clearValidate('excludeCustomerInfo.customerSubtype');
    },
    importDistributorsSuccess(data) {
      Object.assign(this.form.excludeCustomerInfo, {
        fileBusiSeq: data.busiSeq,
        fileUrl: data.fileUrl
      });
      this.clearValidate('excludeCustomerInfo.customerSubtype');
    },
    // 特定人群奖励设置-选择分销商
    selectSpecialDistributorSuccess(data, key) {
      const type = data.subtype;
      const dataList = type ? data[this.subtypeMap[type]] || [] : [];
      Object.assign(this.form[key].specialConfig.customerInfo, data, {
        subtype: type,
        dataList
      });
      const { specialType } = this.form[key].specialConfig;
      this.clearValidate(`${key}.specialConfig.customerInfo.customerSubtype`);
      const target = key === 'newCustomerConfig' ? this.hasNewCustomerSpecialPointList : this.hasCommonCustomerSpecialPointList;
      if (target) {
        this.initSpecialPointList(key);
      } else if (specialType === 'SPECIAL_MULTIPLE_POINT_ISSUE_DEFINE') {
        this.form[key].specialConfig.specialType = 'SPECIAL_BASIC_POINT_ISSUE';
      }
    },
    // 特定人群奖励设置-导入分销商
    importSpecialDistributorsSuccess(data, key) {
      Object.assign(this.form[key].specialConfig.customerInfo, {
        fileBusiSeq: data.busiSeq,
        fileUrl: data.fileUrl
      });
      this.clearValidate(`${key}.specialConfig.customerInfo.customerSubtype`);
    },
    // 选择奖励积分方式改变
    specialTypeChange(val, key) {
      if (val === 'SPECIAL_MULTIPLE_POINT_ISSUE_DEFINE') {
        this.initSpecialPointList(key);
      }
    },
    // 移除校验提示
    clearValidate(formProp) {
      this.$refs['form'].clearValidate(formProp);
    },
    // 获取品牌
    fetchBrand() {
      listAllBrandName().then((rs) => {
        this.brandList = rs.data.map((item) => ({
          value: item.id,
          label: item.name
        }));
      });
    },
    initData() {
      this.id && this.getData();
    },
    // 添加任务
    createAdd() {
      if (this.form.detailCmdVOS.length === 3) {
        return true;
      }
      const detailCmdVOS = {
        id: '',
        point: '',
        subtype: 'ALL',
        type: '',
        taskGradeDetailCmds: [
          {
            grade: null,
            point: null
          },
          {
            grade: null,
            point: null
          },
          {
            grade: null,
            point: null
          },
          {
            grade: null,
            point: null
          },
          {
            grade: null,
            point: null
          },
          {
            grade: null,
            point: null
          },
          {
            grade: null,
            point: null
          }
        ] // 任务型活动权益明细
      };
      this.form.detailCmdVOS.push(detailCmdVOS);
    },
    // 删除任务
    delTask(index) {
      this.form.detailCmdVOS.splice(index, 1);
    },
    // 获取活动详情
    getData() {
      getForInfor(this.id).then(({ data = {} }) => {
        const { startDate, endDate, excludeCustomerInfo = {}, izEnable = '-1' } = data;
        this.izEnable = izEnable;
        const newExcludeCustomerInfo = merge(cloneDeep(initExcludeCustomerInfo), excludeCustomerInfo);
        const key = this.subtypeMap[newExcludeCustomerInfo.subtype];
        if (key) {
          newExcludeCustomerInfo[key] = newExcludeCustomerInfo?.dataList || [];
        }
        const newCustomerConfig = merge(cloneDeep(initCustomerConfig), data.newCustomerConfig || {});
        const commonCustomerConfig = merge(cloneDeep(initCustomerConfig), data.commonCustomerConfig || {});
        const formData = {
          ...data,
          timeData: this.isCopy ? [] : [startDate, endDate],
          name: data.name || null,
          excludeCustomerInfo: newExcludeCustomerInfo
        };
        Object.assign(this.form, formData);
        // 处理消费型活动
        if (data.subtype === 'PURCHASE') {
          const newCustomerInfo = newCustomerConfig.specialConfig.customerInfo;
          const newCustomerKey = this.subtypeMap[newCustomerInfo.subtype];
          if (newCustomerKey) {
            newCustomerInfo[newCustomerKey] = newCustomerInfo?.dataList || [];
          }
          const commonCustomerInfo = commonCustomerConfig.specialConfig.customerInfo;
          const commonCustomerKey = this.subtypeMap[commonCustomerInfo.subtype];
          if (commonCustomerKey) {
            commonCustomerInfo[commonCustomerKey] = commonCustomerInfo?.dataList || [];
          }
          Object.assign(this.form, {
            includeCmd: {
              brandIds: (data?.includeVO?.brandList || []).map((item) => item.id),
              commodityIds: data?.includeVO?.commodityList || []
            },
            excludeCmd: {
              brandIds: (data?.excludeVO?.brandList || []).map((item) => item.id),
              commodityIds: data?.excludeVO?.commodityList || []
            },
            newCustomerConfig,
            commonCustomerConfig
          });
        } else {
          // 处理任务型活动权益数据并初始化
          const detailCmdVOS = data?.creditActivityDetailVOS || [];
          this.form.detailCmdVOS = detailCmdVOS.map((item) => {
            const taskGradeDetailVOS = item?.taskGradeDetailVOS || [];
            item.id = this.isCopy ? '' : item?.id || '';
            // 初始化任务权益并拼接数据
            item.taskGradeDetailCmds = this.taskGradeDetailCmds.map((i) => {
              const target = taskGradeDetailVOS.find((v) => v.grade === i.gradeLabel);
              return target ? { ...target } : { grade: null, point: null };
            });
            return item;
          });
        }
        this.$nextTick(() => {
          const ref = this.$refs.form;
          ref && ref.clearValidate();
        });
      });
    },

    // 初始化指定商品
    handleCommodityType(val) {
      if (val === 'SUB_COMMODITY') {
        this.form.izExcludeCommodity = '0';
        this.form.excludeCmd = {
          brandIds: [],
          commodityIds: []
        };
      }
    },
    // 按逻辑剔除不相干数据
    beforeRuleOut() {
      const subType = this.subType; // 活动类型, 购物型 PURCHASE, 任务型 TASK
      const commodityType = this.form.commodityType; // 全部商品-ALL，指定商品-SUB_COMMODITY 指定分组 SUB_BRAND
      const izExcludeCommodity = this.form.izExcludeCommodity; // 从参与商品剔除   不剔除 1 , 剔除 0
      if (subType === 'PURCHASE') {
        switch (commodityType) {
          case 'ALL':
            this.form.includeCmd = {
              brandIds: [],
              commodityIds: []
            };
            break;
          case 'SUB_COMMODITY':
            this.form.includeCmd.brandIds = [];
            break;
          case 'SUB_BRAND':
            this.form.includeCmd.commodityIds = [];
            break;
        }
      } else {
        // 处理数据
        this.form.detailCmd = this.form.detailCmdVOS.map((item) => {
          return {
            ...item,
            taskGradeDetailCmds: item.taskGradeDetailCmds.filter((i) => i.grade)
          };
        });
      }

      if (izExcludeCommodity === '0') {
        this.form.excludeCmd.brandIds = [];
        this.form.excludeCmd.commodityIds = [];
      }

      // 复制时去除ID
      if (this.isCopy) {
        this.form.id = null;
      }
    },
    // 获取参数
    getParams() {
      this.beforeRuleOut();
      const { includeCmd, excludeCmd, name, timeData, customerThreshold } = this.form;
      const [startDate, endDate] = timeData;
      // 商品id集合
      const commodityIds = includeCmd.commodityIds.map(({ id }) => id);
      // 剔除商品id集合
      const excludeCommodityIds = excludeCmd.commodityIds.map(({ id }) => id);
      let params = pickBy(
        {
          ...this.form,
          name,
          startDate,
          endDate,
          subtype: this.subType,
          type: 'CREDIT_ISSUED', // CREDIT_ISSUED-积分发放
          includeCmd: {
            brandIds: includeCmd.brandIds,
            commodityIds
          },
          excludeCmd: {
            brandIds: excludeCmd.brandIds,
            commodityIds: excludeCommodityIds
          }
        },
        (val) => !!val
      );
      // 活动任务类型字段排除
      if (!customerThreshold.includes('NEW_CUSTOMER')) {
        delete params.newCustomerConfig;
      }
      if (!customerThreshold.includes('COMMON_CUSTOMER')) {
        delete params.commonCustomerConfig;
      }
      // 排除不需要的字段
      const omitKey = ['types', 'timeData', 'point', 'creditActivityDetailVOS', 'excludeVO', 'includeVO', 'detailCmdVOS'];
      if (this.subType === 'TASK') {
        omitKey.push('izExcludeCommodity', 'includeCmd', 'excludeCmd', 'customerThreshold', 'commodityType', 'newCustomerConfig', 'commonCustomerConfig');
      }
      params = omit(params, omitKey);
      return params;
    },
    // 封装验证函数
    submitForm(formName) {
      return new Promise((resolve, reject) => {
        this.$refs[formName].validate((valid, object) => {
          if (valid) {
            resolve();
          } else {
            reject(object);
          }
        });
      });
    },
    // 提交
    onSubmit() {
      this.loading = true;
      this.submitForm('form')
        .then(() => {
          // 判断活动权益
          const {
            detailCmdVOS,
            excludeCmd: { brandIds, commodityIds },
            izExcludeCommodity
          } = this.form;
          if (this.subType === 'TASK') {
            const invalidType = detailCmdVOS.find((item) => !item.type);
            if (invalidType) {
              this.loading = false;
              return this.$message.error('请选择任务类型');
            }
            const invalidPoint = detailCmdVOS.some((item) => item.subtype === 'ALL' && !item.point);
            if (invalidPoint) {
              this.loading = false;
              return this.$message.error('请输入数值');
            }
            // 校验会员等级或数值是否为空
            const invalidGrade = detailCmdVOS.some((item) => item.subtype !== 'ALL' && item.taskGradeDetailCmds.every((task) => !task.grade));
            const invalid = detailCmdVOS.some((item) => item.subtype !== 'ALL' && item.taskGradeDetailCmds.some((task) => task.grade && !task.point));
            if (invalidGrade || invalid) {
              this.loading = false;
              return this.$message.error('请选择会员等级或输入数值');
            }
          } else {
            // 判断剔除品牌和商品是否有选择
            if (izExcludeCommodity === '1' && !brandIds.length && !commodityIds.length) {
              this.loading = false;
              return this.$message.error('剔除品牌与剔除商品最少要选择一个');
            }
          }
          const params = this.getParams();
          const request = this.id && !this.isCopy ? creditActivityUpdate : creditActivityCreate;
          request(params)
            .then((res) => {
              if (res.code === '0') {
                this.$message.success(res.msg);
                this.$back({ path: '/activity/integral/list' });
              }
            })
            .catch((err) => {
              console.log(err);
            })
            .finally(() => {
              this.loading = false;
            });
        })
        .catch((object) => {
          console.log('error submit!!', object);
          this.loading = false;
        });
    }
  },
  created() {
    this.fetchBrand();
  },
  mounted() {
    this.initData();
  }
};
</script>

<style lang="scss" scoped>
.page-title {
  font-weight: bolder;
  font-size: 18px;
}
.form-tips {
  font-size: 12px;
  margin-top: 12px;
  padding: 12px;
  background-color: var(--background-color-base);
  color: var(--color-info);
  line-height: 150%;
  &--normal {
    margin-top: 0;
  }
}
.mod-header {
  display: flex;
  align-items: center;
  padding-bottom: 12px;
  margin-bottom: 20px;
  border-bottom: 1px solid #f6f6f8;
  &:before {
    content: '';
    width: 2px;
    background-color: var(--color-primary);
    height: 16px;
    margin-right: 8px;
  }
}
.el-input,
.el-date-editor--datetimerange {
  width: 400px;
  margin-right: 10px;
}
.custom-form-item {
  margin-top: 10px;
  width: 768px;
  ::v-deep {
    .el-input {
      width: 768px !important;
    }
  }
}

.radio-block {
  display: flex;
  align-items: center;
  &:not(:last-child) {
    margin-bottom: 10px;
  }
}

.form-span {
  font-size: 12px;
  margin-left: 10px;
}

.integral-tip {
  font-size: 12px;
  color: #9ea4b2;
}
.radio-block-flex {
  display: flex;
  align-items: baseline;
  margin-top: 10px;
}

.checkbox-list {
  max-width: 900px;
  display: flex;
  flex-wrap: wrap;

  > p {
    width: 220px;
    margin-bottom: 10px;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
  }
}

::v-deep {
  .el-input-number .el-input__inner {
    text-align: left;
  }
  .el-checkbox:last-of-type {
    margin-right: 10px;
  }
  .form-item-margin {
    margin-bottom: 0;
  }
}
.special-point-list {
  width: 400px;
  ::v-deep {
    th,
    td {
      padding: 8px 0 !important;
    }
  }
}
.input-tips {
  span {
    font-size: 12px;
    margin-left: 4px;
  }
}
</style>
