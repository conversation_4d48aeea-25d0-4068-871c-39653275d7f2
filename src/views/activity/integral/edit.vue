<template>
  <integral-info :id="id" :sub-type="subType" is-edit></integral-info>
</template>

<script>
import integralInfo from './components/integral-info.vue';
export default {
  name: 'integral-info-edit',
  components: {
    integralInfo
  },
  data() {
    return {
      id: this.$route.params.id,
      subType: this.$route.params.subType,
    }
  }
}
</script>

<style lang="scss" scoped></style>
