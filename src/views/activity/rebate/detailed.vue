<template>
  <div class="app-container">
    <div class="table-container">
      <div class="top">
        <p class="strong">{{ detail.shopName }}({{ detail.mobile }})</p>
        <p>
          活动名称：<span>{{ detail.title }}</span> 活动时间：<span
            >{{ detail.startDate | parseTime }}-{{
              detail.endDate | parseTime('{y}-{m}-{d} {h}:{i}:{s}')
            }}</span
          >
          活动品牌：<span>{{ detail.brandName }}</span>
        </p>
        <p class="strong">
          可返点采购总金额：<span>￥{{ detail.totalAmount }}</span>
          可返利金额：<span>￥{{ detail.totalVirtualCredit }}</span
          ><span class="statusName">{{ detail.auditStatusName }}</span>
        </p>
        <p>
          返点商品实收总金额：<span>￥{{ detail.totalPayAmount }}</span>
          已退款金额：<span>￥{{ detail.totalRefundAmount }}</span>
        </p>
        <p>
          采购总订单：<span>{{ detail.totalQuantity }}笔</span>
          未发货订单：<span>{{ detail.totalPaidQuantity }}笔</span>
          未收货订单：<span>{{ detail.totalDeliveredQuantity }}笔</span>
          退款中订单：<span>{{ detail.totalRefundingQuantity }}笔</span>
          未过售后期订单：<span
            >{{ detail.totalQuantity - detail.totalFinishedQuantity }}笔</span
          >
        </p>
      </div>
      <el-form :inline="true">
        <el-form-item label="订单号">
          <el-input v-model="sendData.data.orderNo"></el-input>
        </el-form-item>
        <el-form-item label="交易单号">
          <el-input v-model="sendData.data.tradeNo"></el-input>
        </el-form-item>
        <el-form-item label="订单状态">
          <el-select placeholder="请选择" v-model="sendData.data.orderStatus">
            <el-option
              :key="item.value"
              :label="item.label"
              :value="item.value"
              v-for="item in statusOptions"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button @click="onSubmit" type="primary">查询</el-button>
          <el-button @click="onReset" type="primary">重置</el-button>
          <Authority auth="/activity/export/rebate">
            <el-button :loading="isLoading" @click="onExport" type="primary"
              >导出</el-button
            >
          </Authority>
        </el-form-item>
      </el-form>
      <el-table :data="tableData" style="width: 100%">
        <el-table-column
          label="订单号"
          prop="orderNo"
          width="auto"
        ></el-table-column>
        <el-table-column
          label="交易单号"
          prop="tradeNo"
          width="auto"
        ></el-table-column>
        <el-table-column
          label="返点商品种类"
          prop="totalTypeQuantity"
          width="auto"
        ></el-table-column>
        <el-table-column
          label="返点商品购买数量"
          prop="totalQuantity"
          width="auto"
        ></el-table-column>
        <el-table-column
          label="订单状态"
          prop="orderStatusName"
          width="auto"
        ></el-table-column>
        <el-table-column label="下单时间" width="auto">
          <template slot-scope="scope">{{
            scope.row.orderCreateTime | parseDefaultTime
          }}</template></el-table-column
        >
        <el-table-column
          label="返点商品实收总额"
          prop="totalAmount"
          width="auto"
        >
          <template slot-scope="scope"
            >￥{{ scope.row.payAmount }}</template
          ></el-table-column
        >
        <el-table-column label="退款总额" prop="totalAmount" width="auto">
          <template slot-scope="scope"
            >￥{{ scope.row.refundAmount }}</template
          ></el-table-column
        >
        <el-table-column label="可返点采购金额" prop="totalAmount" width="auto">
          <template slot-scope="scope"
            >￥{{ scope.row.virtualCreditBackAmount }}</template
          ></el-table-column
        >
        <el-table-column label="操作" fixed="right" width="100">
          <template slot-scope="scope">
            <router-link
              :to="`/order/inquiry/detail/${scope.row.orderId}`"
              class="link"

              >订单详情</router-link
            >
            <el-button type="text" @click="getGoods(scope.row.id)"
              >查看商品</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          :current-page="sendData.pageNo"
          :page-size="sendData.pageSize"
          :page-sizes="[10, 20, 30, 40,50,100]"
          :total="total"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
          background
          layout="total, sizes, prev, pager, next, jumper"
        ></el-pagination>
      </div>
      <el-dialog
        title="查看商品"
        :visible.sync="dialogTableVisible"
        width="60%"
      >
        <el-table :data="goodsList">
          <el-table-column label="商品信息" width="auto">
            <template slot-scope="scope">
              <div>
                <img :src="scope.row.imgUrl" class="goods__img" />
                <p class="goods__name">{{ scope.row.commodityName }}</p>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="商品规格" width="auto">
            <template slot-scope="scope">
              <p>{{ scope.row.commoditySpecValue }}</p>
              <p>编码:{{ scope.row.commodityCode }}</p>
            </template></el-table-column
          >
          <el-table-column label="单价" width="auto">
            <template slot-scope="scope">
              ￥{{ scope.row.price }}
            </template></el-table-column
          >
          <el-table-column
            prop="quantity"
            label="购买数量"
            width="auto"
          ></el-table-column>
          <el-table-column label="实收" width="auto">
            <template slot-scope="scope">
              ￥{{ scope.row.payAmount }}
            </template></el-table-column
          >
          <el-table-column
            prop="refundStatusName"
            label="是否退款"
            width="auto"
          ></el-table-column>
        </el-table>
        <div slot="footer">
          <el-button type="primary" @click="dialogTableVisible = false"
            >确 定</el-button
          >
        </div>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import {
  getLogDetail,
  listPurchase,
  listCommodity,
  exportPurchase
} from '@/api/activity/rebate';
import { fetchOrderStatusOptions } from '@/api/order/inquiry/list';
import pickBy from 'lodash/pickBy';
import download from '@/utils/download';
import { parseTime } from '@/utils';
export default {
  name: 'rebateDetail',
  data() {
    return {
      statusOptions: [], // 订单状态列表
      sendData: {
        data: {
          backLogId: this.$route.params.id,
          orderNo: '',
          tradeNo: '',
          orderStatus: ''
        },
        pageNo: 1,
        pageSize: 10
      },
      tableData: [],
      detail: {},
      total: 0,
      isLoading: false,
      dialogTableVisible: false,
      goodsList: []
    };
  },
  activated() {
    this.fetchStatus();
    this.getLogDetail();
    this.getList();
  },
  methods: {
    // 订单状态列表
    fetchStatus() {
      fetchOrderStatusOptions().then((res) => {
        const data = res.data.map((item) => ({
          value: item.value,
          label: item.label
        }));
        this.statusOptions = data;
      });
    },
    getLogDetail() {
      getLogDetail(this.$route.params.id).then((res) => {
        this.detail = res.data;
      });
    },
    getList() {
      const listQuery = { ...this.sendData };
      listQuery.data = pickBy(listQuery.data, (val) => !!val);
      listPurchase(listQuery).then((res) => {
        this.tableData = res.data.list;
        this.total = res.data.total;
      });
    },
    onSubmit() {
      this.sendData.pageNo = 1;
      this.sendData.pageSize = 10;
      this.getList();
    },
    handleSizeChange(val) {
      this.sendData.pageSize = val;
      this.sendData.pageNo = 1;
      this.getList();
    },
    handleCurrentChange(val) {
      this.sendData.pageNo = val;
      this.getList();
    },
    onReset() {
      ['orderNo', 'tradeNo', 'orderStatus'].forEach((i) => {
        this.sendData.data[i] = '';
      });
      this.getList();
    },
    // 导出列表
    onExport() {
      this.isLoading = true;
      exportPurchase(this.sendData.data)
        .then((res) => {
          download(
            res,
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            `采购明细-${parseTime(Date.now(), '{y}-{m}-{d}-{h}:{i}:{s}')}.xlsx`
          );
        })
        .finally(() => {
          this.isLoading = false;
        });
    },
    // 查看商品
    getGoods(id) {
      listCommodity(id).then((res) => {
        this.dialogTableVisible = true;
        this.goodsList = res.data;
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.top {
  margin-bottom: 18px;
  span {
    margin-right: 50px;
  }
  .strong {
    font-size: 16px;
    font-weight: bold;
  }
  .statusName {
    font-weight: normal;
    color: #fff;
    background-color: #d7092f;
    line-height: 14px;
    padding: 6px;
  }
}
.link {
  color: var(--color-primary);
  cursor: pointer;
}
.goods {
  &__img {
    width: 60px;
    height: 60px;
    float: left;
    margin-right: 10px;
  }
  &__name {
    display: -webkit-box;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }
}
</style>
