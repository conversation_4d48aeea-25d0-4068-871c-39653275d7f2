<template>
  <div class="rebate-list">
    <div class="content">
      <el-form :inline="true">
        <el-form-item label="活动名称">
          <el-input v-model="sendData.data.title"></el-input>
        </el-form-item>
        <el-form-item label="品牌">
          <el-select multiple placeholder="请选择" v-model="sendData.data.brandIds">
            <el-option :key="item.id" :label="item.name" :value="item.id" v-for="item in searObj.options"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select placeholder="请选择" v-model="sendData.data.status">
            <el-option :key="item.id" :label="item.label" :value="item.value" v-for="item in searObj.statusOptions"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button @click="onSubmit" type="primary">查询</el-button>
          <el-button @click="clearForm">清空</el-button>
        </el-form-item>
      </el-form>
      <div style="margin-bottom: 20px">
        <router-link to="/activity/rebate/create">
          <Authority auth="/activity/edit/rebate">
            <el-button icon="el-icon-plus" size="small" type="primary">新增</el-button>
          </Authority>
        </router-link>
        <el-button @click="setSingle" icon="el-icon-plus" size="small" type="primary">设置单笔返点门槛</el-button>
      </div>
      <el-table :data="tableData" style="width: 100%">
        <el-table-column label="活动名称" prop="title" width="auto"></el-table-column>
        <el-table-column label="活动时间" prop="date" width="auto">
          <template slot-scope="scope">{{ scope.row.startDate | parseTime('{y}-{m}-{d} {h}:{i}:{s}') }}~{{ scope.row.endDate | parseTime('{y}-{m}-{d} {h}:{i}:{s}') }}</template>
        </el-table-column>
        <el-table-column label="参与品牌" prop="brandName" width="auto"></el-table-column>
        <el-table-column label="订单参与门槛（元）" prop="thresholdAmount" width="auto"></el-table-column>
        <el-table-column label="备注" prop="remarks" width="auto"></el-table-column>
        <el-table-column label="状态" prop="statusName" width="auto"></el-table-column>
        <el-table-column label="更新人&更新时间" width="auto">
          <template slot-scope="scope">{{ scope.row.updateByName }} {{ parseInt(scope.row.updateDate) | parseTime('{y}-{m}-{d} {h}:{i}:{s}') }}</template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" width="100">
          <template slot-scope="scope">
            <router-link :to="'/activity/rebate/edit/' + scope.row.id" v-if="scope.row.status === 'USE' || scope.row.status === 'FUTURE'">
              <Authority auth="/activity/edit/rebate">
                <el-button type="text">编辑</el-button>
              </Authority>
            </router-link>
            <router-link :to="'/activity/rebate/detail/' + scope.row.id">
              <Authority auth="/activity/view/rebate">
                <el-button type="text">查看</el-button>
              </Authority>
            </router-link>
            <router-link :to="`/activity/rebate/audit/${scope.row.id}`" v-if="scope.row.status === 'WAIT_AUDIT'">
              <el-button type="text">审核</el-button>
            </router-link>
            <router-link :to="`/activity/rebate/rebateDetail/${scope.row.id}`" v-if="scope.row.status === 'PASS'">
              <el-button type="text">返利详情</el-button>
            </router-link>
            <el-button @click="stopClick(scope.row.id)" type="text" v-if="scope.row.status === 'USE' || scope.row.status === 'FUTURE'">停用</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination :current-page="sendData.pageNo" :page-size="sendData.pageSize" :page-sizes="[10, 20, 30, 40, 50, 100]" :total="total" @current-change="handleCurrentChange" @size-change="handleSizeChange" background layout="total, sizes, prev, pager, next, jumper"></el-pagination>
      </div>
      <el-dialog :before-close="handleClose" :visible.sync="dialogFormVisible" center title="设置单笔订单返点门槛" :close-on-click-modal="false">
        <div class="dialog-div">
          <span>门槛金额</span>
          <el-input autocomplete="off" v-model.trim="typeValue"></el-input>
          <span>（元）</span>
        </div>
        <p class="dialog-text">（单笔订单金额大于等于{{ typeValue || '未设置' }}元才能参与返利计算）</p>
        <div class="dialog-footer" slot="footer">
          <el-button @click="handleClose">取 消</el-button>
          <el-button @click="singleSure" type="primary">确 定</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import { fetchStatusType, listBrand, list, disable, onceCreate, onceUpdate, onceGet } from '@/api/activity/rebate';
import { validateMoney2 } from '@/utils/validate';
export default {
  name: 'rebateList',
  data() {
    return {
      searObj: {
        options: [],
        statusOptions: []
      },
      sendData: {
        data: {
          title: '',
          brandIds: [],
          status: ''
        },
        pageNo: 1,
        pageSize: 10
      },
      tableData: [],
      total: 0,
      dialogFormVisible: false,
      typeValue: '',
      VIRTUAL_CREDIT_ID: ''
    };
  },
  activated() {
    this.init();
  },
  methods: {
    async init() {
      this.getList();
      // 获取品牌列表
      const { data: BrandRes } = await listBrand({});
      this.searObj.options = BrandRes;
      // 获取状态字典
      const { data: statusRes } = await fetchStatusType();
      this.searObj.statusOptions = statusRes;
      this.searObj.statusOptions.unshift({ id: '', label: '全部', value: '' });
    },
    async getList() {
      const { data: res } = await list(this.sendData);
      this.tableData = res.list;
      this.total = res.total;
    },
    onSubmit() {
      this.getList();
    },
    clearForm() {
      this.sendData.data.title = '';
      this.sendData.data.brandIds = [];
      this.sendData.data.status = '';
      this.getList();
    },
    handleSizeChange(val) {
      this.sendData.pageSize = val;
      this.sendData.pageNo = 1;
      this.getList();
    },
    handleCurrentChange(val) {
      this.sendData.pageNo = val;
      this.getList();
    },
    setSingle() {
      onceGet('VIRTUAL_CREDIT').then((res) => {
        if (res.data) {
          this.VIRTUAL_CREDIT_ID = res.data.id;
          this.typeValue = res.data.typeValue;
        } else {
          this.VIRTUAL_CREDIT_ID = '';
        }
      });
      this.dialogFormVisible = true;
    },
    handleClose() {
      this.typeValue = '';
      this.dialogFormVisible = false;
    },
    singleSure() {
      if (this.typeValue && validateMoney2(this.typeValue)) {
        if (this.VIRTUAL_CREDIT_ID) {
          onceUpdate({
            id: this.VIRTUAL_CREDIT_ID,
            type: 'VIRTUAL_CREDIT',
            typeValue: this.typeValue
          }).then((res) => {
            if (res.code === '0') {
              this.$message({
                message: '操作成功',
                type: 'success'
              });
              this.getList();
            }
          });
        } else {
          onceCreate({
            type: 'VIRTUAL_CREDIT',
            typeValue: this.typeValue
          }).then((res) => {
            if (res.code === '0') {
              this.$message({
                message: '操作成功',
                type: 'success'
              });
            }
          });
        }
        this.dialogFormVisible = false;
      } else {
        this.$message({
          message: '单笔订单返点门槛格式为保留2位小数的数字',
          type: 'warning'
        });
      }
    },
    stopClick(id) {
      this.$confirm('确认停用吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          disable(id).then((res) => {
            this.$message({
              type: 'success',
              message: '操作成功！'
            });
            this.getList();
          });
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
    }
  }
};
</script>
<style lang="scss" scoped>
.rebate-list {
  .content {
    background: #fff;
    padding: 20px;
    .dialog-div {
      display: flex;
      align-items: center;
      height: 60px;
      span {
        width: 100px;
        text-align: center;
      }
    }
    .dialog-text {
      text-align: center;
      margin-bottom: 0px;
    }
  }
}
</style>
