<template>
  <div class="app-container">
    <div class="infobar">
      <h3>基本信息</h3>
      <p>特供名称：{{ basic.name }}</p>
      <p>
        特供时间：{{
          basic.startDate | parseTime('{y}-{m}-{d} {h}:{i}:{s}')
        }}-{{ basic.endDate | parseTime('{y}-{m}-{d} {h}:{i}:{s}') }}
      </p>
      <p v-if="basic.status === 'FUTURE'">特供状态：未开始</p>
      <p v-if="basic.status === 'USE'">特供状态：进行中</p>
      <p v-if="basic.status === 'END'">特供状态：已结束</p>
      <p v-if="basic.status === 'STOPPED'">特供状态：已停用</p>
    </div>
    <div class="table-container">
      <div class="box" v-loading="totalLoading">
        <div class="row">
          <div class="item">
            <div class="item-title">成交总金额</div>
            <p class="amount">{{ total.orderAmount }}</p>
          </div>
          <div class="item">
            <div class="item-title">订单笔数</div>
            <p class="amount">{{ total.orderCount }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import {
  getById,
  getActivityStatistic
} from '@/api/activity/disable-special-buy/list';
import pickBy from 'lodash/pickBy';
import cloneDeep from 'lodash/cloneDeep';
// import Promotion from '@/components/Promotion';
export default {
  components: {
    //  promotion: Promotion
  },
  data() {
    const initFilter = {
      memberName: '',
      memberMobile: '',
      drawResult: '',
      id: this.$route.params.id
    };
    return {
      id: this.$route.params.id,
      initFilter,
      filter: cloneDeep(initFilter),
      total: {
        averageAmount: 0,
        commodityQuantity: 0,
        orderNum: 0,
        saleAmount: 0
      },
      totalLoading: false,
      basic: {
        cycleRule: {},
        startDate: '',
        endDate: '',
        label: '',
        name: '',
        status: ''
      },
      basicLoading: true
    };
  },
  computed: {
    // 过滤
    data() {
      return pickBy(this.filter, (val) => !!val);
    }
  },
  mounted() {
    this.getActivityStatistic();
    this.getById();
  },
  methods: {
    getById() {
      this.basicLoading = true;
      getById(this.id)
        .then((response) => {
          const { startDate, endDate, name, status } = response.data;
          this.basic = {
            startDate,
            endDate,
            name,
            status
          };
        })
        .finally(() => {
          this.basicLoading = false;
        });
    },
    getActivityStatistic() {
      this.totalLoading = true;
      getActivityStatistic(this.id)
        .then((response) => {
          const { orderAmount = '0.00', orderCount = 0 } = response.data || {};
          this.total = {
            orderAmount,
            orderCount
          };
        })
        .finally(() => {
          this.totalLoading = false;
        });
    }
  }
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
@import './effect-data';
</style>
