.infobar {
  position: relative;
  padding: 15px 20px;
  line-height: 20px;
  background-color: #fff;
  margin-bottom: 20px;
  .avatar {
    float: left;
    width: 45px;
    height: 45px;
    margin-right: 10px;
    box-sizing: border-box;
    border: 1px solid #e5e5e5;
    border-radius: 50%;
  }
  h3 {
    font-size: 14px;
    color: #111;
    font-weight: 700;
    padding-top: 3px;
    margin: 0;
  }
  p {
    font-size: 12px;
    color: #666;
    margin: 0;
    padding-top: 3px;
  }
  .create-btn {
    position: absolute;
    bottom: 20px;
    right: 25px;
  }
}
.box {
  padding: 20px;
  .row {
    position: relative;
    display: flex;
    .item {
      flex-basis: 33.33%;
      color: #333;
      font-size: 12px;
      text-align: center;
      .amount {
        cursor: pointer;
        display: block;
        font-size: 24px;
        margin: 6px 0 10px;
        color: var(--color-primary);
        text-decoration: none;
      }
      .amount-x {
        color: #f44;
      }
      .amount-yesterday {
        color: #999;
      }
    }
  }
}
