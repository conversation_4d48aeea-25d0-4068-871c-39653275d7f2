<template>
  <div class="app-container">
    <div class="table-container">
      <form @submit.prevent="onSearch" class="commo-search-container commo-search-flex commo-search-margin-bottom10">
        <div class="commo-search-item">
          <span class="commo-search-item-label">特供名称:</span>
          <div class="commo-search-item-content">
            <el-input size="small" clearable v-model.trim="filter.name"></el-input>
          </div>
        </div>
        <div class="commo-search-item">
          <span class="commo-search-item-label">状态:</span>
          <div class="commo-search-item-content">
            <el-select size="small" clearable v-model="filter.status">
              <el-option :key="idx" :label="item.label" :loading="statusOptionsLoading" :value="item.value" v-for="(item, idx) in statusOptions"></el-option>
            </el-select>
          </div>
        </div>
        <div class="commo-search-btns">
          <el-button native-type="submit" size="small" type="primary">查询</el-button>
          <el-button @click="onReset" size="small">重置</el-button>
        </div>
      </form>
      <router-link to="/activity/disable-special-buy/create">
        <Authority auth="/activity/create/disable-special-buy">
          <el-button class="add-btn" icon="el-icon-plus" size="small" type="primary">新增</el-button>
        </Authority>
      </router-link>
      <el-table :data="list" element-loading-text="加载中" fit highlight-current-row ref="multipleTable" v-loading="listLoading">
        <el-table-column align="center" label="特供名称" prop="name"></el-table-column>
        <el-table-column align="center" label="特供标签" prop="tag"></el-table-column>
        <el-table-column align="center" label="特供时间">
          <template :default-time="defaultDate" slot-scope="scope">
            开始{{ scope.row.startDate | parseTime('{y}-{m}-{d} {h}:{i}:{s}') }}
            <br />
            结束{{ scope.row.endDate | parseTime('{y}-{m}-{d} {h}:{i}:{s}') }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="状态" prop="statusName"></el-table-column>
        <el-table-column align="center" label="操作" fixed="right">
          <template slot-scope="scope">
            <router-link :to="'/activity/disable-special-buy/effect-data/' + scope.row.id" class="link">
              <el-button size="small" type="text" v-if="scope.row.status !== 'FUTURE'">效果数据</el-button>
            </router-link>
            <router-link :to="'/activity/disable-special-buy/detail/' + scope.row.id" class="link">
              <Authority auth="/activity/view/disable-special-buy">
                <el-button size="small" type="text">查看</el-button>
              </Authority>
            </router-link>
            <router-link :to="'/activity/disable-special-buy/edit/' + scope.row.id" class="link" v-if="scope.row.status === 'FUTURE' || scope.row.status === 'USE'">
              <Authority auth="/activity/edit/disable-special-buy">
                <el-button size="small" type="text">编辑</el-button>
              </Authority>
            </router-link>
            <el-button @click="disableById(scope.row.id)" size="small" type="text" v-if="scope.row.status === 'USE'">停用</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination :current-page="pageNo" :disabled="listLoading" :page-size="pageSize" :page-sizes="[10, 20, 30, 40, 50, 100]" :total="total" @current-change="handleCurrentChange" @size-change="handleSizeChange" background layout="total, sizes, prev, pager, next, jumper"></el-pagination>
      </div>
    </div>
  </div>
</template>
<script>
import { list, disableById, disableByIddelete, fetchOrderStatusOptions } from '@/api/activity/disable-special-buy/list';
import pickBy from 'lodash/pickBy';
import pick from 'lodash/pick';
import cloneDeep from 'lodash/cloneDeep';
export default {
  components: {},
  data() {
    const initFilter = {
      name: '',
      status: 'ALL'
    };
    return {
      initFilter,
      filter: cloneDeep(initFilter),
      list: [],
      listLoading: true,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      defaultDate: ['00:00:00', '23:59:59'],
      activityIdPayAmounts: [], // 实付订单金额集合
      statusOptions: [], // 订单状态
      statusOptionsLoading: false
    };
  },
  computed: {
    // 过滤
    data() {
      return pickBy(this.filter, (val) => !!val);
    }
  },
  mounted() {
    this.fetchData();
    this.fetchStatus();
  },
  methods: {
    handleSizeChange(val) {
      const self = this;
      self.pageSize = val;
      self.pageNo = 1;
      self.fetchData();
    },
    handleCurrentChange(val) {
      const self = this;
      self.pageNo = val;
      self.fetchData();
    },
    // 停用
    disableById(id) {
      this.$confirm('停用后您将无法重启该活动，确认是否停用?', '提示', {
        confirmButtonText: '停用',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          disableById(id)
            .then((response) => {
              this.fetchData();
            })
            .finally(() => {
              this.listLoading = false;
            });
          this.$message({
            type: 'success',
            message: '停用成功！'
          });
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消停用'
          });
        });
    },
    // 删除
    disableByIddelete(id) {
      this.$confirm('确认删除该活动?', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          disableByIddelete(id)
            .then((response) => {
              this.fetchData();
            })
            .finally(() => {
              this.listLoading = false;
            });
          this.$message({
            type: 'success',
            message: '删除成功！'
          });
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });
        });
    },
    fetchData() {
      this.listLoading = true;
      const listQuery = pick(this, ['pageNo', 'pageSize', 'data']);
      list(listQuery)
        .then((response) => {
          const { list = [], total = 0 } = response.data || {};
          this.list = list;
          this.total = total;
        })
        .finally(() => {
          this.listLoading = false;
        });
    },
    onSearch() {
      this.pageNo = 1;
      this.pageSize = 10;
      this.$nextTick(() => {
        this.fetchData();
      });
    },
    onReset() {
      this.pageNo = 1;
      this.pageSize = 10;
      this.filter = cloneDeep(this.initFilter);
      this.$nextTick(() => {
        this.fetchData();
      });
    },
    fetchStatus() {
      // 订单状态列表
      this.statusOptionsLoading = true;
      fetchOrderStatusOptions()
        .then((rs) => {
          const res = rs.data.map((item) => ({
            value: item.value,
            label: item.label
          }));
          this.statusOptions = res;
        })
        .finally(() => {
          this.statusOptionsLoading = false;
        });
    }
  }
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
@import './styles';
.app-container {
  padding: 0;
}
.employee {
  margin: 0 3px;
  padding: 2px;
  border-radius: 3px;
  border: 1px solid #aaaaaa;
}
</style>
