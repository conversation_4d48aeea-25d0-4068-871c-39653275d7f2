<template>
  <el-form :model="ruleForm" :rules="rules" class="form-container" label-width="130px" ref="ruleForm">
    <div class v-loading="loading">
      <div class="part">
        <div class="title">基本信息</div>
        <div class="content">
          <el-form-item label="特供名称" prop="name">
            <el-input :disabled="isDisabled" v-model.trim="ruleForm.name"></el-input>
          </el-form-item>
          <el-form-item label="特供标签" prop="tag">
            <el-input :disabled="isDisabled" v-model.trim="ruleForm.tag"></el-input>
          </el-form-item>
          <p class="tips">活动标签不超过10个汉字</p>
          <el-form-item label="特供时间" prop="activityDate">
            <el-date-picker :default-time="defaultDate" :disabled="isDetail" end-placeholder="结束日期" range-separator="至" start-placeholder="开始日期" type="datetimerange" unlink-panels v-model="ruleForm.activityDate"></el-date-picker>
          </el-form-item>
        </div>
        <div class="title">特供折扣设置（活动库存与商品库存同步）</div>
        <div class="content">
          <el-form-item>
            <discount-goods-input :disabled="isDisabled" ref="discountGoods" />
          </el-form-item>
          <el-form-item v-if="!isDetail">
            <el-button :loading="saveLoading" @click="submitForm" type="primary">保存</el-button>
          </el-form-item>
        </div>
      </div>
    </div>
  </el-form>
</template>

<script>
import {
  create,
  update,
  getById
  // getLabelList,
  // getUsedLabel
} from '@/api/activity/disable-special-buy/list';
import { parseDefaultTime } from '@/utils';
import DiscountGoodsInput from '@/components/DiscountGoodsInput';
export default {
  components: { DiscountGoodsInput },
  name: 'my-form',
  props: {
    id: {
      type: String,
      default: ''
    },
    isEdit: {
      type: Boolean,
      default: false
    },
    isDetail: {
      type: Boolean,
      default: false
    }
  },
  created() {
    this.initData();
    // this.getLabelList();
    // this.getUsedLabel();
  },
  data() {
    return {
      // defaultLabel: false, // 默认标签是否可以勾选
      // labelList: [], // 所有的标签列表
      // usedlabel: [], // 已经使用过的标签
      saveLoading: false,
      loading: false,
      defaultDate: ['00:00:00', '23:59:59'],
      startDate: '',
      ruleForm: {
        status: '', // 活动状态
        name: '', // 活动名称
        tag: '', // 活动标签
        activityDate: [], // 活动时间
        startDate: '', // 活动开始时间
        endDate: '' // 活动结束时间
        // isCountDownDisplay: false, // 是否显示倒计时:1-显示,0-不显示 ,
        // employeeLabel: [] // 选中的适用员工
      },
      rules: {
        name: [
          { required: true, message: '必填信息', trigger: 'blur' },
          { max: 15, message: '输入不能超过15个字', trigger: 'blur' }
        ],
        tag: [
          { required: true, message: '必填信息', trigger: 'blur' },
          { max: 10, message: '输入不能超过10个字', trigger: 'blur' }
        ],
        activityDate: [{ required: true, message: '必填信息', trigger: 'blur' }]
      }
    };
  },
  watch: {},
  computed: {
    // 是否可以编辑
    isDisabled() {
      return this.isDetail;
    },
    data() {
      const obj = {};
      obj.name = this.ruleForm.name;
      obj.tag = this.ruleForm.tag;
      // obj.isCountDownDisplay = this.ruleForm.isCountDownDisplay ? '1' : '0';
      const [startDate, endDate] = this.ruleForm.activityDate;
      this.startDate = parseFloat(startDate).toString() === 'NaN' ? startDate.getTime() : startDate;
      obj.startDate = parseDefaultTime(startDate);
      obj.endDate = parseDefaultTime(endDate);
      // obj.employeeLabel = this.ruleForm.employeeLabel;
      return obj;
    }
  },
  methods: {
    initData() {
      if (this.id) {
        this.fetchThreshold();
      }
    },
    fetchThreshold() {
      this.loading = true;
      getById(this.id)
        .then((response) => {
          const {
            name,
            tag,
            status,
            startDate,
            endDate,
            // isCountDownDisplay,
            specialBuySkus,
            commodities
            // employeeLabel
          } = response.data;
          const activityDate = [startDate, endDate];
          this.ruleForm = {
            name,
            activityDate,
            tag,
            status
            // isCountDownDisplay: isCountDownDisplay === '1',
            // employeeLabel
          };
          this.$refs.discountGoods.setData(commodities, specialBuySkus);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    submitForm() {
      const p1 = this.$refs.ruleForm.validate();
      const p2 = this.$refs.discountGoods.validate();
      Promise.all([p1, p2]).then(([res1, res2]) => {
        if (res2.length === 0) {
          this.$message.error('请选择商品~');
          return;
        }
        const data = { ...this.data };
        let specialBuySkus = [];
        res2.forEach((item) => {
          specialBuySkus = specialBuySkus.concat(item.specialBuySkus);
        });
        data.specialBuySkus = specialBuySkus;
        const time = new Date().getTime();
        if (this.startDate < time) {
          this.$confirm('活动开始时间，早于当前时间，请确认是否提交?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.request(data);
          });
        } else {
          this.request(data);
        }
      });
    },
    request(data) {
      if (this.isEdit) {
        data.id = this.id;
      }
      const request = this.isEdit ? update : create;
      this.saveLoading = true;
      request(data)
        .then((response) => {
          this.$message.success('保存成功');
          this.$back('/activity/disable-special-buy/list');
        })
        .finally(() => {
          this.saveLoading = false;
        });
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
$text: rgba(0, 0, 0, 0.65);
.el-form-item {
  margin: 0 0 24px;
}
.el-select,
.el-input,
.el-input__inner,
.el-textarea {
  width: 400px;
}
.form-container {
  padding-bottom: 60px;
  position: relative;
  height: 100%;
  overflow: auto;
}
.type-radio .el-radio {
  margin: 3px 6px 3px 0 !important;
}
.fix-part {
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.3);
  background-color: #fff;
  position: fixed;
  left: 0;
  z-index: 2;
  width: 100%;
  bottom: 0;
  text-align: right;
  .el-form-item {
    padding: 10px 0;
    margin: 0 20px;
  }
}
.part {
  background-color: #fff;
  box-shadow: 0 0 2px rgba(0, 0, 0, 0.2);
  & > .title {
    font-weight: bolder;
    border-bottom: 1px solid #e8e8e8;
    padding: 18px 30px;
    font-size: 16px;
    user-select: none;
  }
  & > .content {
    padding: 18px 30px;
  }
  & ~ .part {
    margin-top: 20px;
  }
}
.add-commodity {
  padding-left: 24px;
}
.el-radio {
  margin-top: 10px;
}
.my-input-class {
  width: 180px;
}
.my-span {
  width: 50px;
  line-height: 36px;
  font-size: 14px;
  color: #000;
}
.my-discountContent {
  margin-top: 0;
}
.validate-tips {
  color: var(--color-danger);
  font-size: 12px;
  line-height: 1;
  padding-top: 4px;
}
.my-checked {
  float: left;
  line-height: 35px;
}
.tips {
  color: #666;
  font-size: 12px;
  margin: -4px 0 15px 130px;
}
.reds {
  color: red;
}
.my-discountContent .el-form-item__error {
  padding-left: 22px;
}
.el-form-item__content {
  margin-left: 140px;
}
.get-tips {
  width: 400px;
  border: 1px dashed red;
  position: absolute;
  top: 0;
  left: 425px;
}
</style>
