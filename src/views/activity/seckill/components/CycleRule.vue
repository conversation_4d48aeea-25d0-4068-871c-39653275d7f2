<!--限时抢购-按周期重复-->
<template>
  <el-radio-group
    v-model="cycleType"
    :disabled="!cycleRule || isDetail || statusEdit"
  >
    <el-radio label="EVERY_DAY">每天</el-radio>
    <br />
    <div class="cycle-type-div" v-if="cycleType === 'EVERY_DAY'">
      <div v-for="(item, index) in timeListPicker" :key="item.id">
        <el-time-picker
          is-range
          arrow-control
          v-model="item.time"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          placeholder="选择时间范围"
          :disabled="isDetail || statusEdit"
        ></el-time-picker>
        <el-button
          class="delete"
          v-if="index !== 0 && !isDetail && !statusEdit"
          type="text"
          @click="deleteTime(index)"
          >删除</el-button
        >
        <br />
      </div>
      <br />
      <el-button
        class="add"
        size="mini"
        type="primary"
        @click="addTime()"
        v-if="!isDetail && !statusEdit"
        >添加时间段</el-button
      >
    </div>
    <el-radio label="EVERY_WEEK">每周</el-radio>
    <br />
    <div class="cycle-type-div" v-if="cycleType === 'EVERY_WEEK'">
      <el-select
        v-model="daysList"
        multiple
        placeholder="请选择"
        :disabled="isDetail || statusEdit"
      >
        <el-option
          v-for="item in daysListOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        ></el-option>
      </el-select>
      <div v-for="(item, index) in timeListPicker" :key="item.id">
        <el-time-picker
          is-range
          arrow-control
          v-model="item.time"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          placeholder="选择时间范围"
          :disabled="isDetail || statusEdit"
        ></el-time-picker>
        <el-button
          class="delete"
          v-if="index !== 0 && !isDetail && !statusEdit"
          type="text"
          @click="deleteTime(index)"
          >删除</el-button
        >
        <br />
      </div>
      <br />
      <el-button
        class="add"
        size="mini"
        type="primary"
        @click="addTime()"
        v-if="!isDetail && !statusEdit"
        >添加时间段</el-button
      >
    </div>
    <el-radio label="EVERY_MONTH">每月</el-radio>
    <div class="cycle-type-div" v-if="cycleType === 'EVERY_MONTH'">
      <el-select
        v-model="daysList"
        multiple
        placeholder="请选择"
        :disabled="isDetail || statusEdit"
      >
        <el-option
          v-for="item in daysListOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        ></el-option>
      </el-select>
      <div v-for="(item, index) in timeListPicker" :key="item.id">
        <el-time-picker
          is-range
          arrow-control
          v-model="item.time"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          placeholder="选择时间范围"
          :disabled="isDetail || statusEdit"
        ></el-time-picker>
        <el-button
          class="delete"
          v-if="index !== 0 && !isDetail && !statusEdit"
          type="text"
          @click="deleteTime(index)"
          >删除</el-button
        >
        <br />
      </div>
      <el-button
        class="add"
        size="mini"
        type="primary"
        @click="addTime()"
        v-if="!isDetail && !statusEdit"
        >添加时间段</el-button
      >
    </div>
  </el-radio-group>
</template>

<script>
import uniqueId from 'lodash/uniqueId';
import { parseDefaultTime } from '@/utils';
export default {
  name: 'CycleRule',
  model: {},
  data() {
    return {
      cycleType: '', // 周期重复类型
      timeListPicker: [],
      daysList: [], // 所选择的日期
      isFirst: 0, // 判断是否是第一次加载，如果是则为0
      daysListOptions: [] // 日期下拉框数据
    };
  },
  computed: {
    // 传给父组件数据
    cycleRuleData() {
      let cycleRule = {};
      // 周期重复规则，null表示不按周期 ,
      if (!this.cycleRule) {
        cycleRule = null;
      } else {
        const timeList = [];
        cycleRule.cycleType = this.cycleType;
        cycleRule.daysList = this.daysList;
        this.timeListPicker.forEach((item) => {
          const [first, second] = item.time;
          timeList.push({
            first: parseDefaultTime(first).split(' ')[1],
            second: parseDefaultTime(second).split(' ')[1]
          });
        });
        cycleRule.timeList = timeList;
      }
      return cycleRule;
    }
  },
  watch: {
    cycleRuleData(value) {
      this.$emit('sendCycleRule', value);
    },
    // 是否周期重复
    cycleRule(value) {
      if (!value) {
        this.cycleType = '';
        this.daysList = [];
        this.timeListPicker = [
          {
            id: uniqueId('cycleRule_'),
            time: [
              new Date(2019, 6, 19, 0, 0, 0),
              new Date(2019, 6, 19, 23, 59, 59)
            ]
          }
        ];
      } else {
        if (!this.id) {
          this.cycleType = 'EVERY_DAY';
        }
      }
    },
    // 周期重复类型改变
    cycleType(value) {
      if (!this.id) {
        this.daysList = [];
        this.timeListPicker = [
          {
            id: uniqueId('cycleRule_'),
            time: [
              new Date(2019, 6, 19, 0, 0, 0),
              new Date(2019, 6, 19, 23, 59, 59)
            ]
          }
        ];
      }
      if (this.id && this.isFirst === 1) {
        this.daysList = [];
        this.timeListPicker = [
          {
            id: uniqueId('cycleRule_'),
            time: [
              new Date(2019, 6, 19, 0, 0, 0),
              new Date(2019, 6, 19, 23, 59, 59)
            ]
          }
        ];
      }
      if (this.id && this.isFirst === 0) {
        this.isFirst = 1;
      }
      let daysListOptions = [];
      if (this.cycleType === 'EVERY_WEEK') {
        daysListOptions = [
          { value: 2, label: '周一' },
          { value: 3, label: '周二' },
          { value: 4, label: '周三' },
          { value: 5, label: '周四' },
          { value: 6, label: '周五' },
          { value: 7, label: '周六' },
          { value: 1, label: '周日' }
        ];
      }
      if (this.cycleType === 'EVERY_MONTH') {
        let i = 1;
        while (i < 31) {
          daysListOptions.push({
            value: i,
            label: i + '日'
          });
          i++;
        }
      }
      this.daysListOptions = daysListOptions;
    },
    cycleRuleGet() {
      this.getData();
    }
  },
  props: {
    id: String,
    // 是否是详情
    isDetail: {
      type: Boolean,
      default: false
    },
    // 是否可编辑
    statusEdit: {
      type: Boolean,
      default: false
    },
    // 是否周期重复
    cycleRule: {
      default: false
    },
    // 接收父组件传过来的规则
    cycleRuleGet: {
      type: Object,
      default: function () {
        return {};
      }
    }
  },
  methods: {
    // 获取cycleType，daysList，timeList
    getData() {
      const { cycleType, daysList = [], timeList = [] } =
        this.cycleRuleGet || {};
      const timeListPicker = [];
      timeList.forEach((item, index) => {
        const { first, second } = item;
        timeListPicker.push({
          id: uniqueId('cycleRule_'),
          time: [
            new Date(
              2019,
              6,
              19,
              first.split(':')[0],
              first.split(':')[1],
              first.split(':')[2]
            ),
            new Date(
              2019,
              6,
              19,
              second.split(':')[0],
              second.split(':')[1],
              second.split(':')[2]
            )
          ]
        });
      });
      this.cycleType = cycleType;
      this.timeListPicker = timeListPicker;
      this.daysList = daysList;
    },
    // 删除时间段
    deleteTime(index) {
      this.timeListPicker.splice(index, 1);
    },
    // 添加时间段
    addTime() {
      this.timeListPicker.push({
        id: uniqueId('cycleRule_'),
        time: [
          new Date(2019, 6, 19, 0, 0, 0),
          new Date(2019, 6, 19, 23, 59, 59)
        ]
      });
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.cycle-type-div {
  margin-left: 24px;
  .el-date-editor {
    margin-top: 12px;
  }
  .delete {
    margin-left: 12px;
  }
  .add {
    margin-top: 12px;
    margin-bottom: 12px;
  }
}
::v-deep.el-date-editor .el-range-separator {
  width: 6%;
}
</style>
