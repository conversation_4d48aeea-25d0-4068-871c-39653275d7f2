<template>
  <el-form :model="ruleForm" :rules="rules" class="form-container" label-width="130px" ref="ruleForm">
    <div class v-loading="loading">
      <div class="part">
        <div class="title">基本信息</div>
        <div class="content">
          <el-form-item label="活动名称" prop="name">
            <el-input :disabled="isDetail" v-model.trim="ruleForm.name"></el-input>
          </el-form-item>
          <el-form-item label="活动标签" prop="label">
            <el-input :disabled="isDetail" v-model.trim="ruleForm.label"></el-input>
          </el-form-item>
          <el-form-item label="活动时间" prop="activityDate">
            <el-date-picker :picker-options="mixPickerOptions" :default-time="defaultDate" :disabled="isDetail" end-placeholder="结束日期" range-separator="至" start-placeholder="开始日期" type="datetimerange" v-model="ruleForm.activityDate"></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-checkbox :disabled="isDetail || statusEdit" v-model="ruleForm.cycleRule">按周期重复</el-checkbox>
            <span class="tips no-margin-tips"> （在活动时间内按周期重复。每次开启抢购时 限购数量 及 活动库存 均会刷新。） </span>
            <el-form-item>
              <cycleRule :cycleRule="ruleForm.cycleRule" :cycleRuleGet="cycleRuleGet" :id="id" :isDetail="isDetail" :statusEdit="statusEdit" @sendCycleRule="sendCycleRule"></cycleRule>
            </el-form-item>
          </el-form-item>
          <el-form-item label="预热天数" prop="preheatDay">
            <el-input :disabled="isDetail || statusEdit" v-model.trim="ruleForm.preheatDay"></el-input>
            <span class="tips no-margin-tips">（活动开始前几天，商品详情页面开始展示对活动的预告。）</span>
          </el-form-item>
        </div>
        <div class="title">抢购设置</div>
        <div class="content">
          <table border="0" class="stock-table" rules="none">
            <thead>
              <tr>
                <th class="th-left" colspan="2">
                  <div v-if="!isDetail && !statusEdit">
                    <el-popover @hide="handHide('priceDiscount')" placement="top" style="text-align: center" v-model="visible" width="200">
                      <div class="center-position">
                        <span>打</span>
                        <input class="priceDiscount" type="number" v-model="priceDiscount" />
                        <span>折</span>
                        <validate :inputValue="priceDiscount" :seckillCommodity="seckillCommodity" :type="'discount'" v-on:validate="inputValidate"></validate>
                      </div>
                      <div style="text-align: right; margin: 0">
                        <el-button @click="visible = false" size="mini" type="text">取消</el-button>
                        <el-button @click="handleAllpriceDiscount" size="mini" type="text">确定</el-button>
                      </div>
                      <el-button class="mar" size="mini" slot="reference" type="primary">全部打折</el-button>
                    </el-popover>
                    <el-popover @hide="handHide('priceReduce')" placement="top" style="text-align: center" v-model="visible2" width="200">
                      <div class="center-position">
                        <span>减</span>
                        <input class="priceDiscount" type="number" v-model="priceReduce" />
                        <span>元</span>
                        <validate :inputValue="priceReduce" :seckillCommodity="seckillCommodity" :type="'money'" v-on:validate="inputValidate"></validate>
                      </div>
                      <div style="text-align: right; margin: 0">
                        <el-button @click="visible2 = false" size="mini" type="text">取消</el-button>
                        <el-button @click="handleAllpriceReduce" size="mini" type="text">确定</el-button>
                      </div>
                      <el-button class="mar" size="mini" slot="reference" type="primary">全部减价</el-button>
                    </el-popover>
                    <el-popover @hide="handHide('quantityLimit')" placement="top" style="text-align: center" v-model="visible3" width="200">
                      <div class="center-position">
                        <span>每人</span>
                        <input class="priceDiscount" type="number" v-model="quantityLimit" />
                        <span>个</span>
                        <validate :inputValue="quantityLimit" :seckillCommodity="seckillCommodity" :type="'integer'" v-on:validate="inputValidate"></validate>
                      </div>
                      <div style="text-align: right; margin: 0">
                        <el-button @click="visible3 = false" size="mini" type="text">取消</el-button>
                        <el-button @click="handleQuantityLimit" size="mini" type="text">确定</el-button>
                      </div>
                      <el-button class="mar" size="mini" slot="reference" type="primary">全部限购</el-button>
                    </el-popover>
                  </div>
                </th>
                <th class="th-right" colspan="3">
                  <el-radio-group v-model="scale">
                    <el-radio :disabled="isDetail || statusEdit" :label="2">不抹去</el-radio>
                    <el-radio :disabled="isDetail || statusEdit" :label="0">抹去角分</el-radio>
                    <el-radio :disabled="isDetail || statusEdit" :label="1">抹去分</el-radio>
                  </el-radio-group>
                </th>
              </tr>
              <tr>
                <th>商品信息</th>
                <th>优惠设置</th>
                <th>活动库存</th>
                <th>限购数量</th>
                <th>操作</th>
              </tr>
            </thead>
            <tbody class="tbody-flow">
              <tr :key="item.id" v-for="(item, index) in seckillCommodity">
                <td>
                  <div class="seckillCommodity-info">
                    <img :src="item.thumbnailUrl" alt />
                    <div>
                      <p>{{ item.name }}</p>
                      <!-- <p v-if="id">￥{{ item.oldPrice }}</p> -->
                      <p>￥{{ item.price }}</p>
                      <p>库存：{{ item.stock }}</p>
                    </div>
                  </div>
                </td>
                <td>
                  <div class="seckill-td">
                    <el-radio
                      :disabled="isDetail || statusEdit"
                      @change="
                        item.priceReduce = '';
                        item.priceReduce2 = '';
                      "
                      label="1"
                      v-model="item.radioPreferential"
                      >打折</el-radio
                    >
                    <div class="seckill-td-div">
                      <div>
                        <span>打</span>
                        <input :disabled="item.radioPreferential !== '1' || isDetail || statusEdit" @change="discount(item, index)" type="number" v-model.number="item.priceDiscount" />
                        <span>折</span>
                        <validate :inputValue="item.priceDiscount" :type="'discount'" class="validate-style" ref="validate" v-if="item.radioPreferential === '1'" v-on:validate="inputValidate"></validate>
                      </div>
                      <div>
                        <span>折后</span>
                        <input :disabled="item.radioPreferential !== '1' || isDetail || statusEdit" @change="discountPrise(item, index)" type="number" v-model.number="item.priceDiscount2" />
                        <span>元</span>
                        <validate :inputValue="item.priceDiscount2" :type="'money'" class="validate-style validate-style2" ref="validate" v-if="item.radioPreferential === '1'" v-on:validate="inputValidate"></validate>
                      </div>
                      <!-- 其他规格折扣 -->
                      <div class="seckill-td-div" v-if="item.skuVOList.length > 1 && item.radioPreferential === '1'">
                        <el-popover placement="right" trigger="click" width="400">
                          <el-table :data="item.skuVOList" max-height="400">
                            <el-table-column label="规格" width="150">
                              <template slot-scope="slot">
                                <p>{{ slot.row.firstLevel }}{{ slot.row.secondLevel }}{{ slot.row.thirdLevel }}</p>
                              </template>
                            </el-table-column>
                            <el-table-column label="原价" property="price"></el-table-column>
                            <el-table-column :label="item.priceDiscount ? '打' + item.priceDiscount + '折' : '未设置折扣'" property="address">
                              <template slot-scope="slot">
                                <p v-if="!item.priceDiscount">
                                  {{ slot.row.price }}
                                </p>
                                <p v-if="item.priceDiscount">{{ floor((slot.row.price * item.priceDiscount) / 10, 2) }}元</p>
                              </template>
                            </el-table-column>
                          </el-table>
                          <el-button slot="reference" type="text">{{ item.skuVOList.length }}个规格有折扣</el-button>
                        </el-popover>
                      </div>
                    </div>
                  </div>
                  <div class="seckill-td seckill-td-next">
                    <el-radio
                      :disabled="isDetail || statusEdit"
                      @change="
                        item.priceDiscount = '';
                        item.priceDiscount2 = '';
                      "
                      label="2"
                      v-model="item.radioPreferential"
                      >减价</el-radio
                    >
                    <div class="seckill-td-div">
                      <div>
                        <span>减</span>
                        <input :disabled="item.radioPreferential !== '2' || isDetail || statusEdit" @change="reduce(item, index)" type="number" v-model="item.priceReduce" />
                        <span>元</span>
                        <validate :inputValue="item.priceReduce" :type="'money'" class="validate-style" ref="validate" v-if="item.radioPreferential === '2'" v-on:validate="inputValidate"></validate>
                      </div>
                      <div>
                        <span>减后</span>
                        <input :disabled="item.radioPreferential !== '2' || isDetail || statusEdit" @change="reducePrise(item, index)" type="number" v-model="item.priceReduce2" />
                        <span>元</span>
                        <validate :inputValue="item.priceReduce2" :type="'money'" class="validate-style validate-style2" ref="validate" v-if="item.radioPreferential === '2'" v-on:validate="inputValidate"></validate>
                      </div>

                      <!-- 其他规格减价 -->
                      <div class="seckill-td-div" v-if="item.skuVOList.length > 1 && item.radioPreferential === '2'">
                        <el-popover placement="right" trigger="click" width="400">
                          <el-table :data="item.skuVOList" max-height="400">
                            <el-table-column label="规格" width="150">
                              <template slot-scope="slot">
                                <p>{{ slot.row.firstLevel }}{{ slot.row.secondLevel }}{{ slot.row.thirdLevel }}</p>
                              </template>
                            </el-table-column>
                            <el-table-column label="原价" property="price"></el-table-column>
                            <el-table-column :label="item.priceReduce ? '减' + item.priceReduce + '元' : '减0元'" property="address">
                              <template slot-scope="slot">
                                <p v-if="item.priceReduce">{{ floor(slot.row.price - item.priceReduce, 2) }}元</p>
                                <p v-if="!item.priceReduce">{{ floor(slot.row.price, 2) }}元</p>
                              </template>
                            </el-table-column>
                          </el-table>
                          <el-button slot="reference" type="text">{{ item.skuVOList.length }}个规格有减价</el-button>
                        </el-popover>
                      </div>
                    </div>
                  </div>
                </td>
                <td>
                  <div class="seckill-td">
                    <el-radio :disabled="isDetail || statusEdit" @change="item.setStock = ''" label="1" v-model="item.radioStock">库存与商品同步</el-radio>
                  </div>
                  <div class="seckill-td seckill-td-next">
                    <el-radio :disabled="isDetail || statusEdit" label="2" v-model="item.radioStock">设置库存</el-radio>
                    <div>
                      <input :disabled="item.radioStock !== '2' || isDetail || statusEdit" type="number" v-model="item.setStock" />
                      <span>个</span>
                      <validate :inputValue="item.setStock" :type="'integer'" class="validate-style validate-style4" ref="validate" v-if="item.radioStock === '2'" v-on:validate="inputValidate"></validate>
                    </div>
                    <AddQuantity :onSubmit="appendStock" :querryData="item" :title="'追加库存'" v-if="isEdit && item.radioStock === '2' && ruleForm.status === 'USE'"></AddQuantity>
                  </div>
                </td>
                <td>
                  <div class="seckill-td">
                    <el-radio :disabled="isDetail || statusEdit" @change="item.quantityLimit = ''" label="1" v-model="item.radioLimit">不限购</el-radio>
                  </div>
                  <div class="seckill-td seckill-td-next">
                    <el-radio :disabled="isDetail || statusEdit" label="2" v-model="item.radioLimit">每人</el-radio>
                    <div>
                      <input :disabled="item.radioLimit !== '2' || isDetail || statusEdit" type="number" v-model="item.quantityLimit" />
                      <span>个</span>
                      <validate :inputValue="item.quantityLimit" :type="'integer'" class="validate-style validate-style4" ref="validate" v-if="item.radioLimit === '2'" v-on:validate="inputValidate"></validate>
                    </div>
                    <AddQuantity :onSubmit="appendLimitQuantity" :querryData="item" :title="'追加限购'" v-if="isEdit && item.radioLimit === '2' && ruleForm.status === 'USE'"></AddQuantity>
                  </div>
                </td>
                <td>
                  <el-button @click="handleDelete(index)" type="text" v-if="!isDetail && !isDetail && !statusEdit">删除</el-button>
                </td>
              </tr>
            </tbody>
            <tfoot v-if="!isDetail && !isDetail && !statusEdit">
              <tr>
                <td colspan="5">
                  <add-commodity :commodityList="commodityList" :isShowCommodity="false" :operation="isDetail ? 'detail' : 'edit'" @updateTable="updateCurrentTable" labelTitle="选择抢购商品"></add-commodity>
                  <!-- <div v-if="isDetail === false">
                  <add-commodity
                    :commodityList="commodityList"
                    @updateTable="updateCurrentTable"
                    :operation="'edit'"
                    labelTitle="选择抢购商品"
                    :isShowCommodity="false"
                  ></add-commodity>
                </div>
                <div v-if="isDetail === true">
                  <add-commodity
                    :operation="'detail'"
                    :commodityList="commodityList"
                    @updateTable="updateCurrentTable"
                    labelTitle="选择抢购商品"
                    :isShowCommodity="false"
                  ></add-commodity>
                  </div>-->
                  <div class="validate-tips" v-if="showValidate">请选择商品</div>
                </td>
              </tr>
            </tfoot>
          </table>
        </div>
        <el-form-item label="优惠叠加">
          <el-checkbox :disabled="isDetail || statusEdit" v-model="isOverlay">可叠加 优惠券、满减折送、打包一口价</el-checkbox>
          <span class="tips no-margin-tips red">同时叠加多种优惠，可能造成商品实际成交价格过低，从而造成损失，请谨慎设置。</span>
        </el-form-item>

        <div class="btn-wrap" v-if="!isDetail">
          <el-button @click="back()">取消</el-button>
          <el-button :loading="saveLoading" @click="submitForm('ruleForm')" type="primary">保存</el-button>
        </div>
      </div>
    </div>
  </el-form>
</template>

<script>
import { create, update, getById, appendStock, appendLimitQuantity } from '@/api/activity/seckill/list';
import findIndex from 'lodash/findIndex';
import pickBy from 'lodash/pickBy';
import cloneDeep from 'lodash/cloneDeep';
import floor from 'lodash/floor';
import { parseDefaultTime } from '@/utils';
import { sub } from '@/utils/math';
import Validate from './Validate';
import CycleRule from './CycleRule';
import { validateInteger } from '@/common/validator';
import AddQuantity from '@/components/AddQuantity';
import { parseRangeDate } from '@/utils/range-date';
import AddCommodity from '@/components/AddCommodity';
import uniqBy from 'lodash/uniqBy';
export default {
  components: {
    Validate,
    CycleRule,
    AddQuantity,
    'add-commodity': AddCommodity
  },
  name: 'seckill',
  props: {
    id: String,
    isEdit: {
      type: Boolean,
      default: false
    },
    isDetail: {
      type: Boolean,
      default: false
    }
  },
  created() {
    this.initData();
    // this.fetchData();
  },
  data() {
    const initFilter = {
      name: ''
    };
    return {
      commodityList: [], // 选择商品组件选中的商品
      showValidate: false, // 控制选择商品的校验的提示的显示和隐藏
      status: '', // 活动状态
      validateAll: false, // 是否全部校验通过
      visible: false,
      visible2: false,
      visible3: false,
      priceDiscount: '',
      priceReduce: '',
      quantityLimit: '',
      initFilter,
      filter: cloneDeep(initFilter),
      saveLoading: false,
      loading: false,
      defaultDate: ['00:00:00', '23:59:59'],
      dialogTableVisible: false, // 商品管理弹窗是否可见
      seckillCommodity: [], // 限时抢购商品列表
      scale: 2, // 抹去角分，默认不抹去
      isOverlay: false, // 优惠叠加
      cycleRule: null, // 子组件传来的周期重复规则
      cycleRuleGet: null, // 接口获取到的周期重复规则
      radioPreferential: '', // 优惠设置单选框
      radioStock: '', // 活动库存单选框
      radioLimit: '', // 限购单选框
      listLoading: true,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      ruleForm: {
        name: '', // 活动名称
        label: '', // 活动标签
        activityDate: [], // 活动时间
        startDate: '', // 活动开始时间
        endDate: '', // 活动结束时间
        cycleRule: false, // 是否周期重复
        preheatDay: 7 // 预热天数
      },
      // 用于编辑时比较更新
      activityDate: [], // 活动时间
      rules: {
        name: [
          { required: true, message: '必填信息', trigger: 'blur' },
          { max: 30, message: '输入不能超过30个字', trigger: 'blur' }
        ],
        label: [
          { required: true, message: '必填信息', trigger: 'blur' },
          { max: 5, message: '输入不能超过5个字', trigger: 'blur' }
        ],
        activityDate: [
          { required: true, message: '必填信息', trigger: 'blur' },
          {
            validator: (_, value, callback) => {
              if (!this.isEdit || this.ruleForm.status !== 'USE' || !this.activityDate.length || !value.length) {
                callback();
                return;
              }
              const [start, end] = this.activityDate;
              const [startDate, endDate] = value;
              if (start.getTime() === startDate.getTime() && endDate.getTime() >= end.getTime()) {
                callback();
              } else {
                callback(new Error('进行中的活动只支持延长结束时间'));
              }
            },
            message: '进行中的活动只支持延长结束时间',
            trigger: 'blur'
          }
        ],
        preheatDay: [
          { required: true, message: '必填信息', trigger: 'blur' },
          { validator: validateInteger, trigger: 'blur' }
        ]
      }
    };
  },
  computed: {
    // 过滤
    dataCommodity() {
      return pickBy(this.filter, (val) => !!val);
    },
    statusEdit() {
      // 当活动正在进行中的时候只能修改活动名称和活动标签;statusEdit为false是可以修改
      let statusEdit = false;
      if (this.isEdit) {
        this.status === 'USE' ? (statusEdit = true) : (statusEdit = false);
      }
      return statusEdit;
    },
    data() {
      // 新增或编辑接口传参组装
      const obj = {};
      if (this.id) {
        obj.id = this.id;
      }
      obj.name = this.ruleForm.name;
      obj.label = this.ruleForm.label;
      obj.preheatDay = parseInt(this.ruleForm.preheatDay);
      obj.scale = this.scale;
      const [startDate, endDate] = this.ruleForm.activityDate;
      obj.startDate = parseDefaultTime(startDate);
      obj.endDate = parseDefaultTime(endDate);
      obj.cycleRule = this.cycleRule;
      const commodityParams = [];
      this.seckillCommodity.forEach((item) => {
        const { commodityId, id = '', marketPrice = 0, name, price = 0, seckillId = '', thumbnailUrl } = item;
        let { setStock: stock, quantityLimit, priceDiscount, priceReduce } = item;
        if (!priceDiscount) {
          priceDiscount = 10;
        }
        if (!priceReduce) {
          priceReduce = 0;
        }
        if (!stock) {
          stock = -1;
        }
        if (!quantityLimit) {
          quantityLimit = -1;
        }
        commodityParams.push({
          commodityId,
          id,
          marketPrice,
          name,
          price,
          priceDiscount,
          priceReduce,
          quantityLimit,
          seckillId,
          stock,
          thumbnailUrl
        });
      });
      obj.commodityParams = commodityParams;
      obj.isEnable = 1;
      if (this.isOverlay) {
        obj.isOverlay = 1;
      } else {
        obj.isOverlay = 0;
      }
      return obj;
    }
  },
  methods: {
    // 用来接收添加商品组件的实时展示在页面上的列表的
    updateCurrentTable(commodityList) {
      this.commodityList = commodityList;
      let seckillCommodity = [];
      seckillCommodity = commodityList.map((item) => {
        // const oldPrice = get(minBy(item.skuBriefs, 'price'), 'price');
        return {
          commodityId: item.id,
          name: item.name,
          price: item.price,
          // oldPrice,
          marketPrice: item.marketPrice,
          status: item.status,
          statusName: item.statusName,
          stock: item.stock,
          thumbnailUrl: item.thumbnailUrl,
          skuVOList: item.skuBriefs,
          priceDiscount: '',
          priceDiscount2: '',
          priceReduce: '',
          priceReduce2: '',
          radioLimit: item.radioLimit || '1',
          radioPreferential: item.radioPreferential || '1',
          radioStock: item.radioStock || '1',
          quantityLimit: '',
          setStock: ''
        };
      });
      // 添加商品后的限时抢购商品列表
      let newSeckillCommodity = [];
      // 初始的限时抢购商品列表
      const initSeckillCommodity = [...this.seckillCommodity];
      newSeckillCommodity = uniqBy([...initSeckillCommodity, ...seckillCommodity], 'commodityId');
      // 数组去重
      this.seckillCommodity = newSeckillCommodity;
      this.commodityList.length === 0 ? (this.showValidate = true) : (this.showValidate = false);
    },
    // 追加库存
    async appendStock(value, querryData) {
      await appendStock({
        commodityParamId: querryData.id,
        addNum: value
      });
      // 获取当前追加的商品的索引
      const itemIndex = findIndex(this.seckillCommodity, function (o) {
        return o.id === querryData.id;
      });
      this.seckillCommodity[itemIndex].setStock += value;
    },
    // 修改限购
    async appendLimitQuantity(value, querryData) {
      await appendLimitQuantity({
        commodityId: querryData.commodityId,
        appendLimit: value,
        id: this.ruleForm.id
      });
      // 获取当前追加的商品的索引
      const itemIndex = findIndex(this.seckillCommodity, function (o) {
        return o.id === querryData.id;
      });
      this.seckillCommodity[itemIndex].quantityLimit += value;
    },
    floor,
    sendDialogTableVisible(value) {
      this.dialogTableVisible = value;
    },
    // 获取子组件CycleRule传过来的cycleRule对象
    sendCycleRule(value) {
      this.cycleRule = value;
    },
    handHide(data) {
      this[data] = '';
    },
    // 是否校验通过
    inputValidate(msg) {
      this.validateAll = msg;
      return msg;
    },
    // 列表打折变化
    discount(item, index) {
      const val = item;
      const seckillCommodity = [...this.seckillCommodity];
      val.priceDiscount2 = floor((val.price * val.priceDiscount) / 10, 2);
      this.seckillCommodity[index].skuVOList ? (seckillCommodity[index].skuVOList = [...this.seckillCommodity[index].skuVOList]) : '';
      this.seckillCommodity[index] = val;
    },
    // 打折后价格变化
    discountPrise(item, index) {
      const val = item;
      const seckillCommodity = [...this.seckillCommodity];
      val.priceDiscount = floor((val.priceDiscount2 / val.price) * 10, 2);
      this.seckillCommodity[index].skuVOList ? (seckillCommodity[index].skuVOList = [...this.seckillCommodity[index].skuVOList]) : '';
      this.seckillCommodity[index] = val;
    },
    // 列表减价变化
    reduce(item, index) {
      const val = item;
      const seckillCommodity = [...this.seckillCommodity];
      val.priceReduce2 = sub(val.price, val.priceReduce);
      this.seckillCommodity[index].skuVOList ? (seckillCommodity[index].skuVOList = [...this.seckillCommodity[index].skuVOList]) : '';
      this.seckillCommodity[index] = val;
    },
    // 减价后价格变化
    reducePrise(item, index) {
      const val = item;
      const seckillCommodity = [...this.seckillCommodity];
      val.priceReduce = sub(val.price, val.priceReduce2);
      this.seckillCommodity[index].skuVOList ? (seckillCommodity[index].skuVOList = [...this.seckillCommodity[index].skuVOList]) : '';
      this.seckillCommodity[index] = val;
    },
    // 全部限购
    handleQuantityLimit() {
      this.visible3 = false;
      if (this.seckillCommodity.length > 0) {
        const seckillCommodity = [];
        this.seckillCommodity.forEach((item) => {
          const { id, name, price, priceDiscount, priceDiscount2, priceReduce, priceReduce2, setStock, stock, thumbnailUrl, radioPreferential, radioStock, commodityId, seckillId, skuVOList = [] } = item;
          seckillCommodity.push({
            id,
            name,
            price,
            priceDiscount,
            priceDiscount2,
            priceReduce,
            priceReduce2,
            quantityLimit: this.quantityLimit,
            setStock,
            stock,
            thumbnailUrl,
            radioPreferential,
            radioStock,
            radioLimit: '2',
            commodityId,
            seckillId,
            skuVOList
          });
        });
        this.seckillCommodity = seckillCommodity;
        this.quantityLimit = '';
      } else {
        this.$message.error('请先添加抢购商品');
        this.quantityLimit = '';
        return false;
      }
    },
    // 全部减价
    handleAllpriceReduce() {
      this.visible2 = false;
      if (this.seckillCommodity.length > 0) {
        const seckillCommodity = [];
        this.seckillCommodity.forEach((item) => {
          const { id, name, price, quantityLimit, setStock, stock, thumbnailUrl, radioStock, radioLimit, commodityId, seckillId, skuVOList = [] } = item;
          const skuVOListResult = [];
          skuVOList.forEach((item) => {
            skuVOListResult.push({
              ...item,
              result: this.priceReduce
            });
          });
          seckillCommodity.push({
            id,
            name,
            price,
            priceDiscount: '',
            priceDiscount2: '',
            priceReduce: this.priceReduce,
            priceReduce2: sub(price, this.priceReduce),
            quantityLimit,
            setStock,
            stock,
            thumbnailUrl,
            radioPreferential: '2',
            radioStock,
            radioLimit,
            commodityId,
            seckillId,
            skuVOList: skuVOListResult
          });
        });
        this.seckillCommodity = seckillCommodity;
        this.priceReduce = '';
      } else {
        this.$message.error('请先添加抢购商品');
        this.priceReduce = '';
        return false;
      }
    },
    // 全部打折
    handleAllpriceDiscount() {
      if (!this.priceDiscount) {
        return;
      }
      if (this.seckillCommodity.length > 0) {
        const seckillCommodity = [];
        this.seckillCommodity.forEach((item) => {
          const { id, name, price, quantityLimit, setStock, stock, thumbnailUrl, radioStock, radioLimit, commodityId, seckillId, skuVOList = [] } = item;
          const skuVOListResult = [];
          skuVOList.forEach((item) => {
            skuVOListResult.push({
              ...item,
              result: this.priceDiscount
            });
          });
          seckillCommodity.push({
            id,
            name,
            price,
            priceDiscount: this.priceDiscount,
            priceDiscount2: floor((price * this.priceDiscount) / 10, 2),
            priceReduce: '',
            priceReduce2: '',
            quantityLimit,
            setStock,
            stock,
            thumbnailUrl,
            radioPreferential: '1',
            radioStock,
            radioLimit,
            commodityId,
            seckillId,
            skuVOList: skuVOListResult
          });
        });
        this.seckillCommodity = seckillCommodity;
        this.priceDiscount = '';
        this.visible = false;
      }
    },
    // 取消
    back() {
      this.$back({
        path: '/activity/seckill/list'
      });
    },
    // 删除商品
    handleDelete(index) {
      this.seckillCommodity.splice(index, 1);
      this.commodityList.splice(index, 1);
    },
    // 点击添加商品
    onAdd() {
      this.dialogTableVisible = true;
    },
    // 商品添加取消
    onCancel() {
      this.dialogTableVisible = false;
    },
    initData() {
      if (this.id) {
        this.fetchDetailDate();
      }
    },
    // 根据id获取活动详情
    fetchDetailDate() {
      this.loading = true;
      getById(this.id)
        .then((response) => {
          const { endDate, id, isEnable, isOverlay, preheatDay, label, status, marketingConfigId, name, scale, startDate, cycleRule = null, commodityParams = [] } = response.data;
          const activityDate = [];
          activityDate.push(startDate);
          activityDate.push(endDate);
          // 限购商品列表拼数据
          const seckillCommodity = [];
          // 选择商品组件初始化数据
          // const commodityList = [];
          commodityParams.forEach((item) => {
            const { id, commodityId, seckillId, name, commodityStock: stock, thumbnailUrl, skuVOList, price } = item;
            // 获取最小的商品原价
            // const oldPrice = get(minBy(skuVOList, 'price'), 'price');
            let { quantityLimit, stock: setStock, priceDiscount2 = '', priceReduce2 = '', priceDiscount, priceReduce } = item;
            let [radioPreferential, radioStock, radioLimit] = ['1', '1', '1'];
            if (priceDiscount === 10 && priceReduce !== 0) {
              radioPreferential = '2';
              priceReduce2 = sub(price, priceReduce);
              priceDiscount = '';
            } else {
              priceDiscount2 = floor((price * priceDiscount) / 10, 2);
              priceReduce = '';
            }
            if (setStock !== -1) {
              radioStock = '2';
            } else {
              setStock = '';
            }
            if (quantityLimit !== -1) {
              radioLimit = '2';
            } else {
              quantityLimit = '';
            }
            seckillCommodity.push({
              id,
              name,
              price,
              priceDiscount,
              priceDiscount2,
              priceReduce,
              priceReduce2,
              quantityLimit,
              setStock,
              stock,
              thumbnailUrl,
              radioPreferential,
              radioStock,
              radioLimit,
              commodityId,
              seckillId,
              skuVOList
              // oldPrice
            });
            // commodityList.push({
            //   id: commodityId,
            //   name,
            //   price,
            //   thumbnailUrl,
            //   radioPreferential,
            //   radioStock,
            //   radioLimit,
            //   seckillId,
            //   skuVOList,
            //   oldPrice
            // });
          });
          this.seckillCommodity = seckillCommodity;
          // this.commodityList = commodityList;
          // this.multipleSelectionAll = seckillCommodity;
          // 传给子组件的数据
          this.cycleRuleGet = cycleRule;
          let cycleRuleCheck = false;
          if (cycleRule) {
            cycleRuleCheck = true;
          }
          this.ruleForm = {
            cycleRule: cycleRuleCheck,
            endDate,
            id,
            isEnable,
            status,
            label,
            preheatDay,
            marketingConfigId,
            name,
            startDate,
            activityDate
          };
          if (isOverlay === '1') {
            this.isOverlay = true;
          }
          if (isOverlay === '0') {
            this.isOverlay = false;
          }
          this.scale = scale;
          this.status = status;
          this.ruleForm.activityDate = parseRangeDate(response.data, 'startDate', 'endDate');
          // 暂存数据 用于活动编辑 比较更新
          ['activityDate'].forEach((key) => {
            if (this.ruleForm[key]) this[key] = [...this.ruleForm[key]];
          });
        })
        .finally(() => {
          this.loading = false;
        });
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (!valid) {
          return false;
        }
        const validateResultArr = this.$refs['validate'];
        let validateResult = true;
        validateResultArr.forEach((item) => {
          if (!item.validateResult) {
            validateResult = false;
          }
        });
        // 校验限时抢购设置列表里面的数据是否符合要求
        if (!validateResult) {
          this.$message.error('数据有误，请检查之后再提交');
          return false;
        }
        const data = { ...this.data };
        if (this.isEdit) {
          data.id = this.id;
        }
        const request = this.isEdit ? update : create;
        this.saveLoading = true;
        request(data)
          .then((response) => {
            this.$message.success('保存成功');
            this.$back({
              path: '/activity/seckill/list'
            });
          })
          .finally(() => {
            this.saveLoading = false;
          });
      });
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
@import './SeckillDetail';
.mar {
  margin-right: 6px;
}
</style>
