<!--限时抢购-选择商品组件-->
<template>
  <div>
    <div class="filter-list">
      <div class="filter-item">
        <span class="label">商品名称:</span>
        <div class="content">
          <el-input v-model="filter.name" clearable></el-input>
        </div>
      </div>
      <div class="filter-item">
        <span class="label">状态:</span>
        <div class="content">
          <el-select v-model="filter.status" placeholder="请选择">
            <el-option
              v-for="item in statusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </div>
      </div>
      <div class="filter-item">
        <el-button @click="onSearch" type="primary" size="small"
          >查询</el-button
        >
      </div>
    </div>
    <el-table
      height="500"
      :data="listCommodity"
      ref="multipleTable"
      tooltip-effect="dark"
      style="width: 100%"
      v-loading="listLoading"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        type="selection"
        width="55"
        :selectable="handleSelectable"
      ></el-table-column>
      <el-table-column property="date" label="商品信息" width="350">
        <template slot-scope="scope">
          <div class="commodity-info">
            <img :src="scope.row.thumbnailUrl" alt />
            <p>{{ scope.row.name }}</p>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="price" label="价格">
        <template slot-scope="scope">
          <div v-if="scope.row.minPrice === scope.row.maxPrice">
            <span>￥{{ scope.row.maxPrice }}</span>
          </div>
          <div v-if="scope.row.minPrice !== scope.row.maxPrice">
            <span>￥{{ scope.row.minPrice }}</span>
            <span>-</span>
            <span>￥{{ scope.row.maxPrice }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column property="stock" label="库存"></el-table-column>
      <el-table-column property="address" label="参与活动">
        <template slot-scope="scope">
          <p v-for="(item, index) in scope.row.activityArr" :key="index">
            {{ item.activityTypeName + '：' + item.name }}
          </p>
        </template>
      </el-table-column>
    </el-table>
    <div class="pagination">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pageNo"
        :page-sizes="[10, 20, 30, 40,50,100]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        :disabled="listLoading"
      ></el-pagination>
    </div>
    <el-button @click="onCancel" type="primary" size="small">取消</el-button>
    <el-button @click="onSave" type="primary" size="small">保存</el-button>
  </div>
</template>

<script>
import cloneDeep from 'lodash/cloneDeep';
import { listCommodity, listCommodityTool } from '@/api/activity/seckill/list';
import pickBy from 'lodash/pickBy';
export default {
  name: 'GoodTable',
  model: {},
  data() {
    const initFilter = {
      name: '',
      status: '1'
    };
    return {
      initFilter,
      filter: cloneDeep(initFilter),
      listCommodity: [], // 商品列表数据包含活动信息
      noActivityCommodity: [], // 不包含活动信息的商品列表
      multipleSelectionAll: [], // 翻页之后合并选中的商品数据
      multipleSelection: [],
      listLoading: true,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      // 商品上架状态
      statusOptions: [
        {
          value: '0',
          label: '待上架'
        },
        {
          value: '1',
          label: '上架中'
        },
        {
          value: '3',
          label: '已下架'
        }
      ]
    };
  },
  computed: {
    // 过滤
    dataCommodity() {
      return pickBy(this.filter, (val) => !!val);
    }
  },
  created() {
    this.fetchData();
  },
  watch: {
    // 当设置列表的数据变化时清空选中状态
    seckillCommodity(value) {
      if (value.length > 0) {
        this.$refs.multipleTable.clearSelection();
      }
    },
    deleteItem(value) {
      // 列表中删除的商品再选中的商品列表里面删除
      const multipleSelectionAll = [];
      this.multipleSelectionAll.forEach((item) => {
        if (item.commodityId !== value) {
          multipleSelectionAll.push(item);
        }
      });
      this.multipleSelectionAll = multipleSelectionAll;
      this.$emit('sendMultipleSelectionAll', multipleSelectionAll);
    }
  },
  props: {
    id: String,
    // 是否是详情
    isDetail: {
      type: Boolean,
      default: false
    },
    // 是否可编辑
    statusEdit: {
      type: Boolean,
      default: false
    },
    // 限时抢购商品列表
    seckillCommodity: {
      type: Array,
      default: function () {
        return [];
      }
    },
    deleteItem: {
      type: String,
      default: null
    }
  },
  methods: {
    // 限时抢购商品是否可以选择
    handleSelectable(row, index) {
      if (row.activityArr) {
        return false;
      } else {
        return true;
      }
    },
    // 商品添加取消
    onCancel() {
      this.$emit('sendDialogTableVisible', false);
    },
    // 商品添加保存
    onSave() {
      const multipleSelectionAll = this.multipleSelection.concat(
        this.multipleSelectionAll
      );
      this.multipleSelectionAll = multipleSelectionAll;
      this.$emit('sendMultipleSelectionAll', multipleSelectionAll);
    },
    // 商品查询
    onSearch() {
      this.pageNo = 1;
      this.pageSize = 10;
      this.$nextTick(() => {
        this.fetchData();
      });
    },
    // 获取商品列表
    fetchData() {
      this.listLoading = true;
      const listQuery = {
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        data: this.dataCommodity
      };
      listCommodity(listQuery)
        .then((response) => {
          const { list = [], total = 0 } = response.data || {};
          this.noActivityCommodity = list;
          this.total = total;
          const ids = [];
          list.forEach((item) => {
            ids.push(item.id);
          });
          this.getListCommodityTool(ids);
        })
        .finally(() => {
          this.listLoading = false;
        });
    },
    getListCommodityTool(ids) {
      const listQuery = {
        activityType: 'SECKILL',
        commodityIds: ids,
        isValidDate: '1'
      };
      // 根据id获取商品参与的活动
      listCommodityTool(listQuery).then((response) => {
        if (response.success) {
          const data = response.data;
          const listCommodity = cloneDeep(this.noActivityCommodity);
          // 遍历商品
          data.forEach((item) => {
            const { marketingToolCommodityEsVOS = [], commodityId } =
              item || {};
            // 遍历商品参与的活动
            const activityArr = [];
            marketingToolCommodityEsVOS.forEach((item2) => {
              const { name, activityTypeName } = item2;
              activityArr.push({
                name: name,
                activityTypeName: activityTypeName
              });
            });
            // 遍历普通商品列表，并将activityTypeName拼接到listCommodity
            listCommodity.forEach((item3, index) => {
              if (item3.id === commodityId) {
                item3.activityArr = activityArr;
              }
              listCommodity[index] = item3;
            });
          });
          this.listCommodity = listCommodity;
        }
      });
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.pageNo = 1;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.pageNo = val;
      const multipleSelectionAll = this.multipleSelection.concat(
        this.multipleSelectionAll
      );
      this.multipleSelectionAll = multipleSelectionAll;
      this.fetchData();
    },
    // 商品选中
    handleSelectionChange(val) {
      const multipleSelection = [];
      val.forEach((item, index) => {
        const {
          id: commodityId,
          name,
          minPrice: price,
          marketPrice,
          status,
          statusName,
          stock,
          thumbnailUrl,
          skuVOList = []
        } = item;
        multipleSelection.push({
          commodityId,
          name,
          price,
          marketPrice,
          status,
          statusName,
          stock,
          thumbnailUrl,
          skuVOList
        });
      });
      this.multipleSelection = multipleSelection;
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.filter-list {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  .filter-item {
    font-size: 14px;
    box-sizing: border-box;
    display: flex;
    margin-bottom: 16px;
    padding: 0 24px;
    width: 33.333%;
    min-width: 334px;
    text-align: right;
    .label {
      padding-right: 16px;
      white-space: nowrap;
      display: inline-block;
      line-height: 32px;
    }
    .content {
      flex: 1;
    }
    .el-button {
      margin-left: 12px;
    }
    &.btns-open {
      display: block;
      width: 100%;
    }
  }
}
.commodity-info {
  display: flex;
  img {
    width: 50px;
    height: 50px;
  }
  p {
    margin-left: 12px;
  }
}
</style>
