$text: rgba(0, 0, 0, 0.65);
.el-form-item {
  margin: 0 0 24px;
}
.el-select,
.el-input,
.el-input__inner,
.el-textarea {
  width: 400px;
}
.form-container {
  padding-bottom: 60px;
  position: relative;
  height: 100%;
  overflow: auto;
}
.type-radio .el-radio {
  margin: 3px 6px 3px 0 !important;
}
.fix-part {
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.3);
  background-color: #fff;
  position: fixed;
  left: 0;
  z-index: 2;
  width: 100%;
  bottom: 0;
  text-align: right;
  .el-form-item {
    padding: 10px 0;
    margin: 0 20px;
  }
}
.part {
  background-color: #fff;
  box-shadow: 0 0 2px rgba(0, 0, 0, 0.2);
  & > .title {
    font-weight: bolder;
    border-bottom: 1px solid #e8e8e8;
    padding: 18px 30px;
    font-size: 16px;
    user-select: none;
  }
  & > .content {
    padding: 18px 30px;
    width: 100%;
    overflow: auto;
  }
  & ~ .part {
    margin-top: 20px;
  }
}
.add-commodity {
  padding-left: 24px;
}
.el-radio {
  margin-top: 10px;
}
.my-input-class {
  width: 180px;
}
.my-span {
  width: 50px;
  line-height: 36px;
  font-size: 14px;
  color: #000;
}
.my-discountContent {
  margin-top: 0;
}
.validate-tips {
  color: var(--color-danger);
  font-size: 12px;
  line-height: 1;
  padding-top: 4px;
}
.my-checked {
  float: left;
  line-height: 35px;
}
.tips {
  color: #666;
  font-size: 12px;
  margin: -4px 0 15px 130px;
}
.no-margin-tips {
  margin: 0;
}
.red {
  color: red;
}
.my-discountContent .el-form-item__error {
  padding-left: 22px;
}
.el-form-item__content {
  margin-left: 140px;
}
.get-tips {
  width: 400px;
  border: 1px dashed red;
  position: absolute;
  top: 0;
  left: 425px;
}
.cycle-type-div {
  margin-left: 24px;
  .el-date-editor {
    margin-top: 12px;
  }
  .delete {
    margin-left: 12px;
  }
  .add {
    margin-top: 12px;
  }
}
.stock-table {
  width: 100%;
  border: 1px solid #e5e5e5;
  text-align: center;
  background-color: #fff;
  & > thead {
    background-color: #f8f8f8;
    th {
      font-weight: 400;
      color: rgba(27, 23, 23, 0.85);
    }
    .th-right {
      text-align: right;
      padding-right: 24px;
    }
    .th-left {
      text-align: left;
      padding-left: 24px;
    }
  }
  & > tfoot {
    border-top: 1px solid #e5e5e5;
  }
  td,
  th {
    padding: 16px 10px;
  }
  td {
    color: rgba(0, 0, 0, 0.65);
    border-top: 1px solid #e5e5e5;
    & + td {
      border-left: 1px solid #e5e5e5;
    }
  }
  .foot-label {
    color: rgba(27, 23, 23, 0.85);
  }
  .links {
    > * + * {
      margin-left: 2px;
    }
  }
  .btns {
    margin-left: 8px;
  }
}
.filter-list {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  .filter-item {
    font-size: 14px;
    box-sizing: border-box;
    display: flex;
    margin-bottom: 16px;
    padding: 0 24px;
    width: 33.333%;
    min-width: 334px;
    text-align: right;
    .label {
      padding-right: 16px;
      white-space: nowrap;
      display: inline-block;
      line-height: 32px;
    }
    .content {
      flex: 1;
    }
    .el-button {
      margin-left: 12px;
    }
    &.btns-open {
      display: block;
      width: 100%;
    }
  }
}
.tbody-flow {
  min-width: 100%;
  overflow: auto;
}
.commodity-info {
  display: flex;
  img {
    width: 50px;
    height: 50px;
  }
  p {
    margin-left: 12px;
  }
}
.seckillCommodity-info {
  display: flex;
  img {
    width: 50px;
    height: 50px;
  }
  p {
    font-size: 12px;
    text-align: left;
    margin: 0;
    margin-bottom: 5px;
    margin-left: 12px;
  }
}
.seckill-td {
  font-size: 14px;
  display: flex;
  align-items: flex-start;
  white-space: nowrap;
  .seckill-td-div {
    display: flex;
    align-items: flex-start;
    .el-button--text {
      padding: 0;
      margin-top: 4px;
    }
  }
  .el-radio {
    margin: 0;
    display: flex;
    align-items: center;
    margin-right: 10px;
    margin-top: 4px;
  }
  input {
    padding: 4px;
    width: 66px;
    border: 1px solid #dcdfe6;
    border-radius: 3px;
    outline: none;
    color: rgba(0, 0, 0, 0.65);
    transition: border-color,
      box-shadow 0.2s cubic-bezier(0.645, 0.045, 0.355, 1),
      -webkit-box-shadow 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  }
  input[disabled='disabled'] {
    background-color: #f5f7fa;
    border-color: #e4e7ed;
    color: #c0c4cc;
    cursor: not-allowed;
  }
  span {
    margin: 0 5px;
  }
}
.seckill-td-next {
  margin-top: 12px;
}
.btn-wrap {
  text-align: center;
  margin: 24px 0;
  background-color: #fff;
  height: 80px;
}
.priceDiscount {
  width: 95px;
}
.center-position {
  text-align: center;
}
.el-popover div {
  text-align: center !important;
}
.tips-validate {
  color: red;
}
.validate-style {
  width: 100%;
}
.validate-style2 {
  width: 100%;
  margin-left: 7px;
}
.validate-style4 {
  width: 100%;
  margin-left: -14px;
}
