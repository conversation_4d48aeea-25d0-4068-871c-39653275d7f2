<!--表格里input校验-->
<template>
  <div>
    <p class="tips-validate" v-if="showTips">{{ allPriceTips }}</p>
  </div>
</template>

<script>
export default {
  name: 'validate',
  model: {},
  data() {
    return {
      validateResult: false,
      showTips: false, // 是否展示校验提示
      allPriceTips: '', // 全部打折校验提示
      ereg: {
        discount: /^((0\.[1-9]{1})|(([1-9]{1})(\.\d{1})?))$/, // 折扣校验 大于0小于10且保留一位小数
        money:
          /^([1-9]{1}[0-9]{0,3}(\,[0-9]{3,4})*(\.[0-9]{0,2})?|[1-9]{1}\d*(\.[0-9]{0,2})?|0(\.[0-9]{0,2})?|(\.[0-9]{1,2})?)$/, // 保留两位小数的金钱校验
        integer: /^([1-9]\d*|[0]{1,1})$/ // 大于1的整数校验
      }
    };
  },
  computed: {},
  created() {
    this.priceDiscountValidate();
  },
  watch: {
    inputValue() {
      this.priceDiscountValidate();
    },
    seckillCommodity() {
      this.priceDiscountValidate();
    }
  },
  props: {
    seckillCommodity: {
      type: Array,
      default: function () {
        return [1];
      }
    },
    inputValue: {
      default: 10
    },
    // integer-大于1的正整数；money-保留两位小数的金钱；integer-折扣大于0小于10，保留一位小数
    type: {
      type: String,
      default: ''
    }
  },
  methods: {
    // 折扣校验
    validateDiscount() {
      if (!this.ereg.discount.test(this.inputValue)) {
        this.showTips = true;
        this.allPriceTips = '输入的数据有误';
        this.validateResult = false;
        this.$emit('validate', false);
        return;
      } else {
        this.validateResult = true;
        this.$emit('validate', true);
      }
    },
    // 保留两位小数的金钱校验
    validateMoney() {
      if (!this.ereg.money.test(this.inputValue)) {
        this.showTips = true;
        this.allPriceTips = '输入的数据有误';
        this.validateResult = false;
        this.$emit('validate', false);
        return;
      } else {
        this.validateResult = true;
        this.$emit('validate', true);
      }
    },
    // 大于等于1的整数校验
    validateInteger() {
      if (!this.ereg.integer.test(this.inputValue)) {
        this.showTips = true;
        this.allPriceTips = '输入的数据有误';
        this.validateResult = false;
        this.$emit('validate', false);
        return;
      } else if (this.inputValue < 1 || this.inputValue > 999999999) {
        this.showTips = true;
        this.allPriceTips = '请输入1-999999999的数字';
        this.validateResult = false;
        this.$emit('validate', false);
        return;
      } else {
        this.validateResult = true;
        this.$emit('validate', true);
      }
    },
    // 全部打折校验
    priceDiscountValidate() {
      if (this.seckillCommodity.length === 0) {
        this.showTips = true;
        this.allPriceTips = '请先添加抢购商品';
        return;
      }
      if (!this.inputValue) {
        this.showTips = true;
        this.allPriceTips = '必填信息';
        return;
      } else {
        this.showTips = false;
      }
      // discount-折扣校验
      if (this.type === 'discount') {
        this.validateDiscount();
      }
      // 金钱校验
      if (this.type === 'money') {
        this.validateMoney();
      }
      // 大于等于1的整数校验
      if (this.type === 'integer') {
        this.validateInteger();
      }
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.tips-validate {
  color: red;
}
</style>
