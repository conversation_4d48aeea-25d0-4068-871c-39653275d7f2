<template>
  <div class="app-container">
    <div class="table-container">
      <form @submit.prevent="onSearch" class="commo-search-container commo-search-flex commo-search-margin-bottom10">
        <div class="commo-search-item">
          <span class="commo-search-item-label">活动名称:</span>
          <div class="commo-search-item-content">
            <el-input size="small" clearable v-model.trim="filter.name"></el-input>
          </div>
        </div>
        <div class="commo-search-item">
          <span class="commo-search-item-label">活动id:</span>
          <div class="commo-search-item-content">
            <el-input size="small" clearable v-model.trim="filter.id"></el-input>
          </div>
        </div>
        <div class="commo-search-item">
          <span class="commo-search-item-label">状态:</span>
          <div class="commo-search-item-content">
            <el-select size="small" clearable v-model="filter.status">
              <el-option label="全部" value="ALL"></el-option>
              <el-option label="未开始" value="FUTURE"></el-option>
              <el-option label="进行中" value="USE"></el-option>
              <el-option label="已结束" value="END"></el-option>
              <el-option label="已停用" value="STOPPED"></el-option>
            </el-select>
          </div>
        </div>
        <div class="commo-search-btns">
          <el-button native-type="submit" size="small" type="primary">查询</el-button>
          <el-button @click="onReset" size="small">重置</el-button>
        </div>
      </form>
      <router-link to="/activity/seckill/create">
        <Authority auth="/activity/create/seckill">
          <el-button class="add-btn" icon="el-icon-plus" size="small" type="primary">新增</el-button>
        </Authority>
      </router-link>
      <el-table :data="list" element-loading-text="加载中" fit highlight-current-row ref="multipleTable" v-loading="listLoading">
        <el-table-column align="center" label="活动id" prop="id"></el-table-column>
        <el-table-column align="center" label="活动名称" prop="name"></el-table-column>
        <el-table-column align="center" label="活动标签" prop="label"></el-table-column>
        <el-table-column align="center" label="活动时间" width="200">
          <template :default-time="defaultDate" slot-scope="scope">
            <p>开始：{{ scope.row.startDate | parseTime('{y}-{m}-{d} {h}:{i}:{s}') }}</p>
            <p>结束：{{ scope.row.endDate | parseTime('{y}-{m}-{d} {h}:{i}:{s}') }}</p>
          </template>
        </el-table-column>
        <el-table-column align="center" label="状态" prop="statusName"></el-table-column>
        <el-table-column align="center" label="操作">
          <template slot-scope="scope">
            <router-link :to="'/activity/seckill/effect-data/' + scope.row.id" class="link">
              <el-button size="small" type="text" v-if="scope.row.status !== 'FUTURE'">效果数据</el-button>
            </router-link>
            <router-link :to="'/activity/seckill/detail/' + scope.row.id" class="link">
              <Authority auth="/activity/view/seckill">
                <el-button size="small" type="text">查看</el-button>
              </Authority>
            </router-link>
            <router-link :to="'/activity/seckill/edit/' + scope.row.id" class="link">
              <Authority auth="/activity/edit/seckill">
                <el-button size="small" type="text" v-if="scope.row.status === 'USE' || scope.row.status === 'FUTURE'">编辑</el-button>
              </Authority>
            </router-link>
            <el-button type="text">
              <promotion :id="scope.row.id" :key="scope.row.id" :promType="'seckill'"></promotion>
            </el-button>
            <router-link class="link" to>
              <Authority auth="/activity/edit/seckill">
                <el-button @click="disableById(scope.row.id)" size="small" type="text" v-if="scope.row.status === 'USE'">停用</el-button>
              </Authority>
            </router-link>
            <router-link class="link" to>
              <Authority auth="/activity/edit/seckill">
                <el-button @click="deleteById(scope.row.id)" size="small" type="text" v-if="scope.row.status === 'FUTURE'">删除</el-button>
              </Authority>
            </router-link>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination :current-page="pageNo" :disabled="listLoading" :page-size="pageSize" :page-sizes="[10, 20, 30, 40, 50, 100]" :total="total" @current-change="handleCurrentChange" @size-change="handleSizeChange" background layout="total, sizes, prev, pager, next, jumper"></el-pagination>
      </div>
    </div>
  </div>
</template>
<script>
import { list, disableById, deleteById } from '@/api/activity/seckill/list';
import pickBy from 'lodash/pickBy';
import pick from 'lodash/pick';
import cloneDeep from 'lodash/cloneDeep';
import Promotion from '@/components/Promotion';
export default {
  name: 'activity-seckill-list',
  components: {
    promotion: Promotion
  },
  data() {
    const initFilter = {
      name: '',
      status: '',
      id: ''
    };
    return {
      initFilter,
      filter: cloneDeep(initFilter),
      list: [],
      listLoading: true,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      defaultDate: ['00:00:00', '23:59:59']
    };
  },
  computed: {
    // 过滤
    data() {
      return pickBy(this.filter, (val) => !!val);
    }
  },
  mounted() {
    this.fetchData();
  },
  activated() {
    this.fetchData();
  },
  methods: {
    handleSizeChange(val) {
      this.pageSize = val;
      this.pageNo = 1;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.pageNo = val;
      this.fetchData();
    },
    // 停用
    disableById(id) {
      this.$confirm('停用后您将无法重启该活动，确认是否停用?', '提示', {
        confirmButtonText: '停用',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          disableById(id)
            .then((response) => {
              this.fetchData();
            })
            .finally(() => {
              this.listLoading = false;
            });
          this.$message({
            type: 'success',
            message: '停用成功！'
          });
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消停用'
          });
        });
    },
    // 删除
    deleteById(id) {
      this.$confirm('确认删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          deleteById(id)
            .then((response) => {
              this.fetchData();
            })
            .finally(() => {
              this.listLoading = false;
            });
          this.$message({
            type: 'success',
            message: '删除成功！'
          });
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除'
          });
        });
    },
    fetchData() {
      this.listLoading = true;
      const listQuery = pick(this, ['pageNo', 'pageSize', 'data']);
      list(listQuery)
        .then((response) => {
          const { list = [], total = 0 } = response.data || {};
          this.list = list;
          this.total = total;
        })
        .finally(() => {
          this.listLoading = false;
        });
    },
    onSearch() {
      this.pageNo = 1;
      this.pageSize = 10;
      this.$nextTick(() => {
        this.fetchData();
      });
    },
    onReset() {
      this.pageNo = 1;
      this.pageSize = 10;
      this.filter = cloneDeep(this.initFilter);
      this.$nextTick(() => {
        this.fetchData();
      });
    }
  }
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
@import './styles';
</style>
