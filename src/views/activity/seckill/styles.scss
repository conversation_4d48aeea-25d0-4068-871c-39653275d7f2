.filter-list {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  .filter-item {
    font-size: 14px;
    box-sizing: border-box;
    display: flex;
    margin-bottom: 16px;
    padding: 0 24px;
    width: 33.333%;
    min-width: 334px;
    text-align: right;
    .label {
      padding-right: 16px;
      white-space: nowrap;
      display: inline-block;
      line-height: 32px;
    }
    .content {
      flex: 1;
    }
    &.btns-open {
      display: block;
      width: 100%;
    }
  }
}
.add-btn {
  margin-bottom: 10px;
  margin-top: 10px;
}
.link {
  color: var(--color-primary);
  &:hover {
    color: var(--color-primary);
  }
}
.filter-name {
  margin-right: 10px;
  color: #616366;
  font-weight: 500;
}

.toggle-btn {
  ::v-deep [class*='el-icon-'] + span {
    margin-left: 0;
  }
}

.el-date-editor--daterange.el-input__inner,
.el-select {
  width: 100%;
}
.header {
  display: flex;
  justify-content: space-between;
  padding-left: 5px;
  margin-bottom: 20px;
  .header-title {
    font-weight: 900;
    /* border-bottom: 1px solid #e8e8e8; */
    font-size: 16px;
    color: #333;
    display: inline-flex;
    align-items: center;
  }
  .actions-time {
    font-size: 12px;
    color: #999;
    margin-right: 10px;
  }
  a {
    font-size: 12px;
    color: var(--color-primary);
    text-decoration: none;
  }
}
.index {
  background-color: #fff;
}
.box {
  padding: 20px;
  .row {
    margin-left: 80px;
    position: relative;
    display: flex;
    .item {
      flex-basis: 33.33%;
      color: #333;
      font-size: 12px;
      .amount {
        cursor: pointer;
        display: block;
        font-size: 24px;
        margin: 6px 0 10px;
        color: var(--color-primary);
        text-decoration: none;
      }
      .amount-x {
        color: #f44;
      }
      .amount-yesterday {
        color: #999;
      }
    }
  }
}
.infobar {
  background: #f8f8f8;
  padding-bottom: 5px;
  position: relative;
  height: 150px;
  padding: 15px 20px;
  line-height: 20px;
  background-color: #f8f8f8;
  margin-bottom: 24px;
  .avatar {
    float: left;
    width: 45px;
    height: 45px;
    margin-right: 10px;
    box-sizing: border-box;
    border: 1px solid #e5e5e5;
    border-radius: 50%;
  }
  h3 {
    font-size: 14px;
    color: #111;
    font-weight: 700;
    padding-top: 3px;
    margin: 0;
  }
  p {
    font-size: 12px;
    color: #666;
    margin: 0;
    padding-top: 3px;
  }
  .create-btn {
    position: absolute;
    bottom: 20px;
    right: 25px;
  }
}
