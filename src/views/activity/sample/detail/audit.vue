<template>
  <el-form :model="form" :rules="rules" class="form-container" ref="form">
    <div class="app-container" v-loading="loading">
      <div class="part">
        <div class="title">申请人信息</div>
        <div class="content">
          <table class="head-table">
            <tbody>
              <tr>
                <td>
                  <span>店铺名称:</span>
                  <span v-if="detail.distributor">
                    {{ detail.distributor.distributorVO.shopName }}
                  </span>
                </td>
                <td>
                  <span>专属运营:</span>
                  <span
                    v-if="
                      detail.distributor &&
                      detail.distributor.distributorVO.customerServiceVO
                    "
                  >
                    {{
                      detail.distributor.distributorVO.customerServiceVO.name
                    }}
                    {{
                      detail.distributor.distributorVO.customerServiceVO.mobile
                    }}
                  </span>
                </td>
                <td>
                  <span>公司名称:</span>
                  <span v-if="detail.distributor">
                    {{ detail && detail.distributor.distributorVO.company }}
                  </span>
                </td>
              </tr>
              <tr>
                <td>
                  <span>了解渠道:</span>
                  <span v-if="detail.distributor">
                    {{ detail.distributor.distributorVO.knowChannelName }}
                  </span>
                </td>
                <td>
                  <span>店铺联系人:</span>
                  <span v-if="detail.distributor">
                    {{ detail.distributor.distributorVO.contactName }}
                  </span>
                </td>
                <td>
                  <span>联系方式:</span>
                  <span
                    v-if="
                      detail.distributor && detail.distributor.distributorVO
                    "
                  >
                    <CryptoBlock
                      detail-auth="/activity/:decrypt-mobile"
                      :bizId="detail.distributor.distributorVO.id"
                      bizType="DISTRIBUTOR_MOBILE"
                      v-model.trim="
                        detail.distributor.distributorVO.applyMobile
                      "
                    />
                  </span>
                </td>
              </tr>
              <tr>
                <td>
                  <span>线上/线下:</span>
                  <span v-if="detail.distributor">
                    {{ detail.distributor.distributorVO.channelTypeName }}
                  </span>
                </td>
                <td>
                  <span>渠道名称:</span>
                  <span v-if="detail.distributor">
                    {{ detail.distributor.distributorVO.channelName }}
                  </span>
                </td>
                <td>
                  <span>收货人:</span>
                  <span v-if="detail.address">
                    {{ detail.address.consignee
                    }}<CryptoBlock
                      detail-auth="/activity/:decrypt-mobile"
                      :bizId="id"
                      bizType="SAMPLE_CONSIGNEE_MOBILE"
                      v-model.trim="detail.address.mobile"
                    />
                  </span>
                </td>
              </tr>
              <tr>
                <td>
                  <span>收货地址:</span>
                  <span v-if="detail.address">
                    {{ detail.address.provinceName }}{{ detail.address.cityName
                    }}{{ detail.address.zoneName
                    }}<CryptoBlock
                      detail-auth="/activity/:decrypt-address-detail"
                      :bizId="id"
                      bizType="SAMPLE_ADDRESS_DETAIL"
                      v-model.trim="detail.address.addressDetail"
                    />
                  </span>
                </td>
                <td class="remark">
                  <el-form-item label="备注/原因" prop="approveMsg">
                    <el-input
                      :rows="2"
                      autocomplete="off"
                      type="textarea"
                      v-model="form.approveMsg"
                    ></el-input>
                  </el-form-item>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      <div class="part">
        <div class="title">商品信息</div>
        <div class="content">
          <el-table
            :data="list"
            element-loading-text="加载中"
            fit
            highlight-current-row
            ref="multipleTable"
          >
            <el-table-column
              align="center"
              label="商品Id"
              prop="commodityId"
            ></el-table-column>
            <el-table-column align="center" label="商品">
              <template slot-scope="scope">
                <img :src="scope.row.imgUrl" alt class="goods-img" />
                <div>{{ scope.row.commodityName }}</div>
              </template>
            </el-table-column>
            <el-table-column
              align="center"
              label="单价"
              prop="commodityPrice"
            ></el-table-column>
            <el-table-column
              align="center"
              label="实付"
              prop="price"
            ></el-table-column>
          </el-table>
        </div>
      </div>
      <div class="part">
        <div class="content">
          <el-row>
            <el-col :span="24" class="col-24 btn-wrap">
              <div class="col-btn-submit">
                <el-button
                  :loading="auditPassLoading"
                  @click="audit('PASS')"
                  type="primary"
                  >审核通过</el-button
                >
                <el-button
                  :loading="auditRejectLoading"
                  @click="audit('REJECT')"
                  type="primary"
                  >驳 回</el-button
                >
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
    </div>
  </el-form>
</template>

<script>
import { audit, getDetailById } from '@/api/activity/sample';
export default {
  components: {},
  name: 'sample-audit-detail',
  created() {
    this.initData();
  },
  activated() {
    this.id = this.$route.params.id;
  },
  data() {
    return {
      id: this.$route.params.id,
      list: [], // 商品列表
      address: '', // 地址
      auditStatus: 'PASS', // 审核状态
      auditPassLoading: false,
      auditRejectLoading: false,
      detail: {},
      form: {
        approveMsg: '' // 审批备注
      },
      loading: false,
      rules: {
        approveMsg: []
      }
    };
  },
  methods: {
    initData() {
      this.fetchData();
    },
    // 审核
    audit(status) {
      if (status === 'REJECT') {
        this.rules.approveMsg = [
          { required: true, message: '必填信息', trigger: 'blur' }
        ];
      }
      if (status === 'PASS') {
        this.rules.approveMsg = [{ required: false }];
      }
      this.$nextTick(() => {
        this.$refs['form'].validate((valid) => {
          if (!valid) {
            return false;
          }
          const listQuery = {
            id: this.$route.params.id,
            approveMsg: this.form.approveMsg,
            isPass: status === 'PASS' ? '1' : '0'
          };
          if (status === 'PASS') {
            this.auditPassLoading = true;
          } else {
            this.auditRejectLoading = true;
          }
          audit(listQuery)
            .then(() => {
              this.$message.success('操作成功');
              this.$back();
            })
            .finally(() => {
              this.auditPassLoading = false;
              this.auditRejectLoading = false;
            });
        });
      });
    },
    // 获取详情
    fetchData() {
      this.loading = true;
      return getDetailById(this.$route.params.id)
        .then((response) => {
          const {
            commodityName,
            commodityId,
            imgUrl,
            commodityPrice,
            price,
            approveMsg
          } = response.data;
          this.list = [
            { commodityId, commodityName, imgUrl, commodityPrice, price }
          ];
          this.form.approveMsg = approveMsg;
          this.detail = { ...this.detail, ...response.data };
        })
        .finally(() => {
          this.loading = false;
        });
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
@import 'src/styles/viewsStyles/distributor-management/distributor/distributor-detail';
.goods-img {
  width: 60px;
  height: 60px;
}
.form-container {
  ::v-deep .el-form-item--medium .el-form-item__label {
    line-height: 14px;
  }
  ::v-deep .el-form-item__error {
    left: 70px;
  }
}
</style>
