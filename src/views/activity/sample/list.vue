<template>
  <div class="app-container wrap">
    <div class="table-container">
      <sy-tabs v-model="activeName" v-bind="tabs" class="custom-border-tabs" />
      <keep-alive>
        <component :is="activeName" :ref="activeName"></component>
      </keep-alive>
    </div>
  </div>
</template>
<script>
import sample from '@/components/views/activity/sample/SampleList';
import detail from '@/components/views/activity/sample/DetailList';
export default {
  components: {
    sample,
    detail
  },
  name: 'activity-sample-list',
  data() {
    return {
      activeName: 'sample', // tab切换控制，假如是样品管理为sample, 申请明细为detail
      id: '',
      tabs: {
        options: [
          {
            label: '样品管理',
            value: 'sample'
          },
          {
            label: '申请明细',
            value: 'detail'
          }
        ]
      }
    };
  },
  mounted() {
    this.setTabs();
  },
  activated() {
    this.setTabs();
  },
  methods: {
    setTabs() {
      const { activeName = '' } = this.$route.query;
      if (activeName) {
        this.activeName = activeName;
      }
    }
  }
};
</script>
