<template>
  <div>
    <FormLayout @onSubmit="onSubmit" :submitLoading="submitLoading" :isDetail="isDetail">
      <el-form :model="form" :rules="rules" label-width="120px" ref="form" :disabled="isDetail" size="small">
        <el-form-item label="活动名称" prop="name">
          <el-input type="text" placeholder="请输入内容" v-model.trim="form.name" maxlength="20" show-word-limit class="form-input"></el-input>
        </el-form-item>
        <el-form-item label="活动备注" prop="remark">
          <el-input type="textarea" placeholder="请输入内容" v-model="form.remark" maxlength="20" show-word-limit class="form-input"></el-input>
        </el-form-item>
        <el-form-item label="模块类型" prop="type">
          <el-select v-model.trim="form.type" @change="changeType">
            <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="fnLabel(i)" v-for="(item, i) of cmdList" :key="i">
          <template #label>
            <span class="commo-asterisk">{{ fnLabel(i) }}</span>
          </template>
          <div>
            <el-input type="text" placeholder="请输入内容" v-model.trim="item.name" maxlength="8" show-word-limit class="form-input"></el-input>
            <el-checkbox v-model.trim="item.checked" @change="changeChecked($event, i)" style="margin-left: 10px">子级分类</el-checkbox>
            <el-button type="text" size="mini" style="margin-left: 10px" v-if="cmdList.length > 1" @click="deleteCmd(i)">删除</el-button>
          </div>
        </el-form-item>

        <el-form-item v-if="form.type === 'MULTI'">
          <!-- 多维度 才能添加榜单 -->
          <el-button type="primary" size="mini" icon="el-icon-plus" v-if="cmdList.length < 4" :disabled="isDetail" @click="addCmd">添加榜单</el-button>
          <h4 style="margin: 0; color: #999">最多4个模块</h4>
        </el-form-item>
        <div v-if="form.type">
          <h4>子级模块商品配置:</h4>
          <el-tabs v-model.trim="radio" type="border-card">
            <el-tab-pane :label="'模块' + (form.type === 'MULTI' ? i + 1 : '') + ':' + item.name" :name="i + ''" v-for="(item, i) of cmdList" :key="i">
              <!-- 有子级分类 -->
              <el-form-item label="选择活动商品" v-if="item.checked" key="fl">
                <div>{{ tipText(item) }}</div>
                <el-table :data="item.itemCmdList">
                  <el-table-column align="center" label="分类名称" prop="name">
                    <template slot-scope="scope">
                      <el-input v-model.trim="scope.row.name" size="small" maxlength="4" show-word-limit></el-input>
                    </template>
                  </el-table-column>
                  <el-table-column align="center" label="已关联商品">
                    <template slot-scope="scope">
                      <span v-if="scope.row.itemCmdList">
                        {{ scope.row.itemCmdList.length }}
                      </span>
                      <span v-else>0</span>
                      个
                    </template>
                  </el-table-column>
                  <el-table-column align="center" label="排序">
                    <template slot-scope="scope">
                      <el-button size="small" :disabled="isDetail" @click="moveUp(i, scope.$index)" v-if="scope.$index > 0" type="text" key="1">上移</el-button>
                      <el-button size="small" :disabled="isDetail" @click="shiftDown(i, scope.$index)" v-if="scope.$index < item.itemCmdList.length - 1" type="text" key="2">下移</el-button>
                    </template>
                  </el-table-column>
                  <el-table-column align="center" label="操作" fixed="right">
                    <template slot-scope="scope">
                      <div>
                        <el-button size="small" :disabled="isDetail" @click="selectGoods(i, scope.$index)" type="text" key="1"> 选择商品</el-button>
                        <el-button size="small" @click="relevanceLogic(scope.row, i, scope.$index)" type="text" v-if="scope.row.itemCmdList && scope.row.itemCmdList.length" key="2">已关联商品</el-button>
                        <el-button size="small" :disabled="isDetail" @click="deleteCategory(item.itemCmdList, i, scope.$index)" v-if="item.itemCmdList && item.itemCmdList.length > 1" key="3" type="text">删除分类</el-button>
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
                <div>
                  <el-button type="primary" v-if="item.itemCmdList.length < 10" size="mini" icon="el-icon-plus" @click="addSubtotal(i)">添加子级分类</el-button>
                </div>
              </el-form-item>
              <!-- 无子级 -->
              <el-form-item label="选择活动商品" v-else key="sp">
                <div>{{ tipText(item) }}</div>
                <el-button style="margin-bottom: 10px" size="mini" @click="selectGoods(i)" type="primary" :disabled="isDetail">添加</el-button>
                <el-table :data="item.itemCmdList">
                  <el-table-column align="center" label="商品略缩图">
                    <template slot-scope="scope">
                      <img v-if="scope.row.commodityInfo" :src="scope.row.commodityInfo.thumbnailUrl || require('@/assets/default-image2.png')" class="thumbImg" />
                      <img v-else :src="require('@/assets/default-image2.png')" alt="" class="thumbImg" />
                    </template>
                  </el-table-column>
                  <el-table-column align="center" label="商品名称" prop="name"></el-table-column>
                  <!-- <el-table-column align="center" label="商品条码" prop="specCode"></el-table-column> -->
                  <el-table-column align="center" label="商品id" prop="typeId"></el-table-column>
                  <el-table-column align="center" label="操作" prop="commodityNum">
                    <template slot-scope="scope">
                      <el-button :disabled="isDetail" size="small" @click="moveUp(i, scope.$index)" v-if="scope.$index > 0" type="text" key="1">上移</el-button>
                      <el-button :disabled="isDetail" size="small" @click="shiftDown(i, scope.$index)" v-if="scope.$index < item.itemCmdList.length - 1" type="text" key="2">下移</el-button>
                      <el-button :disabled="isDetail" size="small" @click="onTop(i, scope.$index)" v-if="scope.$index > 0" type="text" key="3">置顶</el-button>
                      <el-button :disabled="isDetail" size="small" @click="onBot(i, scope.$index)" v-if="scope.$index < item.itemCmdList.length - 1" type="text" key="4">置底</el-button>
                      <el-button :disabled="isDetail" size="small" @click="removeItem(i, scope.$index)" type="text" key="5">取消关联</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </el-form-item>
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-form>
      <ManagementGoods ref="ManagementGoods" @onSubmit="ManagementCommodity" v-bind="$props" />
      <!-- 选择商品 -->
      <selectData type="commodity" ref="select_commodity" :selectable="(row) => row.status !== '3' && row.isHidden !== '1'" :requestAxios="requestAxios" @onCommit="selectedCommodity" :maxSelectNum="20" isMultiple></selectData>
    </FormLayout>
  </div>
</template>

<script>
import FormLayout from '@/components/FormLayout';
import dict from '@/components/Common/dicts';
import { hotCommodityActivityGet, hotCommodityActivityUpdate, hotCommodityActivityCreate, zgCommodityListPageByActivity } from '@/api/activity/activity-hotcommodity.js';
import SelectData from '@/components/SelectData';
import ManagementGoods from './ManagementGoods';
import { list as pageList } from '@/api/commodity/list.js';
import cloneDeep from 'lodash/cloneDeep'; // 对象深拷贝

export default {
  name: 'hotcommodityForm',
  components: { FormLayout, ManagementGoods, SelectData },
  data() {
    return {
      submitLoading: false,
      presentIndexObj: {}, // 当前选中的分类对象
      commodityList: [],
      radio: '0',
      cmdList: [],
      typeOptions: [],
      form: {
        name: '',
        remark: '',
        type: ''
      },
      list: [], // 无子级
      classifyList: [], // 分类列表
      rules: {
        name: [
          { required: true, message: '请输入活动名称', trigger: 'blur' },
          { max: 30, message: '名称长度不能超过30', trigger: 'blur' }
        ],
        type: [{ required: true, message: '请选择活动类型', trigger: 'blur' }]
      }
    };
  },
  watch: {
    radio(v) {
      this.zgCommodityListPageByActivity(v);
    }
  },
  props: {
    isDetail: Boolean,
    isEdit: Boolean,
    id: { type: String, default: '' }
  },
  created() {
    dict('COMMON_HOT_COMMODITY_ACTIVTY_TYPE').then((res) => {
      this.typeOptions = res;
    });
    this.hotCommodityActivityGet();
  },
  mounted() {},
  computed: {},
  methods: {
    requestAxios: pageList,
    fnLabel(i) {
      const { type } = this.form;
      if (type === 'MULTI') {
        return '一级模块名称' + (i + 1);
      } else {
        return '一级模块名称';
      }
    },
    zgCommodityListPageByActivity(v = this.radio) {
      // 获取商品数据
      const i = parseInt(v);
      const cmd = this.cmdList[i];
      const { checked, itemCmdList = [] } = cmd;
      if (checked || itemCmdList.length === 0) return;
      const ids = cmd.itemCmdList.map((i) => i.typeId);
      const listQuery = {
        pageNo: 1,
        pageSize: 20,
        data: { commodityIds: ids }
      };
      zgCommodityListPageByActivity(listQuery).then((res) => {
        const newList = itemCmdList.map((item) => {
          const fd = res.data?.list.find((i) => i.id === item.typeId);
          return {
            ...fd,
            ...item,
            type: 'COMMODITY'
          };
        });
        this.$set(this.cmdList, [i], { ...cmd, itemCmdList: newList });
      });
    },
    changeType() {
      this.cmdList = [];
      this.addCmd();
    },
    tipText(item) {
      if (item.checked) {
        // 有子级
        return '最多添加10个分类，每个分类最多关联20个商品';
      } else {
        // 无子级
        return '最多关联20个商品';
      }
    },
    hotCommodityActivityGet() {
      if (!this.id) return;
      hotCommodityActivityGet(this.id).then((res) => {
        const { name, remark, type, itemCmdList = [] } = res.data || {};
        this.form.name = name;
        this.form.remark = remark;
        this.form.type = type;
        this.cmdList = itemCmdList.map((item) => {
          const { itemCmdList = [] } = item || {};
          return {
            ...item,
            itemCmdList: itemCmdList.map((itemMM) => {
              return {
                ...itemMM,
                itemCmdList: itemMM.itemCmdList || []
              };
            })
          };
        });
      });
    },
    // 添加榜单
    addCmd() {
      const obj = {
        name: '',
        type: 'BOARD',
        checked: false,
        itemCmdList: []
      };
      this.cmdList.push(obj);
    },
    // 删除榜单
    deleteCmd(index) {
      this.$confirm('确认是否删除该模块？')
        .then(() => {
          this.cmdList.splice(index, 1);
        })
        .catch(() => {
          this.$message.info('取消操作！');
        });
    },
    // 初始化榜单/修改榜单信息
    initCmd(i = 0, obj = {}) {
      const fl = { checked: true, itemCmdList: [], name: '', type: 'BOARD', ...obj };
      this.$set(this.cmdList, [i], fl);
      if (this.cmdList[i].itemCmdList.length > 0 || !this.cmdList[i].checked) return;
      this.addSubtotal(i);
    },
    // 添加子级分类
    addSubtotal(i = 0) {
      const cmdObj = this.cmdList[i];
      const fl = { checked: true, itemCmdList: [], name: '', type: 'CATEGORY' };
      cmdObj.itemCmdList.push(fl);
      this.$set(this.cmdList, [i], cmdObj);
    },
    // 添加商品
    selectGoods(i = 0, $index) {
      this.presentIndexObj = { i, $index }; // 当前点击的分类对象
      const list = this.getItemCmdList(i, $index);
      this.$refs.select_commodity.open(this.fnCommodityData(list));
    },
    // 处理商品数据
    fnCommodityData(list) {
      return list.map((item) => ({ ...item, id: item.typeId }));
    },
    // 获取子级数据
    getItemCmdList(i, $index) {
      const itemCmdList = this.cmdList[i].itemCmdList || [];
      let list = [];
      if ($index === undefined) {
        // 无子级
        list = itemCmdList;
      } else {
        // 有子级
        list = itemCmdList[$index].itemCmdList;
      }
      return list;
    },
    // 商品选择完毕
    selectedCommodity(rows) {
      const { i, $index } = this.presentIndexObj;
      const obj = this.cmdList[i];
      const list = rows.map((item) => {
        if (item.typeId) return item;
        return {
          commodityInfo: item,
          checked: false,
          name: item.name,
          typeId: item.id,
          type: 'COMMODITY'
        };
      });
      if ($index === undefined) {
        // 无子级
        obj.itemCmdList = list;
        this.zgCommodityListPageByActivity();
      } else {
        // 有子级
        obj.itemCmdList[$index].itemCmdList = list;
        this.initCmd(i, obj);
      }
    },
    // 已关联商品
    relevanceLogic(row, i, $index) {
      const { itemCmdList = [], name } = row;
      this.presentIndexObj = { i, $index }; // 当前点击的分类对象
      const commodityIds = itemCmdList.map((i) => i.typeId);
      this.$refs.ManagementGoods.open({ commodityIds, moduleName: name });
    },
    // 取消已关联商品
    ManagementCommodity(ids) {
      const { i, $index } = this.presentIndexObj;
      const obj = this.cmdList[i];
      const itemCmdList = ids.map((id) => {
        const item = obj.itemCmdList[$index].itemCmdList.find((item) => id === item.typeId);
        return item;
      });
      obj.itemCmdList[$index].itemCmdList = itemCmdList;
      this.initCmd(i, obj);
    },
    // 监听子级分类 初始话数据
    changeChecked(bool, i) {
      const { name } = this.cmdList[i];
      const obj = {
        checked: bool,
        name
      };
      this.initCmd(i, obj);
    },
    // 删除子级分类
    deleteCategory(list, i, $index) {
      const cmdObj = this.cmdList[i];
      list.splice($index, 1);
      this.$set(this.cmdList, [i], { ...cmdObj, itemCmdList: list });
    },
    // 上移
    moveUp(i, $index) {
      this.changeSort(i, $index, 0);
    },
    // 下移
    shiftDown(i, $index) {
      this.changeSort(i, $index, 1);
    },
    // 排序  type 为0 上移， 1 为下移
    changeSort(i, index, type) {
      const obj = this.cmdList[i];
      const eventList = cloneDeep(obj.itemCmdList);
      const fItem = eventList[type ? index : index - 1];
      const data = eventList.splice(type ? index + 1 : index, 1, fItem);
      eventList.splice(type ? index : index - 1, 1, ...data);
      obj.itemCmdList = eventList;
      this.$set(this.cmdList, [i], obj);
    },
    // 置顶
    onTop(i, index) {
      const obj = this.cmdList[i];
      const eventList = cloneDeep(obj.itemCmdList);
      const fItem = eventList[index];
      eventList.splice(index, 1);
      eventList.unshift(fItem);
      obj.itemCmdList = eventList;
      this.$set(this.cmdList, [i], obj);
    },
    // 置底
    onBot(i, index) {
      const obj = this.cmdList[i];
      const eventList = cloneDeep(obj.itemCmdList);
      const fItem = eventList[index];
      eventList.splice(index, 1);
      eventList.push(fItem);
      obj.itemCmdList = eventList;
      this.$set(this.cmdList, [i], obj);
    },
    // 删除
    removeItem(i, index) {
      this.$confirm('确认是否取消关联？')
        .then(() => {
          const obj = this.cmdList[i];
          const eventList = cloneDeep(obj.itemCmdList);
          eventList.splice(index, 1);
          obj.itemCmdList = eventList;
          this.$set(this.cmdList, [i], obj);
        })
        .catch(() => {
          this.$message.info('取消操作！');
        });
    },
    // 保存
    onSubmit() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          if (this.isEdit) {
            // 修改
            this.hotCommodityActivityUpdate();
          } else {
            // 新增
            this.hotCommodityActivityCreate();
          }
        }
      });
    },
    hotCommodityActivityCreate() {
      this.submitLoading = true;
      const getParams = {
        ...this.form,
        itemCmdList: this.cmdList
      };
      if (this.form.type === 'SINGLE') {
        const [obj = {}] = this.cmdList;
        getParams.hasSubLevel = obj.checked ? '1' : '0';
      }
      if (this.verify()) {
        this.submitLoading = false;
        return;
      }
      hotCommodityActivityCreate(getParams)
        .then((res) => {
          this.$message({
            type: 'success',
            message: '操作成功!'
          });
          this.$back({ path: '/activity/activity-hotcommodity/list' });
        })
        .finally(() => {
          this.submitLoading = false;
        });
    },
    // 修改
    hotCommodityActivityUpdate() {
      this.submitLoading = true;
      const getParams = {
        ...this.form,
        id: this.id,
        itemCmdList: this.cmdList
      };

      if (this.form.type === 'SINGLE') {
        const [obj = {}] = this.cmdList;
        getParams.hasSubLevel = obj.checked ? '1' : '0';
      }
      if (this.verify()) {
        this.submitLoading = false;
        return;
      }
      hotCommodityActivityUpdate(getParams)
        .then((res) => {
          this.$message({
            type: 'success',
            message: '操作成功!'
          });
          this.$back({ path: '/activity/activity-hotcommodity/list' });
        })
        .finally(() => {
          this.submitLoading = false;
        });
    },
    // 商品校验
    verify() {
      for (const [index, item] of this.cmdList.entries()) {
        const i = index + 1;
        let flag = '';
        const { itemCmdList = [], checked, name = '' } = item;
        if (!name) {
          flag = this.$message.warning(`请填写【模块${i}】的模块名称`);
          return flag;
        }
        if (checked) {
          // 有子级
          flag = this.fsClassify(itemCmdList, name);
        } else {
          // 无子级
          if (itemCmdList.length === 0) {
            flag = this.$message.warning(`【模块${i}】的下无选择的商品`);
          }
        }

        if (flag) return flag;
      }
    },
    fsClassify(list, parentName) {
      let flag = '';
      for (const item of list) {
        const { name = '', itemCmdList } = item;

        if (!name) {
          flag = this.$message.warning(`【${parentName}】模块下有未输入名称的分类`);
          return flag;
        }
        if (itemCmdList.length === 0) {
          flag = this.$message.warning(`【${parentName}】模块下有未选择商品的分类`);
        }
        if (flag) return flag;
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.form-input {
  width: 222px;
}
.thumbImg {
  width: 50px;
  height: 50px;
}
</style>
