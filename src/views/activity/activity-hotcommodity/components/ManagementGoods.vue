<template>
  <el-dialog title="已关联商品" :visible.sync="dialogVisible" width="1200px">
    <!-- 搜索栏 -->
    <div v-if="moduleName">操作模块：{{ moduleName }}</div>
    <!-- <filter-form :options="searchFormOptions" @query="onSubmit_searchForm" ref="filterForm"></filter-form> -->
    <el-table :data="list" v-loading="listLoading" height="500px">
      <el-table-column align="center" label="商品略缩图">
        <template slot-scope="scope">
          <img :src="scope.row.thumbnailUrl || require('@/assets/default-image2.png')" class="thumbImg" />
        </template>
      </el-table-column>
      <el-table-column align="center" label="商品名称" prop="name"></el-table-column>
      <!-- <el-table-column align="center" label="商品条码" prop="specCode"></el-table-column> -->
      <el-table-column align="center" label="商品id" prop="id"></el-table-column>
      <el-table-column label="商品状态" align="center">
        <template slot-scope="scope">
          {{ scope.row.status | filtrationStatus }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="是否隐藏" prop="isHidden">
        <template slot-scope="scope">
          <p>{{ scope.row.isHidden === '1' ? '是' : '否' }}</p>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" prop="commodityNum">
        <template slot-scope="scope">
          <div><el-button size="small" :disabled="isDetail" @click="associated(scope.row)" type="text">取消关联</el-button></div>
          <div><el-button size="small" :disabled="isDetail" @click="moveUp(scope.$index)" v-if="scope.$index > 0" type="text">上移</el-button></div>
          <div><el-button size="small" :disabled="isDetail" @click="shiftDown(scope.$index)" v-if="scope.$index < list.length - 1" type="text">下移</el-button></div>
          <div><el-button size="small" :disabled="isDetail" @click="onTop(scope.$index)" type="text" v-if="scope.$index > 0">置顶</el-button></div>
        </template>
      </el-table-column>
    </el-table>
    <div slot="footer" class="dialog-footer" v-if="!isDetail">
      <el-button @click="dialogVisible = false" size="small">取 消</el-button>
      <ButtonHoc type="primary" @click="onSubmit" size="small">保存操作</ButtonHoc>
    </div>
  </el-dialog>
</template>

<script>
// import dict from '@/components/Common/dicts';
// import FilterForm from '@/components/Form/FilterForm';
// import pickBy from 'lodash/pickBy';
import { zgCommodityListPageByActivity as pageList } from '@/api/activity/activity-hotcommodity.js';
import cloneDeep from 'lodash/cloneDeep'; // 对象深拷贝
// 已关联商品
export default {
  name: 'ManagementGoods',
  components: {
    // FilterForm
  },
  data() {
    return {
      moduleName: '',
      commodityIds: [], // 商品ids
      listLoading: false,
      pageNo: 1,
      pageSize: 20,
      list: [],
      dialogVisible: false
    };
  },
  props: {
    isDetail: Boolean,
    isEdit: Boolean,
    id: { type: String, default: '' }
  },
  created() {},
  mounted() {},
  filters: {
    filtrationStatus(statusKey) {
      // 0-待上架，1-上架，2-定时上架，3-下架 ,
      const statusObj = {
        0: '待上架',
        1: '上架',
        2: '定时上架',
        3: '下架'
      };
      return statusObj[statusKey] || statusKey;
    }
  },
  computed: {
    searchFormOptions() {
      return [
        {
          component: 'input',
          label: '商品名称',
          prop: 'name'
        },
        {
          component: 'input',
          label: '商品条码',
          prop: 'specCode'
        },
        {
          component: 'input',
          label: '商品id',
          prop: 'id'
        },
        {
          component: 'select',
          label: '商品状态',
          prop: 'status',
          options: [
            {
              value: '0',
              label: '禁用'
            },
            {
              value: '1',
              label: '启用'
            }
          ]
        },
        {
          component: 'select',
          label: '是否隐藏',
          prop: 'isHidden',
          options: [
            {
              value: '0',
              label: '非隐藏商品'
            },
            {
              value: '1',
              label: '隐藏商品'
            }
          ]
        }
      ];
    }
  },
  watch: {},
  methods: {
    // 搜索
    onSubmit_searchForm() {
      this.fetchData();
    },
    // 获取列表参数
    getParams() {
      // const formData = this.$refs.filterForm.getParams();
      // const params = pickBy({ ...formData, commodityIds: this.commodityIds }, (val) => !!val);
      return { commodityIds: this.commodityIds };
    },
    fetchData() {
      this.listLoading = true;
      const listQuery = {
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        data: this.getParams()
      };
      pageList(listQuery)
        .then((response) => {
          const { list = [] } = response.data || {};
          this.list = list;
        })
        .finally(() => {
          this.listLoading = false;
        });
    },
    open(prm) {
      this.commodityIds = prm.commodityIds;
      this.moduleName = prm.moduleName;
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.fetchData();
      });
    },
    associated(row) {
      this.$confirm('确认是否取消关联？')
        .then(() => {
          const index = this.commodityIds.indexOf(row.id);
          if (index !== -1) {
            this.commodityIds.splice(index, 1);
            if (this.commodityIds.length > 0) {
              this.fetchData();
            } else {
              this.list = [];
            }
          }
        })
        .catch(() => {
          this.$message.info('取消操作！');
        });
    },
    onSubmit() {
      this.dialogVisible = false;
      this.$emit(
        'onSubmit',
        this.list.map((i) => i.id)
      );
    },
    // 上移
    moveUp($index) {
      this.changeSort($index, 0);
    },
    // 下移
    shiftDown($index) {
      this.changeSort($index, 1);
    },
    // 排序  type 为0 上移， 1 为下移
    changeSort(index, type) {
      const eventList = cloneDeep(this.list);
      const fItem = eventList[type ? index : index - 1];
      const data = eventList.splice(type ? index + 1 : index, 1, fItem);
      eventList.splice(type ? index : index - 1, 1, ...data);
      this.list = eventList;
    },
    // 置顶
    onTop(index) {
      const eventList = cloneDeep(this.list);
      const fItem = eventList[index];
      eventList.splice(index, 1);
      eventList.unshift(fItem);
      this.list = eventList;
    }
  }
};
</script>

<style lang='scss'  scoped>
.thumbImg {
  width: 50px;
  height: 50px;
}
</style>