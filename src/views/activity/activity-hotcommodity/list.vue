<!-- 热销商品 -->
<template>
  <div class="app-container">
    <div class="table-container">
      <!-- 搜索栏 -->
      <filter-form :options="searchFormOptions" @query="onSubmit_searchForm" ref="filterForm"></filter-form>
      <div style="margin-bottom: 10px">
        <router-link to="/activity/activity-hotcommodity/create">
          <Authority auth="/activity/activity-hotcommodity/create">
            <el-button class="add-btn" icon="el-icon-plus" size="small" type="primary">新增</el-button>
          </Authority>
        </router-link>
      </div>

      <el-table :data="list" element-loading-text="加载中" fit highlight-current-row ref="multipleTable" v-loading="listLoading">
        <el-table-column align="center" label="活动名称" prop="name"></el-table-column>
        <el-table-column align="center" label="活动ID" prop="id"></el-table-column>
        <el-table-column align="center" label="关联商品数" prop="commodityNum">
          <template slot-scope="{ row }">
            <el-button size="small" type="text">
              <Authority auth="/activity/activity-hotcommodity/edit">
                <router-link :to="{ path: '/activity/activity-hotcommodity/edit/' + row.id }">{{ row.commodityNum }}</router-link>
                <template #fail> {{ row.commodityNum }} </template>
              </Authority>
            </el-button>
          </template>
        </el-table-column>
        <el-table-column align="center" label="维度" prop="typeName"></el-table-column>
        <el-table-column align="center" label="创建时间" prop="createDate">
          <template slot-scope="scope">{{ scope.row.createDate | parseTime }}</template>
        </el-table-column>
        <el-table-column align="center" label="备注" prop="remark">
          <template slot-scope="{ row }"> {{ row.remark }}</template>
        </el-table-column>
        <el-table-column align="center" label="操作" fixed="right">
          <template slot-scope="{ row }">
            <div>
              <Authority auth="/activity/activity-hotcommodity/edit">
                <el-button size="small" type="text">
                  <router-link :to="{ path: '/activity/activity-hotcommodity/edit/' + row.id }">编辑</router-link>
                </el-button>
              </Authority>
              <Authority auth="/activity/activity-hotcommodity/delete">
                <el-button size="small" @click="hotCommodityActivityDelete(row)" type="text">删除</el-button>
              </Authority>
              <Authority auth="/activity/activity-hotcommodity/detail">
                <el-button @click="invalid(row)" size="small" type="text"> <router-link :to="{ path: '/activity/activity-hotcommodity/detail/' + row.id }">详情</router-link></el-button>
              </Authority>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination :current-page="pageNo" :disabled="listLoading" :page-size="pageSize" :page-sizes="[10, 20, 30, 40, 50]" :total="total" @current-change="handleCurrentChange" @size-change="handleSizeChange" background layout="total, sizes, prev, pager, next, jumper"></el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import FilterForm from '@/components/Form/FilterForm';
import pickBy from 'lodash/pickBy';
import omit from 'lodash/omit';
import { hotCommodityActivityListPage, hotCommodityActivityDelete } from '@/api/activity/activity-hotcommodity';
import dict from '@/components/Common/dicts';

export default {
  name: 'activity-hotcommodity-list',
  data() {
    return {
      list: [],
      pageNo: 1,
      pageSize: 10,
      total: 0,
      listLoading: false
    };
  },
  filters: {
    fisListStatus(v = 'df') {
      const o = {
        UNRECEIVED: '待认领',
        RECEIVE: '已认领',
        df: ''
      };
      return o[v];
    }
  },
  components: { FilterForm },

  computed: {
    searchFormOptions() {
      return [
        {
          component: 'input',
          label: '活动名称',
          prop: 'name'
        },
        {
          component: 'select',
          label: '维度',
          prop: 'type',
          options: dict('COMMON_HOT_COMMODITY_ACTIVTY_TYPE')
        },
        {
          prop: 'LogoutApplyDate',
          label: '创建时间',
          component: 'dateRange',
          type: 'daterange',
          props: ['startDate', 'endDate']
        }
      ];
    }
  },
  mounted() {
    // 页面加载 自动运行
    this.fetchData();
  },

  methods: {
    // 删除
    hotCommodityActivityDelete(row) {
      this.$confirm(`确认是否删除【${row.name}】该活动?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          hotCommodityActivityDelete(row.id).then((res) => {
            this.$message({
              type: 'success',
              message: '删除成功!'
            });
            this.fetchData();
          });
        })
        .catch(() => {});
    },
    // 有线索直接关联
    openConfirm(data) {
      this.$confirm('已存在对应线索, 是否确认关联?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.associated(data);
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
    },
    // 店铺商机-关联
    associated(data) {},
    // 转交
    transfer(row) {},
    fetchData() {
      this.listLoading = true;
      const listQuery = {
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        data: this.getParams()
      };
      hotCommodityActivityListPage(listQuery)
        .then((response) => {
          const { list = [], total = 0 } = response.data || {};
          this.list = list;
          this.total = total;
        })
        .finally(() => {
          this.listLoading = false;
        });
    },
    // 获取列表参数
    getParams() {
      const formData = this.$refs.filterForm.getParams();

      const params = pickBy(formData, (val) => !!val);
      return omit(params, ['industrySalesPctIntRange', 'industrySaleroomPctInt']);
    },
    // 搜索
    onSubmit_searchForm() {
      this.pageNo = 1;
      this.pageSize = 10;
      this.fetchData();
    },
    // 分页控制pageSize
    handleSizeChange(val) {
      this.pageSize = val;
      this.pageNo = 1;
      this.fetchData();
    },
    // 分页控制pageNo
    handleCurrentChange(val) {
      this.pageNo = val;
      this.fetchData();
    },
    // 无效
    invalid(row) {}
  }
};
</script>
<style lang='scss' scoped>
</style>
