<template>
  <div class="stock-form">
    <div class="item" v-if="isSpecVisible">
      <div class="title">商品规格</div>
      <div class="content">
        <div :key="i" class="spec-item" v-for="(spec, i) in specifications">
          <div class="spec-title">
            <span class="title">规格名：</span>
            <div class="spec-title-items">
              <span>
                <input maxlength="8" :class="{ disabled: !isSpecEditable }" :disabled="!isSpecEditable" :value="spec.name" @change="onSpecNameChange($event, spec)" type="text" />
              </span>
              <template v-if="i === 0 && commodityType === 'normal'">
                <span @click="showCommodityImg = !showCommodityImg" class="add-picture">
                  <input @click.stop type="checkbox" v-model="showCommodityImg" />
                  <span>添加规格图片</span>
                </span>
              </template>
            </div>
            <i @click="onSpecDelete(i)" class="el-icon-circle-close spec-close" v-if="isSpecEditable"></i>
          </div>
          <div class="spec-val-item">
            <span class="item-title">规格值：</span>
            <div class="spec-items">
              <span :key="j" class="spec-val" v-for="(val, j) in spec.list">
                <spec-input :disabled="isDetail" :class="verifyClassName(spec.list[j].name)" :deleteDisabled="!isSpecEditable && !!val.id" :onDelete="() => onSpecValDelete(spec, i, j)" :value="spec.list[j].name" @change="onSpecValChange($event, spec, i, j)" type="text" />
                <div :value="val">
                  <div class="spec-img" v-if="i === 0" v-show="showCommodityImg">
                    <ImageManagement :maxSize="100 * 1024" v-model="spec.list[j].showImg"></ImageManagement>
                    <div class="tip">宽高：500*500，大小：最大100k，数量：1张</div>
                  </div>
                </div>
              </span>
              <el-button type="text" @click.prevent="onAddSpecValClick(spec)" :disabled="isDetail" v-if="spec.name && commodityType === 'normal'">添加规格值</el-button>
            </div>
          </div>
        </div>
        <div class="spec-title spec-bg" v-if="commodityType === 'normal'">
          <el-button size="mini" :disabled="specifications.length > 2 || !isSpecEditable" @click.prevent="onAddSpecClick">添加规格名</el-button>
        </div>
      </div>
    </div>
    <div class="item" v-if="filterSpecs.length">
      <div class="title">规格明细</div>
      <div class="stock">
        <table border="0" class="stock-table" rules="none">
          <thead>
            <tr>
              <th :key="i" v-for="(spec, i) in filterSpecs">{{ spec.name }}</th>
              <th :key="column[columnKey]" v-for="column in columns">
                {{ column.label }}
                <el-tooltip class="item" effect="dark" placement="top" v-if="column.prop === 'minControlPrice'">
                  <div slot="content" class="header-tooltip-sort">活动大促价是用于控制分销商销售的最低售价，<br />可以用该价格来作为我们巡店价格监控的参考标准。</div>
                  <i class="el-icon-question"></i>
                </el-tooltip>
              </th>
            </tr>
          </thead>
          <tbody>
            <tr :key="i" v-for="(sku, i) in getRows(filterSpecs)">
              <td :key="j" :rowspan="spec.rowspan" v-for="(spec, j) in filterRowSpan(i)" :class="verifyClassName(sku[spec.name])">
                {{ sku[spec.name] }}
              </td>
              <td :key="column[columnKey]" v-for="column in columns" class="columns-td" :class="{ 'td-cd': column.prop === 'salesVolume' || column.prop === 'stock' }">
                <span class="asterisk" v-if="columnShow(column.label) && commodityType === 'normal'">*</span>
                <input
                  maxlength="8"
                  :disabled="isDetail || (column.label === '库存' && isUpdate && judgeSkuId(sku)) || column.label === '虚拟销量'"
                  :style="{ width: column.width }"
                  :value="getSkuValue(i, column.prop)"
                  @change="onSkuInput($event, i, column.prop)"
                  type="text"
                  v-if="column.editable"
                />
                <AddQuantity :onSubmit="column.label === '库存' ? appendStock : updateAddSalesVolume" :querryData="sku" :title="column.label === '库存' ? '追加库存' : '追加销量'" v-if="(column.label === '库存' || column.label === '虚拟销量') && isUpdate && judgeSkuId(sku)"></AddQuantity>
                <template v-if="!column.editable">
                  {{ getSkuValue(i, column.prop) || column.defaultValue }}
                </template>
                <div v-if="(isUpdate || isAdd || isCopy) && column.prop === 'specCode' && commodityType === 'normal'">
                  <el-button type="primary" size="mini" @click="openFXSelectGoodsBatch(i)">选择商品</el-button>
                </div>
              </td>
            </tr>
          </tbody>
          <tfoot v-if="commodityType === 'normal'">
            <tr>
              <td :colspan="filterSpecs.length + 4">
                <span>
                  <span class="foot-label">批量设置：</span>
                  <span v-if="batchSettingKey">
                    <input maxlength="8" style="width: 100px" @keyup.enter="onBatchSave" @keyup.esc="batchSettingKey = ''" ref="batchInput" type="text" v-model.trim="batchSettingVal" />
                    <span class="links btns">
                      <a @click="onBatchSave" class="link-type">保存</a>
                      <a @click="batchSettingKey = ''" class="link-type">取消</a>
                    </span>
                  </span>
                  <span v-else>
                    <span class="links">
                      <el-button type="text" :disabled="isDetail" :key="column[columnKey]" @click="onBatchSetClick(column.prop)" class="link-type batch-set-link" v-for="column in batchSetColumns">{{ column.label }}</el-button>
                    </span>
                  </span>
                </span>
              </td>
            </tr>
          </tfoot>
        </table>
      </div>
    </div>
    <template v-if="!specifications.length">
      <div :key="column[columnKey]" class="item" v-for="column in columns">
        <div class="title">
          <span style="color: var(--color-danger); margin-right: 4px" v-if="column.label !== '虚拟销量'">*</span>{{ column.label }}
          <el-tooltip class="item" effect="dark" placement="top" v-if="column.prop === 'minControlPrice'">
            <div slot="content" class="header-tooltip-sort">活动大促价是用于控制分销商销售的最低售价，<br />可以用该价格来作为我们巡店价格监控的参考标准。</div>
            <i class="el-icon-question"></i>
          </el-tooltip>
        </div>
        <div class="item-content">
          <el-input maxlength="8" :disabled="isDetail || (column.label === '库存' && isUpdate) || column.label === '虚拟销量' || !column.editable" v-model.trim="stockData[column.prop]"></el-input>
          <div v-if="(column.label === '库存' || column.label === '虚拟销量') && isUpdate" class="add-quantiy">
            <AddQuantity :onSubmit="column.label === '库存' ? appendStock : updateAddSalesVolume" :querryData="stockData" :title="column.label === '库存' ? '追加库存' : '追加销量'"></AddQuantity>
          </div>
          <div v-if="(isUpdate || isAdd || isCopy) && column.prop === 'specCode'" class="add-FX-selectGoods">
            <el-button type="primary" size="mini" @click="openFXSelectGoodsSingle">选择商品</el-button>
          </div>
        </div>
      </div>
    </template>
    <FXSelectGoods ref="FXSelectGoodsRadio" :brandInfo="brandInfo" @save="FXSelectGoodsSave" configType="Radio" />
    <div class="error-tip" v-if="!purchaseType && commodityType === 'normal'">请先选择贸易类型</div>
  </div>
</template>
<script>
import SpecInput from './SpecInput';
import get from 'lodash/get';
import set from 'lodash/set';
// import has from 'lodash/has';
import isEmpty from 'lodash/isEmpty';
import pickBy from 'lodash/pickBy';
import min from 'lodash/min';
import isArray from 'lodash/isArray';
import ImageManagement from '@/components/ImageManagement';
import { validateMoney2, isInteger } from '@/utils/validate';
import { appendStock, updateAddSalesVolume } from '@/api/commodity/index';
import AddQuantity from '@/components/AddQuantity';
import { getCompareList, getMap, getStockData, getSkuPickKeys, getSkuCompareKeys, getDefaultColumns } from './utils';
import FXSelectGoods from '@/components/FXSelectGoods/index.vue';
export default {
  name: 'commodity-stock',
  data() {
    const { columns } = this;
    return {
      oldFXSKUKeys: {}, // 当前要替换的sku 坐标
      specifications: [],
      data: {},
      showSaveBtn: false,
      batchSettingKey: '',
      batchSettingVal: '',
      showCommodityImg: false,
      stockData: getStockData(columns),
      skuVOlist: [],
      oldFXSKUIndex: ''
    };
  },
  activated() {
    const { query } = this.$route;
    if (this.isAdd && query.specCode) {
      // 三方订单 快速创建商品逻辑
      const { columns } = this;
      this.stockData = getStockData(columns);
    }
  },
  computed: {
    // 批量设置的列
    batchSetColumns() {
      return this.columns.filter((item) => item.batchSet);
    },
    // // 可编辑的列
    // editableColumns() {
    //   return this.columns.filter((item) => item.editable);
    // },
    filterSpecs() {
      const filterList = [];
      this.specifications.forEach((item) => {
        const { list, name, commodityId } = item;
        const fList = list.filter((item) => item && item.name);
        if (!fList.length) {
          return;
        }
        filterList.push({ name, list: fList, commodityId });
      });
      this.$emit('update:SKUNumbers', this.getRows(filterList)?.length || '0');
      return filterList;
    }
  },
  watch: {
    batchSettingKey(val) {
      if (val) {
        this.batchSettingVal = '';
      }
    },
    isSpecVisible(val) {
      if (!val) {
        this.specifications = [];
      }
    }
  },
  methods: {
    // 价格过滤 批发价（元） 需要根据commodityType和采货类型来确定显示什么价格
    filtrationPrice(item) {
      const purchaseType = this.purchaseType; // 采货类型、 采销、一件代发   { id: 'PURCHASE', name: '采销' }, { id: 'DROP_SHIPPING', name: '一件代发' }
      let key = '';
      switch (item.commodityType) {
        case 'GENERAL_TRADE':
          // 大贸
          key = purchaseType === 'PURCHASE' ? 'purchaseSupplyPriceTrade' : 'dropShippingSupplyPriceTrade';
          break;
        case 'CROSS_BORDER_TRADE':
          // 海淘
          key = purchaseType === 'PURCHASE' ? 'purchaseSupplyPriceOverseas' : 'dropShippingSupplyPriceOverseas';
          break;
        case 'GENERAL_AND_CROSS_BORDER_TRADE':
          // 大贸海淘  如果是【大贸、大贸海淘】都显示为【大贸】
          key = purchaseType === 'PURCHASE' ? 'purchaseSupplyPriceTrade' : 'dropShippingSupplyPriceTrade';
          break;
      }
      return key ? item[key] ?? '' : '';
    },
    // 添加替换选中的sku
    FXSelectGoodsSave(newSkuObj = {}) {
      const newo = {};
      if (!this.filterSpecs.length) {
        // 无规格
        ['specCode', 'sort', 'retailPrice', 'minControlPrice', 'price', 'miniOrderQuantity'].forEach((itemKey) => {
          if (itemKey === 'specCode') {
            newo[itemKey] = newSkuObj['barcode'] ?? '';
            return;
          }
          if (itemKey === 'price') {
            newo[itemKey] = this.filtrationPrice(newSkuObj) ?? '';
            return;
          }
          newo[itemKey] = newSkuObj[itemKey] ?? '';
        });
        if (this.stockData.commodityId) {
          // 编辑时
          this.stockData = { ...this.stockData, ...newo };
        } else {
          // 新增时
          this.stockData = { ...newo };
        }
        this.$emit('assignFormData', newSkuObj);
        return;
      } else {
        // 多规格
        const oldFXSKUKeys = this.oldFXSKUKeys;
        const oldsku = get(this.data, oldFXSKUKeys) || {}; // 规格值对象数据
        // 已有老数据
        if (oldsku.commodityId) {
          Object.assign(newo, { ...oldsku });
        }
        ['specCode', 'sort', 'retailPrice', 'minControlPrice', 'price', 'miniOrderQuantity'].forEach((itemKey) => {
          if (itemKey === 'specCode') {
            newo[itemKey] = newSkuObj['barcode'] ?? '';
            return;
          }
          if (itemKey === 'price') {
            newo[itemKey] = this.filtrationPrice(newSkuObj) ?? '';
            return;
          }
          newo[itemKey] = newSkuObj[itemKey] ?? '';
        });
        set(this.data, oldFXSKUKeys, newo);
        this.data = { ...this.data }; // 触发vue双向绑定
        if (this.oldFXSKUIndex === 0) {
          // 修改第一个sku 要触发覆盖商品属性
          this.$emit('assignFormData', newSkuObj);
        }
      }
    },
    // 判断商品是否存在skuid,只有存在则展示追加库存和追加销量
    judgeSkuId(querryData) {
      const obj = get(this.data, Object.values(querryData));
      return obj && obj.id;
    },
    // 选择商品 多规格
    openFXSelectGoodsBatch(i) {
      const Keys = this.getDeepKeys();
      if (!this.purchaseType) {
        this.$message.error('请先选择采货类型');
        return;
      }
      if (!this.brandInfo?.id) {
        this.$message.error('请先选择商品品牌');
        return;
      }
      this.oldFXSKUKeys = Array.isArray(Keys[i]) ? Keys[i] : [Keys[i]];
      this.oldFXSKUIndex = i;
      this.$refs.FXSelectGoodsRadio.open();
    },
    // 选择商品 单规格
    openFXSelectGoodsSingle() {
      if (!this.purchaseType) {
        this.$message.error('请先选择采货类型');
        return;
      }
      if (!this.brandInfo?.id) {
        this.$message.error('请先选择商品品牌');
        return;
      }
      this.$refs.FXSelectGoodsRadio.open();
    },
    // 追加库存
    async appendStock(value, querryData) {
      let postData = {};
      // 多规格
      if (this.filterSpecs.length) {
        const commodity = get(this.data, Object.values(querryData));
        postData = {
          commodityId: commodity.commodityId,
          stock: value,
          skuId: commodity.id
        };
        const path = Object.values(querryData);
        path.push('stock');
        const req = await appendStock(postData);
        if (req.success) {
          set(this.data, path, get(this.data, path) + value);
        } else {
          this.$message.error(req.msg);
        }
        return;
      }
      // 单规格
      postData = {
        commodityId: querryData.commodityId,
        stock: value,
        skuId: querryData.id
      };
      const req = await appendStock(postData);
      if (req.success) {
        this.stockData.stock = this.stockData.stock + value;
      } else {
        this.$message.error(req.msg);
      }
    },
    // 追加销量
    async updateAddSalesVolume(value, querryData) {
      let postData = {};
      // 多规格
      if (this.filterSpecs.length) {
        const commodity = get(this.data, Object.values(querryData));
        postData = {
          commodityId: commodity.commodityId,
          salesVolume: value,
          skuId: commodity.id
        };
        const path = Object.values(querryData);
        path.push('salesVolume');
        const req = await updateAddSalesVolume(postData);
        if (req.success) {
          set(this.data, path, get(this.data, path) + value);
        } else {
          this.$message.error(req.msg);
        }
        return;
      }
      // 单规格
      postData = {
        commodityId: querryData.commodityId,
        salesVolume: value,
        skuId: querryData.id
      };
      const req = await updateAddSalesVolume(postData);
      if (req.success) {
        this.stockData.salesVolume = this.stockData.salesVolume + value;
      } else {
        this.$message.error(req.msg);
      }
    },
    setData({ skuVOlist, specificationVOList }) {
      const specifications = specificationVOList.map(({ level, specificationItemVOList = [], ...rest }) =>
        this.getItem({
          ...rest,
          list: specificationItemVOList.map(({ sort, ...item }) => this.getItem(item))
        })
      );
      const data = {};
      this.showCommodityImg = specifications.some(({ list }) => list.some(({ showImg }) => !!showImg));
      skuVOlist.forEach((sku) => {
        const { firstLevel, secondLevel, threeLevel, ...rest } = sku;
        //  const str = [firstLevel, secondLevel, threeLevel].filter((val) => !!val).join('.');
        const str = [firstLevel, secondLevel, threeLevel].filter((val) => !!val);
        set(data, str, this.getItem(rest));
      });
      if (specifications.length) {
        this.data = data;
      } else {
        // 如果没有规格
        this.stockData = { ...this.getItem(skuVOlist[0]) };
      }
      this.specifications = specifications;
      this.skuVOlist = skuVOlist;
      // 这里的数据用于前后对比
      this._compareData = {
        skuMap: getMap(skuVOlist),
        specMap: getMap(specificationVOList)
      };
    },
    getData() {
      const createSpecificationCmdList = this.specifications.map(({ name, list, ...rest }, index) => ({
        ...rest,
        level: `${index + 1}`,
        name,
        createSpecificationItemCmd: list.map((item, j) => ({
          ...item,
          sort: j + 1
        }))
      }));
      const list = this.filterSpecs;
      const getCreateSkuCmdList = () => {
        if (!list.length) {
          // 如果没有录入规格 就启用stockData中的数据
          return [{ ...this.stockData }];
        }
        return this.getRows(list).map((item, sort) => {
          const obj = {};
          const skuList = [];
          ['firstLevel', 'secondLevel', 'threeLevel'].forEach((key, idx) => {
            if (list[idx]) {
              const { name } = list[idx];
              const value = item[name];
              obj[key] = value;
              skuList.push(value);
            }
          });
          return {
            ...obj,
            ...get(this.data, skuList),
            sort: sort + 1
          };
        });
      };
      return {
        createSpecificationCmdList,
        createSkuCmdList: getCreateSkuCmdList()
      };
    },
    getItem(item) {
      // 这个函数可以根据实际情况抹掉数据的id 应用于商品复制
      if (this.isCopy) {
        // eslint-disable-next-line no-unused-vars
        const { id, ...rest } = item;
        return rest;
      }
      return item;
    },
    getMap(list) {
      const map = {};
      list.forEach((item) => {
        const obj = {};
        Object.keys(item).forEach((key) => {
          const value = item[key];
          if (isArray(value)) {
            obj[key] = this.getMap(value);
          }
        });
        map[item.id] = {
          ...item,
          ...obj
        };
      });
      return map;
    },
    // 获取规格详情单个值
    getSkuValue(i, key) {
      //  return get(this.data, `${this.filterSpecs.map((item) => sku[item.name]).join('.')}.${key}`);   有小数会导致娶不到
      const Keys = this.getDeepKeys();
      const path = Array.isArray(Keys[i]) ? Keys[i] : [Keys[i]];
      if (!get(this.data, path)) {
        return '';
      }
      if (this.commodityType === 'yearly' && key === 'price') {
        return '-';
      }
      return get(this.data, [...path, key]) ?? '';
    },
    // 获取规格详情对象
    getSkuObj(sku) {
      //   return get(this.data, `${this.filterSpecs.map((item) => sku[item.name]).join('.')}`);
      return get(this.data, [...this.filterSpecs.map((item) => sku[item.name])]);
    },
    /**
     * 深度遍历 O(n^3)
     */
    getDeepKeys(val) {
      let keys = [];
      const filterSpecs = this.filterSpecs;
      const deep = val || filterSpecs.length;
      for (let i = 0; i < min([deep, filterSpecs.length]); i++) {
        const specs = filterSpecs[i].list;
        const res = [];
        keys.forEach((k) => {
          specs.forEach((s) => {
            // `${k}.${s.name}`   字符串不能识别.会出错
            res.push([k, s.name].flat());
          });
        });
        keys = res.length ? res : specs.map((item) => item.name);
      }
      return keys;
    },
    getRows(specifications = []) {
      function loop(list, key, deep, parentList) {
        const res = [];
        parentList.forEach((parent) => {
          list.forEach((val) => {
            res.push({
              ...parent,
              [key]: val.name
            });
          });
        });
        return res;
      }
      if (!specifications.length) {
        return [];
      }
      const [first, ...rest] = specifications;
      return [
        first.list.map((val) => ({
          [first.name]: val.name
        })),
        ...rest
      ].reduce((acc, cur, index) => {
        return loop(cur.list, cur.name, index, acc);
      });
    },
    onBatchSetClick(key) {
      this.batchSettingKey = key;
      this.$nextTick(() => {
        this.$refs.batchInput.focus();
      });
    },
    onSkuInput(e, i, key) {
      const reg1 = /^([1-9]{1}[0-9]{0,3}(\,[0-9]{3,4})*(\.[0-9]{0,2})?|[1-9]{1}\d*(\.[0-9]{0,2})?|0(\.[0-9]{0,2})?|(\.[0-9]{1,2})?)$/;
      const inputCheck = {
        specCode: (val) => {
          if (!val || !/^[a-zA-Z\d]+$/.test(val)) {
            this.$message({
              message: '商品条码仅支持英文大小写、数字',
              type: 'warning'
            });
          }
        },
        price: (val) => {
          if (!val || val === '0' || !reg1.test(val)) {
            this.$message({
              message: '批发价为大于0的整数或两位小数',
              type: 'warning'
            });
          }
        },
        retailPrice: (val) => {
          if (!val || val === '0' || !reg1.test(val)) {
            this.$message({
              message: '建议零售价为大于0的整数或两位小数',
              type: 'warning'
            });
          }
        },
        minControlPrice: (val) => {
          if (!val || val === '0' || !reg1.test(val)) {
            this.$message({
              message: '活动大促价为大于0的整数或两位小数',
              type: 'warning'
            });
          }
        },
        stock: (val) => {
          if (!isInteger(val)) {
            this.$message({
              message: '库存仅支持大于等于0的整数',
              type: 'warning'
            });
          }
        },
        miniOrderQuantity: (val) => {
          if (!isInteger(val)) {
            this.$message({
              message: '起订量仅支持大于等于0的整数',
              type: 'warning'
            });
          }
        }
      };
      inputCheck[key] && inputCheck[key](e.target.value);
      //! 这里直接set实际上并没有触发 依赖收集
      //   set(this.data, `${this.filterSpecs.map((item) => sku[item.name]).join('.')}.${key}`, e.target.value.replace(/\s+/g, ''));
      const Keys = this.getDeepKeys();
      const path = Array.isArray(Keys[i]) ? Keys[i] : [Keys[i]];
      set(this.data, [...path, key], e.target.value.replace(/\s+/g, '')); // 去空格
    },
    onShowImgDelete(spec, j) {
      const list = [...spec.list];
      const item = { ...list[j] };
      delete item.showImg;
      list.splice(j, 1, item);
      spec.list = list;
    },
    onSpecNameChange(e, spec) {
      const value = e.target.value;
      if (!value) {
        e.target.value = spec.name;
        return;
      }
      if (this.specifications.some((item) => item.name === value)) {
        // 重复命名
        this.$message.error('重复命名');
        e.target.value = spec.name;
        return;
      }
      // 规格名发生变化时 清空list
      spec.name = value;
      // spec.list = [{ name: '' }];
      // this.data = {};
    },
    onSpecDelete(index) {
      const list = [...this.specifications];
      list.splice(index, 1);
      this.specifications = list;
      this.data = {};
    },
    onSpecValDelete(spec, i, j) {
      // i 规格名下标    0 、 1、 2   最多三级
      const { list } = spec;
      const changeList = [...list];
      const key = list[j].name;
      changeList.splice(j, 1);
      spec.list = changeList;
      if (this.filterSpecs.length === 1) {
        // 一级规格名
        const obj = get(this.data, [key]); // 这个规格名原来的数据
        if (obj) {
          delete this.data[key]; // 删除原规格名
        }
      } else {
        const keys = this.getDeepKeys(i);
        keys.forEach((str) => {
          if (Array.isArray(str)) {
            // 三级规格名
            const k = [...str, key]; // odlvalue
            const obj = get(this.data, k); // 这个规格名原来的数据
            if (obj) {
              const parentObj = get(this.data, str); // 找到父级
              delete parentObj[key]; // 删除原父级数据
            }
          } else {
            // 二级规格名
            const k = [str, key]; // odlvalue
            const obj = get(this.data, k); // 这个规格名原来的数据
            if (obj) {
              const parentObj = get(this.data, str); // 找到父级
              delete parentObj[key]; // 删除原父级数据;
            }
          }
        });
      }
    },
    onSpecValChange(e, spec, i, j) {
      // i 规格名下标    0 、 1、 2   最多三级
      const value = e.target.value; // 当前值
      const odlvalue = e.target._value; // 原来的值
      const { list } = spec;
      if (!value) {
        e.target.value = list[j].name;
        return;
      }
      if (list.some((item) => item.name === value)) {
        // 重名
        this.$message.error('重复命名');
        e.target.value = list[j].name;
        return;
      }
      const key = list[j].name;
      const changeList = [...spec.list];
      changeList.splice(j, 1, { ...list[j], name: value });
      spec.list = changeList;
      if (!key) {
        return;
      }
      if (this.filterSpecs.length === 1) {
        // 一级规格名
        const obj = get(this.data, [odlvalue]); // 这个规格名原来的数据
        set(this.data, [value], obj); // 替换原规格数据
        delete this.data[odlvalue]; // 删除原规格名
      } else {
        const keys = this.getDeepKeys(i);
        keys.forEach((str) => {
          if (Array.isArray(str)) {
            // 三级规格名
            const k = [...str, odlvalue]; // odlvalue
            const newK = [...str, value];
            const obj = get(this.data, k); // 这个规格名原来的数据
            if (obj) {
              set(this.data, newK, obj);
              const parentObj = get(this.data, str); // 找到父级
              delete parentObj[odlvalue]; // 删除原父级数据
            }
          } else {
            // 二级规格名
            const k = [str, odlvalue]; // odlvalue
            const newK = [str, value];
            const obj = get(this.data, k); // 这个规格名原来的数据
            if (obj) {
              set(this.data, newK, obj);
              const parentObj = get(this.data, str); // 找到父级
              delete parentObj[odlvalue]; // 删除原父级数据;
            }
          }
        });
      }
    },
    // 递归删除对象属性
    clearValue(obj, name) {
      Object.keys(obj).forEach((key) => {
        if (typeof obj[key] === 'object') {
          if (key === name) {
            delete obj[key];
          } else {
            this.clearValue(obj[key], name);
          }
        }
      });
    },
    onAddSpecValClick(spec) {
      spec.list.push({ name: '' });
    },
    onAddSpecClick() {
      this.specifications.push({ name: '', list: [] });
    },
    onBatchSave() {
      const keys = this.getDeepKeys();
      keys.forEach((str) => {
        set(this.data, [str, this.batchSettingKey].flat(), this.batchSettingVal);
      });
      //   this.updateData();
      this.batchSettingKey = '';
    },
    // this.data 更新双向绑定
    updateData() {
      Object.keys(this.data).forEach((k) => {
        this.$set(this.data, k, this.data[k]);
      });
    },
    filterRowSpan(index) {
      let rowspan = 1;
      const list = [];
      for (let i = this.filterSpecs.length - 1; i >= 0; i--) {
        const item = this.filterSpecs[i];
        if (index % rowspan === 0) {
          list.unshift({
            ...item,
            rowspan
          });
        }
        rowspan *= item.list.length;
      }
      return list;
    },
    // 活动大促价校验
    validateVinControlPrice(val) {
      const number = Number(val);
      if (isNaN(number) || !validateMoney2(number) || number < 0.01) {
        return true;
      }
    },
    // 校验并返回数据
    validate() {
      const reg1 = /^([1-9]{1}[0-9]{0,3}(\,[0-9]{3,4})*(\.[0-9]{0,2})?|[1-9]{1}\d*(\.[0-9]{0,2})?|0(\.[0-9]{0,2})?|(\.[0-9]{1,2})?)$/;
      return new Promise((rs, rj) => {
        const data = this.getData();
        const { createSpecificationCmdList, createSkuCmdList, ...rest } = data;
        const restrictionNum = this.restrictionNum;
        let err;
        if (restrictionNum && !isInteger(restrictionNum)) {
          rj('请输入正确限购数');
          return;
        }
        for (const v of createSkuCmdList) {
          if (!v.specCode || !/^[a-zA-Z\d]+$/.test(v.specCode)) {
            err = '商品条码仅支持英文大小写、数字';
            break;
          }
          if (this.commodityType === 'normal') {
            if (!v.price || v.price === '0' || !reg1.test(v.price)) {
              err = '批发价为大于0的整数或两位小数';
              break;
            }
            if (!v.retailPrice || v.retailPrice === '0' || !reg1.test(v.retailPrice)) {
              err = '建议零售价为大于0的整数或两位小数';
              break;
            }
            if (!v.minControlPrice || v.minControlPrice === '0' || !reg1.test(v.minControlPrice)) {
              err = '活动大促价为大于0的整数或两位小数';
              break;
            }
          }
          if (!isInteger(v.stock)) {
            err = '库存仅支持大于等于0的整数';
            break;
          }
          if (!isInteger(v.miniOrderQuantity)) {
            err = '起订量仅支持大于等于0的整数';
            break;
          }
          if (parseFloat(v.price) > parseFloat(v.retailPrice)) {
            err = '批发价不能高于建议零售价';
            break;
          }
          if (v.miniOrderQuantity && restrictionNum && restrictionNum !== '0' && parseFloat(v.miniOrderQuantity) > parseFloat(restrictionNum)) {
            err = '起订量不能高于商品限购数';
            break;
          }
        }
        if (err) {
          rj(err);
          return;
        }
        // 新增 或 复制

        if (this.isAdd || !this._compareData || this.isCopy) {
          rs(data);
          return;
        }
        const { skuMap, specMap } = this._compareData;
        const specVOCmdList = getCompareList(createSpecificationCmdList, specMap, [
          {
            key: 'level'
          },
          {
            key: 'name'
          },
          {
            key: 'createSpecificationItemCmd',
            newKey: 'specificationItemVOCmd',
            dataKey: 'specificationItemVOList',
            compareKeys: [
              {
                key: 'name'
              },
              {
                key: 'showImg'
              },
              {
                key: 'sort'
              }
            ],
            resultKeys: ['addSpecItemList', 'delSpecItemIdList', 'updSpecItemList'],
            pickKeys: ['name']
          }
        ]);
        const specVOCmd = {};
        ['addSpecList', 'specIdList', 'updSpecList'].forEach((key, i) => {
          const list = specVOCmdList[i];
          if (!list.length) {
            return;
          }
          specVOCmd[key] = list;
        });
        const skuVOCmdList = getCompareList(createSkuCmdList, skuMap, getSkuCompareKeys(this.columns), getSkuPickKeys(this.columns));
        const skuVOCmd = {};
        ['addSkuList', 'delSkuIdList', 'updSkuList'].forEach((key, i) => {
          const list = skuVOCmdList[i];
          if (!list.length) {
            return;
          }
          skuVOCmd[key] = list;
        });
        rs(pickBy({ specVOCmd, skuVOCmd, ...rest }, (obj) => !isEmpty(obj)));
      });
    },
    columnShow(val) {
      const list = ['规格标识', '虚拟销量'];
      return (
        list.findIndex((i) => {
          return i === val;
        }) === -1
      );
    },
    verifyClassName(name = '') {
      if (name.indexOf('请修改') >= 0) {
        return 'verify-tip';
      }
      return '';
    }
  },
  components: {
    'spec-input': SpecInput,
    ImageManagement,
    AddQuantity,
    FXSelectGoods
  },
  props: {
    commodityType: {
      type: String,
      default: 'normal'
    },
    id: null,
    // 商品的限购数
    restrictionNum: '',
    SKUNumbers: [String, Number], // 已选SKU的数量
    value: null,
    // 采货类型
    purchaseType: {
      type: String,
      default: ''
    },
    // 商品品牌信息
    brandInfo: {
      type: Object,
      default: () => {}
    },
    isCopy: Boolean,
    isAdd: Boolean,
    isDetail: Boolean,
    isUpdate: Boolean, // 是否能编辑
    // 商品规格是否展示
    isSpecVisible: Boolean,
    // 商品规格是否可变更
    isSpecEditable: {
      type: Boolean,
      default: true
    },
    /**
     * [
     *  {
     *     label: '商品条码', // 列名
     *     prop: 'specCode', // 列属性
     *     editable: true, // 是否支持编辑
     *     batchSet: true // 是否支持批量设置
     *     defaultValue: '', // 默认值 默认为''
     *     width: '100px'  // 宽度 默认100px
     *   }
     * ]
     */
    columns: {
      type: Array,
      default: getDefaultColumns
    },
    // 列标识 默认：prop
    columnKey: {
      type: String,
      default: 'prop'
    }
  }
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
@import './styles';
.error-tip {
  color: var(--color-danger);
  margin-left: 150px;
  margin-top: 10px;
  font-size: 12px;
}
.verify-tip {
  color: var(--color-danger) !important;
  ::v-deep .input {
    color: var(--color-danger) !important;
  }
}
.columns-td {
  position: relative;
  padding-left: 10px !important;
  text-align: center;
  .asterisk {
    color: var(--color-danger);
    margin-right: 4px;
    position: absolute;
    top: 50%;
    left: 2px;
    margin-top: -10px;
  }
  &.td-cd {
    input {
      margin: 5px;
    }
  }
}
.add-quantiy {
  margin-left: 15px;
  display: inline-block;
}
</style>
