<template>
  <div class="stock-form">
    <div class="item" v-if="isSpecVisible">
      <div class="title">商品规格</div>
      <div class="content">
        <div :key="i" class="spec-item" v-for="(spec, i) in specifications">
          <div class="spec-title">
            <span class="title">规格名：</span>
            <div class="spec-title-items">
              <span>
                <input :class="{ disabled: !isSpecEditable }" :disabled="!isSpecEditable" :value="spec.name" @change="onSpecNameChange($event, spec)" type="text" />
              </span>
              <template v-if="i === 0">
                <span @click="showCommodityImg = !showCommodityImg" class="add-picture">
                  <input @click.stop type="checkbox" v-model="showCommodityImg" />
                  <span>添加规格图片</span>
                </span>
              </template>
            </div>
            <i @click="onSpecDelete(i)" class="el-icon-circle-close spec-close" v-if="isSpecEditable"></i>
          </div>
          <div class="spec-val-item">
            <span class="item-title">规格值：</span>
            <div class="spec-items">
              <span :key="j" class="spec-val" v-for="(val, j) in spec.list">
                <spec-input :deleteDisabled="!isSpecEditable && !!val.id" :onDelete="() => onSpecValDelete(spec, i, j)" :value="spec.list[j].name" @change="onSpecValChange($event, spec, i, j)" type="text" />
                <div :value="val">
                  <div class="spec-img" v-if="i === 0" v-show="showCommodityImg">
                    <ImageManagement :maxSize="100 * 1024" v-model="spec.list[j].showImg"></ImageManagement>
                    <div class="tip">宽高：500*500，大小：最大100k，数量：1张</div>
                  </div>
                </div>
              </span>
              <a @click.prevent="onAddSpecValClick(spec)" class="link-type add-btn" v-if="spec.name">添加规格值</a>
            </div>
          </div>
        </div>
        <div class="spec-title spec-bg">
          <el-button :disabled="specifications.length > 2 || !isSpecEditable" @click.prevent="onAddSpecClick" size="mini">添加规格名</el-button>
        </div>
      </div>
    </div>
    <div class="item" v-if="filterSpecs.length">
      <div class="title">规格明细</div>
      <div class="stock">
        <table border="0" class="stock-table" rules="none">
          <thead>
            <tr>
              <th :key="i" v-for="(spec, i) in filterSpecs">{{ spec.name }}</th>
              <th :key="column[columnKey]" v-for="column in columns">
                {{ column.label }}
                <el-tooltip class="item" effect="dark" placement="top" v-if="column.prop === 'minControlPrice'">
                  <div slot="content" class="header-tooltip-sort">活动大促价是用于控制分销商销售的最低售价，<br />可以用该价格来作为我们巡店价格监控的参考标准。</div>
                  <i class="el-icon-question"></i>
                </el-tooltip>
              </th>
            </tr>
          </thead>
          <tbody>
            <tr :key="i" v-for="(sku, i) in getRows(filterSpecs)">
              <td :key="j" :rowspan="spec.rowspan" v-for="(spec, j) in filterRowSpan(i)">
                {{ sku[spec.name] }}
              </td>
              <td :key="column[columnKey]" v-for="column in columns">
                <span style="color: var(--color-danger); margin-right: 4px" v-if="columnShow(column.label)">*</span>
                <input :disabled="(column.label === '库存' && isUpdate && judgeSkuId(sku)) || column.label === '虚拟销量'" :style="{ width: column.width }" :value="getSkuValue(sku, column.prop)" @change="onSkuInput($event, sku, column.prop)" type="text" v-if="column.editable" />
                <AddQuantity :onSubmit="column.label === '库存' ? appendStock : updateAddSalesVolume" :querryData="sku" :title="column.label === '库存' ? '追加库存' : '追加销量'" v-if="(column.label === '库存' || column.label === '虚拟销量') && isUpdate && judgeSkuId(sku)"></AddQuantity>
                <template v-if="!column.editable">
                  {{ getSkuValue(sku, column.prop) || column.defaultValue }}
                </template>
              </td>
            </tr>
          </tbody>
          <tfoot>
            <tr>
              <td :colspan="filterSpecs.length + 4">
                <span>
                  <span class="foot-label">批量设置：</span>
                  <span v-if="batchSettingKey">
                    <input @keyup.enter="onBatchSave" @keyup.esc="batchSettingKey = ''" ref="batchInput" type="text" v-model.trim="batchSettingVal" />
                    <span class="links btns">
                      <a @click="onBatchSave" class="link-type">保存</a>
                      <a @click="batchSettingKey = ''" class="link-type">取消</a>
                    </span>
                  </span>
                  <span v-else>
                    <span class="links">
                      <a :key="column[columnKey]" @click="onBatchSetClick(column.prop)" class="link-type batch-set-link" v-for="column in batchSetColumns">{{ column.label }}</a>
                    </span>
                  </span>
                </span>
              </td>
            </tr>
          </tfoot>
        </table>
      </div>
    </div>
    <template v-if="!specifications.length">
      <div :key="column[columnKey]" class="item" v-for="column in columns">
        <div class="title">
          <span style="color: var(--color-danger); margin-right: 4px" v-if="column.label !== '虚拟销量'">*</span>{{ column.label }}
          <el-tooltip class="item" effect="dark" placement="top" v-if="column.prop === 'minControlPrice'">
            <div slot="content" class="header-tooltip-sort">活动大促价是用于控制分销商销售的最低售价，<br />可以用该价格来作为我们巡店价格监控的参考标准。</div>
            <i class="el-icon-question"></i>
          </el-tooltip>
        </div>
        <div class="item-content">
          <el-input :disabled="(column.label === '库存' && isUpdate) || column.label === '虚拟销量' || !column.editable" v-model.trim="stockData[column.prop]"></el-input>
          <AddQuantity :onSubmit="column.label === '库存' ? appendStock : updateAddSalesVolume" :querryData="stockData" :title="column.label === '库存' ? '追加库存' : '追加销量'" v-if="(column.label === '库存' || column.label === '虚拟销量') && isUpdate"></AddQuantity>
        </div>
      </div>
    </template>
  </div>
</template>
<script>
import SpecInput from './SpecInput';
import get from 'lodash/get';
import set from 'lodash/set';
import has from 'lodash/has';
import isEmpty from 'lodash/isEmpty';
import pickBy from 'lodash/pickBy';
import min from 'lodash/min';
import isArray from 'lodash/isArray';
import ImageManagement from '@/components/ImageManagement';
import { validateMoney2, isInteger } from '@/utils/validate';
import { appendStock, updateAddSalesVolume } from '@/api/commodity/index';
import AddQuantity from '@/components/AddQuantity';
import { getCompareList, getMap, getStockData, getSkuPickKeys, getSkuCompareKeys, getDefaultColumns } from './utils';
export default {
  name: 'commodity-stock',
  data() {
    const { columns } = this;
    return {
      specifications: [],
      data: {},
      showSaveBtn: false,
      batchSettingKey: '',
      batchSettingVal: '',
      showCommodityImg: false,
      stockData: getStockData(columns),
      skuVOlist: []
    };
  },
  activated() {
    if (this.$route.name === '/commodity/add') {
      const { columns } = this;
      this.stockData = getStockData(columns);
    }
  },
  computed: {
    // 批量设置的列
    batchSetColumns() {
      return this.columns.filter((item) => item.batchSet);
    },
    // // 可编辑的列
    // editableColumns() {
    //   return this.columns.filter((item) => item.editable);
    // },
    filterSpecs() {
      const filterList = [];
      this.specifications.forEach((item) => {
        const { list, name, commodityId } = item;
        const fList = list.filter((item) => item && item.name);

        if (!fList.length) {
          return;
        }
        filterList.push({ name, list: fList, commodityId });
      });
      return filterList;
    }
  },
  watch: {
    batchSettingKey(val) {
      if (val) {
        this.batchSettingVal = '';
      }
    },
    isSpecVisible(val) {
      if (!val) {
        this.specifications = [];
      }
    }
  },
  methods: {
    // 判断商品是否存在skuid,只有存在则展示追加库存和追加销量
    judgeSkuId(querryData) {
      return has(this.data, Object.values(querryData));
    },
    // 追加库存
    async appendStock(value, querryData) {
      let postData = {};
      // 多规格
      if (this.filterSpecs.length) {
        const commodity = get(this.data, Object.values(querryData));
        postData = {
          commodityId: commodity.commodityId,
          stock: value,
          skuId: commodity.id
        };
        const path = Object.values(querryData);
        path.push('stock');
        const req = await appendStock(postData);
        if (req.success) {
          set(this.data, path, get(this.data, path) + value);
        } else {
          this.$message.error(req.msg);
        }
        return;
      }
      // 单规格
      postData = {
        commodityId: querryData.commodityId,
        stock: value,
        skuId: querryData.id
      };
      const req = await appendStock(postData);
      if (req.success) {
        this.stockData.stock = this.stockData.stock + value;
      } else {
        this.$message.error(req.msg);
      }
    },
    // 追加销量
    async updateAddSalesVolume(value, querryData) {
      let postData = {};
      // 多规格
      if (this.filterSpecs.length) {
        const commodity = get(this.data, Object.values(querryData));
        postData = {
          commodityId: commodity.commodityId,
          salesVolume: value,
          skuId: commodity.id
        };
        const path = Object.values(querryData);
        path.push('salesVolume');
        const req = await updateAddSalesVolume(postData);
        if (req.success) {
          set(this.data, path, get(this.data, path) + value);
        } else {
          this.$message.error(req.msg);
        }
        return;
      }
      // 单规格
      postData = {
        commodityId: querryData.commodityId,
        salesVolume: value,
        skuId: querryData.id
      };
      const req = await updateAddSalesVolume(postData);
      if (req.success) {
        this.stockData.salesVolume = this.stockData.salesVolume + value;
      } else {
        this.$message.error(req.msg);
      }
    },
    setData({ skuVOlist, specificationVOList }) {
      const specifications = specificationVOList.map(({ level, specificationItemVOList = [], ...rest }) =>
        this.getItem({
          ...rest,
          list: specificationItemVOList.map(({ sort, ...item }) => this.getItem(item))
        })
      );
      const data = {};
      this.showCommodityImg = specifications.some(({ list }) => list.some(({ showImg }) => !!showImg));
      skuVOlist.forEach((sku) => {
        const { firstLevel, secondLevel, threeLevel, ...rest } = sku;
        const str = [firstLevel, secondLevel, threeLevel].filter((val) => !!val).join('.');
        set(data, str, this.getItem(rest));
      });
      if (specifications.length) {
        this.data = data;
      } else {
        // 如果没有规格
        this.stockData = { ...this.getItem(skuVOlist[0]) };
      }
      this.specifications = specifications;
      this.skuVOlist = skuVOlist;
      // 这里的数据用于前后对比
      this._compareData = {
        skuMap: getMap(skuVOlist),
        specMap: getMap(specificationVOList)
      };
    },
    getData() {
      const createSpecificationCmdList = this.specifications.map(({ name, list, ...rest }, index) => ({
        ...rest,
        level: `${index + 1}`,
        name,
        createSpecificationItemCmd: list.map((item, j) => ({
          ...item,
          sort: j + 1
        }))
      }));
      const list = this.filterSpecs;
      const getCreateSkuCmdList = () => {
        if (!list.length) {
          // 如果没有录入规格 就启用stockData中的数据
          return [{ ...this.stockData }];
        }
        return this.getRows(list).map((item, sort) => {
          const obj = {};
          const skuList = [];
          ['firstLevel', 'secondLevel', 'threeLevel'].forEach((key, idx) => {
            if (list[idx]) {
              const { name } = list[idx];
              const value = item[name];
              obj[key] = value;
              skuList.push(value);
            }
          });
          return {
            ...obj,
            ...get(this.data, skuList.join('.')),
            sort: sort + 1
          };
        });
      };
      return {
        createSpecificationCmdList,
        createSkuCmdList: getCreateSkuCmdList()
      };
    },
    getItem(item) {
      // 这个函数可以根据实际情况抹掉数据的id 应用于商品复制
      if (this.isCopy) {
        // eslint-disable-next-line no-unused-vars
        const { id, ...rest } = item;
        return rest;
      }
      return item;
    },
    getMap(list) {
      const map = {};
      list.forEach((item) => {
        const obj = {};
        Object.keys(item).forEach((key) => {
          const value = item[key];
          if (isArray(value)) {
            obj[key] = this.getMap(value);
          }
        });
        map[item.id] = {
          ...item,
          ...obj
        };
      });
      return map;
    },
    getSkuValue(sku, key) {
      return get(this.data, `${this.filterSpecs.map((item) => sku[item.name]).join('.')}.${key}`);
    },
    /**
     * 深度遍历 O(n^3)
     */
    getDeepKeys(val) {
      let keys = [];
      const filterSpecs = this.filterSpecs;
      const deep = val || filterSpecs.length;
      for (let i = 0; i < min([deep, filterSpecs.length]); i++) {
        const specs = filterSpecs[i].list;
        const res = [];
        keys.forEach((k) => {
          specs.forEach((s) => {
            res.push(`${k}.${s.name}`);
          });
        });
        keys = res.length ? res : specs.map((item) => item.name);
      }
      return keys;
    },
    getRows(specifications = []) {
      function loop(list, key, deep, parentList) {
        const res = [];
        parentList.forEach((parent) => {
          list.forEach((val) => {
            res.push({
              ...parent,
              [key]: val.name
            });
          });
        });
        return res;
      }
      if (!specifications.length) {
        return [];
      }
      const [first, ...rest] = specifications;
      return [
        first.list.map((val) => ({
          [first.name]: val.name
        })),
        ...rest
      ].reduce((acc, cur, index) => {
        return loop(cur.list, cur.name, index, acc);
      });
    },
    onBatchSetClick(key) {
      this.batchSettingKey = key;
      this.$nextTick(() => {
        this.$refs.batchInput.focus();
      });
    },
    onSkuInput(e, sku, key) {
      const reg1 = /^([1-9]{1}[0-9]{0,3}(\,[0-9]{3,4})*(\.[0-9]{0,2})?|[1-9]{1}\d*(\.[0-9]{0,2})?|0(\.[0-9]{0,2})?|(\.[0-9]{1,2})?)$/;
      const inputCheck = {
        specCode: (val) => {
          if (!val || !/^[a-zA-Z\d]+$/.test(val)) {
            this.$message({
              message: '规格编码仅支持英文大小写、数字',
              type: 'warning'
            });
          }
        },
        price: (val) => {
          if (!val || val === '0' || !reg1.test(val)) {
            this.$message({
              message: '批发价为大于0的整数或两位小数',
              type: 'warning'
            });
          }
        },
        retailPrice: (val) => {
          if (!val || val === '0' || !reg1.test(val)) {
            this.$message({
              message: '建议零售价为大于0的整数或两位小数',
              type: 'warning'
            });
          }
        },
        minControlPrice: (val) => {
          if (!val || val === '0' || !reg1.test(val)) {
            this.$message({
              message: '活动大促价为大于0的整数或两位小数',
              type: 'warning'
            });
          }
        },
        stock: (val) => {
          if (!isInteger(val)) {
            this.$message({
              message: '库存仅支持大于等于0的整数',
              type: 'warning'
            });
          }
        },
        miniOrderQuantity: (val) => {
          if (!isInteger(val)) {
            this.$message({
              message: '起订量仅支持大于等于0的整数',
              type: 'warning'
            });
          }
        }
      };
      inputCheck[key] && inputCheck[key](e.target.value);
      //! 这里直接set实际上并没有触发 依赖收集
      set(
        this.data,
        `${this.filterSpecs.map((item) => sku[item.name]).join('.')}.${key}`,
        e.target.value.replace(/\s+/g, '') // 去空格
      );
    },
    onShowImgDelete(spec, j) {
      const list = [...spec.list];
      const item = { ...list[j] };
      delete item.showImg;
      list.splice(j, 1, item);
      spec.list = list;
    },
    onSpecNameChange(e, spec) {
      const value = e.target.value;
      if (!value) {
        e.target.value = spec.name;
        return;
      }
      if (this.specifications.some((item) => item.name === value)) {
        // 重复命名
        this.$message.error('重复命名');
        e.target.value = spec.name;
        return;
      }
      // 规格名发生变化时 清空list
      spec.name = value;
      spec.list = [{ name: '' }];
      this.data = {};
    },
    onSpecDelete(index) {
      const list = [...this.specifications];
      list.splice(index, 1);
      this.specifications = list;
      this.data = {};
    },
    onSpecValDelete(spec, i, j) {
      const { list } = spec;
      const changeList = [...list];
      const key = list[j].name;
      changeList.splice(j, 1);
      spec.list = changeList;
      const keys = this.getDeepKeys(i);
      keys.forEach((str) => {
        const obj = get(this.data, str);
        if (obj) {
          delete obj[key];
        }
      });
    },
    onSpecValChange(e, spec, i, j) {
      const value = e.target.value;
      const { list } = spec;
      if (!value) {
        e.target.value = list[j].name;
        return;
      }
      if (list.some((item) => item.name === value)) {
        // 重名
        this.$message.error('重复命名');
        e.target.value = list[j].name;
        return;
      }
      const key = list[j].name;
      const changeList = [...spec.list];
      changeList.splice(j, 1, { ...list[j], name: value });
      spec.list = changeList;
      if (!key) {
        return;
      }
      // 规格值发生变化 需要保留原有的值
      const keys = this.getDeepKeys(i);
      keys.forEach((str) => {
        const obj = get(this.data, str);
        if (obj) {
          obj[value] = obj[key];
          delete obj[key];
        }
      });
    },
    onAddSpecValClick(spec) {
      spec.list.push({ name: '' });
    },
    onAddSpecClick() {
      this.specifications.push({ name: '', list: [] });
    },
    onBatchSave() {
      const keys = this.getDeepKeys();
      keys.forEach((str) => {
        set(this.data, `${str}.${this.batchSettingKey}`, this.batchSettingVal);
      });
      this.batchSettingKey = '';
    },
    filterRowSpan(index) {
      let rowspan = 1;
      const list = [];
      for (let i = this.filterSpecs.length - 1; i >= 0; i--) {
        const item = this.filterSpecs[i];
        if (index % rowspan === 0) {
          list.unshift({
            ...item,
            rowspan
          });
        }
        rowspan *= item.list.length;
      }
      return list;
    },
    // 活动大促价校验
    validateVinControlPrice(val) {
      const number = Number(val);
      if (isNaN(number) || !validateMoney2(number) || number < 0.01) {
        return true;
      }
    },
    // 校验并返回数据
    validate() {
      const reg1 = /^([1-9]{1}[0-9]{0,3}(\,[0-9]{3,4})*(\.[0-9]{0,2})?|[1-9]{1}\d*(\.[0-9]{0,2})?|0(\.[0-9]{0,2})?|(\.[0-9]{1,2})?)$/;
      return new Promise((rs, rj) => {
        const data = this.getData();
        const { createSpecificationCmdList, createSkuCmdList, ...rest } = data;
        const restrictionNum = this.restrictionNum;
        let err;
        if (restrictionNum && !isInteger(restrictionNum)) {
          rj('请输入正确限购数');
          return;
        }
        for (const v of createSkuCmdList) {
          if (!v.specCode || !/^[a-zA-Z\d]+$/.test(v.specCode)) {
            err = '规格编码仅支持英文大小写、数字';
            break;
          }
          if (!v.price || v.price === '0' || !reg1.test(v.price)) {
            err = '批发价为大于0的整数或两位小数';
            break;
          }
          if (!v.retailPrice || v.retailPrice === '0' || !reg1.test(v.retailPrice)) {
            err = '建议零售价为大于0的整数或两位小数';
            break;
          }
          if (!v.minControlPrice || v.minControlPrice === '0' || !reg1.test(v.minControlPrice)) {
            err = '活动大促价为大于0的整数或两位小数';
            break;
          }
          if (!isInteger(v.stock)) {
            err = '库存仅支持大于等于0的整数';
            break;
          }
          if (!isInteger(v.miniOrderQuantity)) {
            err = '起订量仅支持大于等于0的整数';
            break;
          }
          if (parseFloat(v.price) > parseFloat(v.retailPrice)) {
            err = '批发价不能高于建议零售价';
            break;
          }
          if (v.miniOrderQuantity && restrictionNum && restrictionNum !== '0' && parseFloat(v.miniOrderQuantity) > parseFloat(restrictionNum)) {
            err = '起订量不能高于商品限购数';
            break;
          }
        }
        if (err) {
          rj(err);
          return;
        }
        // 新增 或 复制
        if (!this._compareData || this.isCopy) {
          rs(data);
          return;
        }
        const { skuMap, specMap } = this._compareData;
        const specVOCmdList = getCompareList(createSpecificationCmdList, specMap, [
          {
            key: 'level'
          },
          {
            key: 'name'
          },
          {
            key: 'createSpecificationItemCmd',
            newKey: 'specificationItemVOCmd',
            dataKey: 'specificationItemVOList',
            compareKeys: [
              {
                key: 'name'
              },
              {
                key: 'showImg'
              },
              {
                key: 'sort'
              }
            ],
            resultKeys: ['addSpecItemList', 'delSpecItemIdList', 'updSpecItemList'],
            pickKeys: ['name']
          }
        ]);
        const specVOCmd = {};
        ['addSpecList', 'specIdList', 'updSpecList'].forEach((key, i) => {
          const list = specVOCmdList[i];
          if (!list.length) {
            return;
          }
          specVOCmd[key] = list;
        });
        const skuVOCmdList = getCompareList(createSkuCmdList, skuMap, getSkuCompareKeys(this.columns), getSkuPickKeys(this.columns));
        const skuVOCmd = {};
        ['addSkuList', 'delSkuIdList', 'updSkuList'].forEach((key, i) => {
          const list = skuVOCmdList[i];
          if (!list.length) {
            return;
          }
          skuVOCmd[key] = list;
        });
        rs(pickBy({ specVOCmd, skuVOCmd, ...rest }, (obj) => !isEmpty(obj)));
      });
    },
    columnShow(val) {
      const list = ['规格标识', '虚拟销量'];
      return (
        list.findIndex((i) => {
          return i === val;
        }) === -1
      );
    }
  },
  components: {
    'spec-input': SpecInput,
    ImageManagement,
    AddQuantity
  },
  props: {
    // 商品的限购数
    restrictionNum: '',
    value: null,
    isCopy: Boolean,
    isUpdate: Boolean,
    // 商品规格是否展示
    isSpecVisible: Boolean,
    // 商品规格是否可变更
    isSpecEditable: {
      type: Boolean,
      default: true
    },
    /**
     * [
     *  {
     *     label: '规格编码', // 列名
     *     prop: 'specCode', // 列属性
     *     editable: true, // 是否支持编辑
     *     batchSet: true // 是否支持批量设置
     *     defaultValue: '', // 默认值 默认为''
     *     width: '100px'  // 宽度 默认100px
     *   }
     * ]
     */
    columns: {
      type: Array,
      default: getDefaultColumns
    },
    // 列标识 默认：prop
    columnKey: {
      type: String,
      default: 'prop'
    }
  }
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
@import './styles';
</style>
