@import './variables.scss';
.tip {
  color: #0D1B3F;
  font-size: 12px;
  line-height: 16px;
  padding-top: 4px;
}
.item {
  font-size: 14px;
  & + .item {
    margin-top: 16px;
  }
  .link-type {
    font-weight: 400;
  }
  & > .title {
    float: left;
    width: 150px;
    line-height: 36px;
    padding-right: 12px;
    box-sizing: border-box;
    text-align: right;
    color: var(--color-text-primary);
    font-weight: 700;
    font-size: 12px;
  }
  & > .item-content {
    margin-left: 150px;
    width: 600px;
    position: relative;
    .add-FX-selectGoods {
      position: absolute;
      right: 25px;
      top: 6px;
    }
  }
  .stock {
    margin-left: 150px;
    .stock-table {
      width: 100%;
      border: 1px solid #ebeef5;
      text-align: left;
      & > thead {
        background-color: #F0F2F6;
        th {
          font-weight: 400;
          color: $color3;
        }
      }
      & > tfoot {
        border-top: $border;
      }
      td,
      th {
        padding: 16px 10px;
      }
      td {
        @include input;
        input[type='text'] {
          width: 50%;
        }
        input[type='text'].spec-code-input {
          width: 220px;
        }
        color: $color3;
        border-top: $border;
        border-left: $border;
        // & + td {
        //   border-left: $border;
        // }
      }
      .foot-label {
        color: $color3;
      }
      .links {
        > * + * {
          margin-left: 16px;
        }
      }
      .btns {
        margin-left: 8px;
        & > * + * {
          margin-left: 2px;
        }
      }
    }
  }
  & > .content {
    border: 1px solid #E6E8EB;
    padding: 10px;
    margin-left: 150px;
    border-radius: 4px;
    @include input;
    .spec-title {
      position: relative;
      padding: 7px 10px;
      background-color: #f8f8f8;
      border-radius: 4px;
      display: flex;
      &.spec-bg {
        background-color: #f5e8e1;
      }
      .el-button {
        font-weight: normal;
      }
      .title {
        @include title;
      }
      .spec-title-items {
        flex: 1 1;
      }
      .add-picture {
        margin-left: 10px;
        color: $color2;
        cursor: pointer;
        input {
          cursor: pointer;
        }
        span {
          margin-left: 2px;
        }
      }
      .spec-close {
        @include close;
        visibility: visible;
      }
    }
    .spec-val-item {
      padding: 0 10px;
      margin-top: 10px;
      display: flex;
      .spec-items {
        flex: 1 1;
        & > * {
          display: inline-block;
          vertical-align: top;
          margin: 0 10px 10px 0;
        }
      }
      .item-title {
        @include title;
        margin-bottom: 10px;
      }
      .add-btn {
        line-height: $lineHeight;
      }
      .spec-val {
        position: relative;
        ::v-deep {
          .input-wrap input[type='text'] {
            width: 180px;
          }
        }
        .spec-img {
          position: relative;
          display: inline-block;
          margin-top: 8px;
          .spec-avatar {
            ::v-deep .el-upload {
              width: 100px;
              height: 100px;
              line-height: 100px;
            }
          }
          .img-delete {
            @include close;
          }
          &:hover .img-delete {
            visibility: visible;
          }
        }
      }
    }
  }
}
::v-deep.el-input {
  width: 80%;
}
