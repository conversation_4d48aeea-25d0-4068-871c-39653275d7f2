@mixin title {
  color: $color3;
  line-height: $lineHeight;
}
@mixin input {
  input[type='text'] {
    border: 1px solid #dcdfe6;
    padding: 6px;
    width: 100px;
    border-radius: 3px;
    outline: none;
    @include transition(border-color, box-shadow);
    &:hover {
      border-color: #c0c4cc;
    }
    &:focus {
      border: 1px solid var(--color-primary);
      box-shadow: 0 0 5px #bdf;
    }
  }
}
@mixin transition($attr...) {
  transition: $attr 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
}
@mixin close {
  position: absolute;
  right: -7px;
  color: $color4;
  top: -7px;
  font-size: 20px;
  visibility: hidden;
  cursor: pointer;
  @include transition(color);
  &:hover {
    color: $color2;
  }
}
$lineHeight: 30px;
$marginLeft: 72px;
$color1: rgba(0, 0, 0, 0.55);
$color2: rgba(0, 0, 0, 0.65);
$color3: #6E768C;
$color4: rgba(0, 0, 0, 0.25);

$border: 1px solid #ebeef5;
