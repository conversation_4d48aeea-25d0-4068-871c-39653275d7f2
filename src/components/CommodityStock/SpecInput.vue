<template>
  <div class="input-wrap">
    <input
      type="text"
      v-bind="$attrs"
      class="input"
      :class="{ disabled: !!disabled }"
      :disabled="!!disabled"
      v-bind:value="value"
      @change="$emit('change', $event)"
    />
    <i
      class="el-icon-circle-close item-close"
      @click="onDelete"
      v-if="!disabled && !deleteDisabled"
    ></i>
  </div>
</template>
<script>
export default {
  name: 'spec-input',
  inheritAttrs: false,
  props: ['onDelete', 'value', 'disabled', 'deleteDisabled']
};
</script>
<style rel="stylesheet/scss" lang="scss" scoped>
@import './variables.scss';
.input-wrap {
  position: relative;
  display: inline-block;
  @include input;
  .item-close {
    @include close;
  }
  &:hover .item-close {
    visibility: visible;
  }
}
</style>


