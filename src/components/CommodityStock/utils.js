import isArray from 'lodash/isArray';
import isEmpty from 'lodash/isEmpty';
import pick from 'lodash/pick';

export function getMap(list, primaryKey = 'id') {
  const map = {};
  list.forEach(item => {
    const obj = {};
    Object.keys(item).forEach(key => {
      const value = item[key];
      if (isArray(value)) {
        obj[key] = getMap(value);
      }
    });
    map[item[primaryKey]] = {
      ...item,
      ...obj
    };
  });
  return map;
}

export function getCompareList(list = [], compareMap, compareKeys, pickKeys) {
  const idSet = new Set(list.map(item => item.id));
  const createList = [];
  const deleteList = Object.keys(compareMap).filter(id => !idSet.has(id));
  const updateList = [];
  list.forEach(data => {
    const { id } = data;
    if (!id) {
      // 没有id代表新增
      createList.push(data);
      return;
    }
    const compareData = compareMap[id];
    if (!compareData) {
      // 有ID 但是map中没有 为垃圾数据 直接忽略 理论上不存在
      return;
    }
    const obj = {};
    compareKeys.forEach(compareKey => {
      // const value = data[key];
      const {
        key,
        newKey,
        compareKeys: keys,
        resultKeys,
        dataKey,
        pickKeys: defaultKeys
      } = compareKey;
      const value = data[key];
      if (newKey) {
        // 数组递归对比
        const result = getCompareList(
          value,
          compareData[dataKey],
          keys,
          defaultKeys
        );
        const updateData = {};
        resultKeys.forEach((str, i) => {
          const dataList = result[i];
          if (!dataList.length) {
            return;
          }
          updateData[str] = dataList;
        });
        if (isEmpty(updateData)) {
          return;
        }
        obj[newKey] = updateData;
        return;
      }
      if (value !== compareData[key]) {
        // 值对比
        obj[key] = value || '';
      }
    });
    if (isEmpty(obj)) {
      return;
    }
    updateList.push({
      ...obj,
      id,
      ...pick(data, pickKeys) // 修改默认必带的字段
    });
  });
  // 增删改
  return [createList, deleteList, updateList];
}

// 初始化stockData
export function getStockData(columns) {
  const obj = {};
  columns.forEach(column => {
    obj[column.prop] = column.defaultValue || '';
  });
  return obj;
}

// 根据columns返回sku的compareKeys，作为getCompareList方法的入参
export function getSkuCompareKeys(columns) {
  return [
    {
      key: 'sort'
    }
  ].concat(columns.map(column => ({ key: column.prop })));
}

// 根据columns返回sku的pickKeys，作为getCompareList方法的入参
export function getSkuPickKeys(columns) {
  return ['sort'].concat(columns.map(column => column.prop));
}

// 返回默认的columns
export function getDefaultColumns() {
  return [
    {
      label: '价格',
      prop: 'price',
      editable: true,
      batchSet: true
    },
    {
      label: '库存',
      prop: 'stock',
      editable: true,
      batchSet: true // 是否支持批量设置
    },
    {
      label: '虚拟销量',
      prop: 'salesVolume',
      editable: true,
      batchSet: true // 是否支持批量设置
    },
    {
      label: '规格编码', // 列名
      prop: 'specCode', // 列属性
      editable: true // 是否支持编辑
    },
    {
      label: '销量',
      prop: 'salesVolume',
      defaultValue: 0
    }
  ];
}
