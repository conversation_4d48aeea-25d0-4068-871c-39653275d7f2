.select-input.el-input {
  ::v-deep .el-input__inner {
    background-color: #fff;
    color: #606266;
  }
  ::v-deep &.is-disabled .el-input__inner {
    border-color: #dcdfe6;
    background-color: #fff;
    color: #606266;
  }
  ::v-deep .el-input-group__append {
    background-color: #fff;
    &:hover {
      background-color: #f5f7fa;
    }
  }
  ::v-deep &.disabled {
    .el-input__inner {
      background-color: #f5f7fa;
      color: #c0c4cc;
      border-color: #e4e7ed;
    }
    .el-input-group__append {
      background-color: #f5f7fa;
      .el-button {
        background-color: transparent;
        color: #c0c4cc;
      }
    }
  }
}

.el-pagination {
  text-align: right;
}
.refresh {
  margin-left: 10px;
}
.input-with-select {
  width: 450px;
  margin-left: 10px;
  ::v-deep .el-input-group__prepend {
    background-color: #fff;
  }
}

.table-container {
  min-height: 277px;
  ::v-deep tbody tr {
    cursor: pointer;
  }
}
.top-info {
  margin-bottom: 15px;
}
.pagination {
  margin-top: 16px;
  text-align: right;
}
.thumbImg {
  width: 50px;
  height: 50px;
}
