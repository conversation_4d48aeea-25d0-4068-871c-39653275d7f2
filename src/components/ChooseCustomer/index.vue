<template>
  <el-dialog
    title="选择交接人"
    :visible="dialogVisible"
    @update:visible="$emit('update:dialogVisible', $event)"
    :before-close="beforeClose"
    width="900px"
  >
    <div class="top-info">
      <el-input
        placeholder="请输入专属顾问姓名"
        v-model="initFilter.serviceName"
        class="input-with-select"
      >
        <el-button
          slot="append"
          icon="el-icon-search"
          @click="onSearch"
        ></el-button>
      </el-input>
    </div>
    <el-table
      ref="multipleTable"
      :data="list"
      v-loading.body="tableloading"
      element-loading-text="加载中"
      fit
      highlight-current-row
    >
      <el-table-column align="center" label="id" prop="id"></el-table-column>
      <el-table-column
        align="center"
        label="顾问"
        prop="createDate"
        width="180"
      >
        <template slot-scope="scope">
          {{ scope.row.name }}：{{ scope.row.mobile }}
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        label="分销商人数"
        prop="distributorNum"
      ></el-table-column>
      <el-table-column align="center" label="微信二维码" prop="qrCode">
        <template slot-scope="scope">
          <img :src="scope.row.qrCode" class="thumbImg" />
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" fixed="right" width="120px">
        <template slot-scope="scope">
          <el-button
            type="text"
            @click="chooseCustomer(scope.row.id)"
            :loading="chooseLoading"
            >选择</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <div class="pagination">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pageNo"
        :page-sizes="[10, 20, 30, 40, 50, 100]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        :disabled="tableloading"
      ></el-pagination>
    </div>
  </el-dialog>
</template>

<script>
import { list, handOver } from '@/api/distributorManagement/customer/list';
import pick from 'lodash/pick';
import pickBy from 'lodash/pickBy';
export default {
  props: {
    // 弹窗是否显示
    dialogVisible: {
      type: Boolean,
      default: false
    },
    // 父组件选中的客服id
    fromCsId: {
      type: String,
      default: ''
    }
  },
  name: 'ChooseCustomer',
  components: {},
  data() {
    const initFilter = {
      serviceName: ''
    };
    return {
      initFilter,
      list: [],
      pageNo: 1,
      pageSize: 10,
      total: 0,
      tableloading: false,
      chooseLoading: false
    };
  },
  created() {},
  computed: {
    // 过滤
    data() {
      const obj = {};
      obj.serviceName = this.initFilter.serviceName;
      return pickBy(obj, (val) => !!val);
    }
  },
  watch: {
    dialogVisible(val) {
      if (val) {
        this.fetchData();
      }
    }
  },
  methods: {
    onSearch() {
      this.pageNo = 1;
      this.pageSize = 10;
      this.fetchData();
    },
    handleSizeChange(pageSize) {
      this.pageSize = pageSize;
      this.fetchData();
    },
    handleCurrentChange(pageNo) {
      this.pageNo = pageNo;
      this.fetchData();
    },
    beforeClose() {
      this.$emit('changeVisible', false);
    },
    fetchData() {
      this.tableloading = true;
      const listQuery = pick(this, ['pageNo', 'pageSize', 'data']);
      list(listQuery)
        .then((response) => {
          const { list = [], total = 0 } = response.data || {};
          this.list = list;
          this.total = total;
        })
        .finally(() => {
          this.tableloading = false;
        });
    },
    // 选择
    chooseCustomer(id) {
      this.chooseLoading = false;
      const data = {
        fromCsId: this.fromCsId,
        toCsId: id
      };
      handOver(data)
        .then((response) => {
          if (response.success) {
            this.$message({
              type: 'success',
              message: '操作成功'
            });
          }
        })
        .finally(() => {
          this.chooseLoading = false;
          this.beforeClose();
          this.$emit('refreshList', true);
        });
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
@import './styles';
</style>
