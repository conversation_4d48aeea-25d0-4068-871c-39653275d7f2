<template>
  <el-drawer size="60%" :title="title" :visible="visible" @close="close" destroy-on-close append-to-body custom-class="common-drawer">
    <div class="drawer-header m-t-12" v-if="isDetail">
      <el-radio-group v-model="tabActive">
        <el-radio-button label="basicInfo">合同基础信息</el-radio-button>
        <el-radio-button label="operationRecord">操作记录</el-radio-button>
      </el-radio-group>
    </div>
    <div class="drawer-content">
      <!--基础信息-->
      <template v-if="tabActive === 'basicInfo'">
        <el-descriptions class="custom-descriptions m-b-24" border :column="2" :labelStyle="{ width: '14%' }" :contentStyle="{ width: '36%' }">
          <el-descriptions-item label="飞书合同编号">{{ contractInfo.externalContractNumber }}</el-descriptions-item>
          <el-descriptions-item label="合同申请人">{{ contractInfo.externalContractCreateName }}</el-descriptions-item>
          <el-descriptions-item label="合同名称">
            <div class="com-flex">
              <span class="com-flex-1">{{ contractInfo.contractName }}</span>
              <button-hoc v-if="contractInfo.externalFeiShuContractId" type="text" style="padding: 0" @click="jumpContract">查看</button-hoc>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="有效期">{{ contractInfo.startDate | parseTime('{y}-{m}-{d}') }} 至 {{ contractInfo.endDate | parseTime('{y}-{m}-{d}') }}</el-descriptions-item>
          <el-descriptions-item label="甲方">{{ contractInfo.ownCompanyName }}</el-descriptions-item>
          <el-descriptions-item v-if="contractInfo.contractParticipantList && contractInfo.contractParticipantList.length" label="乙方">
            <div>
              <p v-for="(item, index) in contractInfo.contractParticipantList" :key="index">{{ item.company }}</p>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="合同品牌" v-if="contractInfo.externalContractBrandNames">{{ contractInfo.externalContractBrandNames }}</el-descriptions-item>
          <el-descriptions-item label="是否为框架合同">{{ contractInfo.izFrameworkAgreement ? (contractInfo.izFrameworkAgreement === '1' ? '是' : '否') : '-' }}</el-descriptions-item>
        </el-descriptions>
        <el-form :model="contractForm" :rules="contractFormRules" ref="contractForm" label-position="left" label-width="120px" :disabled="isDetail">
          <div class="common-line-title m-b-18"><span class="common-line-title__text">01 完善合同信息</span></div>
          <el-form-item label="是否为年框合同" prop="izYearlyContract">
            <el-select v-model="contractForm.izYearlyContract" clearable :disabled="izYearlyContractDisabled">
              <el-option :key="index" :label="item.label" :value="item.value" v-for="(item, index) in yearlyContractOptions"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="结算方式" prop="settlementType">
            <el-select v-model="contractForm.settlementType" clearable :disabled="!isEmpty(contractInfo.settlementType)">
              <el-option :key="index" :label="item.label" :value="item.value" v-for="(item, index) in settlementTypeOptions"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="结算周期" prop="settlementPeriod" v-if="['SALE_CREDIT', 'DISTRIBUTE_SALE_CREDIT'].includes(contractForm.settlementType)">
            <el-select v-model="contractForm.settlementPeriod" clearable :disabled="!isEmpty(contractInfo.settlementPeriod)">
              <el-option :key="index" :label="item.label" :value="item.value" v-for="(item, index) in settlementPeriodOptions"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="授权渠道" prop="authChannels">
            <el-select clearable multiple v-model="contractForm.authChannels" :disabled="!isEmpty(contractInfo.authChannels)" placeholder="请选择授权渠道">
              <el-option :key="item.channel" :label="item.channelName" :value="item.channel" v-for="item in channelOptions"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="其他授权渠道" prop="otherChannelInfo" v-if="new Set(contractForm.authChannels.map((item) => item)).has('OTHER')">
            <el-select clearable multiple v-model="contractForm.otherChannelInfo" placeholder="请选择其他授权渠道">
              <el-option :key="item" :label="item" :value="item" v-for="item in otherChannelOptions"></el-option>
            </el-select>
          </el-form-item>
          <div v-for="(mainBody, index) in contractForm.contractParticipantList" :key="mainBody.id">
            <el-form-item label="关联渠道主数据" style="margin-bottom: 8px">主体：{{ mainBody.merchantTypeName }}-{{ mainBody.company }}-{{ contractInfo.platformOwnerName }}</el-form-item>
            <div v-for="(channel, cIndex) in mainBody.channelRelationList" :key="cIndex" class="channel-info-row">
              <span class="font-12" style="width: 120px"></span>
              <el-form-item label-width="auto" :prop="'contractParticipantList.' + index + '.channelRelationList.' + cIndex + '.partyRelation.omsChannelId'">
                <sy-select v-model="channel.partyRelation.omsChannelId" v-bind="channelInfoMainData(index)" class="main-data-wrap" :disabled="channel.hasYearlyPolicy === '1'" @change="changeMainData($event, index, cIndex)" />
              </el-form-item>
              <div v-if="isYearlyContract" class="m-l-12">
                <el-form-item label-width="auto" label="" :prop="'contractParticipantList.' + index + '.channelRelationList.' + cIndex + '.brandIdList'">
                  <sy-select v-model="channel.brandIdList" v-bind="{ ...channelInfoBrand, disabled: channel.hasYearlyPolicy === '1' }" />
                </el-form-item>
              </div>
              <div v-if="contractInfo.platformOwner === 'BUILD_SELF_CHANNEL'" class="m-l-12">
                <el-button type="text" @click="addChannel(index)">添加渠道</el-button>
                <el-button type="text" @click="removeChannel(index, cIndex)" v-if="cIndex > 0 && channel.hasYearlyPolicy !== '1'">删除</el-button>
              </div>
            </div>
          </div>
          <!--自建合同特有字段-->
          <template v-if="contractInfo.dataType && contractInfo.dataType !== 'ONLINE'">
            <div class="common-line-title m-b-18"><span class="common-line-title__text">02 确认合同变更信息</span></div>
            <el-form-item label="是否关联其他合同" prop="izContractRelation" v-if="contractInfo.classification === 'SALE'">
              <el-switch v-model="contractForm.izContractRelation" active-value="1" inactive-value="0" />
            </el-form-item>
            <template v-if="isContractRelation">
              <el-form-item label="关联合同ID" prop="subcontractList">
                <select-contract v-model="contractForm.subcontractList" :is-closable="isEdit" :exclude-ids="excludeContractIds" :distributor-id="distributorId"></select-contract>
              </el-form-item>
              <el-form-item prop="izTerminateCooperation">
                <template v-slot:label>
                  <span>是否为终止协议</span>
                  <el-tooltip class="m-l-4" effect="dark" placement="top">
                    <div slot="content">
                      <p class="m-b-4"><strong>确认后则不允许修改效期变更的标签；</strong></p>
                      <p class="m-b-4">1. 终止合作：是对原合同的状态变更为“已终止状态”；</p>
                      <p class="m-b-4">2. 新签订的合同是一份终止协议的合同，这份终止协议的合同也不能作为下单和开票的有效合同供业务使用；</p>
                      <p>3. 只有不是终止协议的情况下，才可以选择变更内容，是合作主体变更还是其他内容的具体变更</p>
                    </div>
                    <i class="el-icon-warning"></i>
                  </el-tooltip>
                </template>
                <el-radio-group v-model="contractForm.izTerminateCooperation" :disabled="!isEmpty(contractInfo.izTerminateCooperation)">
                  <el-radio label="0">否</el-radio>
                  <el-radio label="1">是<span class="color-info">（签订协议终止合作，原合同将变更为已失效状态，若关联年框政策将变更为“已停用”）</span> </el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="合同主体变更" prop="contractInfoChange">
                <el-radio-group v-model="contractForm.contractInfoChange" :disabled="!isEmpty(contractInfo.contractInfoChange) || isTerminateCooperation">
                  <el-radio label="CONTRACT_INFO_CHANGE">合作主体更换<span class="color-info">（适用于客户更换合作公司主体或者我方变更了合作主体，认领此合同后，该合同生效后，关联的原合同变更为失效状态）</span></el-radio>
                  <el-radio label="JOINT_PERFORMANCE" class="m-t-12">共同履约年框政策<span class="color-info">（适用于签订了补充协议，需要和原合同的主体一起履约年框政策）</span> </el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="合作内容变更">
                <el-checkbox true-label="1" false-label="0" v-model="contractForm.izAccountPeriodChange" :disabled="!isEmpty(contractInfo.izAccountPeriodChange) || isTerminateCooperation">账期变更</el-checkbox>
                <br />
                <div class="com-flex com-col-center" v-if="contractForm.izAccountPeriodChange === '1'">
                  <el-form-item label-width="auto" prop="accountPeriodChangeSaleCreditAmount">
                    <el-input v-model="contractForm.accountPeriodChangeSaleCreditAmount" :disabled="!!contractInfo.accountPeriodChangeSaleCreditAmount" placeholder="赊销额度">
                      <template slot="append">元</template>
                    </el-input>
                  </el-form-item>
                  <el-form-item label-width="auto" prop="accountPeriodChangeSettlementPeriod">
                    <el-select v-model="contractForm.accountPeriodChangeSettlementPeriod" :disabled="!isEmpty(contractInfo.accountPeriodChangeSettlementPeriod)" placeholder="请选择结算周期" class="m-l-12">
                      <el-option :key="index" :label="item.label" :value="item.value" v-for="(item, index) in settlementPeriodOptions"></el-option>
                    </el-select>
                  </el-form-item>
                </div>
                <el-checkbox true-label="1" false-label="0" v-model="contractForm.izYearlyPolicyChange" :disabled="!isEmpty(contractInfo.izYearlyPolicyChange) || isTerminateCooperation"
                  >年框政策变更<span class="color-info">（原合同关联的年框政策，将以补充协议签订为准，请及时在系统更新政策）</span></el-checkbox
                >
                <br />
                <el-checkbox true-label="1" false-label="0" v-model="contractForm.izCooperationContentOther" :disabled="!isEmpty(contractInfo.izCooperationContentOther) || isTerminateCooperation">其他<span class="color-info">（新增合作渠道或者新增合作品牌等）</span> </el-checkbox>
              </el-form-item>
            </template>
          </template>
        </el-form>
      </template>
      <!--操作记录-->
      <common-operate-record :biz-id="id" biz-type="DISTRIBUTOR_CONTRACT_CHANGE" v-if="tabActive === 'operationRecord'" />
    </div>
    <div class="drawer-footer" v-if="isEdit">
      <button-hoc type="primary" :loading="submitLoading" @click="submit">保 存</button-hoc>
      <el-button @click="close">取 消</el-button>
    </div>
  </el-drawer>
</template>
<script>
import { brandBriefListAll } from '@/api/brand/list';
import { getDistributorChannel } from '@/api/distributorManagement/distributor/list';
import { distributorContractGet, distributorContractUpdate } from '@/api/distributorManagement/statistic/list';
import SelectContract from '@/components/SelectContract';
import CommonOperateRecord from '@/components/CommonOperateRecord';
import { cloneDeep, isEmpty, merge } from 'lodash';
import { dataAssetsListChannel } from '@/api/common';
const initChannelRelationList = [{ brandIdList: [], partyRelation: { id: '', sapChannelCode: '', sapChannelName: '', omsChannelId: '', omsChannelName: '' } }];
export default {
  name: 'ContractInfo',
  components: { SelectContract, CommonOperateRecord },
  props: {
    currentRow: {
      type: Object,
      default: () => {
        return {};
      }
    },
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      channelOptions: [],
      otherChannelOptions: [],
      brandList: [],
      omsChannel: {},
      tabActive: 'basicInfo',
      contractInfo: {},
      contractForm: {
        izYearlyContract: '',
        settlementType: '',
        settlementPeriod: '',
        authChannels: [],
        otherChannelInfo: [],
        distributorContractInfoId: '',
        contractParticipantList: [{ channelRelationList: cloneDeep(initChannelRelationList) }],
        izContractRelation: '',
        subcontractList: [],
        izTerminateCooperation: '',
        contractInfoChange: '',
        izAccountPeriodChange: '',
        izYearlyPolicyChange: '',
        izCooperationContentOther: '',
        accountPeriodChangeSaleCreditAmount: '', // 账期变更-赊销额度
        accountPeriodChangeSettlementPeriod: '' // 账期变更-结算周期
      },
      contractFormRules: {
        izYearlyContract: [{ required: true, message: '必填信息', trigger: 'change' }],
        settlementType: [{ required: true, message: '必填信息', trigger: 'change' }],
        settlementPeriod: [{ required: true, message: '必填信息', trigger: 'change' }],
        distributorContractInfoId: [{ required: true, message: '必填信息', trigger: 'change' }],
        subcontractList: [{ required: true, message: '必填信息', trigger: 'change' }],
        accountPeriodChangeSaleCreditAmount: [{ required: true, message: '必填信息', trigger: 'change' }],
        accountPeriodChangeSettlementPeriod: [{ required: true, message: '必填信息', trigger: 'change' }]
      },
      izYearlyContractDisabled: false, // 禁用是否为年框合同
      submitLoading: false
    };
  },
  computed: {
    // 是否为终止协议
    isTerminateCooperation() {
      return this.contractForm.izTerminateCooperation === '1';
    },
    channelInfoBrand() {
      return {
        multiple: true,
        showEye: true,
        collapseTags: true,
        clearable: true,
        flashOptions: true,
        filterable: true,
        placeholder: '请选择年框品牌',
        options: this.brandList,
        optionsProps: {
          label: 'name',
          value: 'id'
        },
        bind: {
          'multiple-limit': this.contractInfo.platformOwner === 'BUILD_SELF_CHANNEL' ? 0 : 1
        }
      };
    },
    title() {
      return this.isEdit ? '编辑合同信息' : '查看合同信息';
    },
    // 合同id
    id() {
      return this.currentRow?.id ?? '';
    },
    // 选择合同需要排除的合同id
    excludeContractIds() {
      if (this.isEdit) {
        return [this.id];
      }
      return [];
    },
    // 分销商id
    distributorId() {
      return this.currentRow?.distributorId ?? '';
    },
    isDetail() {
      return this.currentRow?.operateType === 'detail';
    },
    isEdit() {
      return this.currentRow?.operateType === 'edit';
    },
    yearlyContractOptions() {
      return this.$dict['soyoungzg_common_whether'];
    },
    settlementTypeOptions() {
      return this.$dict['soyoungzg_distributor_type'];
    },
    settlementPeriodOptions() {
      return this.$dict['distributor_contract_settlement_period'];
    },
    isYearlyContract() {
      return this.contractForm.izYearlyContract === '1';
    },
    // 是否需要关联合同
    isContractRelation() {
      const { contractInfo, contractForm } = this;
      return contractInfo.classification === 'OTHER' || (contractInfo.classification === 'SALE' && contractForm.izContractRelation === '1');
    },
    channelInfoMainData() {
      const that = this;
      return function (index) {
        const distributorCooperationId = that.currentRow.contractParticipantList[index]?.distributorCooperationId ?? '';
        const options = distributorCooperationId ? that.omsChannel[distributorCooperationId] : [];
        return {
          filterable: true,
          clearable: true,
          flashOptions: true,
          options,
          optionsProps: {
            label: 'sapChannelName',
            value: 'omsChannelId'
          }
        };
      };
    }
  },
  created() {
    this.getContractInfo();
    this.getChannel();
    this.getBrandList();
    this.getDataAssetsListChannel();
  },
  methods: {
    isEmpty,
    // 获取合同信息
    getContractInfo() {
      const { id = '' } = this;
      if (!id) return;
      distributorContractGet(id).then((res) => {
        const data = res.data || {};
        // 数据合并
        merge(this.contractInfo, this.currentRow, data);
        this.initForm();
      });
    },
    // 初始表单数据
    initForm() {
      const {
        izContractRelation = '',
        izAccountPeriodChange = '',
        izYearlyPolicyChange = '',
        izCooperationContentOther = '',
        accountPeriodChangeSaleCreditAmount = '',
        accountPeriodChangeSettlementPeriod = '',
        izYearlyContract = '',
        settlementType = '',
        settlementPeriod = '',
        authChannels,
        otherChannelInfo,
        subcontractList = [],
        izTerminateCooperation = '',
        contractInfoChange = '',
        creditSalesAmount,
        settlementTypeValue,
        contractParticipantList = []
      } = this.contractInfo;
      // contractParticipantList数据初始化
      contractParticipantList.forEach((item) => {
        if (!item.channelRelationList || item.channelRelationList.length === 0) {
          item.channelRelationList = cloneDeep(initChannelRelationList);
        }
      });
      // 判断是否要禁用是否为年框合同
      this.izYearlyContractDisabled = izYearlyContract === '1';
      Object.assign(this.contractForm, {
        izContractRelation,
        izYearlyContract,
        settlementType,
        settlementPeriod,
        authChannels,
        otherChannelInfo,
        izAccountPeriodChange,
        izYearlyPolicyChange,
        izCooperationContentOther,
        accountPeriodChangeSaleCreditAmount,
        accountPeriodChangeSettlementPeriod,
        subcontractList: subcontractList.map((item) => {
          return { isSelected: !!item.id, id: item.relationContractId ?? '', contractName: item.relationContractName ?? '' };
        }),
        izTerminateCooperation,
        contractInfoChange,
        creditSalesAmount,
        settlementTypeValue,
        contractParticipantList
      });
    },
    // 获取品牌列表
    getBrandList() {
      brandBriefListAll().then(({ data = [] }) => {
        this.brandList = data;
      });
    },
    // 主数据渠道信息
    changeMainData(val, index, cIndex) {
      const distributorCooperationId = this.currentRow.contractParticipantList[index]?.distributorCooperationId ?? '';
      const omsChannelList = distributorCooperationId ? this.omsChannel[distributorCooperationId] : [];
      const { sapChannelCode = '', sapChannelName = '', omsChannelName = '' } = omsChannelList.find((item) => item.omsChannelId === val) || {};
      Object.assign(this.contractForm.contractParticipantList[index].channelRelationList[cIndex].partyRelation, {
        sapChannelCode,
        sapChannelName,
        omsChannelName
      });
    },
    // 获取合同主数据渠道
    getDataAssetsListChannel() {
      this.currentRow.contractParticipantList.map(({ distributorCooperationId = '' }) => {
        if (!distributorCooperationId) return;
        dataAssetsListChannel({ distributorCooperationId }).then(({ data = [] }) => {
          this.$set(this.omsChannel, `${distributorCooperationId}`, data);
        });
      });
    },
    // 获取授权渠道
    getChannel() {
      const { distributorId } = this;
      getDistributorChannel(distributorId).then((res) => {
        this.channelOptions = res?.data?.channelInfoVOList || [];
        this.otherChannelOptions = res?.data?.otherChannelInfo || [];
      });
    },
    //  跳转飞书合同
    jumpContract() {
      const { externalFeiShuContractId = '' } = this.contractInfo;
      if (!externalFeiShuContractId) return;
      const url = `https://contract.feishu.cn/management/all/detail/${externalFeiShuContractId}`;
      window.open(url, '_blank');
    },
    // 新增渠道
    addChannel(index) {
      this.contractForm.contractParticipantList[index].channelRelationList.push({
        brandIdList: [],
        partyRelation: {
          id: '',
          sapChannelCode: '',
          sapChannelName: '',
          omsChannelId: '',
          omsChannelName: ''
        }
      });
    },
    // 删除渠道
    removeChannel(index, cIndex) {
      this.contractForm.contractParticipantList[index].channelRelationList.splice(cIndex, 1);
    },
    // 关闭弹窗
    close() {
      this.$emit('update:visible', false);
    },
    // 提交
    submit() {
      this.$refs.contractForm.validate((valid) => {
        if (valid) {
          const { contractForm, id, isContractRelation } = this;
          // 如果需要关联合同，需判断是否为终止协议、合同主体变更、合作内容变更必须要选一项
          if (isContractRelation && !contractForm.izTerminateCooperation && !contractForm.contractInfoChange && !contractForm.izAccountPeriodChange && !contractForm.izYearlyPolicyChange && !contractForm.izCooperationContentOther) {
            return this.$message.error('请选择终止协议、合同主体变更、合作内容变更中的一项');
          }
          const form = cloneDeep(contractForm);
          this.submitLoading = true;
          form.contractParticipantList.forEach((item) => {
            item.channelRelationList = item.channelRelationList.map((channel) => {
              return {
                partyRelation: channel.partyRelation,
                id: channel.id || '',
                brandIdList: channel.brandIdList
              };
            });
          });
          const params = { id, ...form };
          if (isContractRelation) {
            params.subcontractList = form.subcontractList.map((item) => {
              return {
                relationContractId: item.id,
                relationContractName: item.contractName
              };
            });
          }
          distributorContractUpdate(params)
            .then((res) => {
              if (res.code === '0') {
                this.$message({
                  type: 'success',
                  message: '操作成功',
                  duration: 800,
                  onClose: () => {
                    this.$emit('success');
                    this.close();
                  }
                });
              } else {
                this.$message.error(res.msg ?? '操作失败');
              }
            })
            .finally(() => {
              this.submitLoading = false;
            });
        }
      });
    }
  }
};
</script>

<style scoped lang="scss">
.custom-descriptions {
  ::v-deep .el-descriptions-item__label.is-bordered-label {
    background: rgba(236, 238, 246, 0.2);
    color: var(--color-info);
  }
}
::v-deep {
  .el-form {
    .el-select,
    .el-input {
      width: 240px;
    }
  }
  .sy-table.el-table {
    margin: 0;
  }
  .el-descriptions-item {
    position: relative;
  }
  .el-descriptions__body {
    color: var(--color-text-secondary);
  }
  .main-data-wrap {
    width: 340px;
    .el-select,
    .el-input {
      width: 100%;
    }
  }
}
.channel-info-row {
  display: flex;
  align-items: center;
  margin-bottom: 18px;
  .el-form-item {
    margin: 0;
  }
}
</style>
