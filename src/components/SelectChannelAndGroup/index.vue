<template>
  <el-dialog :title="title" :close-on-click-modal="false" width="500px" :visible="visible" @update:visible="$emit('update:visible', $event)">
    <div>
      <el-form :rules="rules" ref="form" :model="form">
        <el-form-item label="选择团队" v-if="showGroup" prop="groupId">
          <el-select v-model="form.groupId" placeholder="请选择">
            <el-option v-for="item in groupOptions" :key="item.value" :label="item.label" :value="item.value"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="选择分销商渠道分类" v-if="isShowChannel" prop="channelType">
          <el-radio v-model="form.channelType" label="ONLINE">线上</el-radio>
          <el-radio v-model="form.channelType" label="OFFLINE">线下</el-radio>
        </el-form-item>
      </el-form>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="$emit('update:visible', false)">取 消</el-button>
      <el-button type="primary" @click="confirm()">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
import { getStaffGroupSelect } from '@/api/common/index';
export default {
  name: 'SelectChannelAndGroup',
  props: {
    title: {
      type: String,
      default: ''
    },
    visible: Boolean,
    isShowGroup: {
      type: Boolean,
      default: true
    },
    isShowChannel: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      groupOptions: [],
      form: {
        groupId: '',
        channelType: 'ONLINE'
      },
      rules: {
        groupId: [{ required: true, message: '请选择团队', trigger: 'blur' }],
        channelType: [{ required: true, message: '请选择渠道分类', trigger: 'blur' }]
      }
    };
  },
  watch: {
    visible(val) {
      if (val) {
        this.form.groupId = '';
        this.form.channelType = 'ONLINE';
        this.getStaffGroupSelect();
      }
    }
  },
  computed: {
    showGroup() {
      if (!this.isShowGroup) return false;
      const userGroup = this.$store.state.user.userGroup;
      if (!userGroup) return true;
      const findValue = this.groupOptions.find((item) => item.value === userGroup);
      if (findValue) {
        this.form.groupId = userGroup;
        return false;
      } else {
        return true;
      }
    }
  },
  methods: {
    confirm() {
      this.$refs.form.validate((valid) => {
        if (!valid) return;
        this.$emit('confirm', {
          channelType: this.form.channelType,
          groupId: this.form.groupId
        });
        this.$emit('update:visible', false);
      });
    },
    // 获取分组列表数据
    getStaffGroupSelect() {
      if (!this.isShowGroup) return;
      getStaffGroupSelect().then((res) => {
        const groupList = res.data;
        this.groupOptions = groupList
          .filter((item) => item.isShow === '1')
          .map((items) => {
            return {
              label: items.name,
              value: items.id
            };
          });
      });
    }
  }
};
</script>
