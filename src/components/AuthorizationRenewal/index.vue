<template>
  <el-dialog title="授权书续签" :visible.sync="visibe" @close="closeDialog" destroy-on-close>
    <el-form :model="renewalForm" :rules="renewalRules" ref="renewalForm" label-width="120px" label-position="left">
      <el-form-item label="到期时间" prop="expiredDate">
        <span>{{ renewalForm.expiredDate | parseTime('{y}-{m}-{d}') }}</span>
      </el-form-item>
      <el-form-item>
        <template v-slot:label>
          <span>初签合同：</span>
          <el-tooltip effect="dark" placement="top">
            <div slot="content">
              大贸授权：初签合同显示合同的信息； <br />
              跨境授权：显示合同的主体名称；
            </div>
            <i class="el-icon-question"></i>
          </el-tooltip>
        </template>
        <span v-if="resignObj.contractId">{{ resignObj.contractStatusName }}-</span>
        <span v-if="resignObj.spliceDesc">{{ resignObj.spliceDesc }}</span>
      </el-form-item>
      <el-form-item label="签署合同" prop="contractInfoMainKey" :rules="hasContract ? [{ required: true, message: '请选择签署合同', trigger: 'change' }] : []">
        <el-select v-model="renewalForm.contractInfoMainKey" style="width: 468px" @change="contractInfoMainKeyChange">
          <el-option :key="idx" :label="item.spliceDesc" :value="item.contractInfoMainKey" v-for="(item, idx) in contractForPcList"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="续签时长" prop="licenseDuration">
        <el-input placeholder="请输入数字" v-model.trim="renewalForm.licenseDuration" class="input-with-select" @input="initResignDate">
          <template v-slot:append>
            <el-select v-model="renewalForm.licenseDurationType" placeholder="请选择" class="append-input" @change="initResignDate">
              <el-option label="天" value="DAY"></el-option>
              <el-option label="月" value="MONTH"></el-option>
            </el-select>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item label="授权期限" prop="startDate">
        <el-date-picker v-model="renewalForm.startDate" type="date" placeholder="开始时间" value-format="timestamp" :picker-options="pickerOptions" @change="initResignDate"></el-date-picker>
        <span style="padding: 0 8px">至</span>
        <el-date-picker disabled v-model="renewalForm.endDate" type="date" placeholder="结束时间" value-format="timestamp"></el-date-picker>
      </el-form-item>
      <el-form-item label="承诺函" prop="commitmentLetter" :rules="hasCommitmentLetter ? [{ required: true, message: '请上传承诺函', trigger: 'change' }] : []">
        <upload ref="upload" show-file-list :accept="accept" is-private :limit="1" is-valid :max-capacity="10" :on-success="handleSuccess" :on-remove="handleRemove">
          <button-hoc size="small" type="primary">上传承诺函</button-hoc>
        </upload>
        <button-hoc type="text" @click="downloadPromiseTemplate">点击下载承诺函模板</button-hoc>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="visibe = false">取消</el-button>
      <button-hoc type="primary" :loading="sureLoading" @click="submitResign">确定</button-hoc>
    </div>
  </el-dialog>
</template>
<script>
import Upload from '@/components/Upload/index.vue';
import cloneDeep from 'lodash/cloneDeep';
import { isInteger } from '@/utils/validate';
import { preResign, resignForPC } from '@/api/brand/authorization-audit';
import moment from 'moment';
import { brandLicenseListSpecialBrand } from '@/api/brand/authorization-manager';

export default {
  name: 'AuthorizationRenewal',
  data() {
    const initRenewalForm = {
      contractId: '', // contractId
      distributorContractInfoId: '',
      contractInfoMainKey: '',
      licenseDuration: '', // 时长
      licenseDurationType: 'MONTH', // 时长类型
      expiredDate: '', // 到期时间
      startDate: new Date().getTime(), // 续签开始时间
      endDate: '', // 续签结束时间
      commitmentLetter: '' // 承诺函
    };
    const isIntegervalidate = (rule, value, callback) => {
      if (isInteger(value) && value > 0) {
        callback();
      } else {
        callback(new Error('请输入大于0的正整数'));
      }
    };
    return {
      brandId: '', // 品牌id
      id: '', // 授权书id
      specialBrandList: [],
      resignObj: {},
      accept: '.doc,.docx,.jpeg,.jpg,.png,.pdf',
      sureLoading: false, // btn loading
      visibe: false,
      initRenewalForm,
      renewalForm: cloneDeep(initRenewalForm),
      renewalRules: {
        licenseDuration: [{ required: true, validator: isIntegervalidate, trigger: 'blur' }]
      },
      pickerOptions: {
        disabledDate: this.disabledDate
      }
    };
  },
  components: { Upload },
  computed: {
    // 是否需要上传承诺函
    hasCommitmentLetter() {
      const { brandId, commitmentLetter = '' } = this.resignObj;
      const { izFrameworkAgreement = '' } = this.currentContractInfo;
      return this.specialBrandList.includes(brandId) || izFrameworkAgreement === '0' || !!commitmentLetter;
    },
    // 是否需要续签合同
    hasContract() {
      const { brandId } = this;
      return this.specialBrandList.includes(brandId) || this.resignObj.tradeType === 'GENERAL_TRADE';
    },
    // 签署合同对象
    currentContractInfo() {
      const { contractInfoMainKey = '' } = this.renewalForm;
      return this.contractForPcList.find((item) => item.contractInfoMainKey === contractInfoMainKey) || {};
    },
    // 合同下列表
    contractForPcList() {
      const { contractForPcList = [] } = this.resignObj;
      return contractForPcList.map((item) => {
        return {
          ...item,
          contractInfoMainKey: `${item.id}${item.distributorContractInfoId}`
        };
      });
    }
  },
  mounted() {
    this.getSpecialAuthorizedBrand();
  },
  methods: {
    // 获取特殊授权品牌
    getSpecialAuthorizedBrand() {
      brandLicenseListSpecialBrand().then((res) => {
        this.specialBrandList = res.data || [];
      });
    },
    // 续签数据初始化
    initRenewal({ id, brandId }) {
      preResign(id).then((res) => {
        console.log('initRenewal', res.data);
        this.brandId = brandId;
        this.id = id;
        this.resignObj = res.data;
        const { contractStatus = '', contractId = '', distributorContractInfoId = '' } = res.data;
        ['expiredDate', 'licenseDuration', 'licenseDurationType', 'spliceDesc'].forEach((item) => {
          this.renewalForm[item] = res.data[item];
        });
        if (contractStatus && contractStatus === 'USE') {
          this.renewalForm.contractInfoMainKey = `${contractId}${distributorContractInfoId}`;
          this.renewalForm.contractId = contractId;
          this.renewalForm.distributorContractInfoId = distributorContractInfoId;
        }
        this.visibe = true;
        this.initResignDate();
      });
    },
    // 合同变更
    contractInfoMainKeyChange() {
      // 已选择合同且合同生效中 + 未选择合同，默认为当前时间
      this.renewalForm.startDate = new Date().getTime();
      const { id = '', distributorContractInfoId = '', contractStatus = '', startDate } = this.currentContractInfo;
      this.renewalForm.contractId = id;
      this.renewalForm.distributorContractInfoId = distributorContractInfoId;
      if (contractStatus && contractStatus === 'NOT_START') {
        this.renewalForm.startDate = startDate;
      }
      this.initResignDate();
    },
    // 授权期限禁用时间处理
    disabledDate(time) {
      let resignStartDate = Date.now() - 24 * 60 * 60 * 1000;
      // 已选择合同且未生效
      if (this.renewalForm?.contractId) {
        const { contractStatus = '', startDate } = this.currentContractInfo;
        if (contractStatus && contractStatus === 'NOT_START') {
          resignStartDate = startDate;
        }
      }
      return time.getTime() < resignStartDate;
    },
    // 初始化续签时间
    initResignDate() {
      const date = this.renewalForm.startDate;
      if (!date) {
        this.renewalForm.endDate = '';
        return;
      }
      const { licenseDuration, licenseDurationType } = this.renewalForm;
      const type = licenseDurationType === 'DAY' ? 'd' : 'M';
      const endDate = moment(moment(date).add(licenseDuration, type)).format('x');
      this.renewalForm.endDate = Number(endDate);
    },
    // 下载承诺函模板
    downloadPromiseTemplate() {
      window.location.href = process.env.VUE_APP_MUSHUROOMFILEURL + '/static/file/soyoung-zg/template/销售承诺函模板.docx';
    },
    // 关闭弹窗重置数据
    closeDialog() {
      this.visibe = false;
      this.id = '';
      this.brandId = '';
      this.renewalForm = cloneDeep(this.initRenewalForm);
    },
    // 上传成功
    handleSuccess(res) {
      this.renewalForm.commitmentLetter = res.data || '';
      this.$refs['renewalForm'].clearValidate(['commitmentLetter']);
    },
    // 清除文件
    handleRemove() {
      this.renewalForm.commitmentLetter = '';
    },
    // 续签
    submitResign() {
      this.$refs['renewalForm'].validate((valid) => {
        if (!valid) {
          return false;
        }
        const renewalForm = { ...this.renewalForm };
        delete renewalForm.contractInfoMainKey;
        const params = {
          ...renewalForm,
          id: this.id
        };
        this.sureLoading = true;
        resignForPC(params)
          .then(() => {
            this.$message.success('操作成功');
            this.closeDialog();
            this.$emit('resign-success');
          })
          .finally(() => {
            this.sureLoading = false;
          });
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.append-input {
  width: 64px !important;
  ::v-deep .el-input {
    width: 100% !important;
  }
}
.input-with-select {
  width: 250px;
  ::v-deep .el-input-group__prepend {
    background-color: #fff;
  }
}
</style>
