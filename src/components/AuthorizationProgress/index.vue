<template>
  <div>
    <el-button type="text" @click="openProgress">查看授权进度</el-button>
    <el-dialog title="查看授权进度" :visible.sync="dialogVisible" width="1100px" :before-close="handleClose">
      <div class="content">
        <div class="top-text">
          <p>时间统计的为近35天最高的采购情况（先判断首采，再判断累计采货，最后判断保证金）</p>
        </div>
        <form @submit.prevent="onSearch" class="commo-search-container commo-search-flex commo-search-margin-bottom10">
          <div class="commo-search-item">
            <span class="commo-search-item-label">品牌名称:</span>
            <div class="commo-search-item-content">
              <SelectBrandName size="small" v-model="filter.brandId" />
            </div>
          </div>
          <div class="commo-search-item">
            <span class="commo-search-item-label">授权渠道：</span>
            <div class="commo-search-item-content">
              <SelectChannel size="small" v-model="filter.channel" />
            </div>
          </div>
          <div class="commo-search-item">
            <span class="commo-search-item-label">店铺类型：</span>
            <div class="commo-search-item-content">
              <SelectShopType size="small" v-model="filter.shopType" />
            </div>
          </div>
          <div class="commo-search-btns">
            <el-button native-type="submit" size="small" type="primary">查询</el-button>
            <el-button @click="onReset" size="small">重置</el-button>
            <ExportBtn size="small" url="/soyoungzg/api/brandLicense/exportBrandLicenseProcess" :interfaceData="interfaceData" fileName="授权进度" />
          </div>
        </form>
        <el-table :data="list" element-loading-text="加载中" fit height="400" highlight-current-row ref="multipleTable" v-loading="listLoading" class="table-box">
          <el-table-column align="center" label="品牌名称" prop="brandName"></el-table-column>
          <el-table-column align="center" label="当前是否可以申请授权" prop="isLicense">
            <template slot-scope="scope"> {{ scope.row.isLicense === '1' ? '是' : '否' }}</template>
          </el-table-column>
          <el-table-column align="center" label="可申请授权书数量">
            <template slot-scope="scope">
              <el-tooltip v-if="scope.row.orderNos && scope.row.orderNos.length > 0" class="item" effect="dark" placement="top-start">
                <span class="link">{{ scope.row.licenseNum }}</span>
                <div slot="content">
                  <div v-for="text of scope.row.orderNos" :key="text">
                    {{ text }}
                  </div>
                </div>
              </el-tooltip>
              <span v-else>{{ scope.row.licenseNum }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="离授权门槛差">
            <template slot-scope="scope">
              <div v-if="scope.row.isLicense === '0'">
                <div v-if="scope.row.isFirstPurchase">首采差：{{ scope.row.isFirstPurchase === '1' ? '已达成' : '未达成' }}</div>
                <div v-if="scope.row.isTotalPurchase">
                  <span v-if="scope.row.isTotalPurchase === '1'">累计采货：已达成</span>
                  <span v-else>累计采货差：￥{{ scope.row.totalPurchaseAmount }}</span>
                </div>
                <div v-if="scope.row.isDeposit">
                  <span v-if="scope.row.isDeposit === '1'">保证金：已达成</span>
                  <span v-else>保证金差：￥{{ scope.row.depositAmount }}</span>
                </div>
              </div>
              <div v-else>0</div>
            </template>
          </el-table-column>
          <el-table-column align="center" label="对应授权渠道和店铺类型">
            <template slot-scope="scope">
              <CommoEllipsis :key="getUniqueId('CommoEllipsis_')" :isShowView="true" :contentWidth="140" :isTextCenter="true" :lineClamp="2" :text="scope.row.channelAndShopType.join('、')">
                <div v-for="(txt, i) of scope.row.channelAndShopType" :key="i">
                  {{ txt }}
                </div>
              </CommoEllipsis>
            </template>
          </el-table-column>
          <el-table-column align="center" label="操作" fixed="right">
            <template slot-scope="scope">
              <el-button size="small" type="text" @click="linkAll(scope.row)">查看全部</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// 分销商 查看授权进度弹出框

import { listBrandLicenseProcess } from '@/api/customer-management/list';
import cloneDeep from 'lodash/cloneDeep'; // 对象深拷贝
import pickBy from 'lodash/pickBy'; // 返回一个新对象，值由真值组成
import SelectBrandName from '@/components/ListSearch/SelectBrandName';
import SelectChannel from '@/components/ListSearch/SelectChannel';
import SelectShopType from '@/components/ListSearch/SelectShopType';
import ExportBtn from '@/components/ListSearch/ExportBtn';
import CommoEllipsis from '@/components/CommoEllipsis';
import uniqueId from 'lodash/uniqueId';
export default {
  components: {
    SelectBrandName,
    SelectChannel,
    SelectShopType,
    ExportBtn,
    CommoEllipsis
  },
  data() {
    const initFilter = {
      // 搜索条件初始值
      brandId: '',
      channel: '',
      shopType: ''
    };
    return {
      listLoading: false,
      initFilter, // 搜索条件初始值加入到Data
      filter: cloneDeep(initFilter), // 搜索条件的值
      list: [
        {
          brandName: '品牌名称',
          channelAndShopType: ['淘宝-专卖店', '淘宝-专营店（有店）', '淘宝-专营店（无店）', '淘宝-专卖店', '淘宝-专营店（有店）', '淘宝-专营店（无店）'],
          depositAmount: 2,
          licenseNum: 3,
          totalPurchaseAmount: 200,
          isLicense: '1'
        }
      ], // 列表
      dialogVisible: false
    };
  },
  props: {
    rowData: {
      type: Object,
      default: () => {}
    }
  },
  created() {},
  computed: {
    // 表单接口传入的参数
    interfaceData() {
      const listQuery = pickBy(this.filter, (val) => !!val);
      listQuery.distributorId = this.rowData.id;
      return listQuery;
    }
  },
  methods: {
    // 获取唯一的id
    getUniqueId(string) {
      return uniqueId(string);
    },
    // 搜索
    onSearch() {
      this.fetchData();
    },
    // 重置
    onReset() {
      this.filter = cloneDeep(this.initFilter);
      this.fetchData();
    },
    // 获取列表信息
    fetchData() {
      this.listLoading = true;
      listBrandLicenseProcess(this.interfaceData)
        .then((response) => {
          this.list = response.data || [];
          this.$nextTick(() => {
            this.$refs.multipleTable.doLayout(); // 解决表格错位
          });
        })
        .finally(() => {
          this.listLoading = false;
        });
    },
    handleClose() {
      this.dialogVisible = false;
    },
    openProgress() {
      this.fetchData();
      this.dialogVisible = true;
    },
    linkAll(row) {
      this.handleClose();

      const linkParams = {
        distributorId: this.rowData.id,
        brandId: row.brandId
      };
      const path = `/customer-management/license-process`;
      sessionStorage.setItem(path, JSON.stringify(linkParams));
      this.$router.push({
        path: '/customer-management/license-process'
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.top-text {
  margin-bottom: 20px;
  padding: 0 10px;
  background-color: rgba(64, 158, 255, 0.19);
  border: 1px solid var(--color-primary);
  color: #000;
  font-size: 14px;
  text-align: left;
}
.table-box {
  ::v-deep &.el-table::before {
    height: 0;
  }
}
.link {
  color: var(--color-primary);
}
</style>
