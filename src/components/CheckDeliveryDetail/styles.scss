.empty {
  color: #666;
}
.animation-icon {
  ::v-deep .el-timeline-item__node {
    background-color: #ab0033;
    position: relative;
    &::after {
      content: '';
      position: absolute;
      width: 24px;
      height: 24px;
      border-radius: 50%;
      background: rgba(215, 9, 47, 0.3);
      animation: radiate 5s infinite;
    }
    &::before {
      content: '';
      position: absolute;
      width: 24px;
      height: 24px;
      border-radius: 50%;
      background: rgba(215, 9, 47, 0.3);
      animation: radiate 5s infinite;
      animation-delay: 2s;
    }
  }
}
@keyframes radiate {
  0% {
    transform: scale(0);
  }
  80% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(0.7);
    opacity: 0.4;
  }
}
.delivery-detail {
  padding-top: 20px;
  border-top: 2px solid #e5e5e5;
  padding-left: 24px;
  background: #fcfcfc;
  padding-bottom: 10px;
  ::v-deep .el-timeline {
    font-size: 12px;
  }
}
.disnone {
  ::v-deep .el-tabs__header {
    display: none;
  }
}
.delivery {
  display: inline-block;
  ::v-deep .el-dialog--center .el-dialog__body {
    padding-top: 0;
  }

  ::v-deep .el-dialog {
    max-width: 700px !important;
  }

  .line {
    margin-bottom: 0;
    padding-top: 10px;

    .label,
    .info {
      line-height: 18px;
      word-break: break-all;
    }

    .label {
      font-weight: 600;
    }
  }

  .btn {
    font-size: 12px;
    display: inline-block;
    background: #5f3bce;
    flex: none;
    color: #fff;
    padding: 0;
    cursor: pointer;
    border-radius: 3px;
    line-height: 22px;
    height: 22px;
    box-sizing: border-box;
    width: 60px;
    text-align: center;
    border: none;

    ::v-deep span {
      display: block;
      height: 22px;
      line-height: 20px;
      font-weight: normal;
    }
  }
}

.line {
  margin-bottom: 0;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  height: 80px;
  overflow-y: scroll;
  flex-wrap: wrap;
  &.text-input {
    align-items: flex-start;

    .info {
      display: flex;
      padding-top: 10px;

      .service-notes {
        line-height: 20px;
      }
    }
  }

  .label {
    font-weight: 600;
    text-align: right;
    vertical-align: middle;
    font-size: 12px;
    color: #606266;
    line-height: 40px;
    padding: 0 5px 0 0;
    flex: none;
    box-sizing: border-box;
  }

  .info {
    line-height: 30px;
    font-size: 12px;
    color: #606266;
    word-break: break-all;
    margin-right: 20px;
    &.info-flex {
      display: flex;
      align-content: center;
      align-items: center;
      flex-wrap: wrap;
    }
  }

  .line-sub {
    display: flex;
  }
}
::v-deep .line::-webkit-scrollbar {
  -webkit-appearance: none;
}
::v-deep .line::-webkit-scrollbar:vertical {
  width: 5px;
}
::v-deep .line::-webkit-scrollbar-thumb {
  border-radius: 4px;
  background-color: rgba(0, 0, 0, 0.5);
  box-shadow: 0 0 1px rgba(255, 255, 255, 0.5);
}
.goods-detail {
  display: flex;
  width: 270px;
  margin-right: 30px;
  margin-bottom: 4px;
  &--img {
    border: 1px solid #e5e5e5;
    flex-shrink: 0;
    margin-right: 10px;
    position: relative;
    img {
      width: 70px;
      height: 70px;
    }
  }
  &--view {
    flex: 1;
  }
  &--title {
    font-weight: 500;
    color: #333333;
    line-height: 17px;
    margin-bottom: 3px;
    width: 230px;
  }
  &--specification {
    font-weight: 400;
    color: #999999;
    line-height: 17px;
  }
  &--quantity {
    position: absolute;
    left: 0;
    bottom: 0;
    font-size: 10px;
    background: rgba(3, 3, 3, 0.4);
    color: #fff;
    width: 100%;
    text-align: center;
    height: 15px;
    line-height: 15px;
  }
}
