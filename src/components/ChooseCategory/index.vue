<template>
  <div>
    <el-dialog title="改分组" :visible.sync="dialogTableVisible">
      <div class="top-info">
        <form @submit.prevent="onSearch">
          <el-input placeholder="搜索分组名称" v-model.trim="name" class="input-with-select"></el-input>
          <el-button native-type="submit" type="primary" size="small">查询</el-button>
          <el-button @click="onReset">刷新</el-button>
          <router-link class="link" to="/setting/commodity/category">
            <span class="link">管理分组</span>
          </router-link>
          <span class="tips">已选商品： {{ commodityIdList.length }}</span>
        </form>
      </div>
      <el-table :data="list">
        <el-table-column label="是否选中" width="150">
          <template slot-scope="scope">
            <div class="checkbox" @click="changeStatus(scope.row.allCommodityOwn, scope.$index, scope.row.id)">
              <svg class="icon" aria-hidden="true" v-show="scope.row.allCommodityOwn === '1'">
                <use xlink:href="#icon-xuanzhong" />
              </svg>
              <svg class="icon" aria-hidden="true" v-show="scope.row.allCommodityOwn === '0'">
                <use xlink:href="#icon-banxuanzhong" />
              </svg>
              <svg class="icon" aria-hidden="true" v-show="scope.row.allCommodityOwn === '-1'">
                <use xlink:href="#icon-weixuanzhong" />
              </svg>
            </div>
          </template>
        </el-table-column>
        <el-table-column property="name" label="分组名称" width="200"></el-table-column>
        <el-table-column align="center" label="显示状态">
          <template slot-scope="scope">
            <span class="hided" v-if="scope.row.isHidden === '1'">已隐藏</span>
            <span class="hided" v-if="scope.row.isHidden === '0'">显示中</span>
          </template>
        </el-table-column>
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogTableVisible = false">取 消</el-button>
        <el-button type="primary" @click="onSubmit" :loading="submitLoading">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listCategoryWithCommodity, batchUpdateCategory } from '@/api/setting/commodity/category';
import remove from 'lodash/remove';
import cloneDeep from 'lodash/cloneDeep';
export default {
  name: 'ChooseCategory',
  props: {
    // 需要修改商品的列表
    commodityIdList: {
      type: Array,
      default() {
        return [];
      }
    },
    dialogVisible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      name: '',
      list: [], // 列表显示的数据
      originList: [], // 接口获取的数据
      listLoading: true,
      createCategoryId: [], // 指定商品需要新增的品类id
      submitLoading: false,
      deleteCategoryId: [] // 指定商品需要删除的品类id
    };
  },
  watch: {
    dialogTableVisible(newValue) {
      if (newValue) {
        this.fetchData();
      } else {
        this.name = '';
      }
    }
  },
  computed: {
    // 弹窗是否可见
    dialogTableVisible: {
      get() {
        return this.dialogVisible;
      },
      set(newValue) {
        this.$emit('dialogVisible', newValue);
      }
    },
    // 获取列表数据传参
    dataList() {
      const obj = {};
      obj.commodityIdList = this.commodityIdList.map((item) => {
        return item.id;
      });
      if (this.name) {
        obj.name = this.name;
      }
      return obj;
    },
    // 改分组确认传参
    updateData() {
      const obj = {};
      obj.commodityIdList = this.commodityIdList.map((item) => {
        return item.id;
      });
      if (this.createCategoryId.length > 0) {
        obj.createCategoryId = this.createCategoryId;
      }
      if (this.deleteCategoryId.length > 0) {
        obj.deleteCategoryId = this.deleteCategoryId;
      }
      return obj;
    },
    // 原始全选中的数据
    selectedList() {
      const list = [];
      this.originList.forEach((item) => {
        if (item.allCommodityOwn === '1') {
          list.push(item.id);
        }
      });
      return list;
    },
    // 原始部分选中的数据
    partSelectedList() {
      const list = [];
      this.originList.forEach((item) => {
        if (item.allCommodityOwn === '0') {
          list.push(item.id);
        }
      });
      return list;
    }
  },
  methods: {
    // 改变选中状态
    changeStatus(state, index, id) {
      // 点击选中变成未选中1变成-1
      if (state === '1') {
        // 选中变成未选中，createCategoryId移除该id
        remove(this.createCategoryId, function (n) {
          return n === id;
        });
        // 删除的id加一个
        if (this.selectedList.indexOf(id) > -1 || this.partSelectedList.indexOf(id) > -1) {
          this.deleteCategoryId.push(id);
        }
        this.list[index].allCommodityOwn = '-1';
      }
      // 点击部分选中变成选中0变成1
      if (state === '0') {
        this.createCategoryId.push(id);
        this.list[index].allCommodityOwn = '1';
      }
      // 点击未选中变成选中-1变成1
      if (state === '-1') {
        // 未选中变成选中，deleteCategoryId移除该id
        remove(this.deleteCategoryId, function (n) {
          return n === id;
        });
        // 新增的id加一个
        if (this.selectedList.indexOf(id) === -1) {
          this.createCategoryId.push(id);
        }
        this.list[index].allCommodityOwn = '1';
      }
      this.$forceUpdate();
    },
    // 获取分组列表信息
    fetchData() {
      this.listLoading = true;
      listCategoryWithCommodity(this.dataList)
        .then((response) => {
          this.list = response.data;
          this.originList = cloneDeep(this.list);
        })
        .finally(() => {
          this.listLoading = false;
        });
    },
    onSubmit() {
      let selectFlag = false; // 判断是否有勾选任意一个分组，因为新增商品的时候分组是必填，这里必须要有勾选分组
      this.list.forEach((v, i) => {
        if (v.allCommodityOwn === '1' || v.allCommodityOwn === '0') {
          selectFlag = true;
        }
      });
      if (!selectFlag) {
        this.$message.error('您未勾选任何分组哦~');
        return;
      }
      this.submitLoading = true;
      batchUpdateCategory(this.updateData)
        .then((response) => {
          this.$message.success(response.msg);
        })
        .finally(() => {
          this.submitLoading = false;
          this.dialogTableVisible = false;
          this.$emit('clearCommodityList', []);
        });
    },
    onSearch() {
      this.pageNo = 1;
      this.pageSize = 5;
      this.fetchData();
      this.createCategoryId = [];
      this.deleteCategoryId = [];
    },
    onReset() {
      this.pageNo = 1;
      this.pageSize = 5;
      this.name = '';
      this.fetchData();
      this.createCategoryId = [];
      this.deleteCategoryId = [];
    }
  }
};
</script>

<style lang="scss" scoped>
@import '../AddCommodity/styles';
.icon {
  width: 14px;
  height: 14px;
}
.tips {
  padding-left: 10px;
  color: #999;
  font-size: 12px;
}
</style>