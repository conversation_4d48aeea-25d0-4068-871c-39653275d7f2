<!-- 加密解密 组件 -->
<template>
  <div :style="[cryptoBlockStyle]">
    <div class="crypto_block" :class="fixed ? 'crypto_block-fixed' : ''">
      <el-input
        v-if="isInput"
        v-model="inputVal"
        class="crypto_block_input"
        :disabled="isDisabledInput"
        clearable
        v-bind="$attrs"
        :placeholder="placeholder"
      >
      </el-input>
      <span v-else class="crypto_block_text">{{ inputVal }}</span>
      <template v-if="isEnable">
        <div
          class="crypto_block_input-append"
          ref="crypto_block_input-append"
          v-if="appendShow"
        >
          <Authority :auth="detailAuth">
            <el-button
              class="crypto_block_input-btn"
              type="text"
              @click="toView"
              size="mini"
              :disabled="isEdit || viewDisabled"
              >查看</el-button
            >
          </Authority>
          <Authority :auth="editAuth">
            <el-button
              class="crypto_block_input-btn"
              type="text"
              size="mini"
              v-if="!disabled && isInput"
              @click="toEdit"
              >{{ isEdit ? '取消编辑' : '编辑' }}</el-button
            >
          </Authority>
        </div>
      </template>
    </div>
  </div>
</template>

<script>
/**
 * 加密解密 - 组件
 * bizType	bizId	备注
    REGISTER_MOBILE	用户ID(memberId或者userId)	分销商登录手机号(用于赊销账单明细等页面)	
    DISTRIBUTOR_MOBILE	分销商ID	分销商登录手机号	
    DISTRIBUTOR_CONTACT_PHONE	分销商ID	分销商联系方式	
    DISTRIBUTOR_IDCARD_NO	分销商ID	分销商身份证号	
    DISTRIBUTOR_CONTRACT_INFO_CONTACT_PHONE	合同主体ID	合同主体联系人电话	
    DISTRIBUTOR_CONTRACT_INFO_CONTACT_IDCARD_NO	合同主体ID	联系人身份证号码	
    DISTRIBUTOR_CONTRACT_INFO_CONTACT_ADDRESS	合同主体ID	合同主体联系人地址	
    DISTRIBUTOR_LEADS_MOBILE	团队线索ID	团队线索手机号	
    DISTRIBUTOR_LEADS_CONTACT_PHONE	客户资料ID	客户资料联系人手机号	
    DISTRIBUTOR_LEADS_COMPANY_MOBILE	客户资料ID	企业联系方式	
    DISTRIBUTOR_LEADS_CHANNEL_MOBILE	渠道线索ID	渠道线索手机号	
    CHAT_WINDOW_MOBILE	客服聊天窗口用户ID(memberId)	用户手机号(客服聊天窗口的收货人手机号和收货地址，统一调用喵咪提供的订单解密接口)	
    INVOICE_TITLE_BANK_ACCOUNT	发票抬头id	发票抬头中的银行账号	
    INVOICE_TITLE_ADDRESS	发票抬头id	发票抬头中的企业地址信息	
    INVOICE_TITLE_MOBILE	发票抬头id	发票抬头中的企业联系电话信息	
    INVOICE_TITLE_CONTACT_PHONE	发票抬头id	发票抬头中的联系人电话信息	
    INVOICE_TITLE_CONTACT_ADDRESS	发票抬头id	发票抬头中的联系人地址信息	
    INVOICE_TITLE_ADDRESS_DETAIL	发票抬头id	发票抬头详细地址	
    ORDER_CONSIGNEE_MOBILE	订单id	订单收货信息收货人电话	
    ORDER_CONSIGNEE_ADDRESS_DETAIL	订单id	订单收货信息-街道明细	
    REFUND_CONSIGNEE_MOBILE	退款单id	退货退款信息-收货人电话	
    REFUND_CONSIGNEE_ADDRESS_DETAIL	退款单id	退货退款信息-街道明细	
    ORDER_GLOBAL_IDCARD_NO	订单id	订单海淘信息身份证号码	
 */
import { zgSecretDecrypt } from './crypto';
export default {
  name: 'CryptoBlock',
  data() {
    return {
      viewDisabled: false, // 已经查看的需要禁用
      backupVal: '', // 备份
      appendClientWidth: 28,
      flag: true,
      isEdit: false, // 是否编辑
      isEnable: false // 总开关  是否启用解密功能
    };
  },
  props: {
    placeholder: String,
    // 类型
    tagType: {
      type: String, // input 输入框  、文本 text
      default: 'text'
    },
    bizId: String, // 解密实体id（如:分销商id，线索id，发票id）
    bizType: {
      type: String,
      default: ''
    }, // 解密类型(如: DISTRIBUTOR_MOBILE 解密分销商手机号),可用值:DISTRIBUTOR_MOBILE
    value: String, // 值
    fixed: Boolean, // 按钮是否fixed布局
    disabled: Boolean, // 是否禁用
    reset: Boolean, // 组件编辑重置
    editAuth: String, // 编辑权限
    detailAuth: String // 查看权限
  },
  activated() {
    if (this.isEdit) return; // 编辑状态不重置
    setTimeout(() => {
      !this.viewDisabled && this.resetEdit();
    }, 800);
  },
  components: {},
  computed: {
    inputVal: {
      set(val = '') {
        const v = this.isEdit ? val.replace(/[*]/g, '') : val;
        this.$emit('input', v);
      },
      get() {
        return this.value;
      }
    },
    // 输入框是否禁用
    isDisabledInput() {
      if (this.disabled) return true;
      if (!this.isEnable) return false;
      if (!this.value) return false;
      if (this.bizId && !this.isEdit) return true;
      return false;
    },
    // 是否展示附加按钮
    appendShow() {
      return !!this.bizId;
    },
    // 是输入框
    isInput() {
      return this.tagType === 'input';
    },
    cryptoBlockStyle() {
      const st = {};
      if (this.tagType === 'text') {
        st.display = 'inline-block';
      }
      return st;
    }
  },
  watch: {
    // 弹出框 需要出发重置时使用
    reset(v) {
      if (!v) {
        this.inputVal = '';
      }
      this.resetEdit();
    },
    value(v) {
      this.flag && this.setDefaultValue(v);
      this.viewDisabled = !this.matchDecryption(v);
    }
  },
  created() {
    if (this.value) {
      // 原来就有默认值
      this.setDefaultValue(this.value);
    }
  },
  mounted() {},

  methods: {
    // 星号布尔值
    matchDecryption(v = '') {
      return !!v.match(RegExp(/[*]/));
    },
    // 默认值
    setDefaultValue(v) {
      this.flag = false;
      this.backupVal = v;
      if (this.matchDecryption(v)) {
        // 第一次调用是有*星号 表示原框有默认值
        this.isEnable = true;
      } else {
        // 第一次调用没有*星号 表示原框没有默认值，不需要编辑与查看按钮
        this.isEnable = false;
      }
    },
    // 重置组件
    resetEdit() {
      Object.assign(this.$data, this.$options.data());
      this.value && this.setDefaultValue(this.value);
    },

    // 查看
    toView() {
      if (!this.inputVal) {
        return this.$message({
          message: '没有需要解密的信息',
          type: 'warning'
        });
      }
      const p = {
        bizId: this.bizId,
        bizType: this.bizType,
        path: this.$route.path
      };

      if (Object.values(p).some((i) => !i)) return;
      zgSecretDecrypt(p).then((res) => {
        if (res.data) {
          this.inputVal = res.data;
          this.$nextTick(() => {
            this.viewDisabled = true;
          });
        }
      });
    },
    // 编辑
    toEdit() {
      this.isEdit = !this.isEdit;
      this.inputVal = this.isEdit ? '' : this.backupVal;
    }
  }
};
</script>
<style lang="scss" scoped>
.crypto_block {
  display: flex;
  align-items: center;
  // width: 100%;
  &_text {
    margin-right: 3px;
  }
  &-fixed {
    position: relative;
  }
}
.crypto_block_input {
  &-append {
    flex: 1;
    display: flex;
    margin-left: 3px;
  }
  &-append-fixed {
    position: absolute;
  }
  &-btn {
    padding: 0 1px;
    margin-left: 0;
  }
}
</style>
