.author-keys {
  position: relative;
  border: 1px solid #dcdfe6;
  min-height: 36px;
  border-radius: 4px;
  word-break: break-all;
  white-space: normal;
  line-height: 24px;
  padding-right: 20px;
  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
  .tags {
    padding: 0 15px 6px 15px;
    & > * {
      margin-top: 6px;
      margin-right: 6px;
      max-width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      position: relative;
    }
  }
  .add-btn {
    position: absolute;
    right: 0;
    color: #606266;
    top: 0;
    bottom: 0;
    width: 20px;
    text-align: center;
    background-color: #dcdfe6;
    border-radius: 0 4px 4px 0;
    cursor: pointer;
    overflow: hidden;
    vertical-align: middle;
    &:hover {
      background-color: #c0c4cc;
    }
    & > span {
      font-size: 16px;
      position: relative;
      display: inline-block;
      top: 50%;
      transform: translateY(-50%);
    }
  }
}

.dialog {
  ::v-deep .el-dialog__body {
    height: 450px;
    overflow: auto;
  }
}
