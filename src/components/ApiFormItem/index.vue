<template>
  <el-form-item label="关联权限" prop="authorKeys">
    <div class="author-keys">
      <div class="tags">
        <el-tag
          closable
          v-for="item in value"
          :key="item.id"
          @close="onTagClose(item)"
          :type="item.id | filterTagType"
          :title="item.permissionName || item.permissionKey"
        >{{ item.permissionName || item.permissionKey }}</el-tag>
      </div>
      <div class="add-btn" @click="onAuthorClick">
        <span>+</span>
      </div>
    </div>
    <el-dialog class="dialog" append-to-body title="关联权限" :visible.sync="innerDialogVisible">
      <ApiTree ref="authority" :default-checked-keys="checkedKeys"/>
    </el-dialog>
  </el-form-item>
</template>
<script>
import ApiTree from '@/components/ApiTree';
export default {
  name: 'ApiFormItem',
  computed: {
    checkedKeys() {
      return this.value.map(item => item.id);
    }
  },
  watch: {
    innerDialogVisible(val) {
      const self = this;
      if (!self.$refs.authority) {
        return;
      }
      if (val) {
        self.$refs.authority.setCheckedKeys(self.checkedKeys);
      } else {
        const keys = self.$refs.authority.getCheckedNodes(true);
        self.$emit(
          'input',
          keys.map(({ code, id, label }) => ({
            id,
            permissionKey: code,
            permissionName: label
          }))
        );
      }
    }
  },
  data() {
    return {
      innerDialogVisible: false,
      apiMap: {},
      tagTypes: ['', 'success', 'info', 'danger']
    };
  },
  methods: {
    onTagClose(obj) {
      const self = this;
      const list = self.value.filter(item => item !== obj);
      self.$emit('input', list);
    },
    onAuthorClick() {
      const self = this;
      self.innerDialogVisible = true;
    },
    onConfirm(keys) {
      const self = this;
      self.$emit('input', keys);
    }
  },
  filters: {
    filterTagType(index) {
      const tagTypes = ['', 'success', 'info', 'warning', 'danger'];
      return tagTypes[index % tagTypes.length];
    }
  },
  props: {
    value: {
      type: Array,
      default: function() {
        return [];
      }
    }
  },
  components: {
    ApiTree
  }
};
</script>
<style lang="scss" scoped>
@import './styles';
</style>



