import dict from '@/components/Common/dicts';
import { exportExcel } from '@/api/distributorManagement/distributor/list';
import { organization_tree } from '@/api/setting/divide-group';

// 权限配置
export const Authority = {
  view: '/distributor-management/:detail',
  edit: 'distributor-management-edit',
  tripartiteCreate: '/distributor-management/cooperate-shop:create',
  tripartiteImport: '/distributor-management/cooperate-shop:import',
  audit: '/distributor-management/:audit', // 审核
  export: '/distributor-management/:export',
  assign: '/distributor-management/:assign', // 分配
  frozen: '/distributor-management/:frozen', // 冻结
  batchFrozen: 'distributor-management-batch-frozen', // 批量冻结
  record: '/distributor-management/:record', // 变动记录
  edit_DC: '/distributor-management/:edit_DC', // 编辑拓展顾问
  edit_EC: '/distributor-management/:edit_EC', // 编辑专属顾问
  edit_Control: '/distributor-management/:edit-control', // 进销存管控设置
  followRecord: '/distributor-management/:follow-record', // 查看跟进记录
  authorizeDegree: '/distributor-management/:authorize-degree', // 查看授权进度
  improveMobile: '/distributor-management/:improve-mobile', // 完善手机号
  changeMobile: '/distributor-management/:change-mobile', // 更改手机号
  createTeam: '/distributor-management/:create-team', // 建群、取消建群
  remark: '/distributor-management/:remark', // 备注
  integralSystem: 'distributor-management-integral-system' // 积分体系设置
};

export const FilterFormOptions = [
  {
    prop: 'groupId',
    label: '所属团队',
    component: 'select',
    options: dict('COMMON_SHOPTAFFGROUP_LIST')
  },
  {
    prop: 'ids',
    label: '分销商ID',
    component: 'input',
    placeholder: '请输入分销商ID，支持逗号分隔查询多条'
  },
  { prop: 'shopName', label: '店铺名称', component: 'input' },
  { prop: 'platformShopId', label: '店铺ID', component: 'input' },
  { prop: 'applyMobile', label: '登录手机号', component: 'input' },
  {
    prop: 'csIds',
    label: '专属顾问',
    component: 'select',
    multiple: true,
    collapseTags: true,
    options: dict('COMMON_EXPAND_ADVISER')
  },
  {
    prop: 'developCustomerServiceIdList',
    label: '拓展顾问',
    multiple: true,
    collapseTags: true,
    component: 'select',
    options: dict('COMMON_EXPAND_ADVISER')
  },
  {
    prop: 'purchaseType',
    label: '客户类型',
    component: 'select',
    options: dict('COMMON_BUSINESS_TYPE')
  },
  {
    prop: 'auditDate',
    props: ['startAuditDate', 'endAuditDate'],
    label: '审核时间',
    component: 'dateRange',
    type: 'daterange'
  },
  {
    prop: 'auditBy',
    label: '审核人',
    component: 'select',
    options: dict('COMMON_STAFF_LIST')
  },
  {
    prop: 'status',
    label: '审核状态',
    component: 'select',
    options: dict('DISTRIBUTOR_AUDIT_STATUS').then((res) => res.filter((i) => i.value !== 'NOT_REGISTER'))
  },
  {
    prop: 'activityDate',
    props: ['startDate', 'endDate'],
    label: '注册时间',
    component: 'dateRange',
    type: 'daterange'
  },
  {
    prop: 'source',
    label: '入驻方式',
    component: 'select',
    options: dict('DISTRIBUTOR_JOINMODE')
  },
  {
    prop: 'level',
    label: '客户内部评级',
    component: 'select',
    options: dict('COMMON_PORTRAIT_LEVEL')
  },
  {
    prop: 'developTypes',
    label: '拓展方式',
    component: 'select',
    multiple: true,
    collapseTags: true,
    options: window.$vue.$dict['soyoungzg_distributor_develop_type']
  },
  {
    prop: 'channelCode',
    label: '经营渠道',
    component: 'select',
    collapseTags: true,
    options: window.$vue.$dict['soyoungzg_channel']
  },
  {
    prop: 'mainChannels',
    label: '主营渠道',
    component: 'select',
    multiple: true,
    collapseTags: true,
    options: window.$vue.$dict['soyoungzg_channel']
  },
  {
    prop: 'customerAttributes',
    label: '客户层级',
    component: 'select',
    multiple: true,
    collapseTags: true,
    options: window.$vue.$dict['soyoungzg_distributor_customer_attribute']
  },
  {
    prop: 'customerOrganizationId',
    label: '专属顾问分组',
    component: 'cascader',
    props: {
      value: 'id',
      label: 'name',
      emitPath: false,
      checkStrictly: true,
      expandTrigger: 'hover'
    },
    options: organization_tree({}).then((res) => res?.data ?? [])
  },
  {
    prop: 'bizType',
    label: '顾问业务类型',
    component: 'select',
    options: dict('COMMON_CS_BIZ_TYPE')
  },
  {
    prop: 'assignStatus',
    label: '分配状态',
    component: 'select',
    options: dict('COMMON_DISTRIBUTOR_ASSIGN_STATUS')
  },
  {
    prop: 'assignDateTime',
    props: ['startAssignDate', 'endAssignDate'],
    label: '分配时间',
    component: 'dateRange',
    type: 'daterange'
  },
  {
    prop: 'assignOrganizationId',
    label: '分配分组',
    component: 'cascader',
    props: {
      value: 'id',
      label: 'name',
      emitPath: false,
      checkStrictly: true,
      expandTrigger: 'hover'
    },
    options: organization_tree({}).then((res) => res?.data ?? [])
  },
  {
    prop: 'intentionCategoryValues',
    label: '客户类目',
    component: 'select',
    multiple: true,
    collapseTags: true,
    options: window.$vue.$dict['syzg_business_category']
  },
  {
    prop: 'industryLevel',
    label: '行业评级',
    component: 'select',
    options: dict('COMMON_TRADE_LEVEL')
  },
  {
    prop: 'groupHandoverStatus',
    label: '建群转接',
    component: 'select',
    options: dict('DISTRIBUTOR_ISCREATEGOURP')
  },
  { prop: 'buyerName', label: '三方买家信息' },
  {
    prop: 'izFlowStatus',
    label: '是否在公海池',
    component: 'select',
    options: dict('DISTRIBUTOR_ISPUBLIC')
  },
  {
    prop: 'toPublicDay',
    label: '离划入公海池时间',
    component: 'select',
    options: dict('DISTRIBUTOR_TOPUBLICDAY')
  },
  {
    prop: 'businessCategoryList',
    label: '线索主营类目',
    multiple: true,
    collapseTags: true,
    component: 'select',
    options: dict('COMMON_CATEGORY')
  },
  {
    prop: 'interestedBrandList',
    label: '线索偏好品牌',
    component: 'select',
    multiple: true,
    collapseTags: true,
    options: dict('COMMON_PREFER_BRAND')
  },
  {
    prop: 'psiControl',
    label: '进销存管控',
    component: 'select',
    options: [
      {
        label: '参与',
        value: 'YES'
      },
      {
        label: '不参与',
        value: 'NO'
      }
    ]
  },
  {
    prop: 'izYearlyContract',
    label: '是否为年框客户',
    component: 'select',
    options: [...window.$vue.$dict['soyoungzg_common_whether'], { label: '未维护', value: 'EMPTY' }]
  },
  {
    prop: 'isSyncMerchant',
    label: '是否入驻水羊直供',
    component: 'select',
    options: [
      {
        label: '是',
        value: '1'
      },
      {
        label: '否',
        value: '0'
      }
    ]
  },
  {
    prop: 'freezeReason',
    label: '冻结原因',
    component: 'select',
    options: dict('DISTRIBUTOR_FREEZEREASON')
  },
  {
    prop: 'customerChannelKind',
    label: '渠道分类',
    component: 'select',
    options: window.$vue.$dict['soyoungzg_channel_type']
  },
  {
    prop: 'businessPlate',
    label: '业务模块-线上',
    component: 'select',
    options: dict('DISTRIBUTOR_BUSINESSPLATE')
  },
  {
    prop: 'businessPlateOffline',
    label: '业务模块-线下',
    component: 'select',
    options: window.$vue.$dict['soyoungzg_distributor_business_plate_offline']
  },
  {
    prop: 'businessTypeAndExtends',
    label: '客户业务类型-线下',
    component: 'select',
    multiple: true,
    collapseTags: true,
    options: window.$vue.$dict['distributor_business_type_and_extend']
  },
  {
    prop: 'relationChannels',
    label: '关联渠道',
    multiple: true,
    collapseTags: true,
    component: 'select',
    options: dict('COMMON_EXTERNALCHANNEL_LIST_ALL')
  },
  {
    prop: 'channelEcosystems',
    label: '渠道生态归属',
    component: 'select',
    multiple: true,
    collapseTags: true,
    options: window.$vue.$dict['distributor_channel_ecosystem']
  },
];

// 筛选条件配置参数
const FilterConfigItems = [
  {
    name: '分销商基础信息',
    childs: [
      'groupId',
      'ids',
      'shopName',
      'platformShopId',
      'channelEcosystems',
      'applyMobile',
      'csIds',
      'developCustomerServiceIdList',
      'purchaseType',
      'auditDate',
      'auditBy',
      'status',
      'activityDate',
      'source',
      'level',
      'businessPlate',
      'developTypes',
      'channelCode',
      'mainChannels',
      'customerAttributes',
      'psiControl',
      'izYearlyContract',
      'isSyncMerchant',
      'freezeReason',
      'customerChannelKind',
      'businessPlateOffline',
      'businessTypeAndExtends',
      'relationChannels',
    ]
  },
  {
    name: '内部分组信息',
    childs: ['customerOrganizationId', 'bizType']
  },
  {
    name: '客户分配信息',
    childs: ['assignStatus', 'assignDateTime', 'assignOrganizationId', 'intentionCategoryValues', 'industryLevel', 'groupHandoverStatus']
  },
  {
    name: '客户其他信息',
    childs: ['buyerName', 'izFlowStatus', 'toPublicDay', 'businessCategoryList', 'interestedBrandList']
  }
];
export const FilterConfigItemsOptions = FilterConfigItems.map((i) => ({
  ...i,
  childs: i.childs.map((prop) => FilterFormOptions.find((j) => j.prop === prop))
}));

export const ExportOptions = {
  auth: Authority.export,
  name: '分销商管理',
  async request(params) {
    const ids = params.ids;
    const data = { ...params };
    if (ids && ids.includes(',')) {
      data.ids = ids.split(',').filter((i) => !!i);
    }
    const res = await exportExcel(data);

    try {
      const resObj = JSON.parse(new TextDecoder('utf-8').decode(new Uint8Array(res)));
      if (resObj.data === '1') {
        this.$message.warning('导出数据超过3000条,已发送飞书信息,点击链接下载!');
      }
    } catch (error) {
      return res;
    }
  }
};

export const BarOptions = [
  {
    id: 'addTripartiteDistributor',
    label: '添加三方分销商',
    authority: Authority.tripartiteCreate,
    path: '/distributor-management/distributor/add'
  },
  {
    id: 'importTripartiteDistributor',
    label: '导入三方分销商',
    authority: Authority.tripartiteImport
  },
  {
    id: 'batchEditBD',
    label: '编辑拓展顾问',
    authority: Authority.edit_DC
  },
  {
    id: 'batchEditEC',
    label: '编辑专属顾问',
    authority: Authority.edit_EC
  },
  {
    id: 'batchAmongAdviser',
    label: '批量分配',
    authority: Authority.assign
  },
  {
    id: 'batchFrozenDistributor',
    label: '批量冻结',
    authority: Authority.batchFrozen
  },
  {
    id: 'izJoinCredit',
    label: '积分体系管控设置',
    authority: Authority.integralSystem
  }
];

// 列表展示内容
export const TableOptions = [
  {
    prop: 'shopName',
    label: '店铺名称',
    fixed: 'left',
    width: '140',
    isCustom: true
  },
  {
    prop: 'distributorDetail',
    label: '分销商信息',
    width: '180',
    isCustom: true
  },
  {
    prop: 'channelInfoVOList',
    label: '经营渠道',
    width: '120',
    showOverflowTooltip: true,
    formatter: (r) => (r?.channelInfoVOList ?? []).map((i) => i.channelName).join('、')
  },
  {
    prop: 'channelEcosystemName',
    label: '渠道生态归属',
    width: '120'
  },
  {
    prop: 'customerChannelKindName',
    label: '渠道分类',
    width: '120'
  },
  {
    prop: 'customerServiceId',
    label: '专属顾问',
    width: '120',
    isCustom: true
  },
  {
    prop: 'developCustomerServiceId',
    label: '拓展顾问',
    width: '100',
    isCustom: true
  },
  {
    prop: 'industryLevel',
    label: '行业评级',
    width: '80'
  },
  {
    prop: 'statusName',
    label: '审核状态',
    isCustom: true
  },
  {
    prop: 'createDate',
    label: '入驻时间/创建时间',
    width: '140',
    isCustom: true
  },
  {
    prop: 'firstOrderTime',
    label: '首采合作时间',
    width: '120',
    isCustom: true
  },
  {
    prop: 'intentionCategoryName',
    label: '意向类目'
  },
  {
    prop: 'groupId',
    width: '80',
    label: '所属团队',
    formatter: (r) => r?.customerServiceVO?.groupName
  },
  {
    prop: 'assignStatusName',
    label: '分配状态'
  },
  {
    prop: 'psiControlName',
    label: '进销存管控',
    width: '120'
  },
  {
    prop: 'auditDate',
    label: '审核人/时间',
    width: '120',
    isCustom: true
  },
  {
    prop: 'assignDate',
    label: '分配时间',
    width: '100',
    isCustom: true
  },
  {
    prop: 'groupHandoverStatusName',
    label: '建群转接'
  },
  {
    prop: 'sourceName',
    label: '入驻方式',
    width: '80'
  },
  {
    prop: 'levelName',
    label: '客户内部评级',
    width: '120'
  },
  {
    prop: 'businessPlateName',
    label: '业务模块-线上',
    width: '100'
  },
  {
    prop: 'externalBuyerInfos',
    label: '三方买家信息',
    width: '120',
    formatter: (r, c, v = []) => v[0] && v[0].buyerName
  },
  {
    prop: 'businessCategoryName',
    label: '线索主营类目',
    width: '180',
    showOverflowTooltip: true
  },
  {
    prop: 'assignOrganizationName',
    label: '分配分组',
    width: '100'
  },
  {
    prop: 'purchaseTypeName',
    width: '80',
    label: '客户类型'
  },
  {
    prop: 'contractStatusName',
    width: '80',
    label: '合同状态'
  },
  {
    prop: 'firstWechatBindingDate',
    width: '120',
    label: '绑定企微时间',
    isCustom: true
  },
  {
    prop: 'flowDate',
    width: '160',
    label: '最新拥有专属顾问时间',
    isCustom: true
  },
  {
    prop: 'approveMsg',
    width: '120',
    label: '分销商备注'
  },
  {
    prop: 'syncMerchantDate',
    label: '开启允许登录商家端时间',
    width: '180',
    isCustom: true
  },
  {
    prop: 'interestedBrands',
    label: '线索偏好品牌',
    width: '180',
    showOverflowTooltip: true,
    formatter: (r) => (r?.interestedBrands ?? []).map((i) => i.name).join('、')
  },
  {
    prop: 'developTypeName',
    label: '拓展方式'
  },
  {
    prop: 'mainChannelName',
    label: '主营渠道'
  },
  {
    prop: 'customerAttributeName',
    label: '客户层级'
  },
  {
    prop: 'izYearlyContractName',
    label: '是否为年框客户',
    width: '100'
  },
  {
    prop: 'isSyncMerchantName',
    label: '是否入住水羊直供',
    width: '120'
  }
];

// 列表操作
export const TableBarOptions = [
  {
    id: 'auditDistributor',
    label: '审核',
    authority: Authority.audit,
    // 待审核
    loadCondition: ({ status }) => status === 'WAIT_AUDIT',
    button: {
      type: 'text'
    }
  },
  {
    id: 'editDistributor',
    label(data) {
      return data.status === 'QUALIFICATIONS_WAIT_AUDIT' ? '审核' : '编辑';
    },
    authority: Authority.edit,
    // 审核通过
    loadCondition: ({ status }) => status === 'PASS' || status === 'QUALIFICATIONS_WAIT_AUDIT',
    button: {
      type: 'text'
    }
  },
  {
    id: 'remarkDistributor',
    label: '备注',
    authority: Authority.remark,
    isWrap: true,
    button: {
      type: 'text'
    }
  },
  {
    id: 'createGroup',
    label(data) {
      return data.groupHandoverStatus === 'DOWN_GROUP' ? '取消建群' : '建群';
    },
    authority: Authority.createTeam,
    // 审核通过，冻结，注销中
    loadCondition: ({ status }) => ['PASS', 'DISABLE', 'LOGOUT_WAIT_AUDIT', 'QUALIFICATIONS_WAIT_AUDIT'].includes(status),
    button: {
      type: 'text'
    }
  },
  {
    id: 'frozenDistributor',
    label({ status }) {
      return status === 'PASS' ? '冻结' : '解冻';
    },
    authority: Authority.frozen,
    isWrap: true,
    // 审核通过，冻结
    loadCondition: ({ status }) => ['PASS', 'DISABLE', 'QUALIFICATIONS_WAIT_AUDIT'].includes(status),
    button: {
      type: 'text'
    }
  },
  {
    id: 'assignShop',
    label: '分配',
    authority: Authority.assign,
    loadCondition: ({ status, assignStatus }) => {
      const white = ['WAIT_ASSIGN', 'WAIT_TEAM1_ASSIGN', 'WAIT_TEAM2_ASSIGN'];
      return ['PASS', 'DISABLE', 'LOGOUT_WAIT_AUDIT', 'QUALIFICATIONS_WAIT_AUDIT'].includes(status) && white.includes(assignStatus);
    },
    button: {
      type: 'text'
    }
  },
  {
    id: 'recordDistributor',
    label: '变动记录',
    authority: Authority.record,
    isWrap: true,
    button: {
      type: 'text'
    }
  },
  {
    id: 'improveMobile',
    label: '完善手机号',
    authority: Authority.improveMobile,
    loadCondition({ status, applyMobile }) {
      return !applyMobile && ['PASS', 'DISABLE'].includes(status);
    },
    isWrap: true,
    button: {
      type: 'text'
    }
  },
  {
    id: 'changeMobile',
    label: '更改手机号',
    authority: Authority.changeMobile,
    loadCondition({ applyMobile }) {
      return applyMobile;
    },
    isWrap: true,
    button: {
      type: 'text'
    }
  },
  {
    id: 'checkFollowUpRecord',
    label: '跟进记录',
    authority: Authority.followRecord,
    // 审核通过，冻结，注销中，注销
    loadCondition: ({ status }) => ['PASS', 'DISABLE', 'LOGOUT_WAIT_AUDIT', 'LOGOUT_PASS', 'QUALIFICATIONS_WAIT_AUDIT'].includes(status),
    isWrap: true,
    button: {
      type: 'text'
    }
  },
  {
    id: 'checkAuthorizeDegree',
    label: '查看授权进度',
    authority: Authority.authorizeDegree,
    // 审核通过，冻结，注销中
    loadCondition: ({ status }) => ['PASS', 'DISABLE', 'LOGOUT_WAIT_AUDIT', 'QUALIFICATIONS_WAIT_AUDIT'].includes(status),
    isWrap: true,
    button: {
      type: 'text'
    }
  }
];

// 列表冻结弹框配置
export const FrozenDialogOptions = [
  {
    prop: 'createByName',
    label: '冻结执行人',
    component: 'input',
    disabled: true,
    type: 'text'
  },
  {
    prop: 'reason',
    label: '冻结原因',
    component: 'select',
    options: dict('DISTRIBUTOR_FREEZEREASON').then((res) => {
      const options = res.filter(({ value }) => value !== 'SLIENT');
      return options;
    }),
    placeholder: '请选择冻结原因'
  },
  {
    prop: 'reasonDesc',
    label: '原因描述',
    component: 'input',
    rows: '4',
    showWordLimit: true,
    type: 'textarea',
    maxlength: 100,
    placeholder: '对冻结原因的进一步描述，最多100字'
  }
];
