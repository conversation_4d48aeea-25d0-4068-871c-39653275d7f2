<!-- 分销商管理 - 编辑、审核、详情 -->
<template>
  <div class="page-container">
    <header class="header-box">
      <div class="header-box--head">
        <img :src="distributorDetailHead" alt="" />
      </div>
      <div class="header-box--info">
        <div class="header-box--info__name">
          {{ distributorInfo.shopName }}
          <Authority auth="/distributor-management/:decrypt-all">
            <el-button type="text" @click="ijDecrypt" :disabled="ijDecryptDisabled" v-if="id">一键解密</el-button>
          </Authority>
        </div>
        <div>登录手机号： {{ distributorInfo.applyMobile || '--' }}</div>
        <div>店铺ID： {{ distributorInfo.platformShopId || '--' }}</div>
        <div>分销商ID： {{ id || '--' }}</div>
      </div>
    </header>
    <article>
      <el-tabs class="custom-border-tabs" v-model="activeName">
        <el-tab-pane label="基础信息" name="basic">
          <Basic v-bind="$props" :ijDecryptDisabled="ijDecryptDisabled" ref="basic" :distributorInfo.sync="distributorInfo" @changeTab="changeTab"></Basic>
        </el-tab-pane>
        <el-tab-pane label="分销商主体信息" name="contractInfo" v-if="isShowTab">
          <ContractInfo v-bind="$props" ref="contractInfo" v-if="activeName === 'contractInfo'"></ContractInfo>
        </el-tab-pane>
        <el-tab-pane label="合同信息" name="contract" v-if="isShowTab">
          <Contract v-bind="$props" @changeTab="changeTab" ref="contract" v-if="activeName === 'contract'" :star-level="distributorInfo.starLevel"></Contract>
        </el-tab-pane>
        <el-tab-pane label="授权书信息" name="authorizationList" v-if="isShowTab">
          <AuthorizationList v-bind="$props" ref="authorizationList" v-if="activeName === 'authorizationList'" :shopName="distributorInfo.shopName"></AuthorizationList>
        </el-tab-pane>
        <el-tab-pane label="变动记录" name="changeRecord" v-if="isShowTab">
          <change-record :id="id" :isDetail="isDetail" :distributorDevelopId="distributorInfo.distributorDevelopId" v-if="activeName === 'changeRecord'"></change-record>
        </el-tab-pane>
        <el-tab-pane label="店铺白名单" name="whiteList" v-if="isShowTab">
          <shop-white :isDetail="isDetail" :id="id" v-if="activeName === 'whiteList'" :otherChannelInfo="distributorInfo.otherChannelInfo" :channelInfoVOList="distributorInfo.channelInfoVOList" @changeTab="changeTab"></shop-white>
        </el-tab-pane>
      </el-tabs>
    </article>
  </div>
</template>

<script>
import { removeItem } from '@/utils/sessionStorage';
const staticHref = window.QIANKUN_DATA.subLocation.href;
import ContractInfo from './ContractInfo';
import Contract from './ContractNew';
import Basic from './Basic.vue';
import ChangeRecord from './change-record';
import ShopWhite from './shop-white';
import AuthorizationList from './AuthorizationList';

export default {
  data() {
    return {
      distributorDetailHead: `${staticHref}/imgs/distributor-detail.png`, // 静态文件图片
      activeName: 'basic',
      distributorInfo: {},
      ijDecryptDisabled: false
    };
  },
  name: 'DistributorDetail',
  props: {
    id: String,
    isEdit: {
      type: Boolean,
      default: false
    },
    isDetail: {
      type: Boolean,
      default: false
    },
    isAudit: {
      type: Boolean,
      default: false
    },
    isAdd: {
      type: Boolean,
      default: false
    },
    selectedGroupId: {
      // 平台方需要先选择团队才可以新增分销商
      type: String,
      default: ''
    }
  },
  components: { ContractInfo, Contract, Basic, ChangeRecord, ShopWhite, AuthorizationList },
  computed: {
    isShowTab() {
      //  WAIT_AUDIT-待审核,REJECT-审核未通过 不可查看其他信息
      const { status } = this.distributorInfo;
      return this.isEdit || (this.isDetail && !['WAIT_AUDIT', 'REJECT'].includes(status));
    }
  },
  watch: {
    '$route.query': {
      handler() {
        this.setTabs();
      },
      immediate: true,
      deep: true
    },
    activeName(val) {
      // 重新渲染三方买家信息
      if (val && val === 'basic') {
        const ref = this.$refs['basic'];
        ref && ref.handleBuyerInfo();
      }
    }
  },
  mounted() {
    this.setTabs();
  },
  activated() {
    console.log('activated');
  },
  destroyed() {
    // 清除线索信息
    removeItem('SOYOUNG_ZG_ONLINE_CLUE_LIST');
    removeItem('SOYOUNG_ZG_OFFLINE_CLUE_LIST');
  },
  methods: {
    setTabs() {
      const { activeName = '' } = this.$route.query;
      if (activeName) {
        this.changeTab(activeName);
      }
      this.setPageScrollTo();
    },
    // 全局页面滚动条回正
    setPageScrollTo() {
      const pagedom = document.getElementsByClassName('content-inner-scrollTo')[0];
      pagedom && pagedom.scrollTo(0, 0);
    },
    changeTab(activeName) {
      this.activeName = activeName;
    },
    ijDecrypt() {
      this.ijDecryptDisabled = true;
      this.$refs['basic'].ijDecrypt(() => {
        this.ijDecryptDisabled = false;
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.header-box {
  display: flex;
  margin-bottom: 16px;
  font-size: 14px;
  &--head {
    width: 100px;
    height: 100px;
    flex-shrink: 0;
    margin-right: 16px;
    img {
      width: 100%;
      height: 100%;
    }
  }
  &--info {
    & > div {
      line-height: 1.6;
    }
    &__name {
      font-size: 16px;
      font-weight: 500;
      margin-bottom: 7px;
    }
  }
}
</style>
