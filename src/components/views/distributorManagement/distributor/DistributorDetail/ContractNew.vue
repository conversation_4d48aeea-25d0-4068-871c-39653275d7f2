<!-- 分销商管理 - 合同信息 -->
<template>
  <div class="contract">
    <sy-normal-table ref="tableRef" v-bind="table" v-el-horizontal-scroll />
    <!--签署合同-->
    <el-drawer size="60%" @close="closeDialog" :title="formTitle" :visible.sync="dialogFormVisible" destroy-on-close custom-class="common-drawer">
      <div class="drawer-content form">
        <!--基础信息-->
        <div class="form-title">
          <strong class="form-title__text">基础信息</strong>
        </div>
        <el-form :disabled="isDetail" :model="form" :rules="rules" label-width="120px" label-position="left" ref="form">
          <el-row :gutter="24" type="flex" style="flex-wrap: wrap">
            <el-col :span="12" v-if="formType === 'OFFLINE'">
              <el-form-item label="合同名称" prop="contractName">
                <el-input v-model.trim="form.contractName" placeholder="请输入合同名称或OA流程主题名称"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="formType === 'OFFLINE' || formType === 'DISTRIBUTE' || formType === 'SALE_CREDIT'">
              <el-form-item label="甲方主体" prop="appId">
                <el-select clearable size="small" v-model="form.appId" placeholder="请选择">
                  <el-option :key="idx" :label="item.company" :value="item.id" v-for="(item, idx) in ownCompanyOptions"></el-option>
                </el-select>
                <el-tooltip effect="dark" placement="top" v-if="formType === 'DISTRIBUTE' || formType === 'SALE_CREDIT'" style="margin-left: 12px">
                  <div slot="content">
                    <div>1、自有品牌大贸业务（含伊菲丹和佩尔赫乔），选择湖南水羊电子商务有限公司</div>
                    <div>2、国际品牌大贸业务，选择长沙水羊网络科技有限公司</div>
                  </div>
                  <i class="el-icon-question"></i>
                </el-tooltip>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="授权渠道" prop="channelInfo">
                <el-select clearable multiple collapse-tags size="small" v-model="form.channelInfo" placeholder="请选择授权渠道" style="margin-right: 12px">
                  <el-option :key="item.channel" :label="item.channelName" :value="item.channel" v-for="item in channelOptions"></el-option>
                </el-select>
                <el-button @click="linkchannel" type="text">管理授权渠道</el-button>
                <el-button type="text" size="samll" @click="getChannel">刷新</el-button>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="new Set(form.channelInfo.map((item) => item)).has('OTHER')">
              <el-form-item label="其他授权渠道" prop="otherChannelInfo">
                <el-select clearable multiple size="small" v-model="form.otherChannelInfo" placeholder="请选择其他授权渠道">
                  <el-option :key="item" :label="item" :value="item" v-for="item in otherChannelOptions"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="formType === 'OFFLINE'">
              <el-form-item label="合同类型" prop="type">
                <el-select clearable size="small" v-model="form.type">
                  <el-option label="采销合同" value="DISTRIBUTE"></el-option>
                  <el-option label="赊销合同" value="SALE_CREDIT"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="个人/企业" prop="merchantType">
                <el-select size="small" v-model="form.merchantType" @change="changeDistributorContract">
                  <el-option label="个人" value="PERSONAL"></el-option>
                  <el-option label="企业" value="ENTERPRISE"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- 线上 -->
            <!-- 企业 -->
            <el-col :span="12" v-if="form.merchantType === 'ENTERPRISE'">
              <el-form-item label="企业名称" prop="company">
                <el-select clearable size="small" v-model="form.company" @change="companyChange" style="margin-right: 12px">
                  <el-option :label="item.company" :value="item.id" :key="item.id" v-for="item in distributorContractInfoList"></el-option>
                </el-select>
                <el-button type="text" size="samll" @click="toDistributorContractInfo">管理主体信息</el-button>
                <el-button type="text" size="samll" @click="changeDistributorContract(form.merchantType)">刷新</el-button>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="form.merchantType === 'ENTERPRISE'">
              <el-form-item label="税号" prop="taxNo">
                <el-input v-model.trim="form.taxNo" :disabled="true"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="form.merchantType === 'PERSONAL'">
              <el-form-item label="联系人真实姓名" prop="contactRealName">
                <el-select clearable size="small" v-model="form.contactRealName" @change="contactRealNameChange" style="margin-right: 12px">
                  <el-option :label="item.contactRealName" :value="item.id" :key="item.id" v-for="item in distributorContractInfoList"></el-option>
                </el-select>
                <el-button type="text" size="samll" @click="toDistributorContractInfo">管理主体信息</el-button>
                <el-button type="text" size="samll" @click="changeDistributorContract(form.merchantType)">刷新</el-button>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="form.merchantType === 'ENTERPRISE'">
              <el-form-item label="法定代表人姓名" prop="contactRealName">
                <el-input v-model.trim="form.contactRealName" :disabled="true"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="contactIdcardNoName" prop="contactIdcardNo">
                <CryptoBlock tag-type="input" :biz-id="form.id" detail-auth="/distributor-management/:decrypt-idcard" biz-type="DISTRIBUTOR_CONTRACT_INFO_CONTACT_IDCARD_NO" :reset="dialogFormVisible" disabled v-model.trim="form.contactIdcardNo" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="联系人地址" prop="contactAddress">
                <CryptoBlock tag-type="input" :biz-id="form.id" detail-auth="/distributor-management/:decrypt-address-detail" biz-type="DISTRIBUTOR_CONTRACT_INFO_CONTACT_ADDRESS" :reset="dialogFormVisible" disabled v-model.trim="form.contactAddress" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item :label="form.merchantType === 'PERSONAL' ? '联系人电话' : '法定代表人电话'" prop="contactPhone">
                <CryptoBlock tag-type="input" :biz-id="form.id" detail-auth="/distributor-management/:decrypt-mobile" biz-type="DISTRIBUTOR_CONTRACT_INFO_CONTACT_PHONE" :reset="dialogFormVisible" disabled v-model.trim="form.contactPhone" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="联系人邮箱" prop="contactEmail">
                <el-input v-model.trim="form.contactEmail" :disabled="true"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="form.type === 'SALE_CREDIT' || type === 'SALE_CREDIT'">
              <el-form-item label="每月还款日">
                <el-input :disabled="true" v-model="form.repaymentDay"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="form.type === 'SALE_CREDIT' || type === 'SALE_CREDIT'">
              <el-form-item label="保证金">
                <el-input :disabled="true" v-model="form.depositAmount"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="form.type === 'SALE_CREDIT' || type === 'SALE_CREDIT'">
              <el-form-item label="授信额度">
                <el-input :disabled="true" v-model="form.saleCreditAmount"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="合同有效期" prop="creditDate">
                <el-date-picker :default-time="['00:00:00', '23:59:59']" align="right" end-placeholder="结束日期" start-placeholder="开始日期" type="daterange" v-model="form.creditDate" v-date-picker-input-readonly></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="formType === 'OFFLINE'">
              <el-form-item label="上传合同文件" prop="contractUrl">
                <PdfUpload v-model="form.contractUrl" isPrivate group="file/brandlicense"></PdfUpload>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="formType === 'OFFLINE'">
              <el-form-item label="备注" prop="remarks">
                <el-input type="textarea" v-model.trim="form.remarks"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div class="form-title">
          <strong class="form-title__text">主体开票信息</strong>
          <span class="m-r-12 font-12 color-danger">请前去主体信息里面补充信息</span>
          <el-button type="text" @click="toDistributorContractInfo">管理主体信息</el-button>
        </div>
        <el-form :disabled="isDetail" :model="invoiceInfoForm" :rules="invoiceInfoRules" ref="invoiceInfoForm" label-width="120px" label-position="left">
          <el-row type="flex" style="flex-wrap: wrap">
            <el-col :span="12">
              <el-form-item label="发票抬头" prop="title">
                <el-input v-model.trim="invoiceInfoForm.title" disabled></el-input>
              </el-form-item>
            </el-col>
            <template v-if="form.merchantType === 'ENTERPRISE'">
              <el-col :span="12">
                <el-form-item label="税号" prop="taxNo">
                  <el-input v-model.trim="invoiceInfoForm.taxNo" disabled></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="开户银行" prop="bankName">
                  <el-input type="textarea" :autosize="{ minRows: 1, maxRows: 2 }" v-model.trim="invoiceInfoForm.bankName" disabled></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="银行账号" prop="bankAccount">
                  <CryptoBlock tag-type="input" :biz-id="invoiceTitleId" detail-auth="/distributor-management/:decrypt-bank-account" biz-type="INVOICE_TITLE_BANK_ACCOUNT" :reset="dialogFormVisible" disabled v-model.trim="invoiceInfoForm.bankAccount" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="企业地址" prop="address">
                  <CryptoBlock tag-type="input" type="textarea" :autosize="{ minRows: 2, maxRows: 4 }" :biz-id="invoiceTitleId" detail-auth="/distributor-management/:decrypt-address-detail" biz-type="INVOICE_TITLE_ADDRESS" :reset="dialogFormVisible" disabled v-model.trim="invoiceInfoForm.address" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="企业电话" prop="mobile">
                  <CryptoBlock tag-type="input" :biz-id="invoiceTitleId" detail-auth="/distributor-management/:decrypt-mobile" biz-type="INVOICE_TITLE_MOBILE" :reset="dialogFormVisible" disabled v-model.trim="invoiceInfoForm.mobile" />
                </el-form-item>
              </el-col>
            </template>
            <el-col :span="24">
              <p style="margin-bottom: 12px">发票联系人信息</p>
            </el-col>
            <el-col :span="12">
              <el-form-item label="联系人姓名" prop="contactName">
                <el-input v-model.trim="invoiceInfoForm.contactName" disabled></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="联系人地址" prop="areaCodes">
                <sy-address style="width: 300px" :bind="{ clearable: true, disabled: true }" v-model="invoiceInfoForm.areaCodes"></sy-address>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="addressDetail" label="联系人详细地址">
                <CryptoBlock
                  tag-type="input"
                  type="textarea"
                  :autosize="{ minRows: 2, maxRows: 4 }"
                  :biz-id="invoiceTitleId"
                  biz-type="INVOICE_TITLE_CONTACT_ADDRESS"
                  detail-auth="/distributor-management/:decrypt-address-detail"
                  :reset="dialogFormVisible"
                  disabled
                  v-model.trim="invoiceInfoForm.addressDetail"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="联系人电话" prop="contactPhone">
                <CryptoBlock tag-type="input" :biz-id="invoiceTitleId" detail-auth="/distributor-management/:decrypt-mobile" biz-type="INVOICE_TITLE_CONTACT_PHONE" :reset="dialogFormVisible" disabled v-model.trim="invoiceInfoForm.contactPhone" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="联系人邮箱" prop="email">
                <el-input v-model.trim="invoiceInfoForm.email" disabled></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div class="form-title">
          <strong class="form-title__text">收付款信息</strong>
          <template v-if="isAddBankCardInfo">
            <el-button @click="cancelEditBankCardInfo">取消新增</el-button>
            <button-hoc type="primary" :loading="addBankCardInfoLoading" @click="saveEditBankCardInfo">保存</button-hoc>
          </template>
          <el-button v-else type="primary" @click="isAddBankCardInfo = true">新增收付款账号</el-button>
        </div>
        <el-form :disabled="isDetail" v-if="isAddBankCardInfo" :model="bankCardInfoForm" :rules="bankCardInfoRules" ref="bankCardInfoForm" label-width="120px" label-position="left">
          <el-row type="flex" style="flex-wrap: wrap">
            <el-col :span="12">
              <el-form-item label="开户行" prop="bankName">
                <el-input v-model.trim="bankCardInfoForm.bankName" maxlength="32" show-word-limit placeholder="请输入"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="开户名" prop="cardholder">
                <el-input v-model.trim="bankCardInfoForm.cardholder" maxlength="32" show-word-limit placeholder="请输入"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="账号" prop="cardNumber">
                <el-input v-model.trim="bankCardInfoForm.cardNumber" maxlength="30" show-word-limit placeholder="请输入"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="联行号" prop="bankCode">
                <el-input v-model.trim="bankCardInfoForm.bankCode" placeholder="请输入"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="attachmentJsonList">
                <ul slot="label" class="custom-label">
                  <li>开户行许可证/</li>
                  <li>收款银行确认函</li>
                </ul>
                <PictureCardCrypto accept=".doc,.docx,.pdf,.png,.jpeg,.jpg" :limit="2" :maxSize="2000 * 1024" v-model="bankCardInfoForm.attachmentJsonList"></PictureCardCrypto>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <div class="selected-info" v-if="distributorBankCardId && selectedDistributorBankCardInfo">已选择：{{ selectedDistributorBankCardInfo }}</div>
        <sy-table v-bind="bankCardTable" ref="bankCardTable" />
        <!--开票信息-->
        <div class="form-title" v-if="form.type === 'SALE_CREDIT' || type === 'SALE_CREDIT'">
          <strong class="form-title__text">开票信息</strong>
          <span class="form-title__desc">(开票信息需要在分销商管理维护好，这边不支持修改。)</span>
        </div>
        <el-form :disabled="true" :model="form" label-width="120px" label-position="left" v-if="form.type === 'SALE_CREDIT' || type === 'SALE_CREDIT'">
          <el-row :gutter="24" type="flex" style="flex-wrap: wrap">
            <el-col :span="12">
              <el-form-item label="公司名称">
                <el-input v-model="form.invoiceTitleVO.title"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="开户行">
                <el-input v-model="form.invoiceTitleVO.bankName"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="开户行账号">
                <CryptoBlock tag-type="input" :biz-id="form.invoiceTitleVO.id" fixed disabled detail-auth="/distributor-management/:decrypt-bank-account" biz-type="INVOICE_TITLE_BANK_ACCOUNT" v-model.trim="form.invoiceTitleVO.bankAccount" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="纳税人识别号">
                <el-input v-model="form.invoiceTitleVO.taxNo"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="企业地址">
                <CryptoBlock tag-type="input" :biz-id="form.invoiceTitleVO.id" fixed disabled detail-auth="/distributor-management/:decrypt-address-detail" biz-type="INVOICE_TITLE_ADDRESS" v-model.trim="form.invoiceTitleVO.address" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div class="drawer-footer">
        <button-hoc :loading="saveLoading" @click="saveContract" type="primary">确 定</button-hoc>
        <el-button @click="dialogFormVisible = false">取 消</el-button>
      </div>
    </el-drawer>
    <!--合同预览 -->
    <contract-preview :title="currentContractTitle" v-model="contractDialog" :list="currentContract" />
    <!--补充收付款信息 -->
    <markup-subject-information v-if="isAddBankCardInfoDialog" :visible.sync="isAddBankCardInfoDialog" :currentRow="currentRow" @success="getTableData"></markup-subject-information>
    <!--合同详情-->
    <contract-info :visible.sync="contractInfoDialog" v-if="contractInfoDialog" :currentRow="currentRow" @success="getTableData"></contract-info>
  </div>
</template>
<script>
import { createDistributorContract, createOffLineContract, createSaleCreditContract, getContractDetail, getDistributorChannel, listContract, revokeSigningContract, stopContract } from '@/api/distributorManagement/distributor/list';
import { distributorBankCardCreateByContractInfo, distributorBankCardListEnableByContractInfoId, distributorContractInfoListAllInfo, list } from '@/api/distributorManagement/enterprise-subject';
import { parseDefaultTime, parseTime } from '@/utils';
import PdfUpload from '@/components/Upload/PdfUpload';
import cloneDeep from 'lodash/cloneDeep';
import { distributorContractListOwnCompany } from '@/api/distributorManagement/statistic/list';
import { downloadOssFile } from '@/utils/downloadUrl';
import PictureCardCrypto from '@/components/Upload/PictureCardCrypto';
import MarkupSubjectInformation from '@/components/MarkupSubjectInformation';
import ContractPreview from '@/components/ContractPreview';
import ContractInfo from '@/components/ContractInfo';

export default {
  name: 'Contract',
  components: {
    ContractInfo,
    ContractPreview,
    PictureCardCrypto,
    PdfUpload,
    MarkupSubjectInformation
  },
  props: {
    id: String,
    isEdit: {
      type: Boolean,
      default: false
    },
    isDetail: {
      type: Boolean,
      default: false
    },
    isAudit: {
      type: Boolean,
      default: false
    },
    starLevel: String
  },
  data() {
    const formInit = {
      invoiceTitleVO: {},
      id: '',
      creditDate: null,
      otherChannelInfo: [],
      taxNo: '',
      contactRealName: '',
      company: '',
      merchantType: '',
      contactPhone: '',
      contactEmail: '',
      contactIdcardNo: '',
      contactAddress: '',
      contractUrl: '',
      remarks: ''
    }; // 展示form
    return {
      // 创建方式
      dataTypeOptions: [
        { label: '在线签署', value: 'ONLINE' },
        { label: '手动补录', value: 'OFFLINE' },
        { label: '飞书合同关联', value: 'FEISHU_REL' },
        { label: '手动认领合同', value: 'MANUALLY_RELEVANCY' }
      ],
      ownCompanyOptions: [],
      tableData: [],
      timeList: null,
      total: 0,
      dialogFormVisible: false,
      dialogSupplement: false,
      type: '', // 用户类型
      formInit,
      form: {
        contractName: '',
        invoiceTitleVO: {},
        appId: '',
        id: '', // 主体ID
        channelInfo: [],
        creditDate: null,
        otherChannelInfo: [],
        taxNo: '',
        contactRealName: '',
        company: '',
        merchantType: '',
        contactPhone: '',
        contactEmail: '',
        contactIdcardNo: '',
        contactAddress: '',
        contractUrl: '',
        remarks: ''
      }, // 展示form
      empForm: {
        id: '', // 主体ID
        channelInfo: [],
        otherChannelInfo: []
      }, // 展示form
      invoiceTitleShow: false, // 赊销开票显示
      formType: '', // 合同类型
      formTitle: '', // 合同标题
      channelOptions: [], // 授权渠道
      otherChannelOptions: [], // 其他授权渠道
      rules: {
        contractName: [{ required: true, message: '必填信息', trigger: 'blur' }],
        appId: [{ required: true, message: '必填信息', trigger: 'blur' }],
        channelInfo: [{ required: true, message: '必填信息', trigger: 'change' }],
        otherChannelInfo: [{ required: true, message: '必填信息', trigger: 'change' }],
        merchantType: [{ required: true, message: '必填信息', trigger: 'change' }],
        type: [{ required: true, message: '必填信息', trigger: 'change' }],
        contactRealName: [{ required: true, message: '必填信息', trigger: 'blur' }],
        company: [{ required: true, message: '必填信息', trigger: 'blur' }],
        taxNo: [{ required: true, message: '必填信息', trigger: 'blur' }],
        contactPhone: [{ required: true, message: '必填信息', trigger: 'blur' }],
        contactEmail: [{ required: true, message: '必填信息', trigger: 'blur' }],
        contactIdcardNo: [{ required: true, message: '必填信息', trigger: 'blur' }],
        contactAddress: [{ required: true, message: '必填信息', trigger: 'blur' }],
        creditDate: [{ required: true, message: '必填信息', trigger: 'change' }],
        contractUrl: [{ required: true, message: '必填信息', trigger: 'blur' }],
        remarks: [{ required: true, message: '必填信息', trigger: 'blur' }]
      },
      rules2: {
        channelInfo: [{ required: true, message: '必填信息', trigger: 'change' }],
        otherChannelInfo: [{ required: true, message: '必填信息', trigger: 'change' }]
      },
      saveLoading: false,
      currentContractInfoList: [],
      distributorBankCardList: [],
      channelInfo: [],
      distributorId: '', // 分销商id
      contractId: '', // 合同id
      invoiceInfoForm: {
        title: '',
        taxNo: '',
        bankName: '',
        bankAccount: '',
        mobile: '',
        contactName: '',
        contactPhone: '',
        email: '',
        address: '',
        areaCodes: [],
        addressDetail: ''
      },
      invoiceInfoRules: {
        title: [{ required: true, message: '必填信息', trigger: 'blur' }],
        taxNo: [{ required: true, message: '必填信息', trigger: 'blur' }],
        bankName: [{ required: true, message: '必填信息', trigger: 'blur' }],
        bankAccount: [{ required: true, message: '必填信息', trigger: 'blur' }],
        mobile: [{ required: true, message: '必填信息', trigger: 'blur' }],
        contactName: [{ required: true, message: '必填信息', trigger: 'blur' }],
        contactPhone: [{ required: true, message: '必填信息', trigger: 'blur' }],
        email: [{ required: true, message: '必填信息', trigger: 'blur' }],
        address: [{ required: true, message: '必填信息', trigger: 'blur' }],
        areaCodes: [{ required: true, message: '必填信息', trigger: 'blur' }],
        addressDetail: [{ required: true, message: '必填信息', trigger: 'blur' }]
      },
      bankCardInfoForm: {
        bankName: '',
        cardholder: '',
        cardNumber: '',
        bankCode: '',
        attachmentJsonList: []
      },
      bankCardInfoRules: {
        bankName: [{ required: true, message: '必填信息', trigger: 'blur' }],
        cardholder: [{ required: true, message: '必填信息', trigger: 'blur' }],
        cardNumber: [{ required: true, message: '必填信息', trigger: 'blur' }]
      },
      isAddBankCardInfo: false,
      addBankCardInfoLoading: false,
      invoiceTitleId: '', // 新增主体开票ID
      distributorBankCardId: '', // 收付款信息ID
      isAddBankCardInfoDialog: false,
      currentRow: {},
      personType: 'MAINLAND_PERSON', // 个人/企业
      contractDialog: false,
      currentContract: [],
      currentContractTitle: '',
      contractInfoDialog: false
    };
  },
  mounted() {
    this.init();
    this.initFilter();
  },
  methods: {
    init() {
      this.getById();
      this.getChannel();
    },
    // 获取结算方式名称
    getSettlementTypeName(val) {
      return window.$vue.$dict['soyoungzg_distributor_type'].find((item) => item.value === val)?.label || '';
    },
    // 渲染合同状态标签
    getTagType(status) {
      if (status === 'USE') {
        return 'success';
      }
      if (['STOPPED', 'END'].includes(status)) {
        return 'danger';
      }
      return 'info';
    },
    // 获取甲方主体
    getOwnCompanyOptions(type) {
      const params = {
        showType: 'USE'
      };
      // 采销&赊销主体信息过滤
      if (type === 'DISTRIBUTE' || type === 'SALE_CREDIT') {
        params.bizCode = 'default';
      }
      distributorContractListOwnCompany(params).then((response) => {
        this.ownCompanyOptions = response.data || [];
      });
    },
    getTableData() {
      const ref = this.$refs.tableRef;
      ref && ref.handlerSearch();
    },
    async getChannel() {
      try {
        const { data: channel } = await getDistributorChannel(this.id);
        this.channelOptions = channel.channelInfoVOList;
        this.otherChannelOptions = channel.otherChannelInfo;
      } catch (error) {
        this.$message.error('获取授权渠道出错');
      }
    },
    // 基础信息
    getById() {
      getContractDetail(this.id).then((res) => {
        // 分销商类型
        this.type = res.data.type;
        // 授权渠道
        this.form.channelInfo = res.data.channelInfoVOList.map((item) => {
          return item.channel;
        });
        this.channelInfo = this.form.channelInfo;
        // 个人/企业
        this.form.merchantType = res.data.merchantType;
        // 发票信息
        this.form.invoiceTitleVO = res.data.invoiceTitleVO || {};
        // 赊销
        if (res.data.type === 'SALE_CREDIT') {
          this.form.repaymentDay = res.data.repaymentDay;
          this.form.depositAmount = res.data.depositAmount;
          this.form.saleCreditAmount = res.data.saleCreditAmount;
        }
      });
    },
    // 取消收付款编辑
    cancelEditBankCardInfo() {
      this.isAddBankCardInfo = false;
      this.resetForm('bankCardInfoForm');
    },
    // 保存编辑收付款账号信息
    saveEditBankCardInfo() {
      this.$refs['bankCardInfoForm'].validate((valid) => {
        if (!valid) {
          return false;
        }
        if (!this.form.id) {
          this.$message.error('请先选择主体信息');
          return false;
        }
        this.addBankCardInfoLoading = true;
        const { bankCardInfoForm, id } = this;
        const params = {
          ...bankCardInfoForm,
          attachmentJsonList: bankCardInfoForm.attachmentJsonList.map((item) => item.url),
          distributorContractInfoId: this.form.id,
          distributorId: id
        };
        distributorBankCardCreateByContractInfo(params)
          .then(() => {
            this.$message.success('新增成功');
            this.isAddBankCardInfo = false;
            this.resetForm('bankCardInfoForm');
            this.getDistributorBankCardList();
          })
          .finally(() => {
            this.addBankCardInfoLoading = false;
          });
      });
    },
    // 获取当前主体下的收付款账号
    getDistributorBankCardList() {
      const { id, merchantType } = this.form;
      distributorBankCardListEnableByContractInfoId(id).then((res) => {
        this.distributorBankCardList = res.data || [];
        this.getDistributorContractInfoList(merchantType);
      });
    },
    resetForm(formName) {
      const ref = this.$refs[formName];
      ref && ref.resetFields();
    },
    openForm(type) {
      // 如果当前客户未进行星级评级，不允许操作
      const { starLevel = '' } = this;
      if (!starLevel) {
        this.$message.warning('签署合同之前，请选择该客户星级评级');
        return;
      }
      if (type === 'DISTRIBUTE') {
        this.formType = 'DISTRIBUTE';
        this.formTitle = '签署采销合同';
      }
      if (type === 'SALE_CREDIT') {
        this.formType = 'SALE_CREDIT';
        this.formTitle = '签署赊销合同';
      }
      if (type === 'OFFLINE') {
        this.formType = 'OFFLINE';
        this.formTitle = '录入线下签署合同';
      }
      // 获取甲方主体信息
      this.getOwnCompanyOptions(type);
      this.dialogFormVisible = true;

      // 主体信息
      this.getDistributorContractInfoList(this.form.merchantType);
      this.form.creditDate = null;
    },
    // 校验单个表单
    checkForm(formName) {
      return new Promise((resolve, reject) => {
        this.$refs[formName].validate((valid) => {
          if (valid) {
            resolve();
          } else {
            reject();
          }
        });
      });
    },
    // 校验全部表单
    checkAllForm() {
      return Promise.all([this.checkForm('form'), this.checkForm('invoiceInfoForm')]);
    },
    saveContract() {
      this.checkAllForm()
        .then(() => {
          const { distributorBankCardId, invoiceTitleId, isAddBankCardInfo } = this;
          if (!distributorBankCardId) {
            this.$message.error('请选择收付款信息');
            return;
          }
          if (isAddBankCardInfo) {
            this.$message.warning('请先完成收付款账号新增操作');
            return;
          }
          let request = '';
          const data = cloneDeep(this.form);
          if (this.formType === 'DISTRIBUTE') {
            request = createDistributorContract;
            ['type', 'contractUrl', 'remarks', 'contractName'].forEach((item) => {
              delete data[item];
            });
          }
          if (this.formType === 'SALE_CREDIT') {
            request = createSaleCreditContract;
            ['type', 'contractUrl', 'remarks', 'contractName'].forEach((item) => {
              delete data[item];
            });
          }
          if (this.formType === 'OFFLINE') {
            request = createOffLineContract;
            data.authChannels = data.channelInfo;
            ['contactRealName', 'contactIdcardNo', 'contactAddress', 'contactPhone', 'contactEmail', 'channelInfo'].forEach((item) => {
              delete data[item];
            });
          }
          console.log('data===', data);
          const [startDate, endDate] = this.form.creditDate;
          data.startDate = parseDefaultTime(startDate);
          data.endDate = parseDefaultTime(endDate);
          if (data.merchantType === 'PERSONAL') {
            delete data.company;
            delete data.taxNo;
            for (const item of this.distributorContractInfoList) {
              if (data.contactRealName === item.id) {
                data.contactRealName = item.contactRealName;
                break;
              }
            }
          } else {
            for (const item of this.distributorContractInfoList) {
              if (data.company === item.id) {
                data.company = item.company;
                break;
              }
            }
          }
          ['channelInfoVOList', 'creditDate', 'depositAmount', 'invoiceTitleVO', 'repaymentDay', 'saleCreditAmount', 'merchantTypeName', 'typeName'].forEach((item) => {
            delete data[item];
          });
          data.distributorId = this.id;
          data.distributorBankCardId = distributorBankCardId;
          data.invoiceTitleId = invoiceTitleId;
          data.distributorContractInfoId = this.form.id;
          this.saveLoading = true;
          request(data)
            .then((res) => {
              if (res.code === '0') {
                this.getTableData();
                this.dialogFormVisible = false;
                this.$message({
                  type: 'success',
                  message: '合同发起成功!'
                });
              }
            })
            .finally(() => {
              this.saveLoading = false;
            });
        })
        .catch(() => {
          console.log('表单校验未通过');
        });
    },
    closeDialog() {
      this.resetForm('form');
      this.resetForm('invoiceInfoForm');
      this.resetForm('bankCardInfoForm');
      this.form.creditDate = '';
      this.distributorBankCardList = [];
    },
    // 复制合同链接
    onCopy: function (e) {
      const input = document.createElement('input');
      input.value = e;
      document.body.appendChild(input);
      input.select();
      document.execCommand('Copy');
      document.body.removeChild(input);
      this.$message.success('合同链接' + e + '复制成功');
    },
    revokeContract(id) {
      this.$confirm('此操作将撤销签署该合同, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          revokeSigningContract(id).then((res) => {
            if (res.code === '0') {
              this.getTableData();
              this.$message({
                type: 'success',
                message: '撤销签署成功!'
              });
            }
          });
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
    },
    stopContract(id) {
      this.$confirm('此操作将终止该合同, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          stopContract(id).then((res) => {
            if (res.code === '0') {
              this.getTableData();
              this.$message({
                type: 'success',
                message: '终止合同成功!'
              });
            }
          });
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
    },
    // 查看合同
    getContract(row, title) {
      const { contractName = '', contractUrl = '' } = row;
      if (!contractUrl) return;
      this.currentContractTitle = title;
      this.contractDialog = true;
      this.currentContract = [{ name: contractName, url: contractUrl }];
    },
    // 查看合同附件
    getContracts(row) {
      this.currentContractTitle = '查看合同附件';
      this.contractDialog = true;
      this.currentContract = row.contractCauses.map((item) => {
        return {
          name: item.fileName,
          url: item.ossFileUrl
        };
      });
    },
    // 获取分销商主体信息
    getDistributorContractInfoList(val) {
      distributorContractInfoListAllInfo({ distributorId: this.id }).then(({ data = [] }) => {
        this.currentContractInfoList = data.filter((item) => item.distributorContractInfoVO.merchantType === val);
      });
    },
    // 改变分销商主体信息
    changeDistributorContract(val) {
      this.getDistributorContractInfoList(val);
      this.clearForm();
    },
    clearForm() {
      ['id', 'taxNo', 'contactRealName', 'contactIdcardNo', 'contactAddress', 'contactPhone', 'contactEmail', 'company'].forEach((item) => {
        this.form[item] = '';
      });
      this.resetForm('invoiceInfoForm');
      this.invoiceTitleId = '';
      this.distributorBankCardId = '';
      this.distributorBankCardList = [];
    },
    companyChange(val) {
      if (!val) {
        this.clearForm();
        return;
      }
      const index = this.distributorContractInfoList.findIndex((item) => item.id === val);
      const currentInfo = this.currentContractInfoList[index];
      const { distributorContractInfoVO, invoiceTitleVO, distributorBankCardVOList } = currentInfo;
      this.personType = distributorContractInfoVO.personType || 'MAINLAND_PERSON';
      // 基础信息
      ['id', 'taxNo', 'contactRealName', 'contactIdcardNo', 'contactAddress', 'contactPhone', 'contactEmail'].forEach((item) => {
        this.form[item] = distributorContractInfoVO[item];
      });
      // 主体开票信息
      ['title', 'taxNo', 'bankName', 'bankAccount', 'mobile', 'contactName', 'contactPhone', 'email', 'address', 'areaCodes', 'addressDetail'].forEach((item) => {
        this.invoiceInfoForm[item] = invoiceTitleVO[item];
      });
      this.invoiceTitleId = invoiceTitleVO.id;
      //  收付款信息
      this.distributorBankCardList = distributorBankCardVOList;
    },
    contactRealNameChange(val) {
      if (!val) {
        this.clearForm();
        return;
      }
      const index = this.distributorContractInfoList.findIndex((item) => item.id === val);
      const currentInfo = this.currentContractInfoList[index];
      const { distributorContractInfoVO, invoiceTitleVO, distributorBankCardVOList } = currentInfo;
      this.personType = distributorContractInfoVO.personType || 'MAINLAND_PERSON';
      // 基础信息
      ['id', 'contactIdcardNo', 'contactAddress', 'contactPhone', 'contactEmail'].forEach((item) => {
        this.form[item] = distributorContractInfoVO[item];
      });
      // 主体开票信息
      ['title', 'taxNo', 'bankName', 'bankAccount', 'mobile', 'contactName', 'contactPhone', 'email', 'address', 'areaCodes', 'addressDetail'].forEach((item) => {
        this.invoiceInfoForm[item] = invoiceTitleVO[item];
      });
      this.invoiceTitleId = invoiceTitleVO.id;
      //  收付款信息
      this.distributorBankCardList = distributorBankCardVOList;
    },
    toDistributorContractInfo() {
      this.dialogFormVisible = false;
      this.$emit('changeTab', 'contractInfo');
    },
    linkchannel() {
      this.dialogFormVisible = false;
      this.$emit('changeTab', 'basic');
    },
    // 跳转到授权书管理
    jumpCertificate(id) {
      this.$router.push({
        path: '/brand/authority/certificate/list',
        query: {
          distributorContractId: id,
          signStatus: 'SUCCESS'
        }
      });
    },
    // 查看年框政策,一个合同对应多个年框政策的情况下，跳转到年框政策台账列表，带上合同id
    jumpYearlyPolicy(row) {
      const { id, izYearlyContract, distributorYearlyPolicyIds = [] } = row;
      if (izYearlyContract === '1' && distributorYearlyPolicyIds.length > 1) {
        this.$router.push(`/yearly-policy-management/policy-list?relContractId=${id}`);
      } else {
        const policyId = distributorYearlyPolicyIds[0];
        this.$router.push(`/yearly-policy-management/policy-list/detail/${policyId}`);
      }
    },
    // 补充收付款信息
    handleMakeUpPay(row) {
      this.currentRow = row;
      this.isAddBankCardInfoDialog = true;
    },
    // 初始化筛选
    initFilter() {
      const { state, appId, distributorContractInfoId = '' } = this.$route.query;
      if (distributorContractInfoId) {
        this.$refs.tableRef.setFiltersValue(distributorContractInfoId, 'distributorContractInfoId');
      }
      if (appId && appId !== 'undefined') {
        this.$refs.tableRef.setFiltersValue(appId, 'appId');
      }
      if (state === 'valid') {
        // 有效 ： 合同未生效、合同生效中
        this.$refs.tableRef.setFiltersValue(['NOT_START', 'USE'], 'statusList');
        this.$refs.tableRef.setFiltersValue('FINISH', 'signStatus');
      }
      this.$router.push({ query: {} });
      this.getTableData();
    },
    //  跳转飞书合同
    jumpFeishuContract(externalFeiShuContractId) {
      if (!externalFeiShuContractId) {
        return;
      }
      const url = `https://contract.feishu.cn/management/all/detail/${externalFeiShuContractId}`;
      window.open(url, '_blank');
    },
    // 复制主体信息
    handleCopyImproveInformation(row) {
      const { platformOwner, buildSelfPartyRelationVOList = [] } = row;
      let value = '';
      if (platformOwner === 'SYZG_CHANNEL') {
        value = `${row.sapChannelCode}-${row.sapChannelCodeName}-${row.omsChannelId}-${row.omsChannelIdName}`;
      } else if (buildSelfPartyRelationVOList.length) {
        // 换行显示
        value = buildSelfPartyRelationVOList
          .map((item) => `${item.sapChannelCode}-${item.sapChannelName}-${item.omsChannelId}-${item.omsChannelName}`)
          .toString()
          .replace(/,/g, '\n');
      }
      this.$copyText(value)
        .then(() => {
          this.$message.success('复制成功');
        })
        .catch(() => {
          this.$message.info(`复制失败,请手动复制`);
        });
    },
    // 下载合同
    downloadContract({ contractUrl, contractName }) {
      if (!contractUrl) {
        return;
      }
      this.$confirm(`确认下载该合同吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        downloadOssFile(contractUrl, contractName);
      });
    },
    // 查看/编辑合同信息
    handleContractInfo(row, type) {
      if (!type || !['detail', 'edit'].includes(type)) {
        return;
      }
      this.currentRow = { ...row, operateType: type };
      this.contractInfoDialog = true;
      this.contractInfoDialog = true;
    },
    // 是否隐藏生成年框政策台账按钮
    isHideGeneratePolicyButton(row) {
      const { izYearlyContract, distributorYearlyPolicyIds = [], yearlyBrandIds, status } = row;
      if (!(izYearlyContract === '1' && distributorYearlyPolicyIds.length === 0)) {
        return true;
      }
      return !(distributorYearlyPolicyIds.length === 0 && yearlyBrandIds && ['USE', 'NOT_START'].includes(status));
    }
  },
  watch: {
    isAddBankCardInfoDialog(val) {
      if (!val) {
        this.getTableData();
      }
    }
  },
  computed: {
    selectedDistributorBankCardInfo() {
      const { distributorBankCardId, distributorBankCardList } = this;
      if (!distributorBankCardId) {
        return '';
      }
      const obj = distributorBankCardList.find((item) => item.id === distributorBankCardId);
      return obj ? `${obj.cardholder}-${obj.bankName}-${obj.cardNumber}` : '';
    },
    distributorContractInfoList() {
      return this.currentContractInfoList.map((item) => item.distributorContractInfoVO);
    },
    table() {
      const that = this;
      return {
        initSearch: false,
        tip: [
          {
            text: '合同签订注意事项：E签宝企业进行注册后，如果营业执照是注册时间是在近期的话，E签宝那边需要3-5个工作日审核，所以会签订不了合同'
          }
        ],
        filters: [
          {
            tag: 'el-input',
            prop: 'id',
            label: '合同ID',
            bind: {
              placeholder: '请输入合同ID'
            }
          },
          {
            tag: 'el-input',
            prop: 'externalContractNumber',
            label: '飞书合同编号',
            bind: {
              placeholder: '请输入飞书合同编号',
              clearable: true
            }
          },
          {
            tag: 'sy-date-picker',
            prop: ['startValidDate', 'endValidDate'],
            label: '合同有效期',
            bind: {
              bind: {
                type: 'daterange',
                rangeSeparator: '至',
                startPlaceholder: '开始时间',
                endPlaceholder: '结束时间',
                valueFormat: 'timestamp',
                defaultTime: ['00:00:00', '23:59:59']
              }
            }
          },
          {
            tag: 'sy-select',
            prop: 'distributorContractInfoId',
            label: '乙方主体',
            bind: {
              placeholder: '请选择乙方主体',
              filterable: true,
              options: () =>
                list({ distributorId: this.id }).then((res) =>
                  res.data.map((item) => {
                    return {
                      label: item.merchantType === 'ENTERPRISE' ? item.company : item.contactRealName,
                      value: item.id
                    };
                  })
                )
            }
          },
          {
            tag: 'sy-select',
            prop: 'appId',
            label: '甲方主体',
            bind: {
              placeholder: '请选择',
              filterable: true,
              options: async () => {
                const res = await distributorContractListOwnCompany();
                return res.data;
              },
              optionsProps: {
                label: 'company',
                value: 'id'
              }
            }
          },
          {
            tag: 'sy-select',
            prop: 'dataType',
            label: '创建方式',
            bind: {
              placeholder: '请选择创建方式',
              filterable: true,
              options: that.dataTypeOptions
            }
          },
          {
            tag: 'sy-select',
            prop: 'signStatus',
            label: '签署状态',
            bind: {
              placeholder: '请选择',
              filterable: true,
              options: [
                { label: '全部', value: '' },
                { label: '待签署', value: 'WAITING' },
                { label: '拒签', value: 'REJECT' },
                { label: '撤销', value: 'CANCEL' },
                { label: '签署完成', value: 'FINISH' }
              ]
            }
          },
          {
            tag: 'sy-select',
            prop: 'statusList',
            label: '合同状态',
            bind: {
              placeholder: '请选择',
              filterable: true,
              multiple: true,
              options: [
                { label: '全部', value: '' },
                { label: '合同未生效', value: 'NOT_START' },
                { label: '合同生效中', value: 'USE' },
                { label: '合同过期', value: 'END' },
                { label: '合同终止', value: 'STOPPED' }
              ]
            }
          },
          {
            tag: 'sy-select',
            prop: 'platformOwner',
            label: '主体平台归属',
            bind: {
              filterable: true,
              placeholder: '请选择',
              options: [...that.$dict['distributor_contract_platform_owner_type'], { label: '未完善', value: 'NONE' }]
            }
          }
        ],
        btns() {
          return [
            {
              hide: that.type !== 'DISTRIBUTE',
              text: '签署采销合同',
              type: 'primary',
              disabled: !that.isEdit,
              call: () => that.openForm('DISTRIBUTE')
            },
            {
              hide: true,
              text: '签署赊销合同',
              type: 'primary',
              call: () => that.openForm('SALE_CREDIT')
            },
            {
              hide: process.env.VUE_APP_ENV_CONFIG === 'prod',
              text: '线下已签署,补签合同',
              type: 'primary',
              code: '/distributor-management/etroactive-contract',
              call: () => that.openForm('OFFLINE')
            }
          ];
        },
        columns() {
          return [
            {
              label: '合同信息',
              width: 160,
              prop: 'render',
              itemBind: {
                fixed: 'left'
              },
              render: (h, { row }) => (
                <div class="link-type" onClick={() => that.handleContractInfo(row, 'detail')}>
                  <p class="leading-small">{row.dataType === 'ONLINE' ? '水羊直供产品经销合同' : row.contractName}</p>
                  <p>{row.id}</p>
                  {row.contractRelationNum && (
                    <p>
                      关联合同{row.contractRelationNum}个<span class="m-l-4">></span>
                    </p>
                  )}
                  <common-copy-text desc="合同ID" btnType="primary" value={row.id} />
                </div>
              )
            },
            {
              label: '签署主体',
              prop: 'render',
              itemBind: {
                minWidth: 180
              },
              render: (h, { row }) => (
                <div v-frag>
                  <p>甲方：{row.ownCompanyName || '-'}</p>
                  {row.contractParticipantList && row.contractParticipantList.length
                    ? row.contractParticipantList.map((item, index) => (
                        <p>
                          乙方{row.contractParticipantList.length > 1 ? Number(index + 1) : ''}：{item.company}
                        </p>
                      ))
                    : '乙方：-'}
                </div>
              )
            },
            {
              label: '合作状态',
              width: 110,
              render: (h, { row }) => (
                <div v-frag>
                  <p>
                    <el-tag type={that.getTagType(row.status)}>{row.statusName}</el-tag>
                  </p>
                  {row.startDate ? <span>{parseTime(row.startDate, '{y}-{m}-{d}')}</span> : ''} 至 {row.endDate ? <span>{parseTime(row.endDate, '{y}-{m}-{d}')}</span> : ''}
                </div>
              )
            },
            {
              label: '签署状态',
              prop: 'signStatusName',
              width: 80
            },
            {
              label: '授权渠道',
              type: 'array',
              prop: 'authChannelNames',
              itemBind: {
                minWidth: 100
              }
            },
            {
              label: '渠道主数据',
              itemBind: {
                minWidth: 150
              },
              render: (h, { row }) => (
                <div v-frag>
                  {row.contractParticipantList && row.contractParticipantList.length
                    ? row.contractParticipantList.map((item, index) =>
                        item.channelRelationList.length ? (
                          <div v-frag>
                            {row.contractParticipantList.length > 1 && <span>{'乙方' + Number(index + 1)}</span>}
                            {item.channelRelationList.map((channel) => (
                              <div>
                                <span>{channel.partyRelation.sapChannelName || '-'}</span>
                                {channel.brandList && channel.brandList.length && <span>-</span>}
                                {channel.brandList &&
                                  channel.brandList.length &&
                                  channel.brandList.map((brand, bIndex) => (
                                    <span>
                                      {brand.name}
                                      {bIndex < channel.brandList.length - 1 ? '、' : ''}
                                    </span>
                                  ))}
                              </div>
                            ))}
                          </div>
                        ) : (
                          '-'
                        )
                      )
                    : '-'}
                </div>
              )
            },
            {
              label: '合作信息',
              width: 140,
              render: (h, { row }) => (
                <div v-frag>
                  {row.classificationName && <p>{row.classificationName}</p>}
                  <p>
                    {row.izFrameworkAgreement === '1' ? (
                      <div v-frag>
                        <span>框架合同</span>
                        {row.izYearlyContract === '1' && <span>-年框合同</span>}
                      </div>
                    ) : (
                      '非框架合同'
                    )}
                  </p>
                  {row.settlementType && <p>{that.getSettlementTypeName(row.settlementType)}</p>}
                </div>
              )
            },
            {
              label: '合同创建信息',
              itemBind: {
                minWidth: 100
              },
              render: (h, { row }) => (
                <div v-frag>
                  <p>{row.dataTypeName}</p>
                  {row.externalContractCreateName ? <p>申请人：{row.externalContractCreateName}</p> : ''}
                  {row.externalContractNumber && (
                    <a class="link-type" title="飞书合同编号" onClick={() => that.jumpFeishuContract(row.externalFeiShuContractId)}>
                      {row.externalContractNumber}
                    </a>
                  )}
                </div>
              )
            },
            {
              label: '关联授权书',
              width: 100,
              render: (h, { row }) => (
                <div v-frag>
                  {row.licenses ? (
                    <span class="link-type" onClick={() => that.jumpCertificate(row.id)}>
                      {row.licenses.length}
                    </span>
                  ) : (
                    0
                  )}
                </div>
              )
            },
            {
              label: '操作',
              width: 120,
              itemBind: {
                fixed: 'right'
              },
              render: (h, { row }) => (
                <div v-frag>
                  {row.dataType === 'MANUALLY_RELEVANCY' || row.dataType === 'FEISHU_REL' ? (
                    <div v-frag>
                      <div>
                        <Authority auth="complete-contract-information">
                          <el-button type="text" onClick={() => that.handleContractInfo(row, 'edit')}>
                            完善合同信息
                          </el-button>
                        </Authority>
                      </div>
                      {!that.isHideGeneratePolicyButton(row) && (
                        <div>
                          <Authority auth="yearly-policy-management-policy-list-add">
                            <el-button type="text" onClick={() => that.$router.push(`/yearly-policy-management/policy-list/created?contractId=${row.id}`)}>
                              生成年框政策台账
                            </el-button>
                          </Authority>
                        </div>
                      )}
                      {row.izYearlyContract === '1' && row.distributorYearlyPolicyIds && row.distributorYearlyPolicyIds.length && (
                        <div>
                          <Authority auth="yearly-policy-management-policy-list-detail">
                            <el-button type="text" onClick={() => that.jumpYearlyPolicy(row)}>
                              查看年框政策
                            </el-button>
                          </Authority>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div v-frag>
                      {row.signStatus === 'WAITING' && (
                        <ul>
                          <li>
                            <el-button type="text" onClick={() => that.onCopy(row.shortSignUrl)}>
                              复制签署链接
                            </el-button>
                          </li>
                          <li>
                            <el-button type="text" disabled={!that.isEdit} onClick={() => that.revokeContract(row.id)}>
                              撤销签署
                            </el-button>
                          </li>
                        </ul>
                      )}
                      {row.signStatus === 'FINISH' && ['USE', 'NOT_START'].includes(row.status) && (
                        <div>
                          <el-button type="text" disabled={!that.isEdit} onClick={() => that.stopContract(row.id)}>
                            终止合同
                          </el-button>
                        </div>
                      )}
                    </div>
                  )}
                  {row.contractUrl && (
                    <ul>
                      <li>
                        <el-button type="text" onClick={() => that.getContract(row, '查看合同')}>
                          查看合同
                        </el-button>
                      </li>
                      <li>
                        <Authority auth="distributor-management-statistic-download-contract">
                          <button-hoc type="text" onClick={() => that.downloadContract(row)}>
                            下载合同
                          </button-hoc>
                        </Authority>
                      </li>
                    </ul>
                  )}
                  {row.contractCauses && row.contractCauses.length && (
                    <div>
                      <el-button type="text" onClick={() => that.getContracts(row)}>
                        查看合同附件
                      </el-button>
                    </div>
                  )}
                  {row.izSupplementBankCard !== '1' && (
                    <div>
                      <Authority auth="improve-the-receipt-and-payment-information">
                        <el-button type="text" onClick={() => that.handleMakeUpPay(row)}>
                          补充收付款信息
                        </el-button>
                      </Authority>
                    </div>
                  )}
                </div>
              )
            }
          ];
        },
        async search({ filtersValue, pageFilter }) {
          const res = await listContract({
            data: {
              distributorId: that.id,
              ...filtersValue
            },
            ...pageFilter
          });
          const { list = [], total = 0 } = res.data || {};
          return {
            list: list.map((item) => {
              return { ...item, isShowMore: false };
            }),
            total
          };
        }
      };
    },
    bankCardTable() {
      const that = this;
      return {
        data: this.distributorBankCardList,
        bind: {
          height: 300
        },
        columns() {
          return [
            {
              prop: 'cardholder',
              label: '户名'
            },
            {
              prop: 'bankName',
              label: '开户行'
            },
            {
              prop: 'cardNumber',
              label: '账户'
            },
            {
              label: '操作',
              width: 120,
              type: 'btns',
              btns({ row }) {
                return [
                  {
                    text: row.id === that.distributorBankCardId ? '已选择' : '选择',
                    type: 'text',
                    call() {
                      if (row.id === that.distributorBankCardId) {
                        return;
                      }
                      that.distributorBankCardId = row.id;
                    }
                  }
                ];
              }
            }
          ];
        }
      };
    },
    contactIdcardNoName() {
      const { personType } = this;
      const { merchantType } = this.form;
      const before = merchantType === 'ENTERPRISE' ? '法定代表人' : '联系人';
      const after = personType === 'MAINLAND_PERSON' ? '身份证' : '护照';
      return `${before}${after}`;
    }
  }
};
</script>
<style lang="scss" scoped>
::v-deep .el-table th > .cell {
  white-space: pre-line;
}

.custom-label {
  li {
    line-height: 20px;
  }
}
.form {
  ::v-deep {
    .el-input,
    .el-select,
    .el-textarea,
    .el-date-editor {
      width: 240px;
    }
  }
  &-title {
    display: flex;
    align-items: center;
    padding: 12px 0;
    &__text {
      display: flex;
      align-items: center;
      min-width: 120px;
      font-size: 14px;
      color: var(--color-text-primary);
      font-weight: bold;
      &:before {
        content: '';
        height: 10px;
        width: 2px;
        background: var(--color-primary);
        margin-right: 4px;
      }
    }
    &__desc {
      margin-left: 4px;
      font-size: 12px;
      color: var(--color-info);
      font-weight: normal;
    }
  }
}
.selected-info {
  font-size: 14px;
  color: var(--color-text-primary);
  font-weight: bold;
}
::v-deep {
  .contract-main-data {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    .main-data-btns {
      padding-left: 4px;
      display: flex;
      flex-direction: column;
      .el-button {
        margin: 4px 0 0 0;
        font-size: 16px;
      }
    }
  }
  .sap-channel-name-container {
    p {
      display: none;
      // 前3个元素显示
      &:nth-of-type(-n + 3) {
        display: block;
      }
    }
  }
}
</style>
