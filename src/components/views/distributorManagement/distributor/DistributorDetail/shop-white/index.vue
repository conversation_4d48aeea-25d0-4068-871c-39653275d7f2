<template>
  <div v-loading="loading">
    <instructions show-title is-hide>
      <template slot="title">
        <ul class="tip-view">
          <li>店铺白名单：主要是指客户在第三方平台上开的店铺加入了我们的白名单后会被认定是合规的店铺；</li>
          <li>
            <span>分销商的经营渠道有：</span>
            <el-tag class="tag" v-for="(item, index) in tags" :key="index" size="mini">
              {{ item.channelName }}
            </el-tag>
            <el-button v-if="!isDetail" style="padding: 0" type="text" @click="changeTab('basic')">渠道不对？点击修改渠道</el-button>
          </li>
        </ul>
      </template>
    </instructions>
    <div class="btn-view" v-if="!isDetail">
      <Authority auth="/white-list/:import">
        <el-button type="primary" @click="onImport">导入店铺白名单</el-button>
      </Authority>
    </div>
    <!-- 列表展示 -->
    <table-exhibition @query="query" :table="table" :options="options" ref="table"> </table-exhibition>
    <dialog-import v-bind="importOptions" @onSuccess="query" ref="dialogImport"></dialog-import>
  </div>
</template>

<script>
import TableExhibition from '@/components/Table/TableExhibition';
import { distributorWebsiteWhitelistCreateInBatch } from '@/api/white-list';
import { listPageByDistributor_shopWhite } from '@/api/distributorManagement/distributor/list';
import DialogImport from '@/components/Common/Import/dialogImport';
export default {
  // 分销商信息店铺白名单
  name: 'distributor-shopWhite',
  components: { TableExhibition, DialogImport },
  data() {
    return {
      table: {},
      loading: false
    };
  },
  mounted() {
    this.params = {
      data: {
        distributorId: this.id,
        status: 'PASS'
      },
      ...this.$refs.table.getParams()
    };
    this.query();
  },
  props: {
    isDetail: Boolean,
    id: String,
    // 经营渠道
    channelInfoVOList: {
      type: Array,
      default: () => []
    },
    // 其他渠道
    otherChannelInfo: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    importOptions() {
      return {
        title: '店铺白名单导入',
        createInBatch: distributorWebsiteWhitelistCreateInBatch,
        action: process.env.VUE_APP_BASE_URL + '/soyoungzg/api/distributorWebsiteWhitelist/batchImport',
        downloadFailRequest: process.env.VUE_APP_BASE_URL + '/soyoungzg/api/distributorWebsiteWhitelist/downloadImportResult',
        templateUrl: process.env.VUE_APP_MUSHUROOMFILEURL + '/static/file/soyoung-zg/template/新店铺白名单导入模板.xlsx',
        data: {
          distributorId: this.id
        }
      };
    },
    tags() {
      if (this.channelInfoVOList.length) {
        return this.channelInfoVOList.map((item) => {
          if (item.channel === 'OTHER') {
            return {
              ...item,
              channelName: `其他-${this.otherChannelInfo.join(',')}`
            };
          }
          return item;
        });
      }
      return [];
    },
    options() {
      return [
        {
          prop: 'platformName',
          label: '平台名称'
        },
        {
          prop: 'platformShopName',
          label: '店铺名称'
        },
        {
          prop: 'platformShopId',
          label: '店铺ID'
        },
        {
          prop: 'shopWebsite',
          label: '店铺网址'
        },
        {
          prop: 'violateNum',
          label: '违规总次数',
          formatter: (r, c, v = 0) => v
        }
      ];
    }
  },
  methods: {
    query(params = {}) {
      Object.assign(this.params, params);
      this.loading = true;
      listPageByDistributor_shopWhite(this.params)
        .then((res) => {
          this.table = res?.data ?? {};
        })
        .finally(() => {
          this.loading = false;
        });
    },
    changeTab(name) {
      this.$emit('changeTab', name);
    },
    onImport() {
      this.$refs.dialogImport.open();
    }
  }
};
</script>

<style lang="scss" scoped>
.tip-view {
  width: 100%;
  li {
    line-height: 150%;
    &:not(:last-child) {
      margin-bottom: 4px;
    }
  }
}
.btn-view {
  margin: 10px 0;
}
.tag {
  margin-right: 4px;
}
.channel-box {
  display: flex;
  margin-top: 10px;
}
</style>
