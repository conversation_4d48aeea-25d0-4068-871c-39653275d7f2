<template>
  <div v-frag>
    <div class="first-row">
      <ul>
        <li><sy-address :bind="{ disabled: true }" v-model="firstRow.areaCodes" /></li>
        <li><el-input v-model="firstRow.addressDetail" type="textarea" :autosize="{ minRows: 2, maxRows: 4 }" readonly /></li>
      </ul>
      <button-hoc type="text" @click="handleAddress('detail')">查看地址</button-hoc>
      <button-hoc type="text" @click="handleAddress('edit')">编辑地址</button-hoc>
    </div>

    <el-dialog :visible.sync="visible" :title="title" width="50%">
      <el-table :data="tableData" size="small">
        <el-table-column type="index" label="序号" width="80"></el-table-column>
        <el-table-column prop="area" label="所在地区" width="350">
          <template slot-scope="scope">
            <sy-address style="width: 100%" :bind="{ clearable: true, disabled: isDetail }" v-model="scope.row.areaCodes" />
          </template>
        </el-table-column>
        <el-table-column prop="addressDetail" label="详细地址">
          <template slot-scope="scope">
            <el-input v-model="scope.row.addressDetail" type="textarea" maxlength="50" show-word-limit :autosize="{ minRows: 2, maxRows: 4 }" :readonly="isDetail" placeholder="请输入详细地址"></el-input>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="80">
          <template slot-scope="scope">
            <el-button type="text" @click="deleteRow(scope.row)" v-if="isEdit && scope.$index > 0">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <template v-if="isEdit">
        <el-button type="primary" @click="addRow" style="margin-top: 16px">添加地址</el-button>
        <div slot="footer" class="dialog-footer">
          <el-button @click="close">取消</el-button>
          <button-hoc type="primary" @click="submit">确定</button-hoc>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { isEmpty, cloneDeep } from 'lodash';

export default {
  name: 'AddressDetailList',
  props: {
    value: {
      type: Array,
      default: () => [{ areaCodes: [], addressDetail: '' }]
    }
  },
  data() {
    return {
      type: 'detail',
      visible: false,
      tableData: []
    };
  },
  watch: {
    visible(val) {
      if (val) {
        this.tableData = cloneDeep(this.value);
      }
    }
  },
  computed: {
    isEdit() {
      return this.type === 'edit';
    },
    isDetail() {
      return this.type === 'detail';
    },
    title() {
      return this.isDetail ? '查看分销商地址' : '编辑分销商地址';
    },
    // 截取数组第一条
    firstRow() {
      return this.value[0] || {};
    }
  },
  methods: {
    handleAddress(type) {
      this.type = type;
      this.visible = true;
    },
    close() {
      this.visible = false;
    },
    deleteRow(row) {
      this.tableData.splice(this.tableData.indexOf(row), 1);
    },
    addRow() {
      this.tableData.push({
        areaCodes: [],
        addressDetail: ''
      });
    },
    // 验证数据
    validateData(data) {
      return data.some((item) => isEmpty(item.areaCodes) || isEmpty(item.addressDetail));
    },
    submit() {
      const { tableData } = this;
      if (this.validateData(tableData)) {
        return this.$message.error('请填写完整地址');
      }
      this.$emit('input', tableData);
      this.close();
    }
  }
};
</script>
<style lang="less" scoped>
.first-row {
  display: flex;
  align-items: flex-start;
  ul {
    margin-right: 12px;
  }
  li {
    width: 300px;
    & > * {
      width: 100%;
    }
    &:not(:last-child) {
      margin-bottom: 12px;
    }
  }
}
</style>
