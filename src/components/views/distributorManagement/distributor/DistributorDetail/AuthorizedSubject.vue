<!-- 分销商管理 - 主体信息 -->
<template>
  <div>
    <instructions show-title is-hide>
      <template slot="title">
        <ul class="tips-text">
          <li>
            什么情况需要走客户主数据新增流程:
            <el-tooltip placement="top">
              <div slot="content">
                <p>什么情况需要走主数据流程:当该分销商有签订新主体的合同,那么需要走主数据流程在SAP新增客户主数据</p>
                <p>什么是新主体合同:就是合同里面签订的甲方主体和乙方主体有变更的情况下,需要新增客户主数据</p>
                <p>什么情况不需要走主数据流程:跟客户用相同的主体【续签】合同司、签订补充协议、签订年框等;</p>
              </div>
              <el-button type="text">查看说明</el-button>
            </el-tooltip>
          </li>
          <li>什么情况可以修改主体信息:当有合同被占用的话,是不能修改主体信息的,可以修改【对应主数据渠道】信息、当主体下面没有合同是可以修改主体信息的;</li>
        </ul>
      </template>
    </instructions>
    <sy-normal-table v-bind="table" ref="basicTable" v-el-horizontal-scroll />
    <!-- 客户主体信息填写链接 -->
    <el-dialog title="客户主体信息填写链接" class="link-dialog" :visible.sync="linkDialogVisible" width="900px" append-to-body>
      <div class="link-dialog-title">小程序端填写页面地址</div>
      <div class="info-container">
        <div class="img-box">
          <img :src="mpCode" alt class="code" />
        </div>
        <div class="detail">
          <div class="input-container">
            <el-input id="h5CodeUrl" readonly v-model="h5CodeUrl"></el-input>
            <el-button data-clipboard-target="#h5CodeUrl" ref="copyBtn" type="primary">复制</el-button>
          </div>
        </div>
      </div>
      <div class="link-dialog-title">PC官网端填写页面地址</div>
      <div class="info-container">
        <el-input id="pcUrl" readonly v-model="PCurl"></el-input>
        <el-button data-clipboard-target="#pcUrl" ref="copyBtn1" type="primary">复制</el-button>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="linkDialogVisible = false">取 消</el-button>
      </span>
    </el-dialog>
    <!-- 分销商主体信息管理 -->
    <distributor-subject-manage ref="distributorSubjectManage" v-bind="$props" :distributorId="distributorId" @success="refreshBasicTable" />
    <!-- 完善主数据渠道信息 -->
    <improve-main-data-information :currentRow="currentRow" v-if="improveInformationDialog" :show.sync="improveInformationDialog" @close="refreshBasicTable" />
  </div>
</template>

<script>
import { externalChannelListSap, externalChannelList } from '@/api/distributorManagement/distributor/list';
import { distributorContractInfoListPage, getBasicInfo, listSapChannelInfo, listOmsChannelInfo } from '@/api/distributorManagement/enterprise-subject';
import ImproveMainDataInformation from '@/components/ImproveMainDataInformation/index.vue';
import { parseTime } from '@/utils';
import download from '@/utils/download';
import ClipboardJS from 'clipboard';
import { distributorContractListOwnCompany } from '@/api/distributorManagement/statistic/list';
import { distributorContractInfoListBrief, exportExcel } from '@/api/distributorManagement/enterprise-subject';
import { fetchCode } from '@/api/hidden-product-promotion.js';
import DistributorSubjectManage from '@/components/DistributorSubjectManage/index.vue';

export default {
  name: 'AuthorizedSubject',
  components: { DistributorSubjectManage, ImproveMainDataInformation },
  props: {
    id: String,
    isEdit: {
      type: Boolean,
      default: false
    },
    isDetail: {
      type: Boolean,
      default: false
    },
    isAudit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      distributorId: this.id,
      distribution: {},
      // newData
      ownCompanyOptions: [], // 甲方主体
      distributorContractInfoList: [], // 乙方主体信息
      channelShopNames: [], // SAP主数据渠道
      linkDialogVisible: false,
      mpCode: '',
      h5CodeUrl: 'pages/contract-info/add/index',
      PCurl: `${process.env.NODE_ENV === 'production' ? 'https://www.syoungmall.com/' : 'https://testsyzg.syounggroup.com/'}syoung/shop/contract-info?distributorId=${this.id}`,
      clipboard: null, // 复制对象
      copyBtn1: null,
      currentRow: {}, // 当前操作行
      improveInformationDialog: false,
      distributorSubjectDialog: false,
      msChannelList: [],
      sapChannelList: [],
      omsChannelList: [],
      showDialogTable: true,
      showAllTable: false,
      AllbankCardInfoList: [],
      isSelfChannel: null, // 主体平台归属为【自建渠道】“BUILD_SELF_CHANNEL”的；下面的必填信息只有：【主体信息】的部分字段；必填字段有：个人/企业、公司类型、企业名称、税号；
      sapOptions: [],
      omsOptions: []
    };
  },
  watch: {
    linkDialogVisible(val) {
      if (val && !this.clipboard) {
        this.$nextTick(() => {
          this.clipboard = new ClipboardJS(this.$refs.copyBtn.$el);
          this.clipboard.on('success', (e) => {
            this.$message.success('复制成功');
          });
          this.clipboard.on('error', (e) => {
            this.$message.error('复制失败');
          });
          this.clipboard1 = new ClipboardJS(this.$refs.copyBtn1.$el);
          this.clipboard1.on('success', (e) => {
            this.$message.success('复制成功');
          });
          this.clipboard1.on('error', (e) => {
            this.$message.error('复制失败');
          });
        });
      }
    }
  },
  computed: {
    table() {
      const that = this;
      return {
        filters: [
          {
            tag: 'sy-select',
            prop: 'merchantType',
            label: '主体类型',
            bind: {
              filterable: true,
              placeholder: '请选择',
              options: 'distributor_merchant_type'
            }
          },
          {
            tag: 'sy-select',
            prop: 'appIdList',
            label: '甲方主体',
            bind: {
              filterable: true,
              multiple: true,
              placeholder: '请选择',
              flashOptions: true,
              options: that.ownCompanyOptions,
              optionsProps: {
                value: 'id',
                label: 'company'
              }
            }
          },
          {
            tag: 'sy-select',
            prop: 'idList',
            label: '乙方主体',
            bind: {
              filterable: true,
              multiple: true,
              placeholder: '请选择',
              options: that.distributorContractInfoList,
              flashOptions: true,
              optionsProps: {
                value: 'id',
                label: 'company'
              }
            }
          },
          {
            tag: 'sy-select',
            prop: 'izCompleteChannelInfo',
            label: '主数据是否完善',
            bind: {
              filterable: true,
              placeholder: '请选择',
              options: 'soyoungzg_common_whether'
            }
          },
          {
            tag: 'sy-select',
            prop: 'izUseOrder',
            label: '是否用于下单',
            bind: {
              filterable: true,
              placeholder: '请选择',
              options: 'soyoungzg_common_whether'
            }
          },
          {
            tag: 'sy-select',
            prop: 'sapChannelCodeList',
            label: 'SAP渠道',
            bind: {
              filterable: true,
              multiple: true,
              placeholder: '请选择',
              options: that.sapOptions,
              flashOptions: true
            }
          },
          {
            tag: 'sy-select',
            prop: 'omsChannelIdList',
            label: 'OMS渠道',
            bind: {
              filterable: true,
              multiple: true,
              placeholder: '请选择',
              options: that.omsOptions,
              flashOptions: true
            }
          },
          {
            tag: 'el-input',
            prop: 'bankAccount',
            label: '收付款账号',
            bind: {
              placeholder: '请输入收付款账号'
            }
          },
          {
            tag: 'sy-select',
            prop: 'platformOwner',
            label: '主体平台归属',
            bind: {
              filterable: true,
              placeholder: '请选择',
              options: [...that.$dict['distributor_contract_platform_owner_type'], { label: '未完善', value: 'noValue' }]
            }
          },
          {
            tag: 'sy-select',
            prop: 'dataSource',
            label: '添加方式',
            bind: {
              filterable: true,
              placeholder: '请选择',
              options: that.$dict['contract_info_data_source']
            }
          }
        ],
        btns: [
          {
            text: '新增主体信息',
            type: 'primary',
            disabled: that.isDetail,
            code: 'authorized-subject-add',
            call() {
              that.$refs['distributorSubjectManage'].addSubject();
            }
          },
          {
            text: '客户主体填写链接',
            type: 'primary',
            code: 'authorized-subject-link',
            call() {
              !that.mpCode && that.getQrcode();
              that.linkDialogVisible = true;
            }
          },
          {
            text: '导出',
            type: 'primary',
            code: 'authorized-subject-export',
            confirm: '是否导出数据？',
            bind: {
              loading: that.exportLoading
            },
            call: ({ filtersValue }) => that.onExport(filtersValue)
          }
        ],
        columns() {
          return [
            {
              prop: 'platformOwnerName',
              label: '主体平台归属',
              width: 100
            },
            {
              label: '主体类型',
              prop: 'merchantTypeName'
            },
            {
              label: '乙方主体名称',
              multiLine: 3,
              render: (h, { row }) => (
                <Authority auth="authorized-subject-detail">
                  <a class="link-type" title="查看主体信息" onClick={() => that.handleSubject(row, true)}>
                    {row.merchantType === 'ENTERPRISE' ? row.company : row.contactRealName}
                  </a>
                </Authority>
              )
            },
            {
              label: '甲方主体名称',
              prop: 'ownCompanyName',
              multiLine: 3
            },
            {
              prop: 'contactRealName',
              label: '联系人',
              multiLine: 3,
              itemBind: {
                minWidth: 80
              }
            },
            {
              label: '合同数量',
              render(h, { row }) {
                const toPath = `/distributor-management/distributor/edit/${that.id}?activeName=contract&appId=${row.appId}&distributorContractInfoId=${row.distributorContractInfoId}`;
                return (
                  <el-button
                    type="text"
                    onClick={() => {
                      this.$router.push(toPath);
                    }}
                  >
                    {row.contractQuantity}
                  </el-button>
                );
              }
            },
            {
              label: '有效合同数',
              render(h, { row }) {
                const toPath = `/distributor-management/distributor/edit/${that.id}?activeName=contract&appId=${row.appId}&distributorContractInfoId=${row.distributorContractInfoId}&state=valid`;
                return (
                  <el-button
                    type="text"
                    onClick={() => {
                      this.$router.push(toPath);
                    }}
                  >
                    {row.validContractQuantity}
                  </el-button>
                );
              }
            },
            {
              label: '授权书总数',
              render(h, { row }) {
                const toPath = `/distributor-management/distributor/edit/${row.distributorId}?activeName=authorizationList&appId=${row.appId}&distributorContractInfoId=${row.distributorContractInfoId}&state=all`;
                return (
                  <el-button
                    type="text"
                    onClick={() => {
                      this.$router.push(toPath);
                    }}
                  >
                    {row.licenseQuantity}
                  </el-button>
                );
              }
            },
            {
              label: '有效授权书数',
              render(h, { row }) {
                const toPath = `/distributor-management/distributor/edit/${row.distributorId}?activeName=authorizationList&appId=${row.appId}&distributorContractInfoId=${row.distributorContractInfoId}&state=valid`;
                return (
                  <el-button
                    type="text"
                    onClick={() => {
                      this.$router.push(toPath);
                    }}
                  >
                    {row.validLicenseQuantity}
                  </el-button>
                );
              }
            },
            {
              label: '添加方式',
              width: 130,
              render: (h, { row }) => (
                <div v-frag>
                  <p>{row.dataSource === 'MANUALLY_ADD' ? row.createByName : row.dataSourceName}</p>
                  <p>{parseTime(row.createDate, '{y}-{m}-{d} {h}:{i}')}</p>
                </div>
              )
            },
            {
              label: '对应主数据渠道',
              itemBind: {
                minWidth: 200
              },
              render: (h, { row }) =>
                (row.platformOwner === 'SYZG_CHANNEL' && row.sapChannelCode && row.omsChannelId) || (row.platformOwner === 'BUILD_SELF_CHANNEL' && row.buildSelfPartyRelationVOList && row.buildSelfPartyRelationVOList.length) ? (
                  <div class="contract-info-perfect">
                    {row.platformOwner === 'SYZG_CHANNEL' && (
                      <div class="com-flex-1">
                        <el-tooltip effect="dark" placement="top">
                          <ul slot="content">
                            <li>
                              {row.sapChannelName}（{row.sapChannelCode}）
                            </li>
                          </ul>
                          <p>{row.sapChannelName}</p>
                        </el-tooltip>
                      </div>
                    )}
                    {row.platformOwner === 'BUILD_SELF_CHANNEL' && (
                      <div class="com-flex-1">
                        <el-tooltip effect="dark" placement="top">
                          <div slot="content">
                            {row.buildSelfPartyRelationVOList.map((item, index) => (
                              <p>
                                渠道{index + 1}：{item.sapChannelName}（{item.sapChannelCode}）
                              </p>
                            ))}
                          </div>
                          <div class={row.isShowMore ? '' : 'sap-channel-name-container'}>
                            {row.buildSelfPartyRelationVOList.map((item) => (
                              <p>
                                <span>{item.sapChannelName}</span>
                                {item.brandVOList && item.brandVOList.length && (
                                  <span v-frag>
                                    <span>-</span>
                                    {item.brandVOList.map((brand, idx) => (
                                      <span v-frag>
                                        <span>{brand.name}</span>
                                        {idx < item.brandVOList.length - 1 && <span>、</span>}
                                      </span>
                                    ))}
                                  </span>
                                )}
                              </p>
                            ))}
                          </div>
                        </el-tooltip>
                        {row.buildSelfPartyRelationVOList.length > 3 && <sy-button type="text" text={row.isShowMore ? '收起' : '展开'} call={() => (row.isShowMore = !row.isShowMore)} />}
                      </div>
                    )}
                    <el-button type="text" icon="el-icon-copy-document" onClick={() => that.handleCopyImproveInformation(row)}></el-button>
                  </div>
                ) : row.platformOwner === 'SYZG_CHANNEL' && row.izCreateOAFlow === '1' && row.channelEnabled === '1' ? (
                  <span>OA流程已发起</span>
                ) : (
                  <span>-</span>
                )
            },
            {
              label: '是否可用于下单',
              render: (h, { row }) => {
                return row.izUseOrder === '1' ? (
                  <span>可用</span>
                ) : (
                  <div>
                    <p>不可用</p>
                    <el-tooltip placement="top">
                      <div slot="content">{row.unableOrderReasonName}</div>
                      <el-button type="text">查看原因</el-button>
                    </el-tooltip>
                  </div>
                );
              }
            },
            {
              label: '操作',
              type: 'btns',
              width: 80,
              itemBind: {
                fixed: 'right'
              },
              btns({ row }) {
                return [
                  {
                    text: '编辑主体',
                    type: 'text',
                    disabled: !that.isEdit,
                    code: 'authorized-subject-edit',
                    call: () => that.handleSubject(row, false)
                  },
                  {
                    hide: !row.platformOwner,
                    text: '客户建档',
                    type: 'text',
                    disabled: !that.isEdit,
                    call: () => that.handleClickImproveInformation(row)
                  }
                ];
              }
            }
          ];
        },
        async search({ filtersValue, pageFilter }) {
          const { pageNo, pageSize } = pageFilter;
          if (filtersValue.platformOwner === 'noValue') {
            filtersValue.platformOwner = '';
          } else if (filtersValue.platformOwner === '') {
            filtersValue.platformOwner = null;
          }
          const res = await distributorContractInfoListPage({
            data: { ...filtersValue, distributorId: that.distributorId },
            pageNo,
            pageSize
          });
          const { list = [], total = 0 } = res.data || {};
          return {
            list: list.map((item) => {
              return { ...item, isShowMore: false };
            }),
            total
          };
        }
      };
    }
  },
  created() {
    this.initTable();
  },
  methods: {
    handleSubject(row, isDetail) {
      const ref = this.$refs['distributorSubjectManage'];
      ref && ref.handleSubject(row, isDetail);
    },
    refreshBasicTable() {
      const ref = this.$refs.basicTable;
      ref && ref.handlerSearch();
    },
    init() {
      this.getDistributor();
    },
    // 获取小程序二维码
    getQrcode() {
      const prames = {
        page: this.h5CodeUrl,
        bizType: 'PROMOTION',
        scene: `useless`
      };
      fetchCode(prames).then((response) => {
        if (response.data && response.data.code === 0) {
          this.mpCode = response.data.msg;
        } else {
          this.$message.error(response.data.msg);
        }
      });
    },
    // 初始化table中search数据
    initTable() {
      // 获取甲方主体
      this.getOwnCompanyOptions();
      // 获取乙方主体
      this.getDistributorContractInfoListBrief();
      this.initOptions();
    },
    // 获取所有options
    initOptions() {
      // table filter options
      externalChannelListSap({}).then((res) => {
        this.sapOptions = res.data.map((i) => {
          return { value: i.sapChannelCode, label: `${i.sapChannelCode}-${i.sapChannelName}` };
        });
      });
      externalChannelList({}).then((res) => {
        this.omsOptions = res.data.map((i) => {
          return { value: i.channelId, label: `${i.channelId}-${i.channelName}` };
        });
      });
      // SAP渠道sapChannelName: `${i.sapChannelCode}-${i.sapChannelName}`,
      listSapChannelInfo({}).then((res) => {
        this.sapChannelList = res.data.map((i) => {
          return { ...i, sapChannelName: `${i.sapChannelCode}-${i.sapChannelName}` };
        });
      });
      // oms渠道omsChannelName: `${i.omsChannelCode}-${i.omsChannelName}`
      listOmsChannelInfo({}).then((res) => {
        this.omsChannelList = res.data.map((i) => {
          return { ...i, omsChannelName: `${i.omsChannelCode}-${i.omsChannelName}` };
        });
      });
    },
    // 获取分销商信息
    getDistributor() {
      getBasicInfo(this.distributorId).then((res) => {
        this.distribution = res.data;
      });
    },
    // 清除所有表单校验
    clearAllFormValidate() {
      ['contractInfoForm', 'invoiceInfoForm', 'bankCardInfoForm'].forEach((formName) => {
        this.$refs[formName].clearValidate();
      });
    },
    // 选择甲方主体
    getOwnCompanyOptions() {
      distributorContractListOwnCompany().then((res) => {
        this.ownCompanyOptions = res.data || [];
      });
    },
    // 获取分销商合同主体信息
    getDistributorContractInfoListBrief() {
      const params = {
        distributorId: this.id
      };
      distributorContractInfoListBrief(params).then((res) => {
        this.distributorContractInfoList =
          res.data.map((i) => {
            if (i.merchantType === 'PERSONAL') {
              i.company = i.contactRealName;
            }
            return i;
          }) || [];
      });
    },
    // 导出
    onExport(filtersValue) {
      this.exportLoading = true;
      exportExcel({ ...filtersValue, distributorId: this.distributorId })
        .then((res) => {
          download(res, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', `${this.id}-合同主体数据导出-${parseTime(Date.now(), '{y}-{m}-{d}-{h}:{i}:{s}')}.xlsx`);
        })
        .finally(() => {
          this.exportLoading = false;
        });
    },
    // 点击完善信息
    handleClickImproveInformation(row) {
      this.currentRow = { ...row };
      this.improveInformationDialog = true;
    },
    // 复制主体信息
    handleCopyImproveInformation(row) {
      const { platformOwner, buildSelfPartyRelationVOList = [] } = row;
      let value = '';
      if (platformOwner === 'SYZG_CHANNEL') {
        value = `${row.sapChannelName}`;
      } else if (buildSelfPartyRelationVOList.length) {
        // 换行显示
        value = buildSelfPartyRelationVOList
          .map((item) => `${item.sapChannelName}`)
          .toString()
          .replace(/,/g, '\n');
      }
      this.$copyText(value)
        .then(() => {
          this.$message.success('复制成功');
        })
        .catch(() => {
          this.$message.info(`复制失败,请手动复制`);
        });
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
::v-deep {
  .columns-operation {
    .cell {
      display: flex;
      flex-direction: column;
      padding-right: 8px !important;
      .el-button {
        margin: 4px 0;
        text-align: left;
      }
    }
  }
  .el-table th > .cell {
    white-space: pre-line;
  }
  .el-dialog .el-table {
    margin: 0;
  }
}
.tips-text {
  line-height: 150%;
}
.btn-div {
  margin-bottom: 20px;
}
.custom-label {
  li {
    line-height: 20px;
  }
}
.link-dialog {
  .link-dialog-title {
    margin: 10px 0;
    font-size: 14px;
  }
  .info-container {
    display: flex;
    width: 100%;
    border: 1px solid #e4e7ed;
    padding: 10px;
    .code {
      width: 150px;
      height: 150px;
    }
    .detail {
      margin-left: 10px;
    }
    .btn-container {
      margin-top: 10px;
      text-align: left;
    }
  }
}
::v-deep {
  .relevancy-form {
    .content-wrap {
      padding: 0;
    }
    .el-form .el-form-item {
      width: 515px;
    }
  }
  .contract-info-perfect {
    display: flex;
    align-items: flex-start;
    .el-icon-copy-document {
      margin: 4px 0 0 0;
      font-size: 14px;
    }
  }
  .sap-channel-name-container {
    p {
      display: none;
      // 前3个元素显示
      &:nth-of-type(-n + 3) {
        display: block;
      }
    }
  }
}
</style>
