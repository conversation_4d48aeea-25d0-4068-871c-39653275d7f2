<template>
  <div class="page-container">
    <filter-form :options="setedFilterFormOptions" :exportOptions="_exportOptions" :basicData="basicData" @query="query" ref="filterForm" isClearRouteParams>
      <template slot="formItem_buyerName" slot-scope="{ form }">
        <div class="commo-flex custom">
          <el-select
            v-model="form.buyerType"
            placeholder="请选择买家信息"
            style="flex: 0 0 120px; margin-right: 10px"
            @change="
              form.buyerName = '';
              form.channelIdList = [];
            "
          >
            <el-option v-for="{ value, label } in dict_distributorType" :key="value" :label="label" :value="value"> </el-option>
          </el-select>
          <el-input v-if="form.buyerType !== 'PURCHASE'" v-model.trim="form.buyerName" placeholder="请输入买家昵称"></el-input>
          <el-select v-else v-model="form.channelIdList" placeholder="请选择经营渠道" style="width: 100%" multiple collapse-tags clearable filterable>
            <el-option v-for="{ value, label } in dict_externalchannelList" :key="value" :label="label" :value="value"> </el-option>
          </el-select>
        </div>
      </template>
    </filter-form>

    <!-- 操作栏 -->
    <div class="action">
      <div class="action-content">
        <action-bar style="margin: 16px 0" :itemOptions="_barOptions" @actionBarClick="onBarOptions"></action-bar>
      </div>
      <div class="tableHeaderConfig">
        <!--筛选条件设置-->
        <set-item-config title="筛选条件设置" :configList="_filterConfigItemsOptions" :getSetConfigList="getFilterConfigSetList" @onSave="saveFilterConfigSetList"></set-item-config>
        <!-- 列表表头设置 -->
        <set-item-config title="表头设置" :configList="tableOptions" :getSetConfigList="getTableConfigSetList" @onSave="saveTableConfigSetList" :suggestNum="9"></set-item-config>
      </div>
    </div>

    <!-- 列表展示 -->
    <table-exhibition v-loading="loading" @query="query" :table="table" :options="setedTableOptions" ref="table" @selection-change="handleSelectionChange" layoutable v-el-horizontal-scroll>
      <!-- 选择栏 -->
      <el-table-column type="selection" fixed="left" width="40" slot="column_prepend"> </el-table-column>
      <!-- 店铺名称 -->
      <el-table-column slot="column_shopName" slot-scope="{ index, ...option }" :key="option.prop + index" v-bind="option">
        <template slot-scope="{ row = {} }">
          <div style="text-align: left">
            <Authority auth="/distributor-management/:detail">
              <el-tooltip placement="top" :content="row.shopName">
                <div class="link-type commo-ellipsis" @click="checkDistributor(row)">{{ row.shopName || '--' }}</div>
              </el-tooltip>
            </Authority>
            <Authority auth="distributor-management-silent" v-if="row.izSilentCustomer == 1">
              <div style="margin-top: 6px">
                <el-button size="mini" plain type="primary" @click="openSilentCustomerPool(row)">沉寂客户</el-button>
              </div>
            </Authority>
            <div style="margin-top: 6px"><el-button size="mini" @click="$router.push('/data-center/distributor-square/portrait/' + row.id)">查看画像</el-button></div>
            <div style="margin-top: 6px"><el-button size="mini" @click="handleCustomerDescription(row)">客情说明</el-button></div>
          </div>
        </template>
      </el-table-column>

      <!-- 分销商信息 -->
      <el-table-column slot="column_distributorDetail" slot-scope="{ index, ...option }" :key="option.prop + index" v-bind="option">
        <template slot-scope="{ row = {} }">
          <div style="text-align: left">
            <div>店铺ID：{{ row.platformShopId || '--' }}</div>
            <div>手机号：{{ row.applyMobile || '--' }}</div>
            <div>分销商ID：{{ row.id || '--' }}</div>
          </div>
        </template>
      </el-table-column>

      <!-- 审核状态 -->
      <el-table-column align="center" slot="column_statusName" slot-scope="{ index, ...option }" :key="option.prop + index" v-bind="option">
        <template slot-scope="{ row = {} }">
          {{ row[option.prop] }}
          <div v-if="row.status === 'DISABLE'">
            <el-button
              type="text"
              @click="
                id = row.id;
                frozenReasonDialogVisible = true;
              "
              >查看原因</el-button
            >
          </div>
        </template>
      </el-table-column>

      <!-- 专属顾问 -->
      <el-table-column align="center" slot="column_customerServiceId" slot-scope="{ index, ...option }" :key="option.prop + index" v-bind="option">
        <template slot-scope="{ row: { customerServiceVO: { name = '--', organizationName = '' } = {} } }">
          <div>{{ name }}</div>
          <div>{{ organizationName }}</div>
        </template>
      </el-table-column>
      <!-- 拓展顾问 -->
      <el-table-column align="center" slot="column_developCustomerServiceId" slot-scope="{ index, ...option }" :key="option.prop + index" v-bind="option">
        <template slot-scope="{ row: { developCustomerServiceVO: { name = '--', organizationName = '' } = {} } }">
          <div>{{ name }}</div>
          <div>{{ organizationName }}</div>
        </template>
      </el-table-column>
      <!-- 时间显示 -->
      <template v-for="i of ['auditDate', 'assignDate', 'createDate', 'firstOrderTime', 'firstWechatBindingDate', 'flowDate', 'syncMerchantDate']" :slot="`column_${i}`" slot-scope="{ index, ...option }">
        <el-table-column :key="i + index" align="center" v-bind="option">
          <template slot-scope="{ row = {} }">
            <span v-if="i === 'auditDate'">{{ row.auditByName }}</span>
            <div>
              {{ $options.filters.parseTime(row[i], '{y}-{m}-{d}') || '--' }}
            </div>
            <div>
              {{ row[i] | parseTime('{h}:{i}:{s}') }}
            </div>
          </template>
        </el-table-column>
      </template>

      <!-- 操作栏 -->
      <el-table-column label="操作" align="center" fixed="right" width="100" slot="column_append">
        <template slot-scope="scope">
          <action-bar :itemOptions="_tableBarOptions" :record="scope.row" @actionBarClick="({ id }, row) => _self[id](row)"></action-bar>
        </template>
      </el-table-column>
    </table-exhibition>

    <!-- 专属顾问变动记录 -->
    <el-dialog title="变动记录" :visible.sync="recordsDialogVisible" width="1000px" v-if="recordsDialogVisible">
      <el-tabs class="custom-border-tabs" v-model="recordDialogActiveName" style="margin-top: -12px">
        <el-tab-pane v-for="(i, index) of recordOptions" :label="i.label" :name="i.comp" :key="i.comp + index"></el-tab-pane>
      </el-tabs>
      <keep-alive>
        <component :is="recordDialogActiveName" :id="id" v-bind="recordOption" ref="customerRecord"></component>
      </keep-alive>
    </el-dialog>

    <el-dialog title="冻结分销商" :visible.sync="frozenDialogVisible" width="600px">
      <submit-form :options="frozenDialogOptions" @onCancel="frozenDialogVisible = false" label-width="100px" @onSubmit="frozenDialogOnSubmit" ref="frozenDialog"></submit-form>
    </el-dialog>

    <el-dialog title="查看冻结原因" :visible.sync="frozenReasonDialogVisible" width="600px">
      <frozen-reason-list :id="id" v-if="frozenReasonDialogVisible"></frozen-reason-list>
    </el-dialog>

    <el-dialog title="添加备注" :visible.sync="remarkDialogVisible" width="600px">
      <submit-form :options="remarkDialogOptions" @onCancel="remarkDialogVisible = false" label-width="60px" @onSubmit="remarkDialogOnSubmit" ref="remarkDialog"></submit-form>
    </el-dialog>

    <el-dialog title="批量编辑" :visible.sync="adviserDialogVisible" width="600px" v-if="adviserDialogVisible">
      <batch-update-customer :type="batchUpdateCustomerType" :distributorIds="multipleSelection.map((i) => i.id)" @onCancel="adviserDialogVisible = false" @onSubmit="query"></batch-update-customer>
    </el-dialog>

    <el-dialog title="跟进记录" :visible.sync="followUpRecordDialogVisible" width="1000px" v-if="followUpRecordDialogVisible">
      <follow-up-record :distributorId="id" @hook:mounted="() => $refs.followUpRecord.query()" ref="followUpRecord"></follow-up-record>
    </el-dialog>

    <el-dialog :visible.sync="authorizeDegreeDialogVisible" width="60%" top="5vh" v-if="authorizeDegreeDialogVisible">
      <template v-slot:title>
        <span class="el-dialog__title">查看授权进度</span>
        <span class="color-info" style="font-size: 12px; margin-left: 12px">仅用于查看线上自建分销授权门槛</span>
      </template>
      <authorize-degree :distributorId="id" @onCancel="authorizeDegreeDialogVisible = false" @hook:mounted="() => $refs.authorizeDegree.query()" ref="authorizeDegree"></authorize-degree>
    </el-dialog>

    <el-dialog title="积分体系管理设置" :visible.sync="izJoinCreditDialogVisible" width="600px" v-if="izJoinCreditDialogVisible">
      <dialog-integral-system :distributorInfo="multipleSelection.map(({ id, izJoinCredit }) => ({ id, izJoinCredit }))" :izJoinCredit="izJoinCredit" @onCancel="izJoinCreditDialogVisible = false" @onSubmit="query"></dialog-integral-system>
    </el-dialog>
    <!-- 批量冻结 -->
    <dialog-import v-bind="importOptions" @onSuccess="query" ref="dialogImport"></dialog-import>
    <!--客情备注-->
    <customer-description v-model="customerDescriptionVisible" :distributorId="id" :is-detail="isDetail"></customer-description>
    <SelectChannelAndGroup :visible.sync="selectChannelVisible" @confirm="selecChannelType" />
  </div>
</template>

<script>
import { customQueryUserGet, customQueryUserUpdate } from '@/api/common/index.js';
import { customHeaderUserGet, customHeaderUserSave, customHeaderUserListShowColumn } from '@/api/data-center/distributor-square.js';
import { list, enable, distributorCsRelationAssign, establishGroup, establishGroupCancel, getById, updateApproveMsg, postDisable, distribtuorSaveImportRecord } from '@/api/distributorManagement/distributor/list';
import { distributorChangeMobile, improveMobile } from '@/api/distributorManagement/cooperate-shop';
import { mapGetters } from 'vuex';
import dict from '@/components/Common/dicts';
import DistributionCustomerService from '@/components/views/distributorManagement/distributor/components/DistributionCustomerService';
import FilterForm from '@/components/Form/FilterForm';
import SubmitForm from '@/components/Form/SubmitForm';
import ActionBar from '@/components/Common/ActionBar';
import TableExhibition from '@/components/Table/TableExhibition';
import SetItemConfig from '@/components/Table/SetItemConfig';
import Record from './DistributorDetail/change-record/record.vue';
import RecordOptions from './DistributorDetail/change-record/config';
import FollowUpRecord from './components/follow-up-record';
import AuthorizeDegree from './components/authorize-degree';
import BatchUpdateCustomer from './components/BatchUpdateCustomer';
import FrozenReasonList from './components/FrozenReasonList';
import DialogImport from '@/components/Common/Import/dialogImportV3';
import dialogIntegralSystem from './components/dialogIntegralSystem';
import SelectChannelAndGroup from '@/components/SelectChannelAndGroup';
import { FilterFormOptions, FilterConfigItemsOptions, ExportOptions, BarOptions, TableOptions, TableBarOptions, FrozenDialogOptions } from './config';
import CustomerDescription from './components/customer-description';

export default {
  name: 'distributor-management-distributor-list',
  components: {
    SelectChannelAndGroup,
    CustomerDescription,
    FilterForm,
    SubmitForm,
    ActionBar,
    TableExhibition,
    SetItemConfig,
    FollowUpRecord,
    AuthorizeDegree,
    BatchUpdateCustomer,
    FrozenReasonList,
    DialogImport,
    dialogIntegralSystem,
    ...RecordOptions.reduce((pre, cur) => {
      pre[cur.comp] = {
        render() {
          return <Record props={this.$attrs}></Record>;
        }
      };
      return pre;
    }, {})
  },
  data() {
    return {
      loading: false,
      id: '',
      isDetail: true,
      setedFilterFormOptions: FilterFormOptions,
      basicData: {
        buyerType: 'SALE',
        buyerName: '',
        channelIdList: [],
        ...this.$route.query
      },
      tableOptions: [], // 表格所有配置项
      setedTableOptions: TableOptions, // 用户选择后的配置项
      dict_distributorType: [],
      dict_externalchannelList: [],

      table: {},
      multipleSelection: [],

      remarkDialogVisible: false, // 备注弹框
      remarkDialogOptions: [
        {
          prop: 'approveMsg',
          label: '备注',
          component: 'input',
          rows: '4',
          maxlength: '15',
          showWordLimit: true,
          type: 'textarea',
          placeholder: '说明无法审核的原因，如：已有意向顾问(最多15字)',
          rules: [{ required: true, message: '请输入备注', trigger: 'blur' }]
        }
      ],

      frozenReasonDialogVisible: false, // 冻结原因弹框
      frozenDialogVisible: false, // 冻结弹框
      frozenDialogOptions: FrozenDialogOptions,

      adviserDialogVisible: false, // 批量调整顾问弹框
      batchUpdateCustomerType: '', // 批量调整顾问类型（专属，拓展，分配）
      recordsDialogVisible: false, // 变动记录弹窗
      recordDialogActiveName: 'CustomerService',
      followUpRecordDialogVisible: false, // 跟进记录弹框
      authorizeDegreeDialogVisible: false, // 授权进度弹框
      izJoinCreditDialogVisible: false, // 积分体系弹框
      customerDescriptionVisible: false,
      selectChannelVisible: false // 三方分销商导入选择分销商渠道分类
    };
  },
  computed: {
    ...mapGetters(['userInfo', 'userGroup']),
    recordOptions() {
      return RecordOptions.filter((i) => ['CustomerService', 'DevelopService'].includes(i.comp));
    },
    recordOption() {
      return this.recordOptions.find((i) => i.comp === this.recordDialogActiveName);
    },
    importOptions() {
      return {
        accept: '.xls,.xlsx',
        tip: '文件只支持上传大小为:5M以内,条数1000条以内;文件格式支持:xlss,xlsx格式',
        options: {
          _size: { company: 'MB', size: 5 }
        },
        templateUrl: process.env.VUE_APP_MUSHUROOMFILEURL + '/static/file/soyoung-zg/template/分销商批量冻结模板.xlsx',
        name: '分销商批量冻结',
        title: '批量冻结',
        labelTitle: '批量导入冻结清单',
        action: process.env.VUE_APP_BASE_URL + '/soyoungzg/api/distributor/importDisable',
        createInBatch: distribtuorSaveImportRecord,
        downloadFailRequest: process.env.VUE_APP_BASE_URL + '/soyoungzg/api/distributor/exportFailedRecord'
      };
    }
  },
  created() {
    this.init();
  },
  async activated() {
    this.params = {
      data: this.$refs.filterForm.getParams(),
      ...this.$refs.table.getParams()
    };
    // 初次进入，需要等待表格配置项数据返回
    if (!this.tableOptions.length) await this.getTableConfigList();

    this.query();
  },
  methods: {
    async init() {
      this._filterConfigItemsOptions = FilterConfigItemsOptions;
      this.getFilterConfigSetList();

      this._exportOptions = ExportOptions;
      this._barOptions = BarOptions;
      this._tableBarOptions = TableBarOptions;

      this.dict_distributorType = await dict('COMMON_OMSINFO_TYPE');
      this.dict_externalchannelList = await dict('COMMON_EXTERNALCHANNEL_LIST_ALL');
    },
    query(params = {}) {
      Object.assign(this.params, params);
      // 分销商ID查询支持输入多个
      const ids = this.params.data.ids;
      if (ids && ids.includes(',')) {
        this.params.data.ids = ids.split(',').filter((i) => !!i);
      }

      this.loading = true;
      list(this.params)
        .then((res) => {
          this.table = res.data;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    // 打开备注弹框
    async remarkDistributor({ id }) {
      this.remarkDialogVisible = true;

      // 首次打开弹框 子组件没有渲染完毕
      if (!this.$refs.remarkDialog) await this.$nextTick();
      this.$refs.remarkDialog.setData(
        getById(id).then((res) => {
          const approveMsg = res?.data?.distributorVO?.approveMsg ?? '';
          return { approveMsg, id };
        })
      );
    },
    // 编辑备注
    remarkDialogOnSubmit({ form, callback }) {
      return updateApproveMsg(form)
        .then(() => {
          this.updatedCallback();
          this.remarkDialogVisible = false;
        })
        .finally(callback);
    },
    // 冻结 解冻
    async frozenDistributor({ id, status }) {
      if (status === 'PASS') {
        this.frozenDialogVisible = true;

        // 首次打开弹框 子组件没有渲染完毕
        if (!this.$refs.frozenDialog) await this.$nextTick();
        this.$refs.frozenDialog.setData({ reason: 'REPEAT', distributorId: id, createByName: this?.userInfo?.user?.name ?? '' });
        return;
      } else if (status === 'DISABLE') {
        this.$confirm(`确定解冻该分销商账户吗? 解冻后请确保专属顾问绑定完成`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            enable(id).then(this.updatedCallback);
          })
          .catch(() => {
            this.$message.info('已取消');
          });
      }
    },
    // 冻结分销商
    frozenDialogOnSubmit({ form, callback }) {
      return postDisable(form)
        .then(() => {
          this.updatedCallback();
          this.frozenDialogVisible = false;
        })
        .finally(callback);
    },
    // 分配
    assignShop(row) {
      this.$popup(DistributionCustomerService, {
        title: '分配专属顾问'
      })
        .then((data) => {
          const q = {
            brandList: data.brandList,
            csId: data.csId,
            distributorId: row.id
          };
          distributorCsRelationAssign(q).then((res) => {
            this.$message({
              type: 'success',
              message: '分配成功!'
            });
            this.query();
          });
        })
        .catch((error) => error);
    },
    // 查看变动记录
    recordDistributor({ id }) {
      this.id = id;
      this.recordDialogActiveName = 'CustomerService';
      this.recordsDialogVisible = true;
    },
    // 查看分销商
    checkDistributor({ id }) {
      this.$router.push({
        path: `/distributor-management/distributor/detail/${id}`
      });
    },
    // 修改分销商
    editDistributor({ id }) {
      this.$router.push({
        path: `/distributor-management/distributor/edit/${id}`
      });
    },
    // 审核分销商
    auditDistributor({ id }) {
      this.$router.push({
        path: `/distributor-management/distributor/audit/${id}`
      });
    },
    // 批量编辑拓展顾问
    batchEditBD(type = 'develop') {
      if (!this.multipleSelection.length) {
        this.$message.warning('至少选择一条数据');
        return;
      }
      this.batchUpdateCustomerType = type;
      this.adviserDialogVisible = true;
    },
    // 编辑专属顾问
    batchEditEC() {
      this.batchEditBD('exclusive');
    },
    // 批量分配顾问
    batchAmongAdviser() {
      if (this.multipleSelection.some((i) => ['ASSIGN', 'NO_ASSIGN'].includes(i.assignStatus))) {
        this.$message.warning('选择分配数据必须都为待分配状态');
        return;
      }
      this.batchEditBD('assign');
    },
    //  积分体系管控设置
    izJoinCredit() {
      if (!this.multipleSelection.length) {
        this.$message.warning('至少选择一条数据');
        return;
      }
      this.izJoinCreditDialogVisible = true;
    },
    // 建群
    createGroup({ id, groupHandoverStatus }) {
      const request = groupHandoverStatus === 'DOWN_GROUP' ? establishGroupCancel : establishGroup;

      request({ id }).then(this.updatedCallback);
    },
    // 完善手机号
    improveMobile({ id: distributorId }) {
      this.$prompt('请输入手机号码', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /^(86){0,1}1\d{10}$/,
        inputErrorMessage: '手机号码格式不正确'
      })
        .then(({ value: mobile }) => {
          improveMobile({ distributorId, mobile }).then(this.updatedCallback);
        })
        .catch(() => {
          this.$message.info('取消输入');
        });
    },
    // 更改手机号
    changeMobile({ memberId: userId }) {
      this.$prompt('请输入手机号码', '更改手机号码', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /^(86){0,1}1\d{10}$/,
        inputErrorMessage: '手机号码格式不正确'
      })
        .then(({ value: newMobile }) => {
          distributorChangeMobile({ userId, newMobile, verifyType: 'CUSTOMER' }).then(this.updatedCallback);
        })
        .catch(() => {
          this.$message.info('取消输入');
        });
    },
    // 查看跟进记录
    checkFollowUpRecord({ id }) {
      this.id = id;
      this.followUpRecordDialogVisible = true;
    },
    // 查看授权进度
    checkAuthorizeDegree({ id }) {
      this.id = id;
      this.authorizeDegreeDialogVisible = true;
    },
    updatedCallback() {
      this.$message.success('操作成功');
      this.query();
    },
    // 获取列表配置基础数据
    async getTableConfigList() {
      const res = await customHeaderUserGet({ customHeaderId: '2' });
      const { customHeaderColumnCategoryVOList: list = [] } = res.data ?? {};

      this.tableOptions = list.map(({ categoryName: name, columnVOList }) => ({
        name,
        childs: columnVOList.map(({ headerColumn: prop, headerColumnName: label, izUnchangeable, ...i }) => {
          return {
            ...i,
            // 获取前端设置的配置（宽度，是否自定义展示等）
            ...TableOptions.find((i) => i.prop === prop),
            prop,
            label,
            disabled: izUnchangeable === '1'
          };
        })
      }));

      await this.getTableConfigSetList();
    },
    // 获取用户设置配置数据
    async getTableConfigSetList() {
      try {
        const res = await customHeaderUserListShowColumn({ customHeaderId: '2' });
        const keys = (res?.data ?? []).map((i) => i.headerColumn);
        const columns = this.tableOptions.map((i) => i.childs).flat();
        this.setedTableOptions = keys.map((key) => columns.find((i) => i.prop === key)).filter((i) => i);
        return keys;
      } catch (e) {
        console.dir(e);
      }
      return [];
    },
    //  保存用户设置配置数据
    saveTableConfigSetList({ callback, data }) {
      callback(
        customHeaderUserSave({
          customHeaderId: '2',
          selectColumnIds: data.map(({ columnId }) => columnId)
        }).then(this.getTableConfigSetList)
      );
    },
    // 获取筛选条件用户设置配置数据
    async getFilterConfigSetList() {
      try {
        const res = await customQueryUserGet({ id: '1' });
        const keys = (res?.data?.customQueryUserVOList ?? []).map((i) => i.fieldKey);
        const columns = this._filterConfigItemsOptions.map((i) => i.childs).flat();
        const filterColumns = keys.map((key) => columns.find((i) => i.prop === key)).filter((i) => i);
        this.setedFilterFormOptions = filterColumns.length ? filterColumns : columns;
        return keys;
      } catch (e) {
        console.dir(e);
      }
      return [];
    },
    // 保存筛选条件用户设置配置数据
    saveFilterConfigSetList({ callback, data }) {
      callback(
        customQueryUserUpdate(
          data.map(({ prop: fieldKey, label: fieldValue }) => ({
            fieldKey,
            fieldValue,
            customQueryId: '1'
          }))
        ).then(() => {
          this.getFilterConfigSetList();
          this.$refs.filterForm && this.$refs.filterForm.resetData();
        })
      );
    },
    // 打开客户池
    openSilentCustomerPool(row) {
      this.$router.push({
        path: `/distributor-management/silent-customer-management/silent-customer-pool?distributorId=${row.id}&distributorName=${row.shopName}`
      });
    },
    // 批量冻结
    batchFrozenDistributor() {
      this.$refs.dialogImport.open();
    },
    // 客情说明
    handleCustomerDescription({ id }) {
      const hasEditPermission = this.$utils.hasPermission('distributor-management-edit', true);
      this.isDetail = !hasEditPermission;
      this.id = id;
      this.customerDescriptionVisible = true;
    },
    onBarOptions(item) {
      const { id } = item;
      if (id === 'importTripartiteDistributor') {
        this.selectChannelVisible = true;
        return;
      }
      this[id] && this[id]();
    },
    // 导入三方分销商
    selecChannelType({ channelType, groupId }) {
      this.$router.push(`/distributor-management/cooperate-shop/import?channelType=${channelType}&groupId=${groupId}`);
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
@import 'src/styles/viewsStyles/distributor-management/distributor/list';
.div-btn-box {
  display: inline-block;
}
// 表格为空时加入/
::v-deep {
  .el-table {
    .cell:empty::before {
      content: '--';
    }
    .is-leaf {
      .cell:empty::before {
        content: '/';
      }
    }
  }
  .el-dialog {
    .el-dialog__body {
      .table {
        height: 50vh;
        display: flex;
        flex-direction: column;
        .el-table {
          overflow-y: auto;
        }
      }
    }
  }
}
.filter-datetimerange {
  width: 100%;
}

.action {
  display: flex;
  align-items: center;
}
.action-content {
  flex: 1;
}
.tableHeaderConfig {
  display: flex;
  align-items: center;
  & > div {
    margin-right: 10px;
  }
}
.tag {
  cursor: pointer;
}
</style>
