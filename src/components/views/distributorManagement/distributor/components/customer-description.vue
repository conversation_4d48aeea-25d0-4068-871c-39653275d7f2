<template>
  <el-dialog :visible="show" title="客情说明" width="60%" @close="close">
    <p style="margin-bottom: 12px">主要用于填写客户情况，记录客户的调性、公司实力、行业特征、经营实力、特殊注意事项、客户偏好等等</p>
    <rich-text-editor ref="editor" :disabled="isDetail" v-model="customerDescription" />
    <span slot="footer" class="dialog-footer" v-if="!isDetail">
      <el-button @click="close">取消</el-button>
      <button-hoc type="primary" @click="submit">确定</button-hoc>
    </span>
  </el-dialog>
</template>

<script>
import { distributorGetCustomerDescription, distributorUpdateCustomerDescription } from '@/api/distributorManagement/distributor/list';
import RichTextEditor from '@/components/Design/components/RichText/RichTextEditor/index.vue';

export default {
  name: 'customer-description',
  components: { RichTextEditor },
  model: {
    prop: 'show',
    event: 'close'
  },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    //  分销商id
    distributorId: {
      type: String,
      default: '',
      required: true
    },
    isDetail: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      customerDescription: ''
    };
  },
  watch: {
    show: {
      handler(val) {
        if (val) {
          this.getCustomerDescription();
        }
      },
      immediate: true
    }
  },
  methods: {
    // 获取客请备注
    getCustomerDescription() {
      const { distributorId } = this;
      if (!distributorId) return;
      distributorGetCustomerDescription(distributorId).then((res) => {
        this.customerDescription = res?.data?.customerDescription || '';
      });
    },
    close() {
      this.$emit('close', false);
      this.customerDescription = '';
    },
    submit() {
      const data = {
        customerDescription: this.customerDescription,
        id: this.distributorId
      };
      distributorUpdateCustomerDescription(data).then(() => {
        this.$message.success('修改成功');
        this.close();
      });
    }
  }
};
</script>

<style scoped lang="scss">
::v-deep {
  .tox-tinymce {
    width: 100% !important;
    height: 400px !important;
  }
}
</style>
