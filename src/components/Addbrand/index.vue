<!-- 添加品牌 -->
<template>
  <el-dialog title="选择品牌" :visible.sync="visible" @close="beforeClose" center width="900px">
    <div class="top-info">
      <SubmitForm ref="searchForm" v-bind="searchFormOptions" @onSubmit="onSubmit_searchForm" @onReset="onReset"> </SubmitForm>
    </div>
    <el-table @selection-change="handleSelectionChange" :data="list" v-loading="listLoading" height="400">
      <el-table-column type="selection" width="55" :selectable="selectable"> </el-table-column>
      <el-table-column align="center" label="品牌图标">
        <template slot-scope="scope">
          <img :src="scope.row.logoUrl" alt class="brandimg" />
        </template>
      </el-table-column>
      <el-table-column align="center" label="品牌名称" prop="name"></el-table-column>
      <el-table-column align="center" label="业务分组" prop="brandGroupName"></el-table-column>
      <el-table-column label="操作" width="80px">
        <template slot-scope="operation">
          <el-button @click="confirmOK(operation.row)" type="text" v-if="selectable(operation.row)">选择</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="pagination">
      <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="pageNo" :page-sizes="[10, 20, 30, 40, 50, 100]" :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper" :total="total" :disabled="listLoading"></el-pagination>
    </div>
    <div class="dialog-footer" slot="footer">
      <el-button @click="beforeClose">取消</el-button>
      <ButtonHoc @click="onSubmit" type="primary">保存</ButtonHoc>
    </div>
  </el-dialog>
</template>
<script>
import pickBy from 'lodash/pickBy';
import dict from '@/components/Common/dicts';
import { listPage } from '@/api/setting/commodity/brand';
import SubmitForm from '@/components/Common/SubmitForm/index';
export default {
  name: 'Addbrand',
  components: { SubmitForm },
  data() {
    return {
      visible: false,
      multipleSelection: [],
      pageNo: 1,
      pageSize: 10,
      listLoading: false,
      total: 0,
      list: []
    };
  },
  props: {
    dataList: {
      type: Array,
      default: function () {
        return [];
      }
    }
  },
  created() {},
  computed: {
    dataSet() {
      return new Set(this.dataList);
    },
    searchFormOptions() {
      return {
        formItems: [
          {
            type: 'select',
            label: '筛选品牌',
            key: 'ids',
            selectOptions: dict('COMMON_PREFER_BRAND'),
            multiple: true
          }
        ]
      };
    }
  },

  watch: {},
  methods: {
    selectable(row) {
      return !this.dataSet.has(row.brandId);
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    openDialog() {
      this.pageNo = 1;
      this.pageSize = 10;
      this.multipleSelection = [];
      this.visible = true;
      this.$nextTick(() => {
        this.$refs.searchForm.clear();
        this.fetchData();
      });
    },
    handleSizeChange(pageSize) {
      this.pageSize = pageSize;
      this.fetchData();
    },
    handleCurrentChange(pageNo) {
      this.pageNo = pageNo;
      this.fetchData();
    },
    // 单选
    confirmOK(row) {
      this.$emit('modifOk', [row.id], this.beforeClose);
      this.beforeClose();
    },
    // 多选
    onSubmit() {
      const brandIds = this.multipleSelection.map(({ id }) => id);
      if (!brandIds.length) {
        this.$message.warning('请选择品牌');
        return;
      }
      this.$emit('modifOk', brandIds, this.beforeClose);
    },
    beforeClose() {
      this.visible = false;
    },
    // 查询参数
    getParames() {
      const formData = this.$refs.searchForm.formData;
      const params = pickBy({ ...formData }, (val) => !!val);
      return params;
    },
    fetchData() {
      this.listLoading = true;
      const listQuery = {
        pageNo: this.pageNo,
        pageSize: this.pageSize,
        data: this.getParames()
      };
      listPage(listQuery)
        .then((response) => {
          this.list = response.data.list.map((item) => ({
            ...item,
            brandId: item.id
          }));
          this.total = response.data.total;
        })
        .finally(() => {
          this.listLoading = false;
        });
    },
    onSubmit_searchForm() {
      this.fetchData();
    },
    // 重置
    onReset() {
      this.pageNo = 1;
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.select-input.el-input {
  ::v-deep .el-input__inner {
    background-color: #fff;
    color: #606266;
  }
  ::v-deep &.is-disabled .el-input__inner {
    border-color: #dcdfe6;
    background-color: #fff;
    color: #606266;
  }
  ::v-deep .el-input-group__append {
    background-color: #fff;
    &:hover {
      background-color: #f5f7fa;
    }
  }
  ::v-deep &.disabled {
    .el-input__inner {
      background-color: #f5f7fa;
      color: #c0c4cc;
      border-color: #e4e7ed;
    }
    .el-input-group__append {
      background-color: #f5f7fa;
      .el-button {
        background-color: transparent;
        color: #c0c4cc;
      }
    }
  }
}
.brandimg {
  width: 30px;
  height: 30px;
}
.el-pagination {
  text-align: right;
}
.refresh {
  margin-left: 10px;
}
.input-with-select {
  width: 250px;
  margin-left: 10px;
  ::v-deep .el-input-group__prepend {
    background-color: #fff;
  }
}

.table-container {
  min-height: 277px;
  ::v-deep tbody tr {
    cursor: pointer;
  }
}
.top-info {
  margin-bottom: 15px;
}
.pagination {
  margin-top: 16px;
  text-align: right;
}
</style>
