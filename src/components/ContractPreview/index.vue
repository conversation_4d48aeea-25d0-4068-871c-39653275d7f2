<template>
  <el-drawer :title="title" :visible="show" direction="rtl" @close="close" @open="open" size="60%">
    <template v-if="contractList && contractList.length">
      <div class="contract-container" v-if="contractList.length === 1">
        <div class="pdf-container" @contextmenu.prevent>
          <VuePdfEmbed :source="contractList[0].url" />
        </div>
      </div>
      <el-tabs v-model="tabCurrent" v-else class="custom-border-tabs" @tab-click="tabClick">
        <el-tab-pane :label="item.name" :name="item.key" :key="item.key" v-for="item in contractList" lazy>
          <div class="contract-container">
            <div class="pdf-container" @contextmenu.prevent>
              <VuePdfEmbed :source="item.url" />
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </template>
    <div v-else class="empty">暂无附件</div>
  </el-drawer>
</template>

<script>
import VuePdfEmbed from 'vue-pdf-embed/dist/vue2-pdf-embed';
export default {
  name: 'ContractPreview',
  components: { VuePdfEmbed },
  model: {
    prop: 'show',
    event: 'update:show'
  },
  props: {
    list: {
      type: Array,
      default: () => []
    },
    title: {
      type: String,
      default: '查看合同'
    },
    show: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      tabCurrent: '0'
    };
  },
  computed: {
    contractList() {
      return this.list.map((item, index) => ({
        ...item,
        name: item.name.split('.')[0],
        key: `${index}`
      }));
    }
  },
  methods: {
    tabClick() {
      this.resetScroll();
    },
    open() {
      this.resetScroll();
    },
    // 将滚动条置顶
    resetScroll() {
      setTimeout(() => {
        const ref = document.querySelector('.el-drawer__body');
        ref && ref.scrollTo(0, 0);
      }, 0);
    },
    close() {
      this.tabCurrent = '0';
      this.$emit('update:show', false);
    }
  }
};
</script>

<style scoped lang="scss">
.contract-container {
  .empty {
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
::v-deep {
  .el-drawer__header {
    margin-bottom: 4px;
    background: linear-gradient(0deg, rgba(255, 255, 255, 1) 10%, rgba(214, 213, 253, 0.6) 100%);
    & > span {
      color: var(--color-text-primary);
      font-weight: 500;
    }
  }
  .el-tabs__header {
    position: sticky;
    top: 0;
    z-index: 1;
    background-color: #fff;
    padding: 0 20px;
    margin: 0;
  }
  .el-drawer__body {
    overflow: hidden;
    overflow-y: auto;
  }
  .vue-pdf-embed > div {
    &:not(:last-child) {
      margin-bottom: 10px;
    }
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  }
}
.pdf-container {
  background: #f7f8fa;
}
</style>
