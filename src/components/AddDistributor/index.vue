<template>
  <div class="add-clue-container">
    <div class="box">
      <span @click="chooseItem" v-if="editable" class="add-item">选择用户</span>
    </div>
    <el-table :data="dataList" fit max-height="400">
      <el-table-column label="店铺名称" prop="shopName"></el-table-column>
      <el-table-column label="登录手机号" prop="applyMobile"></el-table-column>
      <el-table-column align="center" label="专属顾问" width="180">
        <template slot-scope="scope">
          <p v-if="scope.row.customerServiceVO">
            {{
              scope.row.customerServiceVO.name +
              '：' +
              scope.row.customerServiceVO.mobile
            }}
          </p>
          <p
            v-else-if="scope.row.status === 'WAIT_AUDIT'"
            style="color: #ff0000"
          >
            {{ scope.row.approveMsg }}
          </p>
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right">
        <template slot-scope="scope">
          <el-button
            @click="remove(scope.row.id, scope.$index)"
            type="text"
            v-if="editable"
            >移除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <el-dialog
      :append-to-body="true"
      :visible.sync="dialogFormVisible"
      title="选择用户"
      width="900px"
    >
      <div class="top-info">
        <el-select
          class="input-with-select"
          clearable
          placeholder="采货类型"
          v-model="purchaseType"
        >
          <el-option label="采销" value="PURCHASE"></el-option>
          <el-option label="一件代发" value="DROP_SHIPPING"></el-option>
        </el-select>
        <el-input
          class="input-with-select"
          placeholder="登录手机号"
          v-model="applyMobile"
        ></el-input>
        <div class="input-with-select">
          <SelectCustomerService multiple v-model="csIds" />
        </div>

        <el-button @click="onSearch" type="primary">查询</el-button>
        <el-button @click="onReset">重置</el-button>
      </div>
      <el-table
        :data="list"
        element-loading-text="加载中"
        fit
        highlight-current-row
        v-loading="listLoading"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          type="selection"
          width="55"
          :selectable="selectable"
        ></el-table-column>
        <el-table-column label="店铺名称" prop="shopName"></el-table-column>
        <el-table-column
          label="申请人手机号"
          prop="applyMobile"
        ></el-table-column>
        <el-table-column align="center" label="专属顾问" width="180">
          <template slot-scope="scope">
            <p v-if="scope.row.customerServiceVO">
              {{
                scope.row.customerServiceVO.name +
                '：' +
                scope.row.customerServiceVO.mobile
              }}
            </p>
            <p
              v-else-if="scope.row.status === 'WAIT_AUDIT'"
              style="color: #ff0000"
            >
              {{ scope.row.approveMsg }}
            </p>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          :current-page="pageNo"
          :disabled="listLoading"
          :page-size="pageSize"
          :page-sizes="[5, 10, 20, 30]"
          :total="total"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
          background
          layout="total, sizes, prev, pager, next, jumper"
        ></el-pagination>
      </div>
      <div slot="footer">
        <el-button @click="dialogFormVisible = false">取 消</el-button>
        <el-button type="primary" @click="onSave">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { list } from '@/api/distributorManagement/distributor/list';
import SelectCustomerService from '@/components/ListSearch/SelectCustomerService';
export default {
  name: 'add-clue',
  model: {
    prop: 'dataList',
    event: 'updateTable'
  },
  components: {
    SelectCustomerService
  },
  props: {
    dataList: {
      type: Array,
      default: function () {
        return [];
      }
    },
    // 非必传 是否可以编辑，默认可编辑
    editable: {
      type: Boolean,
      default: true
    },
    // 单选传 false， 默认多选
    multiple: {
      type: Boolean,
      default: true
    }
  },
  created() {},
  filters: {},
  computed: {
    dataSet() {
      return new Set(this.dataList.map(({ id }) => id));
    }
  },
  watch: {},
  data() {
    return {
      selectList: [], // 选中的数据列表
      purchaseType: '',
      csIds: [],
      applyMobile: '',
      listLoading: false,
      list: [],
      pageNo: 1,
      pageSize: 5,
      total: 0,
      dialogFormVisible: false
    };
  },
  methods: {
    selectable(row) {
      return !this.dataSet.has(row.id);
    },
    onSave() {
      this.dialogFormVisible = false;
      this.$emit('updateTable', [...this.dataList, ...this.selectList]);
    },
    handleSelectionChange(val) {
      this.selectList = val;
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.pageNo = 1;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.pageNo = val;
      this.fetchData();
    },
    onSearch() {
      this.pageNo = 1;
      this.pageSize = 5;
      this.fetchData();
    },
    onReset() {
      this.pageNo = 1;
      this.pageSize = 5;
      this.purchaseType = '';
      this.csIds = [];
      this.applyMobile = '';
      this.fetchData();
    },
    remove(commodityId) {
      const list = [...this.dataList];
      const idx = list.findIndex(({ id }) => id === commodityId);
      list.splice(idx, 1);
      this.$emit('updateTable', list);
    },
    chooseItem() {
      this.dialogFormVisible = true;
      this.onReset();
    },
    fetchData() {
      this.listLoading = true;
      const { pageNo, pageSize, purchaseType, csIds, applyMobile } = this;
      const listQuery = {
        pageNo,
        pageSize,
        data: { purchaseType, csIds, applyMobile, status: 'PASS' }
      };
      list(listQuery)
        .then((response) => {
          this.list = response.data.list;
          this.total = response.data.total;
        })
        .finally(() => {
          this.listLoading = false;
        });
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
@import '../AddCommodity/styles';
.box {
  margin: 10px 0;
}
.tips {
  color: #606266;
}
.add-item {
  padding: 10px;
  color: var(--color-primary);
  cursor: pointer;
}
.input-with-select {
  display: inline-block;
  margin: 0 16px 0 0;
  width: 220px;
}
.el-range-editor--small.el-input__inner {
  width: 220px;
}
.top-info {
  margin-bottom: 16px;
  ::v-deep .el-input {
    width: 220px;
    margin-right: 16px;
  }
  .el-button--primary {
    margin: 20px;
  }
  .link {
    color: var(--color-primary);
    cursor: pointer;
  }
}
</style>
