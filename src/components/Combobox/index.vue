<!-- 复合框组件 -->
<template>
  <div class="combo__box">
    <el-select :style="{ width: selectWatch }" v-bind="$attrs" :disabled="disabled" v-model="typeValue" class="combo__box__select" @change="typeValueChange">
      <el-option :label="item.label" :value="item.value" v-for="item of option" :key="item.value"></el-option>
    </el-select>
    <CryptoBlock placeholder="请输入" v-if="isCrypt" tag-type="input" :biz-id="bizId" fixed :edit-auth="editAuth" :detail-auth="detailAuth" class="area-width" :biz-type="bizType" :disabled="disabled" :value="value" @input="input" v-on="$listeners" />
    <template v-else>
      <div v-for="item of option" :key="item.value">
        <div v-show="item.value === typeValue">
          <!--这里不要用v-if-->
          <slot :name="item.value">
            <el-input v-bind="item.$attrs" :value="value" @input="input" :disabled="disabled"></el-input>
          </slot>
        </div>
      </div>
    </template>
    <slot> </slot>
  </div>
</template>

<script>
import CryptoBlock from '@/components/CryptoBlock/cryptoBasicInformation';
export default {
  data() {
    return {
      typeValue: ''
    };
  },
  components: {
    CryptoBlock
  },
  props: {
    isCrypt: Boolean, // 是否加解密
    isSelectReset: Boolean, // 切换select是否重置属性
    disabled: Boolean,
    selectWatch: { type: String, default: '130px' },
    option: { type: Array, default: () => [] }, //  { label: 'xxx', value: 'xxx' , defaultValueType: [] | {} | "" | ALL , $attrs:{}}
    value: { type: String, default: '' },
    defaultType: { type: String, default: '' },
    bizType: { type: String, default: '' },
    detailAuth: { type: String, default: '' },
    editAuth: { type: String, default: '' },
    bizId: { type: String, default: '' }
  },
  watch: {
    defaultType: {
      handler(val) {
        this.typeValue = val;
      },
      immediate: true
    }
  },
  computed: {},
  created() {},
  mounted() {},

  methods: {
    typeValueChange(v) {
      this.$emit('getDefaultType', v);
    },
    input(v) {
      this.$emit('getDefaultType', this.typeValue);
      this.$emit('input', v);
    },
    // edit() {
    //   this.$emit('edit');
    // },
    // toView(callback) {
    //   this.$emit('view', (flag) => {
    //     callback && callback(flag);
    //   });
    // }
  }
};
</script>
<style lang='scss' scoped>
.combo__box {
  display: flex;
  align-items: center;
  &__select {
    margin-right: 6px;
  }
}
</style>
