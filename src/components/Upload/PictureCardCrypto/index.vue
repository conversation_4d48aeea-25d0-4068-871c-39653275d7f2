<template>
  <div class="picture_card_crypto">
    <div class="picture_card_crypto--header" v-if="!isHide">
      <PasteUpload v-if="!disabled && realLimit" @onSuccess="gerImgList" :limit="realLimit" :headers="headers" :action="action" v-bind="$attrs"> </PasteUpload>
      <Authority auth="/distributor-management/:decrypt-attachment">
        <el-button class="to-view-btn" :style="{ marginLeft: !disabled && realLimit ? '10px' : '0' }" type="text" @click="toViewDecryption" :disabled="isDecryption" v-if="originalUrls.length">查看解密附件</el-button>
      </Authority>
    </div>
    <div class="picture_card_crypto--view">
      <PictureCard :disabled="disabled" :limit="realLimit" :value="value" @input="cardInput" :accept="accept" v-bind="$attrs" isSlotFile>
        <div slot="file" slot-scope="{ file }">
          <li class="default_image-box--li">
            <template v-if="file.response">
              <div class="default_image-box--file" v-if="isNotImgFile(file.url)">{{ file.name }}</div>
              <img v-else class="default_image-box--img" :src="fn(file)" />
            </template>
            <template v-else>
              <div class="default_image-box--file" v-if="isNotImgFile(file.url) && isDecryption">{{ file.name || '查看附件' }}</div>
              <img v-else class="default_image-box--img" :src="fn(file)" />
            </template>
            <label class="el-upload-list__item-status-label">
              <i
                :class="{
                  'el-icon-upload-success': true,
                  'el-icon-circle-check': listType === 'text',
                  'el-icon-check': ['picture-card', 'picture'].indexOf(listType) > -1
                }"
              ></i>
            </label>
            <span class="default_image-box--back">
              <span class="default_image-box--back__item-preview" @click="onPreview(file)">
                <i class="el-icon-zoom-in"></i>
              </span>
              <span v-if="!disabled" class="default_image-box--back__item-delete" @click="onRemove(file)">
                <i class="el-icon-delete"></i>
              </span>
            </span>
          </li>
        </div>
      </PictureCard>
    </div>
    <el-dialog :visible.sync="dialogVisible" append-to-body title="文件预览">
      <a v-if="isNotImgFile(dialogImageUrl)" class="file-link" :href="dialogImageUrl">{{ dialogImageUrl }}</a>
      <img v-else :src="dialogImageUrl" alt width="100%" />
    </el-dialog>
  </div>
</template>
<script>
// 图片上传加密解密组件
import PictureCard from '@/components/Upload/PictureCard';
import PasteUpload from '@/components/Upload/PasteUpload';
import { getHeadersWithExtTenant } from '@/utils/request';
import uniqueId from 'lodash/uniqueId';

export default {
  name: 'picture-card-crypto',
  data() {
    return {
      listType: 'picture-card',
      action: `${process.env.VUE_APP_BASE_URL}/file-service/api/image/upload`,
      headers: getHeadersWithExtTenant(),
      dialogVisible: false,
      dialogImageUrl: '', // 预览
      cryptoImg: 'https://oss.syounggroup.com/static/file/soyoung-zg/aliyun/soyoung-zg/pc/crypto-img.png', // 加密图片
      isDecryption: false,
      whiteList: ['png', 'jpg', 'jpeg', 'gif', 'webp']
    };
  },
  watch: {},
  computed: {
    realLimit() {
      // 真实数量限制
      return this.limit - this.value.length;
    },
    originalUrls() {
      // 默认图片list
      // 过滤自己上传的图片
      return this.value.filter((item) => !item.response);
    }
  },
  mounted() {
    this.isDecryption = this.izDecryption;
  },
  methods: {
    isNotImgFile(url) {
      if (!url) {
        return true;
      }
      return this.whiteList.indexOf(url.substring(url.lastIndexOf('.') + 1)) === -1;
    },
    fn(file) {
      return this.isDecryption ? file.url : this.cryptoImg;
    },
    // 解密
    toViewDecryption() {
      this.isDecryption = true;
    },
    // 查看
    onPreview(item) {
      if (item.response) {
        // 自己上传的
        this.dialogImageUrl = item.url;
        this.dialogVisible = true;
      } else {
        // 历史数据
        this.dialogImageUrl = this.isDecryption ? item.url : this.cryptoImg;
        this.dialogVisible = true;
      }
    },
    cardInput(arr) {
      this.$emit('input', arr);
    },
    // 删除
    onRemove(file) {
      const fileList = this.value;
      fileList.splice(fileList.indexOf(file), 1);
      this.cardInput(fileList);
    },
    // 获取图片粘贴上传
    gerImgList(arr) {
      this.$emit('input', [
        ...this.value,
        ...arr.map((url) => ({
          url,
          uid: uniqueId('paste-upload_')
        }))
      ]);
    }
  },
  components: {
    PictureCard,
    PasteUpload
  },
  props: {
    disabled: {
      // 是否可编辑
      type: Boolean,
      default: false
    },
    isHide: {
      // 是否隐藏粘贴图片按钮
      type: Boolean,
      default: false
    },
    value: {
      type: Array,
      default() {
        return [];
      }
    },
    limit: Number,
    accept: {
      type: String,
      default: '.jpeg,.png,.jpg'
    },
    izDecryption: {
      // 预览时是否解密
      type: Boolean,
      default: false
    }
  }
};
</script>
<style lang="scss" scoped>
.picture_card_crypto {
  &--view {
    display: flex;
    flex-wrap: wrap;
  }
  &--header {
    display: flex;
    align-items: center;
    min-height: 42px;
  }
}
.to-view-btn {
  margin-bottom: 6px;
}
ul {
  list-style: none; /*清除列表默认样式*/
  padding: 0; /*清除padding*/
}
.default_image-box {
  margin: 0;
  display: flex;
  vertical-align: top;
  flex-wrap: wrap;
  &--li {
    overflow: hidden;
    background-color: #fff;
    border-radius: 6px;
    box-sizing: border-box;
    width: 100%;
    height: 100%;
    margin: 0 8px 8px 0;
    display: inline-block;
    position: relative;
  }
  &--img {
    width: 100%;
    height: 100%;
  }
  &--file {
    padding: 0 8px;
    height: 100%;
    line-height: 150%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  &--label {
    display: block;
    position: absolute;
    right: -15px;
    top: -6px;
    width: 40px;
    height: 24px;
    background: #13ce66;
    text-align: center;
    transform: rotate(45deg);
    box-shadow: 0 0 1pc 1px rgb(0 0 0 / 20%);
    color: #fff;
    line-height: inherit;
    font-size: 14px;
    i {
      font-size: 12px;
      margin-top: 11px;
      -webkit-transform: rotate(-45deg);
      transform: rotate(-45deg);
    }
  }
  &--back {
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    cursor: default;
    text-align: center;
    color: #fff;
    opacity: 0;
    font-size: 20px;
    background-color: rgba(0, 0, 0, 0.5);
    -webkit-transition: opacity 0.3s;
    transition: opacity 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
    span {
      cursor: pointer;
    }
    &:hover {
      opacity: 1;
    }
    &:hover span {
      display: inline-block;
    }
    &:hover + .default_image-box .default_image-box--label {
      display: none;
    }
    .el-icon-delete {
      margin-left: 15px;
    }
  }
}
.file-link {
  color: var(--color-primary) !important;
  text-decoration: underline;
}
</style>
