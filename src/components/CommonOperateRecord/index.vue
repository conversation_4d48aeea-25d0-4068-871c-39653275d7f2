<template>
  <sy-normal-table v-bind="table" />
</template>
<script>
import { businessRecordLogListPageForUnifiedLogs } from '@/api/common';

export default {
  name: 'CommonOperateRecord',
  props: {
    bizId: {
      type: String,
      required: true,
      default: ''
    },
    bizType: {
      type: String,
      required: true,
      default: ''
    }
  },
  data() {
    return {
      isShowTooltip: false,
      statusType: {
        NEW: '新增',
        UPDATE: '修改',
        ENABLED: '启用',
        STOPPED: '停用',
        SHOW: '显示',
        HIDE: '隐藏'
      }
    };
  },
  computed: {
    // 操作记录
    table() {
      const that = this;
      return {
        tableBind: {
          maxHeight: 700
        },
        filters: [],
        columns() {
          return [
            {
              label: '序号',
              type: 'index',
              width: 60
            },
            {
              label: '操作类型',
              width: 80,
              render: (h, { row }) => (
                <div v-frag>
                  <p>{that.statusType[row.diffContent.optType]}</p>
                </div>
              )
            },
            {
              label: '操作人',
              prop: 'createByName',
              multiLine: 3,
              width: 80
            },
            {
              type: 'time',
              prop: 'createDate',
              label: '操作时间',
              width: 150,
              format: 'yyyy-MM-dd hh:mm:ss'
            },
            {
              label: '操作说明',
              render: (h, { row }) => (
                <div v-frag>
                  {row.diffContent && row.diffContent.changes.length && (
                    <el-tooltip effect="dark" popper-class="operation-instructions-tooltip" placement="top-start" disabled={!that.isShowTooltip}>
                      <div slot="content" class="operation-instructions-remarks-content">
                        {that.operateContentList(row.diffContent.changes)}
                      </div>
                      <span class="operation-instructions-remarks-handler" on-mouseenter={that.visibilityChange()}>
                        {that.operateContentList(row.diffContent.changes)}
                      </span>
                    </el-tooltip>
                  )}
                </div>
              )
            }
          ];
        },
        async search({ filtersValue, pageFilter }) {
          const { pageNo, pageSize } = pageFilter;
          const { bizId, bizType } = that;
          const res = await businessRecordLogListPageForUnifiedLogs({
            data: {
              ...filtersValue,
              bizId,
              bizType
            },
            pageNo,
            pageSize
          });
          const list = res.data?.list || [];
          const total = res.data?.total || 0;
          return {
            list,
            total
          };
        }
      };
    }
  },
  methods: {
    // 是否提示toolTip
    visibilityChange() {
      return (e) => {
        const ev = e.target;
        this.isShowTooltip = ev.scrollWidth > ev.offsetWidth || ev.scrollHeight > ev.offsetHeight;
      };
    },
    operateContentList(list) {
      return (list && list.length && list.join('')) || '-';
    }
  }
};
</script>
<style lang="scss">
.operation-instructions-tooltip {
  white-space: pre-line;
  max-height: 400px;
}
.operation-instructions-remarks-content {
  padding-right: 5px;
  max-height: 385px;
  overflow-y: scroll;
}
.operation-instructions-remarks-handler {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  overflow: hidden;
  -webkit-line-clamp: 6;
  white-space: pre-line;
}
</style>
