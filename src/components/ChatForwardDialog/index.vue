<template>
  <div>
    <el-dialog
      :title="title"
      :visible="visible"
      @update:visible="$emit('update:visible', $event)"
      width="400px"
    >
      <div class="filterbox">
        <el-input
          class="input"
          size="small"
          placeholder="输入客服昵称或分组名称"
          v-model="filterText"
          clearable
        ></el-input>
        <div class="visiblePopoverBox">
          <SvgIcon
            v-popover:visiblePopover
            class="staff-icon"
            iconClass="paixu"
          ></SvgIcon>
          <el-popover
            ref="visiblePopover"
            placement="bottom"
            width="130"
            trigger="click"
          >
            <el-radio class="el-radiobox" v-model="sort" label="1"
              >接待中人数</el-radio
            >
            <br />
            <el-radio class="el-radiobox" v-model="sort" label="2"
              >接待余量</el-radio
            >
          </el-popover>
        </div>
      </div>
      <div>
        <el-tree
          class="filter-tree"
          :data="list"
          :props="defaultProps"
          :filter-node-method="filterNode"
          ref="tree"
          highlight-current
          :render-content="renderContent"
          @node-click="handleCheckClick"
        ></el-tree>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="$emit('update:visible', false)" size="small"
          >取 消</el-button
        >
        <el-button
          type="primary"
          size="small"
          @click="onSubmit"
          :loading="submitLoading"
          >确 定</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  forwardList,
  forward,
  forwardBatch,
  forwardDialogue,
  handOver
} from '@/api/chat/chat-line';
import SvgIcon from '@/components/SvgIcon';
export default {
  name: '',
  components: { SvgIcon },
  props: {
    isSupervisor: Boolean,
    visible: Boolean,
    title: {
      type: String,
      default: '转接会话'
    },
    type: {
      // batch 批量指派 、a_batch 单独指派 、 single 单独转接
      type: String,
      default: 'single'
    },
    dataList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      sort: '1',
      list: [],
      filterText: '',
      checkedData: {},
      submitLoading: false,
      defaultProps: {
        children: 'customerServiceList',
        label: (data, node) => {
          const { groupId } = data;
          if (groupId) {
            return `${data.groupName}(${data.onlineNum}/${data.totalNum})`;
          } else {
            return `${data.csName}(${data.inServiceNum}/${data.maxServiceLimit})`;
          }
        }
      }
    };
  },
  created() {},
  computed: {},
  watch: {
    visible(val) {
      if (val) {
        this.getForwardList();
        this.filterText = '';
      }
    },
    filterText(val) {
      this.$refs.tree.filter(val);
    },
    sort(val) {
      const list = [...this.list];
      if (val === '1') {
        // 接待人数排序
        list.forEach(({ customerServiceList }) => {
          if (customerServiceList) {
            customerServiceList.sort((a, b) => {
              return b.inServiceNum - a.inServiceNum;
            });
          }
        });
        this.list = [...list];
      } else {
        // 余量排序
        list.forEach(({ customerServiceList }) => {
          if (customerServiceList) {
            customerServiceList.sort((a, b) => {
              return (
                b.maxServiceLimit -
                b.inServiceNum -
                (a.maxServiceLimit - a.inServiceNum)
              );
            });
          }
        });
        this.list = [...list];
      }
    }
  },
  methods: {
    renderContent(h, { node, data, store }) {
      let opacity = '1';
      if (data.disabled) opacity = '0.6';
      if (data.groupId) {
        return (
          <div style={{ opacity: opacity }}>
            <SvgIcon
              iconClass="jiedianhuiyuanfenzu"
              className="staff-icon"
            ></SvgIcon>
            <span style="margin: 0 8px;">{data.groupName}</span>
            <span>
              ({data.onlineNum}/{data.totalNum})
            </span>
          </div>
        );
      }
      let color = '#cccccc';
      if (data.status === 'ONLINE') {
        // 在线
        color = '#07fa27';
      } else if (data.status === 'BUSY') {
        // 繁忙
        color = 'orange';
      }
      return (
        <div style={{ opacity: opacity }}>
          <SvgIcon
            iconClass="kefu11"
            style={{ color: color }}
            className="staff-icon"
          ></SvgIcon>
          <span style="margin: 0 8px;"> {data.csName}</span>
          <span>
            ({data.inServiceNum}/{data.maxServiceLimit})
          </span>
          <span style="margin: 0 8px;font-size:12px;color:#999">
            {data.status === 'OFFLINE' ? ' 离线' : ''}
          </span>
        </div>
      );
    },
    filterNode(value, data) {
      if (!value) return true;
      if (data.groupId) {
        return data.groupName.indexOf(value) !== -1;
      } else {
        return data.csName.indexOf(value) !== -1;
      }
    },
    getForwardList() {
      forwardList().then((res) => {
        this.list = res.data.map((item) => ({
          ...item,
          disabled: item.customerServiceList.length === 0,
          customerServiceList: item.customerServiceList.map((val) => ({
            ...val,
            disabled: val.status === 'OFFLINE'
          }))
        }));
      });
    },
    onSubmit() {
      const { groupId, csId, customerServiceList, status } = this.checkedData;
      const sessionIdList = [...this.dataList];
      const data = {
        sessionIdList
      };

      if (this.isSupervisor) {
        if (groupId) {
          this.$message({
            type: 'error',
            message: '请选择转接给个人！'
          });
          return;
        }
        if (status === 'OFFLINE') {
          this.$message({
            type: 'error',
            message: '无法选择离线的客服!'
          });
          return;
        }
        this.submitLoading = true;
        handOver(sessionIdList[0], csId)
          .then((res) => {
            this.$message({
              type: 'success',
              message: '操作成功!'
            });
            this.filterText = '';
            this.$emit('update:visible', false);
            this.$emit('success', sessionIdList);
          })
          .finally(() => {
            this.submitLoading = false;
          });
        return;
      }
      if (groupId) {
        if (customerServiceList.length === 0) {
          this.$message({
            type: 'error',
            message: '无法选择无可用客服的分组!'
          });
          return;
        }
        data.groupId = groupId;
      }
      if (csId) {
        if (status === 'OFFLINE') {
          this.$message({
            type: 'error',
            message: '无法选择离线的客服!'
          });
          return;
        }
        data.csId = csId;
      }
      let req = forwardDialogue; // 单个转接
      if (this.type === 'batch') {
        // 批量指派
        req = forwardBatch;
        if (data.sessionIdList.length <= 0) {
          this.$message({
            type: 'error',
            message: '批量指派未选中人员!'
          });
          return;
        }
      }
      if (this.type === 'a_batch') {
        // 单个指派
        req = forward;
      }
      this.submitLoading = true;
      req(data)
        .then((res) => {
          this.$message({
            type: 'success',
            message: '操作成功!'
          });
          this.filterText = '';
          this.$emit('update:visible', false);
          this.$emit('success', sessionIdList);
        })
        .finally(() => {
          this.submitLoading = false;
        });
    },
    handleCheckClick(data, checked, indeterminate) {
      let obj = {};
      obj = { ...data };
      this.checkedData = { ...obj };
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.filterbox {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  .input {
    width: 200px;
  }
  .visiblePopoverBox {
    position: relative;
    .staff-icon {
      cursor: pointer;
      font-size: 18px;
    }
  }
}
.el-radiobox {
  line-height: 26px;
}
.filter-tree {
  max-height: 400px;
  overflow: auto;
}
</style>
