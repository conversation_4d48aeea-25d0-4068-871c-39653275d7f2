<template>
  <el-dialog title="专属推广码" :visible="dialogVisible" width="900px" append-to-body top="5vh" @open="open" @close="close">
    <el-tabs @tab-click="handleClick" v-model="activeName" class="custom-border-tabs">
      <el-tab-pane label="推广码" name="qrcode">
        <div class="generate" v-loading="loading">
          <div class="generate-item" v-for="(value, key) in code" :key="key">
            <div class="generate-item__title">
              <h3 v-if="key === 'temporary'">临时推广码（有效期30天）</h3>
              <h3 v-else>长期推广码</h3>
            </div>
            <div class="generate-item__content">
              <div class="generate-item__content-left" :id="key + '_qrcode'">
                <img :src="value.mpCode" class="qrcode-img" crossOrigin="anonymous" />
                <template v-if="key === 'temporary'">
                  <p class="color-info" v-if="expirationDate">截止 {{ expirationDate }} 有效</p>
                  <p>失效后，扫码可联系销售主管</p>
                </template>
                <p class="color-info" v-else>该小程序码长期有效</p>
              </div>
              <div class="generate-item__content-right">
                <div class="item">
                  <el-input v-model="value.mpPageUrl" readonly></el-input>
                  <el-button type="primary" v-clipboard:copy="value.mpPageUrl" v-clipboard:error="onError" v-clipboard:success="onCopy">复制小程序路径</el-button>
                  <button-hoc type="primary" @click="drawImage(key, 'qrcode')">下载小程序码</button-hoc>
                </div>
                <div class="item">
                  <el-input v-model="value.merchantPageUrl" type="textarea" :autosize="{ minRows: 2, maxRows: 3 }" readonly></el-input>
                  <el-button type="primary" v-clipboard:copy="value.merchantPageUrl" v-clipboard:error="onError" v-clipboard:success="onCopy">复制商家端路径</el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane label="海报" name="poster">
        <div class="poster-wrap">
          <div class="poster" v-for="(value, key) in code" :key="key">
            <div class="poster-header">
              <h3 v-if="key === 'temporary'">临时推广码（有效期30天）</h3>
              <h3 v-else>长期推广码</h3>
              <button-hoc type="primary" @click="drawImage(key, 'poster')">下载海报</button-hoc>
            </div>
            <div class="poster-content" :id="key + '_poster'">
              <img src="@/assets/poster-bg.png" alt="" class="poster-content__bg" />
              <div class="poster-content__footer">
                <div class="footer-left">
                  <div class="cs-info">
                    <h3>{{ developCSName }}</h3>
                    <span class="tag">资深销售主管</span>
                  </div>
                  <p>扫码可联系您的销售主管</p>
                </div>
                <div class="footer-right">
                  <img :src="value.mpCode" class="qrcode-img" crossOrigin="anonymous" />
                  <p v-if="key === 'temporary' && expirationDate">{{ expirationDate }} 前有效</p>
                  <p v-else>该小程序码长期有效</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
  </el-dialog>
</template>
<script>
import { fetchCode } from '@/api/common/promotion';
import { longToShort } from '@/api/common/dict';
import { parseTime } from '@/utils';
import { toPng } from 'html-to-image';
export default {
  name: 'CaptivePromotion',
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    developCSId: {
      type: String,
      default: ''
    },
    developCSName: {
      type: String,
      default: ''
    },
    promType: {
      type: String,
      default: 'home'
    }
  },
  data() {
    return {
      activeName: 'qrcode',
      loading: false,
      code: {
        temporary: {
          mpCode: '',
          mpPageUrl: '',
          merchantPageUrl: ''
        },
        permanent: {
          mpCode: '',
          mpPageUrl: '',
          merchantPageUrl: ''
        }
      },
      timestamp: null
    };
  },
  computed: {
    merchantBaseUrl() {
      return process.env.NODE_ENV === 'production' ? 'https://syzg.syounggroup.com' : 'https://testsyzg.syounggroup.com';
    },
    // 失效时间
    expirationDate() {
      const { timestamp } = this;
      if (!timestamp) {
        return '';
      }
      return parseTime(timestamp, '{y}/{m}/{d}');
    }
  },
  methods: {
    onCopy() {
      this.$message.success('复制成功');
    },
    onError() {
      this.$message.error('复制失败');
    },
    // 生成图片并下载
    drawImage(key, type) {
      const name = key === 'temporary' ? '临时' : '长期';
      const typeName = type === 'qrcode' ? '推广码' : '推广海报';
      const el = document.getElementById(`${key}_${type}`);
      toPng(el, { pixelRatio: 2 })
        .then((dataUrl) => {
          const link = document.createElement('a');
          link.download = `${name}${typeName}`;
          link.href = dataUrl;
          link.click();
        })
        .catch(() => {
          this.$message.error('生成图片错误，请稍后重试');
        });
    },
    // 生成微信小程序码
    async generateMpCode(type) {
      const { developCSId } = this;
      let params = `developCSId=${developCSId}&type=${type}`;
      // 临时码
      if (type === 'temporary') {
        const now = new Date();
        const timestamp = new Date(now).setDate(new Date(now).getDate() + 30); // 30天
        // const timestamp = new Date(now.getTime() + 1 * 60 * 1000).getTime(); // 10分钟
        this.timestamp = timestamp;
        params = `developCSId=${developCSId}&type=${type}&timestamp=${timestamp}`;
      }
      const page = 'pages/index';
      const reqParams = params.replace(/&/g, ',');
      const res = await longToShort(reqParams);
      const scene = `short=${res.data}`;
      const postData = {
        bizType: 'PROMOTION',
        page,
        scene
      };
      const result = await fetchCode(postData);
      this.code[type].mpCode = result.data.msg;
      this.code[type].mpPageUrl = `/${page}?${scene}`;
      this.code[type].merchantPageUrl = `${this.merchantBaseUrl}?${params}`;
    },
    handleClick({ name }) {
      this.activeName = name;
      this.getData();
    },
    // 打开弹窗
    open() {
      this.getData();
    },
    // 关闭弹窗
    close() {
      this.$emit('update:dialogVisible',false);
    },
    //  生成
    async getData() {
      if (!this.developCSId) {
        return;
      }
      this.loading = true;
      await this.generateMpCode('temporary');
      await this.generateMpCode('permanent');
      this.loading = false;
    }
  }
};
</script>

<style scoped lang="scss">
::v-deep {
  .el-dialog__body {
    padding: 10px 24px;
  }
}
.generate {
  &-item {
    margin-bottom: 20px;
    &__title {
      margin-bottom: 12px;
    }
    &__content {
      display: flex;
      &-left {
        background-color: #fff;
        margin-right: 12px;
        width: 160px;
        padding-bottom: 4px;
        p {
          text-align: center;
          font-size: 12px;
          line-height: 150%;
        }
        .qrcode-img {
          width: 100%;
        }
      }
      &-right {
        flex: 1;
        .item {
          display: flex;
          align-items: flex-start;
          canvas {
            display: none;
          }
          &:not(:last-child) {
            margin-bottom: 10px;
          }
          .link {
            margin-left: 10px;
          }
          ::v-deep {
            .el-textarea,
            .el-input {
              width: 400px;
              margin-right: 10px;
            }
          }
        }
      }
    }
  }
}
.poster-wrap {
  display: flex;
  justify-content: space-between;
}
.poster {
  width: 375px;
  &-header {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    h3 {
      flex: 1;
    }
  }
  &-content {
    position: relative;
    width: 100%;
    &__bg {
      width: 100%;
    }
    &__footer {
      position: absolute;
      bottom: 8px;
      left: 18px;
      right: 18px;
      display: flex;
      align-items: center;
      .footer-left {
        flex: 1;
        .cs-info {
          margin-bottom: 6px;
          display: flex;
          align-items: center;
          h3 {
            color: #333;
            font-size: 24px;
            margin-right: 10px;
          }
          .tag {
            display: inline-flex;
            padding: 2px 6px;
            justify-content: center;
            align-items: center;
            border-radius: 2px;
            background: linear-gradient(90deg, #e1d4bf 0%, #f4efe6 100%);
            color: #463422;
            text-align: center;
            font-size: 12px;
          }
        }
        p {
          color: #999;
          font-size: 14px;
          line-height: 150%;
        }
      }
      .footer-right {
        .qrcode-img {
          width: 85px;
        }
        p {
          color: #999;
          font-size: 8px;
          text-align: center;
        }
      }
    }
  }
}
</style>
