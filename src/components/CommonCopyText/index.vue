<template>
  <div v-frag>
    <el-button v-if="btnType === 'primary'" type="primary" size="mini" @click.stop="copyHandle" style="margin: 0">{{ btnText }}</el-button>
    <el-button v-if="btnType === 'icon'" icon="el-icon-copy-document" style="font-size: 14px" type="text" @click.stop="copyHandle" />
  </div>
</template>

<script>
export default {
  name: 'CommonCopyText',
  props: {
    desc: String,
    btnText: {
      type: String,
      default: '复制'
    },
    btnType: {
      type: String,
      default: 'icon'
    },
    value: {
      type: String | Number,
      required: true
    }
  },
  methods: {
    copyHandle() {
      const { value, desc } = this;
      if (!value) {
        this.$message.error('值不能为空');
        return;
      }
      this.$copyText(value)
        .then((event) => {
          const msg = desc ? `${desc} ${event.text} 复制成功` : `${event.text} 复制成功`;
          this.$message.success(msg);
        })
        .catch(() => {
          this.$message.info(`${desc}复制失败,请手动复制`);
        });
    }
  }
};
</script>
