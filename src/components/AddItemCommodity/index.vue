<!--添加组套商品组价 -->
<template>
  <div>
    <el-button type="text" @click="addCommodity">添加商品</el-button>
    <!--展示选中的套装商品-->
    <table class="stock-table" border="0" rules="none" v-if="commodityList.length > 0">
      <thead>
        <tr>
          <th>商品名</th>
          <th width="100">商品缩略图</th>
          <th>规格</th>
          <th>数量</th>
          <th>操作</th>
        </tr>
      </thead>
      <tbody class="tbody-flow">
        <tr v-for="item in commodityList" :key="item.id">
          <td>
            <div class="seckill-td">
              <div>{{ item.name }}</div>
            </div>
          </td>
          <td>
            <div class="seckill-td">
              <img :src="item.thumbnailUrl" alt class="goods-img" />
            </div>
          </td>
          <td>
            <div>
              {{ item.firstLevel }}
              {{ item.secondLevel }}
              {{ item.threeLevel }}
            </div>
          </td>
          <td>
            <div>
              <input v-model.number="item.quantity" type="number" />
              <validate
                ref="validate"
                class="validate-style"
                :inputValue="item.quantity"
                @validate="inputValidate"
              ></validate>
            </div>
          </td>
          <td>
            <div>
              <el-button type="text" @click="remove(item.commodityId)">删除</el-button>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
    <el-dialog title="选择商品" :visible.sync="dialogFormVisible" :append-to-body="true" width="1000px">
      <div class="top-info">
        <el-input placeholder="搜索商品名称" v-model.trim="name" class="input-with-select"></el-input>
        <el-input placeholder="搜索商品id" v-model.trim="id" class="input-with-select"></el-input>
        <el-select v-model="status" placeholder="请选择">
          <el-option
            v-for="item in statusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
        <el-button type="primary" @click="onSearch">查询</el-button>
        <el-button @click="onReset" type="primary">刷新</el-button>
       <div class="router-links">
          <router-link class="link" :to="'/commodity/list'">
          <span class="link">商品管理</span>
        </router-link>
       </div>
      </div>

      <el-table
        :data="list"
        v-loading="listLoading"
        element-loading-text="加载中"
        fit
        highlight-current-row
      >
        <el-table-column property="id" label="id"></el-table-column>       
        <el-table-column property="typeName" label="商品类型"></el-table-column>
        <el-table-column label="商品缩略图">
          <template slot-scope="imgUrl">
            <img
              :src="imgUrl.row.thumbnailUrl"
              alt
              class="goods-img"
              v-if="!imgUrl.row.skuCommodityVO"
            />
            <img
              :src="imgUrl.row.skuCommodityVO.thumbnailUrl"
              alt
              class="goods-img"
              v-if="imgUrl.row.skuCommodityVO"
            />
          </template>
        </el-table-column>
        <el-table-column property="name" label="商品名称"></el-table-column>

        <el-table-column label="价格">
          <template slot-scope="scope">
            <div v-if="scope.row.maxPrice === scope.row.minPrice">{{ scope.row.minPrice }}</div>
            <div v-else>{{ scope.row.minPrice }}-{{ scope.row.maxPrice }}</div>
          </template>
        </el-table-column>
        <el-table-column label="状态">
          <template slot-scope="scope">{{ scope.row.statusName }}</template>
        </el-table-column>
        <el-table-column label="操作" fixed="right">
          <template slot-scope="operation">
            <el-button
              :disabled="
                operation.row.type === 'CARD' ||
                  operation.row.type === 'VIRTUAL'
              "
              @click="chooseSpec(operation.row)"
              type="text"
            >选择规格</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pageNo"
          :page-sizes="[5, 10, 20, 30]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          :disabled="listLoading"
        ></el-pagination>
      </div>
      <el-dialog width="40%" title="选择规格" :visible.sync="innerVisible" append-to-body>
        <table class="stock-table" border="0" rules="none">
          <thead>
            <tr>
              <th v-for="(spec, i) in specList" :key="i">{{ spec.name }}</th>
              <th>价格</th>
              <th>库存</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(sku, i) in skuList" :key="i">
              <td v-for="(spec, j) in specList" :key="j">{{ handleSpecData(i, j) }}</td>
              <td>{{ sku.price }}</td>
              <td>{{ sku.stock }}</td>
              <td>
                <el-button type="text" v-if="commoditySet.has(sku.id)" disabled>已添加</el-button>
                <el-button @click="handleJoin(sku)" type="text" v-else>添加</el-button>
              </td>
            </tr>
          </tbody>
        </table>
      </el-dialog>
    </el-dialog>
  </div>
</template>

<script>
import { fetchListPageCommodity, listSpec } from '@/api/common/commodity';
import Validate from './validate';
export default {
  name: 'AddItemCommodity',
  model: {
    prop: 'commodityList',
    event: 'updateTable'
  },
  components: {
    Validate
  },
  props: {
    commodityList: {
      type: Array,
      default() {
        return [];
      }
    },
    // 是否显示选择框
    outerDialogFormVisible: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    outerDialogFormVisible(val) {
      this.addCommodity();
    }
  },
  computed: {
    commoditySet() {
      return new Set(this.commodityList.map(({ skuId }) => skuId));
    },
    validateArr() {
      const validateResultArr = this.$refs['validate'];
      return validateResultArr || [];
    }
  },
  data() {
    return {
      validateAll: false, // 组套商品数量填写是否全部校验通过
      id: '',
      name: '',
      // 商品上架状态
      status: '1',
      statusOptions: [
        {
          value: '0',
          label: '待上架'
        },
        {
          value: '1',
          label: '上架中'
        },
        {
          value: '3',
          label: '已下架'
        }
      ],
      listLoading: false,
      list: [],
      pageNo: 1,
      pageSize: 5,
      total: 0,
      dialogFormVisible: false,
      innerVisible: false,
      currentRow: {},
      specList: [],
      skuList: []
    };
  },
  methods: {
    // 组套商品是否校验通过
    inputValidate(msg) {
      this.validateAll = msg;
      return msg;
    },
    addCommodity() {
      this.dialogFormVisible = true;
      this.onReset();
    },
    // 商品列表数据获取
    fetchData() {
      this.listLoading = true;
      const { pageNo, pageSize, name, status, id } = this;
      const listQuery = { pageNo, pageSize, data: { name, status, id } };
      fetchListPageCommodity(listQuery)
        .then(response => {
          this.list = response.data.list;
          this.total = response.data.total;
        })
        .finally(() => {
          this.listLoading = false;
        });
    },
    fetchSpec() {
      listSpec(this.currentRow.id)
        .then(res => {
          if (res.data) {
            this.specList = res.data.specificationVOList;
            this.skuList = res.data.skuVOList;
          }
        })
        .finally(() => {
          this.listLoading = false;
        });
    },
    // 移除商品
    remove(id) {
      const list = [...this.commodityList];
      const idx = list.findIndex(({ commodityId }) => commodityId === id);
      list.splice(idx, 1);
      if (this.operation !== 'detail') {
        this.$emit('updateTable', list);
      }
    },
    handleSpecData(i, j) {
      const levelName = this.specList[j].level;
      let level = '';
      switch (levelName) {
        case '1':
          level = 'firstLevel';
          break;
        case '2':
          level = 'secondLevel';
          break;
        case '3':
          level = 'threeLevel';
          break;
      }
      return this.skuList[i][level];
    },
    // 选择规格
    chooseSpec(row) {
      this.currentRow = row;
      this.innerVisible = true;
      this.fetchSpec();
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.pageNo = 1;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.pageNo = val;
      this.fetchData();
    },
    // 查询
    onSearch() {
      this.pageNo = 1;
      this.pageSize = 5;
      this.fetchData();
    },
    // 重置
    onReset() {
      this.pageNo = 1;
      this.pageSize = 5;
      this.name = '';
      this.status = '1';
      this.id = '';
      this.fetchData();
    },
    // 选择规格产品——添加
    handleJoin(rowData) {
      const list = [...this.commodityList];
      const pushData = { ...rowData };
      pushData['skuId'] = pushData.id;
      pushData['name'] = this.currentRow.name;
      delete pushData['id'];
      this.commoditySet.has(pushData.skuId);
      if (!this.commoditySet.has(pushData.skuId)) {
        list.push(pushData);
      }
      // this.innerVisible = false;
      this.$emit('updateTable', list);
    }
  }
};
</script>

<style lang="scss" scoped>
@import './styles';
</style>
