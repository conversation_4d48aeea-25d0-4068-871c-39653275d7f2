$border: 1px solid #e5e5e5;
$color: rgba(0, 0, 0, 0.65);
$color-sub: rgba(27, 23, 23, 0.85);
.router-links {
  margin-top: 15px;
}
.commodity-td {
  min-width: 70px;
}
.goods-img {
  width: 60px;
  height: 60px;
}

.add-commodity {
  color: var(--color-primary);
  position: relative;
  .on-off {
    position: absolute;
    right: 0;
  }
  span {
    cursor: pointer;
  }
}

.top-info {
  margin-bottom: 16px;
  ::v-deep .el-input {
    width: 220px;
    margin-right: 16px;
  }
  ::v-deep .el-button--primary {
    margin-right: 16px;
  }
  .link {
    color: var(--color-primary);
    cursor: pointer;
  }
}

.stock-table {
  width: 100%;
  border: 1px solid #e5e5e5;
  text-align: left;
  & > thead {
    background-color: #f8f8f8;
    th {
      font-weight: 400;
      color: $color-sub;
    }
  }
  & > tfoot {
    border-top: $border;
  }
  td,
  th {
    padding: 16px 10px;
  }
  td {
    color: $color;
    border-top: $border;
    & + td {
      border-left: $border;
    }
  }
  .foot-label {
    color: $color-sub;
  }
  .links {
    > * + * {
      margin-left: 2px;
    }
  }
  .btns {
    margin-left: 8px;
  }
}
.tableIsPagination {
  text-align: right;
  margin-top: 8px;
}
