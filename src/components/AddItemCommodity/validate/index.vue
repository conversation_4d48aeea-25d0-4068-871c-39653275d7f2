<!--表格里input校验-->
<template>
  <div>
    <p class="tips-validate" v-if="showTips">{{ allPriceTips }}</p>
  </div>
</template>

<script>
export default {
  name: 'validate',
  model: {},
  data() {
    return {
      validateResult: false,
      showTips: false, // 是否展示校验提示
      allPriceTips: '', // 校验提示
      ereg: {
        integer: /^([1-9]\d*|[0]{1,1})$/ // 大于1的整数校验
      }
    };
  },
  computed: {},
  created() {
    this.validateInteger();
  },
  watch: {
    inputValue() {
      this.validateInteger();
    }
  },
  props: {
    inputValue: {
      default: 0
    }
  },
  methods: {
    // 大于等于1的整数校验
    validateInteger() {
      if (!this.ereg.integer.test(this.inputValue)) {
        this.showTips = true;
        this.allPriceTips = '请输入大于0的整数';
        this.validateResult = false;
        this.$emit('validate', false);
        return;
      } else if (this.inputValue < 1) {
        this.showTips = true;
        this.allPriceTips = '请输入大于0的整数';
        this.validateResult = false;
        this.$emit('validate', false);
        return;
      } else {
        this.validateResult = true;
        this.showTips = false;
        this.$emit('validate', true);
      }
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.tips-validate {
  color: red;
  margin: 0;
}
.validate-style {
  width: 100%;
}
</style>
