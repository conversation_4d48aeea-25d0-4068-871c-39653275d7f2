<template>
  <el-drawer :visible="show" @close="close" title="完善主数据渠道信息" size="60%" destroy-on-close append-to-body custom-class="common-drawer">
    <div class="drawer-header p-t-8">
      <div class="m-b-12 font-12">
        <span>当前主体信息：</span>
        <strong v-if="currentRow.platformOwner">【{{ currentRow.platformOwnerName }}】</strong>
        <strong>【乙方：{{ currentRow.merchantType === 'ENTERPRISE' ? currentRow.company : currentRow.contactRealName }}】</strong>
        <strong v-if="currentRow.ownCompanyName">、【甲方：{{ currentRow.ownCompanyName }}】</strong>
      </div>
      <el-radio-group v-model="tabActive">
        <el-radio-button label="basicInfo">基础信息</el-radio-button>
        <el-radio-button label="creationFormInfo">建档表单信息</el-radio-button>
        <el-radio-button label="operationRecord">操作记录</el-radio-button>
      </el-radio-group>
    </div>
    <div class="drawer-content" v-loading="isLoading">
      <!--基础信息-->
      <div v-if="tabActive === 'basicInfo'">
        <div class="common-line-title m-b-12">
          <span class="common-line-title__text">建档信息完善</span>
          <el-button v-if="isContinueCreate" size="mini" @click="backView">返回查看</el-button>
        </div>
        <!--继续建档-->
        <div class="section-card m-b-16" v-if="isBuildSelf && !isFormEdit">
          <span class="font-12 m-r-24">如果需要使用相同的主体创建不同品牌的合作渠道</span>
          <el-button type="primary" size="mini" @click="continueCreate">继续客户建档</el-button>
        </div>
        <template v-if="isSYZG && bindChannelInfo">
          <!--水羊直供关联渠道-->
          <div class="section-card m-b-16" v-if="bindChannelInfo.izBindChannel === '1' && bindChannelInfo.izCurrentDistributorOAFlow === '0'">
            <!--已关联-->
            <span class="font-12" v-if="bindChannelInfo.izDoubleCustomerService === '1'">该主体已由【分销商：{{ bindChannelInfo.shopName }}，专属顾问：{{ bindChannelInfo.csName }}】创建了渠道</span>
            <span class="font-12" v-else>已手动关联主体归属平台为水羊直供的渠道:【{{ bindChannelInfo.sapChannelName }}】 </span>
          </div>
          <div class="section-card m-b-16" v-if="bindChannelInfo.izBindChannel === '0'">
            <!--未在水羊直供发起OA流程-->
            <template v-if="bindChannelInfo.izOAFlow === '0'">
              <span class="font-12 m-r-24">已检测到主体归属平台为水羊直供的渠道【{{ bindChannelInfo.sapChannelName }}】已完成建档，可以直接关联</span>
              <button-hoc size="mini" type="primary" @click="autoBindChannelInfo">一键关联渠道</button-hoc>
            </template>
            <!--在水羊直供发起OA流程-->
            <template v-else>
              <template v-if="isCanBindChannel">
                <span class="font-12 m-r-24" v-if="bindChannelInfo.izDoubleCustomerService === '1'">该主体已由【分销商：{{ bindChannelInfo.shopName }}，专属顾问：{{ bindChannelInfo.csName }}】创建了渠道，可以进行直接关联渠道</span>
                <span class="font-12 m-r-24" v-else>已检测到主体归属平台为水羊直供的渠道【{{ bindChannelInfo.sapChannelName }}】已完成建档，可以直接关联</span>
                <button-hoc size="mini" type="primary" @click="autoBindChannelInfo">一键关联渠道</button-hoc>
              </template>
              <!--水羊直供关联渠道检测结果为空-->
              <template v-else>
                <div>
                  <strong class="font-12 m-r-24">暂未检测到符合该主体已完成建档的渠道信息，不能关联</strong>
                  <button-hoc size="mini" type="primary" @click="getBindChannelInfo">重新检测</button-hoc>
                </div>
                <ul class="font-12 color-info">
                  <li class="m-b-4">原因排查：</li>
                  <li>1、渠道是否已经提交建档流程，如果已提交请关注流程是否已完成；</li>
                  <li>2、渠道没有提交过建档流程，请切换到【新增渠道】去新增；</li>
                  <li>3、如果线下已经手动建档完成，任然无法关联，请联系技术进行排查；</li>
                </ul>
              </template>
            </template>
          </div>
        </template>
        <div v-show="!isFormEdit">
          <!--已完成建档的渠道信息-->
          <div class="m-b-16" v-show="doneChannelList.length">
            <div class="custom-table-header">已完成建档的渠道信息</div>
            <sy-normal-table v-bind="doneChannelTable" ref="doneChannelTableRef" />
          </div>
          <!--建档申请中的信息-->
          <div class="m-b-16" v-show="applyingChannelList.length">
            <div class="custom-table-header">建档申请中的信息</div>
            <sy-normal-table v-bind="applyingChannelTable" ref="applyingChannelTableRef" />
          </div>
          <!--合同已关联建档渠道-->
          <div class="m-b-16" v-show="relationChannelList.length">
            <div class="custom-table-header"><span v-if="isBuildSelf">合同-</span>已关联建档渠道</div>
            <sy-normal-table v-bind="relationChannelTable" ref="relationChannelTableRef" />
          </div>
        </div>
        <el-form :model="form" :rules="rules" ref="form" label-width="130px" label-position="left" v-if="isFormEdit">
          <el-form-item label="建档所需合同" prop="contractIds">
            <el-row>
              <el-col :span="14">
                <sy-select v-model="form.contractIds" v-bind="contractIdsSelectBind" />
              </el-col>
              <el-col :span="10">
                <span class="m-l-12"></span>
                <button-hoc type="text" @click="getContractList">刷新</button-hoc>
                <span class="m-l-12">找不到合同，去</span>
                <el-button type="text" @click="jumpFsContactList">认领合同</el-button>
                <el-tooltip effect="dark" placement="top">
                  <template slot="content">
                    <p>找不到合同的原因：</p>
                    <p>1、合同没有认领，需要人工去认领到分销商里面；</p>
                    <p>2、合同流程没有走完，系统没有拉取到，需要催合同审批的流程到【归档节点并上传已盖章的合同附件】；</p>
                    <p>3、合同已过期，不能进行建档，需要重新签订合同；</p>
                  </template>
                  <i class="el-icon-info m-l-4" />
                </el-tooltip>
              </el-col>
            </el-row>
          </el-form-item>
          <el-form-item label="客户简称" prop="companySimple">
            <el-input v-model="form.companySimple" :maxlength="15" :disabled="!isEmpty(mainCustomerSimpleInfo.contractInfoSimpleName)" placeholder="请输入客户简称" show-word-limit />
            <div class="color-info m-t-4">命名建议：1、如果客户名称为英文，请翻译成中文简称；2、 取名建议：使用地区+客户唯一标识+经营行业（经营行业可选，如：酒店）；</div>
          </el-form-item>
          <template v-if="isBuildSelf">
            <el-row>
              <el-col :span="12">
                <el-form-item label="渠道销售模式" prop="saleMode">
                  <el-select v-model="form.saleMode" clearable placeholder="请选择销售模式">
                    <el-option v-for="item in saleModeOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="销售币种" prop="currency">
                  <el-select v-model="form.currency" clearable placeholder="请选择币种">
                    <el-option v-for="item in currencyOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <div class="com-flex">
              <el-form-item label="渠道结算方式" prop="settlementType">
                <el-select v-model="form.settlementType" clearable class="m-r-12">
                  <el-option :key="index" :label="item.label" :value="item.value" v-for="(item, index) in settlementTypeOptions"></el-option>
                </el-select>
              </el-form-item>
              <template v-if="['SALE_CREDIT', 'DISTRIBUTE_SALE_CREDIT'].includes(form.settlementType)">
                <el-form-item label="" label-width="auto" prop="payCondition">
                  <el-select v-model="form.payCondition" clearable placeholder="请选择结算周期" class="m-r-12">
                    <el-option :key="index" :label="item.label" :value="item.value" v-for="(item, index) in settlementPeriodOptions"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="" label-width="auto" prop="payConditionDay" v-if="form.payCondition === 'ZD99'">
                  <el-input v-model.trim="form.payConditionDay" placeholder="请输入结算天数">
                    <template slot="append">天</template>
                  </el-input>
                </el-form-item>
              </template>
            </div>
            <el-row>
              <el-col :span="12">
                <el-form-item label="渠道归属部门" prop="channelDept">
                  <el-select v-model="form.channelDept" clearable placeholder="请选择渠道归属部门" @change="belongDepartmentChange">
                    <el-option v-for="item in belongDepartmentOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="所属事业部" prop="dept">
                  <el-select v-model="form.dept" placeholder="请先选择渠道归属部门" disabled>
                    <el-option v-for="item in deptOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item label="对应成本中心" prop="costCenterCode">
              <el-select v-model="form.costCenterCode" filterable clearable placeholder="请选择对应成本中心">
                <el-option v-for="item in costCenterList" :key="item.code" :label="item.name" :value="item.code"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="渠道合作品牌" prop="brandType">
              <el-radio-group v-model="form.brandType">
                <div class="com-flex">
                  <el-radio label="MULTI_BRAND">多品牌</el-radio>
                  <el-radio label="SINGLE_BRAND">单品牌</el-radio>
                  <template v-if="form.brandType === 'SINGLE_BRAND'">
                    <el-form-item label-width="auto" prop="brandId" style="margin: 0">
                      <div class="com-flex">
                        <sy-select v-model="form.brandId" v-bind="basicBrandSelectBind" style="width: 240px" />
                        <span class="m-l-12"></span>
                        <button-hoc type="text" @click="getBrandList">刷新</button-hoc>
                        <div class="m-l-12">找不到品牌，去<el-button type="text" @click="jumpBrand">新增品牌</el-button></div>
                      </div>
                    </el-form-item>
                  </template>
                </div>
              </el-radio-group>
            </el-form-item>
          </template>

          <div class="common-line-title m-b-12"><span class="common-line-title__text">建档其他补充信息</span></div>
          <el-form-item label="流程备注" prop="flowRemarks">
            <el-input v-model="form.flowRemarks" :maxlength="300" placeholder="请输入" show-word-limit :autosize="{ minRows: 4, maxRows: 7 }" type="textarea" />
          </el-form-item>
          <el-form-item label="流程备注附件" prop="attachmentFileList" v-if="isSYZG">
            <sy-upload v-model="form.attachmentFileList" v-bind="attachmentBind"></sy-upload>
            <p class="color-info">文件格式：PDF、docx、doc，最多5份；图片格式：jpg、jpeg、png，最多5张;</p>
          </el-form-item>
          <el-form-item label="流程备注附件" prop="remarkAttachmentFiles" v-if="isBuildSelf">
            <sy-upload v-model="form.remarkAttachmentFiles" v-bind="attachmentBind"></sy-upload>
            <p class="color-info">文件格式：PDF、docx、doc，最多5份；图片格式：jpg、jpeg、png，最多5张;</p>
          </el-form-item>
          <el-form-item label="流程信息预览">
            <button-hoc type="primary" v-if="isSYZG" @click="previewFlow">预览流程</button-hoc>
            <button-hoc type="primary" v-else-if="isBuildSelf" @click="previewBuildSelfFlow">预览流程</button-hoc>
          </el-form-item>
        </el-form>
      </div>
      <!--建档表单信息-->
      <div v-if="tabActive === 'creationFormInfo'">
        <div class="m-b-16" v-if="flowChannelList.length">
          <span class="m-r-12 font-12">查看的渠道</span>
          <el-select v-model="flowChannel" placeholder="请选择渠道" style="width: 450px" @change="getBuildSelfFlowForm($event, 'view')">
            <el-option v-for="item in flowChannelList" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
        </div>
        <el-descriptions :column="2" border v-if="previewInfo" :labelStyle="{ width: '14%' }" :contentStyle="{ width: '36%' }">
          <el-descriptions-item label="申请人">{{ previewInfo.applyByName | emptyContent }}</el-descriptions-item>
          <el-descriptions-item label="申请部门">{{ previewInfo.applyUserDeptName | emptyContent }}</el-descriptions-item>
          <el-descriptions-item label="渠道全称">{{ previewInfo.channelFullName | emptyContent }}</el-descriptions-item>
          <el-descriptions-item label="客户简称">{{ previewInfo.companySimple | emptyContent }}</el-descriptions-item>
          <el-descriptions-item label="分销商ID">{{ previewInfo.distributorId | emptyContent }}</el-descriptions-item>
          <el-descriptions-item label="分销商名称">{{ previewInfo.shopName | emptyContent }}</el-descriptions-item>
          <el-descriptions-item label="销售模式">{{ previewInfo.saleModeName | emptyContent }}</el-descriptions-item>
          <el-descriptions-item label="客户公司">{{ previewInfo.company | emptyContent }}</el-descriptions-item>
          <el-descriptions-item label="纳税人识别号">{{ previewInfo.taxNo | emptyContent }}</el-descriptions-item>
          <el-descriptions-item label="销售公司名称">{{ previewInfo.saleCompany | emptyContent }}</el-descriptions-item>
          <el-descriptions-item label="付款条件">
            {{ previewInfo.payConditionName | emptyContent }}
            <p v-if="previewInfo.payCondition === 'ZD99'">
              <span>付款条件天数：</span>
              <span>{{ previewInfo.payConditionDay }}</span>
            </p>
          </el-descriptions-item>
          <el-descriptions-item label="预计开店时间">{{ previewInfo.openStoreDate | parseTime('{y}-{m}-{d}') }}</el-descriptions-item>
          <el-descriptions-item label="客户类型">{{ previewInfo.customerTypeName | emptyContent }}</el-descriptions-item>
          <el-descriptions-item label="销售币别">{{ previewInfo.currencyName | emptyContent }} | {{ previewInfo.currency }}</el-descriptions-item>
          <el-descriptions-item label="客户分类">{{ previewInfo.customerCategoryName | emptyContent }}</el-descriptions-item>
          <el-descriptions-item label="所属事业部">{{ previewInfo.deptName | emptyContent }}</el-descriptions-item>
          <el-descriptions-item label="销售平台">{{ previewInfo.salePlatform | emptyContent }}</el-descriptions-item>
          <el-descriptions-item label="贸易方式">{{ previewInfo.tradeTypeName | emptyContent }}</el-descriptions-item>
          <el-descriptions-item label="发货人">{{ previewInfo.deliveryName | emptyContent }}</el-descriptions-item>
          <el-descriptions-item label="发货人手机号">{{ previewInfo.deliveryMobile | emptyContent }}</el-descriptions-item>
          <el-descriptions-item label="发货模式">{{ previewInfo.deliveryModeName | emptyContent }}</el-descriptions-item>
          <el-descriptions-item label="发货时效要求">{{ previewInfo.deliveryRequired | emptyContent }}</el-descriptions-item>
          <el-descriptions-item label="合单策略">{{ previewInfo.combineOrderName | emptyContent }}</el-descriptions-item>
          <el-descriptions-item label="发货地址">{{ previewInfo.deliveryAddress | emptyContent }}</el-descriptions-item>
          <el-descriptions-item label="库龄要求">{{ previewInfo.inventoryAgeRequiredName | emptyContent }}</el-descriptions-item>
          <el-descriptions-item label="发货仓库名称">{{ previewInfo.deliveryWarehouse | emptyContent }}</el-descriptions-item>
          <el-descriptions-item label="揽收时效要求">{{ previewInfo.receivedTimeRequiredName | emptyContent }}</el-descriptions-item>
          <el-descriptions-item label="配额管理方式">{{ previewInfo.quoteManageName | emptyContent }}</el-descriptions-item>
          <el-descriptions-item label="管理配额渠道">{{ previewInfo.quoteChannelManageName | emptyContent }}</el-descriptions-item>
          <el-descriptions-item label="对应成本中心">{{ previewInfo.costCenter | emptyContent }}</el-descriptions-item>
          <el-descriptions-item label="客户合作合同或协议" :span="2">
            <div v-for="(item, index) in previewInfo.contractAttachmentFiles" :key="index">{{ item.name }}</div>
          </el-descriptions-item>
          <el-descriptions-item label="流程备注">
            <span v-if="isSYZG">{{ previewInfo.remark | emptyContent }}</span>
            <span v-else-if="isBuildSelf">{{ previewInfo.flowRemarks | emptyContent }}</span>
            <span v-else>-</span>
          </el-descriptions-item>
          <el-descriptions-item label="流程备注附件">
            <template v-if="previewInfo.remarkAttachmentFiles && previewInfo.remarkAttachmentFiles.length">
              <div v-for="(item, index) in previewInfo.remarkAttachmentFiles" :key="index">{{ item.name }}</div>
            </template>
            <span v-else>-</span>
          </el-descriptions-item>
        </el-descriptions>
        <!--建档预览信息为空-->
        <div class="section-card" v-else-if="!flowChannelList.length">
          <span class="font-12 m-r-24">请先完成建档信息的填写，然后才能进行查看</span>
        </div>
      </div>
      <!--操作记录-->
      <div v-if="tabActive === 'operationRecord'">
        <sy-normal-table v-bind="operationZgRecord" ref="operationZgRecord" v-if="isSYZG" />
        <sy-normal-table v-bind="operationBuildSelfRecord" ref="operationBuildSelfRecord" v-if="isBuildSelf" />
      </div>
    </div>
    <div class="drawer-footer">
      <button-hoc v-if="hasSubmitBtn" type="primary" @click="submit" :loading="btnLoading">确认发起流程</button-hoc>
      <button-hoc v-if="isBuildSelf && hasSubmitBtn" type="primary" plain @click="previewBuildSelfFlow" :loading="btnLoading">暂存</button-hoc>
      <button-hoc v-if="hasReturnBtn" type="primary" plain @click="backBasicInfo">返回</button-hoc>
    </div>
  </el-drawer>
</template>

<script>
import {
  distributorContractInfoAutoBindChannelInfoBySyzg,
  distributorContractInfoGetBindChannelInfoBySyzg,
  distributorContractInfoGetMainCustomerSimpleName,
  distributorContractInfoListValidContractByInfoIds,
  distributorPartyRelationEnabled,
  distributorPartyRelationFlowGetFlowForm,
  distributorPartyRelationFlowGetPartyRelationFlowInfo,
  distributorPartyRelationFlowListFinishFlowPage,
  distributorPartyRelationFlowListFlowChannel,
  distributorPartyRelationFlowListFlowLogPage,
  distributorPartyRelationFlowListPartyRelationFlowLog,
  distributorPartyRelationFlowListUnFinishFlowPage,
  distributorPartyRelationFlowSaveSelfFlow,
  distributorPartyRelationListMainCostCenter,
  distributorPartyRelationListRelationPage,
  distributorPartyRelationStopped,
  updateChannelInfo
} from '@/api/distributorManagement/enterprise-subject';
import { parseTime, sleep } from '@/utils';
import { brandBriefListAll } from '@/api/brand/list';
import { cloneDeep, isEmpty } from 'lodash';
import { validateInteger } from '@/utils/validate';
const initForm = {
  contractIds: [],
  companySimple: '',
  saleMode: '', // 销售模式
  currency: '', // 销售币种
  settlementType: '', // 结算方式
  payCondition: '',
  payConditionDay: '',
  channelDept: '',
  dept: '',
  costCenterCode: '',
  brandType: 'MULTI_BRAND',
  brandId: '',
  flowRemarks: '',
  attachmentFileList: [], // 建档水羊直供流程附件
  remarkAttachmentFiles: [] // 建档自建流程附件
};
export default {
  name: 'ImproveMainDataInformation',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    currentRow: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  data() {
    return {
      isLoading: true,
      btnLoading: false,
      costCenterList: [],
      brandList: [],
      distributorContractInfoList: [],
      logList: [], // 直供操作记录
      isCreatePreview: false, // 是否创建预览
      previewInfo: null, // 预览流程信息
      formEdit: false, // 是否处于表单编辑状态
      isContinueCreate: false, // 是否继续建档
      currentFlowId: '', // 当前查看的建档流程id
      form: cloneDeep(initForm),
      rules: {
        contractIds: [{ required: true, message: '请选择建档所需合同', trigger: 'change' }],
        companySimple: [{ required: true, message: '请输入客户简称', trigger: 'blur' }],
        saleMode: [{ required: true, message: '请选择销售模式', trigger: 'change' }],
        currency: [{ required: true, message: '请选择销售币种', trigger: 'change' }],
        settlementType: [{ required: true, message: '请选择结算方式', trigger: 'change' }],
        payCondition: [{ required: true, message: '请选择结算周期', trigger: 'change' }],
        payConditionDay: [
          { required: true, message: '请输入结算天数', trigger: 'blur' },
          { validator: validateInteger, trigger: 'blur' }
        ],
        channelDept: [{ required: true, message: '请选择归属部门', trigger: 'change' }],
        dept: [{ required: true, message: '请选择所属事业部', trigger: 'change' }],
        costCenterCode: [{ required: true, message: '请选择成本中心', trigger: 'change' }],
        brandType: [{ required: true, message: '请选择', trigger: 'change' }],
        brandId: [{ required: true, message: '请选择渠道合作品牌', trigger: 'change' }]
      },
      tabActive: 'basicInfo',
      bindChannelInfo: null, // 水羊直供渠道信息
      doneChannelList: [], // 已完成建档的渠道列表
      applyingChannelList: [], // 建档申请中渠道列表
      relationChannelList: [], // 已关联建档渠道列表
      relationChannelTotal: 0, // 已关联建档渠道列表总数
      flowChannel: '', // 建档表单信息-查看渠道
      flowChannelList: [], // 建档表单信息-查看渠道下拉列表
      mainCustomerSimpleInfo: {} // 主数据客户简称信息
    };
  },
  computed: {
    // 主体id
    id() {
      return this.currentRow?.id ?? '';
    },
    saleModeOptions() {
      return this.$dict['soyoungzg_oa_sale_mode'].filter((item) => item.value !== '1');
    },
    settlementTypeOptions() {
      return this.$dict['soyoungzg_distributor_type'];
    },
    settlementPeriodOptions() {
      return this.$dict['soyoungzg_oa_pay_condition'];
    },
    belongDepartmentOptions() {
      return this.$dict['external_channel_config'];
    },
    deptOptions() {
      return this.$dict['soyoungzg_oa_new_dept'];
    },
    currencyOptions() {
      return this.$dict['soyoungzg_oa_currency'];
    },
    basicBrandSelectBind() {
      return {
        flashOptions: true,
        filterable: true,
        multiple: false,
        placeholder: '请选择',
        options: this.brandList,
        optionsProps: {
          label: 'name',
          value: 'id'
        }
      };
    },
    attachmentBind() {
      return {
        type: 'file',
        accept: ['png', 'jpg', 'jpeg', 'pdf', 'doc', 'docx'],
        bind: {
          limit: 5
        },
        limit: {
          type: ['png', 'jpg', 'jpeg', 'pdf', 'doc', 'docx']
        }
      };
    },
    contractIdsSelectBind() {
      return {
        filterable: true,
        placeholder: '请选择有效的合同，可多选',
        flashOptions: true,
        multiple: true,
        collapseTags: true,
        options: this.distributorContractInfoList
      };
    },
    platformOwnerList() {
      return window.$vue.$dict['distributor_contract_platform_owner_type'];
    },
    // 是否有底部提交按钮
    hasSubmitBtn() {
      if (this.isSYZG) return this.tabActive === 'basicInfo' && this.isFormEdit && !this.isDisabledDistributor;
      if (this.isBuildSelf) return this.tabActive === 'basicInfo' && this.isFormEdit && this.hasCreatePermission && !this.isDisabledDistributor;
      return false;
    },
    // 是否有底部返回按钮
    hasReturnBtn() {
      return this.isBuildSelf && this.isCreatePreview && this.tabActive !== 'basicInfo';
    },
    // 是否水羊直供
    isSYZG() {
      return this.currentRow?.platformOwner === 'SYZG_CHANNEL';
    },
    // 是否为自建
    isBuildSelf() {
      return this.currentRow?.platformOwner === 'BUILD_SELF_CHANNEL';
    },
    // 是否可以关联渠道
    isCanBindChannel() {
      const { izBindChannel = '', omsChannelId = '', sapChannelCode = '' } = this.bindChannelInfo || {};
      return izBindChannel === '0' && !isEmpty(omsChannelId) && !isEmpty(sapChannelCode);
    },
    // 已禁用或已注销的分销商
    isDisabledDistributor() {
      const { distributorStatus = '' } = this.currentRow || {};
      return distributorStatus === 'DISABLE' || distributorStatus === 'LOGOUT_PASS';
    },
    // 是否是自建且未拥有新增权限
    hasCreatePermission() {
      return this.$utils.hasPermission('distributor-management-edit-improve-Info-relation-type-create-build-self', true);
    },
    // 是否处于表单状态
    isFormEdit() {
      const { doneChannelList = [], applyingChannelList = [], relationChannelList = [], bindChannelInfo = {} } = this;
      return this.formEdit || (doneChannelList.length === 0 && applyingChannelList.length === 0 && relationChannelList.length === 0 && isEmpty(bindChannelInfo));
    },
    // 直供操作记录
    operationZgRecord() {
      const that = this;
      return {
        pagination: false,
        columns() {
          return [
            {
              prop: 'createDate',
              type: 'time',
              label: '时间',
              format: 'yyyy-MM-dd hh:mm:ss',
              width: 200
            },
            {
              prop: 'createByName',
              label: '操作人',
              width: 200
            },
            {
              prop: 'flowNodeName',
              label: '节点',
              render: (h, { row }) => (
                <span>
                  {row.flowNodeName} {(row.flowNode === 'REJECT' || row.flowNode === 'FINISH') && row.remark && `( ${row.remark} )`}
                </span>
              )
            },
            {
              label: '操作',
              type: 'btns',
              width: 140,
              btns({ row }) {
                return [
                  {
                    hide: !(row.flowNode === 'CREATE' || row.flowNode === 'RESUBMIT'),
                    text: '查看流程',
                    type: 'text',
                    call: () => that.jumpOaSystem(row)
                  }
                ];
              }
            }
          ];
        },
        async search() {
          const { id = '' } = that.currentRow;
          const { data = {} } = await distributorPartyRelationFlowListPartyRelationFlowLog(id);
          const { logVOList = [], total = 0 } = data;
          return {
            list: logVOList,
            total
          };
        }
      };
    },
    // 自建操作记录
    operationBuildSelfRecord() {
      const that = this;
      return {
        columns() {
          return [
            {
              prop: 'createDate',
              type: 'time',
              label: '时间',
              format: 'yyyy-MM-dd hh:mm:ss',
              width: 200
            },
            {
              prop: 'createByName',
              label: '操作人',
              width: 140,
              multiLine: 2
            },
            {
              prop: 'channelFullName',
              label: '流程渠道全称',
              multiLine: 3,
              itemBind: {
                minWidth: 140
              }
            },
            {
              prop: 'flowNodeName',
              label: '节点',
              itemBind: {
                minWidth: 140
              },
              render: (h, { row }) => (
                <span>
                  {row.flowNodeName} {(row.flowNode === 'REJECT' || row.flowNode === 'FINISH') && row.remark && `( ${row.remark} )`}
                </span>
              )
            },
            {
              label: '操作',
              type: 'btns',
              width: 100,
              btns({ row }) {
                return [
                  {
                    text: '查看流程',
                    type: 'text',
                    call: () => that.jumpOaSystem(row)
                  }
                ];
              }
            }
          ];
        },
        async search({ pageFilter }) {
          const { distributorCooperationId = '' } = that.currentRow;
          const params = {
            data: { distributorCooperationId },
            ...pageFilter
          };
          const { data = {} } = await distributorPartyRelationFlowListFlowLogPage(params);
          const { list = [], total = 0 } = data;
          return {
            list,
            total
          };
        }
      };
    },
    // 已完成建档表格
    doneChannelTable() {
      const that = this;
      return {
        tableBind: {
          maxHeight: 288,
          border: true
        },
        columns() {
          return [
            {
              label: '建档完成时间',
              prop: 'finishDate',
              type: 'time',
              format: 'yyyy-MM-dd hh:mm:ss',
              width: 140
            },
            {
              label: '建档SAP渠道编码',
              prop: 'sapChannelCode',
              width: 130,
              multiLine: 2
            },
            {
              label: 'SAP渠道名称',
              prop: 'sapChannelName',
              multiLine: 3
            },
            {
              label: '销售模式',
              prop: 'saleModeName',
              width: 120
            },
            {
              label: 'OMS渠道信息',
              render: (h, { row }) => (
                <div v-frag>
                  <div>ID：{row.omsChannelId || '-'}</div>
                  <div>名称：{row.omsChannelName || '-'}</div>
                </div>
              )
            },
            {
              label: '发起人',
              prop: 'createByName'
            },
            {
              label: '关联状态',
              prop: 'relationStatus',
              type: 'map',
              map: [
                { label: '启用', value: 'ENABLED' },
                { label: '禁用', value: 'STOPPED' }
              ]
            },
            {
              label: '操作',
              type: 'btns',
              btns({ row }) {
                return [
                  {
                    hide: row.relationStatus === 'ENABLED',
                    text: '启用',
                    type: 'text',
                    confirm: '确定启用吗？',
                    call: () => that.handlerDistributorPartyRelation(row)
                  },
                  {
                    hide: row.relationStatus === 'STOPPED',
                    text: '禁用',
                    type: 'text',
                    confirm: '确定禁用吗？',
                    call: () => that.handlerDistributorPartyRelation(row)
                  }
                ];
              }
            }
          ];
        },
        async search({ filtersValue, pageFilter }) {
          const { distributorCooperationId = '', platformOwner } = that.currentRow;
          const params = {
            data: {
              ...filtersValue,
              distributorCooperationId,
              platformOwner
            },
            ...pageFilter
          };
          const { data = {} } = await distributorPartyRelationFlowListFinishFlowPage(params);
          const { list = [], total = 0 } = data;
          that.doneChannelList = list;
          return {
            list,
            total
          };
        }
      };
    },
    // 建档申请中表格
    applyingChannelTable() {
      const that = this;
      return {
        tableBind: {
          maxHeight: 288,
          border: true
        },
        columns() {
          return [
            {
              label: '创建时间',
              prop: 'createDate',
              type: 'time',
              format: 'yyyy-MM-dd hh:mm:ss',
              width: 140
            },
            {
              label: '建档渠道名称',
              prop: 'channelFullName',
              itemBind: {
                minWidth: 140
              },
              multiLine: 3
            },
            {
              label: '销售模式',
              prop: 'saleModeName',
              width: 120
            },
            {
              label: '结算方式',
              prop: 'settlementTypeName',
              width: 100
            },
            {
              label: '创建人',
              prop: 'createByName',
              width: 100
            },
            {
              label: '建档状态',
              prop: 'statusName',
              itemBind: {
                minWidth: 100
              },
              multiLine: 3
            },
            {
              label: '操作',
              type: 'btns',
              width: 140,
              btns({ row }) {
                return [
                  {
                    hide: !row.oaUrl,
                    text: row.status === 'REJECT' ? '查看原流程' : '查看流程',
                    type: 'text',
                    call: () => that.jumpOaSystem(row)
                  },
                  {
                    hide: !(row.status === 'REJECT'),
                    text: '修改OA建档流程',
                    type: 'text',
                    call() {
                      const { id = '' } = row;
                      if (id) {
                        that.getBuildSelfFlowForm(id, 'edit');
                      }
                    }
                  },
                  {
                    hide: !(row.status === 'TEMPORARY'),
                    text: '提交OA建档流程',
                    type: 'text',
                    call() {
                      const { id = '' } = row;
                      if (id) {
                        that.getBuildSelfFlowForm(id, 'edit');
                      }
                    }
                  }
                ];
              }
            }
          ];
        },
        async search({ filtersValue, pageFilter }) {
          const { distributorCooperationId = '', platformOwner } = that.currentRow;
          const params = {
            data: {
              ...filtersValue,
              distributorCooperationId,
              platformOwner
            },
            ...pageFilter
          };
          const { data = {} } = await distributorPartyRelationFlowListUnFinishFlowPage(params);
          const { list = [], total = 0 } = data;
          that.applyingChannelList = list;
          return {
            list,
            total
          };
        }
      };
    },
    relationChannelTable() {
      const that = this;
      return {
        tableBind: {
          maxHeight: 403,
          border: true
        },
        columns() {
          return [
            {
              label: '建档SAP渠道编码',
              prop: 'sapChannelCode',
              width: 130,
              multiLine: 2
            },
            {
              label: 'SAP渠道名称',
              prop: 'sapChannelName',
              multiLine: 2
            },
            {
              label: '销售模式',
              prop: 'saleModeName',
              width: 120
            },
            {
              label: 'OMS渠道信息',
              render: (h, { row }) => (
                <div v-frag>
                  <div>ID：{row.omsChannelId || '-'}</div>
                  <div>名称：{row.omsChannelName || '-'}</div>
                </div>
              )
            },
            {
              label: '发起人',
              prop: 'createByName',
              width: 90
            },
            {
              hide: that.isSYZG,
              label: '是否被引用',
              prop: 'izRelation',
              width: 90,
              type: 'map',
              map: [
                { label: '否', value: '0' },
                { label: '是', value: '1' }
              ]
            },
            {
              hide: that.isBuildSelf,
              label: '关联状态',
              prop: 'relationStatus',
              width: 90,
              type: 'map',
              map: [
                { label: '启用', value: 'ENABLED' },
                { label: '禁用', value: 'STOPPED' }
              ]
            },
            {
              hide: that.isSYZG,
              label: '引用合同数',
              type: 'btns',
              width: 90,
              btns({ row }) {
                return [
                  {
                    text: `${row.contractNum}份`,
                    type: 'text',
                    call: () => that.jumpContactBySap(row)
                  }
                ];
              }
            },
            {
              hide: that.isBuildSelf,
              label: '操作',
              type: 'btns',
              width: 90,
              btns({ row }) {
                return [
                  {
                    hide: row.relationStatus === 'ENABLED',
                    text: '启用',
                    type: 'text',
                    confirm: '确定启用吗？',
                    call: () => that.handlerDistributorPartyRelation(row)
                  },
                  {
                    hide: row.relationStatus === 'STOPPED',
                    text: '禁用',
                    type: 'text',
                    confirm: '确定禁用吗？',
                    call: () => that.handlerDistributorPartyRelation(row)
                  }
                ];
              }
            }
          ];
        },
        async search({ filtersValue, pageFilter }) {
          const { distributorCooperationId = '', platformOwner } = that.currentRow;
          const params = {
            data: {
              ...filtersValue,
              distributorCooperationId,
              platformOwner
            },
            ...pageFilter
          };
          const { data = {} } = await distributorPartyRelationListRelationPage(params);
          const { list = [], total = 0 } = data;
          that.relationChannelList = list;
          that.relationChannelTotal = total;
          return {
            list,
            total
          };
        }
      };
    }
  },
  created() {
    this.init();
  },
  methods: {
    isEmpty,
    init() {
      this.getBindChannelInfo();
      this.getFlowChannelList();
      this.getBrandList();
      this.getCostCenter();
      this.getContractList();
      this.getMainCustomerSimpleInfo();
      sleep(1000).then(() => {
        this.isLoading = false;
      });
    },
    // 继续建档
    continueCreate() {
      this.form = cloneDeep(initForm);
      this.currentFlowId = '';
      this.setCompanySimple();
      this.formEdit = true;
      this.isContinueCreate = true;
    },
    // 返回查看
    backView() {
      this.formEdit = false;
      this.isContinueCreate = false;
    },
    // 返回基础信息
    backBasicInfo() {
      this.tabActive = 'basicInfo';
    },
    // 自建渠道-建档表单信息-渠道下拉列表
    getFlowChannelList() {
      const { distributorCooperationId = '' } = this.currentRow;
      if (!distributorCooperationId) return;
      const params = { distributorCooperationId };
      distributorPartyRelationFlowListFlowChannel(params).then(({ data = [] }) => {
        this.flowChannelList = data.map((item) => ({
          label: item.channelFullName,
          value: item.id
        }));
      });
    },
    // 获取主数据客户简称信息
    getMainCustomerSimpleInfo() {
      const { distributorContractInfoId = '' } = this.currentRow;
      if (!distributorContractInfoId) return;
      distributorContractInfoGetMainCustomerSimpleName({ distributorContractInfoId }).then(({ data = {} }) => {
        this.mainCustomerSimpleInfo = data;
        this.setCompanySimple();
      });
    },
    setCompanySimple() {
      const { contractInfoSimpleName = '' } = this.mainCustomerSimpleInfo;
      if (contractInfoSimpleName) {
        this.form.companySimple = contractInfoSimpleName;
      }
    },
    // 获取水羊直供渠道信息
    getBindChannelInfo() {
      const { distributorId = '', distributorCooperationId = '' } = this.currentRow;
      if (!this.isSYZG || !distributorCooperationId) return;
      const params = { distributorId, distributorCooperationId };
      distributorContractInfoGetBindChannelInfoBySyzg(params).then(({ data }) => {
        this.bindChannelInfo = data;
      });
    },
    // 水羊直供一键关联渠道
    autoBindChannelInfo() {
      const { id = '' } = this.currentRow || {};
      const { omsChannelId = '', sapChannelCode = '' } = this.bindChannelInfo || {};
      const params = { id, omsChannelId, sapChannelCode };
      distributorContractInfoAutoBindChannelInfoBySyzg(params).then((res) => {
        const { code = '', msg = '' } = res;
        if (code !== '0') {
          return this.$message.error(msg || '渠道关联失败');
        }
        this.$message.success('渠道关联成功');
        this.getBindChannelInfo();
        this.relationChannelTableSearch();
      });
    },
    // 渠道归属部门变更
    belongDepartmentChange(val) {
      // 所属事业部根据val进行自动匹配：1、伊菲丹=伊菲丹品牌事业部 2、渠道事业部线上+线下=水羊国际（非御强）事业部；3、CP集团=水羊国际御强事业部;4、御泥坊线下=御泥坊品牌事业部;5、其他品牌=水羊国际（非御强）事业部
      const deptMap = {
        EVIDANS_OFFLINE: '100006',
        CP_OFFLINE: '100004',
        YNF_OFFLINE: '100000',
        CHANNEL_DEPT_OFFLINE: '100002',
        CHANNEL_DEPT_ONLINE: '100002'
      };
      this.form.dept = deptMap[val] || '100002';
      // 所属渠道合作品牌根据val进行自动匹配：1、伊菲丹线下默认选择单品牌，品牌为伊菲丹；2、渠道事业部线上+线下=多品牌；3、CP集团=单品牌，品牌为空；3、御泥坊线下=单品牌，品牌为御泥坊
      // 伊菲丹品牌id：测试环境-4886694099912960,正式环境-2736112504577536
      // 御泥坊品牌id：测试环境-2331179049098752,正式环境-2331179049098752
      const isProd = process.env.NODE_ENV === 'production';
      const brandMap = {
        EVIDANS_OFFLINE: { brandType: 'SINGLE_BRAND', brandId: isProd ? '2736112504577536' : '4886694099912960' },
        CP_OFFLINE: { brandType: 'SINGLE_BRAND', brandId: '' },
        YNF_OFFLINE: { brandType: 'SINGLE_BRAND', brandId: '2331179049098752' },
        CHANNEL_DEPT_OFFLINE: { brandType: 'MULTI_BRAND', brandId: '' },
        CHANNEL_DEPT_ONLINE: { brandType: 'MULTI_BRAND', brandId: '' },
        OTHER: { brandType: 'SINGLE_BRAND', brandId: '' }
      };
      const { brandType = 'MULTI_BRAND', brandId = '' } = brandMap[val] || {};
      this.form.brandType = brandType;
      this.form.brandId = brandId;
    },
    // 获取品牌列表
    getBrandList() {
      brandBriefListAll().then(({ data = [] }) => {
        this.brandList = data;
      });
    },
    // 获取成本中心数据
    getCostCenter() {
      distributorPartyRelationListMainCostCenter().then(({ data = [] }) => {
        this.costCenterList = data;
      });
    },
    // 关闭
    close() {
      this.$emit('update:show', false);
      this.$emit('close');
    },
    //  获取建档所需合同
    getContractList() {
      const params = {
        distributorId: this.currentRow.distributorId,
        distributorPartyRelationId: this.currentRow.id
      };
      distributorContractInfoListValidContractByInfoIds(params).then(({ data = [] }) => {
        this.distributorContractInfoList = data.map((item) => {
          return {
            label: `${item.contractName}-${parseTime(item.startDate, '{y}-{m}-{d}')}至${parseTime(item.endDate, '{y}-{m}-{d}')}`,
            value: item.id
          };
        });
      });
    },
    // 预览直供流程信息
    previewFlow() {
      const { contractIds = [], flowRemarks = '', attachmentFileList = [], companySimple } = this.form;
      if (contractIds.length === 0) {
        this.$message.error('请选择建档所需合同');
        return;
      }
      const params = {
        companySimple,
        contractIds,
        remark: flowRemarks,
        attachmentFileList: attachmentFileList.map(({ name, url }) => ({ name, url })),
        partyRelationId: this.currentRow.id
      };
      distributorPartyRelationFlowGetPartyRelationFlowInfo(params).then(({ data }) => {
        this.previewInfo = data;
        this.isCreatePreview = true;
        this.tabActive = 'creationFormInfo';
      });
    },
    // 预览自建流程信息
    previewBuildSelfFlow() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          const params = this.getBuildSelfFlowParams('TEMPORARY');
          this.saveBuildSelfFlow(params, false);
        }
      });
    },
    // 预览自建流程信息接口请求
    saveBuildSelfFlow(params, isSubmit) {
      this.btnLoading = true;
      distributorPartyRelationFlowSaveSelfFlow(params)
        .then(({ data = {}, code }) => {
          if (code === '0') {
            // 提交
            if (isSubmit) {
              this.$message.success('操作成功');
              this.close();
              return;
            }
            // 预览
            const { id } = data;
            this.getFlowChannelList();
            this.getBuildSelfFlowForm(id, 'create');
          }
        })
        .catch((err) => {
          const msg = err?.data?.msg || '系统错误';
          const code = err?.data?.code || '';
          if (code === '707') {
            this.$confirm(msg, '提示', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning',
              showClose: false,
              closeOnPressEscape: false,
              closeOnClickModal: false
            })
              .then(() => {
                // 提交
                if (isSubmit) {
                  this.submitBuildSelfFlow(true);
                  return;
                }
                // 预览
                const data = {
                  ...this.getBuildSelfFlowParams('TEMPORARY'),
                  validMainChannel: '0'
                };
                this.saveBuildSelfFlow(data);
              })
              .catch(() => {
                this.$message.info('操作已取消');
              });
          } else {
            this.$message.error(msg);
          }
        })
        .finally(() => {
          this.btnLoading = false;
        });
    },
    // 获取自建提交流程参数
    getBuildSelfFlowParams(status) {
      const { distributorCooperationId = '' } = this.currentRow;
      const form = cloneDeep(this.form);
      const { remarkAttachmentFiles = [], costCenterCode = '' } = form;
      // 如果是多品牌，删除brandId
      if (form.brandType === 'MULTI_BRAND') {
        delete form.brandId;
      }
      const costCenter = this.costCenterList.find((item) => item.code === costCenterCode)?.name ?? '';
      const id = this.currentFlowId || '';
      return {
        ...form,
        id,
        costCenter,
        relationType: 'NEW',
        remarkAttachmentFiles: remarkAttachmentFiles.map(({ name, url }) => ({ name, url })),
        distributorCooperationId,
        status
      };
    },
    // 获取自建预览表单字段/建档表单信息, type：创建、预览、编辑
    getBuildSelfFlowForm(id, type = '') {
      const { distributorCooperationId = '' } = this.currentRow;
      if (!id || !distributorCooperationId) return;
      distributorPartyRelationFlowGetFlowForm({ distributorCooperationId, id }).then(({ data }) => {
        // 流程id
        const flowId = data.id || '';
        this.currentFlowId = flowId;
        this.flowChannel = flowId;
        // 预览表单信息
        if (type === 'create') {
          this.isCreatePreview = true;
          this.previewInfo = data;
          this.tabActive = 'creationFormInfo';
        }
        if (type === 'view') {
          this.previewInfo = data;
        }
        if (type === 'edit') {
          const keys = Object.keys(this.form);
          keys.forEach((key) => {
            if (data[key] !== undefined) {
              this.form[key] = data[key];
            }
          });
          this.formEdit = true;
          this.isContinueCreate = true;
        }
      });
    },
    // 已完成建档的渠道信息启用禁用
    handlerDistributorPartyRelation(row) {
      const { partyRelationId = '', relationStatus } = row;
      const params = { partyRelationId, status: relationStatus };
      const requestFun = relationStatus === 'STOPPED' ? distributorPartyRelationEnabled : distributorPartyRelationStopped;
      requestFun(params).then((res) => {
        const { code = '' } = res;
        if (code === '0') {
          this.$message.success('操作成功');
          this.doneChannelTableSearch();
          this.relationChannelTableSearch();
        }
      });
    },
    // doneChannelTable查询
    doneChannelTableSearch() {
      const ref = this.$refs.doneChannelTableRef;
      ref && ref.handlerSearch();
    },
    // relationChannelTable查询
    relationChannelTableSearch() {
      const ref = this.$refs.relationChannelTableRef;
      ref && ref.handlerSearch();
    },
    // 发起OA流程
    submit() {
      this.$refs['form'].validate((valid) => {
        if (valid) {
          this.isSYZG ? this.submitFlow() : this.submitBuildSelfFlow();
        }
      });
    },
    // 直供发起OA流程
    submitFlow() {
      const form = cloneDeep(this.form);
      const { attachmentFileList = [] } = form;
      const relationFlowCmd = { attachmentFileList: attachmentFileList.map(({ name, url }) => ({ name, url })) };
      delete form.attachmentFileList;
      const keys = ['flowRemarks', 'contractIds', 'companySimple'];
      keys.forEach((key) => {
        relationFlowCmd[key] = form[key];
        delete form[key];
      });
      const { id = '', platformOwner } = this.currentRow;
      const costCenter = this.costCenterList.find((item) => item.code === form.costCenterCode)?.name ?? '';
      const params = { ...form, costCenter, id, platformOwner, relationType: 'NEW' };
      params.relationFlowCmd = relationFlowCmd;
      this.btnLoading = true;
      updateChannelInfo(params)
        .then((res) => {
          if (res.code === '0') {
            this.$message.success('操作成功');
            this.close();
          } else {
            this.$message.error(res.msg ?? '操作失败');
          }
        })
        .finally(() => {
          this.btnLoading = false;
        });
    },
    // 自建发起OA流程
    submitBuildSelfFlow(notValidMainChannel = false) {
      const params = this.getBuildSelfFlowParams('CREATE');
      if (notValidMainChannel) {
        params.validMainChannel = '0';
      }
      this.saveBuildSelfFlow(params, true);
    },
    // 跳转oa系统
    jumpOaSystem(row) {
      const { oaUrl = '' } = row;
      oaUrl && window.open(oaUrl, '_blank');
    },
    // 跳转到合同列表带上sap
    jumpContactBySap(row) {
      const { sapChannelCode = '' } = row;
      if (!sapChannelCode) return;
      this.jumpContact(`sapChannelCode=${sapChannelCode}`);
    },
    // 跳转到飞书合同
    jumpFsContactList() {
      this.jumpContact(`activeTab=UnmatchedContracts`);
    },
    // 跳转合同
    jumpContact(query) {
      window.open(`/soyoung-zg#/distributor-management/statistic?${query}`, '_blank');
    },
    // 跳转品牌
    jumpBrand() {
      window.open('/soyoung-zg#/brand/list', '_blank');
    }
  }
};
</script>
<style lang="scss" scoped>
::v-deep {
  .el-descriptions__body {
    color: var(--color-text-secondary);
  }
  .el-descriptions-item__label.is-bordered-label {
    background: rgba(236, 238, 246, 0.2);
    color: var(--color-info);
  }
  .el-form-item {
    .el-select,
    .el-input {
      width: 240px;
    }
    .sy-select-warp {
      max-width: 100%;
    }
    .sy-select {
      width: 100% !important;
      max-width: 100%;
      .el-input {
        width: 100%;
      }
    }
  }

  .sy-table.el-table,
  .sy-normal-table .el-table {
    margin: 0;
  }
  .el-table .el-table__header th {
    background: var(--background-color-base);
  }
  .common-line-title__text {
    min-width: 130px;
  }
}
.channel-info-row {
  margin-bottom: 18px;
  .el-col:first-child {
    flex: none;
    width: 180px !important;
    text-align: right;
    font-size: 12px;
    color: #0d1b3f;
    font-weight: 700;
    padding-right: 12px !important;
  }
  .el-form-item {
    margin: 0;
  }
}
.custom-table-header {
  background: var(--background-color-base);
  padding: 12px;
  font-size: 12px;
  color: #6e768c;
  text-align: center;
  font-weight: 700;
  border: 1px solid #ebeef5;
  border-bottom: none;
}
.section-card {
  padding: 12px;
  background: var(--background-color-base);
}
</style>
