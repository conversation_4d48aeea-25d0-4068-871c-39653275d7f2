<template>
  <div class="select">
    <div class="select-content">
      <slot name="table"></slot>
    </div>

    <div class="select-foot">
      <span v-if="isMultiple" class="select-foot_info"
        >已选{{ selectData.length }}个</span
      >
      <div class="select-foot_operate">
        <el-button @click="onCancel" size="small">取消</el-button>
        <el-button @click="onCommit" size="small" type="primary">{{
          isMultiple ? '批量添加保存' : '保存'
        }}</el-button>
      </div>
    </div>
  </div>
</template>

<script>
// 选择数据项组件
export default {
  name: 'SelectTableRow',
  data() {
    return {
      beforeMultipleSelection: [], // 存储已经选择的项
      multipleSelection: []
    };
  },
  props: {
    title: {
      type: String,
      default: '选择'
    },
    // 单选传 false， 默认多选
    isMultiple: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    selectData() {
      const data = [...this.beforeMultipleSelection];
      this.multipleSelection.forEach((i) => {
        if (!data.find((x) => x.id === i.id)) {
          data.unshift(i);
        }
      });
      return data;
    }
  },
  methods: {
    handleSelectionChange(rows) {
      if (!this.isMultiple) {
        rows.length && (this.beforeMultipleSelection = []);

        this.$parent.toggleRowSelections(
          rows.splice(0, rows.length - 1),
          false
        );
      }
      this.multipleSelection = rows;
    },
    onCancel() {
      this.$emit('onCancel');
    },
    onCommit() {
      const len = this.selectData.length;
      if (len === 0) {
        this.$message.warning('至少选择一条数据');
        return;
      }

      if (!this.isMultiple && len > 1) {
        this.$message.warning('只能选择一条数据');
        return;
      }

      this.$emit('onCommit', this.selectData);
    }
  }
};
</script>

<style lang='scss' scoped>
.select {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  &-content {
    flex: 1;
    overflow: auto;
  }
  &-foot {
    height: 40px;
    line-height: 40px;
    position: relative;
    &_info {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
    }
    &_operate {
      text-align: center;
    }
  }
}
</style>