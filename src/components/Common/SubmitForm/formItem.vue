<template>
  <el-input
    v-if="type === 'input' && options.elType === 'textarea'"
    v-model="formData[options.key]"
    @change="changeValue"
    v-bind="options"
    :type="options.elType"
  >
    <!-- 复合型输入提示 -->
    <template
      :slot="complexType.slot"
      v-for="complexType of options.complexTypes"
    >
      <template v-if="complexType.type === 'text'">{{
        complexType.value
      }}</template>
    </template>
  </el-input>
  <el-input
    v-else-if="type === 'input'"
    v-model.trim="formData[options.key]"
    @change="changeValue"
    v-bind="options"
    :type="options.elType"
  >
    <!-- 复合型输入提示 -->
    <template
      :slot="complexType.slot"
      v-for="complexType of options.complexTypes"
    >
      <template v-if="complexType.type === 'text'">{{
        complexType.value
      }}</template>
    </template>
  </el-input>
  <el-select
    v-else-if="type === 'select'"
    v-model="formData[options.key]"
    @change="changeValue"
    v-bind="options"
  >
    <el-option
      v-for="y in selectOptions"
      :key="y.value"
      :label="y.label"
      :value="y.value"
    >
    </el-option>
  </el-select>
  <el-date-picker
    v-else-if="type === 'datePicker'"
    v-model="formData[options.key]"
    @change="datePickerChange"
    v-bind="options"
    :default-time="defaultDate"
    :type="options.elType"
  >
  </el-date-picker>
  <span v-else>
    {{ formData[options.key] }}
  </span>
</template>

<script>
/**
 * 表单项
 */
export default {
  name: 'submitFormItem',
  components: {},
  data() {
    return {
      selectOptions: '',
      defaultDate: ['00:00:00', '23:59:59']
    };
  },
  props: {
    formData: {
      type: Object
    },
    options: {
      type: Object
    }
  },
  computed: {
    type() {
      return this.options.type;
    }
  },
  watch: {
    'options.selectOptions': {
      handler(value) {
        if (value instanceof Promise) {
          value.then((res) => {
            this.selectOptions = res;
          });
        } else if (Array.isArray(value)) {
          this.selectOptions = value;
        }
      },
      immediate: true
    }
  },
  methods: {
    datePickerChange(e) {
      const { elType, key } = this.options;
      if (elType === 'daterange' && e === null) {
        e = [];

        // element是先触发change，再赋值，所以使用nextTick延迟赋值为[]
        this.$nextTick(() => {
          this.formData[key] = [];
        });
      }

      this.changeValue(e);
    },
    changeValue(e) {
      this.$emit('onFormItemChange', this.options, e);
    }
  }
};
</script>

<style>
</style>