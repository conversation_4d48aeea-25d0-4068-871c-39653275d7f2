<template>
  <div class="submitForm">
    <el-form size="small" :model="formData" :rules="formRules" ref="form" @keyup.enter.native="$slots.default || onSubmit('form')" class="submitForm——form" :style="{ flexWrap: colNum ? 'wrap' : 'nowrap' }">
      <!-- 表单项 -->
      <template v-for="(i, index) in perfectFormItems">
        <el-form-item
          :label="i.label"
          :label-width="i.labelWidth"
          v-if="i.visible"
          :key="i.key"
          :prop="i.key"
          :style="{
            width: hasWidth(i.width) ? i.width : colNum ? 100 / colNum + '%' : 'auto'
          }"
          v-show="putAwayIndex > 0 ? (visibleSearch ? visibleSearch : index < putAwayIndex) : true"
        >
          <template v-if="i.type === 'custom'">
            <slot :name="i.key" :formData="formData" :options="i"></slot>
          </template>
          <template v-else>
            <FormItem @onFormItemChange="onFormItemChange" :options="i" :formData="formData" :value="formData[i.key]"></FormItem>
          </template>
        </el-form-item>
      </template>

      <!-- 操作栏 -->
      <el-form-item
        class="submitForm——form——operate"
        :style="{
          width: colNum ? 100 / colNum + '%' : 'auto',
          textAlign: colNum > 1 ? 'left' : 'center'
        }"
      >
        <slot>
          <el-button @click="onSubmit" size="small" :loading="loading" type="primary">查询</el-button>
          <el-button @click="onReset" size="small" :loading="loading">重置</el-button>
          <Authority v-if="exportOptions" :auth="exportOptions.auth">
            <el-button :loading="exportLoading" @click="onExport" size="small" type="small">导出</el-button>
          </Authority>
          <el-button v-if="putAwayIndex" :icon="visibleSearch ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" @click="visibleSearch = !visibleSearch" class="toggle-btn" size="small" type="text">{{ visibleSearch ? '收起' : '展开' }}</el-button>
        </slot>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import FormItem from './formItem.vue';
import cloneDeep from 'lodash/cloneDeep';
import { parseTime } from '@/utils';
import download from '@/utils/download';

/**
 * 表单提交公共组件
 */
export default {
  name: 'submitForm',
  components: { FormItem },
  data() {
    return {
      formData: {},
      formRules: {},
      perfectFormItems: [],
      loading: false,
      exportLoading: false,
      visibleSearch: false
    };
  },
  props: {
    formItems: {
      type: Array,
      default() {
        return [];
      }
    },
    // 表单项默认数据
    basicformData: {
      type: Object,
      default() {
        return {};
      }
    },
    // 表单项的label宽度 -> '100px'
    formItemLabelWidth: {
      type: String
    },
    // 收起/展开 时的搜索条件表单项个数
    putAwayIndex: {
      type: Number,
      default: 0
    },
    // 一行显示多少列 -> 1
    colNum: {
      type: Number
    },
    // 导出按钮权限
    exportOptions: {
      type: Object
    }
  },
  created() {
    this.init();
  },
  computed: {},
  watch: {
    /* formItems: {
      handler() {
        this.init();
      },
      immediate: true
    } */
  },
  methods: {
    /**
     * 1.初始化配置数据数据
     * 2.对formData进行初始化
     * 3.初始化默认配置 visible,clearable,filterable
     */
    init() {
      this.perfectFormItems = this.formItems.map(({ key, label, labelWidth, value, visible = true, clearable = true, filterable = true, ...args }) => {
        this.$set(this.formData, key, this.getBasicValue(value, args));

        const options = {
          ...args,
          key,
          label: this.getFormItemLabel(label),
          labelWidth: this.getFormItemLabelWidth(labelWidth),
          visible,
          clearable,
          filterable
        };

        // 当传入了默认值 basicformData时，需要触发
        return options;
      });

      this.formRules = this.perfectFormItems
        .filter(({ rule }) => rule)
        .reduce((p, c) => {
          c.rule = cloneDeep(c.rule);
          p[c.key] = c.rule;
          return p;
        }, {});

      if (this.basicformData) {
        this.$nextTick(() => {
          this.setBasicData(this.basicformData);

          this.$refs.form.clearValidate();
        });
      }
    },

    // 设置默认数据
    setBasicData(basicData = {}) {
      this.perfectFormItems.forEach((options) => {
        const { key } = options;
        let value;

        if ((value = basicData[key])) {
          this.$set(this.formData, key, value);

          this.onFormItemChange(options, value);
        }
      });
    },

    // 表单提交回调
    onSubmit() {
      this.validate((valid) => {
        if (valid) {
          this.$emit('onSubmit', this.formData);
        }
      });
    },
    validate(callback) {
      this.$refs.form.validate(callback);
    },
    onReset() {
      this.$emit('onReset');
      this.clear();
      this.onSubmit();
    },
    clear() {
      // 清除数据
      this.formData = {};
      this.formItems.forEach(({ key, value, ...args }) => {
        this.$set(this.formData, key, this.basicformData[key] || this.getBasicValue(value, args));
      });

      // 清除表单校验效果
      this.$refs.form.resetFields();
    },
    // 导出 - 根据查询出的结果来
    onExport() {
      this.exportLoading = true;
      const q = this.exportOptions.params;
      this.exportOptions
        .request({ ...this.formData, ...q })
        .then((res) => {
          if (res instanceof ArrayBuffer) {
            download(res, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', `${this.exportOptions.name}-${parseTime(Date.now(), '{y}-{m}-{d}-{h}:{i}:{s}')}.xlsx`);
          }
        })
        .finally(() => {
          this.exportLoading = false;
        });
    },
    // 表单项值发生改变
    onFormItemChange(options, value) {
      const { key, observers = [], change = () => {} } = options;
      // 触发关联项发生改变
      observers.forEach((k) => {
        const formItem = this.perfectFormItems.find((x) => x.key === k);
        if (formItem && formItem.watcherRun) {
          formItem.watcherRun.apply(this, [this.formData[key], formItem, key, this.formData]);
        }
      });

      // 触发自身change函数
      change.apply(this, [...arguments, this.formData]);
    },
    getFormItemLabel(label) {
      return label ? label + ': ' : '';
    },
    getBasicValue(value, options) {
      // element的多选值默认必须为数组，不然会触发校验报错
      if (typeof value === 'undefined' && options.multiple) {
        return [];
      }

      if (typeof value === 'object') {
        return cloneDeep(value);
      }

      return value;
    },
    getFormItemLabelWidth(width) {
      return width || this.formItemLabelWidth;
    },
    // 判断宽度参数是否为符合要求
    hasWidth(w) {
      const reg = RegExp(/px|%|auto/);
      return w && reg.test(w);
    }
  }
};
</script>

<style lang="scss" scoped>
.submitForm {
  // margin: 0 -24px;
  &——form {
    display: flex;
    ::v-deep {
      .el-form-item {
        font-size: 14px;
        box-sizing: border-box;
        display: flex;
        margin-bottom: 16px;
        padding: 0 24px;
        .el-form-item__label {
          padding-right: 16px;
          white-space: nowrap;
          display: inline-block;
          line-height: 32px;
        }
        .el-form-item__content {
          flex: 1 1;
          & > div {
            width: 100%;
          }
        }
        .el-select__tags {
          flex-wrap: unset;
          overflow: auto;
        }
      }
    }
  }
}
</style>>