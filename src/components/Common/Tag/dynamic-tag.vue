<template>
  <div class="tags">
    <el-tag
      v-for="(tag, index) in tags"
      :key="index"
      @close="handleClose(tag)"
      v-bind="{
        ...$attrs,
        ...(tag.attrs || {})
      }"
    >
      {{ typeof tag === 'object' ? tag.name : tag }}
    </el-tag>
    <template v-if="isAddTag">
      <el-input
        class="input-new-tag"
        v-if="inputVisible"
        v-model.trim="inputValue"
        ref="saveTagInput"
        size="small"
        @keyup.enter.native="handleInputConfirm"
        @blur="handleInputConfirm"
      >
      </el-input>
      <el-button v-else class="button-new-tag" size="small" @click="addTag"
        >编辑标签</el-button
      >
    </template>
    <slot></slot>
  </div>
</template>

<script>
export default {
  data() {
    return {
      inputVisible: false,
      inputValue: ''
    };
  },
  model: {
    prop: 'tags'
  },
  props: {
    tags: {
      type: Array,
      require: true
    },
    isRepeat: {
      type: Boolean,
      default: true
    },
    isAddTag: Boolean
  },
  methods: {
    handleClose(tag) {
      this.tags.splice(this.tags.indexOf(tag), 1);
      this.$emit('input', this.tags);
    },

    addTag() {
      if (this.$slots.default) {
        this.$emit('addTag');
      } else {
        this.inputVisible = true;
        this.$nextTick((_) => {
          this.$refs.saveTagInput.$refs.input.focus();
        });
      }
    },

    handleInputConfirm() {
      const inputValue = this.inputValue;
      if (inputValue) {
        if (
          this.isRepeat &&
          this.tags.find((i) => {
            const { name = i } = i;
            return inputValue === name;
          })
        ) {
          this.$message.warning('标签已存在');
          return;
        }
        this.tags.push(inputValue);
        this.$emit('input', this.tags);
      }
      this.inputVisible = false;
      this.inputValue = '';
    }
  }
};
</script>

<style lang="scss" scoped>
.tags{
  margin: 0 -4px;
}
.el-tag {
  margin: 0 4px;
}
.button-new-tag {
  margin-left: 10px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}
.input-new-tag {
  width: 90px;
  margin-left: 10px;
  vertical-align: bottom;
}
</style>
