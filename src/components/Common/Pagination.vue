<!-- 分页公共组件 -->
<template>
  <div class="pagination">
    <el-pagination
      :current-page="pageNo"
      :page-size="pageSize"
      :page-sizes="pageSizes"
      :total="total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
      background
      layout="total, sizes, prev, pager, next, jumper"
    ></el-pagination>
  </div>
</template>

<script>
export default {
  name: '',
  data() {
    return {
      pageNo: this.p_pageNo, // 当前页码数
      pageSize: this.p_pageSize, // 每页显示数量
      total: 0 // 数据总数目
    };
  },
  props: {
    // 每页显示数量
    p_pageSize: {
      type: Number,
      default: 10,
    },
    // 当前页码数
    p_pageNo: {
      type: Number,
      default: 1
    },
    // 每页显示数量下拉选择栏
    pageSizes: {
      type: Array,
      default() {
        return [10, 20, 30, 40, 50, 100];
      }
    },
    xhrCallback: {
      type: Function,
      default() {
        return function() {}
      }
    }
  },
  created() {
  },
  methods: {
    // 每页显示数量改变
    handleSizeChange(val) {
      this.pageSize = val;
      this.pageNo = 1;
      this.getResponse();
    },
    // 页码改变
    handleCurrentChange(val) {
      this.pageNo = val;
      this.getResponse();
    },
    getResponse() {
      this.xhrCallback(this.pageNo, this.pageSize).then((total) => {
        this.total = total;
      });
    },
    resetQuery() {
      this.pageNo = this.p_pageNo;
      this.pageSize = this.p_pageSize;
      this.getResponse();
    }
  }
};
</script>

<style>
</style>