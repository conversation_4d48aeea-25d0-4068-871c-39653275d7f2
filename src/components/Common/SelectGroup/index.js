import dict from '@/components/Common/dicts';
import store from '@/store';

const GROUPS = {
  ZG: '1',
  CP: '2',
  GJ: '3',
  GF: '10'
};

// 校验是否属于团队
export const checkGroup = (groupId = store.getters.userGroup, groupName) => {
  for (const [key, value] of Object.entries(GROUPS)) {
    if (value === groupId && (!groupName || groupName === key)) return true;
  }
  return false;
};

// 获取团队标识
export const getGroupLabel = (groupId = store.getters.userGroup) => {
  for (const [key, value] of Object.entries(GROUPS)) {
    if (value === groupId) return key;
  }
  return '';
};
// 弹出框选择团队
export const selectGroup = function (fn, groupId) {
  return async function () {
    // 获取用户团队
    const userGroup = this.$store.state.user.userGroup;
    // 判断用户团队是否属于国际或直供
    groupId = [userGroup, groupId].find(checkGroup);
    if (groupId) {
      fn.apply(this, [groupId]);
      return;
    }

    /* eslint-disable */
    const h = this.$createElement;
    /* eslint-enable */
    // 使用对象做值，与select保存关联
    const groupOptions = await dict('SHOW_DISTRIBUTOR_TEAM_LIST');
    const selectTeam = {};
    const vm = (
      <el-select
        value={selectTeam}
        valueKey="value"
        on-input={(v) => {
          // 手动更新数据
          Object.assign(selectTeam, v);
          // 手动触发select更新
          vm.componentInstance.setSelected();
        }}
        placeholder="请选择团队"
      >
        {groupOptions.map((i) => (
          <el-option label={i.label} value={i} key={i.value}></el-option>
        ))}
      </el-select>
    );

    return this.$msgbox({
      title: '选择团队',
      message: vm,
      showCancelButton: true,
      closeOnHashChange: false,
      closeOnClickModal: false,
      confirmButtonText: '确定',
      cancelButtonText: '取消'
    })
      .then(() => {
        fn.apply(this, [selectTeam.value]);
      })
      .catch(fn.bind(this, ''));
  };
};
