<template>
  <div>
    <el-table :data="tableData" class="clue-table" element-loading-text="加载中" fit ref="multipleTable" v-loading="loading" @selection-change="$emit('input', $event)" v-bind="tableOptions">
      <el-table-column v-if="isSelect" type="selection" width="55" label="全选" :selectable="selectable"></el-table-column>
      <template v-for="i in items">
        <el-table-column v-if="i.isSlot" :key="i.key" :label="i.label" :width="i.width" :prop="i.handle ? i.key + '_handle' : i.key">
          <template slot-scope="scope">
            <slot :name="'table_cell_' + i.key" :scope="scope"></slot>
          </template>
        </el-table-column>
        <slot :name="'table_cell_' + i.key" v-else-if="i.isColumnSlot"></slot>
        <el-table-column v-else-if="i.showOverflowTooltip" :key="i.key" :label="i.label" :width="i.width" :prop="i.handle ? i.key + '_handle' : i.key">
          <template slot-scope="scope">
            <el-tooltip effect="dark" placement="top">
              <div v-html="scope.row[i.handle ? i.key + '_handle' : i.key]" slot="content"></div>
              <div class="custom-tooltip">
                {{ scope.row[i.handle ? i.key + '_handle' : i.key] }}
              </div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column v-else :key="i.key" :label="i.label" :width="i.width" :prop="i.handle ? i.key + '_handle' : i.key" v-bind="i.elAttr"></el-table-column>
      </template>

      <!-- 操作 -->
      <slot></slot>
    </el-table>
    <Pagination :p_pageSize="paginationOptions.pageSize" :pageSizes="paginationOptions.pageSizes" :xhrCallback="init" ref="pagination"></Pagination>
  </div>
</template>

<script>
import Pagination from '@/components/Common/Pagination.vue';

/**
 * 列表展示公共组件 包含分页
 */
export default {
  name: 'listExhibition',
  components: { Pagination },
  data() {
    return {
      tableData: [],
      multipleSelection: [],
      loading: false
    };
  },
  props: {
    items: {
      type: Array,
      default() {
        return [];
      }
    },
    isSelect: {
      type: Boolean
    },
    selectable: {
      type: Function
    },
    tableOptions: {
      type: Object,
      default() {
        return {};
      }
    },
    paginationOptions: {
      type: Object,
      default() {
        return {
          pageSize: 20,
          pageSizes: [20, 30, 40, 50, 100]
        };
      }
    }
  },
  // activated() {
  //   this.refreshData();
  // },
  methods: {
    init(pageNo, pageSize) {
      this.loading = true;

      return new Promise((res) => {
        this.$emit('getData', {
          pageNo,
          pageSize,
          callback: async (data = {}) => {
            const { list = [], total = 0 } = data;
            // 处理列表数据
            this.tableData = await Promise.all(
              list.map(async (i, index) => {
                const handleData = await this.items.reduce(async (cur, { handle, key }) => {
                  cur = await cur;
                  if (handle) {
                    cur[key + '_handle'] = await handle.apply(this, [i[key], i, index]);
                  }

                  return cur;
                }, {});

                return {
                  ...i,
                  ...handleData
                };
              })
            );

            this.loading = false;
            res(total);
          }
        });
      });
    },
    queryData() {
      this.$refs.pagination.getResponse();
    },
    refreshData() {
      this.$refs.pagination.resetQuery();
    },
    resetData() {
      this.$refs.pagination.resetQuery();
    }
  }
};
</script>

<style>
.el-tooltip__popper {
  max-width: 400px;
}
</style>
<style lang="scss" scoped>
::v-deep {
  .el-table {
    .custom-tooltip {
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      white-space: normal;
    }
  }
}
</style>
