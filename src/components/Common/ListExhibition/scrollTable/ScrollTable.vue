<template>
  <div class="table">
    <div class="table_header" ref="headerWrapper">
      <RenderDom :vNode="headerRender()"></RenderDom>
    </div>
    <recycle-scroller
      ref="scroller"
      class="table_body"
      :items="getList"
      :item-size="itemSize"
      :key-field="keyField"
    >
      <template slot-scope="{ item, index }">
        <RenderDom :vNode="rowRender(item, index)"></RenderDom>
      </template>
    </recycle-scroller>
    <div class="hidden-columns" ref="hiddenColumns"><slot></slot></div>
  </div>
</template>

<script>
import RecycleScroller from '@/components/Common/ListExhibition/VirtualScroller/RecycleScroller';
import { throttle } from '@/utils';

export default {
  components: {
    RecycleScroller,
    RenderDom: {
      props: { vNode: [Array, String, Object, Number] },
      render(h) {
        if (typeof this.vNode === 'object') {
          return this.vNode;
        } else {
          return h('div', this.vNode);
        }
      }
    }
  },
  data() {
    return {
      isAllSelected: false,
      selection: [],
      handleList: []
    };
  },
  props: {
    list: {
      type: Array,
      default() {
        return [];
      }
    },
    isCheck: {
      type: Boolean,
      default: true
    },
    itemSize: {
      type: Number,
      default: null
    },
    minItemSize: {
      type: [Number, String],
      default: null
    },
    keyField: {
      type: String
    }
  },
  watch: {
    list(v) {
      this.selection = this.selection.filter((i) => v.includes(i));

      if (!this.selection.length) {
        this.isAllSelected = false;
      }
    }
  },
  computed: {
    getList() {
      return this.handleList.length ? this.handleList : this.list;
    }
  },
  created() {
    this.tableId = 'scroll-table_' + Date.now();
    this.$_columns = [];
  },
  mounted() {
    this.bindEvents();
  },
  methods: {
    handleCheckAllChange(val) {},
    insertColumn(column, index) {
      const array = this.$_columns;

      if (typeof index !== 'undefined') {
        array.splice(index, 0, column);
      } else {
        array.push(column);
      }
    },
    rowRender(row, $index) {
      const { $_columns: columns } = this;

      const html = (
        <div
          class="el-table table__row"
          style={{ height: this.itemSize + 'px' }}
        >
          {columns.map((column, cellIndex) => {
            const columnData = { ...column };

            const data = {
              _self: this.context,
              column: columnData,
              isSelected: this.selection.indexOf(row) > -1,
              row,
              $index
            };

            return (
              <span class="table__cell" style={this.getCellStyle(column)}>
                {column.renderCell.call(
                  this._renderProxy,
                  this.$createElement,
                  data
                )}
              </span>
            );
          })}
        </div>
      );
      return html;
    },
    headerRender() {
      const { $_columns: columns, isAllSelected } = this;

      return (
        <div class="el-table table__row">
          {columns.map((column, cellIndex) => (
            <span
              class={'table__cell ' + column.order}
              key={column.id}
              style={this.getCellStyle(column)}
            >
              <div class="cell">
                {column.renderHeader
                  ? column.renderHeader.call(
                      this._renderProxy,
                      this.$createElement,
                      {
                        column,
                        selection: this.selection,
                        isAllSelected,
                        $index: cellIndex
                      }
                    )
                  : column.label}
                {column.sortable ? (
                  <span
                    class="caret-wrapper"
                    on-click={($event) => this.handleSortClick($event, column)}
                  >
                    <i
                      class="sort-caret ascending"
                      on-click={($event) =>
                        this.handleSortClick($event, column, 'ascending')
                      }
                    ></i>
                    <i
                      class="sort-caret descending"
                      on-click={($event) =>
                        this.handleSortClick($event, column, 'descending')
                      }
                    ></i>
                  </span>
                ) : (
                  ''
                )}
              </div>
            </span>
          ))}
        </div>
      );
    },
    getCellStyle({ width }) {
      const style = {};
      if (width) {
        style.flex = `0 0 ${width}px`;
      }
      style.height = this.itemSize + 'px';
      style.lineHeight = this.itemSize + 'px';
      return style;
    },
    toggleRowSelection(row, newVal) {
      const index = this.selection.indexOf(row);
      const included = index !== -1;

      const addRow = () => {
        this.selection.push(row);
      };
      const removeRow = () => {
        this.selection.splice(index, 1);
      };

      if (typeof newVal === 'boolean') {
        if (newVal && !included) {
          addRow();
        } else if (!newVal && included) {
          removeRow();
        }
      } else {
        if (included) {
          removeRow();
        } else {
          addRow();
        }
      }

      const newSelection = (this.selection || []).slice();
      this.$emit('selection-change', newSelection);
    },
    toggleAllSelection() {
      const { list, selection, isAllSelected } = this;

      this.isAllSelected = !isAllSelected;

      list.forEach((i) => this.toggleRowSelection(i, this.isAllSelected));

      this.$emit('selection-change', selection);
    },
    toggleOrder({ order, sortOrders }) {
      if (order === '') return sortOrders[0];
      const index = sortOrders.indexOf(order || null);
      return sortOrders[index > sortOrders.length - 2 ? 0 : index + 1];
    },
    handleSortClick(event, column, givenOrder) {
      event.stopPropagation();
      const order =
        column.order === givenOrder
          ? null
          : givenOrder || this.toggleOrder(column);

      if (!column.sortable) return;

      let sortProp = this.sortProp;
      const sortingColumn = this.sortingColumn;

      if (
        sortingColumn !== column ||
        (sortingColumn === column && sortingColumn.order === null)
      ) {
        if (sortingColumn) {
          sortingColumn.order = null;
        }
        this.sortingColumn = column;
        sortProp = column.property;
      }

      let data = [];
      if (!order) {
        this.sortOrder =
          column.order =
          this.sortingColumn =
          this.sortProp =
            null;
      } else {
        this.sortOrder = column.order = order;
        this.sortProp = sortProp;

        data = this.orderBy(
          this.list,
          sortProp,
          this.sortOrder,
          this.sortingColumn.sortMethod,
          this.sortingColumn.sortBy
        );
      }

      this.handleList.splice(0, this.handleList.length, ...data);
    },
    isObject(obj) {
      return obj !== null && typeof obj === 'object';
    },
    orderBy(array, sortKey, reverse, sortMethod, sortBy) {
      if (
        !sortKey &&
        !sortMethod &&
        (!sortBy || (Array.isArray(sortBy) && !sortBy.length))
      ) {
        return array;
      }
      if (typeof reverse === 'string') {
        reverse = reverse === 'descending' ? -1 : 1;
      } else {
        reverse = reverse && reverse < 0 ? -1 : 1;
      }
      const getKey = sortMethod
        ? null
        : (value, index) => {
            if (sortBy) {
              if (!Array.isArray(sortBy)) {
                sortBy = [sortBy];
              }
              return sortBy.map(function (by) {
                if (typeof by === 'string') {
                  return value[by];
                } else {
                  return by(value, index, array);
                }
              });
            }
            if (sortKey !== '$key') {
              if (this.isObject(value) && '$value' in value) {
                value = value.$value;
              }
            }
            return [this.isObject(value) ? value[sortKey] : value];
          };
      const compare = function (a, b) {
        if (sortMethod) {
          return sortMethod(a.value, b.value);
        }
        for (let i = 0, len = a.key.length; i < len; i++) {
          if (a.key[i] < b.key[i]) {
            return -1;
          }
          if (a.key[i] > b.key[i]) {
            return 1;
          }
        }
        return 0;
      };
      return array
        .map(function (value, index) {
          return {
            value: value,
            index: index,
            key: getKey ? getKey(value, index) : null
          };
        })
        .sort(function (a, b) {
          let order = compare(a, b);
          if (!order) {
            // make stable https://en.wikipedia.org/wiki/Sorting_algorithm#Stability
            order = a.index - b.index;
          }
          return order * reverse;
        })
        .map((item) => item.value);
    },
    syncPostion: throttle(20, function () {
      const { scrollLeft } = this.$refs.scroller.$el;
      const { headerWrapper } = this.$refs;
      if (headerWrapper) headerWrapper.scrollLeft = scrollLeft;
    }),
    bindEvents() {
      this.$refs.scroller.$el.addEventListener('scroll', this.syncPostion, {
        passive: true
      });
    },
    unbindEvents() {
      this.$refs.scroller &&
        this.$refs.scroller.$el.removeEventListener(
          'scroll',
          this.syncPostion,
          {
            passive: true
          }
        );
    }
  },
  destroyed() {
    this.unbindEvents();
  }
};
</script>

<style lang='scss' scoped>
.table {
  display: flex;
  flex-direction: column;
  overflow-x: auto;

  &_header {
    font-size: 14px;
    color: #0d1b3f;
    user-select: none;
    text-align: center;
    font-weight: bold;
    flex: none;
    overflow: hidden;

    .table__cell {
      background-color: #f0f2f6;
    }
  }

  &_body {
    flex: 1;
  }

  &__row {
    display: flex;
    align-items: center;
    text-align: center;
    overflow: visible;
  }

  &__column {
    flex: 1;
    height: 100%;
    text-align: center;
    border-bottom: 1px solid #ebeef5;
    padding: 12px 0;
    min-width: 0;
    box-sizing: border-box;
    text-overflow: ellipsis;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__cell {
    box-sizing: border-box;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: normal;
    word-break: break-all;
    font-size: 14px;
    width: 100%;
    border-bottom: 1px solid #ebeef5;
    & > div {
      display: inline-table;
    }
  }
}

</style>