let columnIdSeed = 1;

export default {
  name: 'ScrollTableColumn',

  props: {
    type: {
      type: String,
      default: 'default'
    },
    label: String,
    className: String,
    labelClassName: String,
    property: String,
    prop: String,
    width: {},
    minWidth: {},
    renderHeader: Function,
    sortable: {
      type: [Boolean, String],
      default: false
    },
    sortMethod: Function,
    sortBy: [String, Function, Array],
    resizable: {
      type: Boolean,
      default: true
    },
    columnKey: String,
    align: String,
    headerAlign: String,
    showTooltipWhenOverflow: Boolean,
    showOverflowTooltip: Boolean,
    fixed: [Boolean, String],
    formatter: Function,
    selectable: Function,
    reserveSelection: Boolean,
    filterMethod: Function,
    filteredValue: Array,
    filters: Array,
    filterPlacement: String,
    filterMultiple: {
      type: Boolean,
      default: true
    },
    index: [Number, Function],
    sortOrders: {
      type: Array,
      default() {
        return ['ascending', 'descending', null];
      },
      validator(val) {
        return val.every(
          order => ['ascending', 'descending', null].indexOf(order) > -1
        );
      }
    }
  },

  data() {
    return {
      isSubColumn: false,
      columns: []
    };
  },

  computed: {
    owner() {
      let parent = this.$parent;
      while (parent && !parent.tableId) {
        parent = parent.$parent;
      }
      return parent;
    },

    realWidth() {
      return this.width;
    },

    realMinWidth() {
      return this.minWidth || 80;
    }
  },

  methods: {
    getPropsData(...props) {
      return props.reduce((prev, cur) => {
        if (Array.isArray(cur)) {
          cur.forEach(key => {
            prev[key] = this[key];
          });
        }
        return prev;
      }, {});
    },

    getColumnElIndex(children, child) {
      return [].indexOf.call(children, child);
    },

    setColumnWidth(column) {
      if (this.realWidth) {
        column.width = this.realWidth;
      }
      if (this.realMinWidth) {
        column.minWidth = this.realMinWidth;
      }
      if (!column.minWidth) {
        column.minWidth = 80;
      }
      column.realWidth =
        column.width === undefined ? column.minWidth : column.width;
      return column;
    },

    setColumnForcedProps(column) {
      // 对于特定类型的 column，某些属性不允许设置
      const type = column.type;
      const { toggleRowSelection, toggleAllSelection } = this.owner;

      const source =
        {
          selection: {
            renderHeader: function(h, { selection, isAllSelected }) {
              return (
                <el-checkbox
                  indeterminate={selection.length > 0 && !isAllSelected}
                  on-input={toggleAllSelection}
                  value={isAllSelected}
                />
              );
            },
            renderCell: function(h, { row, isSelected, column, $index }) {
              return (
                <el-checkbox
                  nativeOn-click={event => event.stopPropagation()}
                  value={isSelected}
                  disabled={
                    column.selectable
                      ? !column.selectable.call(null, row, $index)
                      : false
                  }
                  on-input={() => {
                    toggleRowSelection(row);
                  }}
                />
              );
            },
            sortable: false,
            resizable: false
          },
          index: {
            renderHeader: function(h, { column }) {
              return column.label || '#';
            },
            renderCell: function(h, { $index, column }) {
              let i = $index + 1;
              const index = column.index;

              if (typeof index === 'number') {
                i = $index + index;
              } else if (typeof index === 'function') {
                i = index($index);
              }

              return <div>{i}</div>;
            },
            sortable: false
          }
        }[type] || {};
      Object.keys(source).forEach(prop => {
        const value = source[prop];
        if (value !== undefined) {
          column[prop] =
            prop === 'className' ? `${column[prop]} ${value}` : value;
        }
      });
      return column;
    },

    // 默认渲染栅格方法
    defaultRenderCell(h, { row, column, $index }) {
      const property = column.property;
      const value = property && row[property];
      if (column && column.formatter) {
        return column.formatter(row, column, value, $index);
      }
      return value;
    },

    // 渲染栅格
    setColumnRenders(column) {
      if (column.type !== 'selection') {
        column.renderHeader = (h, scope) => {
          const renderHeader = this.$scopedSlots.header;
          return renderHeader ? renderHeader(scope) : column.label;
        };
      }

      const originRenderCell = column.renderCell || this.defaultRenderCell;

      // 对 renderCell 进行包装
      column.renderCell = (h, data) => {
        let children = null;
        if (this.$scopedSlots.default) {
          children = this.$scopedSlots.default(data);
        } else {
          children = originRenderCell(h, data);
        }
        const props = {
          class: 'cell',
          style: {}
        };
        let content = <div {...props}>{children}</div>;
        if (column.showOverflowTooltip) {
          content = (
            <el-tooltip
              class="item"
              effect="dark"
              content={children}
            >
              {content}
            </el-tooltip>
          );
        }
        return content;
      };
      return column;
    },

    // 合并
    mergeOptions(defaults, config) {
      const options = {};
      let key;
      for (key in defaults) {
        options[key] = defaults[key];
      }
      for (key in config) {
        if (Object.prototype.hasOwnProperty.call(config, key)) {
          const value = config[key];
          if (typeof value !== 'undefined') {
            options[key] = value;
          }
        }
      }
      return options;
    }
  },

  beforeCreate() {
    this.row = {};
    this.column = {};
    this.$index = 0;
    this.columnId = '';
  },

  created() {
    const parent = this.owner;
    this.columnId =
      (parent.tableId || parent.columnId) + '_column_' + columnIdSeed++;

    const type = this.type || 'default';
    const sortable = this.sortable === '' ? true : this.sortable;

    const cellStarts = {
      default: {
        order: ''
      },
      selection: {
        width: 48,
        minWidth: 48,
        realWidth: 48,
        order: '',
        className: 'el-table-column--selection'
      },
      index: {
        width: 48,
        minWidth: 48,
        realWidth: 48,
        order: ''
      }
    };

    const defaults = {
      ...cellStarts[type],
      id: this.columnId,
      type: type,
      property: this.prop || this.property,
      showOverflowTooltip:
        this.showOverflowTooltip || this.showTooltipWhenOverflow,
      // sort 相关属性
      sortable: sortable,
      // index 列
      index: this.index
    };

    const basicProps = [
      'columnKey',
      'label',
      'className',
      'labelClassName',
      'type',
      'renderHeader',
      'formatter',
      'fixed',
      'resizable'
    ];
    const sortProps = ['sortMethod', 'sortBy', 'sortOrders'];
    const selectProps = ['selectable', 'reserveSelection'];

    let column = this.getPropsData(basicProps, sortProps, selectProps);
    column = this.mergeOptions(defaults, column);

    [
      this.setColumnRenders,
      this.setColumnWidth,
      this.setColumnForcedProps
    ].reduce((a, b) => (...args) => a(b(...args)))(column);

    this.columnConfig = column;
  },

  mounted() {
    const parent = this.owner;
    const children = parent.$refs.hiddenColumns.children;
    const columnIndex = this.getColumnElIndex(children, this.$el);

    // 向父容器注册column
    parent.insertColumn(this.columnConfig, columnIndex);
  },

  render(h) {
    return h('div', this.$slots.default);
  }
};
