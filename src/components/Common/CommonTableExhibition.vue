<template>
  <div class="commonTableExhibition">
    <!-- 表单 -->
    <Form v-if="options.Form" :options="options.Form" ref="form" class="form commo-search-container" :keyEnterOnSubmit="onSubmit">
      <slot name="form">
        <div class="dialog-footer" style="margin-left: 20px">
          <el-button @click="onSubmit" size="small" :loading="initLoading" type="primary">查询</el-button>
          <el-button @click="onReset" size="small" :loading="initLoading">重置</el-button>
        </div>
      </slot>
    </Form>

    <!-- 操作栏 -->
    <BatchOperation v-if="options.Operate" :operateList="options.Operate.items"></BatchOperation>

    <!-- 列表 -->
    <template v-if="options.Table">
      <el-table :data="tableData" class="clue-table" element-loading-text="加载中" fit highlight-current-row ref="multipleTable" v-loading="initLoading" @selection-change="handleSelectionChange" @cell-mouse-enter="cellMouseEnter" @cell-mouse-leave="cellMouseLeave">
        >
        <el-table-column v-if="options.Table.isSelect" type="selection" width="55" label="全选" :selectable="options.Table.selectable"></el-table-column>
        <template v-for="i in options.Table.items">
          <el-table-column v-if="i.isSlot" :key="i.key" align="center" :label="i.label" :prop="i.handle ? i.key + '_handle' : i.key" :show-overflow-tooltip="i['show-overflow-tooltip'] ? i['show-overflow-tooltip'] : false">
            <template slot-scope="scope">
              <slot :name="'table_cell_' + i.key" :scope="scope"></slot>
            </template>
          </el-table-column>
          <el-table-column v-else :key="i.key" align="center" :label="i.label" :prop="i.handle ? i.key + '_handle' : i.key" :show-overflow-tooltip="i['show-overflow-tooltip'] ? i['show-overflow-tooltip'] : false"></el-table-column>
        </template>

        <!-- 操作 -->
        <el-table-column label="操作" fixed="right" :width="options.Table.operateWidth || 60" v-if="options.Table.operates">
          <template slot-scope="scope">
            <div v-for="(i, index) in options.Table.operates" :key="index">
              <Authority :auth="i.authority" v-if="i.loadCondition ? i.loadCondition(scope.row, scope) : true">
                <el-button @click="tabelOperateClick(i, scope.row)" type="text">{{ i.label }}</el-button>
              </Authority>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <Pagination :p_pageSize="options.Pagination.pageSize" :pageSizes="options.Pagination.pageSizes" :xhrCallback="init" ref="pagination"></Pagination>
    </template>

    <!-- 弹出框 -->
    <Dialog :callback="dialogCallback" class="dialog" :dialogOptions="options.Dialog" :type="dialogType" ref="dialog">
      <template slot-scope="{ scope }" v-if="isDialogSlot">
        <slot name="dialog" :scope="scope"></slot>
      </template>
    </Dialog>

    <!-- 推广 -->
    <promotion :promotionUrl="promotionUrl" ref="promotion"><span></span></promotion>

    <slot name="other"></slot>

    <!-- 列表列移入弹出框 -->
    <ToolTip ref="toolTip"></ToolTip>
  </div>
</template>

<script>
import Form from '@/components/Dialog/Form';
import BatchOperation from '@/components/BatchOperation';
import Dialog from '@/components/Dialog/index.vue';
import Promotion from '@/components/Promotion';
import Pagination from '@/components/Common/Pagination.vue';

import pickBy from 'lodash/pickBy';

export default {
  name: 'CommonTableExhibition',
  components: { Form, BatchOperation, Dialog, Pagination, Promotion },
  data() {
    return {
      tableData: [],
      multipleSelection: [], // table列表选择的数据
      toolTipTimer: null,
      initLoading: false,
      dialogType: '',

      promotionUrl: void 0,
      toolTipData: {} // 记录每次移入时的数据
    };
  },
  props: {
    options: {
      type: Object
    },
    /* 
      vue 2.5 下父组件具名插槽传递内容到孙子组件下
      <template slot-scope="{ scope }"><slot name="dialog" :scope="scope"></slot></template>
      该结构会阻碍孙子组件的默认内容显示，必须加v-if
    */
    isDialogSlot: {
      type: Boolean,
      default: false
    }
  },
  provide() {
    return {
      rootComponent: this
    };
  },
  mounted() {},
  activated() {
    this.onSubmit();
  },
  methods: {
    init(pageNo, pageSize) {
      this.initLoading = true;
      const formData = this.$refs.form.getSubmitFormData();
      const data = pickBy(formData, (val) => !!val);

      return new Promise((res) => {
        this.$emit('getTableData', {
          data,
          pageNo,
          pageSize,
          callback: (data = {}) => {
            const { list = [], total = 0 } = data;
            // 处理列表数据
            this.tableData = list.map((i, index) => {
              return {
                ...i,
                ...this.options.Table.items.reduce((cur, { handle, key }) => {
                  if (handle) {
                    cur[key + '_handle'] = handle.apply(this, [i[key], i, index]);
                  }
                  return cur;
                }, {})
              };
            });
            this.initLoading = false;
            res(total);
          }
        });
      });
    },
    // 重置数据
    onReset() {
      // 清空数据
      this.$refs.form.parseTemplate();

      this.onSubmit();
    },
    onSubmit() {
      if (!this.options.Table) return;
      // 分页器页码变为1
      this.$refs.pagination.resetQuery();
    },
    handleSelectionChange(rows) {
      this.multipleSelection = rows;

      this.$emit('handleSelectionChange', rows);
    },
    cellMouseEnter(row, column, cell, event) {
      const property = column.property;
      const positions = [event.clientY, event.clientX];

      let toolType = '';
      if (this.$parent.cellMouseEnter && (toolType = this.$parent.cellMouseEnter(property))) {
        if (row[property] !== '') {
          if (this.toolTipTimer) {
            clearTimeout(this.toolTipTimer);
          }
          this.toolTipData = row;
          this.$refs.toolTip.setPositions(positions);
          this.toolTipTimer = setTimeout(() => {
            this.$refs.toolTip.setVisible(true, {
              positions,
              record: this.toolTipData,
              type: toolType
            });
          }, 300);
        } else {
          this.cellMouseLeave();
        }
      }
    },
    cellMouseLeave() {
      if (this.toolTipTimer) {
        clearTimeout(this.toolTipTimer);
      }
      this.$refs.toolTip.setVisible();
    },
    // 列表操作栏点击
    tabelOperateClick(options, row) {
      options.click && options.click.apply(this, [row]);
    },
    // 操作检查是否至少选择了一条数据
    checkSelectTable(message) {
      if (!this.multipleSelection.length) {
        this.$message({
          message,
          type: 'warning'
        });
        return false;
      }
      return true;
    },
    operateSuccess(message) {
      this.$message({
        type: 'success',
        message: `${message}成功`
      });
      this.$refs.pagination.getResponse();
    },
    operateWarning(message) {
      this.$message({
        type: 'warning',
        message: `${message}`
      });
      this.$refs.pagination.getResponse();
    },
    dialogVisible(type, params) {
      this.dialogType = type;
      this.$nextTick(() => {
        this.$refs.dialog.setVisible(true, params);
      });
    },
    // 弹出框回调
    dialogCallback(data) {
      this.$parent.dialogCallback && this.$parent.dialogCallback(data, this);

      return Promise.resolve(this);
    }
  }
};
</script>

<style lang='scss' scoped>
.commonTableExhibition {
  .form {
    position: relative;
    ::v-deep {
      .el-form {
        display: flex;
        flex-wrap: wrap;
      }
      .el-form-item {
        width: 33%;
        display: flex;
        margin-bottom: 16px;

        .el-form-item__label {
          padding-right: 16px;
          white-space: nowrap;
          display: inline-block;
          line-height: 32px;
          font-family: 'PingFangSC-Regular';
          color: rgb(0, 0, 0);
          font-weight: normal;
        }
        .el-form-item__content {
          flex: 1;
          margin-left: 0px !important;
          & > div {
            width: 100%;
          }
        }
      }
    }
  }
  .dialog {
    ::v-deep {
      .el-dialog {
        .el-dialog__header {
          text-align: center;
        }
      }
    }
  }

  &-table {
    margin-top: 10px;
    ::v-deep {
      th > .cell {
        padding-left: 14px;
      }
    }
  }
}
</style>