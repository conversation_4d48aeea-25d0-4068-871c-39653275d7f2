import Statics from './static';
import Dynamics from './dynamic/index';
import pickBy from 'lodash/pickBy';
import { isPlainObject, isArray } from './utils';

function Dict(instanceConfig) {
  this.defaults = instanceConfig;
  // 缓存
  this.cacheMap = new Map();
}

Dict.prototype.request = function (config = {}) {
  // Allow for dict('example/type'[, config]) a la fetch API
  if (typeof config === 'string') {
    config = arguments[1] || {};
    config.type = arguments[0];
  }

  const { type } = config;

  let options;
  // 静态数据 直接返回
  if ((options = Statics[type])) {
    return Promise.resolve(options);
  }

  const { IsCatch, AxiosRequest, FieldKeys } = this.defaults;
  // 动态数据
  if ((options = Dynamics[type])) {
    const { fieldKeys = FieldKeys, isCatch = IsCatch, axiosRequest = AxiosRequest, params = config.params || {} } = options;

    // 获取对应唯一key（type + params）
    const key = this.getKey(type, params);
    // 通过isCatch和key判断是否使用缓存
    if (isCatch && this.cacheMap.has(key)) {
      return this.cacheMap.get(key);
    }
    const promise = axiosRequest(params)
      .then((res) => {
        // 去重
        const values = new Set();
        return res.data.reduce((acc, cur) => {
          const [lk, vk, ...ks] = fieldKeys;
          const label = cur[lk];
          const value = cur[vk];

          if (!values.has(value)) {
            acc.push({
              ...ks.reduce((pre, k) => {
                pre[k] = cur[k];
                return pre;
              }, {}),
              [FieldKeys[0]]: label,
              [FieldKeys[1]]: value
            });
            values.add(value);
          }
          return acc;
        }, []);
      })
      .catch(() => {
        // 查询失败
        if (isCatch && this.cacheMap.has(key)) this.cacheMap.delete(key);
        return [];
      });
    if (isCatch) this.cacheMap.set(key, promise);
    return promise;
  }

  return Promise.resolve([]);
};

// 获取唯一key
Dict.prototype.getKey = function (type, params = {}) {
  // 判断是否为对象
  isPlainObject(params) || (params = { params });
  params = pickBy(params, (val) => !!val);

  // 转换为数组，按key进行排序
  const paramsArr = Object.entries(params).sort(([a], [b]) => a.localeCompare(b));

  // 转换为字符串, 没有考虑参数中存在对象的情况
  return paramsArr.reduce((pre, cur, index) => {
    index || (pre += '-');

    let v = cur[1];
    isArray(v) && (v = [...v].sort());
    return (pre += [cur[0], v].join(':') + ';');
  }, type);
};

Dict.prototype.findLabelByValue = function ({ type, value, params }) {
  const key = this.getKey(type, params);
  return this.request(key).then((res) => res.find(({ value: v }) => v === value));
};

export default Dict;
