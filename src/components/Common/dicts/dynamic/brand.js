import { withExtTenantIdRequest } from '@/utils/request';

// 业务分组
export const BRAND_CLASS = {
  fieldKeys: ['name', 'id'],
  axiosRequest(params = {}) {
    return withExtTenantIdRequest({
      url: 'soyoungzg/api/brand/listBrandInfo',
      method: 'post',
      data: params
    });
  }
};

// 查询专属顾问对应的拓展品牌 数组参数
export const BRAND_CS_LIST_BY_CSIDS = {
  fieldKeys: ['brandName', 'brandId'],
  axiosRequest(params = {}) {
    return withExtTenantIdRequest({
      url: '/soyoungzg/api/csBrandRelation/listByCsIds',
      method: 'post',
      data: params
    });
  }
};

// 获取全部品牌
export const BRAND_LIST_ALL = {
  fieldKeys: ['name', 'id'],
  axiosRequest(params = {}) {
    return withExtTenantIdRequest({
      url: '/soyoungzg/api/brand/listAllBrandName',
      method: 'post',
      data: params
    });
  }
};

// 获取全部品牌
export const BRAND_BRIEF_LIST_ALL = {
  fieldKeys: ['name', 'code'],
  axiosRequest(params = {}) {
    return withExtTenantIdRequest({
      url: '/soyoungzg/api/brand/briefListAll',
      method: 'post',
      data: params
    });
  }
};

// 偏好品类
export const BRAND_COMMODITY_CATEGORY = {
  fieldKeys: ['name', 'id'],
  axiosRequest(params = {}) {
    return withExtTenantIdRequest({
      url: '/commodity/api/category/listAll',
      method: 'post',
      data: params
    });
  }
};

// '商品矩阵(产品定位)，字典：product_position
export const PRODUCT_POSITION = {
  params: {
    type: 'product_position'
  }
};

// 物料类型，字典：sap_materiel_type
export const SAP_MATERIEL_TYPE = {
  params: {
    type: 'sap_materiel_type'
  }
};

// 查询品牌配置：soyoungzg_brand_module
export const SOYOUNGZG_BRAND_MODULE = {
  params: {
    type: 'soyoungzg_brand_module'
  }
};



// 获取全部采购类目
export const BRAND_CATEGORY_LIST_ALL_BRIEF = {
  fieldKeys: ['name', 'id'],
  axiosRequest(params = {}) {
    return withExtTenantIdRequest({
      url: '/soyoungzg/api/brandCategory/listAllBrief',
      method: 'post',
      data: params
    });
  }
};

// 获取所有品牌分类列表
export const BRAND_CATEGORY_LIST_ALL = {
  fieldKeys: ['name', 'id'],
  axiosRequest(params = {}) {
    return withExtTenantIdRequest({
      url: '/soyoungzg/api/brandCategory/listAll',
      method: 'post',
      data: params
    });
  }
};
