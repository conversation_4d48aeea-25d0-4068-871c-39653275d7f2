import { withExtTenantIdRequest } from '@/utils/request';

// 拓展渠道
export const COMMON_EXPAND_CHANNEL = {
  params: {
    type: 'soyoungzg_channel'
  }
};

// 拓展渠道线上
export const COMMON_EXPAND_CHANNEL_ONLINE = {
  params: {
    type: 'soyoungzg_channel_online'
  }
};

// 拓展渠道线下
export const COMMON_EXPAND_CHANNEL_OFFLINE = {
  params: {
    type: 'soyoungzg_channel_offline'
  }
};

// 企业/个人
export const COMMON_ENTERPRISE_NATURE = {
  params: {
    type: 'distributor_merchant_type'
  }
};

// 主营类目
export const COMMON_CATEGORY = {
  params: {
    type: 'syzg_business_category'
  }
};

// 主营类型-国际
export const COMMON_CATEGORY_NEW = {
  params: {
    type: 'syzg_business_category_new '
  }
};

// 拓展顾问
export const COMMON_EXPAND_ADVISER = {
  fieldKeys: ['name', 'id'],
  axiosRequest(params) {
    return withExtTenantIdRequest({
      url: '/soyoungzg/api/customerService/listAll',
      method: 'post',
      data: params
    });
  }
};

// 拓展顾问 取userId
export const COMMON_EXPAND_ADVISER_USER = {
  fieldKeys: ['name', 'userId'],
  axiosRequest(params) {
    return withExtTenantIdRequest({
      url: '/soyoungzg/api/customerService/listAll',
      method: 'post',
      data: params
    });
  }
};

// 拓展顾问显示分组
export const COMMON_EXPAND_ADVISER_SHOWTEAM = {
  fieldKeys: ['name', 'id'],
  axiosRequest(params) {
    return withExtTenantIdRequest({
      url: '/soyoungzg/api/customerService/listAll',
      method: 'post',
      data: params
    }).then((res) => {
      return {
        data: res.data
          .filter((i) => i.status === 'ENABLE')
          .map(({ name, contactPhone = '', organizationName = '', ...i }) => ({
            ...i,
            name: `${name + ' ' + contactPhone + ' ' + organizationName}`
          }))
      };
    });
  }
};

// 国际拓展顾问
export const COMMON_EXPAND_ADVISER_GJ = {
  fieldKeys: ['name', 'id'],
  axiosRequest(params) {
    return withExtTenantIdRequest({
      url: '/soyoungzg/api/customerService/listAll',
      method: 'post',
      data: {
        groupId: '3'
      }
    });
  }
};

// 国际拓展顾问 取userId
export const COMMON_EXPAND_ADVISER_GJ_USER = {
  fieldKeys: ['name', 'userId'],
  axiosRequest(params) {
    return withExtTenantIdRequest({
      url: '/soyoungzg/api/customerService/listAll',
      method: 'post',
      data: {
        groupId: '3'
      }
    });
  }
};

// 偏好品牌
export const COMMON_PREFER_BRAND = {
  fieldKeys: ['name', 'id'],
  axiosRequest(params) {
    return withExtTenantIdRequest({
      url: '/soyoungzg/api/brand/listAllBrandName',
      method: 'post',
      data: params
    });
  }
};

// 拓展品牌
export const COMMON_EXPAND_BRAND = {
  fieldKeys: ['name', 'id'],
  axiosRequest(params) {
    return withExtTenantIdRequest({
      url: '/soyoungzg/api/brand/listAllBrandName',
      method: 'post',
      data: params
    });
  }
};

// 企业规模
export const COMMON_ENTERPRISE_SCALE = {
  params: {
    type: 'syzg_enterprise_scale'
  }
};

// 获取归属团队信息
export const COMMON_SHOPTAFFGROUP_LIST = {
  fieldKeys: ['name', 'id'],
  axiosRequest(params = {}) {
    return withExtTenantIdRequest({
      url: '/ocean/api/shopStaffGroup/listAll',
      method: 'post',
      data: params
    });
  }
};

// 注册方式
export const COMMON_ZG_SOURCE = {
  params: {
    type: 'soyoungzg_source'
  }
};

// 联系方式 手机号-MOBILE,电话：TElEPHONE,微信：WE_CHAT,QQ:QQ,钉钉：DING_DING,其他：OTHER
export const COMMON_CONTACT_TYPE = {
  params: {
    type: 'distributor_contact_type'
  }
};

// 获取物流公司
export const COMMON_EXPRESS_COMPANY = {
  fieldKeys: ['name', 'code'],
  axiosRequest(params = {}) {
    return withExtTenantIdRequest({
      url: '/soyoungzg/api/orderDelivery/listAllDeliveryCompany',
      method: 'post',
      data: {
        deliveryCompany: {}
      }
    });
  }
};

// 外部平台名称
export const COMMON_EXT_PLATFORM_NAME = {
  params: {
    type: 'soyoungzg_channel_online'
  }
};

// 行业评级 S/A/B/C
export const COMMON_TRADE_LEVEL = {
  params: {
    type: 'soyoungzg_level'
  }
};

// 外部渠道店 （不分页）
export const COMMON_EXTERNALCHANNEL_LIST_ALL = {
  fieldKeys: ['channelName', 'channelId'],
  isCatch: false,
  axiosRequest(params = {}) {
    return withExtTenantIdRequest({
      url: '/soyoungzg/api/externalChannel/list',
      method: 'post',
      data: params
    });
  }
};

// 获取归属团队信息
export const COMMON_STAFF_LIST = {
  fieldKeys: ['name', 'userId'],
  axiosRequest(params = {}) {
    return withExtTenantIdRequest({
      url: '/ocean/api/shopStaff/listAll',
      method: 'post',
      data: params
    });
  }
};

// 获取直供电商归属团队信息
export const COMMON_ZGSTAFF_LIST = {
  fieldKeys: ['name', 'userId'],
  axiosRequest(params = {}) {
    return Promise.all([
      withExtTenantIdRequest({
        url: '/ocean/api/shopStaff/listAll',
        method: 'post',
        data: {
          groupId: '1'
        }
      }),
      withExtTenantIdRequest({
        url: '/ocean/api/shopStaff/listAll',
        method: 'post',
        data: {
          groupId: '2'
        }
      })
    ]).then((res) => {
      const data = res.map((i) => i.data).flat();
      return { data };
    });
  }
};

// 获取国际归属团队信息
export const COMMON_GJSTAFF_LIST = {
  fieldKeys: ['name', 'userId'],
  axiosRequest(params = {}) {
    return withExtTenantIdRequest({
      url: '/ocean/api/shopStaff/listAll',
      method: 'post',
      data: {
        groupId: '3'
      }
    });
  }
};

// 专属顾问所属分组字典
export const CUSTOMER_SERVICE_TEAM = {
  params: {
    type: 'customer_service_team'
  }
};

// 分销商分配状态字典
export const COMMON_DISTRIBUTOR_ASSIGN_STATUS = {
  params: {
    type: 'distributor_assign_status'
  }
};

// 分销商距离可分配的天数
export const COMMON_DISTRIBUTOR_ASSIGNABLE_DAY = {
  params: {
    type: 'distributor_assignable_day'
  }
};

// 专属顾问所属业务类型
export const COMMON_CS_BIZ_TYPE = {
  params: {
    type: 'syzg_cs_biz_type'
  }
};

// 渠道配置 - 业务类型
export const COMMON_SYZH_CHANNEL_BIZ_TYPE = {
  params: {
    type: 'syzg_channel_biz_type'
  }
};

// 渠道配置 - 订单类型
export const COMMON_SYZH_CHANNEL_TYPE = {
  params: {
    type: 'syzg_channel_type'
  }
};

// 分销商广场下拉列表-偏好商品价格区间
export const COMMON_COMMODITY_PRICE_RANGE = {
  params: {
    type: 'commodity_price_range'
  }
};

// 分销商广场下拉列表-商品利润空间
export const COMMON_GROSS_PROFIT_RANGE = {
  params: {
    type: 'gross_profit_range'
  }
};

// 分销商广场下拉列表-客户等级
export const COMMON_PORTRAIT_LEVEL = {
  params: {
    type: 'distributor_portrait_level'
  }
};
// 分销商广场下拉列表-客户生命周期
export const COMMON_ACTIVE_AEGREE = {
  params: {
    type: 'soyoungzg_active_degree'
  }
};

// 获取专属顾问标签
export const COMMON_CS_DEFINEMARK_LIST_ALL = {
  fieldKeys: ['name', 'id'],
  axiosRequest(params = {}) {
    return withExtTenantIdRequest({
      url: '/soyoungzg/api/csDefineMark/listAll',
      method: 'post',
      data: params
    });
  }
};

// 分销商广场下拉列表-采购方式
export const COMMON_DISTRIBUTOR_PURCHASE_TYPE = {
  params: {
    type: 'distributor_purchase_type'
  }
};

// 分销商广场下拉列表-经营渠道
export const COMMON_CHANNEL = {
  params: {
    type: 'soyoungzg_channel'
  }
};

// 分销商广场下拉列表-店铺等级
export const COMMON_SHOP_LEVEL_TYPE = {
  params: {
    type: 'platform_shop_level_type'
  }
};

// 分销商广场下拉列表-主营类目
export const COMMON_BUSINESS_CATEGORY = {
  params: {
    type: 'syzg_business_category'
  }
};

// 分销商广场下拉列表-采购金额下拉列表
export const COMMON_PORTRAIT_PURCHASE_AMOUNT = {
  params: {
    type: 'distributor_portrait_purchase_amount'
  }
};

// 分销商广场下拉列表-采购订单下拉列表
export const COMMON_PORTRAIT_PURCHASE_NUM = {
  params: {
    type: 'distributor_portrait_purchase_num'
  }
};

// 分销商广场下拉列表-采购金额环比下拉列表
export const COMMON_PORTRAIT_PURCHASE_AMOUNT_RATE = {
  params: {
    type: 'distributor_portrait_amount_rate'
  }
};

// 分销商广场下拉列表-采购订单环比下拉列表
export const COMMON_PORTRAIT_PURCHASE_COUNT_RATE = {
  params: {
    type: 'distributor_portrait_order_count_rate'
  }
};

// 分销商广场下拉列表-环比
export const COMMON_RATE = {
  params: {
    type: 'syzg_rate'
  }
};

// 渠道列表-币种
export const COMMON_CURRENCY_CODE = {
  params: {
    type: 'soyoungzg_currency_code'
  }
};

// OMS-币种
export const COMMON_OMS_CURRENCY_CODE = {
  fieldKeys: ['fullDescribe', 'code'],
  axiosRequest() {
    return withExtTenantIdRequest({
      url: '/soyoungzg/api/externalChannel/listByLanguageCodeAndCodes',
      method: 'get'
    });
  }
};

// sap渠道列表
export const COMMON_EXTERNALCHANNEL_LIST_SAP_CHANNEL_ALL = {
  fieldKeys: ['sapChannelName', 'sapChannelCode'],
  isCatch: false,
  axiosRequest(params = {}) {
    return withExtTenantIdRequest({
      url: '/soyoungzg/api/externalChannel/listAllBySapChannel',
      method: 'post',
      data: params
    });
  }
};

// 贸易类型字段：tradeType，大贸：GENERAL_TRADE，海淘：CROSS_BORDER_TRADE，大贸海淘：GENERAL_AND_CROSS_BORDER_TRADE
export const COMMODITY_TRADE_TYPE = {
  params: {
    type: 'commodity_trade_type'
  }
};
// OMS贸易类型字段：一般贸易:GENERAL_TRADE 跨境进口:CROSS_BORDER_IMPORT 跨境出口：CROSS_BORDER_EXPORT
export const OMS_TRADE_TYPE = {
  params: {
    type: 'oms_trade_type'
  }
};

// 获取商品类型 过滤成大贸海淘
export const COMMONODITY_TYPE = {
  fieldKeys: ['label', 'value'],
  isCatch: false,
  axiosRequest(params = {}) {
    return withExtTenantIdRequest({
      url: '/common/api/dict/listByType?type=commodity_type',
      method: 'get'
    }).then((res) => {
      const arr = [];
      res.data.forEach((item) => {
        if (item.value !== 'CARD') {
          arr.push({
            value: item.value,
            label: gl(item.value)
          });
        }
      });
      return { data: arr };
    });
  }
};

function gl(value) {
  let key = '';
  switch (value) {
    case 'ACTUAL':
      // 大贸
      key = '大贸';
      break;
    case 'GLOBAL':
      // 海淘
      key = '海淘';
      break;
  }
  return key;
}

// 分销系统全部员工列表 不分页
export const COMMON_STAFF_LISTALL = {
  fieldKeys: ['name', 'userId'],
  axiosRequest(params = {}) {
    return withExtTenantIdRequest({
      url: '/ocean/api/shopStaffApi/listAll',
      method: 'post',
      data: params
    });
  }
};

// 品牌找分销-历史采购品类
export const CATEGORY_LISTALL = {
  fieldKeys: ['name', 'id'],
  axiosRequest() {
    return withExtTenantIdRequest({
      url: '/soyoungzg/api/brandCategory/list',
      method: 'post',
      data: {
        data: {
          name: ''
        },
        pageNo: 1,
        pageSize: 100
      }
    }).then((res) => {
      return { data: res.data.list };
    });
  }
};

// 品牌找分销-采购品牌
export const BRAND_LISTALL = {
  fieldKeys: ['name', 'id'],
  axiosRequest() {
    return withExtTenantIdRequest({
      url: '/soyoungzg/api/brand/listAllBrandName',
      method: 'post'
    });
  }
};

// 品牌找分销-采购商品
export const COMMODITY_LISTALL = {
  fieldKeys: ['commodityName', 'id'],
  axiosRequest() {
    return withExtTenantIdRequest({
      url: '/soyoungzg/api/commodity/searchList',
      method: 'post',
      data: {
        commodityName: ''
      }
    });
  }
};

// 合同外政策-申请单状态
export const EXTERNAL_POLICY_STATUS = {
  params: {
    type: 'external_policy_status'
  }
};

// 合同外政策-审批状态
export const EXTERNAL_POLICY_AUDIT_STATUS = {
  params: {
    type: 'external_policy_audit_status'
  }
};

// 平台返利发放-状态
export const PLATFORM_POLICY_ISSUE_APPLY_STATUS = {
  params: {
    type: 'platform_policy_issue_apply_staus'
  }
};

// 分销团队接口数据
export const SHOW_DISTRIBUTOR_TEAM_LIST = {
  fieldKeys: ['name', 'id'],
  axiosRequest() {
    return withExtTenantIdRequest({
      url: '/ocean/api/shopStaffGroup/listAll',
      method: 'post',
      data: { isShow: '1' }
    });
  }
};

// 渠道归属
export const SOYOUNGZG_CHANNEL_TYPE = {
  params: {
    type: 'soyoungzg_channel_type'
  }
};

// 拓展渠道线上
export const SOYOUNGZG_CHANNEL_ONLINE = {
  params: {
    type: 'soyoungzg_channel_online'
  }
};

// 拓展渠道线下
export const SOYOUNGZG_CHANNEL_OFFLINE = {
  params: {
    type: 'soyoungzg_channel_offline'
  }
};

// 客户业务类型
export const DISTRIBUTOR_BUSINESS_TYPE_AND_EXTEND = {
  params: {
    type: 'distributor_business_type_and_extend'
  }
};
