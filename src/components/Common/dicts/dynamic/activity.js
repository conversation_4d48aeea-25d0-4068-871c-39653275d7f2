import { withExtTenantIdRequest } from '@/utils/request';
// 新客有礼 - 权益类型
export const ACTIVITY_GIFT_TYPE = {
  params: {
    type: 'soyoungzg_gift_type'
  }
};

// 热销商品 维度的（单维度。多维度）
export const COMMON_HOT_COMMODITY_ACTIVTY_TYPE = {
  params: {
    type: 'hot_commodity_activity_type'
  }
};

// 根据排序 - 所有热销商品列表
export const COMMON_HOT_COMMODITY_ACTIVTY_LIST_ALL = {
  fieldKeys: ['name', 'id'],
  isCatch: false,
  axiosRequest(params) {
    return withExtTenantIdRequest({
      url: '/soyoungzg/api/hotCommodityActivity/listAll',
      method: 'post',
      data: params
    });
  }
};
