import { withExtTenantIdRequest } from '@/utils/request';

// 充值类型
export const DISTRIBUTOR_RRCHARGE_TYPE = {
  params: {
    type: 'soyoungzg_account_type'
  }
};

// 是否创建了群
export const DISTRIBUTOR_ISCREATEGOURP = {
  params: {
    type: 'distributor_group_handover_status'
  }
};

// 分销商审核状态
export const DISTRIBUTOR_AUDIT_STATUS = {
  params: {
    type: 'soyoungzg_audit_status'
  }
};

// 分销商注销审核状态
export const DISTRIBUTOR_LOGOUT_STATUS = {
  params: {
    type: 'soyoungzg_normal_logout_status'
  }
};

// 分销商提现OA创建合作单位状态
export const DISTRIBUTOR_CASHOUT_OA_COOPERATEUNIT_STATUS = {
  params: {
    type: 'oa_cooperator_status'
  }
};

// 分销商离划入公海池时间
export const DISTRIBUTOR_TOPUBLICDAY = {
  params: {
    type: 'soyoungzg_to_public_day'
  }
};

// 分销商入驻方式
export const DISTRIBUTOR_JOINMODE = {
  params: {
    type: 'soyoungzg_source'
  }
};

// 分销商入冻结原因
export const DISTRIBUTOR_FREEZEREASON = {
  params: {
    type: 'syzg_distributor_freeze_reason'
  }
};

// 分销商店铺类型（专营店...）
export const DISTRIBUTOR_SHOP_TYPE = {
  params: {
    type: 'soyoungzg_shop_type'
  }
};

// 分销商业务板块：大分销，淘内，SNS，供应链
export const DISTRIBUTOR_BUSINESSPLATE = {
  params: {
    type: 'soyoungzg_distributor_business_plate'
  }
};

// 分销商白名单 渠道
export const DISTRIBUTOR_WEBSITEWHIT_CHANNEL = {
  fieldKeys: ['platformName', 'platform'],
  isCatch: false,
  axiosRequest(params) {
    return withExtTenantIdRequest({
      url: '/soyoungzg/api/distributorWebsiteWhitelist/listAll',
      method: 'post',
      data: params
    });
  }
};

// 分销商白名单 店铺
export const DISTRIBUTOR_WEBSITEWHITE_SHOP = {
  fieldKeys: ['platformShopName', 'id'],
  isCatch: false,
  axiosRequest(params) {
    return withExtTenantIdRequest({
      url: '/soyoungzg/api/distributorWebsiteWhitelist/listAll',
      method: 'post',
      data: params
    });
  }
};
