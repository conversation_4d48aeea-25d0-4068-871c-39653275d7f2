// 订单状态
export const ORDER_STATUS = {
  params: {
    type: 'syzg_order_status'
  }
};

// 订单类型
export const ORDER_TYPE = {
  params: {
    type: 'syzg_order_type'
  }
};

// 订单创建终端 小程序/APP/PC
export const ORDER_CHANMEL_TYPE = {
  params: {
    type: 'syzg_order_channel_type'
  }
};

// 订单来源 特供采购/普通订单/新客有礼/小样订单/推品清单
export const ORDER_SOURCE = {
  params: {
    type: 'syzg_order_source'
  }
};

// 订单创建方式 普通类型/一件代发
export const ORDER_CREATE_TYPE = {
  params: {
    type: 'syzg_order_sale_type'
  }
};

// 订单口岸 长沙/郑州综保（总署版）/上海（总署版）
export const ORDER_SEAPORT = {
  params: {
    type: 'order_global_customs'
  }
};

// 订单退款状态
export const ORDER_REFUND_STATUS = {
  params: {
    type: 'refund_status'
  }
};