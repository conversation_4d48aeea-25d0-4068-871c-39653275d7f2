// 线索状态
export const CLUE_STATUS = {
  params: {
    type: 'distributor_leads_status'
  }
};

// 线索跟进事项
export const CLUE_FOLLOWUP_ITEM = {
  params: {
    type: 'syzg_todo_develop_follow_label'
  }
};

// 线索跟进事项
export const CLUE_FOLLOWUP_ITEM_DB = {
  params: {
    type: 'syzg_todo_distributor_follow_label'
  }
};

// 线索跟进事项
export const CLUE_FOLLOWUP_ITEM_ALL = {
  params: {
    type: 'syzg_todo_follow_label'
  }
};

// 线索跟进事项
export const CLUE_FOLLOWUP_WAY = {
  params: {
    type: 'syzg_todo_follow_way'
  }
};

// 线索跟进结果 - 触达
export const CLUE_FOLLOWUP_RESULT_CONTACT = {
  params: {
    type: 'syzg_todo_contact_follow_result'
  }
};
// 线索跟进结果 - 加微
export const CLUE_FOLLOWUP_RESULT_ADD_WECHAT = {
  params: {
    type: 'syzg_todo_addwechat_follow_result'
  }
};
// 线索跟进结果 - 注册
export const CLUE_FOLLOWUP_RESULT_REGISTER = {
  params: {
    type: 'syzg_todo_register_follow_result'
  }
};
// 线索跟进结果 - 促单
export const CLUE_FOLLOWUP_RESULT_GUIDE_PURCHASE = {
  params: {
    type: 'syzg_todo_guidepurchase_follow_result'
  }
};
// 线索跟进结果 - 返点
export const CLUE_FOLLOWUP_RESULT_CREDIT_BACK = {
  params: {
    type: 'syzg_todo_creditback_follow_result'
  }
};
// 线索跟进结果 - 授权
export const CLUE_FOLLOWUP_RESULT_LICENSE = {
  params: {
    type: 'syzg_todo_license_follow_result'
  }
};
// 线索跟进结果 - 政策
export const CLUE_FOLLOWUP_RESULT_POLICY = {
  params: {
    type: 'syzg_todo_policy_follow_result'
  }
};
// 线索跟进结果 - 节假日
export const CLUE_FOLLOWUP_RESULT_HOLIDAY = {
  params: {
    type: 'syzg_todo_holiday_follow_result'
  }
};
// 线索跟进结果 - 售后
export const CLUE_FOLLOWUP_RESULT_AFTER_SALE = {
  params: {
    type: 'syzg_todo_aftersale_follow_result'
  }
};
// 线索跟进结果 - 唤醒
export const CLUE_FOLLOWUP_RESULT_WAKE_UP = {
  params: {
    type: 'syzg_todo_wakeup_follow_result'
  }
};

// 拉单渠道
export const EXTERNAL_CHANNEL_CONFIG = {
  params: {
    type: 'external_channel_config'
  }
};
