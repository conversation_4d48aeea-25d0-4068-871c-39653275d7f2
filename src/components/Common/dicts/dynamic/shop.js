import { withExtTenantIdRequest } from '@/utils/request';

// 淘宝-店铺等级 淘宝一钻
export const SHOP_LEVEL = {
  params: ['platform_shop_level'],
  axiosRequest(params = {}) {
    return withExtTenantIdRequest({
      url: `/common/api/dict/listByTypeOrderBySort`,
      method: 'post',
      data: params
    }).then((res) => ({ data: res.data[params[0]] }));
  }
};

// 京东，拼多多 专营店
export const SHOP_TYPE = {
  params: {
    type: 'platform_shop_type'
  }
};

// 店铺平均客单
export const SHOP_AVERAGE_CUSTOMERORDER = {
  params: {
    type: 'soyoungzg_average_cs_price'
  }
};

// 店铺平均月销 - 一件代发
export const SHOP_AVERAGE_MONTHSALE = {
  params: {
    type: 'soyoungzg_monthly_sales_quantity_type'
  }
};

// 店铺平均月销 - 采销
export const SHOP_AVERAGE_MONTHSALE_AMOUNT = {
  params: {
    type: 'soyoungzg_monthly_sales_amount_type'
  }
};

// 了解渠道
export const COMMON_KNOW_CHANNEL = {
  params: {
    type: 'soyoungzg_know_channel'
  }
};
