import Dict from './Dict'
import DefaultOptions from './defaults'
const utils = require('./utils');

function createInstance(defaultConfig) {
  const context = new Dict(defaultConfig);
  const instance = utils.bind(Dict.prototype.request, context);

  // request上注入Dict.prototype 并绑定this为context
  utils.extend(instance, Dict.prototype, context);

  // Copy context to instance
  utils.extend(instance, context);

  // Factory for creating new instances
  instance.create = function create(instanceConfig) {
    return createInstance({
      ...defaultConfig,
      ...instanceConfig
    });
  };

  return instance;
}

const dict = createInstance(DefaultOptions);

dict.Dict = Dict;

export default dict;
