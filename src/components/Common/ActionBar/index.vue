<!-- 操作栏 -->
<template>
  <div class="bar">
    <template v-for="(i, index1) in barItems">
      <div :key="index1" class="bar-line">
        <template v-for="(item, index2) in i">
          <Authority :auth="item.authority" :key="index2" v-if="item.loadCondition ? item.loadCondition(record) : true">
            <slot :name="'actionBar_' + item.id" v-bind="item" :row="record" :class="barClass" v-if="item.isCustom"></slot>
            <router-link :to="item.path" v-else-if="item.path" :class="barClass">
              <button-hoc v-bind="getButtonProps(item.button)">{{ getLabel(item.label) }}</button-hoc>
            </router-link>
            <button-hoc v-else v-bind="getButtonProps(item.button)" @click="$u_throttle($emit('actionBarClick', item, record, index2))" :class="barClass">{{ getLabel(item.label) }}</button-hoc>
          </Authority>
        </template>
      </div>
    </template>
  </div>
</template>

<script>
export default {
  name: 'ActionBar',
  data() {
    return {};
  },
  props: {
    // 子项
    itemOptions: {
      type: Array,
      require: true
    },
    record: {
      type: Object,
      default() {
        return {};
      }
    }
  },
  computed: {
    barItems() {
      return this.itemOptions.reduce((pre, { isWrap, ...args }, index) => {
        if (index === 0 || isWrap) pre.push([]);

        pre[pre.length - 1].push(args);
        return pre;
      }, []);
    },
    isWrap() {
      return this.itemOptions.find((i) => i.isWrap);
    },
    barClass() {
      return `bar-line__${this.isWrap ? 'multi' : 'single'}`;
    }
  },
  methods: {
    getLabel(label) {
      return typeof label === 'function' ? label(this.record) : label;
    },
    getButtonProps(props) {
      return {
        type: 'primary',
        size: 'small',
        ...props
      };
    }
  }
};
</script>

<style lang="scss" scoped>
.bar {
  margin-bottom: 16px;
  &-line {
    display: flex;
    &__single {
      &:not(:first-child) {
        margin-left: 10px;
      }
    }
    &__multi {
      flex: 1;
      margin: 0;
      padding: 4px 0;
      text-align: left;
    }
  }
}
</style>
