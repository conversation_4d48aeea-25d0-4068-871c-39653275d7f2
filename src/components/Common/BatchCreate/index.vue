<template>
  <div class="batchCreate">
    <div style="position: absolute; z-index: 10; top: -20px">
      <el-button v-if="type === 'Table' && showDelete" type="primary" size="small" style="" @click="batchDelete">删除</el-button>
      <el-button v-if="deleteAllErorData" type="primary" size="small" style="" @click="deleteAllError()">{{ deleteAllErorData }}</el-button>
    </div>
    <el-table :data="tableData" ref="table" height="100%" @selection-change="handleSelectionChange" :row-style="{ height: rowFormHeight + 'px' }">
      <el-table-column type="selection" width="55" fixed="left" v-if="type === 'Table'"></el-table-column>
      <el-table-column label="提示" width="60" fixed="left" v-if="isTip && tipType === 'add'" align="center">
        <template slot-scope="scope">
          <slot name="tip" :scope="scope"></slot>
        </template>
      </el-table-column>
      <el-table-column label="提示" width="60" fixed="left" v-if="isTip && tipType === 'export'" align="center">
        <template slot-scope="scope">
          <el-tooltip class="item" effect="dark" :content="scope.row.duplicatedMsg || scope.row.errorMsg || '成功'" placement="top-end"> <span :class="scope.row.isSuccess === '0' ? 'el-icon-warning' : 'el-icon-success'"></span></el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="ID" type="index" fixed="left" align="center"> </el-table-column>
      <el-table-column label="操作" align="center" fixed="left" width="100" v-if="type === 'Table' && showOperation">
        <template slot-scope="scope">
          <el-button type="text" @click="deleteForm(scope)">删除</el-button>
          <br />
          <el-button type="text" @click="updateForm(scope)" v-if="isUpdate">{{ editFormStates.includes(scope.$index) ? '保存' : '编辑' }}</el-button>
        </template>
      </el-table-column>
      <el-table-column align="center" v-for="i in tableColumns" :key="i.key" :prop="i.key" :label="i.label" v-bind="i.elOptions" :width="parseInt(i.elOptions.width) + 6">
        <template slot="header" slot-scope="{}">
          <span class="batchCreate--isRequired" v-if="i.rule && i.rule.some((x) => x.required)">*</span>
          {{ i.label }}
          <el-tooltip class="item" effect="dark" :content="i.info" placement="top" v-if="i.info">
            <i class="el-icon-info"></i>
          </el-tooltip>
        </template>
      </el-table-column>
      <div slot="append">
        <template v-for="index in editFormStates">
          <div :key="index" :style="getFormColumnStyle(index)" class="batchCreate__form">
            <SubmitForm @hook:mounted="onSubmitFormMounted(index)" :formItems="formItems" :basicformData="formDatas[index]" @onFormItemChange="(args) => $emit('onFormItemChange', index, args)" ref="searchForm">
              <template><span></span></template>
            </SubmitForm>
          </div>
        </template>
      </div>
    </el-table>

    <!-- 做表单校验 -->
    <SubmitForm v-show="false" :formItems="formItems" ref="templateForm">
      <template><span></span></template>
    </SubmitForm>
  </div>
</template>

<script>
import SubmitForm from '@/components/Common/SubmitForm/index.vue';

export default {
  components: { SubmitForm },
  name: 'batchCreate',
  data() {
    return {
      formItems: [],
      formStates: [],
      formDatas: [], // 原始数据
      tableData: [], // 展示数据
      basicformData: {}, // 展示数据
      editFormStates: [], // 正在编辑的行的索引
      multipleSelection: [],
      rowFormHeight: 0
    };
  },
  props: {
    type: {
      type: 'Form' | 'Table',
      default: 'Form'
    },
    isTip: {
      type: Boolean,
      default: true
    },
    isUpdate: {
      type: Boolean,
      default: true
    },
    formRowLen: {
      type: Number,
      default: 20
    },
    tableColumns: {
      type: Array,
      default() {
        return [];
      }
    },
    deleteAllErorData: {
      type: String,
      default: ''
    },
    showDelete: {
      type: Boolean,
      default: true
    },
    showOperation: {
      type: Boolean,
      default: true
    },
    tipType: {
      type: String,
      default: 'add' // add: 添加 export：导入
    }
  },
  computed: {},
  created() {
    this.formItems = this.tableColumns.map((i) => ({
      ...i,
      label: void 0
    }));
  },
  mounted() {},
  methods: {
    init(tableBasicData) {
      this.tableData = [];
      this.editFormStates = [];

      if (this.type === 'Form') {
        this.tableData = tableBasicData;
        this.formDatas.push(...tableBasicData);
        this.$nextTick(() => {
          this.editFormStates = tableBasicData.map((i, index) => index);
        });
      } else {
        // 数据集合
        this.formDatas = tableBasicData.map((i) => {
          // 列表展示获取真实值
          this.tableData.push(this.getConvertTableData({ ...i }));
          return i;
        });
      }
    },
    onSubmitFormMounted(index) {
      this.$emit('onSubmitFormMounted', index);

      if (!this.rowFormHeight) {
        const ref = this.$refs.searchForm[0];
        this.rowFormHeight = ref && ref.$el.offsetHeight + 24; // td 上下padding 12px
      }
    },
    handleSelectionChange(val, ...args) {
      this.multipleSelection = val;
    },
    // 获取下拉选项对应label值
    getConvertTableData(formData) {
      // 主动触发表单change ,watcherRun
      this.$refs.templateForm.setBasicData(formData);

      this.$refs.templateForm.perfectFormItems.forEach(({ key, type, selectOptions }) => {
        if (type === 'select') {
          selectOptions.then((res) => {
            if (formData[key]) {
              const v = Array.isArray(formData[key]) ? formData[key] : [formData[key]];
              formData[key] = v
                .map((i) => {
                  const opt = res.find((x) => x.value === i);
                  return opt ? opt.label : '';
                })
                .join(',');
            }
          });
        }
      });

      return formData;
    },
    getFormColumnStyle(index) {
      const marginLeft =
        this.$refs.table.columns.slice(0, this.type === 'Table' ? 4 : 2).reduce((pre, cur) => {
          return (pre += cur.width || 0);
        }, 0) + 'px';
      return {
        marginLeft,
        height: this.rowFormHeight - 1 + 'px',
        top: this.rowFormHeight * index + 'px'
      };
    },
    // 通过隐藏的模板校验
    validateFormToTemplate({ index, formData, callback = () => {} }) {
      // 获取searchForm的位置
      const $index = this.editFormStates.indexOf(index);
      if ($index === -1) {
        callback(false);
        return;
      }

      const { formRules: fr, formData: fd } = this.$refs.searchForm[$index];
      const tempForm = this.$refs.templateForm;
      Object.assign(tempForm.formRules, fr);
      Object.assign(tempForm.formData, formData || fd);

      tempForm.$refs.form.validate((...args) => callback(formData || fd, ...args));
    },
    // Form模式时获取 searchForm的表单数据
    getSubmitForm(index) {
      return this.$refs.searchForm[index];
    },
    getFormData(index) {
      const i = this.editFormStates.indexOf(index);
      if (i !== -1) {
        return this.getSubmitForm(i).formData;
      } else {
        return this.formDatas[index];
      }
    },
    batchDelete() {
      if (this.mixins_requireChoose(this.multipleSelection)) {
        this.mixins_confirm({
          name: `删除`,
          resolve: () => {
            this.multipleSelection.forEach((i) => {
              let index = this.tableData.indexOf(i);

              this.tableData.splice(index, 1);
              index = this.editFormStates.indexOf(index);
              if (index !== -1) {
                this.editFormStates.splice(index, 1);
              }
            });

            this.multipleSelection = [];
            this.emitDelete();
            return Promise.resolve();
          }
        });
      }
    },
    // 向上触发删除事件
    emitDelete() {
      this.$emit('deleteRow');
    },
    // 删除行
    deleteForm({ $index }) {
      this.mixins_confirm({
        name: `删除`,
        resolve: () => {
          this.formDatas.splice($index, 1);
          this.tableData.splice($index, 1);
          const index = this.editFormStates.indexOf($index);
          if (index !== -1) {
            this.editFormStates.splice(index, 1);
          }
          this.emitDelete();
          return Promise.resolve();
        }
      });
    },
    // 编辑行
    updateForm({ $index }) {
      const index = this.editFormStates.indexOf($index);
      if (index !== -1) {
        this.mixins_confirm({
          name: `保存编辑内容`,
          resolve: () => {
            // 保存
            this.formDatas[$index] = this.$refs.searchForm[index].formData;

            // 获取真实值
            this.tableData.splice($index, 1, this.getConvertTableData({ ...this.formDatas[$index] }));
            this.editFormStates.splice(index, 1);
            return Promise.resolve();
          },
          reject: () => {
            // 取消
            this.$refs.searchForm[index].formData = this.formDatas[$index];
            this.$emit('onFormItemChange', $index, [this.formItems[$index]]);

            this.editFormStates.splice(index, 1);
          }
        });
      } else {
        // 开始编辑
        this.mixins_confirm({
          name: '开始编辑',
          resolve: () => {
            this.editFormStates.push($index);
            return Promise.resolve();
          }
        });
      }
    },
    // 删除所有错误数据
    deleteAllError() {
      this.tableData = this.tableData.filter((item) => item.isSuccess === '1');
    }
  }
};
</script>

<style lang="scss" scoped>
.batchCreate {
  position: relative;
  padding-top: 20px;

  &--isRequired {
    font-size: 14px;
    color: #de3509;
  }

  &__form {
    position: absolute;
    display: flex;
    align-items: center;
    background-color: #fff;
  }

  ::v-deep {
    .submitForm {
      margin: 0;
      .el-form-item {
        position: relative;
        padding: 0 3px;
        margin: 0;
        align-items: center;
      }
    }
  }
}

.el-icon-warning {
  color: var(--color-danger);
  font-size: 20px;
  vertical-align: middle;
}
.el-icon-success {
  color: var(--color-success);
  font-size: 20px;
  vertical-align: middle;
}
</style>
