<template>
  <el-dialog :title="title" :visible.sync="visible" @closed="onCancel">
    <el-form :model="form" :rules="rules" ref="form">
      <el-form-item :label="labelTitle+'：'" prop="data">
        <template>
          <upload-excel v-bind="importOptions" ref="uploadExcel" v-model="form.data" @input="($event) => onInput($event, 'data')"></upload-excel>
        </template>
        <template>
          <div class="tip">
            <span v-if="form.data && (form.data.successNum || form.data.failNum)">
              <strong>处理结果</strong> 成功<span class="success">{{ form.data.successNum }}</span>条，失败<span class="fail">{{
    form.data.failNum }}</span>条
            </span>
            <button-hoc style="padding: 0" type="text" @click="downLoadFailRecord"
              v-if="form.data && form.data.failNum">下载失败记录 <svg-icon icon-class="cloud-rain"></svg-icon></button-hoc>
          </div>
        </template>
      </el-form-item>
      <el-form-item style="text-align: right" class="form-footer">
        <el-button @click="onCancel">取消</el-button>
        <button-hoc type="primary" @click="onSubmit()">确定</button-hoc>
      </el-form-item>
    </el-form>
  </el-dialog>
</template>

<script>
import { publicExportExcel } from '@/api/common';
import download from '@/utils/download';
import UploadExcel from '@/components/UploadExcel';
export default {
  // 弹框公共导入
  name: 'import-dialog',
  components: { UploadExcel },
  inheritAttrs: false,
  data() {
    return {
      visible: false,
      form: {
        data: ''
      },
      rules: {
        data: [
          {
            validator: (rule, value = '', callback) => {
              const data = value;
              if (data) {
                if (data.code && data.code !== '0') {
                  callback(data.msg || '上传文件存在错误数据不能导入');
                } else {
                  callback();
                }
              } else {
                callback('请上传导入模板！');
              }
            },
            trigger: 'change'
          }
        ],
      }
    };
  },
  props: {
    createInBatch: {
      type: Function,
      require: true
    },
    downloadFailRequest: {
      type: String
    },
    title: {
      type: String,
      require: true
    },
    name: {
      type: String,
      require: true
    },
    labelTitle: {
      type: String,
      default: '批量导入等级分'
    }
  },
  computed: {
    importOptions() {
      return {
        name: this.name,
        ...this.$attrs
      };
    }
  },
  methods: {
    open() {
      this.visible = true;
      this.$nextTick(() => {
        this.$refs.form.resetFields();
        this.$refs.uploadExcel.setValue();
      })
    },
    // 表单数据更新
    onInput(val, prop) {
      this.$set(this.form, prop, val);
      this.$refs.form.validateField(prop);
    },
    // 下载模板
    downloadTemplate() {
      const type = typeof this.templateUrl;
      if (type === 'string') {
        window.location.href = this.templateUrl;
      } else if (type === 'function') {
        this.templateUrl().then((res) => {
          if (res instanceof ArrayBuffer) {
            download(res, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', `${this.name || this.title}.xlsx`);
          }
        });
      }
    },
    // 下载失败记录
    downLoadFailRecord() {
      publicExportExcel(this.downloadFailRequest, {
        busiSeq: this.form.data.busiSeq
      }).then((res) => {
        download(res, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', `${this.name || this.title}失败结果下载.xlsx`);
      });
    },
    onCancel() {
      this.visible = false;
    },
    onSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.createInBatch({
            busiSeq: this.form.data.busiSeq
          })
          .then(() => {
            this.onCancel();
            this.$emit('onSuccess');
          })
          .finally();
        } else {
          // 表单校验不通过，显示错误信息
          console.log('error submit!!');
          return false;
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.success {
  color: var(--color-success)
}

.fail {
  color: var(--color-danger);
}
</style>