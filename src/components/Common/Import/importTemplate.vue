<template>
  <div class="table-container import" v-loading="loading">
    <import-page
      :name="name"
      ref="importPage"
      :fileSizeLimit="fileSizeLimit"
      :fileRowNumLimit="fileRowNumLimit"
      :fileAcceptLimit="fileAcceptLimit"
      :uploadRequest="uploadRequest"
      :downloadFailRequest="downloadFailRequest"
      :templateUrl="templateUrl"
      @uploadFileSuccess="uploadFileSuccess"
      @onSubmit="onSubmit"
    >
      <template #left v-if="channelType">
        <div>
          当前选择渠道分类：<strong>{{ channelType === 'OFFLINE' ? '线下' : '线上' }}</strong>
        </div>
      </template>
      <div class="import-content">
        <div class="import-content--operate">
          <el-button type="primary" @click="batchDelete" v-if="isShowDelete">删除</el-button>
          <el-button type="primary" @click="batchReject">{{ deleteError }}</el-button>
        </div>
        <ScrollTable ref="scroller" :list="list" :item-size="60" :key-field="tableKeyField" @selection-change="handleSelectionChange">
          <scroll-table-column :type="selection" :width="50"></scroll-table-column>
          <scroll-table-column :width="80" label="提示" prop="isSuccess" sortable>
            <template slot-scope="scope">
              <el-tooltip class="item" effect="dark" :content="scope.row.errorMsg || '成功'" placement="top-end"> <span :class="scope.row.isSuccess === '0' ? 'el-icon-warning' : 'el-icon-success'"></span></el-tooltip>
            </template>
          </scroll-table-column>
          <scroll-table-column type="index" :width="50" label="序号"></scroll-table-column>

          <scroll-table-column v-for="i of tableOptions" :key="i.key" :prop="`${i.handle ? '_handle_' : ''}` + i.key" :label="i.label" v-bind="i.style">
            <template slot="header" slot-scope="{ column }">
              {{ column.label }}
              <span class="span-require" v-if="i.required">*</span>
            </template>
          </scroll-table-column>
        </ScrollTable>
      </div>
    </import-page>
  </div>
</template>

<script>
import importPage from './importPage.vue';
import ScrollTable from '@/components/Common/ListExhibition/scrollTable/ScrollTable';
import ScrollTableColumn from '@/components/Common/ListExhibition/scrollTable/scrollTableColumn';

export default {
  name: 'importTemplate',
  components: { importPage, ScrollTable, ScrollTableColumn },
  data() {
    return {
      loading: false,
      list: [],
      multipleSelection: []
    };
  },
  computed: {},
  mounted() {},
  props: {
    name: String,
    // 导入列表展示内容
    tableOptions: {
      type: Array,
      require: true
    },
    // 解析文档接口
    uploadRequest: {
      type: String,
      require: true
    },
    // 创建接口
    importRequest: {
      type: Function,
      require: true
    },
    // 校验接口
    validRequest: {
      type: Function,
      require: true
    },
    // 下载导入模板失败记录接口
    downloadFailRequest: {
      type: String,
      require: true
    },
    // 模板地址
    templateUrl: {
      type: String | Function,
      require: true
    },
    // 保存成功，返回路由地址
    backUrl: {
      type: String
    },
    // 列表唯一值
    tableKeyField: {
      type: String
    },
    fileSizeLimit: Number,
    fileRowNumLimit: Number,
    fileAcceptLimit: Array,
    channelType: {
      type: String,
      default: '' // ONLINE:线上 OFFLINE:线下
    },
    isShowDelete: {
      type: Boolean,
      default: true
    },
    deleteError: {
      type: String,
      default: '剔除'
    },
    selection: {
      type: String,
      default: 'selection'
    }
  },
  methods: {
    // 文件上传成功
    uploadFileSuccess(data) {
      this.handleData(data);
    },
    // 保存
    onSubmit(callback) {
      if (!this.list.length) {
        callback();
        return;
      }

      if (this.list.filter((i) => i.isSuccess === '0').length) {
        this.$message.warning('有错误信息，不能保存!');
        callback();
        return;
      }

      this.$confirm('是否确认保存', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          // 提交数据
          this.importRequest(this.list)
            .then((res) => {
              this.$message.success(res.msg);
              if (res.code === '0') this.cancel();
            })
            .catch(() => {})
            .finally(callback);
        })
        .catch(() => {
          this.$message.info('已取消操作!');
          callback();
        });
    },
    cancel() {
      this.backUrl && this.$back({ path: this.backUrl });
    },
    // 批量删除
    batchDelete() {
      if (!this.multipleSelection.length) return;

      this.validDelete(() => {
        this.multipleSelection.forEach((i) => {
          const index = this.list.indexOf(i);

          this.list.splice(index, 1);
        });
      });
    },
    // 删除所有错误数据
    batchReject() {
      this.validDelete(() => {
        this.list = this.list.filter((i) => i.isSuccess !== '0');
      });
    },
    validDelete(operate) {
      this.$confirm('确认删除, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          operate();
          this.$message.success('删除成功');
          if (!this.list.length) return;

          this.loading = true;
          this.validRequest(this.list).then((res) => {
            this.handleData(res.data.dataDetails);
            this.$message.success('校验完成');
          });
        })
        .catch(() => {
          this.$message.info('已取消操作!');
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    handleData(data = []) {
      this.list = data.map((i) => {
        this.tableOptions.forEach(({ key, handle }) => {
          // 数据需要二次处理
          if (typeof handle === 'function') {
            i[`_handle_${key}`] = handle(i[key]);
          }
        });
        return i;
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.import {
  height: 100%;

  &-content {
    display: flex;
    flex-direction: column;

    &--operate {
      flex: 0 0 34px;
      margin-bottom: 5px;
    }
    & > div:last-child {
      flex: 1;
      height: 0;
    }
  }
}

.el-icon-warning {
  color: var(--color-warning);
  font-size: 20px;
  vertical-align: middle;
}
.el-icon-success {
  color: var(--color-success);
  font-size: 20px;
  vertical-align: middle;
}

.span-require {
  color: var(--color-danger);
  font-size: 18px;
  position: absolute;
  transform: translateX(100%);
}
</style>
