<template>
  <el-dialog :title="title" :visible.sync="visible" @closed="$refs.submitForm.perfectForm(true)">
    <submit-form :model="form" :options="submitOptions" ref="submitForm" @onCancel="onCancel" @onSubmit="onSubmit" valid-fail-scrolled>
      <template slot="formItemError_data" slot-scope="{ error }">
        <div class="el-form-item__error">
          <span>{{ error }}</span>
          <button-hoc v-if="form.data && form.data[0] && form.data[0].failNum" style="padding: 0" type="text" @click="downLoadFailRecord">下载《{{ title }}失败记录》<svg-icon icon-class="cloud-rain"></svg-icon></button-hoc>
        </div>
      </template>
    </submit-form>
  </el-dialog>
</template>

<script>
import SubmitForm from '@/components/Form/SubmitFormV2';
import { publicExportExcel } from '@/api/common';
import download from '@/utils/download';

export default {
  // 弹框公共导入
  name: 'import-dialog',
  components: { SubmitForm },
  data() {
    return {
      visible: false,

      form: {}
    };
  },
  props: {
    title: {
      type: String,
      require: true
    },
    downloadFailRequest: {
      type: String
    },
    templateUrl: {
      type: String | Function,
      require: true
    },
    limitNum: {
      type: Number,
      default: 500
    },
    createInBatch: {
      type: Function,
      require: true
    }
  },
  computed: {
    submitOptions() {
      return [
        {
          prop: 'data',
          label: '上传导入模板',
          component: 'upload',
          // limit: 1,
          accept: '.xlsx',
          listType: 'button',
          _tip: `导入数量：建议在${this.limitNum}条以内`,
          uploadIconRender: () => {
            return (
              <div>
                <el-button size="small" type="primary">
                  上传
                </el-button>
                <el-button
                  type="text"
                  on-click={(evt) => {
                    evt.stopPropagation();
                    this.$u_throttle(this.downloadTemplate);
                  }}
                >
                  下载《{this.title}模板》<i class="el-icon-download"></i>
                </el-button>
              </div>
            );
          },
          change(val) {
            // 导入只保留一个
            if (Array.isArray(val) && val.length > 1) {
              val.slice(0, val.length - 1).forEach((i, index) => this.$refs.formItem_data[0].onRemove(index));
            }
          },
          rules: [
            {
              validator: (rule, value = [], callback) => {
                const data = value[0];
                if (data) {
                  callback(data.failNum ? '上传文件存在错误数据不能导入！' : void 0);
                } else {
                  callback('请上传导入模板！');
                }
              },
              trigger: 'change'
            }
          ],
          ...this.$attrs
        }
      ];
    }
  },
  methods: {
    open() {
      this.visible = true;
    },
    // 下载模板
    downloadTemplate() {
      const type = typeof this.templateUrl;
      if (type === 'string') {
        window.location.href = this.templateUrl;
      } else if (type === 'function') {
        this.templateUrl().then((res) => {
          if (res instanceof ArrayBuffer) {
            download(res, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', `导入模板.xlsx`);
          }
        });
      }
    },
    // 下载失败记录
    downLoadFailRecord() {
      publicExportExcel(this.downloadFailRequest, {
        busiSeq: this.form.data[0].busiSeq
      }).then((res) => {
        download(res, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', `${this.title}失败结果下载.xlsx`);
      });
    },
    onCancel() {
      this.visible = false;
    },
    onSubmit(cb) {
      this.createInBatch(this.form.data[0].dataDetails)
        .then(() => {
          this.onCancel();
          this.$emit('onSuccess');
        })
        .finally(cb);
    }
  }
};
</script>

<style>
</style>
