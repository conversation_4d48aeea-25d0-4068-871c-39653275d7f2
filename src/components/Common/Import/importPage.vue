<template>
  <div class="import">
    <!-- 步骤 -->
    <el-row :gutter="24" type="flex" align="middle" class="import-step" v-if="showInstructions">
      <el-col>
        <div>第一步</div>
        <div>下载模板</div>
      </el-col>
      <el-col>
        <svg-icon icon-class="arrow-right"></svg-icon>
      </el-col>
      <el-col>
        <div>第二步</div>
        <div>按照要求填写内容</div>
      </el-col>
      <el-col>
        <svg-icon icon-class="arrow-right"></svg-icon>
      </el-col>
      <el-col>
        <div>第三步</div>
        <div>上传文档，自动去重</div>
      </el-col>
      <el-col>
        <svg-icon icon-class="arrow-right"></svg-icon>
      </el-col>
      <el-col>
        <div>第四步</div>
        <div>确认导入</div>
      </el-col>
    </el-row>

    <!-- 导入信息 -->
    <div class="import-operate">
      <slot name="left"></slot>
      <div>批量导入{{ name }}：</div>
      <el-upload ref="upload" class="upload-demo" :headers="headers" :action="action" :show-file-list="showFileList" :accept="accept" :before-upload="beforeUploadFile" :on-change="handleChange" :on-success="handleSuccess" :on-error="handleError" :on-remove="handleRemove" :file-list="fileList">
        <el-button type="primary" :loading="uploadLoading" :disabled="isDisabledUpload">+上传文档</el-button>
      </el-upload>
      <div class="import-operate__template" @click="downloadTemplate" v-if="templateUrl">
        <span>下载《{{ name }}导入模板》<svg-icon icon-class="cloud-rain"></svg-icon></span>
      </div>
      <div class="import-operate__tip">文件只支持上传大小为：{{ fileSizeLimit }}M以内，条数{{ fileRowNumLimit }}条以内；文件格式支持：{{ fileAcceptLimit.join(',') }}格式</div>
    </div>
    <!-- 导入 成功/失败 信息 -->
    <div class="import-state" v-show="isPreview">
      <template v-if="source && source === 'salesData' && importDataFCount === 0 && saleNumLessThanZero > 0">
        <strong class="color-danger">本次导入有{{ abnormalDataCount }}个商品的销售数量小于0，需要备注原因确认导入的准确性</strong>
        <span class="import-state--download" @click="downLoadFailRecord">下载异常数据</span>
      </template>
      <template v-else>
        本次导入<span class="import-state--success">{{ importDataCount }}</span
        >条记录，成功<span class="import-state--success">{{ importDataSCount }}</span
        >条，失败<span class="import-state--fail">{{ importDataFCount }}</span
        >条<span class="import-state--download" v-if="downloadFailRequest && importDataFCount" @click="downLoadFailRecord">下载失败记录</span>
      </template>
    </div>
    <div class="import-preview" v-show="isPreview && showFooter">
      <slot></slot>
      <div class="import-preview__footer">
        <el-button @click="onSubmit" :loading="loading" type="primary">保存</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import download from '@/utils/download';
import { publicExportExcel } from '@/api/common';
import { getHeaders, handleError } from '@/utils/request';

export default {
  components: {},
  data() {
    return {
      fileList: [], // 上传文件列表
      file: null, // 上传的文件
      headers: getHeaders(), // 获取项目公共请求头
      loading: false,
      uploadLoading: false,
      exportLoading: false, // 导出缓冲
      uploadRes: {}, // 上传返回的结果
      importDataCount: 0, // 上传文件数量
      importDataSCount: 0, // 成功数量
      importDataFCount: 0, // 失败数量
      saleNumLessThanZero: 0,
      dataDetails: [], // 明细数据
      busiSeq: '', // 失败记录的唯一标识
      isPreview: false // 是否已经上传
    };
  },
  props: {
    showInstructions: {
      type: Boolean,
      default: true
    },
    // 来源
    source: String,
    // 上传名称
    name: String,
    // 上传文件大小限制
    fileSizeLimit: {
      type: Number,
      default: 5
    },
    // 上传文件条数数量限制
    fileRowNumLimit: {
      type: Number,
      default: 200
    },
    // 上传文件格式限制
    fileAcceptLimit: {
      type: Array,
      default() {
        return ['xlsx'];
      }
    },
    templateUrl: String | Function, // 模板地址
    // 上传请求地址
    uploadRequest: {
      type: String,
      require: true
    },
    isDisabledUpload: {
      type: Boolean,
      default: false
    },
    downloadFailRequest: String, // 下载导入失败记录请求地址
    // 是否显示底部的保存按钮
    showFooter: {
      type: Boolean,
      default: true
    },
    showFileList: Boolean // 是否显示上传的文件列表
  },
  computed: {
    action() {
      return process.env.VUE_APP_BASE_URL + this.uploadRequest;
    },
    accept() {
      return '.' + this.fileAcceptLimit.join(',.');
    },
    // 异常数据数量
    abnormalDataCount() {
      return this.dataDetails.filter((item) => item.saleNum < 0).length;
    }
  },
  methods: {
    handleChange(file, fileList) {
      if (fileList.length > 0) {
        this.fileList = [fileList[fileList.length - 1]]; // 这一步，是 展示最后一次选择的文件
      }
    },
    getFile() {
      return this.file;
    },
    // 获取上传结果
    getUploadRes() {
      return this.uploadRes;
    },
    // 下载模板
    downloadTemplate() {
      const type = typeof this.templateUrl;
      if (type === 'string') {
        window.location.href = this.templateUrl;
      } else if (type === 'function') {
        this.templateUrl().then((res) => {
          if (res instanceof ArrayBuffer) {
            download(res, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', `${this.name}导入模板.xlsx`);
          }
        });
      }
    },
    // 下载失败记录
    downLoadFailRecord() {
      if (this.exportLoading && this.busiSeq) return;
      this.exportLoading = true;

      publicExportExcel(this.downloadFailRequest, {
        busiSeq: this.busiSeq
      })
        .then((res) => {
          download(res, 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', `${this.name}导入失败结果下载.xlsx`);
        })
        .finally(() => {
          this.exportLoading = false;
        });
    },
    // 上传文件之前的钩子, 参数为上传的文件,若返回 false 或者返回 Promise 且被 reject，则停止上传
    beforeUploadFile(file) {
      this.uploadLoading = true;
      const size = file.size / 1024 / 1024;
      if (size > this.fileSizeLimit) {
        this.$message.warning(`文件大小不得超过${this.fileSizeLimit}M`);
        return false;
      }
      return true;
    },
    // 外部更新列表
    updateData(data) {
      const { totalNum, successNum, failNum, busiSeq } = data;
      this.busiSeq = busiSeq;
      this.importDataCount = totalNum;
      this.importDataSCount = successNum;
      this.importDataFCount = failNum;
    },
    // 文件上传成功时的钩子
    handleSuccess(res, file) {
      this.uploadLoading = false;

      if (res.code === '0') {
        this.$message.success(res.msg);
        const { totalNum, successNum, failNum, dataDetails, busiSeq, saleNumLessThanZero = 0 } = res.data;
        this.uploadRes = res.data;
        this.busiSeq = busiSeq;
        this.importDataCount = totalNum;
        this.importDataSCount = successNum;
        this.importDataFCount = failNum;
        this.saleNumLessThanZero = saleNumLessThanZero;
        this.isPreview = true;
        this.showFileList ? (this.file = file) : this.$refs.upload.clearFiles();
        this.dataDetails = dataDetails;
        this.$emit('uploadFileSuccess', dataDetails);
      } else {
        this.$message.error(res.msg || '文件导入失败');
        this.file = null;
        this.isPreview = false;
        this.$refs.upload.clearFiles();
      }
    },
    // 文件上传失败时的钩子
    handleError(err) {
      this.uploadLoading = false;
      handleError(err);
      this.$refs.upload.clearFiles();
      this.file = null;
    },
    // 从文件列表删除文件的处理
    handleRemove() {
      this.isPreview = false;
      this.file = null;
      this.$emit('onRemove');
    },
    // 保存
    onSubmit() {
      this.loading = true;
      this.$emit('onSubmit', () => {
        this.loading = false;
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.import {
  display: flex;
  flex-direction: column;
  &-step {
    text-align: center;
    & > div:nth-of-type(2n) {
      font-size: 48px;
    }
  }

  &-operate {
    margin-top: 40px;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    & > div {
      margin-right: 20px;
    }
    &__template {
      color: #5f3bce;
      cursor: pointer;
    }
    &__tip {
      color: var(--color-info);
      font-size: 12px;
    }
  }

  &-state {
    width: 100%;
    padding: 12px;
    background: #f0f2f6;
    color: #909399;
    margin: 20px 0;
    &--success {
      color: var(--color-success);
    }
    &--fail {
      color: var(--color-danger);
    }
    &--download {
      color: var(--color-primary);
      margin-left: 20px;
      cursor: pointer;
    }
  }

  &-preview {
    flex: 1;
    height: 0;
    display: flex;
    flex-direction: column;
    & > div:first-child {
      flex: 1;
      height: 0;
    }
    &__footer {
      flex: none;
      margin-top: 10px;
    }
  }
}
</style>
