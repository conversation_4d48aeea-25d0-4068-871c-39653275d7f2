<template>
  <div class="box">
    <div class="box-title">{{ title }}</div>
    <div class="box-content" v-loading="loading">
      <slot></slot>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    title: String,
    loading: <PERSON>olean
  }
};
</script>

<style lang="scss" scoped>
.box {
  background-color: #fff;
  box-shadow: 0 0 2px rgba(0, 0, 0, 0.2);
  &-title {
    font-weight: 500;
    border-bottom: 1px solid #e8e8e8;
    padding: 18px 30px;
    font-size: 16px;
    user-select: none;
  }
  &-content {
    padding: 18px 30px;
  }
}
</style>