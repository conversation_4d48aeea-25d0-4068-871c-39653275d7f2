<template>
  <div class="batchOperation">
    <template v-for="(i, index) in list">
      <div
        v-if="i.loadCondition ? loadCondition(i.loadCondition) : true"
        :key="index"
        class="batchOperation-list"
      >
        <Authority :auth="i.authority">
          <template v-if="i.routerLink">
            <router-link :to="i.routerLink">
              <el-button
                size="small"
                type="primary"
                :loading="button_loading[index]"
                @click="event_click(index)"
                :disabled="i.disabled"
                >{{ i.label }}</el-button
              >
            </router-link>
          </template>
          <template v-else>
            <template v-if="i.type === 'Text'">
              <span>{{ getTextLabel(i) }}</span>
            </template>
            <template v-else>
              <el-button
                size="small"
                type="primary"
                :loading="button_loading[index]"
                @click="event_click(index)"
                :disabled="i.disabled"
                >{{ i.label }}</el-button
              >
              <template v-if="i.child">
                <el-date-picker
                  v-if="i.child.type === 'DatePicker'"
                  :picker-options="i.child.pickerOptions"
                  @change="changeTime(i.child)"
                  class="selectTime"
                  type="datetime"
                  v-model="timeValue"
                  :ref="'datePicker_' + index"
                ></el-date-picker>
              </template>
            </template>
          </template>
        </Authority>
      </div>
    </template>
  </div>
</template>

<script>
import config from './config.js';

export default {
  data() {
    return {
      timeValue: '',
      button_loading: []
    };
  },
  props: {
    pageName: {
      type: String,
      default: ''
    },
    operateList: {
      type: Array
    }
  },
  inject: {
    rootComponent: {
      default: {} // 根组件对象
    }
  },
  computed: {
    list() {
      if (this.operateList) {
        return this.operateList;
      }
      const options = this.pageName
        .split('_')
        .reduce((cur, pre) => cur[pre], config);
      return options.list;
    }
  },
  methods: {
    loadCondition(loadCondition) {
      return loadCondition.apply(this);
    },
    event_click(index) {
      this.list[index].event_click &&
        this.list[index].event_click.apply(this, [index]);

      this.$emit('operateClick', this.list[index]);
    },
    changeTime(options) {
      options.event_click.apply(this);
    },
    getTextLabel(options) {
      return options.handle
        ? options.handle.apply(this, [options])
        : options.label;
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
@import './styles';
</style>