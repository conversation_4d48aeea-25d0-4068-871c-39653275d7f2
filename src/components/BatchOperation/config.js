export default {
  commodity: {
    // isAuthority
    // 商品管理
    manage: {
      list: [
        {
          authority: 'commodity-list-edit',
          event_click() {
            this.$parent.changeCategory();
          },
          label: '改分组',
          type: 'Button'
        },
        {
          authority: 'commodity-list-edit',
          event_click() {
            this.$parent.changeStatus('down');
          },
          loadCondition() {
            return this.$parent.status === '1' || this.$parent.status === '';
          },
          label: '下架',
          type: 'Button'
        },
        {
          authority: 'commodity-list-edit',
          event_click(index) {
            this.$parent.upOrDown('down', () => {
              this.timeValue = '';
              this.$refs['datePicker_' + index][0].focus();
            });
          },
          loadCondition() {
            return this.$parent.status === '1' || this.$parent.status === '';
          },
          label: '定时下架',
          type: 'Button',
          child: {
            event_click() {
              this.$parent.changeUpOrDownTime('down', this.timeValue);
            },
            type: 'DatePicker',
            pickerOptions: {
              disabledDate(date) {
                return date.getTime() + 86400000 < Date.now();
              }
            }
          }
        },
        {
          authority: 'commodity-list-edit',
          event_click() {
            this.$parent.changeStatus('wait');
          },
          loadCondition() {
            return this.$parent.status !== '0';
          },
          label: '待上架',
          type: 'Button'
        },
        {
          authority: 'commodity-list-edit',
          event_click() {
            this.$parent.changeStatus('up');
          },
          loadCondition() {
            return this.$parent.status !== '1';
          },
          label: '上架',
          type: 'Button'
        },
        {
          authority: 'commodity-list-edit',
          event_click(index) {
            this.$parent.upOrDown('up', () => {
              this.timeValue = '';
              this.$refs['datePicker_' + index][0].focus();
            });
          },
          loadCondition() {
            return this.$parent.status !== '1';
          },
          label: '定时上架',
          type: 'Button',
          child: {
            event_click() {
              this.$parent.changeUpOrDownTime('up', this.timeValue);
            },
            type: 'DatePicker',
            pickerOptions: {
              disabledDate(date) {
                return date.getTime() + 86400000 < Date.now();
              }
            }
          }
        },
        {
          authority: 'commodity-list-edit',
          event_click() {
            this.$parent.getRecovery();
          },
          loadCondition() {
            return this.$parent.status === '3';
          },
          label: '批量移到回收站',
          type: 'Button'
        },
        {
          authority: 'commodity-list-edit',
          event_click() {
            this.$parent.changeStatus('hide');
          },
          label: '隐藏',
          type: 'Button'
        },
        {
          authority: 'commodity-list-edit',
          event_click() {
            this.$parent.changeStatus('show');
          },
          label: '取消隐藏',
          type: 'Button'
        }
      ]
    }
  },
};
