<template>
  <el-date-picker
    :picker-options="options.pickerOptions"
    @change="changeTime()"
    class="selectTime"
    ref="downTime"
    type="datetime"
    v-model="timeValue"
  ></el-date-picker>
</template>

<script>
export default {
  data() {
    return {
      timeValue: ''
    }
  },
  props: ['options'],
  methods: {
    changeTime() {
      this.options.event_click()
    }
  }
};
</script>

<style>
</style>