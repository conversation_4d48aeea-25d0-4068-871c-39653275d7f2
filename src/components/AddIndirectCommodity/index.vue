<template>
  <div>
    <el-button type="text" @click="addCommodity">选择商品</el-button>
    <el-dialog :title="labelTitle || '选择商品'" :visible.sync="dialogFormVisible" :append-to-body="true" width="1200px" @close="handleDialogClose">
      <sy-normal-table v-bind="table" ref="table" />
    </el-dialog>
  </div>
</template>

<script>
import dict from '@/components/Common/dicts';
import { productCategoryListByQuery } from '@/api/brand/commodity-set';
import dataDeal from '@/utils/dataDeal';
import { listAllBrandName } from '@/api/brand/brand-info';
import { creditActivityListZgCommodity } from '@/api/activity/integral';

export default {
  name: 'AddIndirectCommodity',
  model: {
    prop: 'commodityList',
    event: 'updateTable'
  },
  props: {
    // 活动来源
    source: {
      type: String,
      default: 'integral'
    },
    // 积分活动id
    activityId: String,
    // 已选择商品
    commodityList: Array,
    // 按钮及弹窗标题文案
    labelTitle: String,
    // 默认多选
    multiple: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      productCategoryOptions: [],
      dialogFormVisible: false
    };
  },
  computed: {
    table() {
      const that = this;
      return {
        filters() {
          return [
            {
              tag: 'sy-select',
              prop: 'brandIds',
              label: '品牌',
              bind: {
                placeholder: '请选择品牌',
                filterable: true,
                options: async () => {
                  const res = await listAllBrandName();
                  return res.data.map((item) => ({
                    value: item.id,
                    label: item.name
                  }));
                }
              }
            },
            {
              tag: 'el-input',
              prop: 'commodityCodes',
              label: '商品编码',
              str2Arr: true,
              bind: {
                placeholder: '批量查询使用英文逗号分隔'
              }
            },
            {
              tag: 'el-input',
              prop: 'barcodes',
              label: '商品条码',
              str2Arr: true,
              bind: {
                placeholder: '批量查询使用英文逗号分隔'
              }
            },
            {
              tag: 'el-input',
              prop: 'commodityName',
              label: '商品名称',
              bind: {
                placeholder: '请输入商品名称'
              }
            },
            {
              tag: 'el-input',
              prop: 'productName',
              label: 'SPU名称',
              bind: {
                placeholder: '请输入SPU名称'
              }
            },
            {
              tag: 'el-cascader',
              prop: 'productCategoryCodes',
              label: '产品品类',
              bind: {
                collapseTags: true,
                showAllLevels: false,
                options: that.productCategoryOptions,
                props: {
                  value: 'id',
                  label: 'name',
                  multiple: true,
                  expandTrigger: 'hover'
                }
              }
            },
            {
              tag: 'sy-select',
              prop: 'productPosition',
              label: '产品矩阵',
              bind: {
                placeholder: '请选择',
                filterable: true,
                options: async () => await dict('PRODUCT_POSITION')
              }
            },
            {
              tag: 'sy-select',
              prop: 'commodityType',
              label: '贸易类型',
              bind: {
                placeholder: '请选择',
                filterable: true,
                options: async () => await dict('COMMODITY_TRADE_TYPE')
              }
            }
          ];
        },
        btns() {
          return [
            {
              text: '批量添加',
              type: 'primary',
              bind: {
                disabled: false
              },
              async call({ selection }) {
                that.handleJoinALl(selection);
              }
            }
          ];
        },
        columns() {
          return [
            {
              type: 'selection'
            },
            {
              label: '商品名称',
              prop: 'commodityName',
              width: 200,
              multiLine: 3
            },
            {
              label: '商品规格',
              prop: 'spec'
            },
            {
              label: '商品条码',
              prop: 'barcode'
            },
            {
              label: '商品编码',
              prop: 'commodityCode'
            },
            {
              label: '产品品类',
              prop: 'productCategoryName'
            },
            {
              label: 'SPU名称',
              prop: 'productName'
            },
            {
              label: '产品矩阵',
              prop: 'productPositionName'
            },
            {
              label: '操作',
              type: 'btns',
              btns({ row }) {
                return [
                  {
                    hide: !that.commoditySet.has(row.id),
                    text: '已添加',
                    type: 'text',
                    bind: {
                      disabled: true
                    }
                  },
                  {
                    hide: that.commoditySet.has(row.id),
                    text: '添加',
                    type: 'text',
                    call() {
                      that.handleJoin(row);
                    }
                  }
                ];
              }
            }
          ];
        },
        async search({ filtersValue, pageFilter }) {
          const productCategoryCodes = (filtersValue?.productCategoryCodes ?? []).flatMap(subArray => subArray.slice(-1));
          const res = await creditActivityListZgCommodity({
            ...pageFilter,
            data: { 
              ...filtersValue, 
              activityId: that.activityId,
              productCategoryCodes 
            }
          });
          const { list = [], total = 0 } = res?.data || {};
          return {
            list,
            total
          };
        }
      };
    },
    commoditySet() {
      return new Set(this.commodityList.map(({ id }) => id));
    }
  },
  mounted() {
    this.getProductCategory();
  },
  methods: {
    // 刷新
    refreshList() {
      const ref = this.$refs.table;
      ref && ref.handlerSearch();
    },
    // 选择商品
    addCommodity() {
      // 如果是积分商品
      if (this.source === 'integral' && !this.activityId) {
        this.$message.error('请先选择积分活动');
        return;
      }
      this.refreshList();
      this.dialogFormVisible = true;
    },
    // 获取产品分类
    getProductCategory() {
      productCategoryListByQuery().then((res) => {
        this.productCategoryOptions = dataDeal.list2Tree(res.data, 'id', 'parentId', 'children', '0');
      });
    },
    // 弹窗关闭
    handleDialogClose() {},
    // 单个添加
    handleJoin(rowData) {
      let list = [...this.commodityList];
      const pushData = { ...rowData };
      if (!this.multiple) {
        this.dialogFormVisible = false;
        list = [pushData];
      } else {
        list.push(pushData);
      }
      this.$emit('updateTable', list);
    },
    // 批量添加
    handleJoinALl(selection) {
      if (!selection.length) {
        this.$message({
          message: '添加商品不能为空',
          type: 'warning'
        });
        return;
      }
      const listChecked = selection.filter(item => !this.commoditySet.has(item.id));
      const list = [...this.commodityList, ...listChecked];
      this.$emit('updateTable', list);
      this.dialogFormVisible = false;
    }
  }
};
</script>

<style lang="scss" scoped></style>
