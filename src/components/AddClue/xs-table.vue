<!-- 线索表格 -->
<template>
  <div>
    <header>
      <div class="commo-asterisk article-box--title">
        <div v-if="list.length">
          <!-- 这里是线索的编辑权限 -->
          <Authority auth="/customer-management/clue-management/:edit">
            <el-button @click="edit(clueData)" :disabled="!editable" type="text">编辑</el-button>
          </Authority>
          <el-button v-if="isRemove" @click="remove(clueData)" :disabled="!editable" type="text">移除</el-button>
        </div>
        <div class="box" v-show="!multipleSelection.length">
          <span class="tips">暂未匹配线索，需要手动关联线索</span>
          <el-button @click="chooseItem" v-if="editable" type="text" class="add-item">立即绑定</el-button>
        </div>
      </div>
    </header>
    <main>
      <div v-if="list.length">
        <el-descriptions :column="6" label-class-name="descriptions-label-name" content-class-name="descriptions-content-name">
          <el-descriptions-item v-if="isOnline" label="所属团队">{{ clueData.csGroupName | defaultTxt }}</el-descriptions-item>
          <el-descriptions-item label="拓展渠道">{{ clueData.developChannelName | defaultTxt }}</el-descriptions-item>
          <template v-if="isOnline">
            <el-descriptions-item label="拓展顾问">{{ clueData.developCsName | defaultTxt }}</el-descriptions-item>
            <el-descriptions-item label="拓展关键词">{{ clueData.keyWord | defaultTxt }}</el-descriptions-item>
            <el-descriptions-item label="拓展品牌">{{ clueData.favoriteBrandNames | defaultTxt }}</el-descriptions-item>
            <el-descriptions-item label="添加人">{{ clueData.createByName | defaultTxt }}</el-descriptions-item>
            <el-descriptions-item label="添加时间">{{ parseTime(clueData.createDate) | defaultTxt }}</el-descriptions-item>
          </template>
          <el-descriptions-item :label="isOffline ? '客户名称' : '店铺名称'">{{ clueData.platformShopName | defaultTxt }}</el-descriptions-item>
          <el-descriptions-item v-if="isOffline" label="客户业务类型">{{ clueData.businessTypeAndExtendName | defaultTxt }}</el-descriptions-item>
          <el-descriptions-item v-if="isOnline" label="店铺ID"> {{ clueData.platformShopId | defaultTxt }}</el-descriptions-item>
          <el-descriptions-item v-if="isOnline" label="行业评级">{{ clueData.industryLevel | defaultTxt }}</el-descriptions-item>
          <el-descriptions-item label="主营类目">{{ clueData.businessCategoryNames | defaultTxt }}</el-descriptions-item>
          <el-descriptions-item v-if="isOnline" label="店铺等级/类型">{{ clueData.platformShopLevelName | defaultTxt }}</el-descriptions-item>
          <el-descriptions-item v-if="isOnline" label="店铺链接">{{ clueData.platformShopLink | defaultTxt }}</el-descriptions-item>
          <el-descriptions-item v-if="isOnline" label="推广属性">{{ clueData.promoteChannelNames | defaultTxt }}</el-descriptions-item>
          <el-descriptions-item label="偏好品牌">{{ clueData.interestedBrandNames | defaultTxt }}</el-descriptions-item>
          <el-descriptions-item label="联系人">{{ clueData.contactName | defaultTxt }}</el-descriptions-item>
          <el-descriptions-item label="联系人岗位">{{ clueData.contactPost | defaultTxt }}</el-descriptions-item>
          <el-descriptions-item label="联系人手机号">{{ clueData.mobile | defaultTxt }}</el-descriptions-item>
          <el-descriptions-item label="微信ID">{{ clueData.wechat | defaultTxt }}</el-descriptions-item>
          <el-descriptions-item label="店铺平均客单价">{{ clueData.averageCustomerPriceName | defaultTxt }}</el-descriptions-item>
          <el-descriptions-item v-if="isOnline" label="店铺月均销量">{{ clueData.monthlySalesName | defaultTxt }}</el-descriptions-item>
          <el-descriptions-item v-if="isOffline" label="店铺数量">{{ clueData.shopQuantity | defaultTxt }}</el-descriptions-item>
          <el-descriptions-item v-if="isOffline" label="店铺位置">{{ clueData.shopAddress | defaultTxt }}</el-descriptions-item>
          <template v-if="isOnline">
            <el-descriptions-item label="店铺平均月销">{{ clueData.monthlySalesName | defaultTxt }}</el-descriptions-item>
            <el-descriptions-item label="企业名称">{{ clueData.company | defaultTxt }}</el-descriptions-item>
            <el-descriptions-item label="企业法人">{{ clueData.companyLegalName | defaultTxt }}</el-descriptions-item>
            <el-descriptions-item label="企业联系方式">{{ clueData.companyLegalMobile | defaultTxt }}</el-descriptions-item>
            <el-descriptions-item label="企业规模">{{ clueData.companyScaleName | defaultTxt }}</el-descriptions-item>
          </template>
          <el-descriptions-item :label="isOffline ? '客户描述' : '店铺描述'">{{ clueData.description | defaultTxt }}</el-descriptions-item>
        </el-descriptions>
      </div>
      <div v-else class="no-list">暂无线索</div>
    </main>
    <!-- 操作渠道线索弹框 -->
    <el-dialog :visible.sync="channelClueFormDialogVisible" title="编辑渠道线索" width="75%" @opened="$refs.channelClueForm.init()" @closed="$refs.channelClueForm.clear()">
      <channel-clue-form
        :groupId="groupId"
        :id="clueData.rowId"
        :status="clueData.status"
        :distributorExtId="clueData.id"
        :customerChannelKind="clueData.customerChannelKind"
        ref="channelClueForm"
        @onSubmit="channelClueFormOnSubmit"
        @onCancel="channelClueFormDialogVisible = false"
      ></channel-clue-form>
    </el-dialog>
  </div>
</template>

<script>
import ChannelClueForm from '@/views/customer-management/customer-clue-management/component/channelClueForm';
import { distributorLeadsManagerListAll } from '@/api/distributorManagement/distributor/add';
import { parseTime } from '@/utils';

export default {
  data() {
    return {
      dialogSubmitFormVisible: false,
      listLoading: false,
      list: [],
      ids: [],

      groupId: '',
      channelClueFormDialogVisible: false
    };
  },
  props: {
    multipleSelection: Array,
    userGroup: String,
    editable: Boolean,
    isRemove: Boolean
  },
  filters: {
    defaultTxt(a) {
      if (!a && a !== 0) return '--';
      if (Array.isArray(a)) {
        return a.join('、');
      }
      return a;
    }
  },
  components: { ChannelClueForm },
  watch: {},
  computed: {
    clueData() {
      // 国际线索展示只需要去第一条
      const [clue = {}] = this.list;
      return clue;
    },
    // 判断线索渠道归属是否为线下
    isOffline() {
      const { customerChannelKind = 'ONLINE' } = this.clueData;
      return customerChannelKind === 'OFFLINE';
    },
    // 判断线索渠道归属是否为线上
    isOnline() {
      const { customerChannelKind = 'ONLINE' } = this.clueData;
      return customerChannelKind === 'ONLINE';
    }
  },

  created() {},

  mounted() {},

  methods: {
    parseTime,
    chooseItem() {
      this.$emit('chooseItem');
    },
    // 列表初始化
    init(ids = this.ids) {
      if (!ids) return;
      const qu = {
        ids: typeof ids === 'string' ? [ids] : ids
      };
      this.ids = ids;
      distributorLeadsManagerListAll(qu).then((res) => {
        const list = res.data;
        this.setData(list);
      });
    },
    // 输出数据
    getData() {
      this.$emit('updateTable', this.list);
    },
    // 设置数据
    setData(list){
      this.list = this.setConversionList(list);
      this.getData();
    },
    // 清除数据
    removeData() {
      this.list = [];
      this.$emit('updateTable', []);
    },
    remove(row) {
      this.$confirm(`确定移除该条信息`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          const list = [...this.list];
          this.list = list.filter(({ id }) => id !== row.id);
          this.getData();
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消'
          });
        });
    },
    // 列表合并转换
    setConversionList(list) {
      return list.reduce((pre, cur) => {
        const { id } = cur;
        const channelClues = cur.distributorLeadsChannelVOList || [cur];
        pre.push(
          ...channelClues.map((i, index) => ({
            ...cur,
            ...i,
            _isMerge: !index,
            _mergeNum: channelClues.length,
            id,
            mobile: cur.mobile,
            developCsName: i.developCsName,
            platformShopId: cur.platformShopId,
            rowId: i.id
          }))
        );
        return pre;
      }, []);
    },
    edit({ csGroupId }) {
      this.groupId = csGroupId || this.userGroup;
      this.channelClueFormDialogVisible = true;
    },
    // 渠道线索表单提交回调
    channelClueFormOnSubmit() {
      this.init();
      this.$emit('channelClueFormOnSubmit');
      this.channelClueFormDialogVisible = false;
    }
  }
};
</script>

<style lang="scss">
.el-tooltip__popper {
  max-width: 400px;
}
.descriptions-label-name {
  color: #0d1b3f;
  font-weight: bold;
  font-size: 12px;
}
.descriptions-content-name {
  font-size: 12px;
  color: #3e4965;
}
</style>
<style lang="scss" scoped>
@import 'src/styles/goods-table.scss';
.el-form-box {
  .el-form-item {
    display: flex;
    ::v-deep .el-form-item__content {
      margin-left: 0 !important;
      flex: 1;
    }
    ::v-deep {
      input,
      .el-select,
      .el-input {
        width: 100%;
      }
    }
  }
}
.refund-list th {
  width: auto;
  padding: 0 10px;
  min-width: 80px;
}
::v-deep {
  .submitForm——form .el-form-item {
    margin-right: 0 !important;
  }
}
.no-list {
  min-height: 60px;
  text-align: center;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #3e4965;
  border: 1px solid #f4f4f4;
}
.article-box--title {
  display: flex;
  align-items: center;
  .tx {
    margin-right: 16px;
    line-height: 1.6;
    font-weight: bold;
    font-size: 16px;
  }
}
.textarea-txt {
  font-size: 12px;
  width: 300px;
}
::v-deep .el-textarea .el-textarea__inner {
  resize: none;
}
.add-item {
  margin-left: 10px;
  font-weight: bold;
}
</style>
