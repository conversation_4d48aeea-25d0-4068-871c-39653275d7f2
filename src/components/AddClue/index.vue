<template>
  <div class="add-clue-container">
    <xsTable @channelClueFormOnSubmit="$emit('channelClueFormOnSubmit')" @chooseItem="chooseItem" :multipleSelection="multipleSelection" :userGroup="userGroup" :editable="editable" :isRemove="isRemove" @updateTable="updateTable" ref="xsTable"></xsTable>

    <el-dialog :close-on-click-modal="false" :visible.sync="dialogFormVisible" width="1200px">
      <template #title>
        <span class="el-dialog__title">关联线索</span>
        <span class="el-dialog__title__tip">只能搜索到未绑定分销商的线索 <i class="el-icon-warning-outline"></i></span>
      </template>
      <div class="top-info">
        <el-input size="small" class="input-with-select" placeholder="联系方式" v-model.trim="contactPhone" clearable></el-input>
        <el-input size="small" class="input-with-select" placeholder="店铺名称" v-model.trim="platformShopName" clearable></el-input>
        <el-input size="small" class="input-with-select" placeholder="店铺ID" v-model.trim="platformShopId" clearable></el-input>
        <el-select placeholder="线索渠道归属" v-model="innerCustomerChannelKind" :disabled="isCustomerChannelKindDisabled" class="input-with-select">
          <el-option v-for="item in customerChannelKindOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
        <el-date-picker size="small" v-model="createDate" type="datetimerange" align="right" start-placeholder="拓展开始日期" end-placeholder="拓展结束日期" :default-time="['00:00:00', '23:59:59']" clearable></el-date-picker>
        <el-date-picker size="small" v-model="lastFollowDate" type="datetimerange" align="right" start-placeholder="最近跟进开始日期" end-placeholder="最近跟进结束日期" :default-time="['00:00:00', '23:59:59']" clearable></el-date-picker>
        <el-button @click="onSearch" type="primary" size="small">查询</el-button>
        <el-button @click="onReset" size="small">重置</el-button>
      </div>
      <el-table :data="list" element-loading-text="加载中" fit highlight-current-row v-loading="listLoading" max-height="400px" :span-method="clueSpanMethod">
        <el-table-column label="所属团队" prop="csGroupName"></el-table-column>
        <el-table-column label="线索渠道归属" prop="customerChannelKindName" width="100"></el-table-column>
        <el-table-column label="拓展渠道" prop="developChannelName"></el-table-column>
        <el-table-column label="店铺/客户名称" prop="platformShopName" width="120"></el-table-column>
        <el-table-column label="店铺ID" prop="platformShopId" width="120">
          <template slot-scope="scope">
            {{ scope.row.platformShopId || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="是否已注册" prop="statusName" width="100"></el-table-column>
        <el-table-column label="联系方式" prop="mobile" width="120">
          <template slot-scope="scope">
            {{ scope.row.mobile || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="拓展顾问" prop="developCsName"></el-table-column>
        <el-table-column label="店铺描述" prop="description">
          <template slot-scope="scope">
            {{ scope.row.description || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="偏好品牌" prop="interestedBrandNames" width="120" align="center">
          <template slot-scope="scope">
            <div v-if="scope.row.interestedBrandNames && scope.row.interestedBrandNames.length">
              <CommoEllipsis :key="getUniqueId('CommoEllipsis_')" :isShowView="false" :lineClamp="2" :contentWidth="100" :text="scope.row.interestedBrandNames.join('、')" />
            </div>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="店铺平均客单价" prop="averageCustomerPriceName" width="120">
          <template slot-scope="scope">
            {{ scope.row.averageCustomerPriceName || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="添加时间" prop="createDate" width="120">
          <template slot-scope="scope">
            {{ scope.row.createDate | parseTime('{y}-{m}-{d}') }}
          </template>
        </el-table-column>
        <el-table-column label="添加人" prop="createByName"> </el-table-column>
        <el-table-column label="操作" fixed="right" prop="operations">
          <template slot-scope="operation">
            <span v-if="dataSet.has(operation.row.id)">已选中</span>
            <el-button v-else @click="handleJoin(operation.row)" type="text">添加</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination :current-page="pageNo" :disabled="listLoading" :page-size="pageSize" :page-sizes="[5, 10, 20, 30]" :total="total" @current-change="handleCurrentChange" @size-change="handleSizeChange" background layout="total, sizes, prev, pager, next, jumper"></el-pagination>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getClueList } from '@/api/customer-management/clue';
import getRangeDate from '@/utils/range-date';
import { parseTime } from '@/utils';
import { updateChannelClue } from '@/api/customer-management/clue.js';
import xsTable from './xs-table.vue';
import CommoEllipsis from '@/components/CommoEllipsis';
import uniqueId from 'lodash/uniqueId';

export default {
  name: 'add-clue',
  components: { xsTable, CommoEllipsis },
  props: {
    // 默认的团队线索IDS
    distributorDevelopExtInfoVOListIds: {
      type: Array,
      default: function () {
        return [];
      }
    },
    // 新增的时候输入的店铺id
    p_platformShopId: {
      type: String
    },
    // 非必传 是否可以编辑，默认可编辑
    editable: {
      type: Boolean,
      default: true
    },
    // 单选传 false， 默认多选
    multiple: {
      type: Boolean,
      default: true
    },
    // 是否可以移除
    isRemove: {
      type: Boolean,
      default: true
    },
    userGroup: String, // 团队ID ‘1’直供  ‘2’ 电商  ‘3’国际   团队判断中 1、2 （直供和电商）是一样的一类，3（国际）是一类，还有除了1、2、3之外的其他 （可以全部视为平台）
    customerChannelKind: String // 客户渠道分类
  },
  computed: {
    customerChannelKindOptions() {
      return window.$vue.$dict['soyoungzg_channel_type'];
    },
    dataSet() {
      return new Set(this.multipleSelection.map((i) => i.id));
    },
    // 国际团队
    isGjGroup() {
      return this.userGroup === '3';
    },
    // 其他团队   平台和技术可以查看所有的字段；财务可以查看所有字段；客服可以查看所有字段；
    isOther() {
      return !['1', '2', '3', '10'].includes(this.userGroup);
    },
    isCustomerChannelKindDisabled() {
      return this.customerChannelKind === 'ONLINE';
    }
  },
  data() {
    return {
      contactPhone: '', // 联系方式
      platformShopName: '',
      platformShopId: '',
      company: '',
      createDate: [],
      lastFollowDate: [],
      listLoading: false,
      list: [],
      pageNo: 1,
      pageSize: 5,
      total: 0,
      dialogFormVisible: false,
      channelClueFormItems: [],
      dialogLoading: false,
      dialogSubmitFormData: {},
      dialogSubmitFormVisible: false,
      multipleSelection: [], // 选择的线索列表行数据
      innerCustomerChannelKind: this.customerChannelKind
    };
  },
  methods: {
    setIds(ids) {
      if (ids && ids.length > 0) {
        this.$refs.xsTable.init(ids);
      }
    },
    // 更新数据
    updateTable(list) {
      this.multipleSelection = list;
      this.$emit('updateTable', list);
    },
    parseTime,
    // 获取唯一的id
    getUniqueId(string) {
      return uniqueId(string);
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.pageNo = 1;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.pageNo = val;
      this.fetchData();
    },
    onSearch() {
      this.pageNo = 1;
      this.pageSize = 5;
      this.fetchData();
    },
    onReset() {
      this.clearForm();
      this.fetchData();
    },
    clearForm() {
      this.pageNo = 1;
      this.pageSize = 5;
      this.platformShopName = '';
      this.platformShopId = '';
      this.contactPhone = '';

      this.company = '';
      this.createDate = [];
      this.lastFollowDate = [];
    },
    // 添加
    handleJoin(rowData) {
      this.dialogFormVisible = false;
      this.$refs.xsTable.init(rowData.id);
    },
    // 立即绑定
    chooseItem() {
      if (!this.customerChannelKind) {
        return this.$message.error('请选择客户渠道分类');
      }
      this.clearForm();
      this.dialogFormVisible = true;
      this.innerCustomerChannelKind = this.customerChannelKind;
      this.p_platformShopId && (this.platformShopId = this.p_platformShopId);
      this.fetchData();
    },
    // element 合并列表列规则
    clueSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex < 3 || column.property === 'operations') {
        if (row._isMerge) {
          return {
            rowspan: row._mergeNum,
            colspan: 1
          };
        } else {
          return {
            rowspan: 0,
            colspan: 0
          };
        }
      }
    },
    fetchData() {
      this.listLoading = true;
      const { pageNo, pageSize, platformShopName, platformShopId, company, createDate, lastFollowDate, contactPhone, innerCustomerChannelKind } = this;
      const data = {
        customerChannelKind: innerCustomerChannelKind,
        platformShopName,
        platformShopId,
        company,
        contactPhone,
        status: 'NOT_REGISTER',
        ...getRangeDate(createDate, 'createDateStart', 'createDateEnd'),
        ...getRangeDate(lastFollowDate, 'lastFollowDateStart', 'lastFollowDateEnd')
      };
      if (!this.isOther) {
        data.groupIds = this.userGroup;
      }
      const listQuery = {
        pageNo,
        pageSize,
        data
      };
      getClueList(listQuery)
        .then((response) => {
          const { list, total } = response.data;
          this.list = this.setConversionList(list);
          this.total = total;
        })
        .finally(() => {
          this.listLoading = false;
        });
    },
    // 列表合并转换
    setConversionList(list) {
      return list.reduce((pre, cur) => {
        const { id } = cur;
        const channelClues = cur.distributorLeadsChannelVOList || [cur];
        pre.push(
          ...channelClues.map((i, index) => ({
            ...cur,
            ...i,
            _isMerge: !index,
            _mergeNum: channelClues.length,
            mobile: cur.mobile,
            developCsName: i.developCsName,
            platformShopId: cur.platformShopId,
            id
          }))
        );
        return pre;
      }, []);
    },
    // 弹框-> 操作渠道线索
    onSubmit_dialog(formData) {
      let axiosInterface;
      if (this.dialogSubmitFormData.id) {
        // 编辑渠道线索
        formData.id = this.dialogSubmitFormData.id;
        axiosInterface = updateChannelClue;
      }
      formData.distributorExtId = this.clueDetail.distributorId;
      // 请求接口
      this.dialogLoading = true;
      axiosInterface(formData)
        .then((res) => {
          this.fetchData();
          this.$message.success(res.msg);
        })
        .finally(() => {
          this.dialogLoading = false;
        });
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
@import '../AddCommodity/styles';
.box {
  margin: 10px 0;
}
.tips {
  color: var(--color-info);
}
.add-item {
  padding: 10px;
  color: var(--color-primary);
  cursor: pointer;
}
.input-with-select {
  margin-left: 0;
}
.el-range-editor--small.el-input__inner {
  width: 240px;
}
.top-info {
  margin-bottom: 16px;
  ::v-deep {
    .el-input,
    .el-range-editor--small.el-input__inner {
      width: 240px;
      margin-right: 16px;
    }
  }

  .el-button--primary {
    margin: 10px 0;
  }
  .link {
    color: var(--color-primary);
    cursor: pointer;
  }
}
.el-dialog__title__tip {
  color: var(--color-danger);
  display: inline-block;
  margin-left: 6px;
  font-size: 12px;
}
</style>
