<template>
  <el-breadcrumb class="app-breadcrumb" separator="/">
    <transition-group name="breadcrumb">
      <el-breadcrumb-item
        v-for="(item, index) in filterLevelList"
        :key="item.path"
      >
        <span
          v-if="
            item.redirect === 'noredirect' ||
            index == filterLevelList.length - 1
          "
          class="no-redirect"
          >{{ item.meta.title }}</span
        >
        <router-link v-else :to="item.redirect || item.path">{{
          item.meta.title
        }}</router-link>
      </el-breadcrumb-item>
    </transition-group>
  </el-breadcrumb>
</template>

<script>
import { mapGetters } from 'vuex';
import {
  setItem
  // ,
  // getItem, removeItem
} from '@/utils/sessionStorage';
export default {
  created() {
    this.getBreadcrumb();
  },
  data() {
    return {
      levelList: null
    };
  },
  watch: {
    $route(route) {
      if (route.params.id && route.params.id !== ':id') {
        // 保存id用于返回上级页面
        setItem('higher', route.params.id);
      }
      this.getBreadcrumb();
    }
  },
  computed: {
    filterLevelList() {
      return (this.levelList || []).filter(
        (item) => item.meta && item.meta.title
      );
    },
    ...mapGetters(['userInfo']),
    menuMap() {
      const { menus } = this.userInfo;
      const map = {};
      menus.forEach((menu) => {
        if (menu.href) {
          map[menu.href] = menu;
        }
      });
      return map;
    }
  },
  methods: {
    getBreadcrumb() {
      this.levelList = this.$route.matched.filter((item) => item.name);
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.app-breadcrumb.el-breadcrumb {
  display: inline-block;
  font-size: 14px;
  line-height: 64px;
  margin-left: 10px;
  .no-redirect {
    color: #97a8be;
    cursor: text;
  }
}
</style>
