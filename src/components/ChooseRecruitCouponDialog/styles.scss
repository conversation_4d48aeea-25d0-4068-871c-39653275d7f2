.filter-form {
  .search-input {
    float: right;
    width: 220px;
  }
  margin-bottom: 16px;
}
.danger {
  color: var(--color-danger);
  &:hover {
    opacity: 0.8;
  }
}

.date {
  font-size: 12px;
  line-height: 17px;
  color: rgba(0, 0, 0, 0.35);
  i {
    cursor: pointer;
  }
}

.status-btn {
  padding: 2px 8px;
  background-color: #c3c3c3;
  border-radius: 4px;
  font-size: 12px;
}

.active {
  color: #fff;
  background-color: #cf0a2c;
}

.title {
  color: rgba(0, 0, 0, 0.85);
  font-weight: 500;
  line-height: 17px;
}

.commodity-title {
  font-size: 0;
  width: 100%;
  letter-spacing: 0.5px;
  line-height: 20px;
  .tag {
    background-color: #ffcfd4;
    color: #cf0a2c;
    font-size: 12px;
    padding: 2px 6.5px;
    border-radius: 10px;
    margin-right: 6px;
  }
  .text {
    font-size: 14px;
    color: #222;
    word-break: break-all;
  }
}

.thumbnail {
  max-width: 70px;
  max-height: 90px;
  float: left;
}

.desc {
  margin-left: 80px;
  text-align: left;
  > div + div {
    margin-top: 8px;
  }
}

.info {
  > div + div {
    margin-top: 6px;
  }
}

.yuan {
  text-decoration: line-through;
}

.disabled {
  cursor: not-allowed;
  color: #c8c9cc;
}

.remove-link {
  display: none;
}

.lottery-tabel {
  tbody tr:hover {
    .remove-link {
      display: inline;
    }
    .has-join {
      display: none;
    }
  }
}

.price {
  white-space: nowrap;
}

.top-info {
  margin-bottom: 16px;
  ::v-deep .el-input {
    width: 220px;
    margin-right: 16px;
  }
  ::v-deep .el-button--primary {
    margin-right: 16px;
  }
  .link {
    color: var(--color-primary);
    cursor: pointer;
    margin-left: 10px;
  }
}
