<template>
  <el-dialog
    title="选择优惠券"
    width="900px"
    :visible="visible"
    @update:visible="$emit('update:visible', $event)"
  >
    <div class="top-info">
      <el-input
        placeholder="搜索优惠券名称"
        v-model="filter.name"
        class="input-with-select"
      >
        <el-button
          slot="append"
          icon="el-icon-search"
          @click="onSearch"
        ></el-button>
      </el-input>
      <label>优惠券状态</label>
      <el-select v-model="filter.status" clearable>
        <el-option label="全部" value="ALL"></el-option>
        <el-option label="未开始" value="FUTURE"></el-option>
        <el-option label="使用中" value="USE"></el-option>
        <el-option label="已结束" value="END"></el-option>
      </el-select>
      <el-button @click="onSearch" type="primary">查询</el-button>
      <el-button @click="onReset">重置</el-button>
      <router-link class="link" :to="'/activity/coupon/list'">
        <span class="link">优惠券管理</span>
      </router-link>
    </div>

    <el-table
      :data="list"
      style="width: 100%"
      v-loading="listLoading"
      class="lottery-tabel"
    >
      <el-table-column
        label="优惠券名称"
        align="center"
        prop="name"
      ></el-table-column>
      <el-table-column
        label="类型"
        prop="couponTypeName"
        align="center"
      ></el-table-column>
      <el-table-column
        label="优惠内容"
        align="center"
        prop="couponContent"
      ></el-table-column>
      <el-table-column label="已兑换 / 剩余" align="center">
        <template slot-scope="scope">
          {{
            scope.row.drawNum + '/' + (scope.row.issueNum - scope.row.drawNum)
          }}
        </template>
      </el-table-column>
      <el-table-column
        label="状态"
        align="center"
        prop="statusName"
      ></el-table-column>
      <el-table-column label="操作" fixed="right" align="center">
        <template slot-scope="scope">
          <template v-if="chooseSet.has(scope.row.id)">
            <a
              class="link-type select-none danger remove-link"
              @click="onRemoveClick(scope.row)"
              >移除</a
            >
            <span class="has-join">已添加</span>
          </template>
          <div v-else-if="scope.row.status === 'END'">已结束</div>
          <a
            class="link-type select-none"
            :class="{ disabled }"
            v-else
            @click="onAddClick(scope.row)"
            >添加</a
          >
        </template>
      </el-table-column>
    </el-table>
    <div class="pagination">
      <el-pagination
        background
        @current-change="handleCurrentChange"
        :current-page="pageNo"
        :page-size="pageSize"
        layout="total, prev, pager, next, jumper"
        :total="total"
        :disabled="listLoading"
      ></el-pagination>
    </div>
  </el-dialog>
</template>
<script>
import { listPageCouponDetail } from '@/api/common/coupon';
import pick from 'lodash/pick';
import pickBy from 'lodash/pickBy';

export default {
  name: 'ChooseRecruitCouponDialog',
  components: {},
  computed: {
    data() {
      return pickBy(this.filter, (val) => !!val);
    },
    chooseSet() {
      return new Set(this.chooseList.map(({ id }) => id));
    },
    // 同时支持受控和非受控两种模式
    chooseList: {
      set(list) {
        if (this.value !== undefined) {
          this.$emit('input', list);
          return;
        }
        this.currentList = list;
      },
      get() {
        return this.value || this.currentList;
      }
    },
    disabled() {
      return this.chooseList.length >= this.max;
    }
  },
  data() {
    const initFilter = {
      // 活动名称
      name: '',
      status: ''
    };
    return {
      list: [],
      filter: {
        ...initFilter
      },
      pageNo: 1,
      pageSize: 5,
      total: 0,
      listLoading: false,
      currentList: []
    };
  },
  watch: {
    visible(val) {
      if (val) {
        this.onReset();
      }
    }
  },
  methods: {
    onSearch() {
      this.pageNo = 1;
      this.fetchDataList();
    },
    onReset() {
      this.pageNo = 1;
      this.filter = { ...this.initFilter };
      if (this.couponStatus) {
        this.filter.status = this.couponStatus;
      }
      this.fetchDataList();
    },
    onAddClick(data) {
      if (this.disabled) {
        return;
      }
      this.chooseList = [...this.chooseList, data];
    },
    onRemoveClick(data) {
      this.chooseList = this.chooseList.filter(({ id }) => id !== data.id);
    },
    fetchDataList() {
      this.listLoading = true;
      const listQuery = pick(this, ['pageNo', 'pageSize', 'data']);
      listPageCouponDetail(listQuery)
        .then((response) => {
          this.list = response.data.list;
          this.total = response.data.total;
        })
        .finally(() => {
          this.listLoading = false;
        });
    },
    filterPrice(row) {
      const { creditPrice = 0, moneyPrice } = row;
      return moneyPrice
        ? `${creditPrice}积分 + ${moneyPrice}元`
        : `${creditPrice}积分`;
    },
    filterStock(row) {
      const { stock = 0, exchangeNum = 0 } = row;
      const remain = stock - exchangeNum;
      return `${exchangeNum} / ${remain < 0 ? 0 : remain}`;
    },
    handleSizeChange: function (val) {
      this.pageSize = val;
      this.fetchDataList();
    },
    handleCurrentChange: function (val) {
      this.pageNo = val;
      this.fetchDataList();
    }
  },
  props: {
    visible: Boolean,
    value: Array,
    // 最多选择数量
    max: {
      type: Number,
      default: 6
    },

    // 优惠券状态查询条件
    couponStatus: {
      type: String,
      default: ''
    }
  }
};
</script>
<style lang="scss" scoped>
@import './styles.scss';
</style>