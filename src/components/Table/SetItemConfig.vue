<template>
  <div>
    <el-button @click="open" size="small" type="primary">{{ title }}<i class="el-icon-setting icon-tools-class" style="margin-left: 5px"></i></el-button>

    <el-dialog :title="title" :visible.sync="visible" @close="selectKeyList = []" width="850px" v-loading="loading" element-loading-background="rgba(0, 0, 0, .1)">
      <div class="dialog-content">
        <!-- 配置选项栏 -->
        <div class="dialog-content__config config">
          <div class="config-header" v-if="tip">{{ tip }}</div>
          <div class="config-content">
            <div v-for="({ name = '全选', childs }, index) of configList" :key="index" class="config-content__box">
              <!-- 全选不能嵌入到checkbox-group下 -->
              <el-checkbox class="config-content__box-title" :indeterminate="isIndeterminateList[index]" v-model="checkAllList[index]" @change="handleCheckAllChange(configList[index], index)">{{ name }}</el-checkbox>
              <el-checkbox-group v-model="selectKeyList" class="config-content__box-checkbox">
                <el-checkbox v-for="{ prop, label, disabled } of childs" :key="prop" :label="prop" @change="handleCheckChange(configList[index], index)" :disabled="disabled">
                  <sy-text :tips="{ content: label, placement: 'top' }">{{ label }}</sy-text>
                </el-checkbox>
              </el-checkbox-group>
            </div>
          </div>
        </div>
        <!-- 已选数据展示栏 -->
        <div class="dialog-content__draggable">
          <div class="draggable-header">已选字段拖拽排序<i class="el-icon-rank"></i></div>
          <div class="draggable-content">
            <div class="fixed" v-if="fixedSelectOptionsList.length">
              <template v-for="(item, index) of fixedSelectOptionsList">
                <div :key="index" class="fixed-item commo-ellipsis">
                  <span class="fixed-item--serial">{{ index + 1 }}</span>
                  {{ item[fieldLabel] }}
                  <i class="el-icon-more-outline more-outline"></i>
                </div>
              </template>
            </div>
            <draggable class="draggable" v-model="selectKeyList">
              <transition-group>
                <template v-for="(item, index) of selectOptionsList">
                  <div v-if="!item.disabled" :key="item[fieldValue]" class="draggable-item commo-ellipsis">
                    <span class="draggable-item--serial">{{ index + 1 }}</span>
                    {{ item[fieldLabel] }}
                    <i class="el-icon-more-outline more-outline"></i>
                  </div>
                </template>
              </transition-group>
            </draggable>
          </div>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="visible = false">取 消</el-button>
        <button-hoc type="primary" @click="onSave">确 定</button-hoc>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import draggable from 'vuedraggable';

export default {
  name: 'SetItemConfig', // 筛选设置
  components: { draggable },
  data() {
    return {
      visible: false,
      loading: false,
      selectKeyList: [], // 当前页面选择项
      checkAllList: [], // 分类时的全选状态值存储
      isIndeterminateList: []
    };
  },
  props: {
    // 标题
    title: {
      type: String,
      require: true
    },
    fieldLabel: {
      type: String,
      default: 'label'
    },
    fieldValue: {
      type: String,
      default: 'prop'
    },
    // 建议选择数量
    suggestNum: Number,
    // 限制选择数量
    maxLimitNum: Number,
    // 配置选择项
    configList: {
      type: Array,
      require: true
    },
    // 配置选择项
    getSetConfigList: {
      type: Function,
      require: true
    }
  },
  computed: {
    tip() {
      const labels = {
        suggestNum: '建议',
        maxLimitNum: '限制'
      };
      const k = Object.keys(labels).find((k) => this[k]);
      return k && `${labels[k]}数量：${this.title}的数据${labels[k]}在${this[k]}个以内`;
    },
    // 获取当前选中项详情
    selectOptionsList() {
      return this.selectKeyList.map((key) =>
        this.configList
          .map((i) => i.childs)
          .flat()
          .find((i) => key === i[this.fieldValue])
      );
    },
    fixedSelectOptionsList() {
      return this.selectOptionsList.filter((i) => i.disabled);
    }
  },
  methods: {
    async open() {
      this.visible = true;

      try {
        this.selectKeyList = await this.getSetConfigList();
        this.configList.forEach(this.handleCheckChange);
      } catch (e) {
        console.dir(e);
      }
    },
    handleCheckChange({ childs }, index) {
      let count = 0;
      const len = childs.length;
      for (let i = 0; i < len; i++) {
        if (count && count < i) break;
        if (this.selectKeyList.includes(childs[i][this.fieldValue])) count++;
      }

      this.$set(this.checkAllList, index, count === len);
      this.$set(this.isIndeterminateList, index, count > 0 && count < len);
    },
    handleCheckAllChange({ childs }, index) {
      const keys = childs.map((i) => i[this.fieldValue]);
      this.selectKeyList = this.checkAllList[index] ? [...new Set([...this.selectKeyList, ...keys])] : this.selectOptionsList.filter((i) => i.disabled || !keys.includes(i[this.fieldValue])).map((i) => i[this.fieldValue]);

      this.isIndeterminateList[index] = !this.checkAllList[index] && this.selectKeyList.length !== 0;
    },
    // 保存当前选中项
    onSave() {
      const len = this.selectOptionsList.length;
      if (!len) {
        this.$message.warning('请选择数据！');
        return;
      }

      if (this.maxLimitNum && len > this.maxLimitNum) {
        this.$message.warning(`最多选择${this.maxLimitNum}项！`);
        return;
      }

      this.loading = true;
      this.$emit('onSave', {
        data: this.selectOptionsList,
        callback: (res) => {
          res
            .then(() => {
              this.visible = false;
              this.$message.success('操作成功');
            })
            .finally(() => {
              this.loading = false;
            });
        }
      });
    }
  }
};
</script>

<style lang='scss' scoped>
.dialog {
  &-content {
    display: flex;
    margin-top: 10px;
    height: 50vh;
    &__config {
      flex: 1;
      height: 100%;
      overflow: auto;
      .config {
        display: flex;
        flex-direction: column;
        &-header {
          height: 24px;
          line-height: 24px;
        }
        &-content {
          flex: 1;
          overflow-y: auto;
          &__box {
            &:not(:first-child) {
              margin-top: 10px;
            }
            &-title {
              font-size: 14px;
              line-height: 24px;
              font-weight: 700;
              ::v-deep {
                .el-checkbox__input {
                  .el-checkbox__inner {
                    width: 16px;
                    height: 16px;
                  }
                }
                .el-checkbox__label {
                  font-size: 14px;
                }
              }
            }
            &-checkbox {
              display: flex;
              flex-wrap: wrap;
              & > label {
                width: calc(33%);
                padding: 0;
                margin: 10px 0 0 0;
                display: flex;
                align-items: center;
                ::v-deep .el-checkbox__label {
                  width: 0;
                  flex: 1;
                  & > span {
                    width: 100%;
                    display: block;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                  }
                }
              }
            }
          }
        }
      }
    }

    &__draggable {
      flex: none;
      width: 300px;
      display: flex;
      flex-direction: column;
      .draggable-header {
        font-weight: 700;
        color: #666;
        margin-bottom: 10px;
        font-size: 14px;
        flex: none;
      }
      .draggable-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        height: 0;
      }
    }
  }
}
.draggable,
.fixed {
  background: rgba(0, 28, 102, 0.03);
  padding: 16px;
  flex: 1;
  overflow-y: auto;
  &-item {
    cursor: move;
    line-height: 1.6;
    border: 1px solid #ddd;
    color: #666;
    background-color: #ffff;
    margin-bottom: 10px;
    padding: 4px 20px 4px 3px;
    font-size: 14px;
    position: relative;
    &--serial {
      color: #7f62d8;
    }
    .more-outline {
      position: absolute;
      right: 4px;
      top: 8px;
    }
  }
}
.fixed {
  padding-bottom: 0;
  flex: none;
  &-item {
    cursor: not-allowed;
  }
}
</style>
