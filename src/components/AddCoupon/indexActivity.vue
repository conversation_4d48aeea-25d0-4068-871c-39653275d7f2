<template>
  <div class="add-commodity-container">
    <div class="add-commodity" v-if="showPageTable">
      <el-button @click="addGift" v-if="editable" type="primary" size="mini">选择</el-button>
    </div>
    <el-table :data="couponList" fit max-height="400" v-show="isShowCoupon">
      <el-table-column label="优惠券">
        <template slot-scope="scope">
          <div>{{ scope.row.name || scope.row.prizeName }}</div>
        </template>
      </el-table-column>
      <el-table-column label="优惠类型">
        <template slot-scope="scope">
          <div>{{ scope.row.couponTypeName }}</div>
        </template>
      </el-table-column>
      <el-table-column label="优惠内容">
        <template slot-scope="scope">
          <div>{{ scope.row.couponContent }}</div>
        </template>
      </el-table-column>
      <el-table-column label="状态">
        <template slot-scope="scope">
          <div>{{ scope.row.statusName }}</div>
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right" v-if="editable">
        <template slot-scope="scope">
          <el-button @click="remove(scope.row.id, scope.$index)" type="text" v-if="editable">移除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog :append-to-body="true" :visible.sync="dialogFormVisible" title="选择优惠券" width="900px">
      <el-form inline>
        <el-form-item>
          <el-input class="input-with-select" placeholder="搜索优惠券名称" v-model.trim="name" clearable size="small"></el-input>
        </el-form-item>
        <el-form-item>
          <el-input class="input-with-select" placeholder="优惠券ID" v-model.trim="id" clearable size="small" @clear="handeClear"></el-input>
        </el-form-item>
        <el-form-item>
          <el-select size="small" v-model="couponTypeCode" :disabled="disabled" placeholder="优惠券类型">
            <el-option :key="i" :label="item.label" :value="item.value" v-for="(item, i) in couponTypeList"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select size="small" clearable multiple collapse-tags v-model="statusList">
            <el-option :key="idx" :label="item.label" :loading="statusOptionsLoading" :value="item.value" v-for="(item, idx) in statusOptions"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button @click="onSearch" type="primary" size="small">查询</el-button>
          <el-button @click="onReset" size="small">刷新</el-button>
          <span class="link-type" @click="clickLink('/activity/coupon/list')"> 优惠券管理 </span>
        </el-form-item>
      </el-form>
      <el-table :data="list" element-loading-text="加载中" fit highlight-current-row v-loading="listLoading">
        <el-table-column label="优惠券ID" property="id"></el-table-column>
        <el-table-column label="优惠券名称" property="name"></el-table-column>
        <el-table-column label="类型" property="couponTypeName"></el-table-column>
        <el-table-column label="优惠券内容" property="couponContent"></el-table-column>
        <el-table-column label="已领取/剩余">
          <template slot-scope="scope">
            <div>{{ scope.row.drawNum }}/{{ scope.row.issueNum - scope.row.drawNum }}</div>
          </template>
        </el-table-column>
        <el-table-column label="状态" property="statusName"></el-table-column>
        <el-table-column label="操作" fixed="right">
          <template slot-scope="operation">
            <span v-if="couponSet.has(operation.row.id)">已添加</span>
            <span v-if="!couponSet.has(operation.row.id) && operation.row.status !== 'END' && operation.row.issueNum - operation.row.drawNum <= 0">库存不足</span>
            <el-button @click="handleJoin(operation.row)" type="text" v-if="!couponSet.has(operation.row.id) && operation.row.status !== 'END' && operation.row.issueNum - operation.row.drawNum > 0">添加</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination :current-page="pageNo" :disabled="listLoading" :page-size="pageSize" :page-sizes="[5, 10, 20, 30]" :total="total" @current-change="handleCurrentChange" @size-change="handleSizeChange" background layout="total, sizes, prev, pager, next, jumper"></el-pagination>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { couponList, fetchStatusOptions } from '@/api/common/commodity';
export default {
  name: 'add-coupon-index-activity',
  model: {
    prop: 'couponList',
    event: 'updateTable'
  },
  props: {
    couponList: Array,
    source: String, // 来源页面，inviteActivity(邀请有礼)
    // 是否显示优惠内容
    showContent: {
      type: Boolean,
      default: false
    },
    isShowCoupon: {
      type: Boolean,
      default: true
    }, // 是否展示商品表格
    editable: {
      type: Boolean,
      default: false
    }, // 非必传 是否可以编辑，默认不可编辑
    // 单选传 false， 默认多选
    multiple: {
      type: Boolean,
      default: true
    },
    isPayGift: {
      type: Boolean,
      default: true
    },
    appendToBody: Boolean,
    pk: {
      type: String,
      default: 'id'
    },
    // 从外层控制选择弹框
    outerDialogFormVisible: {
      type: Boolean,
      default: false
    },
    // 不显示选择的table
    showPageTable: {
      type: Boolean,
      default: true
    },
    // 选择时弹出确认窗
    showMessageBox: {
      type: Boolean,
      default: false
    },
    // 选择时弹出确认窗文案
    showMessageBoxText: {
      type: String,
      default: '确认选择'
    },
    couponType: {
      type: String,
      default: null
    },
    disabled: {
      type: Boolean,
      default: true
    },
  },
  created() {
    this.fetchStatus();
  },
  filters: {},
  computed: {
    couponSet() {
      return new Set(this.couponList.map((item) => item[this.pk]));
    }
  },
  watch: {
    outerDialogFormVisible(val) {
      if (val) {
        this.addGift();
      }
    }
  },
  data() {
    return {
      name: '',
      id: null,
      statusList: ['USE'],
      couponTypeCode: null,
      statusOptions: [], // 状态列表
      statusOptionsLoading: true,
      listLoading: false,
      list: [],
      pageNo: 1,
      pageSize: 5,
      total: 0,
      dialogFormVisible: false,
      currentRow: {},
      couponTypeList: [{
        label: '满减券',
        value: 'MONEY_OFF'
      }, {
        label: '折扣券',
        value: 'RATE_OFF'
      }, {
        label: '样品券',
        value: 'SAMPLE'
      }, {
        label: '运费劵',
        value: 'DELIVERY_FEE'
      }, {
        label: '核销卡券',
        value: 'VERIFICATION_CARD'
      }]
    };
  },
  methods: {
    clickLink(path) {
      this.dialogFormVisible = false;
      this.$router.push(path);
    },
    fetchStatus() {
      // 状态列表
      this.statusOptionsLoading = true;
      fetchStatusOptions()
        .then((rs) => {
          const res = rs.data.map((item) => ({
            value: item.value,
            label: item.label
          }));
          this.statusOptions = res.filter((item) => item.value === 'FUTURE' || item.value === 'USE');
        })
        .finally(() => {
          this.statusOptionsLoading = false;
        });
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.pageNo = 1;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.pageNo = val;
      this.fetchData();
    },
    onSearch() {
      this.pageNo = 1;
      this.pageSize = 5;
      this.fetchData();
    },
    onReset() {
      this.pageNo = 1;
      this.pageSize = 5;
      this.name = '';
      this.fetchData();
    },
    handleJoin(rowData) {
      let list = [...this.couponList];
      const pushData = { ...rowData };
      // 积分兑换活动
      if (this.source === 'integralExchange') {
        pushData.commodityRelationDetailList = [{
          leaveStock: '',
          point: '',
          izEnable: '1',
        }];
        pushData.imgUrl = '';
        pushData.bizName = pushData.name;
        pushData.content = pushData.couponContent;
        pushData.detailTotalStock = pushData.issueNum - pushData.drawNum;
      }
      if (!this.showMessageBox) {
        if (!this.multiple) {
          this.dialogFormVisible = false;
          list = [pushData];
        } else {
          list.push(pushData);
        }
      } else {
        this.$confirm(`${this.showMessageBoxText}`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.dialogFormVisible = false;
            list = [pushData];
            this.$emit('updateTable', list);
          })
          .catch(() => {});
        return;
      }
      this.$emit('updateTable', list);
    },
    remove(commodityId) {
      const list = [...this.couponList];
      const idx = list.findIndex(({ id }) => id === commodityId);
      list.splice(idx, 1);
      this.$emit('updateTable', list);
    },
    handeClear() {
      this.id = null;
    },
    addGift() {
      this.dialogFormVisible = true;
      this.couponTypeCode = this.couponType;
      this.onReset();
    },
    fetchData() {
      this.listLoading = true;
      const { pageNo, pageSize, name, statusList, couponTypeCode, id = null } = this;
      const listQuery = {
        pageNo,
        pageSize,
        data: { name, statusList, couponType: couponTypeCode, id }
      };
      couponList(listQuery)
        .then((response) => {
          this.list = response.data.list;
          this.total = response.data.total;
        })
        .finally(() => {
          this.listLoading = false;
        });
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
::v-deep .el-dialog {
  width: 60%;
}
.link-type {
  margin-left: 10px;
}
</style>
