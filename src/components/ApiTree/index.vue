<template>
  <div>
    <el-input
      size="small"
      class="input"
      v-model="filterText"
      placeholder="输入关键字进行过滤"
      clearable
    ></el-input>
    <el-tree
      v-loading="loading"
      :data="dataList"
      node-key="id"
      ref="tree"
      class="tree"
      :props="defaultProps"
      :filter-node-method="filterNode"
      highlight-current
      show-checkbox
      :default-checked-keys="defaultKeys"
    >
      <span slot-scope="{ node, data }">
        <span v-html="filterLightText(data.label)" class="label"></span>
        <span v-html="filterLightText(data.code)" class="code"></span>
      </span>
    </el-tree>
  </div>
</template>
<script>
import { fetchGroupApis, listAllApps } from '@/api/system/route';
import debounce from 'lodash/debounce';
import isEmpty from 'lodash/isEmpty';
export default {
  name: 'ApiTree',
  data() {
    return {
      apiMap: {},
      dataList: [],
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      filterText: '',
      loading: true,
      initPromise: null // 初始化状态
    };
  },
  computed: {
    defaultKeys() {
      const self = this;
      const { defaultCheckedKeys, selectKeys, apiMap } = self;
      if (defaultCheckedKeys) {
        return defaultCheckedKeys;
      }
      if (!selectKeys || isEmpty(apiMap)) {
        return [];
      }
      return selectKeys
        .filter((code) => apiMap[code])
        .map((code) => apiMap[code].id);
    }
  },
  created() {
    const self = this;
    self.initPromise = Promise.all([fetchGroupApis(), listAllApps()])
      .then(([resApi, resApp]) => {
        const apiGroups = resApi.data;
        const apps = resApp.data;
        const appMap = {};
        const apiMap = {};
        const dataList = apps.map(({ appId, name, id }) => {
          const obj = {
            code: appId,
            label: name,
            id,
            disabled: true
          };
          appMap[appId] = obj;
          return obj;
        });
        apiGroups.forEach((api) => {
          const { group, apis } = api;
          const { id, name, code, tenantId } = group;
          const data = appMap[tenantId];
          if (!data) {
            return;
          }
          const obj = { id, label: name, code, disabled: true };
          if (apis && apis.length > 0) {
            obj.children = apis.map(({ id, permissionName, permissionKey }) => {
              const obj = { id, label: permissionName, code: permissionKey };
              apiMap[permissionKey] = obj;
              return obj;
            });
            obj.disabled = false;
            if (!data.children) {
              data.disabled = false;
            }
          }
          if (!data.children) {
            data.children = [obj];
          } else {
            data.children.push(obj);
          }
        });
        self.apiMap = apiMap;
        self.dataList = dataList;
      })
      .finally(() => {
        self.loading = false;
      });
  },
  watch: {
    filterText(val) {
      const self = this;
      debounce(() => self.$refs.tree.filter(val), 300)();
    }
  },
  methods: {
    filterNode(value, data) {
      if (!value) return true;
      return (
        data.label.indexOf(value) !== -1 || data.code.indexOf(value) !== -1
      );
    },
    filterLightText(val) {
      const self = this;
      if (!self.filterText) {
        return val;
      }
      return val.replace(
        new RegExp(self.filterText, 'g'),
        `<span style="color: red;">${self.filterText}</span>`
      );
    },
    getCheckedNodes(...args) {
      return this.$refs.tree.getCheckedNodes(...args);
    },
    setCheckedKeys(keys) {
      this.$refs.tree.setCheckedKeys(keys);
    },
    setCheckedPermissionKey(keys = []) {
      const ids = keys
        .filter((key) => this.apiMap[key])
        .map((key) => this.apiMap[key].id);
      this.$refs.tree.setCheckedKeys(ids);
    },
    getCheckedPermissionKeys(...args) {
      return this.$refs.tree.getCheckedNodes(...args).map((obj) => obj.code);
    }
  },
  props: {
    defaultCheckedKeys: Array,
    selectKeys: Array,
    sourceList: Array
  }
};
</script>
<style lang="scss" scoped>
@import './styles';
</style>



