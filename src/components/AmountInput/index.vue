<template>
  <el-input :value="value" @change="onChange" @input="onInput" ref="elInput"></el-input>
</template>
<script>
import round from 'lodash/round';
export default {
  name: 'amount-input',
  methods: {
    onChange(value) {
      this.$emit('input', value ? round(parseFloat(value), 2) || 0 : value);
    },
    onInput(value) {
      if (value && !/^[0-9|\.]*$/.test(value)) {
        this.$nextTick(() => {
          const target = this.getInputTarget();
          target.value = this.value;
        });
        return;
      }
      this.$emit('input', value);
    },
    getInputTarget() {
      return this.$refs.elInput.$el.querySelector('input');
    }
  },
  props: ['value']
};
</script>