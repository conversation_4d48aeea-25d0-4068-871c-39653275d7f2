<template>
  <div class="select-distributor">
    <slot>
      <el-button v-if="!isDetail" @click="open(false)" size="mini" type="primary">选择</el-button>
      <div class="relation" v-if="relationDescObj.num">
        已关联{{ relationDescObj.desc }}个分销商
        <el-button class="btn" type="text" :disabled="false" @click="open(true)">查看已关联</el-button>
      </div>
    </slot>
    <el-dialog :title="title" :visible.sync="visible" width="70%" append-to-body @closed="onCancel">
      <div class="container" v-if="visible">
        <el-row class="container-title">
          <span>选择关联分销商方式：</span>
          <el-radio-group v-model="activeName">
            <el-radio :label="tab.type" v-for="tab of Tabs" :key="tab.type">{{ tab.label }}</el-radio>
          </el-radio-group>
        </el-row>
        <keep-alive>
          <component :is="activeName" :key="activeName" @hook:mounted="initChild" :ref="activeName" class="container-content"></component>
        </keep-alive>
      </div>
      <span slot="footer" class="dialog-footer" v-if="!isDetail">
        <el-button @click="onCancel" :disabled="false">取消</el-button>
        <button-hoc type="primary" @click="onSubmit()">保存</button-hoc>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import CustomDistributor from './CustomDistributor';
import Organization from './Organization';
import DistributorGrade from './DistributorGrade';
import PurchaseChannel from './PurchaseChannel';
import lifeCycleDistributor from './lifeCycleDistributor';
import CustomerAttribute from './CustomerAttribute';
import omit from 'lodash/omit';
import { distributorPortraitCustomerInfoGet } from '@/api/data-center/distributor-square';
export default {
  // 选择分销商
  name: 'select-distributor',
  components: {
    CUSTOMER_TYPE_DEFINE: CustomDistributor,
    CUSTOMER_TYPE_CS_ORG: Organization,
    CUSTOMER_TYPE_ACTIVE_DEGREE: lifeCycleDistributor,
    CUSTOMER_TYPE_PURCHASE_CHANNEL: PurchaseChannel,
    CUSTOMER_TYPE_DISTRIBUTOR_GRADE: DistributorGrade,
    CUSTOMER_TYPE_CUSTOMER_ATTRIBUTE: CustomerAttribute
  },
  inheritAttrs: false,
  data() {
    return {
      visible: false,
      isRelation: false,
      relationDescObj: {},
      activeName: 'CUSTOMER_TYPE_DEFINE',
      sourceNameMap: {
        integralExchange: '积分兑换活动',
        integral: '积分活动',
        salesPolicy: '销售政策'
      }
    };
  },
  props: {
    //
    relationData: Object,
    // 活动来源
    source: String,
    // 自定义显示的Tab
    showTabs: {
      type: Array,
      default: () => []
    },
    // 详情需要禁用选择
    isDetail: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    title() {
      return this.sourceName ? `${this.sourceName}-选择分销商` : '选择分销商';
    },
    sourceName() {
      return this.sourceNameMap[this.source] || '';
    },
    Tabs() {
      return [
        {
          type: 'CUSTOMER_TYPE_DEFINE',
          label: '自定义关联',
          dataKey: 'csIds',
          descHandle(num) {
            return num;
          }
        },
        {
          type: 'CUSTOMER_TYPE_CS_ORG',
          label: '按分组',
          dataKey: 'organizationIds',
          calcCsNum: () => this._customerNum ?? this.relationData.customerNum,
          descHandle(num, csNum) {
            return num;
          }
        },
        {
          type: 'CUSTOMER_TYPE_ACTIVE_DEGREE',
          label: '按生命周期',
          dataKey: 'activeDegreeIds',
          calcCsNum: () => this._customerNum ?? this.relationData.customerNum,
          descHandle(num) {
            return num;
          }
        },
        {
          type: 'CUSTOMER_TYPE_PURCHASE_CHANNEL',
          label: '按采货渠道',
          dataKey: 'purchaseChannelIds',
          calcCsNum: () => this._customerNum ?? this.relationData.customerNum,
          descHandle(num) {
            return num;
          }
        },
        {
          type: 'CUSTOMER_TYPE_DISTRIBUTOR_GRADE',
          label: '按分销商等级',
          dataKey: 'distributorGradeIds',
          calcCsNum: () => this._customerNum ?? this.relationData.customerNum,
          descHandle(num) {
            return num;
          }
        },
        {
          type: 'CUSTOMER_TYPE_CUSTOMER_ATTRIBUTE',
          label: '按客户层级',
          dataKey: 'customerAttributeIds',
          calcCsNum: () => this._customerNum ?? this.relationData.customerNum,
          descHandle(num) {
            return num;
          }
        }
      ].filter((item) => {
        if (this.showTabs.length) {
          return this.showTabs.includes(item.type);
        }
        return true;
      });
    }
  },
  watch: {
    relationData: {
      handler(val) {
        this.setRelationDescObj();
      },
      deep: true
    }
  },
  created() {
    this.setRelationDescObj();
  },
  methods: {
    open(isRelation = false) {
      this.visible = true;
      this.isRelation = isRelation;
      this.activeName = (this.relationData.subtype === 'CUSTOMER_TYPE_FILE_IMPORT' ? 'CUSTOMER_TYPE_DEFINE' : this.relationData.subtype) ?? 'CUSTOMER_TYPE_DEFINE';
    },
    initChild() {
      const { dataKey } = this.Tabs.find((i) => i.type === this.activeName);
      const list = (this.relationData[dataKey] || []).map(this.activeName === 'CUSTOMER_TYPE_DEFINE' || this.activeName === 'CUSTOMER_TYPE_CS_ORG' ? (id) => ({ id }) : (item) => item);
      const relationVm = this.$refs[this.activeName];
      relationVm.init(list, this.isRelation);
    },
    setRelationDescObj() {
      console.log('setRelationDescObj======', this.relationData);
      const { subtype, ...data } = this.relationData;
      const type = subtype === 'CUSTOMER_TYPE_FILE_IMPORT' ? 'CUSTOMER_TYPE_DEFINE' : subtype;
      if (!type) return;

      const { dataKey, calcCsNum, descHandle } = this.Tabs.find((i) => i.type === type);
      const num = data?.[dataKey]?.length || 0;
      this.relationDescObj = {
        num,
        desc: descHandle((calcCsNum && calcCsNum()) || num)
      };
    },
    onCancel() {
      this.visible = false;
    },
    async onSubmit() {
      this.visible = false;
      const subtype = this.activeName;
      const data = this.$refs[subtype].selectData;
      this.$emit('onSuccess', {
        subtype,
        ...this.Tabs.reduce((cur, options) => {
          const { dataKey } = options;
          cur[dataKey] = subtype === options.type ? (subtype === 'CUSTOMER_TYPE_DEFINE' || subtype === 'CUSTOMER_TYPE_CS_ORG' ? data.map(({ id }) => id) : data.map((item) => item)) : [];
          return cur;
        }, {})
      });
      // 排除不需要的字段
      let params = this.relationData;
      const omitKey = ['csIds', 'organizationIds', 'activeDegreeIds', 'purchaseChannelIds', 'distributorGradeIds', 'customerAttributeIds'];
      params = omit(params, omitKey);
      if (subtype !== 'CUSTOMER_TYPE_DEFINE' && data.length) this._customerNum = (await distributorPortraitCustomerInfoGet(params))?.data?.customerNum;
      this.setRelationDescObj();
    }
  }
};
</script>

<style lang="scss" scoped>
.relation {
  display: inline-block;
  font-size: 12px;
  margin-left: 10px;
}

.btn {
  margin-left: 10px;
}

.container {
  display: flex;
  flex-direction: column;

  &-title {
    height: 36px;
    line-height: 36px;
  }

  &-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    margin-top: 8px;
  }
}
</style>
