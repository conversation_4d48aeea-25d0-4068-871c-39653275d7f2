<template>
  <div class="content" :style="'width: ' + contentWidth + 'px'" :class="isTextCenter ? 'content-center' : ''">
    <el-tooltip placement="top" v-if="!isShowView && isShowTooltip" :effect="effect">
      <div slot="content">
        <slot>{{ text }}</slot>
      </div>
      <div class="text" :class="lineClamp === 1 ? 'commo-ellipsis' : 'commo-ellipsis-' + lineClamp">
        {{ text }}
      </div>
    </el-tooltip>
    <div class="text" :style="{ width: '100%', textAlign: textAlignCenter }" :class="lineClamp === 1 ? 'commo-ellipsis' : 'commo-ellipsis-' + lineClamp" v-if="isShowView || !isShowTooltip">
      {{ text }}
    </div>
    <el-tooltip placement="top" v-if="isShowView && isShowTooltip" :effect="effect">
      <div slot="content">
        <slot>{{ text }}</slot>
      </div>
      <i v-if="isShowView" class="el-icon-view icon-style" ref="iconView"></i>
    </el-tooltip>
    <div class="text text-content" :class="{ 'text-content-nowrap': lineClamp === 1 }" :style="lineClamp === 2 || lineClamp === 3 ? 'width:' + textContentWidth + 'px' : ''" ref="textContent">
      {{ text }}
    </div>
  </div>
</template>
<!-- 超出隐藏 文本组件-->
<script>
export default {
  name: 'commo-ellipsis',
  props: {
    // 文字显示内容
    text: {
      type: String,
      default: ''
    },
    // 是否显示查看icon
    isShowView: {
      type: Boolean,
      default: true
    },
    // 文字是否居中展示,默认是false,文字左对齐显示
    isTextCenter: {
      type: Boolean,
      default: false
    },
    // 超过多少行展示... 默认是1行，可选2、3行
    lineClamp: {
      type: Number,
      default: 1
    },
    // 容器长度,默认是200px
    contentWidth: {
      type: Number,
      default: 200
    },
    // 主题  dark/light
    effect: {
      type: String,
      default: 'dark'
    },
    textAlignCenter: {
      type: String,
      default: 'center'
    }
  },
  data() {
    return {
      isShowTooltip: true, // 鼠标悬浮是否展示文字提示
      textContentWidth: null, // 隐藏div的宽度
      rowNum: null, // 隐藏div文字行数
      textContentHeight: null // 隐藏div的高度
    };
  },
  mounted() {
    this.isShowTips();
  },
  watch: {
    text() {
      this.$nextTick(() => {
        this.isShowTips();
      });
    }
  },
  methods: {
    // 判断是否展示文字提示
    isShowTips() {
      // 隐藏div的宽度
      this.textContentWidth = this.$refs.textContent.clientWidth;
      // 隐藏div的高度
      this.textContentHeight = this.$refs.textContent.clientHeight;
      // 当lineClamp为1的时候判断是否展示文字提示
      this.isShowTooltip = this.$refs.textContent.clientWidth > this.contentWidth;
      // 当lineClamp为多行的时候判断是否展示文字提示
      if (this.lineClamp > 1) {
        // 计算眼睛icon的宽度
        const iconViewWidth = this.$refs?.iconView?.clientWidth ?? 0;
        // 当lineClamp为2时候计算隐藏div的宽度，有眼睛icon的时候要减去icon的宽度
        this.textContentWidth = this.isShowView ? this.contentWidth - iconViewWidth : this.contentWidth;
        this.rowNum = Math.round(this.textContentHeight / 22);
        this.isShowTooltip = this.rowNum > this.lineClamp;
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.content {
  width: 100%;
  display: flex;
  align-items: center;
  position: relative;
}
.content-center {
  justify-content: center;
}
.text {
  line-height: 22px;
}
.text-content {
  position: absolute;
  visibility: hidden;
}
.text-content-nowrap {
  white-space: nowrap;
}
.icon-style {
  color: var(--color-primary);
}
// 超出1行显示省略号
.commo-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
// 超出2行显示省略号
.commo-ellipsis-2 {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
// 超出3行显示省略号
.commo-ellipsis-3 {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}
</style>
