<!-- 锚点组件 -->
<template>
  <div class="anchor-wrap" ref="scrollView">
    <div class="content" ref="content">
      <div v-for="(item, index) in labelList" :key="index" :id="'anchor_' + index">
        <slot :name="item.key"></slot>
      </div>
    </div>
    <div class="anchor">
      <el-aside>
        <el-tabs @tab-click="handleClick" v-model="activeName" tab-position="left" style="height: auto">
          <el-tab-pane :name="'anchor_' + index" v-for="(item, index) in labelList" :key="index" :label="item.name"></el-tab-pane>
        </el-tabs>
      </el-aside>
    </div>
  </div>
</template>
<script>
import debounce from 'lodash/debounce';
export default {
  name: 'Anchor',
  data() {
    return {
      debouncedPageScrollHandler: null,
      pageInfo: {},
      activeName: 'anchor_0'
    };
  },
  // 注意：这个组件要求父页面所有高度都是100%
  props: {
    labelList: {
      type: Array
    }
  },
  mounted() {
    this.debouncedPageScrollHandler = debounce(this.scrolls, 10);
    this.$refs.scrollView.addEventListener('scroll', this.debouncedPageScrollHandler);
  },
  beforeDestroy() {
    this.$refs.scrollView.removeEventListener('scroll', this.debouncedPageScrollHandler);
  },
  methods: {
    scrolls() {
      const currentScrollTop = this.$refs.scrollView.scrollTop + 1;
      const labelList = this.labelList;
      // 依次和各节点原先的offsetTop进行比较
      for (let i = 0; i < labelList.length; i++) {
        const anchor = this.$el.querySelector('#anchor_' + i);
        const top = anchor.offsetTop;
        const next = this.$el.querySelector('#anchor_' + (i + 1));
        // 如果scrollTop正好和某节点的offsetTop相等
        // 或者scrollTop介于当前判断的节点和下一个节点之间
        // 由于需要下一个节点，所以当前节点不能是最后一个节点
        if (currentScrollTop === top || (i < labelList.length - 1 && currentScrollTop > top && currentScrollTop < next.offsetTop)) {
          this.activeName = 'anchor_' + i;
          break;
        } else if (i === labelList.length - 1) {
          // 如果判断到一个节点，只要 scrollTop大于节点的offsetTop即可
          if (currentScrollTop > top) {
            this.activeName = 'anchor_' + i;
            break;
          }
        }
      }
    },
    /**
     * 页面滚动
     */
    scrollEvent() {
      this.isScroll = true;
      const scroll = this.scrollView.scrollTop;
      this.isShowBackTop = scroll !== 0;
      if (this.isToggle) return (this.isToggle = false);
      for (let i = this.labelList.length - 1; i > 0; i--) {
        if (scroll >= this.labelList[i].offsetTop) {
          this.activeName = 'tab' + i;
          break;
        }
        if (scroll <= this.labelList[0].offsetTop) {
          this.activeName = 'tab' + 0;
        }
      }
    },
    /**
     * 切换tab
     */
    handleClick(tab) {
      const name = tab.name;
      // this.activeName = name;
      const anchor = this.$el.querySelector('#' + name);
      this.$refs.scrollView.scrollTop = anchor.offsetTop;
    }
  }
};
</script>
<style lang="scss" scoped>
.anchor-wrap {
  height: 100%;
  overflow-y: scroll;
  position: relative;
}
.anchor {
  position: fixed;
  right: 10px;
  top: 30%;
  width: auto;
  ::v-deep {
    .el-aside {
      width: auto !important;
    }
    .is-active {
      color: #5f3bce;
      font-size: 16px;
    }
  }
}
.content {
  //  width: calc(100% - 300px);
}
</style>
