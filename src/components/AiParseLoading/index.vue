<template>
  <div v-show="visible" class="ai-parse-loading">
    <div class="ai-parse-loading__content" :class="{ 'ai-parse-loading__content--half': isHalf, 'ai-parse-loading__content--full': isFull }">
      <i class="el-icon-loading"></i>
      <div class="ai-robot">
        <svg-icon icon-class="ai-robot" />
        <span>AI解析中</span>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'AiParseLoading',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    isFull: {
      type: Boolean,
      default: true
    },
    isHalf: {
      type: Boolean,
      default: false
    }
  }
};
</script>

<style scoped lang="scss">
.ai-parse-loading {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 2001;
  background-color: rgba(0, 0, 0, 0.1);
  &__content {
    position: fixed;
    top: 50%;
    &--full {
      left: 50%;
      transform: translate(-50%, -50%);
    }
    &--half {
      left: 80%;
      transform: translate(-200%, -50%);
    }
    border-radius: 4px;
    background-color: #fff;
    padding: 24px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    .el-icon-loading {
      font-size: 24px;
      color: var(--color-primary);
    }
    .ai-robot {
      margin-top: 8px;
      display: flex;
      align-items: center;
      font-size: 12px;
      color: var(--color-info);
      .svg-icon {
        width: 1.6em;
        height: 1.6em;
        margin-right: 8px;
      }
    }
  }
}
</style>
