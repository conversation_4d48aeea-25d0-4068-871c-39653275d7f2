<!-- 组合式表单子组件 -->
<template>
  <div class="combined-box" :class="{ 'combined-box__wrap': isWrap }">
    <el-tooltip effect="dark" placement="top" class="combined-box__item__tooltip" :content="tooltip" :disabled="!tooltip">
      <label v-show="showLabel" class="label-tp" :style="labelStyle">{{ label }} </label>
    </el-tooltip>

    <div class="combined-box__content" :class="{ 'flex-wrap': isWrap }" :style="gap ? `gap: ${gap}` : 'gap: 0'">
      <div class="combined-box__item" v-show="!item.hidden" v-for="item of configuration" :key="item.key" :style="{ width: item.width || '180px' }">
        <label v-if="item.label" class="combined-box__item__label">
          <el-tooltip v-if="item.tooltip" effect="dark" placement="top" class="combined-box__item__tooltip">
            <div slot="content">{{ item.tooltip }}</div>
            <i :class="item.tooltipIcon ? item.tooltipIcon : 'el-icon-warning-outline'"></i>
          </el-tooltip>
          {{ item.label }}
        </label>
        <div class="combined-box__item__content" :class="[item.label ? 'combined-box__item__content__mage' : '']">
          <SelectTag v-if="item.type === 'selectTag'" v-model="form[item.key]" :options="filterOptions(item.key, 'options')"></SelectTag>
          <el-select v-if="item.type === 'select'" v-model="form[item.key]" v-bind="item.$attrs" class="select-box--tpt" :size="getSize(item.$attrs)" :clearable="item.$attrs.clearable === false ? false : true">
            <el-option :key="idx" :label="o.label" :value="o.value" v-for="(o, idx) in filterOptions(item.key, 'options')"></el-option>
          </el-select>
          <SelectByPopover v-if="item.type === 'selectByPopover'" v-model="form[item.key]" :item="item" :options="item.isOption ? item.options : filterOptions(item.key, 'options')" @refreshOptions="refreshOptions" @defineInput="defineInput" />
          <SelectByCheckbox v-if="item.type === 'selectByCheckbox'" v-model="form[item.key]" :item="item" :defaultIndex="item.defaultIndex" :options="filterOptions(item.key, 'options')" />
          <el-cascader v-if="item.type === 'cascader'" v-model="form[item.key]" :options="item.options" v-bind="item.$attrs" :size="getSize(item.$attrs)" :clearable="item.$attrs.clearable === false ? false : true" :style="{ width: item.$attrs.width || '180px' }"> </el-cascader>
          <div v-if="item.type === 'selectComposite'" class="select-box">
            <el-select v-model="form[item.key]" v-bind="item.$attrs" class="select-box--tpt select-box-main" :size="getSize(item.$attrs)" :clearable="item.$attrs.clearable === false ? false : true">
              <el-option :key="idx" :label="o.label" :value="o.value" v-for="(o, idx) in filterOptions(item.key, 'options')"></el-option>
            </el-select>
            <el-select v-if="item.slot && item.slot.type === 'select'" v-model="form[item.slot.slotKey]" v-bind="item.slot.$attrs" class="select-box--tpt" :size="getSize(item.$attrs)" :clearable="item.slot.$attrs.clearable === false ? false : true" :style="{ width: item.slot.width || '100%' }">
              <el-option :key="idx" :label="o.label" :value="o.value" v-for="(o, idx) in filterOptions(item.slot.slotKey, 'slotOptions')"></el-option>
            </el-select>
          </div>
          <el-input v-else-if="item.type === 'input'" v-model.trim="form[item.key]" v-bind="item.$attrs" :size="getSize(item.$attrs)" :clearable="item.$attrs.clearable === false ? false : true">
            <template slot="append">
              <span v-if="item.append">{{ item.append }}</span>
            </template>
          </el-input>
          <el-input v-else-if="item.type === 'selectInput'" v-model.trim="form[item.key]" v-bind="item.$attrs" :size="getSize(item.$attrs)" class="f-select-input" :clearable="item.$attrs.clearable === false ? false : true" :style="{ width: item.width || '180px', flexShrink: 0 }">
            <template slot="prepend">
              <el-select v-model="form[item.slot.slotKey]" v-bind="item.slot.$attrs" class="select-box--tpt" :style="{ width: item.slot.width || '185px' }" :size="getSize(item.$attrs)">
                <el-option :key="idx" :label="item.label" :value="item.value" v-for="(item, idx) in filterOptions(item.slot.slotKey, 'slotOptions')"></el-option>
              </el-select>
            </template>
            <template slot="append">
              <span v-if="item.slot.append">{{ item.slot.append }}</span>
            </template>
          </el-input>
          <el-date-picker v-else-if="item.type === 'datePicker'" v-model="form[item.key]" v-bind="item.$attrs" :size="getSize(item.$attrs)" :clearable="item.$attrs.clearable === false ? false : true" :style="{ width: item.$attrs.width || '250px' }"> </el-date-picker>
          <div v-else-if="item.type === 'selectDatePicker'" class="select-date-picker" :size="getSize(item.$attrs)" :style="{ width: item.width || '250px' }">
            <el-select v-model="form[item.slot.slotKey]" v-bind="item.slot.$attrs" class="select-box--tpt" :size="getSize(item.$attrs)" :style="{ width: item.slot.width || '110px' }">
              <el-option :key="idx" :label="item.label" :value="item.value" v-for="(item, idx) in filterOptions(item.slot.slotKey, 'slotOptions')"></el-option>
            </el-select>
            <el-date-picker class="date-picker" v-model="form[item.key]" v-bind="item.$attrs" :clearable="item.$attrs.clearable === false ? false : true" :size="getSize(item.$attrs)"> </el-date-picker>
          </div>
          <RangeInput v-else-if="item.type === 'RangeInput'" v-model="form[item.key]" v-bind="item.$attrs" :clearable="item.$attrs.clearable === false ? false : true" :size="getSize(item.$attrs)" />
          <div v-else-if="item.type === 'selectRangeInput'" class="select-date-picker" :size="getSize(item.$attrs)" :style="{ width: item.width || '250px' }">
            <el-select v-model="form[item.slot.slotKey]" v-bind="item.slot.$attrs" class="select-box--tpt" :size="getSize(item.$attrs)" :style="{ width: item.slot.width || '110px' }">
              <el-option :key="idx" :label="o.label" :value="o.value" v-for="(o, idx) in filterOptions(item.slot.slotKey, 'slotOptions')"></el-option>
            </el-select>
            <RangeInput v-model="form[item.key]" v-bind="item.$attrs" :size="getSize(item.$attrs)" :clearable="item.$attrs.clearable === false ? false : true" />
          </div>
          <!--custom 自定义组件-->
          <slot v-else-if="item.type === 'custom'" :name="item.slotName"></slot>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import RangeInput from '@/components/ListSearch/RangeInput';
import pickBy from 'lodash/pickBy';
import SelectTag from './select-tag.vue';
import SelectByPopover from './select-by-popover.vue';
import SelectByCheckbox from './select-by-checkbox.vue';
// 是否方法
function isFunction(val) {
  return toString.call(val) === '[object Function]';
}
export default {
  name: 'CombinedFormItem',
  components: { RangeInput, SelectTag, SelectByPopover, SelectByCheckbox },
  data() {
    return {
      form: {},
      defaultFormValue: {}, // 默认值
      options: {},
      slotOptions: {}
    };
  },
  props: {
    configuration: {
      type: Array,
      default() {
        return [];
      }
    },
    size: String,
    label: String,
    tooltip: String,
    labelWidth: {
      type: String,
      default: '90px'
    },
    isWrap: Boolean,
    showLabel: {
      type: Boolean,
      default: true
    },
    labelAlign: {
      type: String,
      default: 'left'
    },
    gap: {
      type: String,
      default: ''
    }
  },
  watch: {
    form: {
      handler() {
        this.$emit('refresh');
      },
      deep: true
    }
  },
  computed: {
    labelStyle() {
      return {
        minWidth: this.labelWidth,
        textAlign: this.labelAlign
      };
    }
  },
  created() {
    this.init();
  },
  methods: {
    // 初始化
    init() {
      this.configuration.forEach((item) => {
        if (item.options) {
          this.$set(this.options, item.key, []);
          this.processOptions(item.options, item.key, 'options');
        }
        if (item.slot && item.slot.options) {
          this.$set(this.slotOptions, item.slot.slotKey, []);
          this.processOptions(item.slot.options, item.slot.slotKey, 'slotOptions');
        }

        if (item.defaultValue) {
          this.defaultFormValue[item.key] = item.defaultValue;
        }
        if (item.slot && item.slot.defaultValue) {
          this.defaultFormValue[item.slot.slotKey] = item.slot.defaultValue;
        }
      });
      this.setFormDefaultValue();
    },
    // 处理默认值
    setFormDefaultValue() {
      const formDefaultValue = Object.keys(this.defaultFormValue);
      if (formDefaultValue.length) {
        formDefaultValue.forEach((key) => {
          this.$set(this.form, key, this.defaultFormValue[key]);
        });
      }
    },
    filterOptions(key, options) {
      return this[options][key];
    },
    // 自定义输入框回调
    defineInput({ item, value }) {
      if (!value) {
        delete this.form[`${item.key}define`];
      }
      this.$set(this.form, `${item.key}define`, value);
    },
    // 处理异步动态options
    refreshOptions({ opions, item }) {
      this.$set(this.options, item.key, []);
      const oldOptions = this.options[item.key] || [];
      // 去重
      const newOptions = [...opions, ...oldOptions].filter((item, index, self) => index === self.findIndex((t) => t.value === item.value));
      this.processOptions(newOptions, item.key, 'options');
    },
    // 处理异步options
    processOptions(val = [], Key, options) {
      if (typeof val === 'object' && isFunction(val.then) && isFunction(val.catch)) {
        val.then((res) => {
          this.$set(this[options], Key, res);
        });
      } else if (Array.isArray(val)) {
        this.$set(this[options], Key, val);
      }
    },
    getSize(e = {}) {
      const { size } = e;
      if (size) return size;
      if (this.size) return this.size;
      return '';
    },
    //  选项
    getOptions() {
      return { ...this.options, ...this.slotOptions };
    },
    // 外部调用 值
    getValue() {
      const value = pickBy(this.form, (val) => {
        if (Array.isArray(val)) {
          return val.length;
        }
        return !!val;
      });
      let config = null;
      const map = new Map();
      this.configuration.forEach((item) => {
        if (!map.has(item.key)) {
          map.set(item.key, item);
        }
      });
      if (Object.keys(value).length) {
        // 返回所有配置项
        config = {
          configurationMap: map,
          getOptions: this.getOptions()
        };
      }
      return { value, config };
    },
    // 重置
    onItemReset(Delkey, slotKey, setDefaultValue = true) {
      if (Delkey) {
        if (this.form[Delkey] === '' || !this.form[Delkey]?.length) return;
        this.ItemReset(Delkey);
        if (slotKey) {
          this.ItemReset(slotKey);
        }
        if (setDefaultValue) {
          this.setFormDefaultValue();
        }
        return;
      }

      Object.keys(this.form).forEach((key) => {
        this.ItemReset(key);
      });
      if (setDefaultValue) {
        this.setFormDefaultValue();
      }
    },
    ItemReset(key) {
      const data = this.form[key];
      if (Array.isArray(data)) {
        this.form[key] = [];
      } else {
        this.form[key] = '';
      }
    }
  }
};
</script>
<style lang="scss" scoped>
.combined-box {
  display: flex;
  align-items: baseline;
  margin-bottom: 6px;
  &__wrap {
    align-items: flex-start;
    margin-bottom: 0;
  }
  .label-tp {
    font-size: 12px;
    text-align: left;
    color: #6e768c;
    font-weight: normal;
  }
  &__content {
    display: flex;
    flex: 1;
  }
  &__item {
    &:not(:last-child) {
      margin-right: 8px;
    }
    display: flex;
    align-items: center;
    &__tooltip {
      color: #3e4965;
      margin-right: 3px;
    }
    &__label {
      vertical-align: middle;
      line-height: 34px;
      font-weight: normal;
      font-size: 12px;
      text-align: left;
      margin-right: 10px;
    }
    &__content {
      flex: 1;
      &__mage {
      }
      &:after {
        clear: both;
      }
      & > .el-select {
        display: block;
      }
    }
    ::v-deep {
      .el-select .el-select__tags .el-tag .el-select__tags-text {
        display: inline-block;
        max-width: 60px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        vertical-align: middle;
      }
      .el-select .el-tag__close.el-icon-close,
      .el-cascader .el-tag__close.el-icon-close {
        right: -3px;
      }
      .el-cascader .el-cascader__tags .el-tag > span {
        display: inline-block;
        max-width: 60px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        vertical-align: middle;
      }
    }
  }
  .flex-wrap {
    .combined-box__item {
      margin-bottom: 8px;
    }
  }
}
::v-deep {
  .el-date-editor--datetimerange.el-input,
  .el-date-editor--datetimerange.el-input__inner {
    width: auto;
  }
  .range__inputbox {
    width: 100%;
  }
}
.select-box {
  display: flex;
  align-items: center;
  &-main {
    flex-shrink: 0;
    margin-right: 1px;
  }
}
.select-date-picker {
  display: flex;
  .el-select {
    flex-shrink: 0;
    margin-right: 1px;
    ::v-deep .el-input__inner {
      background-color: #f7f8fa;
      color: #868d9f;
      vertical-align: middle;
      display: table-cell;
      position: relative;
      border: 1px solid #e6e8eb;
      white-space: nowrap;
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
      border-right-width: 0;
    }
  }
  .date-picker {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
  }
}
::v-deep {
  .el-input-group__prepend div.el-select .el-input__inner {
    border-top: 1px solid #e6e8eb !important;
    border-bottom: 1px solid #e6e8eb !important;
    background-color: #fff;
    color: #0d1b3f;
  }

  .select-date-picker .el-select .el-input__inner {
    border: 1px solid #e6e8eb !important;
    background-color: #fff;
    color: #0d1b3f;
  }
}
.f-select-input {
  ::v-deep {
    .el-input-group__prepend div.el-select .el-input__inner {
      border-top: 0 !important;
    }
  }
}
.flex-wrap {
  flex-wrap: wrap;
}
.combined-box__wrap {
  .label-tp {
    line-height: 32px;
    margin-top: 5px;
  }
}
</style>
