<template>
  <div class="select-tag">
    <el-tag :key="index" :type="isSelect(item.value) ? 'primary' : 'info'" :effect="isSelect(item.value) ? 'dark' : 'light'" v-for="(item, index) in options" @click="handleChecks(item)">{{ item.label }}</el-tag>
  </div>
</template>
<script>
export default {
  name: 'select-tag',
  props: {
    value: {
      type: Array,
      default: () => []
    },
    options: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    isSelect(value) {
      const index = this.value.indexOf(value);
      return index > -1;
    },
    handleChecks(tag) {
      const { value } = this;
      const index = value.indexOf(tag.value);
      if (index > -1) {
        value.splice(index, 1);
      } else {
        value.push(tag.value);
      }
      this.$emit('input', value);
    }
  }
};
</script>

<style scoped lang="scss">
.select-tag {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 2px;
  ::v-deep {
    .el-tag {
      margin: 0px 4px 0px 0;
      cursor: pointer;
    }
    .el-tag--small {
      padding: 0 4px;
    }
    .el-tag--info {
      color: #3e4965 !important;
      background-color: transparent !important;
      border-color: transparent !important;
      &:hover {
        color: #5f3bce !important;
      }
    }
  }
}
</style>
