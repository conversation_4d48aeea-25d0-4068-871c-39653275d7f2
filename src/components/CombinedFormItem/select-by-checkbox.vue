<template>
  <div class="select-by-checkbox">
    <el-dropdown trigger="hover" @command="handleCommand" placement="bottom" @visible-change="visibleChange">
      <div>
        <el-tooltip placement="top-start" :disabled="!item.tooltip">
          <div slot="content" v-html="item.tooltip"></div>
          <div class="text-content" :class="{ isSelected: isSelected || isHover }">
            <el-checkbox v-model="isChecked" @change="checkChange" style="margin-right: 2px" size="mini" />
            <span>{{ item.labelName }}</span>
            <i class="el-icon-arrow-down arrow" :class="{ hover: isHover }" />
          </div>
        </el-tooltip>
      </div>
      <el-dropdown-menu slot="dropdown" class="custom-dropdown-menu">
        <el-dropdown-item v-for="(o, idx) in options" :key="idx" :label="o.label" :value="o.value" :command="o.value" :disabled="curVal === o.value" class="selected">
          {{ o.label }}
        </el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
  </div>
</template>
<script>
export default {
  name: 'select-by-checkbox',
  props: {
    value: {
      type: String,
      default: ''
    },
    item: {
      type: Object,
      default: () => {}
    },
    options: {
      type: Array,
      default: () => []
    },
    defaultIndex: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      curVal: '',
      isChecked: false,
      isHover: false
    };
  },
  watch: {
    value: {
      handler(val) {
        this.curVal = val;
        this.isChecked = !!val;
      }
    }
  },
  computed: {
    isSelected() {
      const value = this.curVal;
      if (Array.isArray(value)) {
        return value.length > 0;
      }
      if (typeof value === 'object') {
        return Object.values(value).flat().length > 0;
      }
      if (typeof value === 'string') {
        return value.trim().length > 0;
      }
    }
  },
  methods: {
    handleCommand(command) {
      this.curVal = command;
      this.isChecked = true;
      this.$emit('input', this.curVal);
    },
    checkChange(isChecked) {
      if (isChecked) {
        this.curVal = this.options[this.defaultIndex].value;
      } else {
        this.curVal = '';
      }
      this.$emit('input', this.curVal);
    },
    visibleChange(value) {
      this.isHover = value;
    }
  }
};
</script>

<style scoped lang="scss">
.custom-dropdown-menu {
  max-height: 300px;
  overflow-y: auto;
  ::v-deep {
    .el-dropdown-menu__item.is-disabled {
      cursor: default;
      color: #5f3bce;
      pointer-events: none;
      background-color: #efebfa;
    }
  }
}
.select-by-checkbox {
  width: fit-content;
  .text-content {
    cursor: pointer;
    display: flex;
    gap: 2px;
    align-items: center;
    font-size: 12px;
    color: #3e4965;
    height: 24px;
    line-height: 24px;
    &:hover {
      color: #5f3bce;
    }
    .arrow {
      display: inline-block;
      transition: transform 0.3s ease;
      &.hover {
        transform: rotate(180deg);
      }
    }
  }
  .isSelected {
    color: #5f3bce;
  }

  ::v-deep {
    .el-checkbox__inner {
      border-radius: 2px;
      box-sizing: border-box;
    }
  }
}
</style>
