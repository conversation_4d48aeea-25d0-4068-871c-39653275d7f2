<template>
  <div class="select-by-popover">
    <el-popover :placement="'bottom-start'" trigger="hover" @after-enter="afterEnterHandler(item)" popper-class="popoverNotArrow" @show="isHover = true" @hide="isHover = false">
      <div slot="reference">
        <el-tooltip placement="top-start" :disabled="!item.tooltip">
          <div slot="content" v-html="item.tooltip"></div>
          <div class="text-content" :class="{ isSelected: isSelected || isHover }">
            <span>{{ item.labelName }}</span>
            <i class="el-icon-arrow-down arrow" :class="{ hover: isHover }" />
          </div>
        </el-tooltip>
      </div>
      <el-select v-if="item.action" ref="selectRef" v-model="curVal" :popper-append-to-body="false" v-bind="item.$attrs" clearable @change="change" :remote-method="requestFied" filterable :remote="true" collapseTags>
        <el-option v-for="o in remoteOptions" :key="o.id" :label="o.label" :value="o.value" />
      </el-select>
      <el-select v-else ref="selectRef" v-model="curVal" :popper-append-to-body="false" v-bind="item.$attrs" clearable @change="change" filterable automatic-dropdown collapseTags>
        <el-option v-for="(o, idx) in options" :key="idx" :label="o.label" :value="o.value" />
      </el-select>
    </el-popover>
    <div v-if="curVal === 'define'" class="define-content">
      <span>自定义</span>
      <el-input v-model="defineMinText" placeholder="下限" size="mini" @change="defineChange" @input="handleInputByMin" />
      <span>-</span>
      <el-input v-model="defineMaxText" placeholder="上限" size="mini" @change="defineChange" @input="handleInputByMax" />
    </div>
  </div>
</template>
<script>
import { commoditySearchList } from '@/api/commodity/index';
export default {
  name: 'select-by-popover',
  props: {
    value: {
      type: [String, Number, Array, Boolean],
      default: ''
    },
    item: {
      type: Object,
      default: () => {}
    },
    options: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      curVal: '',
      remoteOptions: [],
      defineMinText: null,
      defineMaxText: null,
      isHover: false
    };
  },
  watch: {
    value: {
      handler(val) {
        this.curVal = val;
      }
    }
  },
  computed: {
    requestFied() {
      return this[this.item.action];
    },
    isSelected() {
      const value = this.curVal;
      if (Array.isArray(value)) {
        return value.length > 0;
      }
      if (typeof value === 'object') {
        return Object.values(value).flat().length > 0;
      }
      if (typeof value === 'string') {
        return value.trim().length > 0;
      }
    }
  },
  methods: {
    change() {
      // 自定义选项
      if (this.curVal === 'define') {
        return;
      } else {
        this.defineMinText = null;
        this.defineMaxText = null;
        this.$emit('defineInput', { value: '', item: this.item });
      }
      this.$emit('input', this.curVal);
    },
    afterEnterHandler() {
      this.$refs[`selectRef`].focus();
    },
    commoditySearchList(val = '') {
      if (!val) {
        return;
      }
      const params = {
        data: { commodityName: val },
        pageNo: 1,
        pageSize: 30
      };
      this.remoteOptions = [];
      commoditySearchList(params).then((res) => {
        this.remoteOptions = res.data.list.map((item) => {
          return {
            label: `${item.commodityName} (${item.barcode})`,
            value: item.barcode
          };
        });
        this.$emit('refreshOptions', { opions: this.remoteOptions, item: this.item });
      });
    },
    defineChange() {
      this.$emit('defineInput', { value: `${this.defineMinText},${this.defineMaxText}`, item: this.item });
      this.$emit('input', this.curVal);
    },
    handleInputByMin(value) {
      this.defineMinText = value.replace(/[^0-9]/g, '');
    },
    handleInputByMax(value) {
      this.defineMaxText = value.replace(/[^0-9]/g, '');
    }
  }
};
</script>

<style>
.popoverNotArrow {
  margin-top: 2px !important;
}
</style>
<style scoped lang="scss">
.select-by-popover {
  width: fit-content;
  display: flex;
  align-items: center;
  .define-content {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    padding-left: 10px;
    color: #5f3bce;
    ::v-deep {
      .el-input--mini,
      .el-input--small {
        width: 80px;
      }
      .el-input--mini .el-input__inner {
        height: 24px;
        line-height: 24px;
      }
      .el-input--mini .el-input__icon {
        line-height: 24px;
      }
    }
  }
  .text-content {
    cursor: pointer;
    display: flex;
    gap: 2px;
    align-items: center;
    font-size: 12px;
    color: #3e4965;
    height: 24px;
    line-height: 24px;
    &:hover {
      color: #5f3bce;
    }
    .arrow {
      display: inline-block;
      transition: transform 0.3s ease;
      &.hover {
        transform: rotate(180deg);
      }
    }
  }
  .isSelected {
    color: #5f3bce;
  }
}
</style>
