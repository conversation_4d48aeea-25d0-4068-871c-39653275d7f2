<template>
  <div class="add-commodity-container">
    <div class="add-commodity" v-if="(commodityType !== 'addGift' && operation !== 'detail' && showPageTable) || (commodityType === 'addGift' && operation === 'create' && showPageTable)">
      <el-button :type="source === 'integralExchange' ? 'primary' : 'text'" size="mini" @click="addCommodity">{{ labelTitle || giftLabelTitle || `选择${typeName}` }}</el-button>
      <span class="commodity-tip" v-if="commodityList.length && source === 'integralExchange'">共选择了{{ commodityList.length }}个商品，{{ skuNum }}个SKU</span>
      <el-button v-if="isRetract" class="on-off" type="text" @click="isShowCommodity = !isShowCommodity">{{ isShowCommodity ? '全部收起' : '全部展开' }}</el-button>
    </div>
    <el-table :data="tableIsPagination ? paginationList : commodityList" fit max-height="600" v-if="showPageTable" v-show="isShowCommodity">
      <el-table-column label="id" :prop="isgift ? 'commodityId' : 'id'" width="120"></el-table-column>
      <el-table-column label="商品图片" v-if="!isPayGift" width="90">
        <template slot-scope="imgUrl">
          <img :src="imgUrl.row.thumbnailUrl" alt class="goods-img" v-if="!imgUrl.row.skuCommodityVO" />
          <img :src="imgUrl.row.skuCommodityVO.thumbnailUrl" alt class="goods-img" v-if="imgUrl.row.skuCommodityVO" />
        </template>
      </el-table-column>
      <el-table-column prop="name" label="商品名称" v-if="!isPayGift" min-width="150">
        <template slot-scope="scope">
          <div v-if="commodityType === 'addGift' && scope.row.skuCommodityVO && scope.row.skuCommodityVO.name">
            {{ scope.row.skuCommodityVO.name }}
          </div>
          <div v-else>{{ scope.row.name }}</div>
          <div v-if="scope.row.skuCommodityVO">
            <div>
              {{ scope.row.skuCommodityVO.firstLevel }}
              {{ scope.row.skuCommodityVO.secondLevel }}
              {{ scope.row.skuCommodityVO.threeLevel }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="商品名称" v-if="isPayGift">
        <template slot-scope="scope">
          {{ scope.row.name ? scope.row.name : scope.row.prizeName }}
        </template>
      </el-table-column>
      <el-table-column label="件数" v-if="isQuantity">
        <template slot-scope="scope">
          <el-input size="small" placeholder="请输入数量" :min="1" :max="scope.row.stock" v-model="scope.row.quantity"> </el-input>
        </template>
      </el-table-column>
      <el-table-column label="价格" v-if="!isPayGift && commodityType !== 'recommendCommodity'">
        <template slot-scope="scope">
          <div v-if="!scope.row.skuCommodityVO">{{ scope.row.price }}</div>
          <div v-if="scope.row.skuCommodityVO">
            {{ scope.row.skuCommodityVO.price }}
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="typeName" label="商品类型" v-if="commodityType === 'recommendCommodity'"></el-table-column>
      <el-table-column prop="statusName" label="商品状态" v-if="commodityType === 'recommendCommodity'"></el-table-column>
      <el-table-column label="限领次数" v-if="commodityType === 'gift'">
        <template slot-scope="scope">
          <div v-if="scope.row.limitNum !== 0">{{ scope.row.limitNum }}</div>
          <div v-else>不限次数</div>
        </template>
      </el-table-column>
      <el-table-column prop="skuCommodityVO.stock" label="库存" v-if="commodityType === 'addGift'"></el-table-column>
      <el-table-column prop="stock" label="库存" v-if="commodityType !== 'addGift' && commodityType !== 'recommendCommodity'"></el-table-column>
      <el-table-column label="采货类型">
        <template slot-scope="{ row }">
          <span v-if="row.purchaseType === 'PURCHASE'">采销</span>
          <span v-if="row.purchaseType === 'DROP_SHIPPING'">一件代发</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" prop="statusName"></el-table-column>
      <el-table-column label="操作" fixed="right" v-if="(commodityType !== 'addGift' && operation !== 'detail') || (commodityType === 'addGift' && operation === 'create') || isPayGift">
        <template slot-scope="scope">
          <el-button @click="remove(scope.row.id, scope.$index)" type="text">移除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      v-if="tableIsPagination"
      v-show="isShowCommodity"
      @size-change="tableHandleSizeChange"
      @current-change="tableHandleCurrentChange"
      :current-page="tableCurrent"
      :page-sizes="[4, 10, 20, 30]"
      :page-size="tableSize"
      layout="total, sizes, prev, pager, next"
      :total="commodityList.length"
      class="tableIsPagination"
    ></el-pagination>
    <el-dialog :title="labelTitle || '选择商品'" :visible.sync="dialogFormVisible" :append-to-body="true" width="1200px" @close="handleDialogClose">
      <div class="top-info">
        <el-select v-model="brandGroups" placeholder="品牌分组（可多选）" clearable multiple :collapse-tag="source !== 'fullDiscount'" :disabled="source === 'fullDiscount'" size="small">
          <el-option v-for="item in brandGroupList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
        </el-select>
        <el-select v-model="brandIds" placeholder="品牌（可多选）" clearable multiple collapse-tags size="small">
          <el-option v-for="item in brandList" :key="item.value" :label="item.label" :value="item.value"> </el-option>
        </el-select>
        <el-input placeholder="搜索商品名称" :validate-event="false" v-model="name" class="input-with-select" clearable></el-input>
        <el-input placeholder="搜索商品id" :validate-event="false" v-model="id" class="input-with-select" clearable></el-input>
        <el-input placeholder="搜索商品条码" :validate-event="false" v-model="specCode" class="input-with-select" clearable></el-input>
        <el-input placeholder="搜索规格标识" :validate-event="false" v-model="skuId" class="input-with-select" clearable></el-input>
        <el-select v-model="categoryId" placeholder="请选择分组" clearable>
          <el-option v-for="item in categoryIdOptions" :key="item.value" :label="item.name" :value="item.id"></el-option>
        </el-select>
        <el-select v-model="status" placeholder="请选择" v-if="commodityType !== 'gift'" clearable>
          <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
        <el-select v-model="isHidden" placeholder="是否是隐藏商品" v-if="commodityType !== 'gift'" clearable>
          <el-option v-for="item in isHiddenOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
        <el-button type="primary" @click="onSearch">查询</el-button>
        <el-button @click="onReset" type="primary">刷新</el-button>
        <div class="add-all" v-if="commodityType === 'gift' || commodityType === 'addGift'">
          <p v-if="commodityType === 'gift'">
            如果找不到对应商品,可点击
            <span class="link" @click="clickLink('/activity/gift')">
              <span class="link">【赠品管理】</span>
            </span>
            进行查看
          </p>
          <p v-else>
            如果找不到对应商品,可点击
            <span class="link" @click="clickLink('/commodity/list')">
              <span class="link">【商品管理】</span>
            </span>
            进行查看
          </p>
        </div>
        <div class="add-all" v-else>
          <p>批量操作:</p>
          <el-checkbox v-model="allChecked" @change="allCheckedFun" :disabled="!allCheckedBtn || !multiple">全选</el-checkbox>
          <el-button type="primary" @click="handleJoinALl" :disabled="!allCheckedBtn || !multiple">批量添加</el-button>
          <p>
            如果找不到对应商品,可点击
            <span class="link" @click="clickLink('/commodity/list')"> <span class="link">【商品管理】</span> </span>进行查看
          </p>
        </div>
      </div>

      <el-table :data="list" v-loading="listLoading" element-loading-text="加载中" fit highlight-current-row>
        <el-table-column width="55" v-if="commodityType === 'normal' || commodityType === 'recommendCommodity'">
          <template slot-scope="scope">
            <el-checkbox v-model="scope.row.isChecked" :disabled="commoditySet.has(scope.row.id)" @change="checkedChange"></el-checkbox>
          </template>
        </el-table-column>
        <el-table-column property="id" label="id" width="160px"></el-table-column>
        <el-table-column label="商品缩略图">
          <template slot-scope="imgUrl">
            <img :src="imgUrl.row.thumbnailUrl" alt class="goods-img" v-if="!imgUrl.row.skuCommodityVO" />
            <img :src="imgUrl.row.skuCommodityVO.thumbnailUrl" alt class="goods-img" v-if="imgUrl.row.skuCommodityVO" />
          </template>
        </el-table-column>
        <el-table-column property="name" label="商品名称"></el-table-column>
        <el-table-column label="商品品牌">
          <template slot-scope="scope">
            <span v-if="commodityType !== 'gift'">{{ scope.row.brandName || '-' }}</span>
            <span v-if="commodityType === 'gift'">{{ scope.row.skuCommodityVO.brandName || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="价格" v-if="commodityType !== 'addGift'">
          <template slot-scope="scope">
            <div v-if="commodityType !== 'gift'">{{ scope.row.price }}</div>
            <div v-if="commodityType === 'gift' && scope.row.skuCommodityVO">
              {{ scope.row.skuCommodityVO.price }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="状态">
          <template slot-scope="scope">
            <div v-if="commodityType !== 'gift'">
              {{ scope.row.statusName }}
            </div>
            <div v-if="commodityType === 'gift' && scope.row.skuCommodityVO">
              {{ scope.row.skuCommodityVO.status | parseStatus }}
            </div>
          </template>
        </el-table-column>
        <el-table-column property="stock" label="库存" v-if="commodityType !== 'gift'"></el-table-column>
        <el-table-column label="参与活动" max-width="250">
          <template slot-scope="scope">
            <div class="activity-list">
              <div v-for="(item, i) in scope.row.joinActivityList" :key="i">
                {{ item }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="限领次数" v-if="commodityType === 'gift'">
          <template slot-scope="scope">
            <div v-if="scope.row.limitNum !== 0">{{ scope.row.limitNum }}</div>
            <div v-else>不限次数</div>
          </template>
        </el-table-column>
        <el-table-column property="isHidden" label="是否隐藏" v-if="commodityType !== 'gift'"
          ><template slot-scope="scope">
            {{ scope.row.isHidden === '1' ? '是' : '否' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right">
          <template slot-scope="operation">
            <div>
              <span v-if="commoditySet.has(operation.row.id) && commodityType !== 'addGift'">已添加</span>
              <el-button :disabled="elFilterDisabled(operation.row)" @click="chooseSpec(operation.row)" v-if="commodityType === 'addGift'" type="text" key="addGift">选择规格</el-button>
              <el-button :disabled="elFilterDisabled(operation.row)" @click="handleJoin(operation.row, operation.$index)" type="text" v-if="!commoditySet.has(operation.row.id) && commodityType !== 'addGift'" key="commodity">添加</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="pageNo" :page-sizes="[5, 10, 20, 30]" :page-size="pageSize" layout="total, sizes, prev, pager, next, jumper" :total="total" :disabled="listLoading"></el-pagination>
      </div>

      <el-dialog width="40%" title="选择规格" :visible.sync="innerVisible" append-to-body>
        <table class="stock-table" border="0" rules="none">
          <thead>
            <tr>
              <th v-for="(spec, i) in specList" :key="i">{{ spec.name }}</th>
              <th>价格</th>
              <th>库存</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(sku, i) in skuList" :key="i">
              <td v-for="(spec, j) in specList" :key="j">
                {{ handleSpecData(i, j) }}
              </td>
              <td>{{ sku.price }}</td>
              <td>{{ sku.stock }}</td>
              <td>
                <el-button @click="handleJoin(sku)" type="text">添加</el-button>
                <!-- 已添加按钮需要补齐 -->
              </td>
            </tr>
          </tbody>
        </table>
      </el-dialog>
    </el-dialog>
  </div>
</template>

<script>
import { customReq, listGift, listSpec, listSpokesManCommodity } from '@/api/common/commodity';
import { goodsType } from '@/api/commodity/list';
import { getDictionary } from '@/api/common/dictionary';
import { listAllBrandName } from '@/api/brand/brand-info';
import { creditActivityListTradeCenterCommodity } from '@/api/activity/integral';

export default {
  name: 'add-commodity',
  model: {
    prop: 'commodityList',
    event: 'updateTable'
  },
  props: {
    // 积分活动id
    activityId: String,
    isShowCommodity: {
      type: Boolean,
      default: true
    }, // 是否展示商品表格
    // 按钮及弹窗标题文案
    labelTitle: String,
    giftLabelTitle: String,
    // spokesman 表示代言人商品， gift 表示赠品， normal 没有传就是默认的普通商品, addGift 表示添加商品为赠品的情况，card表示卡券商品可以选择,bargain-砍价,integral-积分商品
    commodityType: {
      type: String,
      default: 'normal'
    },
    // 外部控制组件的数据请求
    parseQuery: Function,
    // 控制商品是否支持被选择
    filterDisabled: Function,
    // 单选传 false， 默认多选
    multiple: {
      type: Boolean,
      default: true
    },
    // 是否显示页面的已选中表格
    showPageTable: {
      type: Boolean,
      default: true
    },
    // 是否显示选择框
    outerDialogFormVisible: {
      type: Boolean,
      default: false
    },
    // 是否来自于礼包支付
    isPayGift: {
      type: Boolean,
      default: false
    },
    // 是否可以编辑数量
    isQuantity: {
      type: Boolean,
      default: false
    },
    commodityList: Array,
    // create 不传默认表示新增， edit表示编辑， detail 表示详情
    operation: {
      type: String,
      default: 'create'
    },
    // 是否展示已选中表格展开与收起按钮
    isRetract: {
      type: Boolean,
      default: false
    },
    // 是否显示已选中表格的分页
    tableIsPagination: {
      type: Boolean,
      default: false
    },
    // 赠品详情页面 会用到
    isgift: {
      type: Boolean,
      default: false
    },
    // 自定义的请求路径
    customReqUrl: {
      type: String
    },
    // 活动品牌
    bizChannel: String,
    // 活动来源
    source: String,
    // 积分计算方式
    izPointMultiple: String,
    // 积分兑换倍率
    pointMultiple: [String, Number]
  },
  filters: {
    parseStatus(status) {
      let result = '';
      switch (status) {
        case '0':
          result = '待上架';
          break;
        case '1':
          result = '上架';
          break;
        case '3':
          result = '下架';
          break;
      }
      return result;
    }
  },
  computed: {
    commoditySet() {
      return new Set(this.commodityList.map(({ id }) => id));
    },
    paginationList() {
      // 显示分页后的表格数据  已选中表格
      const commodityList = [...this.commodityList];
      if (commodityList.length <= this.tableSize) return commodityList;
      return commodityList.slice(this.tableSize * (this.tableCurrent - 1), this.tableSize * this.tableCurrent);
    },
    brandList() {
      const { bizChannel } = this;
      if (!bizChannel) {
        return this.originBrandList.map((item) => ({
          value: item.id,
          label: item.name
        }));
      }
      const arr = this.originBrandList.filter((item) => item.orderBrandType === bizChannel);
      return arr.map((item) => ({
        value: item.id,
        label: item.name
      }));
    },
    // 已选中的商品sku数量
    skuNum() {
      const skuNum = this.commodityList.reduce((sum, { detailNum, commodityRelationDetailList: detailList = [] }) => sum + (detailList.length || detailNum), 0);
      return skuNum;
    }
  },
  created() {
    if (this.commodityType === 'gift') {
      this.typeName = '赠品';
    } else {
      this.typeName = '商品';
    }
    this.initData();
  },
  watch: {
    outerDialogFormVisible(val) {
      console.log('outerDialogFormVisible changed', val);
      if (val) {
        this.addCommodity();
      }
    },
    bizChannel() {
      this.filterBrandInfo();
    },
    dialogFormVisible(val) {
      if (!val) {
        this.$emit('updateOuterDialogFormVisible', false);
      }
    }
  },
  data() {
    return {
      id: '', // 商品id搜索条件
      specCode: '',
      skuId: '',
      tableSize: 4,
      tableCurrent: 1,
      name: '',
      typeName: '商品', // 默认是商品，假如选择的是赠品叫做赠品
      listLoading: false,
      list: [],
      pageNo: 1,
      pageSize: 5,
      total: 0,
      dialogFormVisible: false,
      innerVisible: false,
      isHidden: '0', // 是否隐藏商品
      isHiddenOptions: [
        {
          value: '0',
          label: '非隐藏商品'
        },
        {
          value: '1',
          label: '隐藏商品'
        }
      ],
      currentRow: {},
      specList: [],
      skuList: [],
      // 商品上架状态
      status: '1',
      statusOptions: [
        {
          value: '0',
          label: '待上架'
        },
        {
          value: '1',
          label: '上架中'
        },
        {
          value: '3',
          label: '已下架'
        }
      ],
      categoryId: '', // 商品分组id
      categoryIdOptions: [],
      allChecked: false, // 是否全选
      allCheckedBtn: false, // 能否全选
      brandGroups: [],
      brandGroupList: [],
      brandIds: [],
      originBrandList: []
    };
  },
  methods: {
    // 数据初始化
    async initData() {
      await this.fetchBrandGroup();
      await this.fetchBrand();
      this.filterBrandInfo();
      this.getGoodsType();
    },
    // 过滤品牌分组及品牌
    filterBrandInfo() {
      const { bizChannel = '' } = this;
      if (!bizChannel) {
        return;
      }
      // 重置已选品牌
      if (this.brandIds.length) {
        this.brandIds = [];
      }
      // 自有品牌
      if (bizChannel === 'NORMAL') {
        this.brandGroups = this.brandGroupList.filter((item) => item.value === 'SF').map((item) => item.value);
      }
      // 国际品牌
      if (bizChannel === 'INTERNATION') {
        this.brandGroups = this.brandGroupList.filter((item) => item.value !== 'SF').map((item) => item.value);
      }
    },
    // 获取品牌
    fetchBrand() {
      listAllBrandName().then((res) => {
        this.originBrandList = res?.data ?? [];
      });
    },
    // 获取品牌分组
    fetchBrandGroup() {
      getDictionary('BRAND_GROUP').then((res) => {
        this.brandGroupList = res || [];
      });
    },
    clickLink(link) {
      this.dialogFormVisible = false;
      this.$router.push(link);
    },
    // 控制商品是否可以被选择
    elFilterDisabled(row) {
      if (this.filterDisabled) return this.filterDisabled(row);
      return row.type === 'CARD' && this.commodityType !== 'CARD';
    },
    tableHandleSizeChange(val) {
      this.tableSize = val;
    },
    tableHandleCurrentChange(val) {
      this.tableCurrent = val;
    },
    handleSpecData(i, j) {
      const levelName = this.specList[j].level;
      let level = '';
      switch (levelName) {
        case '1':
          level = 'firstLevel';
          break;
        case '2':
          level = 'secondLevel';
          break;
        case '3':
          level = 'threeLevel';
          break;
      }
      return this.skuList[i][level];
    },
    handleChange() {
      this.$forceUpdate();
    },
    fetchSpec() {
      listSpec(this.currentRow.id)
        .then((res) => {
          if (res.data) {
            this.specList = res.data.specificationVOList;
            this.skuList = res.data.skuVOList;
          }
        })
        .finally(() => {
          this.listLoading = false;
        });
    },
    chooseSpec(row) {
      this.currentRow = row;
      this.innerVisible = true;
      this.fetchSpec();
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.pageNo = 1;
      this.fetchData();
    },
    handleCurrentChange(val) {
      this.pageNo = val;
      this.fetchData();
    },
    handleJoin(rowData, index) {
      if (index) {
        this.list[index].isChecked = true;
      }
      let list = [...this.commodityList];
      let pushData = { ...rowData };
      if (this.commodityType === 'addGift') {
        pushData = { ...this.currentRow };
        pushData.skuCommodityVO = { ...rowData };
      }
      // 积分兑换活动
      if (this.source === 'integralExchange') {
        pushData.commodityRelationDetailList = rowData.skuBriefs.map((item) => {
          this.$set(item, 'izEnable', '1');
          this.$set(item, 'leaveStock', '');
          this.$set(item, 'skuStock', item.stock);
          this.$set(item, 'skuPrice', item.dmMallPrice);
          this.$set(item, 'skuBarcode', item.specCode);
          this.$set(item, 'skuSupplyPrice', item.price);
          this.$set(item, 'bizTag', '');
          if (this.izPointMultiple === '1') {
            this.$set(item, 'point', Math.ceil(this.pointMultiple * item.dmMallPrice));
          } else {
            this.$set(item, 'point', '');
          }
          return item;
        });
        pushData.imgUrl = !pushData.skuCommodityVO ? pushData?.thumbnailUrl || '' : pushData?.skuCommodityVO?.thumbnailUrl || '';
        pushData.bizName = pushData.name;
        pushData.detailTotalStock = pushData.stock;
      }
      if (this.source === 'regularPolicy') {
        pushData.skuId = rowData.id;
        pushData.commodityId = this.currentRow.id;
        pushData.specCode = rowData.specCode;
      }
      pushData.giftNum = '';
      if (!this.multiple) {
        this.dialogFormVisible = false;
        list = [pushData];
      } else {
        list.push(pushData);
      }
      if (this.operation !== 'detail') {
        this.$emit('updateTable', list);
      }
      this.innerVisible = false;
    },

    remove(commodityId) {
      const list = [...this.commodityList];
      const idx = list.findIndex(({ id }) => id === commodityId);
      list.splice(idx, 1);
      if (this.operation !== 'detail') {
        this.$emit('updateTable', list);
      }
    },
    addCommodity() {
      // 如果来源是满件折送且未选活动品牌
      if (!this.bizChannel && this.source === 'fullDiscount') {
        this.$message.error('请先选择活动品牌');
        return;
      }
      // 如果是积分商品
      if (this.commodityType === 'integral' && !this.activityId) {
        this.$message.error('请先选择积分活动');
        return;
      }
      // 如果是积分兑换
      if (this.source === 'integralExchange' && this.izPointMultiple === '1' && this.pointMultiple <= '0') {
        this.$message.error('请先设置兑换倍率');
        return;
      }
      this.dialogFormVisible = true;
      this.onReset();
    },
    fetchData() {
      this.listLoading = true;
      const { parseQuery, pageNo, pageSize, name, status, id, specCode, skuId, categoryId, isHidden, brandIds, brandGroups } = this;
      const listQuery = {
        pageNo,
        pageSize,
        data: { name, status, id, specCode, skuId, categoryId, isHidden, brandIds, brandGroups }
      };
      let request;
      if (this.commodityType === 'integral') {
        request = creditActivityListTradeCenterCommodity;
        listQuery.data.activityId = this.activityId;
      } else if (this.commodityType === 'spokesman') {
        request = listSpokesManCommodity;
      } else if (this.commodityType === 'gift') {
        request = listGift;
        listQuery.data.isEnable = '1';
      } else {
        request = customReq;
      }
      request(parseQuery ? parseQuery(listQuery) : listQuery, this.customReqUrl)
        .then((response) => {
          const { list = [], total = 0 } = response.data || {};
          list.forEach((i) => {
            if (this.commoditySet.has(i.id)) {
              i.isChecked = true;
            } else {
              i.isChecked = false;
              this.allCheckedBtn = true;
            }
          });
          this.list = list;
          this.total = total;
        })
        .finally(() => {
          this.listLoading = false;
        });
    },
    onSearch() {
      this.pageNo = 1;
      this.pageSize = 5;
      this.fetchData();
    },
    onReset() {
      this.pageNo = 1;
      this.pageSize = 5;
      this.name = '';
      this.status = '1';
      this.id = '';
      this.specCode = '';
      this.skuId = '';
      this.categoryId = '';
      this.fetchData();
    },
    getGoodsType() {
      goodsType().then((res) => {
        this.categoryIdOptions = res.data;
      });
    },
    allCheckedFun(val) {
      this.list.forEach((i) => {
        if (!this.commoditySet.has(i.id)) {
          i.isChecked = val;
        }
      });
    },
    checkedChange() {
      let flag = true;
      for (const i of this.list) {
        if (!this.commoditySet.has(i.id) && !i.isChecked) {
          flag = false;
          break;
        }
      }
      this.allChecked = flag;
    },
    handleJoinALl() {
      const listChecked = [];
      this.list.forEach((i) => {
        if (!this.commoditySet.has(i.id) && i.isChecked) {
          listChecked.push(i);
        }
      });
      // 积分兑换活动
      if (this.source === 'integralExchange') {
        listChecked.map((item) => {
          item.commodityRelationDetailList = item.skuBriefs.map((i) => {
            this.$set(i, 'izEnable', '1');
            this.$set(i, 'leaveStock', '');
            this.$set(i, 'skuStock', i.stock);
            this.$set(i, 'skuPrice', i.dmMallPrice);
            this.$set(i, 'skuBarcode', i.specCode);
            this.$set(i, 'skuSupplyPrice', i.price);
            this.$set(i, 'bizTag', '');
            if (this.izPointMultiple === '1') {
              this.$set(i, 'point', Math.ceil(this.pointMultiple * i.dmMallPrice));
            } else {
              this.$set(i, 'point', '');
            }
            return i;
          });
          this.$set(item, 'imgUrl', !item.skuCommodityVO ? item?.thumbnailUrl || '' : item?.skuCommodityVO?.thumbnailUrl || '');
          this.$set(item, 'bizName', item.name);
          this.$set(item, 'giftNum', '');
          item.detailTotalStock = item.stock;
          return item;
        });
      }
      if (listChecked.length < 1) {
        this.$message({
          message: '添加商品不能为空',
          type: 'warning'
        });
        return;
      }
      const list = [...this.commodityList, ...listChecked];
      if (this.operation !== 'detail') {
        this.$emit('updateTable', list);
      }
      this.allChecked = false;
      this.dialogFormVisible = false;
      this.innerVisible = false;
    },
    handleDialogClose() {
      if (this.list.length) {
        this.list.map((item) => (item.isChecked = false));
      }
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
@import './styles';
</style>
