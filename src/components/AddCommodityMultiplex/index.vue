<!-- 选择分销商品 -  用于创建交易商品-->
<template>
  <div v-frag>
    <div class="add-commodity">
      <el-button @click="open" v-if="editable" :type="btnType" size="mini">选择商品</el-button>
      <span class="commodity-tip" v-if="multipleSelectionData.length && source === 'integralExchange'">共选择了{{ multipleSelectionData.length }}个商品，{{ multipleSelectionData.length }}个SKU</span>
    </div>
    <div class="result-table" v-if="multipleSelectionData.length" v-show="isShowCommodity">
      <el-table :data="multipleSelectionData" fit max-height="400" size="mini">
        <el-table-column align="center" label="商品名称" prop="commodityName"></el-table-column>
        <el-table-column align="center" label="商品规格" prop="spec" v-if="source !== 'violationRecord'"></el-table-column>
        <el-table-column align="center" label="商品条码" prop="barcode"></el-table-column>
        <el-table-column label="操作" width="80px" v-if="editable">
          <template slot-scope="scope">
            <el-button type="text" @click="remove(scope.row.id)" v-if="editable">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-dialog :append-to-body="true" class="el-dialog-box" :title="title" :visible.sync="dialogVisible" width="70%" top="50px">
      <instructions show-title is-hide>
        <template slot="title">
          <div>只能添加品牌档案里面分销商品里【启用】状态的商品，如果查不到，那么请先去品牌档案里面维护对应商品信息； <el-button type="text" @click="link">维护商品信息</el-button></div>
        </template>
      </instructions>
      <sy-normal-table v-bind="table" ref="table" />
    </el-dialog>
  </div>
</template>

<script>
import { productCategoryListByQuery } from '@/api/brand/commodity-set.js';
import dataDeal from '@/utils/dataDeal.js';
import { commodityListByCreateCommodity } from '@/api/commodity/index.js';
import { brandBriefListAll } from '@/api/brand/list';
import dict from '@/components/Common/dicts';

export default {
  name: 'AddCommodityMultiplex',
  data() {
    return {
      dataList: [],
      dialogVisible: false,
      originBrandList: [],
      brandList: [],
      productCategoryOptions: []
    };
  },
  model: {
    prop: 'multipleSelectionData',
    event: 'save'
  },
  props: {
    multipleSelectionData: {
      // 已选
      type: Array,
      default: () => []
    },
    isBrandStart: Boolean,
    // 默认多选
    multiple: {
      type: Boolean,
      default: true
    },
    // 商品品牌信息
    brandInfo: {
      type: Object,
      default: () => {}
    },
    // 品牌id
    brandId: {
      type: String,
      default: ''
    },
    // 是否可编辑
    editable: {
      type: Boolean,
      default: false
    },
    isShowCommodity: {
      type: Boolean,
      default: true
    },
    // 活动来源
    source: {
      type: String,
      default: 'integral'
    },
    // 积分计算方式
    izPointMultiple: String,
    // 积分兑换倍率
    pointMultiple: [String, Number]
  },
  components: {},
  computed: {
    btnType() {
      return ['integralExchange', 'violationRecord'].includes(this.source) ? 'primary' : 'text';
    },
    title() {
      return '选择商品';
    },
    table() {
      const that = this;
      return {
        initSearch: false,
        tableBind: {
          height: 400
        },
        filters() {
          return [
            {
              tag: 'sy-select',
              prop: 'brandCode',
              label: '品牌',
              bind: {
                placeholder: '请选择品牌',
                filterable: true,
                options: that.brandList,
                flashOptions: true,
                disabled: that.source === 'violationRecord'
              }
            },
            {
              tag: 'el-input',
              prop: 'commodityName',
              label: '商品名称',
              bind: {
                placeholder: '请输入商品名称'
              }
            },
            {
              tag: 'el-input',
              prop: 'barcodes',
              label: '商品条码',
              str2Arr: true,
              bind: {
                placeholder: '批量查询使用英文逗号分隔'
              }
            },
            {
              tag: 'el-input',
              prop: 'commodityCodes',
              label: '商品编码',
              str2Arr: true,
              bind: {
                placeholder: '批量查询使用英文逗号分隔'
              }
            },
            {
              tag: 'el-cascader',
              prop: 'productCategoryCodes',
              label: '产品品类',
              bind: {
                collapseTags: true,
                showAllLevels: false,
                options: that.productCategoryOptions,
                props: {
                  value: 'id',
                  label: 'name',
                  multiple: true,
                  expandTrigger: 'hover'
                }
              }
            },
            {
              tag: 'sy-select',
              prop: 'status',
              label: '商品状态',
              bind: {
                placeholder: '请选择',
                filterable: true,
                options: [
                  { label: '启用', value: '1' },
                  { label: '禁用', value: '0' }
                ]
              }
            },
            {
              tag: 'el-input',
              prop: 'productCode',
              label: 'SPU编码',
              bind: {
                placeholder: '请输入SPU编码'
              }
            },
            {
              tag: 'el-input',
              prop: 'productName',
              label: 'SPU名称',
              bind: {
                placeholder: '请输入SPU名称'
              }
            },
            {
              tag: 'sy-select',
              prop: 'commodityTypes',
              label: '贸易类型',
              bind: {
                multiple: true,
                placeholder: '请选择（可多选）',
                filterable: true,
                options: [
                  { label: '大贸', value: 'GENERAL_TRADE' },
                  { label: '海淘', value: 'CROSS_BORDER_TRADE' },
                  { label: '大贸海淘', value: 'GENERAL_AND_CROSS_BORDER_TRADE' }
                ]
              }
            },
            {
              prop: 'materialTypeCode',
              label: '物料类型',
              tag: 'sy-select',
              bind: {
                placeholder: '请选择物料类型',
                filterable: true,
                options: async () => dict('SAP_MATERIEL_TYPE')
              }
            }
          ];
        },
        btns() {
          return [
            {
              text: '批量添加',
              type: 'primary',
              bind: {
                disabled: false
              },
              async call({ selection }) {
                that.handleJoinALl(selection);
              }
            }
          ];
        },
        columns() {
          return [
            {
              type: 'selection',
              itemBind: {
                selectable: that.selectable
              }
            },
            {
              label: '商品名称',
              width: 180,
              render: (h, { row }) => (
                <div v-frag>
                  <div>{row.commodityName || '--'}</div>
                  {row.onlineSituation === 'OFFLINE' ? (
                    <el-button type="text" style="width: 100%; color: #ff0000">
                      主数据已下架
                    </el-button>
                  ) : (
                    ''
                  )}
                </div>
              )
            },
            {
              label: '商品规格',
              prop: 'spec',
              multiLine: 3
            },
            {
              label: '商品条码',
              prop: 'barcode',
              multiLine: 3
            },
            {
              label: '商品编码',
              prop: 'commodityCode',
              multiLine: 3
            },
            {
              label: '大贸供货价',
              width: 120,
              render: (h, { row }) => (
                <div v-frag>
                  <div>一件代发：{row.dropShippingSupplyPriceTrade || '--'}</div>
                  <div>采销：{row.purchaseSupplyPriceTrade || '--'}</div>
                </div>
              )
            },
            {
              label: '商城价',
              width: 120,
              render: (h, { row }) => (
                <div v-frag>
                  {(row.commodityType === 'GENERAL_TRADE' || row.commodityType === 'GENERAL_AND_CROSS_BORDER_TRADE') && <div>大贸商城价：{row.dmMallPrice || '--'}</div>}
                  {(row.commodityType === 'CROSS_BORDER_TRADE' || row.commodityType === 'GENERAL_AND_CROSS_BORDER_TRADE') && <div>海淘城价：{row.htMallPrice || '--'}</div>}
                </div>
              )
            },
            {
              label: '产品品类',
              prop: 'productCategoryName',
              multiLine: 3
            },
            {
              label: 'SPU编码/名称',
              render: (h, { row }) => (
                <div v-frag>
                  <div>编码：{row.productCode || '--'}</div>
                  <div>名称：{row.productName || '--'}</div>
                </div>
              )
            },
            {
              label: '贸易类型',
              prop: 'commodityTypeName',
              multiLine: 3
            },
            {
              label: '商品状态',
              render: (h, { row }) => (
                <div v-frag>
                  <div>{row.status === '1' ? '启用' : '禁用'}</div>
                </div>
              )
            },
            {
              label: '操作',
              type: 'btns',
              width: 120,
              itemBind: {
                align: 'center'
              },
              btns({ row }) {
                return [
                  {
                    hide: !that.commoditySet.has(row.id) || row.status === '0' || row.onlineSituation === 'OFFLINE',
                    text: '已添加',
                    type: 'text',
                    bind: {
                      disabled: true
                    }
                  },
                  {
                    hide: that.commoditySet.has(row.id) || row.status === '0' || row.onlineSituation === 'OFFLINE',
                    text: '添加',
                    type: 'text',
                    call() {
                      that.handleJoin(row);
                    }
                  },
                  {
                    hide: !(row.status === '0' || row.onlineSituation === 'OFFLINE'),
                    text: '不可添加',
                    type: 'text',
                    bind: {
                      disabled: true
                    }
                  }
                ];
              }
            }
          ];
        },
        async search({ filtersValue, pageFilter }) {
          const orderBrandType = this.brandInfo?.orderBrandType || '';
          const productCategoryCodes = (filtersValue?.productCategoryCodes ?? []).flatMap((subArray) => subArray.slice(-1));
          const res = await commodityListByCreateCommodity({
            ...pageFilter,
            data: {
              ...filtersValue,
              orderBrandType,
              productCategoryCodes
            }
          });
          const { list = [], total = 0 } = res?.data || {};
          return {
            list,
            total
          };
        }
      };
    },
    // 获取已选缓存对象的id
    commoditySet() {
      return new Set(this.multipleSelectionData.map(({ id }) => id));
    },
    brandCode() {
      return this.originBrandList.find(({ id }) => id === this.brandId)?.code || '';
    }
  },
  created() {
    this.getDict();
  },
  mounted() {
    this.getProductCategory();
  },
  watch: {
    brandInfo(val) {
      if (!val) {
        return;
      }
      const { code, orderBrandType } = val;
      if (!code || !orderBrandType) {
        return;
      }
      const { originBrandList } = this;
      const arr = originBrandList.filter((item) => item.orderBrandType === orderBrandType);
      this.brandList = arr.map(({ code, name }) => ({
        label: name,
        value: code
      }));
    }
  },
  methods: {
    getDict() {
      // 限制必须在商家端显示的品牌
      brandBriefListAll().then((res) => {
        const arr = res?.data ?? [];
        this.originBrandList = arr;
        this.brandList = arr.map(({ code, name }) => ({
          label: name,
          value: code
        }));
      });
    },
    // 获取产品分类
    getProductCategory() {
      productCategoryListByQuery().then((res) => {
        this.productCategoryOptions = dataDeal.list2Tree(res.data, 'id', 'parentId', 'children', '0');
      });
    },
    // 删除已选择
    remove(commodityId) {
      const list = [...this.multipleSelectionData];
      const idx = list.findIndex(({ id }) => id === commodityId);
      list.splice(idx, 1);
      this.$emit('save', list);
    },
    link() {
      this.dialogVisible = false;
      this.$router.push('/brand/list');
    },
    open() {
      // 如果是积分兑换
      if (this.source === 'integralExchange' && this.izPointMultiple === '1' && this.pointMultiple <= '0') {
        this.$message.error('请先设置兑换倍率');
        return;
      }
      // 如果是违规记录
      if (this.source === 'violationRecord' && !this.brandId) {
        this.$message.error('请先选择品牌');
        return;
      }
      this.dialogVisible = true;
      this.setFiltersValue();
      this.refreshList();
    },
    // 设置过滤条件
    setFiltersValue() {
      this.$nextTick(() => {
        const { source, brandCode } = this;
        if (source === 'integralExchange') {
          this.$refs.table.setFiltersValue(['CROSS_BORDER_TRADE', 'GENERAL_AND_CROSS_BORDER_TRADE'], 'commodityTypes');
        } else if (source === 'violationRecord') {
          this.$refs.table.setFiltersValue(brandCode, 'brandCode');
        }
      });
    },
    // 单个添加
    handleJoin(rowData) {
      let list = [...this.multipleSelectionData];
      const pushData = { ...rowData };
      // 积分兑换活动
      if (this.source === 'integralExchange') {
        const obj = {
          leaveStock: '',
          point: '',
          izEnable: '1',
          skuPrice: pushData.htMallPrice,
          bizCode: pushData.barcode
        };
        if (this.izPointMultiple === '1') {
          this.$set(obj, 'point', Math.ceil(this.pointMultiple * pushData.htMallPrice));
        } else {
          this.$set(obj, 'point', '');
        }
        pushData.commodityRelationDetailList = [obj];
        pushData.name = pushData.commodityName;
        pushData.thumbnailUrl = pushData?.logoUrl;
        pushData.imgUrl = pushData?.logoUrl || '';
        pushData.bizName = pushData.commodityName;
        pushData.bizTag = '';
        pushData.statusName = pushData.status === '1' ? '启用' : '禁用';
      }
      if (!this.multiple) {
        this.dialogVisible = false;
        list = [pushData];
      } else {
        list.push(pushData);
      }
      this.$emit('save', list);
    },
    // 批量添加
    handleJoinALl(selection) {
      if (!selection.length) {
        this.$message({
          message: '添加商品不能为空',
          type: 'warning'
        });
        return;
      }
      const listChecked = selection.filter((item) => !this.commoditySet.has(item.id));
      // 积分兑换活动
      if (this.source === 'integralExchange') {
        listChecked.map((item) => {
          const obj = {
            leaveStock: '',
            point: '',
            izEnable: '1',
            skuPrice: item.htMallPrice,
            bizCode: item.barcode
          };
          if (this.izPointMultiple === '1') {
            obj.point = Math.ceil(this.pointMultiple * item.htMallPrice);
          } else {
            obj.point = '';
          }
          this.$set(item, 'commodityRelationDetailList', [obj]);
          this.$set(item, 'bizName', item.commodityName);
          this.$set(item, 'imgUrl', item?.commodityExtendInfoVO?.logoUrl || '');
          this.$set(item, 'bizTag', '');
          item.name = item.commodityName;
          item.thumbnailUrl = item?.commodityExtendInfoVO?.logoUrl;
          item.statusName = item.status === '1' ? '启用' : '禁用';
          return item;
        });
      }
      const list = [...this.multipleSelectionData, ...listChecked];
      this.$emit('save', list);
      this.dialogVisible = false;
    },
    selectable(row) {
      if (row.status === '1' && row.onlineSituation !== 'OFFLINE') {
        return true; // 不禁用
      } else {
        return false; // 禁用
      }
    },
    // 刷新
    refreshList() {
      this.$nextTick(() => {
        const ref = this.$refs.table;
        ref && ref.handlerSearch();
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.result-table {
  margin-top: 12px;
}
.commodity-tip {
  margin-left: 10px;
  font-size: 12px;
}
</style>
