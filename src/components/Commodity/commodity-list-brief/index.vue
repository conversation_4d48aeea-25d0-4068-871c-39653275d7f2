<template>
  <div>
    <filter-form :options="_filterFormOptions" :lineColNum="3" :basicData="filterParams" @query="query" ref="filterForm"></filter-form>

    <!-- 列表展示 -->
    <table-exhibition height="400px" @query="query" :options="_tableOptions" @selection-change="handleSelectionChange" :table="table" ref="table" v-loading="loading">
      <!-- 选择栏 -->
      <el-table-column type="selection" :selectable="(row) => row.onlineSituation !== 'OFFLINE'" width="55" slot="column_prepend"> </el-table-column>

      <el-table-column slot="column_commodityName" slot-scope="options" v-bind="options">
        <template slot-scope="{ row }">
          <div>{{ row.commodityName || '--' }}</div>
          <div v-if="row.onlineSituation === 'OFFLINE'"><el-button type="text" style="width: 100%;color: #ff0000;">主数据已下架</el-button></div>
        </template>
      </el-table-column>
    </table-exhibition>
  </div>
</template>

<script>
import FilterForm from '@/components/Form/FilterForm';
import TableExhibition from '@/components/Table/TableExhibition';
import { FilterFormOptions, TableOptions } from './config';
import { commodityListBrief } from '@/api/commodity/index';

export default {
  // 分销商品
  name: 'commodity-list',
  components: { FilterForm, TableExhibition },
  data() {
    return {
      loading: false,
      table: {},
      previousMultipleSelection: [], // 之前已选择的数据：切换筛选条件之前选择的
      multipleSelection: [] // 当前筛选条件下选择的数据
    };
  },
  props: {
    filterParams: {
      type: Object,
      default() {
        return {};
      }
    },
    // 已选择数据
    selectioned: {
      type: Array
    }
  },
  created() {
    this.init();
  },
  mounted() {
    this.params = {
      data: this.$refs.filterForm.getParams(),
      ...this.$refs.table.getParams()
    };
  },
  computed: {
    selection() {}
  },
  methods: {
    async init() {
      this._filterFormOptions = FilterFormOptions;
      this._tableOptions = TableOptions;
    },
    refresh() {
      this.multipleSelection = [];
      this.previousMultipleSelection = [...this.selectioned];
      this.$refs.filterForm.resetData();
    },
    query(params = {}) {
      Object.assign(this.params, params);

      // 商品编码，商品条码：查询支持输入多个
      ['codes', 'barcodes'].forEach((k) => {
        const v = this.params.data[k];
        if (v) {
          this.params.data[k] = v.split(',').filter((i) => !!i);
        }
      });

      this.loading = true;
      commodityListBrief(this.params)
        .then((res) => {
          this.table = res.data;

          this.selectioned && this.getSelection(true);
        })
        .finally(() => {
          this.loading = false;
        });
    },
    getSelection(isRenew) {
      // 记录之前的数据
      this.multipleSelection.forEach((i) => {
        if (!this.previousMultipleSelection.find(({ id }) => i.id === id)) {
          this.previousMultipleSelection.push(i);
        }
      });
      // 勾选已经选中的
      if (isRenew) {
        this.$nextTick(() => {
          (this.table?.list ?? []).forEach((i) => {
            const index = this.previousMultipleSelection.findIndex(({ id }) => i.id === id);
            if (index !== -1) {
              this.$refs.table.$refs.table.toggleRowSelection(i, true);
              this.previousMultipleSelection.splice(index, 1);
            }
          });
        });
      }
      return this.previousMultipleSelection;
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    batchAddCommodity() {
      if (!this.multipleSelection?.length) {
        this.$message.warning('至少选择一条数据');
        return;
      }

      this.addCommodity(this.multipleSelection);
    }
  }
};
</script>

<style>
</style>
