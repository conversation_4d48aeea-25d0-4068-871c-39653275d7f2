import dict from '@/components/Common/dicts';
import { productCategoryListByQuery } from '@/api/brand/commodity-set.js';
import dataDeal from '@/utils/dataDeal.js';

export const FilterFormOptions = [
  {
    prop: 'commodityCodes',
    label: '商品编码',
    component: 'input',
    placeholder: '批量查询使用英文逗号分隔'
  },
  {
    prop: 'barcodes',
    label: '商品条码',
    component: 'input',
    placeholder: '批量查询使用英文逗号分隔'
  },
  {
    prop: 'commodityName',
    label: '商品名称',
    component: 'input',
    placeholder: '请输入商品名称'
  },
  {
    prop: 'productName',
    label: 'SPU名称',
    component: 'input',
    placeholder: '请输入SPU名称'
  },
  {
    prop: 'productCategoryCodes',
    label: '产品品类',
    component: 'cascader',
    props: {
      value: 'id',
      label: 'name',
      multiple: true,
      expandTrigger: 'hover'
    },
    leafOnly: true,
    collapseTags: true,
    showAllLevels: false,
    options: productCategoryListByQuery().then((res) => {
      return dataDeal.list2Tree(res.data, 'id', 'parentId', 'children', '0');
    })
  },
  {
    prop: 'productPosition',
    label: '产品矩阵',
    component: 'select',
    options: dict('PRODUCT_POSITION')
  },
  {
    prop: 'izProductPosition',
    label: '是否添加产品矩阵',
    component: 'select',
    options: dict('COMMON_ISFLAG')
  }
];

export const TableOptions = [
  { prop: 'commodityName', label: '商品名称', width: 120, isCustom: true },
  { prop: 'spec', label: '商品规格' },
  { prop: 'barcode', label: '商品条码' },
  { prop: 'commodityCode', label: '商品编码' },
  { prop: 'productCategoryName', label: '产品品类' },
  { prop: 'productName', label: 'SPU名称' },
  { prop: 'productPositionName', label: '产品矩阵' }
];
