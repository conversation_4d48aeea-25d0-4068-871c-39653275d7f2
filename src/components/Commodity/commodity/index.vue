<template>
  <div>
    <filter-form :options="_filterFormOptions" :lineColNum="3" :basicData="filterParams" @query="query" ref="filterForm"></filter-form>

    <action-bar style="margin: 16px 0" :itemOptions="_barOptions" @actionBarClick="({ id }) => this[id] && this[id]()"></action-bar>
    <!-- 列表展示 -->
    <table-exhibition height="400px" @query="query" :options="_tableOptions" @selection-change="handleSelectionChange" :table="table" ref="table" v-loading="loading">
      <!-- 选择栏 -->
      <el-table-column type="selection" :selectable="selectable" width="55" slot="column_prepend"> </el-table-column>
      <!-- 操作栏 -->
      <el-table-column label="操作" width="100" slot="column_append">
        <template slot-scope="{ row }">
          <slot :row="row">
            <el-button @click="addCommodity([row])" type="text" size="small">添加</el-button>
          </slot>
        </template>
      </el-table-column>
    </table-exhibition>
  </div>
</template>

<script>
import FilterForm from '@/components/Form/FilterForm';
import ActionBar from '@/components/Common/ActionBar';
import TableExhibition from '@/components/Table/TableExhibition';
import { FilterFormOptions, BarOptions, TableOptions } from './config';
import { commodityList } from '@/api/brand/commodity-set.js';
import cloneDeep from 'lodash/cloneDeep';

export default {
  // 分销商品
  name: 'commodity-list',
  components: { FilterForm, ActionBar, TableExhibition },
  data() {
    return {
      loading: false,
      table: {},
      multipleSelection: []
    };
  },
  props: {
    filterParams: {
      type: Object,
      default() {
        return {};
      }
    },
    commoditys: {
      type: Array,
      default() {
        return [];
      }
    },
    selectable: Function
  },
  created() {
    this.init();
  },
  mounted() {
    this.params = {
      data: this.$refs.filterForm.getParams(),
      ...this.$refs.table.getParams()
    };
  },
  computed: {},
  methods: {
    async init() {
      this._filterFormOptions = FilterFormOptions;
      this._tableOptions = TableOptions;
      this._barOptions = BarOptions;
    },
    refresh() {
      this.$refs.filterForm.resetData();
      this.multipleSelection = [...this.commoditys];
    },
    query(params = {}) {
      Object.assign(this.params, params);

      params = cloneDeep(this.params);
      // 商品编码，商品条码：查询支持输入多个
      ['codes', 'barcodes'].forEach((k) => {
        const v = params.data[k];
        if (v) {
          params.data[k] = v.split(',').filter((i) => !!i);
        }
      });

      this.loading = true;
      commodityList(params)
        .then((res) => {
          this.table = res.data;
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    addCommodity(commoditys) {
      this.$emit('onCommodityChange', commoditys);
    },
    batchAddCommodity() {
      if (!this.multipleSelection?.length) {
        this.$message.warning('至少选择一条数据');
        return;
      }

      this.addCommodity(this.multipleSelection);
    }
  }
};
</script>

<style>
</style>
