<template>
  <div>
    <el-button type="text" @click="openCommodityDialog">{{ title }}</el-button>

    <el-table :data="selectionCommodity">
      <el-table-column v-bind="i" v-for="i of tableColumns" :key="i.prop"></el-table-column>

      <el-table-column fixed="right" label="操作" width="60">
        <template slot-scope="scope">
          <el-button type="text" @click="deleteCommodity(scope.$index)">移除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog :title="title" :visible.sync="dialogVisible" :width="dialogWidth">
      <Commodity ref="commodity" :selectioned="selectionCommodity" :filterParams="filterParams"></Commodity>

      <div slot="footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="save">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Commodity from '../commodity-list-brief';
export default {
  // 分销商品
  name: 'commodity-select',
  components: { Commodity },
  data() {
    return {
      dialogVisible: false,
      tableData: []
    };
  },
  model: {
    prop: 'selectionCommodity',
    event: 'updateSelectionCommodity'
  },
  props: {
    title: {
      type: String,
      default: '选择商品'
    },
    dialogWidth: {
      type: String,
      default: '60%'
    },
    selectionCommodity: {
      type: Array,
      default() {
        return [];
      }
    },
    filterParams: Object
  },
  computed: {
    tableColumns() {
      return [
        { prop: 'commodityName', label: '商品名称', width: 120 },
        { prop: 'spec', label: '商品规格' },
        { prop: 'barcode', label: '商品条码' },
        { prop: 'commodityCode', label: '商品编码' },
        { prop: 'productCategoryName', label: '产品品类' },
        { prop: 'productName', label: 'SPU名称' },
        { prop: 'productPositionName', label: '产品矩阵' }
      ];
    }
  },
  methods: {
    async openCommodityDialog() {
      this.dialogVisible = true;

      // 刷新弹框商品列表
      if (!this.$refs.commodity) await this.$nextTick();
      this.$refs.commodity.refresh();
    },
    save() {
      this.$emit('updateSelectionCommodity', this.$refs.commodity.getSelection());
      this.dialogVisible = false;
    },
    deleteCommodity(index) {
      this.selectionCommodity.splice(index, 1);
      this.$emit('updateSelectionCommodity', this.selectionCommodity);
    }
  }
};
</script>

<style>
</style>