<template>
  <div v-frag>
    <sy-button text="新增竞品品牌" type="text" :disabled="isDisabled" :style="buttonStyle" :call="openDialog" />
    <el-dialog title="新增竞品品牌" :visible.sync="dialogVisible" width="480px" append-to-body @close="reset">
      <el-form :model="form" ref="validateForm" label-width="80px" label-position="left">
        <el-form-item label="品牌名称:" v-for="(item, index) in form.brandList" :prop="'brandList.' + index + '.name'" :key="item.key" :rules="[{ required: true, message: '请输入品牌名称', trigger: 'blur' }]">
          <el-input v-model.trim="item.name" placeholder="请输入品牌名称" maxlength="20" show-word-limit clearable></el-input>
          <el-button v-if="index !== 0" @click="removeRow(item)">删除</el-button>
        </el-form-item>
      </el-form>
      <el-button type="primary" @click="addRow">添加一行</el-button>
      <template slot="footer">
        <el-button @click="closeDialog">取消</el-button>
        <button-hoc type="primary" :loading="loading" @click="submitForm">确定</button-hoc>
      </template>
    </el-dialog>
  </div>
</template>
<script>
import { brandCompetitorInfoCreate } from '@/api/brand/brand-competitor-info';
import { cloneDeep } from 'lodash';
const initForm = {
  brandList: [
    {
      key: Date.now(),
      name: ''
    }
  ]
};
export default {
  name: 'AddCompeteBrands',
  props: {
    isMargin: {
      type: Boolean,
      default: true
    },
    isDisabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dialogVisible: false,
      loading: false,
      form: cloneDeep(initForm)
    };
  },
  computed: {
    buttonStyle() {
      return this.isMargin ? { marginLeft: '12px' } : {};
    }
  },
  methods: {
    // 打开弹窗
    openDialog() {
      this.dialogVisible = true;
    },
    // 关闭弹窗
    closeDialog() {
      this.dialogVisible = false;
    },
    //  添加一行
    addRow() {
      this.form.brandList.push({
        key: Date.now(),
        name: ''
      });
    },
    // 删除当前行
    removeRow(item) {
      const index = this.form.brandList.indexOf(item);
      if (index !== -1) {
        this.form.brandList.splice(index, 1);
      }
    },
    // 重置
    reset() {
      this.$refs['validateForm'].resetFields();
      this.form = cloneDeep(initForm);
    },
    submitForm() {
      this.$refs['validateForm'].validate((valid) => {
        if (!valid) {
          return;
        }
        this.loading = true;
        const params = this.form.brandList.map((item) => {
          return {
            name: item.name
          };
        });
        brandCompetitorInfoCreate(params)
          .then(() => {
            this.$message.success('新增成功');
            this.closeDialog();
            this.$emit('refresh');
          })
          .finally(() => {
            this.loading = false;
          });
      });
    }
  }
};
</script>

<style scoped lang="scss">
::v-deep {
  .el-dialog__body {
    max-height: 400px;
    overflow-y: auto;
  }
  .el-input {
    width: 240px;
    margin-right: 12px;
  }
}
</style>
