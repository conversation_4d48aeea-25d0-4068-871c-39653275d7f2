<template>
  <div>
    <el-button type="text" @click="show">选择员工</el-button>
    <el-dialog title="选择员工" :visible.sync="isShow">
      <form @submit.prevent="onSearch" class="filter-container">
        <div>
          <span class="label">员工姓名:</span>
          <el-input class="content" v-model="filter.name" clearable></el-input>
        </div>
        <div>
          <span class="label">手机号:</span>
          <el-input
            class="content"
            v-model="filter.linkPhone"
            clearable
          ></el-input>
        </div>
        <div>
          <span class="label">登录名:</span>
          <el-input
            class="content"
            v-model="filter.loginName"
            clearable
          ></el-input>
        </div>
        <div>
          <span class="label">角色:</span>
          <el-select
            class="content"
            v-model="filter.roleId"
            clearable
            filterable
            :loading="rolesLoading"
          >
            <el-option
              v-for="item in roles"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            ></el-option>
          </el-select>
        </div>
        <div>
          <span class="label">人员分组:</span>
          <el-select
            class="content"
            v-model="filter.groupId"
            clearable
            filterable
          >
            <el-option
              v-for="item in groupList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </div>
        <div>
          <el-button type="primary" native-type="submit">查询</el-button>
          <el-button @click="onReset">重置</el-button>
        </div>
      </form>
      <el-table
        ref="table"
        :data="list"
        v-loading.body="listLoading"
        element-loading-text="加载中"
        fit
        highlight-current-row
      >
        <el-table-column
          align="center"
          label="员工姓名"
          prop="name"
        ></el-table-column>
        <el-table-column
          align="center"
          label="手机号"
          prop="linkPhone"
        ></el-table-column>
        <el-table-column
          align="center"
          label="登录名"
          prop="loginName"
        ></el-table-column>
        <el-table-column align="center" label="分组">
          <template slot-scope="scope">{{
            scope.row.groupName || '/'
          }}</template>
        </el-table-column>
        <el-table-column align="center" label="角色">
          <template slot-scope="scope">{{ scope.row | filterRoles }}</template>
        </el-table-column>
        <el-table-column
          align="center"
          label="添加人"
          prop="createByName"
        ></el-table-column>
        <el-table-column align="center" label="添加时间">
          <template slot-scope="scope">{{
            scope.row.createDate | parseTime
          }}</template>
        </el-table-column>
        <el-table-column align="center" label="操作" fixed="right">
          <template slot-scope="scope">
            <el-button
              type="text"
              @click="choose(scope.row)"
              style="margin-left: 0px"
              >选择</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          background
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="pageNo"
          :page-sizes="[10, 20, 30, 40,50,100]"
          :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          :disabled="listLoading"
        ></el-pagination>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listPage, listAllRoles } from '@/api/setting/shop/employee';
import { listAll } from '@/api/distributorManagement/customer/list';
import pick from 'lodash/pick';
import pickBy from 'lodash/pickBy';
export default {
  name: 'AddUser',
  props: {
    dialogTableVisible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    const initFilter = {
      name: '',
      linkPhone: '',
      roleId: '',
      loginName: '',
      groupId: ''
    };
    return {
      popoverVisible: {},
      popoverLoading: {},
      list: null,
      listLoading: true,
      pageNo: 1,
      pageSize: 10,
      total: 0,
      filter: { ...initFilter },
      initFilter,
      roles: [],
      groupList: [],
      rolesLoading: true
    };
  },
  filters: {
    filterRoles(user = {}) {
      const { roles = [] } = user;
      if (user.isAdmin === '1') {
        return '店长';
      }
      return roles
        .filter(({ extTenantId }) => extTenantId !== 'defaultExtTenantId')
        .map(({ name }) => name)
        .join(',');
    }
  },
  computed: {
    data() {
      return pickBy(this.filter, (val) => !!val);
    },
    // 弹窗是否展示
    isShow: {
      set(val) {
        this.$emit('changeVisible', val);
      },
      get() {
        return this.dialogTableVisible;
      }
    }
  },
  methods: {
    // 获取员工分组列表
    fetchGroup() {
      listAll({}).then((rs) => {
        const res = rs.data.map((item) => ({
          value: item.id,
          label: item.name
        }));
        this.groupList = res;
      });
    },
    onSearch() {
      this.pageNo = 1;
      this.fetchDataList();
    },
    onReset() {
      this.pageNo = 1;
      this.filter = { ...this.initFilter };
      this.fetchDataList();
    },
    // 当点击选择员工按钮时显示弹窗，并请求数据
    show() {
      this.isShow = true;
      this.fetchDataList();
      this.fetchGroup();
      listAllRoles()
        .then((res) => {
          this.roles = res.data;
        })
        .finally(() => {
          this.rolesLoading = false;
        });
    },
    // 选择员工并将数据传给父组件
    choose(data) {
      this.$emit('getData', data);
      this.isShow = false;
    },
    fetchDataList() {
      this.listLoading = true;
      const listQuery = pick(this, ['pageNo', 'pageSize', 'data']);
      listPage(listQuery)
        .then((response) => {
          this.list = response.data.list;
          this.total = response.data.total;
        })
        .finally(() => {
          this.listLoading = false;
        });
    },
    handleSizeChange: function (val) {
      const self = this;
      self.pageSize = val;
      self.pageNo = 1;
      self.fetchDataList();
    },
    handleCurrentChange: function (val) {
      const self = this;
      self.pageNo = val;
      self.fetchDataList();
    }
  }
};
</script>

<style lang="scss" scoped>
.filter-container {
  display: flex;
  flex-flow: row wrap;
  margin: 0 -24px 8px -24px;
  & > div {
    display: flex;
    min-width: 25%;
    margin-bottom: 16px;
    box-sizing: border-box;
    padding: 0 24px;
    .label {
      line-height: 36px;
      white-space: nowrap;
      margin-right: 16px;
    }
    .content {
      flex: 1;
    }
    &.btns {
      display: block;
      width: 100%;
      text-align: right;
    }
  }
}
</style>
