import Vue from 'vue';
import ElementUI from 'element-ui';
import lowcodelib from 'lowcodelib';
ElementUI.Select.props.filterable = {
  // 全局为设置Element 组件 下拉搜索框 filterable 为 true
  type: Boolean,
  default: true
};
import locale from 'element-ui/lib/locale/lang/zh-CN'; // 中文
import frag from 'vue-frag';
Vue.directive('frag', frag);
import '@/styles/index.scss'; // 全局 css
import App from './App';
import router from '@/router';
import store from './store';
import '@/directives';
import './public-path';
import globalComponent from '@/components/globalComponent'; // 执行全局组件注册
Vue.use(globalComponent);
import { $u_throttle } from '@/utils/enhance.js';
import '@/icons'; // 图表
import * as filters from './filters'; // filter
import './console';
import VueClipboard from 'vue-clipboard2'; // 全局复制
import api from './api/index';
import Mixin from '@/mixins'; // 全局混入
Vue.mixin(Mixin);
import '@/vue-plugins';
window.$vue = new Vue();
window.Vue = Vue;
require('./assets/js/xtools.min.js');
require('./assets/js/xview.js');
Vue.prototype.$api = api;
Vue.prototype.$plugins = window.$plugins;
Vue.prototype.$u_throttle = $u_throttle; // 全局节流
Vue.prototype.$bus = new Vue(); // 中央总线
// eslint-disable-next-line no-unused-vars
Vue.use(VueClipboard);
Vue.use(ElementUI, {
  size: 'small', // set element-ui default size
  locale
});
Vue.use(lowcodelib);

import Viewer from 'v-viewer';
import 'viewerjs/dist/viewer.css';
import '@/styles/variable-sy.css';
import 'lowcodelib/theme/css/index.css';
Vue.use(Viewer, {
  defaultOptions: {
    zIndex: 9999
  }
});
Viewer.setDefaults({
  Options: {
    inline: true,
    button: true,
    navbar: true,
    title: true,
    toolbar: true,
    tooltip: true,
    movable: true,
    zoomable: true,
    rotatable: true,
    scalable: true,
    transition: true,
    fullscreen: true,
    keyboard: true,
    url: 'data-source'
  }
});

// 注入全局的filter
Object.keys(filters).forEach((key) => {
  Vue.filter(key, filters[key]);
});

Vue.config.productionTip = false;
Vue.config.keyCodes = {
  f11: 122
};
window._store = store;
// 传入路由过滤的信息
async function setUserInfoFn() {
  const {
    user_info,
    userToken: { sessionId, userToken }
  } = window.QIANKUN_DATA;
  localStorage.setItem('SOYOUNG_ZG_SESSION_ID', sessionId);
  localStorage.setItem('SOYOUNG_ZG_TOKEN', userToken);
  // btnSet 用户菜单,这里初始化赋值，是为了提升性能，减少authority组件内部循环遍历
  window.btnSet = new Set(user_info.menus.map((it) => it.code));
  // 拿用户信息
  const USER_INFO = {
    ...user_info,
    sessionId,
    userToken,
    csrfToken: userToken
  };
  window.USER_INFO = USER_INFO;
  Vue.prototype.USER_INFO = USER_INFO;
  initDictSort();
  store.dispatch('uniLogin', USER_INFO);
}

// 处理字典排序
function initDictSort() {
  // 检查 window.QIANKUN_DATA 是否存在以及其中的 dict 属性是否存在
  if (!window.QIANKUN_DATA ||!Array.isArray(window.QIANKUN_DATA.dict)) {
    return;
  }
  // 复制一份 dict 数组，避免修改原始数组
  const dict = [...window.QIANKUN_DATA.dict];
  // 先处理字典排序：升序
  dict.sort((a, b) => {
    // 检查 sort 属性是否为数字，避免 NaN
    const sortA = typeof a.sort === 'number' ? a.sort : 0;
    const sortB = typeof b.sort === 'number' ? b.sort : 0;
    return sortA - sortB;
  });

  // 使用 reduce 方法来构建字典映射
  const dictMap = dict.reduce((acc, it) => {
    if (!acc[it.type]) {
      acc[it.type] = [];
    }
    acc[it.type].push({ label: it.label, value: it.value });
    return acc;
  }, {});

  // 检查 Vue 是否存在
  if (typeof Vue !== 'undefined') {
    Vue.prototype.$dict = Object.freeze(dictMap);
  }
}

async function setStaffGroup() {
  await api.staffGetInfo().then(async (res) => {
    if (res.code === '0' && res.data) {
      store.dispatch('setStaffInfoId', res.data);
      store.dispatch('setGroupId', res.data.groupId);
    }
  });
}

// 修复
let app;
async function init(props = {}) {
  const { container } = props;
  setUserInfoFn();
  await setStaffGroup();
  app = new Vue({
    router,
    store,
    render: (h) => h(App)
  }).$mount(container ? container.querySelector('#app') : '#app');
  fixDevTools();
}
// 修复dev-tools无法调试问题
function fixDevTools() {
  if (process.env.NODE_ENV === 'development') {
    const instanceDiv = document.createElement('div');
    instanceDiv.id = 'vueDevtools';
    instanceDiv.__vue__ = app;
    document.body.appendChild(instanceDiv);
  }
}

if (!window.__POWERED_BY_QIANKUN__) {
  init();
}
if (window.__POWERED_BY_QIANKUN__) {
  // eslint-disable-next-line no-undef
  __webpack_public_path__ = window.__INJECTED_PUBLIC_PATH_BY_QIANKUN__ + 'soyoung-zg/';
}
// --------- 微前端生命周期函数------------//
export async function bootstrap() {}

let promise;
export async function mount(props) {
  Vue.prototype.$onGlobalStateChange = props.onGlobalStateChange;
  promise = await init(props);

  // 修复dev-tools无法调试问题
  window.__QIANKUN_SUB_APP_VM__ = app;
}

export async function update(obj) {
  await promise;
  obj.run(window, Vue, app);
  const originalBack = Vue.prototype.$back; // 返回到上一页面
  const originalRedirect = Vue.prototype.$redirect; // 关闭当前页面，打开path
  // 重写微前端back方法
  Vue.prototype.$back = function (location) {
    if (typeof location === 'string') {
      return originalRedirect.call(this, location);
    }
    if (Object.prototype.toString.call(location) === '[object Object]' && location.path) {
      return originalRedirect.call(this, location.path);
    }
    return originalBack.call(this, location);
  };
}

export async function unmount() {
  if (process.env.NODE_ENV === 'development') {
    document.getElementById('vueDevtools').remove();
  }
}
