// 测试时间计算函数
export function testTimer(date1, date2) {
  const arr = [60, 60];
  const res = [];
  const hour = arr.reduce((acc, cur) => {
    res.push(acc % cur);
    return acc / cur;
  }, (date2 - date1) / 1000);
  const [second, minute] = res;
  if (hour) {
    return `${hour}小时${minute}分钟`;
  }
  if (minute) {
    return `${minute}分钟${second}秒`;
  }
  return `${second}秒`;
}

testTimer(1575603824000, 1575620097906);
