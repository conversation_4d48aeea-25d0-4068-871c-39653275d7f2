.table-container {

  ::v-deep .filter-list {
    display: flex;
    flex-wrap: wrap;
    .line {
      width: 100%;
      display: flex;
      line-height: 38px;
      justify-content: space-between;
      padding: 20px;
      .time {
        font-size: 16px;
      }
    }
    .filter-item {
      &.full {
        width: 100%;
      }
      width: 33%;
      font-size: 14px;
      box-sizing: border-box;
      display: flex;
      margin-bottom: 16px;
      padding: 0 24px;
      text-align: right;
      flex-wrap: wrap;

      .label {
        padding-right: 16px;
        white-space: nowrap;
        display: inline-block;
        line-height: 32px;
        min-width: 80px;
        flex: none;
      }
      .el-range-editor--medium.el-input__inner {
        width: calc(100% - 80px);
      }

      .content {
        flex: 1;
        color: #606266;
        text-align: left;
        line-height: 32px;
        width: calc(100% - 80px);
      }
      .indicators {
        width: 100%;
        margin-left: 80px;
        line-height: 32px;
        text-align: left;
        .el-checkbox {
          margin-left: 0;
          margin-right: 30px;
        }
      }
    }
  }
}
