@mixin clearfix {
  &:after {
    content: '';
    display: table;
    clear: both;
  }
}

@mixin scrollBar {
  &::-webkit-scrollbar-track-piece {
    background: #d3dce6;
  }
  &::-webkit-scrollbar {
    width: 6px;
  }
  &::-webkit-scrollbar-thumb {
    background: #99a9bf;
    border-radius: 20px;
  }
}

@mixin relative {
  position: relative;
  width: 100%;
  height: 100%;
}

@mixin input {
  border: 1px solid #dcdfe6;
  padding: 6px;
  width: 100px;
  border-radius: 3px;
  outline: none;
  @include transition(border-color, box-shadow);
  &:hover {
    border-color: #c0c4cc;
  }
  &:focus {
    border: 1px solid var(--color-primary);
    box-shadow: 0 0 5px #bdf;
  }
  &.error {
    border-color: #f44;
    box-shadow: none;
  }
  &[disabled='disabled'] {
    background-color: #f5f7fa;
    border-color: #e4e7ed;
    color: #c0c4cc;
    cursor: not-allowed;
  }
}

@mixin textInput {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 0 15px;
  &:hover {
    border-color: #c0c4cc;
  }
  &:focus {
    border-color: var(--color-primary);
    outline: none;
  }
}

@mixin transition($attr...) {
  transition: $attr 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
}
