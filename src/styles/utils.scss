// 背景颜色
.bg-base {
  background-color: var(--background-color-base);
}
// 文字颜色
.color-primary {
  color: var(--color-primary);
}
.color-success {
  color: var(--color-success);
}
.color-warning {
  color: var(--color-warning);
}
.color-danger {
  color: var(--color-danger);
}
.color-info {
  color: var(--color-info);
}
.color-text-primary {
  color: var(--color-text-primary);
}
.color-text-secondary {
  color: var(--color-text-secondary);
}
.color-text-placeholder {
  color: var(--color-text-placeholder);
}

// 超出显示省略号公共样式
.commo-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
// 超出显示2行省略号公共样式
.commo-ellipsis-2 {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
// 超出显示3行省略号公共样式
.commo-ellipsis-3 {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  word-break: break-all;
}
// 文字对齐
.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

// 定义字体行高
.leading-small {
  line-height: 1.25;
}
.leading-medium {
  line-height: 1.5;
}
.leading-large {
  line-height: 2;
}

// 定义字体(px)单位，大于或等于12的都为px单位字体
@for $i from 6 through 20 {
  .font-#{$i * 2} {
    font-size: #{$i * 2}px;
  }
}

// 定义内外边距(px)单位，历遍0-80
@for $i from 0 through 80 {
  // 只要偶数
  @if $i % 2 == 0 {
    .m-#{$i} {
      margin: #{$i}px;
    }
    .p-#{$i} {
      padding: #{$i}px;
    }
    @each $short, $long in l left, t top, r right, b bottom {
      .m-#{$short}-#{$i} {
        margin-#{$long}: #{$i}px;
      }
      .p-#{$short}-#{$i} {
        padding-#{$long}: #{$i}px;
      }
    }
  }
}
// 公共flex
.com-flex {
  display: flex;
  flex-direction: row;
  align-items: center;
}
// 定义flex等分
@for $i from 0 through 12 {
  .com-flex-#{$i} {
    flex: $i;
  }
}

.com-flex-wrap {
  flex-wrap: wrap;
}

.com-flex-nowrap {
  flex-wrap: nowrap;
}

.com-col-center {
  align-items: center;
}

.com-col-top {
  align-items: flex-start;
}

.com-col-bottom {
  align-items: flex-end;
}

.com-row-center {
  justify-content: center;
}

.com-row-left {
  justify-content: flex-start;
}

.com-row-right {
  justify-content: flex-end;
}

.com-row-between {
  justify-content: space-between;
}

.com-row-around {
  justify-content: space-around;
}
