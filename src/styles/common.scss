body {
  height: 100%;
  min-width: 1200px;
  overflow-x: auto;
  text-rendering: optimizeLegibility;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  background-color: #f0f2f5;
}
html,
body,
p,
ol,
ul,
li,
dl,
dt,
dd,
blockquote,
figure,
fieldset,
legend,
textarea,
pre,
iframe,
hr,
h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
  padding: 0;
}

ul {
  list-style: none;
}

html {
  height: 100%;
  box-sizing: border-box;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  outline: none;
  text-decoration: none;
}

div:focus {
  outline: none;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: ' ';
    clear: both;
    height: 0;
  }
}
//main-container全局样式
.app-container {
  font-size: 14px;
}
.quick-bi-container {
  height: 100%;
  padding: 16px 16px 0;
}
.table-container {
  min-height: 100%;
  background-color: #fff;
  padding: 16px;
  overflow-y: auto;
  border-radius: 8px 8px 0 0;
}
//分页器全局样式
.pagination {
  text-align: right;
  margin-top: 10px;
}

// router-link
.link-type,
.link-type:focus {
  color: var(--main-color);
  cursor: pointer;
  transition: all 0.1s ease 0s;
  &:hover {
    color: #7f62d8;
  }
}
// 去掉type='number'默认样式
input[type='number'] {
  -moz-appearance: textfield;
}
input[type='number']::-webkit-inner-spin-button,
input[type='number']::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.el-message {
  min-width: 300px;
}

@media (max-width: 640px) {
  .el-message-box {
    width: 90%;
  }
}

.popper-max-width-450 {
  max-width: 450px;
}

.select-none {
  user-select: none;
}

.pswp {
  z-index: 3000 !important;
}

.commo-flex {
  display: flex;
  &.space-between {
    justify-content: space-between;
  }
}
// 全局页面背景
.commo-page-container {
  padding: 20px;
  font-size: 14px;
}
.commo-filter-list {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
}
.commo-el-tag-newline {
  white-space: normal;
  height: auto;
  display: block;
}
.commo-filter-item {
  display: flex;
  align-items: center;
  font-size: 14px;
  box-sizing: border-box;
  margin-bottom: 16px;
  padding: 0 24px;
  min-width: 32.333%;
  text-align: right;
  &--label {
    padding-right: 16px;
    white-space: nowrap;
    display: inline-block;
    line-height: 32px;
  }
  &--content {
    padding-right: 16px;
    white-space: nowrap;
    flex: 1;
    .el-input {
      width: 100%;
    }
    .el-date-editor {
      width: 100%;
    }
    .el-select {
      width: 100%;
    }
  }
  &--content2 {
    .el-input {
      width: 50%;
    }
  }
}
.commo-btn-listbox {
  padding: 0 24px;
  box-sizing: border-box;
  margin-bottom: 16px;
}

.commo-font-size12 {
  font-size: 12px;
}

// 让设置了 user-select: none; 的选中  例如 el-button
.commo-user-select-unset {
  user-select: unset !important;
}
.tox-tinymce-aux.tox {
  z-index: 3300 !important;
}
.footer-wrap {
  height: 48px;
}
.footer {
  position: fixed;
  width: 100%;
  left: 0;
  bottom: 0;
  background-color: #fff;
  padding: 8px 16px;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.15);
  text-align: right;
  z-index: 10;
}

// 按中台样式规范 覆盖全局表格搜索样式
.commo-search-container {
  background-color: #f7f8fa !important;
  padding: 16px 200px 4px 16px !important;
  font-size: 12px;
  white-space: nowrap;
  border-radius: 4px;
  position: relative;
  &.commo-search-flex {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
  }
  &.commo-search-margin-bottom10 {
    margin-bottom: 16px;
  }
  .commo-search-item {
    width: 33.33333333%;
    flex: 0 0 33.33333333%;
    /* 超宽屏显示器（宽度小于 1600px） */
    @media (min-width: 1600px) {
      width: 25%;
      flex: 0 0 25%;
    }
    align-items: center;
    display: flex;
    margin-bottom: 14px;
    padding: 0 15px;
    white-space: nowrap;
    &-label {
      width: 100px;
      font-weight: normal;
      display: block;
      line-height: 1.6;
      text-align: left;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      padding-right: 16px;
      flex-shrink: 0;
    }

    &-content {
      flex: 1;
      display: flex;
      align-items: center;
      .el-date-editor--datetimerange.el-input,
      .el-date-editor--datetimerange.el-input__inner {
        width: 100% !important;
      }
      .el-date-editor--daterange.el-input__inner {
        width: 100% !important;
      }
      .el-range-editor--medium .el-range-input {
        font-size: 12px !important;
      }
      & > div,
      .el-input {
        width: 100% !important;
      }
      & > div.custom {
        .el-input {
          width: auto !important;
          flex: 1;
        }
      }
      .el-input-group__prepend,
      .el-input-group__append {
        .el-select .el-input {
          width: auto !important;
        }
      }
      .el-date-editor .el-range-input {
        width: 50% !important;
      }
      .el-date-editor .el-range-separator {
        padding: 0 !important;
      }
      // 日期-时间区间 特殊处理宽度
      &--picker {
        &.el-date-editor--datetimerange.el-input,
        &.el-date-editor--datetimerange.el-input__inner {
          width: 100% !important;
        }
      }
      // 区间 输入框
      &--rangeinput {
        &.el-input {
          width: auto !important;
          flex: 1;
        }
      }
    }
    .commo-search-font-size {
      font-size: 0;
    }
    .commo-search-line {
      font-size: 12px;
    }
    .form__input {
      width: 100% !important;
    }
  }
  .commo-search-btns {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    position: absolute;
    right: 16px;
    top: 16px;
    .el-button + .el-button {
      margin-left: 10px;
    }
  }
}
.commo-btns-row {
  margin: 10px 0;
  &--marginleft10 {
    margin-left: 10px !important;
  }
}
// 弹出框高度超出屏幕
.common-dialog-overWindow {
  .el-dialog {
    height: 80%;
    display: flex;
    flex-direction: column;
    .el-dialog__body {
      flex: 1;
      height: 0;
    }
  }
}
// 全局 tooltip__popper 样式统一设置
.el-tooltip__popper {
  max-width: 400px;
  line-height: 1.4 !important;
}

.common-el-icon-warning {
  color: #999;
  margin: 0 5px;
}

// 悬浮按钮
.common-fixed-footer {
  position: fixed;
  width: 100%;
  left: 0;
  bottom: 0;
  background-color: #fff;
  padding: 8px 16px;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.15);
  text-align: right;
  z-index: 1;
}
.page-container {
  display: flex;
  min-height: 100%;
  flex-direction: column;
  background-color: #fff;
  padding: 16px;
  min-width: 1000px;
  border-radius: 8px 8px 0 0;
}
// 微前端表格组件样式统一
.sy-normal-table {
  .search-container {
    display: flex;
    border-radius: 4px;
    .search-item-handle {
      padding-left: 20px;
      width: auto !important;
    }
    .filters {
      flex: 1;
      .filters-item {
        width: 33.3333333333% !important;
        /* 超宽屏显示器（宽度小于 1600px） */
        @media (min-width: 1600px) {
          width: 25% !important;
        }
        display: inline-flex;
        align-items: flex-start;
        margin-right: 0 !important;
        padding-right: 16px;
      }
      .el-form-item__label {
        text-align: left !important;
        font-weight: normal;
        padding: 0 8px 0 0;
      }
      .filters-label {
        padding-right: 4px;
      }
      .el-form-item__content {
        flex: 1;
        .sy-select-warp,
        .el-cascader {
          width: 100%;
        }
      }
      .el-input {
        width: 100% !important;
      }
      .el-date-editor.el-input__inner {
        width: 100% !important;
      }
      .el-date-editor .el-range-input {
        width: 50% !important;
      }
      .el-date-editor .el-range-separator {
        width: auto !important;
      }
      .filters-label-text {
        font-weight: normal;
        width: 100%;
        padding: 0;
      }
      .sy-select-warp .sy-select .el-tag {
        max-width: 80%;
      }
      .filters-label .sy-tips-wrap {
        right: 0;
      }
    }
  }
}

// 处理弹窗里面的表格样式
.el-dialog {
  .sy-normal-table .search-container .filters .filters-item {
    width: 33.3333333333% !important;
  }
  .commo-search-container .commo-search-item {
    width: 33.3333333333% !important;
  }
  .filter-form-container .commo-search-item {
    width: 33.3333333333% !important;
  }
}
// 全局tabs切换样式
.custom-border-tabs {
  .el-tabs__nav {
    background-color: transparent;
  }
  .el-tabs__header {
    margin: 0 0 16px;
  }
  .el-tabs__nav-wrap:after {
    display: block !important;
    height: 1px !important;
    content: '';
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    background-color: var(--border-color-light);
    z-index: 1;
  }
}

// 覆盖微前端样式
.sy-normal-table .tip {
  color: var(--color-info);
  line-height: 150%;
}

// 公共步骤条样式
.custom-steps-primary {
  .el-step__head.is-process {
    color: var(--color-primary);
    border-color: var(--color-primary);
  }
  .el-step__title.is-process {
    color: var(--color-primary);
  }
  .el-step__head.is-success {
    color: #fff;
    border-color: var(--color-primary);
    .el-step__icon.is-text {
      background-color: var(--color-primary);
    }
    .el-step__icon-inner.is-status {
      font-size: 20px;
    }
    .el-step__line {
      background-color: var(--color-primary);
    }
  }
  .el-step__title.is-success {
    color: var(--color-primary);
  }
  .el-step__icon {
    width: 30px;
    height: 30px;
    font-size: 15px;
  }
  .el-step.is-horizontal .el-step__line {
    top: 15px;
  }
}
@font-face {
  font-family: 'D-DIN-PRO-400';
  src: url('https://oss.syounggroup.com/static/file/hermes-workspace/字体/D-DIN-PRO-400-Regular.otf') format('opentype');
}
@font-face {
  font-family: 'D-DIN-PRO-500';
  src: url('https://oss.syounggroup.com/static/file/hermes-workspace/字体/D-DIN-PRO-500-Medium.otf') format('opentype');
}
@font-face {
  font-family: 'D-DIN-PRO-600';
  src: url('https://oss.syounggroup.com/static/file/hermes-workspace/字体/D-DIN-PRO-600-SemiBold.otf') format('opentype');
}

.tooltip-popper-content-auto {
  max-width: 100% !important;
}

.common-action-bar {
  display: flex;
  align-items: center;
  & > * {
    margin-right: 10px;
  }
}

.form-container {
  border-radius: 8px 8px 0 0;
  background-color: #fff;
}

body .sy-transfer-table-dialog {
  width: 95vw;
}
// 通用抽屉样式
.common-drawer {
  .el-drawer__header {
    background: linear-gradient(0deg, rgba(255, 255, 255, 1) 10%, rgba(214, 213, 253, 0.6) 100%);
    line-height: 26px;
    padding: 12px 16px;
    margin: 0;
    & > span {
      color: var(--color-text-primary);
      font-weight: 500;
    }
  }
  .el-drawer__body {
    max-height: calc(100vh - 50px);
    display: flex;
    flex-direction: column;
    .drawer-header {
      padding-left: 16px;
      padding-right: 16px;
    }
    .drawer-content {
      flex: 1;
      padding: 16px;
      overflow-y: auto;
      overflow-x: hidden;
    }
    .drawer-footer {
      padding: 16px;
    }
  }
}

.common-line-title {
  display: flex;
  align-items: center;
  &__text {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: var(--color-text-primary);
    font-weight: bold;
    &:before {
      content: '';
      height: 12px;
      width: 2px;
      background: var(--color-primary);
      margin-right: 4px;
    }
  }
}
// 上传样式
.el-upload--picture-card {
  width: 118px !important;
  height: 118px !important;
  line-height: 116px !important;
}
.el-upload-list--picture-card .el-upload-list__item {
  width: 118px !important;
  height: 118px !important;
}

.el-upload--picture-card i {
  font-size: 24px !important;
}
