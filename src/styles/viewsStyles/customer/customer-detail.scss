$text: rgba(0, 0, 0, 0.65);
.el-form-item {
  margin-bottom: 0;
}
::v-deep.el-select,
.el-input,
.el-textarea {
  width: 250px;
}
.form-container {
  padding-bottom: 60px;
  position: relative;
  height: 100%;
  overflow: auto;
}
.type-radio .el-radio {
  margin: 3px 6px 3px 0 !important;
}
.fix-part {
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.3);
  background-color: #fff;
  position: fixed;
  left: 0;
  z-index: 2;
  width: 100%;
  bottom: 0;
  text-align: right;
  .el-form-item {
    padding: 10px 0;
    margin: 0 20px;
  }
}
.part {
  background-color: #fff;
  box-shadow: 0 0 2px rgba(0, 0, 0, 0.2);
  & > .title {
    font-weight: 500;
    border-bottom: 1px solid #e8e8e8;
    padding: 18px 30px;
    font-size: 16px;
    user-select: none;
  }
  & > .content {
    padding: 18px 30px;
  }
  & ~ .part {
    margin-top: 20px;
  }
}
.head-table {
  width: 100%;
  border-spacing: 0;
  text-align: left;
  tbody tr td {
    width: 33.33%;
    color: $text;
    vertical-align: top;
    .col {
      display: table;
      span {
        display: table-cell;
      }
      span:nth-child(1) {
        white-space: nowrap;
        color: rgba(0, 0, 0, 0.85);
        padding-right: 10px;
      }
    }
  }
  tbody tr ~ tr td {
    padding-top: 14px;
  }
}
.part,
.btn-box {
  background-color: #fff;
  box-shadow: 0 0 2px rgba(0, 0, 0, 0.2);
}
.btn-box {
  position: fixed;
  width: 100%;
  bottom: 0;
  left: 0;
}
.form-row {
  margin-left: -24px;
  margin-right: -24px;
  height: auto;
}
.el-cascader .el-input,
.el-cascader .el-input__inner {
  width: 250px;
}
.form-col-item {
  margin-bottom: 24px;
  /*display: flex;*/
}
.col-label {
  white-space: nowrap;
  vertical-align: middle;
  display: inline-block;
  line-height: 40px;
  padding-right: 8px;
  min-width: 78px;
  text-align: right;
}
.col-split {
  white-space: nowrap;
  vertical-align: middle;
  display: inline-block;
  line-height: 40px;
}
.col-label label {
  color: rgba(0, 0, 0, 0.85);
  font-weight: normal;
}
.col-btn-submit {
  white-space: nowrap;
  vertical-align: middle;
  display: inline-block;
  line-height: 32px;
  min-width: 78px;
  text-align: right;
  float: right;
}
.form-col-item {
  width: 100%;
}
.area-width {
  width: 250px;
}
.text {
  padding: 10px 0;
  margin: 0 20px;
}
.avatar {
  width: 100px;
  height: 100px;
  position: relative;
  img {
    width: 100%;
    height: 100%;
  }
  .bg {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
  }
}
.avatar-uploader-icon:hover {
  border-color: var(--color-primary);
}
.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 100px;
  height: 100px;
  line-height: 100px;
  text-align: center;
  border: 1px dashed #d9d9d9;
  border-radius: 3px;
}
.avatar {
  width: 100px;
  height: 100px;
  position: relative;
  margin-right: 20px;
  img {
    width: 100%;
    height: 100%;
  }
  .bg {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
  }
}
.img-list {
  display: flex;
  justify-content: flex-start;
}
.btn-wrap {
  display: flex;
  justify-content: center;
}
