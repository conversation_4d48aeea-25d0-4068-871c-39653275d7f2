.filter-list {
  margin-bottom: 10px;
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;

  .filter-item {
    font-size: 14px;
    box-sizing: border-box;
    display: flex;
    margin-bottom: 16px;
    padding: 0 24px;
    width: 33.333%;
    min-width: 334px;
    text-align: right;

    .label {
      padding-right: 16px;
      white-space: nowrap;
      display: inline-block;
      line-height: 32px;
    }

    .content {
      flex: 1;
    }

    &.btns-open {
      display: block;
      width: 100%;
    }
  }
}

.add-btn {
  margin-bottom: 10px;
  margin-top: 10px;
}

.link {
  color: var(--color-primary);

  &:hover {
    color: var(--color-primary);
  }
}

.filter-name {
  margin-right: 10px;
  color: #616366;
  font-weight: 500;
}

.toggle-btn {
  ::v-deep [class*='el-icon-'] + span {
    margin-left: 0;
  }
}

.el-date-editor--daterange.el-input__inner,
.el-cascader,
.el-select {
  width: 100%;
}

.header {
  display: flex;
  justify-content: space-between;
  padding-left: 5px;
  margin-bottom: 20px;

  .header-title {
    font-weight: 900;
    /* border-bottom: 1px solid #e8e8e8; */
    font-size: 16px;
    color: #333;
    display: inline-flex;
    align-items: center;
  }

  .actions-time {
    font-size: 12px;
    color: #999;
    margin-right: 10px;
  }

  a {
    font-size: 12px;
    color: var(--color-primary);
    text-decoration: none;
  }
}

.index {
  background-color: #fff;
}

.box {
  padding: 20px;

  .row {
    margin-left: 80px;
    position: relative;
    display: flex;

    .item {
      flex-basis: 33.33%;
      color: #333;
      font-size: 12px;

      .amount {
        cursor: pointer;
        display: block;
        font-size: 24px;
        margin: 6px 0 10px;
        color: var(--color-primary);
        text-decoration: none;
      }

      .amount-x {
        color: #f44;
      }

      .amount-yesterday {
        color: #999;
      }
    }
  }
}
.thumbImg {
  width: 50px;
  height: 50px;
}
.btn-create {
  margin-bottom: 16px;
}
.commo-table-btns {
  margin-bottom: 10px;
}
