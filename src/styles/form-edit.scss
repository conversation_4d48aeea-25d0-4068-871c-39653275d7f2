$text: rgba(0, 0, 0, 0.65);
.el-form-item {
  margin: 0 0 24px;
}
.el-select,
.el-input,
.el-input__inner,
.el-textarea {
  width: 250px;
}
.form-container {
  padding-bottom: 60px;
  position: relative;
  height: 100%;
  overflow: auto;
}
.type-radio .el-radio {
  margin: 3px 6px 3px 0 !important;
}
.fix-part {
  box-shadow: 0 1px 5px rgba(0, 0, 0, 0.3);
  background-color: #fff;
  position: fixed;
  left: 0;
  z-index: 2;
  width: 100%;
  bottom: 0;
  text-align: right;
  .el-form-item {
    padding: 10px 0;
    margin: 0 20px;
  }
}
.part {
  background-color: #fff;
  box-shadow: 0 0 2px rgba(0, 0, 0, 0.2);
  .sub-title {
    font-weight: bolder;
    font-size: 16px;
    padding: 18px 30px;
  }
  & > .title {
    font-weight: bolder;
    border-bottom: 1px solid #e8e8e8;
    padding: 18px 30px;
    font-size: 16px;
    user-select: none;
  }
  & > .content {
    padding: 18px 30px;
  }
  & ~ .part {
    margin-top: 20px;
  }
}
.add-commodity {
  padding-left: 24px;
}
.el-radio {
  margin-top: 10px;
}
.my-input-class {
  width: 128px;
  margin: 0 10px;
  margin-top: 10px;
}
.my-span {
  width: 50px;
  line-height: 36px;
  font-size: 14px;
  color: #000;
}
.my-discountContent {
  margin-top: 0;
}
.validate-tips {
  color: var(--color-danger);
  font-size: 12px;
  line-height: 1;
  padding-top: 4px;
}

.tips {
  color: #999;
  font-size: 12px;
}

.footer {
  height: 45px;
  line-height: 45px;
  background-color: #fff;
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  text-align: right;
  padding: 0 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 1001;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
