.filter-list {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;

  .filter-item {
    font-size: 14px;
    box-sizing: border-box;
    display: flex;
    margin-bottom: 16px;
    padding: 0 24px;
    min-width: 33.333%;
    text-align: right;

    .label {
      padding-right: 16px;
      white-space: nowrap;
      display: inline-block;
      line-height: 32px;
    }

    .content {
      flex: 1 1;
    }

    .link {
      margin-left: 10px;
    }
  }
}

.link {
  color: var(--color-primary);

  &:hover {
    color: var(--color-primary);
  }
}

.wrap {
  ::v-deep .el-tabs__header {
    margin: 0;
  }

  ::v-deep .el-range-editor--medium.el-input__inner {
    box-sizing: border-box;
    width: 100%;
  }
}

.refund-list {
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB,
    Microsoft YaHei, Arial, sans-serif;
  text-align: center;

  th,
  td {
    text-overflow: ellipsis;
    word-wrap: break-word;
    vertical-align: middle;
    white-space: normal;
    word-break: break-all;
    width: 8%;
  }

  .cell {
    padding: 10px 0;
    width: 100%;
    display: inline-block;
    line-height: 23px;
  }

  .table-head {
    .line {
      background-color: #fafafa !important;

      th {
        font-size: 14px;
        font-weight: 700;
        color: #909399;
        border-bottom: 1px solid #ebeef5;
      }
    }
  }

  .table-body {
    .line {
      background-color: #fff;
      transition: background-color 0.25s ease;

      &.tips-container:hover {
        background-color: #fff;
      }

      &:hover {
        background-color: #f5f7fa;
      }

      td {
        font-size: 14px;
        color: #606266;
        border-bottom: 1px solid #ebeef5;

        &.tips-content {
          border-bottom: none;

          .cell {
            text-align: left;

            span {
              margin-right: 8px;
            }
          }
        }

        &.goods-img {
          width: 6%;

          .table-img {
            width: 60px;
            height: 60px;
            margin-right: 8px;
            vertical-align: top;
          }
        }

        &.goods-img-package {
          width: 20%;
          text-align: left;

          .table-img {
            width: 60px;
            height: 60px;
            margin-right: 8px;
            vertical-align: top;
            float: left;
          }
        }

        &.goods-name {
          width: 20%;

          .cell {
            height: 80px;
          }
        }

        &.price {
          border-right: 1px solid #ebeef5;

          .cell {
            height: 80px;
          }
        }

        &.operation {
          color: var(--color-primary);
          cursor: pointer;
        }
      }
    }
  }
}

.tab-container {
  ::v-deep .el-tabs__nav {
    background: #fff;
  }

  ::v-deep .el-tabs__item {
    border-bottom: 1px solid #e4e7ed;
  }
}

.empty {
  text-align: center;
  height: 60px;
  line-height: 60px;
  font-size: 14px;
  color: #909399;
  border-bottom: 1px solid #ebeef5;
}

.create {
  margin-bottom: 8px;
}
.tips {
  color: var(--color-info);
  font-size: 12px;
}
