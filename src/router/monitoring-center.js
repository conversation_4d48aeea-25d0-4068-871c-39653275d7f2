export default [
  // 智能监控中心
  {
    path: '/monitoring-center/channel-monitor',
    name: '/monitoring-center/channel-monitor',
    component: () => import('@/views/monitoring-center/channel-monitor'),
    meta: {
      title: '渠道趋势监控',
      icon: 'shangpin'
    }
  },
  {
    path: '/monitoring-center/price-monitoring',
    name: 'monitoring-center-price-monitoring',
    component: () => import('@/views/monitoring-center/price-monitoring'),
    meta: {
      title: '智能价格监控',
      icon: 'shangpin'
    }
  },
  {
    path: '/monitoring-center/low-price-goods/:id',
    name: '/monitoring-center/low-price-goods/:id',
    component: () => import('@/views/monitoring-center/low-price-goods'),
    meta: {
      title: '破价商品'
    }
  },
  {
    path: '/monitoring-center/monitoring-goods/:id',
    name: '/monitoring-center/monitoring-goods/:id',
    component: () => import('@/views/monitoring-center/monitoring-goods'),
    meta: {
      title: '监控商品'
    }
  }
];
