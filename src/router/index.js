import Vue from 'vue';
import Router from 'vue-router';
import shop from './shop.js'; // 数据中心 - 店铺
import distributorManagement from './distributor-management/distributor-management.js'; // 分销商管理
import purchaseSellStockManagement from './purchase-sell-stock-management.js'; // 进销存管理
import customerManagement from './distributor-management/customer-management.js'; // 客户管理
import dataCenter from './data-center/data-center'; // 数据中心
import setting from './setting/setting';
import order from './order'; // 客户管理
import activity from './activity'; // 营销活动
import shopDecoration from './shop-decoration/shop-decoration'; // 店铺装修
import monitoringCenter from './monitoring-center'; // 智能监控
import componentCenter from './componentCenter'; // 商品中心
import salesBills from './sales-bills'; // 财务管理
import brand from './brand'; // 品牌管理
import chat from './chat'; // 品牌管理
import brandFestival from './brand-festival';
import yearlyPolicyManagement from './yearly-policy-management.js'; // 年框政策管理
import regularPolicy from './regular-policy.js'; // 常规政策管理

const VueRouterPush = Router.prototype.push;
Router.prototype.push = function push(to) {
  return VueRouterPush.call(this, to).catch((err) => err);
};

Vue.use(Router);

// 折叠菜单页面白名单
const COLLAPSE_MENU_WHITE_LIST = [
  'data-center-distributor-square-portrait', // 分销商个人画像
  'data-center-distributor-square-list', // 分销商广场
  'data-center-brand-square-portrait', // 品牌画像
  'data-center-brand-square-list', // 品牌广场
  'data-center-brand-search-distribution' // 品牌找分销
];
// 默认默认路由
const defaultRoute = [
  {
    path: '/',
    name: '/',
    component: () => import('@/views/home')
  }
];
const moduleRoute = [
  ...shop,
  ...distributorManagement,
  ...purchaseSellStockManagement,
  ...customerManagement,
  ...dataCenter,
  ...setting,
  ...componentCenter,
  ...salesBills,
  ...monitoringCenter,
  ...shopDecoration,
  ...order, // 智能订货中心
  ...activity, // 营销活动
  ...brand, // 品牌营销中心
  ...chat, // 客服
  ...brandFestival,
  ...yearlyPolicyManagement,
  ...regularPolicy // 常规政策管理
];

const returnPage = (item, mushroomMenu = {}) => {
  const obj = {
    path: item.path,
    name: item.name,
    component: item.component,
    meta: {
      pageTitle: mushroomMenu.name,
      title: mushroomMenu.name,
      code: mushroomMenu.code
    }
  };
  if (item.redirect) {
    obj.redirect = item.redirect;
  }
  return obj;
};

// 判断是否带有/:
function filterMatch(path) {
  return !!path.match(RegExp('/:{1}'));
}

/**
 * 主要思路:
 * 通过本地路由信息与用户信息中的路由数据对比筛选出该用户所拥有的路由数据
 */
function filterRouter(arrRoute) {
  if (!arrRoute || !arrRoute.length) {
    return [];
  }
  const menus = window.QIANKUN_DATA.user_info?.menus || [];
  const parentData = [];
  arrRoute.forEach((item) => {
    if (item.children && item.children.length) {
      parentData.push(returnPage(item));
      parentData[parentData.length - 1].children = [];
      filterRouter(item.children, parentData[parentData.length - 1].children);
      return;
    }
    let mushroomMenu;
    if (filterMatch(item.path)) {
      mushroomMenu = menus.find((fv) => fv.href && fv.href.includes(item.path.split('/:')[0]));
    } else {
      mushroomMenu = menus.find((fv) => fv.href && fv.href === item.path);
    }
    if (mushroomMenu) {
      parentData.push(returnPage(item, mushroomMenu));
    }
  });
  return parentData;
}
const routeData = filterRouter(moduleRoute);
const routes = routeData.concat(defaultRoute);
const router = new Router({
  mode: 'hash',
  scrollBehavior: () => ({ y: 0 }),
  routes
});

const chats = ['/chat/chat-history', '/chat/chat-line', '/chat/chat-view', '/chat/chat-efficiency-phrase', '/chat/auto-reply', '/chat/chat-custom-face'];
router.beforeEach((to, from, next) => {
  handlerSubMenu(to);
  const path = to.path;
  if (chats.includes(path)) {
    Vue.prototype.$back();
    window.open(process.env.VUE_APP_ENV_CONFIG === 'prod' ? `https://syzgchat.syounggroup.com${path}` : `https://testsyzgchat.syounggroup.com${path}`);
  } else {
    next();
  }
});

function handlerSubMenu(to) {
  if (COLLAPSE_MENU_WHITE_LIST.includes(to.name)) {
    window.QIANKUN_DATA.setCollapse(true);
  } else {
    window.QIANKUN_DATA.setCollapse(false);
  }
}
export default router;
