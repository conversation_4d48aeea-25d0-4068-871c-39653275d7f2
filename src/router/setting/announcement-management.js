export default [
  // 公告管理
  {
    path: '/announcement-management/list',
    name: 'announcement-management-list',
    component: () => import('@/views/announcement-management/list'),
    meta: {
      title: '公告管理'
    }
  },
  {
    path: '/announcement-management/create',
    name: '/announcement-management/create',
    component: () => import('@/views/announcement-management/create'),
    meta: {
      title: '公告管理 - 新增'
    }
  },
  {
    path: '/announcement-management/edit/:id',
    name: '/announcement-management/edit/:id',
    component: () => import('@/views/announcement-management/edit'),
    meta: {
      title: '公告管理 - 编辑'
    }
  },
  {
    path: '/announcement-management/detail/:id',
    name: '/announcement-management/detail/:id',
    component: () => import('@/views/announcement-management/detail'),
    meta: {
      title: '公告管理 - 详情'
    }
  },
  {
    path: '/announcement-management/copy/:id',
    name: '/announcement-management/copy/:id',
    component: () => import('@/views/announcement-management/copy'),
    meta: {
      title: '公告管理 - 复制'
    }
  }
];
