import announcementManagement from './announcement-management';
export default [
  /* -----------------------------------------------------平台信息设置------------------------------------------------------ */
  // 平台信息设置
  {
    path: '/setting/shop/target',
    name: '/setting/shop/target',
    component: () => import('@/views/setting/shop/target'),
    meta: {
      title: '直供经营目标设置'
    }
  },
  {
    path: '/setting/business-objectives-gj',
    name: '/setting/business-objectives-gj',
    component: () => import('@/views/setting/business-objectives-gj/index'),
    meta: {
      title: '国际经营目标设置'
    }
  },
  {
    path: '/setting/divide-group',
    name: 'setting-divide-group',
    component: () => import('@/views/setting/divide-group/index'),
    meta: {
      title: '分组设置'
    }
  },
  {
    path: '/setting/business-objectives-gj/expand-setting',
    name: '/setting/business-objectives-gj/expand-setting',
    component: () => import('@/views/setting/business-objectives-gj/expand-setting'),
    meta: {
      title: '批量设置拓展目标'
    }
  },
  {
    path: '/setting/business-objectives-gj/growth-setting',
    name: '/setting/business-objectives-gj/growth-setting',
    component: () => import('@/views/setting/business-objectives-gj/growth-setting'),
    meta: {
      title: '批量设置成长目标'
    }
  },
  {
    path: '/setting/shop/operationTaskSet',
    name: 'setting-shop-operationTaskSet',
    component: () => import('@/views/setting/shop/operationTaskSet/list'),
    meta: {
      title: '运营任务设置'
    }
  },
  {
    path: '/setting/shop/createOperationTaskTemplate',
    name: '/setting/shop/createOperationTaskTemplate',
    component: () => import('@/views/setting/shop/operationTaskSet/createOperationTaskTemplate'),
    meta: {
      title: '创建任务模板'
    }
  },
  {
    path: '/setting/shop/updateOperationTaskTemplate/:id',
    name: '/setting/shop/updateOperationTaskTemplate',
    component: () => import('@/views/setting/shop/operationTaskSet/updateOperationTaskTemplate'),
    meta: {
      title: '编辑任务模板'
    }
  },
  {
    path: '/setting/shop/viewOperationTaskTemplate/:id',
    name: '/setting/shop/viewOperationTaskTemplate',
    component: () => import('@/views/setting/shop/operationTaskSet/viewOperationTaskTemplate'),
    meta: {
      title: '查看任务模板'
    }
  },
  {
    path: '/setting/shop/brand-target',
    name: '/setting/shop/brand-target',
    component: () => import('@/views/setting/shop/target/brand-target'),
    meta: {
      title: '品牌经营指标设置'
    }
  },
  {
    path: '/setting/shop/edit-target',
    name: '/setting/shop/edit-target',
    component: () => import('@/views/setting/shop/target/edit-target'),
    meta: {
      title: '设置经营目标'
    }
  },
  {
    path: '/setting/shop/batch-edit',
    name: '/setting/shop/batch-edit',
    component: () => import('@/views/setting/shop/target/batch-edit'),
    meta: {
      title: '批量设置品牌采购目标'
    }
  },

  {
    path: '/setting/shop/auth',
    name: 'setting-shop-auth',
    component: () => import('@/views/setting/shop/auth'),
    meta: {
      title: '权限管理'
    }
  },
  {
    path: '/setting/shop/postage',
    name: 'setting-shop-postage',
    component: () => import('@/views/setting/shop/postage'),
    meta: {
      title: '运费设置'
    }
  },
  {
    path: '/setting/shop/report-setting',
    name: 'setting-shop-report-setting',
    component: () => import('@/views/setting/shop/report-setting'),
    meta: {
      title: '报表设置'
    }
  },
  {
    path: '/setting/shop/report-setting/config',
    name: '/setting/shop/report-setting/config',
    component: () => import('@/views/setting/shop/report-setting/config'),
    meta: {
      title: '配置报表'
    }
  },
  {
    path: '/setting/shop/customer-service',
    name: 'setting-shop-customer-service',
    component: () => import('@/views/setting/shop/customer-service'),
    meta: {
      title: '客服设置'
    }
  },
  {
    path: '/setting/shop/introduce',
    name: 'setting-shop-introduce',
    component: () => import('@/views/setting/shop/introduce'),
    meta: {
      title: '关于我们'
    }
  },
  {
    path: '/setting/shop/introduce/agreement/:type',
    name: 'shop-introduce-agreement',
    component: () => import('@/views/setting/shop/introduce/agreement'),
    meta: {
      title: '平台协议管理'
    }
  },
  {
    path: '/setting/shop/encryption',
    name: '/setting/shop/encryption',
    component: () => import('@/views/setting/shop/encryption'),
    meta: {
      title: '加解密转换'
    }
  },
  {
    path: '/setting/invoice',
    name: 'setting-invoice',
    component: () => import('@/views/setting/invoice'),
    meta: {
      title: '发票管理'
    }
  },
  {
    path: '/setting/logout-management',
    name: 'setting-logout-management',
    component: () => import('@/views/setting/logout-management'),
    meta: {
      title: '注销管理'
    }
  },

  {
    path: '/setting/commodity-abandon',
    name: 'setting-commodity-abandon',
    component: () => import('@/views/setting/commodity-abandon/list'),
    meta: {
      title: '商品回收站',
      icon: 'shangpin'
    }
  },
  /* -----------------------------------------------------店铺员工------------------------------------------------------ */
  {
    path: '/setting/shop/employee/list',
    name: '/setting/shop/employee/list',
    component: () => import('@/views/setting/shop/employee/list'),
    meta: {
      title: '店铺员工'
    },
    hidden: true
  },
  {
    path: '/setting/shop/employee/add',
    name: '/setting/shop/employee/add',
    component: () => import('@/views/setting/shop/employee/add'),
    meta: {
      title: '店铺员工 - 新增'
    }
  },
  {
    path: '/setting/shop/employee/edit/:id',
    name: '/setting/shop/employee/edit/:id',
    component: () => import('@/views/setting/shop/employee/edit'),
    meta: {
      title: '店铺员工 - 编辑'
    },
    hidden: true
  },
  {
    path: '/setting/shop/employee/detail/:id',
    name: '/setting/shop/employee/detail/:id',
    component: () => import('@/views/setting/shop/employee/detail'),
    meta: {
      title: '店铺员工 - 详情'
    },
    hidden: true
  },
  /* -----------------------------------------------------店铺角色------------------------------------------------------ */
  {
    path: '/setting/shop/role/list',
    name: '/setting/shop/role/list',
    component: () => import('@/views/setting/shop/role/list'),
    meta: {
      title: '店铺角色'
    }
  },
  {
    path: '/setting/shop/role/add',
    name: '/setting/shop/role/add',
    component: () => import('@/views/setting/shop/role/add'),
    meta: {
      title: '店铺员工 - 新增'
    },
    hidden: true
  },
  {
    path: '/setting/shop/role/edit/:id',
    name: '/setting/shop/role/edit/:id',
    component: () => import('@/views/setting/shop/role/edit'),
    meta: {
      title: '店铺员工 - 编辑'
    },
    hidden: true
  },
  {
    path: '/setting/shop/role/typeList/:id',
    name: '/setting/shop/role/typeList/:id',
    component: () => import('@/views/setting/shop/role/typeList'),
    meta: {
      title: '店铺员工 - 数量'
    },
    hidden: true
  },
  // 消息通知
  /* -----------------------------------------------------消息通知------------------------------------------------------ */
  {
    path: '/setting/message-notify/list',
    name: '/setting/message-notify/list',
    component: () => import('@/views/setting/message-notify/list'),
    meta: {
      title: '消息通知'
    }
  },
  {
    path: '/setting/message-notify/blacklist/:id',
    name: '/setting/message-notify/blacklist/:id',
    component: () => import('@/views/setting/message-notify/blacklist'),
    meta: {
      title: '添加黑名单'
    }
  },
  {
    path: '/setting/message-notify/promoters/:id',
    name: '/setting/message-notify/promoters/:id',
    component: () => import('@/views/setting/message-notify/promoters'),
    meta: {
      title: '添加推送人员'
    }
  },
  /* -----------------------------------------------------商品设置------------------------------------------------------ */
  {
    path: '/setting/commodity/param',
    name: 'setting-commodity-param',
    component: () => import('@/views/setting/commodity/param/list'),
    meta: {
      title: '商品参数'
    }
  },
  {
    path: '/setting/commodity/param/create',
    name: '/setting/commodity/param/create',
    component: () => import('@/views/setting/commodity/param/create'),
    meta: {
      title: '新增商品参数'
    },
    hidden: true
  },
  {
    path: '/setting/commodity/param/edit/:id',
    name: '/setting/commodity/param/edit/:id',
    component: () => import('@/views/setting/commodity/param/edit'),
    meta: {
      title: '修改商品参数'
    },
    hidden: true
  },
  {
    path: '/setting/commodity/category',
    name: 'setting-commodity-category',
    component: () => import('@/views/setting/commodity/category'),
    meta: {
      title: '商品分组'
    }
  },
  /* -----------------------------------------------------渠道管理------------------------------------------------------ */
  {
    path: '/setting/channel-management',
    name: 'setting-channel-management-auto',
    component: () => import('@/views/setting/channel-management/list'),
    meta: {
      title: '拉单渠道配置'
    }
  },
  {
    path: '/setting/channel-management/detail/:id',
    name: '/setting/channel-management/detail/:id',
    component: () => import('@/views/setting/channel-management/detail'),
    meta: {
      title: '渠道管理-详情'
    }
  },
  {
    path: '/setting/channel-management/edit/:id',
    name: '/setting/channel-management/edit/:id',
    component: () => import('@/views/setting/channel-management/edit'),
    meta: {
      title: '渠道管理-编辑'
    }
  },
  {
    path: '/setting/channel-management/create',
    name: 'setting-channel-management-create',
    component: () => import('@/views/setting/channel-management/create'),
    meta: {
      title: '渠道管理-新增'
    }
  },
  {
    path: '/setting/shop/app-jump-list',
    name: 'setting-shop-app-jump-list',
    component: () => import('@/views/setting/app-jump-list'),
    meta: {
      title: '小程序授权'
    }
  },
  {
    path: '/setting/shop/sync-market-tags',
    name: 'setting-shop-sync-market-tags',
    component: () => import('@/views/setting/sync-market-tags'),
    meta: {
      title: '标签同步企微'
    }
  },
  ...announcementManagement
];
