export default [
  // 店铺装修管理
  /* -----------------------------------------------------店铺装修管理------------------------------------------------------ */
  {
    path: '/shop-decoration/personal-center',
    name: '/shop-decoration/personal-center',
    component: () => import('@/views/shop-decoration/personal-center'),
    meta: {
      title: '自定义个人中心'
    }
  },
  {
    path: '/shop-decoration/commodity-search',
    name: '/shop-decoration/commodity-search',
    component: () => import('@/views/shop-decoration/commodity-search'),
    meta: {
      title: '商品搜索'
    }
  },
  {
    path: '/shop-decoration/micro-page/list',
    name: '/shop-decoration/micro-page/list',
    component: () => import('@/views/shop-decoration/micro-page/list'),
    meta: {
      title: '微页面列表'
    }
  },
  {
    path: '/shop-decoration/micro-page/create',
    name: '/shop-decoration/micro-page/create',
    component: () => import('@/views/shop-decoration/micro-page/create'),
    meta: {
      title: '微页面新增'
    }
  },
  {
    path: '/shop-decoration/micro-page/edit/:id',
    name: '/shop-decoration/micro-page/edit/:id',
    component: () => import('@/views/shop-decoration/micro-page/edit'),
    meta: {
      title: '微页面修改'
    }
  },
  {
    path: '/shop-decoration/micro-page/copy/:id',
    name: '/shop-decoration/micro-page/copy/:id',
    component: () => import('@/views/shop-decoration/micro-page/copy'),
    meta: {
      title: '微页面复制'
    }
  },
  {
    path: '/shop-decoration/goods-detail',
    name: 'shop-decoration-goods-detail',
    component: () => import('@/views/shop-decoration/goods-detail'),
    meta: {
      title: '商详页模板'
    }
  },
  {
    path: '/shop-decoration/cart',
    name: 'shop-decoration-cart',
    component: () => import('@/views/shop-decoration/cart'),
    meta: {
      title: '购物车模板'
    }
  },
  {
    path: '/shop-decoration/market',
    name: '/shop-decoration/market',
    component: () => import('@/views/shop-decoration/market'),
    meta: {
      title: '营销页模板'
    }
  },
  {
    path: '/shop-decoration/navigation',
    name: '/shop-decoration/navigation',
    component: () => import('@/views/shop-decoration/navigation'),
    meta: {
      title: '小程序导航栏'
    }
  },
  {
    path: '/shop-decoration/navigation-h5',
    name: '/shop-decoration/navigation-h5',
    component: () => import('@/views/shop-decoration/navigation-h5'),
    meta: {
      title: 'H5导航栏'
    }
  },
  {
    path: '/shop-decoration/material-manage',
    name: 'shop-decoration-material-manage',
    component: () => import('@/views/shop-decoration/material-manage'),
    meta: {
      title: '素材中心'
    }
  },
  {
    path: '/shop-decoration/website-index',
    name: 'shop-decoration-website-index',
    component: () => import('@/views/shop-decoration/website-index'),
    meta: {
      title: 'PC官网首页模板'
    }
  },
  {
    path: '/shop-decoration/website-brand',
    name: '/shop-decoration/website-brand',
    component: () => import('@/views/shop-decoration/website-brand'),
    meta: {
      title: 'PC官网品牌馆模板'
    }
  },
  {
    path: '/shop-decoration/wx-url-link',
    name: 'shop-decoration-wx-url-link',
    component: () => import('@/views/shop-decoration/wx-url-link'),
    meta: {
      title: '小程序短链生成'
    }
  },
  /* -----------------------------------------------------自定义品类页------------------------------------------------------ */
  {
    path: '/views/shop-decoration/category/index',
    name: '/views/shop-decoration/category/index',
    meta: {
      title: '自定义品类页'
    },
    component: () => import('@/views/shop-decoration/category/index')
  },
  {
    path: '/shop-decoration/category/create/:type/:parentId',
    name: '/shop-decoration/category/create/:type/:parentId',
    component: () => import('@/views/shop-decoration/category/create'),
    meta: {
      title: '自定义品类页 - 新增'
    }
  },
  {
    path: '/shop-decoration/category/edit/:type/:parentId/:id',
    name: '/shop-decoration/category/edit/:type/:parentId/:id',
    component: () => import('@/views/shop-decoration/category/edit'),
    meta: {
      title: '自定义品类页 - 编辑'
    }
  }
];
