export default [
  /* -----------------------------------------------------年框政策管理------------------------------------------------------ */
  {
    path: '/yearly-policy-management/policy-list',
    name: 'yearly-policy-management-policy-list',
    component: () => import('@/views/yearly-policy-management/policy-list/index'),
    meta: {
      title: '年框政策列表'
    }
  },
  {
    path: '/yearly-policy-management/policy-list/created',
    name: 'yearly-policy-management-policy-list-created',
    component: () => import('@/views/yearly-policy-management/policy-list/created'),
    meta: {
      title: '政策录入-新增'
    }
  },
  {
    path: '/yearly-policy-management/policy-list/edit/:id',
    name: 'yearly-policy-management-policy-list-edit',
    component: () => import('@/views/yearly-policy-management/policy-list/edit'),
    meta: {
      title: '政策录入-编辑'
    }
  },
  {
    path: '/yearly-policy-management/policy-list/copy/:id',
    name: 'yearly-policy-management-policy-list-copy',
    component: () => import('@/views/yearly-policy-management/policy-list/copy'),
    meta: {
      title: '政策录入-复制'
    }
  },
  {
    path: '/yearly-policy-management/policy-list/detail/:id',
    name: 'yearly-policy-management-policy-list-detail',
    component: () => import('@/views/yearly-policy-management/policy-list/detail'),
    meta: {
      title: '政策录入-详情'
    }
  },
  {
    path: '/yearly-policy-management/settle-policy/list',
    name: 'yearly-policy-management-settle-policy-list',
    component: () => import('@/views/yearly-policy-management/settle-policy/list'),
    meta: {
      title: '结算政策审核'
    }
  },
  {
    path: '/yearly-policy-management/settle-policy/audit/:id',
    name: 'yearly-policy-management-settle-policy-audit',
    component: () => import('@/views/yearly-policy-management/settle-policy/audit'),
    meta: {
      title: '结算政策审核-审核'
    }
  },
  {
    path: '/yearly-policy-management/settle-policy/detail/:id',
    name: 'yearly-policy-management-settle-policy-detail',
    component: () => import('@/views/yearly-policy-management/settle-policy/detail'),
    meta: {
      title: '结算政策审核-查看'
    }
  },
  {
    path: '/yearly-policy-management/settle-policy/extra-created',
    name: 'yearly-policy-management-settle-policy-extra-created',
    component: () => import('@/views/yearly-policy-management/settle-policy/extra-created'),
    meta: {
      title: '返利发放-新增'
    }
  },
  {
    path: '/yearly-policy-management/settle-policy/extra-edit',
    name: 'yearly-policy-management-settle-policy-extra-edit',
    component: () => import('@/views/yearly-policy-management/settle-policy/extra-edit'),
    meta: {
      title: '返利发放-修改'
    }
  },
  {
    path: '/yearly-policy-management/settle-policy/extra-detail',
    name: 'yearly-policy-management-settle-policy-extra-detail',
    component: () => import('@/views/yearly-policy-management/settle-policy/extra-detail'),
    meta: {
      title: '返利发放-详情'
    }
  },
  {
    path: '/yearly-policy-management/policy-list/extra-created',
    name: 'yearly-policy-management-policy-list-outside-ad',
    component: () => import('@/views/yearly-policy-management/policy-list/extra-created'),
    meta: {
      title: '合同外政策录入-新增'
    }
  },
  {
    path: '/yearly-policy-management/policy-list/extra-edit',
    name: 'yearly-policy-management-policy-list-extra-edit',
    component: () => import('@/views/yearly-policy-management/policy-list/extra-edit'),
    meta: {
      title: '合同外政策录入-修改'
    }
  },
  {
    path: '/yearly-policy-management/policy-list/extra-detail',
    name: 'yearly-policy-management-policy-list-extra-detail',
    component: () => import('@/views/yearly-policy-management/policy-list/extra-detail'),
    meta: {
      title: '合同外政策录入-详情'
    }
  }
];
