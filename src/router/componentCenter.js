export default [
  // 商品管理中心
  {
    path: '/commodity/list',
    name: 'commodity-list',
    component: () => import('@/views/commodity/list'),
    meta: {
      title: '商品管理'
    }
  },
  {
    path: '/commodity/productLabel',
    name: 'commodity-productLabel',
    component: () => import('@/views/commodity/productLabel'),
    meta: {
      title: '商品标签'
    }
  },
  {
    path: '/commodity/pushProductCensus/list',
    name: 'commodity-pushProductCensus-list',
    component: () => import('@/views/commodity/pushProductCensus/list'),
    meta: {
      title: '推品统计'
    }
  },
  {
    path: '/commodity/pushProductManage/list',
    name: 'commodity-pushProductManage-list',
    component: () => import('@/views/commodity/pushProductManage/list'),
    meta: {
      title: '推品管理'
    }
  },
  {
    path: '/commodity/pushProductManage/add',
    name: '/commodity/pushProductManage/add',
    component: () => import('@/views/commodity/pushProductManage/add'),
    meta: {
      title: '推品清单-创建'
    }
  },
  {
    path: '/commodity/pushProductManage/detail',
    name: '/commodity/pushProductManage/detail',
    component: () => import('@/views/commodity/pushProductManage/detail'),
    meta: {
      title: '推品清单-查看'
    }
  },
  {
    path: '/commodity/pushProductManage/edit',
    name: '/commodity/pushProductManage/edit',
    component: () => import('@/views/commodity/pushProductManage/edit'),
    meta: {
      title: '推品清单-编辑'
    }
  },
  {
    path: '/commodity/pushProductManage/copy',
    name: '/commodity/pushProductManage/copy',
    component: () => import('@/views/commodity/pushProductManage/copy'),
    meta: {
      title: '推品清单-复制'
    }
  },
  {
    path: '/commodity/add',
    name: '/commodity/add',
    component: () => import('@/views/commodity/add'),
    meta: {
      title: '商品管理中心-新增商品'
    },
    hidden: true
  },
  {
    path: '/commodity/import-commodity',
    name: '/commodity/import-commodity',
    component: () => import('@/views/commodity/import-commodity'),
    meta: {
      title: '商品管理中心-导入商品'
    },
    hidden: true
  },
  {
    path: '/commodity/edit/:id',
    name: '/commodity/edit/:id',
    component: () => import('@/views/commodity/edit'),
    meta: {
      title: '商品管理中心-修改商品',
      code: 'commodity-list-edit'
    },
    hidden: true
  },
  {
    path: '/commodity/detail/:id',
    name: '/commodity/detail/:id',
    component: () => import('@/views/commodity/detail'),
    meta: {
      title: '商品管理中心-查看商品',
      code: '/commodity/list/:detail'
    },
    hidden: true
  },
  {
    path: '/commodity/copy/:id',
    name: '/commodity/copy/:id',
    component: () => import('@/views/commodity/copy'),
    meta: {
      title: '商品管理中心-复制商品',
      code: 'commodity-list-edit'
    },
    hidden: true
  },
  // 年框商品管理
  {
    path: '/commodity/yearly/list',
    name: 'commodity-yearly-list',
    component: () => import('@/views/commodity/yearly-commodity/list'),
    meta: {
      title: '年框商品池'
    }
  },
  {
    path: '/commodity/yearly/edit/:id',
    name: 'commodity-yearly-edit',
    component: () => import('@/views/commodity/yearly-commodity/edit'),
    meta: {
      title: '年框商品池-编辑'
    },
    hidden: true
  },
  {
    path: '/commodity/yearly/detail/:id',
    name: 'commodity-yearly-detail',
    component: () => import('@/views/commodity/yearly-commodity/detail'),
    meta: {
      title: '年框商品池-查看'
    },
    hidden: true
  }
];
