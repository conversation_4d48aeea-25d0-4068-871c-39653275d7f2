export default [
  // 店铺数据概况
  /* -----------------------------------------------------店铺数据概况------------------------------------------------------ */
  {
    path: '/shop/data',
    name: '/shop/data',
    title: '数据实时概况',
    component: () => import('@/views/shop/real-time-data'),
    meta: {
      title: '数据实时概况'
    }
  },

  {
    path: '/shop/top-goods',
    name: '/shop/top-goods',
    component: () => import('@/views/shop/top-goods'),
    hidden: true,
    meta: {
      title: '商品销售TOP10'
    }
  },
  {
    path: '/shop/order-analysis',
    name: '/shop/order-analysis',
    component: () => import('@/views/shop/order-analysis'),
    meta: {
      title: '订单明细'
    },
    hidden: true
  },
  {
    path: '/shop/data-panel',
    name: '/shop/data-panel',
    component: () => import('@/views/shop/data-panel'),
    meta: {
      title: '数据看板'
    },
    hidden: true
  },
  {
    path: '/shop/goods-data',
    name: '/shop/goods-data',
    component: () => import('@/views/shop/goods-data'),
    meta: {
      title: '商品数据分析'
    },
    hidden: true
  },
  {
    path: '/shop/distributor-statistic',
    name: '/shop/distributor-statistic',
    component: () => import('@/views/shop/distributor-statistic'),
    meta: {
      title: '分销商明细'
    },
    hidden: true
  },
  {
    path: '/shop/inventory',
    name: '/shop/inventory',
    component: () => import('@/views/shop/inventory'),
    meta: {
      title: '售罄/库存过少列表'
    },
    hidden: true
  },
  {
    path: '/shop/distribution-moving',
    name: '/shop/distribution-moving',
    component: () => import('@/views/shop/distribution-moving'),
    meta: {
      title: '动销分销商'
    },
    hidden: true
  },
  /* -----------------------------------------------------品牌销售分析------------------------------------------------------ */
  {
    path: '/shop/brand-data/list',
    name: '/shop/brand-data/list',
    component: () => import('@/views/shop/brand/brand-data'),
    meta: {
      title: '品牌采购详情'
    }
  },
  {
    path: '/shop/brand/distributor-commodity-purchase',
    name: '/shop/brand/distributor-commodity-purchase',
    component: () => import('@/views/shop/brand/distributor-commodity-purchase'),
    meta: {
      title: '分销商商品采购排行'
    }
  },
  {
    path: '/shop/brand-data/brand-purchase',
    name: '/shop/brand-data/brand-purchase',
    component: () => import('@/views/shop/brand/brand-purchase'),
    meta: {
      title: '品牌商品采购列表'
    }
  },
  {
    path: '/shop/brand-data/brand-purchase/commodity-distribution-purchase',
    name: '/shop/brand-data/brand-purchase/commodity-distribution-purchase',
    component: () => import('@/views/shop/brand/commodity-distributor-purchase'),
    meta: {
      title: '商品分销商采购排行'
    }
  },
  {
    path: '/shop/brand-data/brand-virtual-credit',
    name: '/shop/brand-data/brand-virtual-credit',
    component: () => import('@/views/shop/brand/brand-virtual-credit'),
    meta: {
      title: '返利使用明细'
    }
  }
];
