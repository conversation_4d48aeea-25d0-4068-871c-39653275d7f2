export default [
  // 进销存概览
  {
    path: '/purchase-sell-stock-management/overview',
    name: 'purchase-sell-stock-statement-overview',
    component: () => import('@/views/purchase-sell-stock-management/overview')
  },
  // 进销存报表-分销商维度
  {
    path: '/purchase-sell-stock-management/purchase-sell-stock-statement/distributor-report',
    name: 'purchase-sell-stock-statement-distributor-report',
    component: () => import('@/views/purchase-sell-stock-management/purchase-sell-stock-statement/distributor-report.vue')
  },
  // 进销存报表-品牌维度
  {
    path: '/purchase-sell-stock-management/purchase-sell-stock-statement/brand-report',
    name: 'purchase-sell-stock-statement-brand-report',
    component: () => import('@/views/purchase-sell-stock-management/purchase-sell-stock-statement/brand-report.vue')
  },
  // 进销存整体看板
  {
    path: '/purchase-sell-stock-management/purchase-sell-stock-statement/over-all-report',
    name: 'purchase-sell-stock-statement-overview-over-all-report',
    component: () => import('@/views/purchase-sell-stock-management/over-all-report.vue')
  },
  // 报表明细
  {
    path: '/purchase-sell-stock-management/purchase-sell-stock-statement/details-report',
    name: 'purchase-sell-stock-statement-details-report',
    component: () => import('@/views/purchase-sell-stock-management/purchase-sell-stock-statement/details-report')
  },
  /* -----------------------------------------------------进销存报表------------------------------------------------------ */
  // 进销存录入
  {
    path: '/purchase-sell-stock-management/sales-data/sales-data-entry',
    name: 'purchase-sell-stock-sales-data-entry',
    component: () => import('@/views/purchase-sell-stock-management/sales-data/sales-data-entry')
  },
  // 进销存录入-导入
  {
    path: '/purchase-sell-stock-management/sales-data/sales-data-import',
    name: 'purchase-sell-stock-sales-data-import',
    component: () => import('@/views/purchase-sell-stock-management/sales-data/sales-data-import')
  },
  // 进销存录入-录入记录
  {
    path: '/purchase-sell-stock-management/sales-data/sales-data-import-records',
    name: 'purchase-sell-stock-sales-data-import-records',
    component: () => import('@/views/purchase-sell-stock-management/sales-data/sales-data-import-records')
  },
  // 进销存设置
  {
    path: '/purchase-sell-stock-management/setting',
    name: 'purchase-sell-stock-management-setting',
    component: () => import('@/views/purchase-sell-stock-management/setting')
  },
];
