export function isvalidUsername(str) {
  const valid_map = ['admin', 'editor'];
  return valid_map.indexOf(str.trim()) >= 0;
}

/* 合法uri*/
export function validateURL(textval) {
  const urlregex = /^(https?|ftp):\/\/([a-zA-Z0-9.-]+(:[a-zA-Z0-9.&%$-]+)*@)*((25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]?)(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}|([a-zA-Z0-9-]+\.)*[a-zA-Z0-9-]+\.(com|edu|gov|int|mil|net|org|biz|arpa|info|name|pro|aero|coop|museum|[a-zA-Z]{2}))(:[0-9]+)*(\/($|[a-zA-Z0-9.,?'\\+&%$#=~_-]+))*$/;
  return urlregex.test(textval);
}

/* 小写字母*/
export function validateLowerCase(str) {
  const reg = /^[a-z]+$/;
  return reg.test(str);
}

/* 大写字母*/
export function validateUpperCase(str) {
  const reg = /^[A-Z]+$/;
  return reg.test(str);
}

/* 大小写字母*/
export function validatAlphabets(str) {
  const reg = /^[A-Za-z]+$/;
  return reg.test(str);
}

/* 整数金钱校验，位数不大于999999999*/
export function validateMoney(value) {
  const reg = /^([1-9][0-9]*)$/;
  if (value === null || value === '') {
    return '请输入授信额度';
  } else if (reg.test(value)) {
    if (value > 999999999) {
      return '授信额度最大不能大于999999999';
    } else {
      return true;
    }
  } else {
    return '数据格式不正确，请输入大于1的整数';
  }
}
/* 整数校验——库存*/
export function isInteger(value) {
  const reg = /^([1-9]\d*|[0]{1,1})$/;
  if (reg.test(value)) {
    return true;
  } else {
    return false;
  }
}

/* 整数校验——库存大于0*/
export function isInteger2(value) {
  const reg = /^\+?[1-9]\d*$/;
  if (reg.test(value)) {
    return true;
  } else {
    return false;
  }
}

/* 保留两位小数的金钱校验 */
export function validateMoney2(value) {
  const reg = /^([1-9]{1}[0-9]{0,3}(\,[0-9]{3,4})*(\.[0-9]{0,2})?|[1-9]{1}\d*(\.[0-9]{0,2})?|0(\.[0-9]{0,2})?|(\.[0-9]{1,2})?)$/;
  if (reg.test(value)) {
    return true;
  } else {
    return false;
  }
}

/* 保留两位小数的数字校验 */
export function validateFloatNum(value) {
  const reg = /^([1-9]{1}[0-9]{0,3}(\,[0-9]{3,4})*(\.[0-9]{0,2})?|[1-9]{1}\d*(\.[0-9]{0,2})?|0(\.[0-9]{0,2})?|(\.[0-9]{1,2})?)$/;
  if (reg.test(value)) {
    return true;
  } else {
    return false;
  }
}

/* 保留两位小数的百分数校验 */
export function isPercent(value) {
  const reg = /^(\d|[1-9]\d|100)(\.\d{1,2})?$/;
  if (reg.test(value)) {
    return true;
  } else {
    return false;
  }
}

/* 整数的1-100百分数校验 */
export function isIntPercent(value) {
  const reg = /^([1-9][0-9]{0,1}|100)$/;
  if (reg.test(value)) {
    return true;
  } else {
    return false;
  }
}
/**
 * 判断字符串是否是纯数字
 * @param str
 * @returns {boolean}
 */
export function isNumber(str) {
  if (typeof str === 'number') {
    return true;
  }
  if (!str) {
    return false;
  }
  return parseFloat(str, 10).toString() === str;
}
/**
 * 判断字符串是否全是数字
 * @param str
 * @returns {boolean}
 */
export function isAllNumber(str) {
  return /^\d{0,}$/.test(str);
}
/**
 * 判断是否是身份证
 * 参考链接http://www.css88.com/archives/7982
 * @param idcard
 * @returns {boolean}
 */
export function isIDCard(idcard) {
  idcard = idcard.toUpperCase(); // 对身份证号码做处理
  let ereg;

  let Y, JYM;
  let S, M;
  /* 基本校验 */
  // if (String.isNullOrEmpty(idcard)) return false;
  let idcard_array = [];
  idcard_array = idcard.split('');
  /* 身份号码位数及格式检验 */
  switch (idcard.length) {
    case 15:
      if (
        (parseInt(idcard.substr(6, 2)) + 1900) % 4 === 0 ||
        ((parseInt(idcard.substr(6, 2)) + 1900) % 100 === 0 &&
          (parseInt(idcard.substr(6, 2)) + 1900) % 4 === 0)
      ) {
        ereg = /^[1-9][0-9]{5}[0-9]{2}((01|03|05|07|08|10|12)(0[1-9]|[1-2][0-9]|3[0-1])|(04|06|09|11)(0[1-9]|[1-2][0-9]|30)|02(0[1-9]|[1-2][0-9]))[0-9]{3}$/; // 测试出生日期的合法性
      } else {
        ereg = /^[1-9][0-9]{5}[0-9]{2}((01|03|05|07|08|10|12)(0[1-9]|[1-2][0-9]|3[0-1])|(04|06|09|11)(0[1-9]|[1-2][0-9]|30)|02(0[1-9]|1[0-9]|2[0-8]))[0-9]{3}$/; // 测试出生日期的合法性
      }
      if (ereg.test(idcard)) {
        return true;
      } else {
        return false;
      }
    case 18:
      // 18位身份号码检测
      // 出生日期的合法性检查
      // 闰年月日:((01|03|05|07|08|10|12)(0[1-9]|[1-2][0-9]|3[0-1])|(04|06|09|11)(0[1-9]|[1-2][0-9]|30)|02(0[1-9]|[1-2][0-9]))
      // 平年月日:((01|03|05|07|08|10|12)(0[1-9]|[1-2][0-9]|3[0-1])|(04|06|09|11)(0[1-9]|[1-2][0-9]|30)|02(0[1-9]|1[0-9]|2[0-8]))
      if (
        parseInt(idcard.substr(6, 4)) % 4 === 0 ||
        (parseInt(idcard.substr(6, 4)) % 100 === 0 &&
          parseInt(idcard.substr(6, 4)) % 4 === 0)
      ) {
        ereg = /^[1-9][0-9]{5}19[0-9]{2}((01|03|05|07|08|10|12)(0[1-9]|[1-2][0-9]|3[0-1])|(04|06|09|11)(0[1-9]|[1-2][0-9]|30)|02(0[1-9]|[1-2][0-9]))[0-9]{3}[0-9XxAa]$/; // 闰年出生日期的合法性正则表达式
      } else {
        ereg = /^[1-9][0-9]{5}19[0-9]{2}((01|03|05|07|08|10|12)(0[1-9]|[1-2][0-9]|3[0-1])|(04|06|09|11)(0[1-9]|[1-2][0-9]|30)|02(0[1-9]|1[0-9]|2[0-8]))[0-9]{3}[0-9XxAa]$/; // 平年出生日期的合法性正则表达式
      }
      if (ereg.test(idcard)) {
        // 测试出生日期的合法性
        // 计算校验位
        S =
          (parseInt(idcard_array[0]) + parseInt(idcard_array[10])) * 7 +
          (parseInt(idcard_array[1]) + parseInt(idcard_array[11])) * 9 +
          (parseInt(idcard_array[2]) + parseInt(idcard_array[12])) * 10 +
          (parseInt(idcard_array[3]) + parseInt(idcard_array[13])) * 5 +
          (parseInt(idcard_array[4]) + parseInt(idcard_array[14])) * 8 +
          (parseInt(idcard_array[5]) + parseInt(idcard_array[15])) * 4 +
          (parseInt(idcard_array[6]) + parseInt(idcard_array[16])) * 2 +
          parseInt(idcard_array[7]) +
          parseInt(idcard_array[8]) * 6 +
          parseInt(idcard_array[9]) * 3;
        Y = S % 11;
        M = 'F';
        JYM = '10X98765432';
        M = JYM.substr(Y, 1);
        /* 判断校验位 */
        if (M === idcard_array[17]) {
          // 检测ID的校验位false
          return true;
        } else if (idcard_array[17] === 'A') {
          // A结尾不校验规则
          return true;
          /* 检测ID的校验位false; */
        } else {
          return false;
        }
      } else {
        return false;
      }
    default:
      return false;
  }
}

export function isPhoneNumber(str) {
  return /^(?=\d{11}$)^1(?:3\d|4[57]|5[^4\D]|66|7[^249\D]|8\d|9[89])\d{8}$/.test(
    str
  );
}

export function isSimplePhoneNum(str) {
  return /^1\d{10}$/.test(str);
}
/* 折扣校验 大于0小于10且保留一位小数*/
export function validatediscount1(value) {
  const reg = /^([0-9](\.\d)?|9)$/;
  if (reg.test(value)) {
    return true;
  } else {
    return false;
  }
}

// 校验数字的范围
export function validateRangeNumber(min, max) {
  return (_, value, callback) => {
    value = parseFloat(value);
    if ((min != null && value < min) || (max != null && value > max)) {
      callback(new Error('数字超出范围'));
    } else {
      callback();
    }
  };
}

// 校验数字
export function validateNumber(_, value, callback) {
  const str = `${value}`;
  if (!str || /^-?\d+\.?\d*$/.test(str)) {
    callback();
  } else {
    callback('请填写数字');
  }
}

// el-form 校验数字为正整数
export function validateInteger(_, value, callback) {
  if (!value || /^\d+$/.test(`${value}`)) {
    callback();
  } else {
    callback(new Error('请填写整数'));
  }
}

// el-form 校验数字为整数
export function validateIntegerAll(_, value, callback) {
  if (!value || /^-?[1-9]\d*$/.test(`${value}`)) {
    callback();
  } else {
    callback(new Error('请填写整数'));
  }
}

// 校验时间范围
export const validateRangeDate = (start, end) => (_, value, callback) => {
  const [startDate, endDate] = value;
  if (
    value.length &&
    ((start && startDate < start) || (end && endDate < end))
  ) {
    callback(new Error('超出时间范围'));
  } else {
    callback();
  }
};

// 校验手机号   (宽松), 只要是13,14,15,16,17,18,19开头即可
export function validatePhoneLoose(_, value, callback) {
  const str = `${value}`;
  const regular = /^(?:(?:\+|00)86)?1[3-9]\d{9}$/;
  if (regular.test(str)) {
    callback();
  } else {
    callback(new Error('请填写正确手机号'));
  }
}

// 校验手机号   只校验位数11位
export function validatePhoneLooseDigits(_, value, callback) {
  const str = `${value}`;
  if (str.length === 11) {
    callback();
  } else {
    callback(new Error('请填写正确手机号'));
  }
}

// 校验邮箱
export function validateEmail(_, value, callback) {
  const str = `${value}`;
  const regular = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
  if (regular.test(str)) {
    callback();
  } else {
    callback(new Error('请填写正确邮箱'));
  }
}

// 检验大于0且最多保留两位小数
export function validatePositiveNumber(_, value, callback) {
  const str = `${value}`;
  if (/^\d+(\.\d{1,2})?$/.test(str) && parseFloat(str) > 0) {
    callback();
  } else {
    callback(new Error('请填写大于0的数字，最多保留两位小数'));
  }
}

// 校验大于1最多保留1位小数点的数字
export function validateMultiple(_, value, callback) {
  const str = `${value}`;
  if (/^\d+(\.\d{1})?$/.test(str) && parseFloat(str) > 1) {
    callback();
  } else {
    callback(new Error('请填写大于1的数字，最多保留1位小数'));
  }
}
