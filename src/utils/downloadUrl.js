import { withExtTenantIdRequest } from '@/utils/request';
import axios from 'axios';
import <PERSON><PERSON><PERSON><PERSON> from 'jszip';
import FileSaver from 'file-saver';
import { parseTime } from '@/utils';

export function downloadUrl(src, fileName, fileType, isNotImage) {
  if (isNotImage) {
    // 判断是否为chrome里的图片
    fileLinkToStreamDownload(src, fileName, fileType);
  } else {
    ImgtodataURL(src, fileName, fileType);
  }
}
export function fileLinkToStreamDownload(url, fileName, type) {
  const xhr = new XMLHttpRequest();
  xhr.open('get', url, true);
  xhr.setRequestHeader('Content-type', `application/${type}`);
  xhr.responseType = 'blob';
  xhr.onload = function () {
    if (this.status === 200) {
      const blob = this.response;
      downloadNormalFile(blob, fileName);
    }
  };
  xhr.send();
}

function downloadNormalFile(blob, filename) {
  const eleLink = document.createElement('a');
  let href = blob;
  if (typeof blob === 'string') {
    eleLink.target = '_blank';
  } else {
    href = window.URL.createObjectURL(blob); // 创建下载的链接
  }
  eleLink.href = href;
  eleLink.download = filename; // 下载后文件名
  eleLink.style.display = 'none';
  // 触发点击
  document.body.appendChild(eleLink);
  eleLink.click(); // 点击下载
  // 下载完成移除元素
  document.body.removeChild(eleLink);
  if (typeof blob === 'string') {
    window.URL.revokeObjectURL(href); // 释放掉blob对象
  }
}

function ImgtodataURL(url, filename, fileType) {
  getBase64(url, fileType, (_baseUrl) => {
    // 创建隐藏的可下载链接
    const eleLink = document.createElement('a');
    eleLink.download = filename;
    eleLink.style.display = 'none';
    // 图片转base64地址
    eleLink.href = _baseUrl;
    // 触发点击
    document.body.appendChild(eleLink);
    eleLink.click();
    // 然后移除
    document.body.removeChild(eleLink);
  });
}
function getBase64(url, fileType, callback) {
  // 通过构造函数来创建的 img 实例，在赋予 src 值后就会立刻下载图片
  const Img = new Image();
  let dataURL = '';
  Img.src = url;
  Img.setAttribute('crossOrigin', 'Anonymous');
  Img.onload = function () {
    // 要先确保图片完整获取到，这是个异步事件
    const canvas = document.createElement('canvas'); // 创建canvas元素
    const width = Img.width; // 确保canvas的尺寸和图片一样
    const height = Img.height;
    canvas.width = width;
    canvas.height = height;
    canvas.getContext('2d').drawImage(Img, 0, 0, width, height); // 将图片绘制到canvas中
    dataURL = canvas.toDataURL('image/' + fileType); // 转换图片为dataURL
    callback ? callback(dataURL) : null;
  };
}

export const getBlob = function (url) {
  return axios({
    method: 'get',
    url,
    responseType: 'blob',
    headers: {'Cache-Control': 'no-cache'}
  }).then((data) => data.data);
};

export const getFileBinary = function (tree) {
  const promises = [];
  let flag = true;
  const treeList = (data, pfile) => {
    const { name, children } = data;
    const file = name ? pfile.folder(name) : pfile;
    children.forEach((options) => {
      const { url, children } = options;

      if (flag && children) treeList(options, file);
      if (url) {
        // 下载文件, 并存成ArrayBuffer对象
        const arr_name = url.split('/');
        const file_name = arr_name[arr_name.length - 1]; // 获取文件名
        const promise = getBlob(url)
          .then((data) => {
            file.file(file_name, data, {
              binary: true
            });
          })
          .catch(() => {
            flag = false;
            // this.$message.error(`${name}/${file_name}下载失败`);
          });
        promises.push(promise);
      }
    });
  };
  const zip = new JSZip();
  treeList(tree, zip);

  return Promise.all(promises).then(
    () =>
      flag &&
      zip.generateAsync({
        type: 'blob'
      })
  );
};

export const downloadFile = function (binary, name) {
  // 生成二进制流
  FileSaver.saveAs(binary, `${name}-${parseTime(Date.now(), '{y}-{m}-{d}-{h}:{i}:{s}')}.zip`); // 利用file-saver保存文件  自定义文件名
};

export const uploadFile = function (binary, name) {
  const formData = new FormData();
  formData.append('file', binary, name);

  return withExtTenantIdRequest({
    url: '/file/api/file/upload',
    method: 'post',
    data: formData
  });
};

// 下载oss文件
export function downloadOssFile(url, fileName){
  const xhr = new XMLHttpRequest();
  xhr.open('GET', url, true);
  xhr.responseType = 'blob';
  xhr.onload = function() {
    if (xhr.status === 200) {
      const blob = xhr.response;
      const link = document.createElement('a');
      link.href = URL.createObjectURL(blob);
      link.download = fileName;
      link.style.display = 'none';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(link.href);
      console.log('文件下载成功');
    } else {
      console.error('文件下载失败:', xhr.statusText);
    }
  };
  xhr.onerror = function() {
    console.error('网络错误:', xhr.statusText);
  };
  xhr.send();
}
