import round from 'lodash/round';

export default function filterPrice(price) {
  const [a, b] = getPrice(price);
  return `${a}.${b}`;
}

/**
 * 1. 解决了浮点精度问题
 * 2. 价格必须大于零
 * 3. 返回值四舍五入精确到分
 * 4. 解决0.999进1问题
 */
export function getPrice(number) {
  const price = round(number, 2); // 先四舍五入 防止0.999进1问题
  const floatNum = parseInt((price * 100) % 100, 10);
  return [parseInt(price, 10), floatNum < 10 ? `0${floatNum}` : floatNum];
}

export function testPrice(price) {
  return /^[0-9|\.]*$/.test(price) && parseFloat(price) <= 99999999;
}
