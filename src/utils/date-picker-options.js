/*eslint-disable*/
const getDateFn = {
  // 1、 得到今天、昨天、明天日期
  // dates为数字类型，0代表今日,-1代表昨日，1代表明日，返回yyyy-mm-dd格式字符串，dates不传默认代表今日。
  getDate (dates) {
    const dd = new Date()
    const n = dates || 0
    dd.setDate(dd.getDate() + n)
    const y = dd.getFullYear()
    let m = dd.getMonth() + 1
    let d = dd.getDate()
    m = m < 10 ? '0' + m : m
    d = d < 10 ? '0' + d : d
    return y + '-' + m + '-' + d;
  },
  // 2、得到本周、上周、下周的起始、结束日期
  // type为字符串类型，有两种选择，"s"代表开始,"e"代表结束，dates为数字类型，不传或0代表本周，-1代表上
  getMonday (type, dates) {
    const now = new Date()
    const nowTime = now.getTime()
    let day = now.getDay()
    const longTime = 24 * 60 * 60 * 1000
    const n = longTime * 7 * (dates || 0)
    let dd
    if (type === 's') {
      dd = nowTime - (day - 1) * longTime + n
    }
    if (type === 'e') {
      dd = nowTime + (7 - day) * longTime + n
    }
    dd = new Date(dd)
    const y = dd.getFullYear()
    let m = dd.getMonth() + 1
    let d = dd.getDate()
    m = m < 10 ? '0' + m : m
    d = d < 10 ? '0' + d : d
    day = y + '-' + m + '-' + d
    return day
  },
  // 3、得到本月、上月、下月的起始、结束日期
  // type为字符串类型，有两种选择，"s"代表开始,"e"代表结束，months为数字类型，0代表本月，-1代表上月，1代表下月
  getMonth (type, months) {
    const d = new Date()
    let year = d.getFullYear()
    let month = d.getMonth() + 1
    if (Math.abs(months) > 12) {
      months = months % 12
    }
    if (months !== 0) {
      if (month + months > 12) {
        year++
        month = (month + months) % 12
      } else if (month + months < 1) {
        year--
        month = 12 + month + months
      } else {
        month = month + months
      }
    }
    month = month < 10 ? '0' + month : month
    const date = d.getDate()
    const firstday = year + '-' + month + '-' + '01'
    let lastday = ''
    if (month === '01' || month === '03' || month === '05' || month === '07' || month === '08' || month === '10' || month === '12') {
      lastday = year + '-' + month + '-' + 31
    } else if (month === '02') {
      if ((year % 4 === 0 && year % 100 !== 0) || (year % 100 === 0 && year % 400 === 0)) {
        lastday = year + '-' + month + '-' + 29
      } else {
        lastday = year + '-' + month + '-' + 28
      }
    } else {
      lastday = year + '-' + month + '-' + 30
    }
    let day = ''
    if (type === 's') {
      day = firstday
    } else {
      day = lastday
    }
    return day
  },
  // 4、得到今年、去年、明年的开始、结束日期
  // type为字符串类型，有两种选择，"s"代表开始,"e"代表结束，dates为数字类型，不传或0代表今年，-1代表去年，1代表明年
  getYear (type, dates) {
    const dd = new Date()
    const n = dates || 0
    const year = dd.getFullYear() + Number(n)
    let day
    if (type === 's') {
      day = year + '-01-01'
    }
    if (type === 'e') {
      day = year + '-12-31'
    }
    if (!type) {
      day = year + '-01-01/' + year + '-12-31'
    }
    return day
  }
}

export function getDateRange (type) {
  const today = new Date()
  const start = new Date()
  start.setHours(0, 0, 0)

  const end = new Date()
  end.setHours(23, 59, 59)

  if (type === 'recent7Days') {
    const recent7End = end
    const recent7Start = new Date(start.getTime() - 3600 * 1000 * 24 * 6)
    return [recent7Start, recent7End]
  } else if (type === 'recent30Days') {
    const recent30End = end
    const recent30Start = new Date(start.getTime() - 3600 * 1000 * 24 * 29)
    return [recent30Start, recent30End]
  } else if (type === 'thisWeek') {
    const today = new Date()
    today.setHours(0, 0, 0)
    const thisWeekStart = new Date(today.setDate(today.getDate() - today.getDay() + (today.getDay() === 0 ? -6 : 1)))
    let thisWeekEnd = new Date(today.setDate(thisWeekStart.getDate() + 6))
    thisWeekEnd = thisWeekEnd > new Date() ? new Date() : thisWeekEnd
    thisWeekEnd.setHours(23, 59, 59)
    return [thisWeekStart, thisWeekEnd]
  } else if (type === 'lastWeek') {
    const firstDay = new Date(getDateFn.getMonday('s', -1))
    const lastDay = new Date(getDateFn.getMonday('e', -1))
    firstDay.setHours(0, 0, 0)
    lastDay.setHours(23, 59, 59)
    return [firstDay, lastDay]
  } else if (type === 'thisMonth') {
    const thisMonthStart = new Date(today.getFullYear(), today.getMonth(), 1)
    let thisMonthEnd = new Date(today.getFullYear(), today.getMonth() + 1, 0)
    thisMonthEnd = thisMonthEnd > new Date() ? new Date() : thisMonthEnd
    thisMonthEnd.setHours(23, 59, 59)
    return [thisMonthStart, thisMonthEnd]
  } else if (type === 'lastMonth') {
    const lastMonthStart = new Date(today.getFullYear(), today.getMonth() - 1, 1)
    const lastMonthEnd = new Date(today.getFullYear(), today.getMonth(), 0)
    lastMonthEnd.setHours(23, 59, 59)
    return [lastMonthStart, lastMonthEnd]
  } else if (type === 'thisQuarter') {
    const thisQuarterStart = new Date(today.getFullYear(), Math.floor(today.getMonth() / 3) * 3, 1)
    let thisQuarterEnd = new Date(today.getFullYear(), thisQuarterStart.getMonth() + 3, 0)
    thisQuarterEnd = thisQuarterEnd > new Date() ? new Date() : thisQuarterEnd
    thisQuarterEnd.setHours(23, 59, 59)
    return [thisQuarterStart, thisQuarterEnd]
  } else if (type === 'lastQuarter') {
    const lastQuarterEnd = new Date(today.getFullYear(), Math.floor(today.getMonth() / 3) * 3, 0)
    const lastQuarterStart = new Date(lastQuarterEnd.getFullYear(), lastQuarterEnd.getMonth() - 2, 1)
    lastQuarterEnd.setHours(23, 59, 59)
    return [lastQuarterStart, lastQuarterEnd]
  } else if (type === 'thisYear') {
    const thisYearStart = new Date(today.getFullYear(), 0, 1)
    let thisYearEnd = new Date(today.getFullYear(), 11, 31)
    thisYearEnd = thisYearEnd > new Date() ? new Date() : thisYearEnd
    thisYearEnd.setHours(23, 59, 59)
    return [thisYearStart, thisYearEnd]
  } else {
    return []
  }
}
