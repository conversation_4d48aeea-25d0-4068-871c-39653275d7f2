/**
 * 处理输入框输入价格，默认精确到小数点后2位
 * @param {*} value 需要处理的数据
 * @param {*} point 限制输入小数点位数
 * @returns
 */
export function handlePrice(value, point = 2) {
  // 移除多余的数字和小数点
  let newValue = value.replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1');
  // 检查是否有小数点，如果有，限制小数点后的位数
  if (newValue.includes('.')) {
    const parts = newValue.split('.');
    if (parts[1].length > point) {
      newValue = `${parts[0]}.${parts[1].substring(0, point)}`;
    }
  }
  // 防止输入空的小数点
  if (newValue === '.') {
    newValue = '';
  }
  return newValue;
}

/**
 * 处理输入框输入正整数，并限制输入数字的位数
 * @param {string} value 需要处理的数据
 * @param {number} maxLength 限制输入数字的最大位数
 * @returns {string} 格式化后的正整数字符串
 */
export function handleInteger(value, maxLength = 10) {
  // 移除非数字字符
  let newValue = value.replace(/\D/g, '');

  // 限制输入长度
  if (newValue.length > maxLength) {
    newValue = newValue.substring(0, maxLength);
  }

  return newValue;
}
