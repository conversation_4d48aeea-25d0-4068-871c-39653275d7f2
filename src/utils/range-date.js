import isEmpty from 'lodash/isEmpty';
export default function getRangeDate(rangeDate, start, end) {
  if (isEmpty(rangeDate)) {
    return undefined;
  }
  const res = {};
  [start, end].forEach((key, i) => {
    res[key] = rangeDate[i].valueOf();
  });
  return res;
}
// 格式化
export function parseRangeDate(obj, start, end) {
  if (!start || !end || !obj) return [];
  const startDate = obj[start];
  const endDate = obj[end];
  return startDate && endDate ? [new Date(startDate), new Date(endDate)] : [];
}
