export function getOption($this, keys) {
  let set;
  if (keys) {
    set = new Set(keys);
  }
  const options = [
    {
      label: '有动销单品数',
      prop: 'specCodeQuantity'
    },

    {
      label: '初次授权人数',
      prop: 'firstLicenseQuantity'
    },

    {
      label: '复购人数采购金额',
      prop: 'repurchaseSaleAmount'
    },

    {
      label: '复购人数',
      prop: 'repurchaseDistributorQuantity'
    },

    {
      label: '首采人数采购金额',
      prop: 'firstSaleAmount'
    },

    {
      label: '首采人数',
      prop: 'firstDistributorQuantity'
    },

    {
      label: '采购人数',
      prop: 'purchaseDistributorQuantity',
      sortable: 'custom',
      parseLink: row => {
        return todistributionPurchase(row, 'distributionPurchase', $this.data);
      }
    },

    {
      label: '采购件数',
      prop: 'purchaseCommodityQuantity',
      sortable: 'custom'
    },
    {
      label: '订单数',
      prop: 'purchaseOrderQuantity',
      sortable: 'custom',
      parseLink: row => {
        const { brandId } = row; // 品牌信息要从行数据拿
        const {
          csIds, // 专属顾问
          purchaseType, // 采货类型 采销 一件代发
          startDate, // 开始时间
          endDate, // 结束时间
          orderChannels, // 终端 :小程序 app pc
          groupIds, // 团队 水羊直供、CP集团、水羊国际
          channels // 经营渠道  淘宝等
        } = $this.data;
        const data = {
          csIds,
          brandId,
          purchaseType,
          beginDate: startDate,
          endDate,
          orderChannels,
          groupIds,
          channels
        };
        brandId ? (data.brandIds = [brandId]) : '';
        sessionStorage.setItem('/order/inquiry/list', JSON.stringify(data));
        // 订单查询
        return '/order/inquiry/list';
      }
    },

    {
      label: '返利使用',
      prop: 'usedVirtualCredit',
      sortable: 'custom',
      parseLink: row => {
        return todistributionPurchase(row, 'usedVirtualCredit', $this.data);
      }
    },

    {
      label: '采购金额',
      prop: 'saleAmount',
      sortable: 'custom',
      parseLink: row => {
        return todistributionPurchase(row, 'brandPurchase', $this.data);
      }
    }
  ];
  return keys ? options.filter(option => set.has(option.prop)) : options;
}

export function todistributionPurchase(row, type, $this) {
  const { brandId, brandName, usedVirtualCredit } = row; // 品牌信息要从行数据拿
  const {
    csIds, // 专属顾问
    purchaseType, // 采货类型 采销 一件代发
    startDate, // 开始时间
    endDate, // 结束时间
    time, // 时间的分类
    orderChannels, // 终端 :小程序 app pc
    groupIds, // 团队 水羊直供、CP集团、水羊国际
    channels // 经营渠道  淘宝等
  } = $this;
  let link = '';
  const data = {
    time,
    brandId,
    brandName,
    customerServiceIds: csIds,
    csIds,
    statisticsBy: 'payTime',
    totalUsedVirtualCredit: usedVirtualCredit,
    purchaseType,
    beginDate: startDate,
    endDate,
    orderChannels,
    groupIds,
    channels
  };
  brandId ? (data.brandIds = [brandId]) : '';
  if (type === 'distributionPurchase') {
    // 分销明细
    link = '/shop/distributor-statistic';
    sessionStorage.setItem(link, JSON.stringify(data));
  } else if (type === 'brandPurchase') {
    // 品牌商品采购列表
    link = '/shop/brand-data/brand-purchase';
    sessionStorage.setItem('brandPurchaseData', JSON.stringify(data));
  } else if (type === 'usedVirtualCredit') {
    // 返利使用明细
    link = '/shop/brand-data/brand-virtual-credit';
    sessionStorage.setItem(link, JSON.stringify(data));
  }
  return link;
}
