// 图片处理js
// oss 阿里云图片缩放、图片压缩公共方法  定义宽度  文档： https://help.aliyun.com/document_detail/44688.html
export function photo_resize_w(url, size = '750') {
  if (!url || typeof url !== 'string') return '';
  const prs = `x-oss-process=image/resize,w_${size},limit_1`;
  if (url.indexOf('?') >= 0) {
    const [u1, u2] = url.split('?');
    return `${u1}?${u2}&${prs}`;
  }
  return `${url}?${prs}`;
}

// oss 阿里云图片格式转换 jpg/png/webp/bmp/gif/tiff/heic/avif
export function photo_ossformat(url, format = 'webp') {
  if (!url || typeof url !== 'string') return '';
  const q = `x-oss-process=image/format,${format}`;
  let s = `${url}?${q}`;
  if (url.indexOf('?') >= 0) {
    s = `${url}&${q}`;
  }
  return s;
}
