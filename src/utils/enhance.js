/**
 * @desc 函数节流
 * @param func 函数
 * @param wait 延迟执行毫秒数
 */
export const throttle = (func, wait = 200) => {
  return function (...args) {
    const now = +new Date(); // 当前时间
    // 将当前时间和上⼀一次执⾏行行函数时间对⽐比
    // 如果差值⼤大于设置的等待时间就执⾏行行函数
    if (!func.lastTime) {
      func.lastTime = 0;
    }
    if (now - func.lastTime > wait) {
      func.lastTime = now;
      console.log('[warning]', 'debounce throttle func.apply');
      func.apply(this, args);
    } else {
      console.log('[warning]', 'debounce throttle interrupt !!!');
    }
  };
};

/**
 * @desc 函数节流2
 * @param func 函数
 * @param wait 延迟执行毫秒数
 */
// 上一次执⾏行行该函数的时间
let lastTime = 0;
export const throttleAt = (func, wait = 200) => {
  return function (...args) {
    const now = +new Date(); // 当前时间
    // 将当前时间和上⼀一次执⾏行行函数时间对⽐比
    // 如果差值⼤大于设置的等待时间就执⾏行行函数
    if (now - lastTime > wait) {
      lastTime = now;
      // console.log('[warning]', 'debounce throttle func.apply');
      func.apply(this, args);
    } else {
      // console.log('[warning]', 'debounce throttle interrupt !!!');
    }
  };
};

// 防抖:多次触发事件后，事件处理函数只执行一次，并且是在触发操作结束时执行。
export function fn_debounce(fn, delay = 200) {
  let timer = null;
  return function () {
    if (timer) {
      clearTimeout(timer);
    }
    timer = setTimeout(() => {
      timer = null;
      fn.apply(this, arguments);
    }, delay);
  };
}
// 节流:触发函数事件后，短时间间隔内无法连续调用，只有上一次函数执行后，过了规定的时间间隔，才能进行下一次的函数调用。
export function fn_throttle(fn, interval = 200) {
  let last = null;
  let timer = null;
  return function () {
    const now = +new Date();
    if (last && now - last < interval) {
      clearTimeout(timer);
      timer = setTimeout(() => {
        last = now;
        fn.apply(this, arguments);
      }, interval);
    } else {
      last = now;
      fn.apply(this, arguments);
    }
  };
}

let throttleVal = '';
// 节流函数   解决上面节流函数的BUG
export function $u_throttle(func, wait = 500, immediate = true, event) {
  throttleVal = event;
  if (typeof func !== 'function') return func;
  if (immediate) {
    if (!func.flag) {
      func.flag = true;
      typeof func === 'function' && func.call(this, throttleVal);
      func.timer = setTimeout(() => {
        func.flag = false;
      }, wait);
    }
  } else {
    if (!func.flag) {
      func.flag = true;
      func.timer = setTimeout(() => {
        func.flag = false;
        typeof func === 'function' && func.call(this, throttleVal);
      }, wait);
    }
  }
}
