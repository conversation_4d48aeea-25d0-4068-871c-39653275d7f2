import axios from 'axios';
import {
  Message
  // MessageBox
} from 'element-ui';
import { filterMessage, FETCH_ERROR, FETCH_TIME_OUT, DISCONNECT_NETWORK, SERVICE_INSIDE_ERROR, ACCESS_DENIED } from '@/constants/message';
import store from '../store';
// import router from '../router';
import { getUserInfo } from '@/common/localStorage/user';
// import Vue from 'vue';
function getCookie(name) {
  // 读取COOKIE
  const reg = new RegExp('(^| )' + name + '(?:=([^;]*))?(;|$)');
  const val = document.cookie.match(reg);
  return val ? (val[2] ? unescape(val[2]) : '') : null;
}
const defaultConfig = {
  processError: true, // 自动提示错误信息 默认打开
  process404: true // 处理404
};
// 创建axios实例
const service = axios.create({
  baseURL: process.env.VUE_APP_BASE_URL, // 'http://************'  'http://**********', // 'http://**********', // process.env.VUE_APP_BASE_URL, // api的base_url
  timeout: 30000, // 请求超时时间
  withCredentials: true,
  'X-Session-Id': getCookie(`${location.hostname}.mushroom.session.id`),
  ...defaultConfig
});
let flag = false;
let errFlag = false;
export class RequestError {
  constructor(key) {
    const self = this;
    self.initKey = key || FETCH_ERROR; // 初始化
    self.key = self.initKey;
  }
  set default(key) {
    // 替换当前类默认的错误提示
    const self = this;
    if (self.key === FETCH_ERROR) {
      self.key = key;
    }
  }
  get text() {
    const { key, message } = this;
    return message || filterMessage(key) || '';
  }
  set response(res) {
    const self = this;
    if (res.code === '500' && (!res.msg || res.msg.toLowerCase().indexOf('exception') !== -1)) {
      self.key = SERVICE_INSIDE_ERROR;
    } else if (res.code === '400') {
      self.key = ACCESS_DENIED;
      return;
    } else {
      // 取接口返回的错误
      self.message = res.msg;
    }
    self.data = res;
  }
  set error(error) {
    const self = this;
    const { request = {}, status: fecthStatus, message: fetchMessage } = error;
    const { status: reqStatus, message: reqMessage } = request;
    const status = reqStatus || fecthStatus;
    const message = reqMessage || fetchMessage || '';
    if (status === 500) {
      self.key = SERVICE_INSIDE_ERROR;
    } else if (status === 400) {
      // 默认
    } else if (message === 'Network Error') {
      self.key = DISCONNECT_NETWORK;
    } else if (message.indexOf('timeout') === 0) {
      self.key = FETCH_TIME_OUT;
    } else {
      // 取error信息
      self.message = error.message;
    }
    self.data = error;
  }
}

// 统一的头部获取
export const getHeaders = () => {
  const userInfo = getUserInfo();
  const headers = {
    'X-Tenant-Id': process.env.VUE_APP_TENANT_ID // 一级租户ID
  };
  const grayEnv = ['gray', 'uat'];
  // 灰度发布头部
  // console.log(
  //   'grayEnv.includes(process.env.VUE_APP_ENV_CONFIG)',
  //   grayEnv.includes(process.env.VUE_APP_ENV_CONFIG)
  // );
  if (grayEnv.includes(process.env.VUE_APP_ENV_CONFIG)) {
    headers.region = 'gray'; // 令牌
  }
  // console.log('headers', headers);
  if (userInfo) {
    headers['X-Csrf-Token'] = userInfo.csrfToken; // 令牌
    headers['X-Session-Id'] = userInfo.sessionId; // 令牌
  }
  return headers;
};

export function getHeadersWithExtTenant() {
  return {
    ...getHeaders(),
    'X-Ext-Tenant-Id': process.env.VUE_APP_TENANT_ID
  };
}

export const handleResponse = (response) => {
  // 兼容历史版本
  /**
   * code为非0是抛错 可结合自己业务进行修改
   */
  const res = response.data;
  if (res instanceof ArrayBuffer) {
    // 如果是文件流
    return res;
  }
  return getRes(res, response.config);
};

export function getRes(res, config = defaultConfig) {
  // 这部分抽处理用于处理类似于图片上传之类的请求
  if (res.code === '0') {
    return Promise.resolve(res);
  }
  const { process404, processError, autoErrorMsg = true } = config;
  if (!flag && process404) {
    flag = true;
    if (res.code === '400') {
      flag = false;
      if (window.location.host.indexOf('localhost:') !== -1) return; // 防止localhost 不停的跳转
      store.dispatch('FedLogOut').then(() => {
        window.$plugins.clearLStorage();
        window.$plugins.clearSStorage();
        store.replaceState({});
        window.QIANKUN_DATA.$goLogin();
      });
      // })
      // .finally(() => {
      //   flag = false;
      // });
      if (!flag) return;
    }
  }
  const requestError = new RequestError();
  requestError.response = res;
  if (processError && autoErrorMsg) {
    Message.error(requestError.text);
  }
  return Promise.reject(requestError);
}

export const handleError = (error) => {
  const requestError = new RequestError();
  requestError.error = error;
  if ((error.config || defaultConfig).processError) {
    if (errFlag === false) {
      errFlag = true;
      Message.error(requestError.text);
      setTimeout(() => {
        errFlag = false;
      }, 1000);
    }
  }
  return Promise.reject(requestError);
};

// request拦截器
service.interceptors.request.use(
  (config) => {
    const { headers, url: oldUrl, ...args } = config;
    return {
      ...args,
      url: oldUrl.replace('@DEFAULT_SERVICE', process.env.DEFAULT_SERVICE),
      headers: Object.assign({}, getHeaders(), headers)
    };
  },
  (error) => {
    // Do something with request error
    console.log(error); // for debug
    Promise.reject(error);
  }
);

// response拦截器
service.interceptors.response.use(handleResponse, handleError);

export function parseUrl(url, data) {
  const keys = (data && Object.keys(data)) || [];
  const paramStr = keys.map((key) => `${key}=${data[key]}`).join('&');
  return `${process.env.VUE_APP_BASE_URL}${url}${paramStr && `?${paramStr}`}`;
}

export const closeErrorRequest = (config) => {
  // 不提示错误的request
  return service(
    Object.assign(config, {
      processError: false,
      useSSO: false,
      headers: {
        'X-Ext-Tenant-Id': process.env.VUE_APP_TENANT_ID,
        ...config.headers
      }
    })
  );
};

export function withExtTenantIdRequest(config) {
  try {
    return service({
      ...config,
      headers: {
        'X-Ext-Tenant-Id': process.env.VUE_APP_TENANT_ID,
        ...config.headers
      }
    });
  } catch (err) {
    console.error('抛出异常', err);
  }
}
export function mockRequest(config) {
  return service({
    ...(process.env.MOCK && {
      baseURL: 'https://easy-mock.com/mock/5cb9651018a7126a27520bc2/dazhou/example'
    }),
    ...config,
    headers: {
      'X-Ext-Tenant-Id': process.env.VUE_APP_TENANT_ID,
      ...config.headers
    }
  });
}

export default service;
