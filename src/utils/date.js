// 获取近多少天的时间戳范围,当天传0，近七天传7，近1个月传30，以此类推
export function getTimeRange(data) {
  const today = new Date();
  const otherDay = new Date();
  const year = today.getFullYear();
  const month = today.getMonth() + 1;
  const day = today.getDate();
  // 选择昨天
  if (data === 'yesterday') {
    const str = today.getTime() - 1000 * 60 * 60 * 24; // 昨天的时间
    return {
      startDate: getStartTime(str),
      endDate: getEndTime(str)
    };
  }
  // 选择当天
  if (data === '0') {
    const str1 = year + '/' + month + '/' + day + ' ' + '00:00:00';
    const str2 = year + '/' + month + '/' + day + ' ' + '23:59:59';
    return {
      startDate: new Date(str1).getTime(),
      endDate: new Date(str2).getTime()
    };
  }
  // 选择当月
  if (data === 'THIS_MONTH') {
    const yDate = new Date(year, month, 0);
    const str1 = year + '/' + month + '/' + '1' + ' ' + '00:00:00';
    const str2 = year + '/' + month + '/' + yDate.getDate() + ' 23:59:59'; // 当月月最后一天
    return {
      startDate: new Date(str1).getTime(),
      endDate: new Date(str2).getTime()
    };
  }
  // 选择上月
  if (data === 'LAST_MONTH') {
    const yDate = new Date(year, month - 1, 0);
    const str1 = year + '/' + (month - 1) + '/' + '1' + ' ' + '00:00:00';
    const str2 = year + '/' + (month - 1) + '/' + yDate.getDate() + ' 23:59:59'; // 上个月最后一天
    return {
      startDate: new Date(str1).getTime(),
      endDate: new Date(str2).getTime()
    };
  }
  // 3个月以上
  if (data === 'BEFORE_MONTH') {
    const targetday_milliseconds = today.getTime() - 1000 * 60 * 60 * 24 * 90;
    otherDay.setTime(targetday_milliseconds);
    return { startDate: 1577808000000, endDate: otherDay.getTime() };
  }
  // 选择其他天数,近多少day天
  const targetday_milliseconds = today.getTime() - 1000 * 60 * 60 * 24 * data;
  // otherDay.setTime(targetday_milliseconds);
  return {
    startDate: getStartTime(targetday_milliseconds),
    endDate: today.getTime()
  };
}
export function getStartTime(timeStamp) {
  // 获取当天开始时间 00:00:00 000
  const date = new Date(timeStamp);
  const [hours, minutes, seconds] = [
    date.getHours(),
    date.getMinutes(),
    date.getSeconds()
  ];
  return (
    (Math.floor(timeStamp / 1000) - hours * 60 * 60 - minutes * 60 - seconds) *
    1000
  );
}

export function getEndTime(timeStamp) {
  // 获取当天截止时间 23:59:59 999
  const time = getStartTime(timeStamp);
  return time + 1000 * 60 * 60 * 24 - 1000;
}

export function getTimeStamp(time) {
  // 将23:59:59 转化为时间戳
  const list = time.split(':');
  const h = (str) => parseInt(str) * 60 * 60;
  const m = (str) => parseInt(str) * 60;
  const s = (str) => parseInt(str);
  const [hh, mm, ss] = list;
  return (h(hh) + m(mm) + s(ss)) * 1000;
}

// 日期生成器,返回一年的日历
export function dateGenerator() {
  const set = new Set([1, 3, 5, 7, 8, 10, 12]);
  const month = [];
  const getLabel = (num) => (num < 10 ? `0${num}` : `${num}`);
  let i = 1;
  while (i <= 12) {
    const date = [];
    const last = i === 2 ? 28 : set.has(i) ? 31 : 30;
    let j = 1;
    while (j <= last) {
      const dayStr = getLabel(j);
      date.push({
        value: dayStr,
        label: dayStr
      });
      j++;
    }
    const monthStr = getLabel(i);
    month.push({
      value: monthStr,
      label: monthStr,
      children: date
    });
    i++;
  }
  return month;
}

// 获取指定日期前N月代码
export function GetPreMonthDay(date, monthNum, dw = '-') {
  const dateArr = date.split('-');
  const year = dateArr[0]; // 获取当前日期的年份
  const month = dateArr[1]; // 获取当前日期的月份
  const day = dateArr[2]; // 获取当前日期的日
  // let days = new Date(year, month, 0);
  // days = days.getDate(); // 获取当前日期中月的天数
  let year2 = year;
  let month2 = parseInt(month) - monthNum;
  if (month2 <= 0) {
    const absM = Math.abs(month2);
    year2 =
      parseInt(year2) - Math.ceil(absM / 12 === 0 ? 1 : parseInt(absM) / 12);
    month2 = 12 - (absM % 12);
  }
  let day2 = day;
  let days2 = new Date(year2, month2, 0);
  days2 = days2.getDate();
  if (day2 > days2) {
    day2 = days2;
  }
  if (month2 < 10) {
    month2 = '0' + month2;
  }
  const t2 = year2 + dw + month2 + dw + day2;
  return t2;
}
