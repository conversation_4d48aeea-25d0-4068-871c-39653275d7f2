// 时间格式化
export function parseTime(time, cFormat, hideZero) {
  if (!time) {
    return null;
  }
  if (arguments.length === 0) {
    return null;
  }
  const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}';
  let date;
  if (typeof time === 'object') {
    date = time;
  } else {
    if (('' + time).length === 10) time = parseInt(time) * 1000;
    date = new Date(time);
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  };
  const time_str = format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
    let value = formatObj[key];
    if (key === 'a') {
      return ['一', '二', '三', '四', '五', '六', '日'][value - 1];
    }
    if (!hideZero && result.length > 0 && value < 10) {
      value = '0' + value;
    }
    return value || 0;
  });
  return time_str;
}
// 返回一个默认值的parseTime
export function parseDefaultTime(time) {
  return parseTime(time, '{y}-{m}-{d} {h}:{i}:{s}');
}
// 返回计算距离现在的时间
export function formatTime(time, option) {
  time = +time * 1000;
  const d = new Date(time);
  const now = Date.now();

  const diff = (now - d) / 1000;

  if (diff < 30) {
    return '刚刚';
  } else if (diff < 3600) {
    // less 1 hour
    return Math.ceil(diff / 60) + '分钟前';
  } else if (diff < 3600 * 24) {
    return Math.ceil(diff / 3600) + '小时前';
  } else if (diff < 3600 * 24 * 2) {
    return '1天前';
  }
  if (option) {
    return parseTime(time, option);
  } else {
    return d.getMonth() + 1 + '月' + d.getDate() + '日' + d.getHours() + '时' + d.getMinutes() + '分';
  }
}
// 根据链接里的参数返回一个对象集合
export function param2Obj(url) {
  const search = url.split('?')[1];
  if (!search) {
    return {};
  }
  return JSON.parse('{"' + decodeURIComponent(search).replace(/"/g, '\\"').replace(/&/g, '","').replace(/=/g, '":"') + '"}');
}
// js创建一个事件并且立即执行  el dom元素   eventName 事件名称  detail 自定义参数
export function trigger(el, eventName, detail = {}) {
  // const e = document.createEvent('HTMLEvents');
  // e.initEvent(type, true, true);
  const e = new CustomEvent(eventName, {
    detail: { ...detail },
    bubbles: true, // 是否冒泡
    cancelable: true // 是否取消默认事件
  }); // 创建一个更高度自定义事件
  el.dispatchEvent(e);
}

// 判断JSON
export function isJSON(str) {
  if (typeof str === 'string') {
    try {
      JSON.parse(str);
      return true;
    } catch (e) {
      return false;
    }
  }
  return false;
}

// 生成指定长度的随机数
export function getRandomString(len = 100) {
  const chars = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'];
  let res = '';
  for (let i = 0; i < len; i++) {
    const id = Math.ceil(Math.random() * 35);
    res += chars[id];
  }
  return res;
}
// 解析结束时间
export function parseEndTime(time) {
  const date = new Date(time);
  const y = date.getFullYear();
  const m = date.getMonth();
  const d = date.getDate();
  return new Date(y, m, d, 23, 59, 59);
}

/**
 * @param {*} delay
 * @param {*} noTrailing Boolean 节流状态会存储上一次不合格的点击setTimeout  time:delay - elapsed
 * @param {*} callback
 * @param {*} debounceMode 判断是否是防抖 throttle -> undefined; debounce -> Boolean
 * @returns
 */
export function throttle(delay, noTrailing, callback, debounceMode) {
  let timeoutID;
  let cancelled = false;

  let lastExec = 0;

  function clearExistingTimeout() {
    if (timeoutID) {
      clearTimeout(timeoutID);
    }
  }

  function cancel() {
    clearExistingTimeout();
    cancelled = true;
  }

  if (typeof noTrailing !== 'boolean') {
    debounceMode = callback;
    callback = noTrailing;
    noTrailing = undefined;
  }
  function wrapper(...arguments_) {
    const self = this;
    const elapsed = Date.now() - lastExec;

    if (cancelled) {
      return;
    }

    function exec() {
      lastExec = Date.now();
      callback.apply(self, arguments_);
    }

    function clear() {
      timeoutID = undefined;
    }

    if (debounceMode && !timeoutID) {
      exec();
    }

    clearExistingTimeout();

    if (debounceMode === undefined && elapsed > delay) {
      // 节流 立即执行
      exec();
    } else if (noTrailing !== true) {
      /**
       * 1.节流状态 fn:exec  time:delay - elapsed
       */
      timeoutID = setTimeout(debounceMode ? clear : exec, debounceMode === undefined ? delay - elapsed : delay);
    }
  }

  wrapper.cancel = cancel;
  return wrapper;
}

// 防抖
/**
 * @param {*} delay
 * @param {*} atBegin 判断第一次是否执行
 * @param {*} callback
 * @returns
 */
export function debounce(delay, atBegin, callback) {
  return callback === undefined ? throttle(delay, atBegin, false) : throttle(delay, callback, atBegin !== false);
}

// 对象数组去重（重复键)
export function arrDistinctByKey(arr, key) {
  const temp = new Map(); // 使用Map可以比对多种类型，不限于字符串
  return arr.filter((item) => !temp.has(item[key]) && temp.set(item[key], true));
}

// 对象数组取交集（相同键）
export function arrIntersectionByKey(arr1, arr2, key) {
  const arr2Keys = arr2.map((item) => item[key]);
  return arr1.filter((item) => arr2Keys.includes(item[key]));
}

export function judgeDataType(val, type) {
  const dataType = Object.prototype.toString
    .call(val)
    .replace(/\[object (\w+)\]/, '$1')
    .toLowerCase();
  return type ? dataType === type : dataType;
}

/**
 * 递归去除对象里面值为空的属性的方法
 * @param obj
 * @returns {{}}
 */
export function removeEmpty(obj) {
  Object.keys(obj).forEach((key) => {
    if (obj[key] && typeof obj[key] === 'object') {
      removeEmpty(obj[key]);
    } else if (obj[key] === null || obj[key] === undefined || obj[key] === '' || (Array.isArray(obj[key]) && obj[key].length === 0)) {
      delete obj[key];
    }
    if (obj[key] && typeof obj[key] === 'object' && Object.keys(obj[key]).length === 0) {
      delete obj[key];
    }
  });
  return obj;
}

/**
 * 睡眠函数
 * @param time 单位毫秒
 * @returns {Promise<unknown>}
 */
export function sleep(time = 2000) {
  return new Promise((resolve) => setTimeout(resolve, time));
}
