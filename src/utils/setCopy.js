import ClipboardJS from 'clipboard';
import { Message } from 'element-ui';
/**
 * 点击复制
 * @param {*} btn
 * @param {*} getTarget
 */
export default function setCopy(btn, getTarget) {
  const clipboard = new ClipboardJS(btn, {
    target: getTarget
  });
  const handle = type => e => {
    switch (type) {
      case 'success':
        Message.success('复制成功');
        return;
      case 'error':
        Message.error('复制失败');
        return;
    }
  };
  clipboard.on('success', handle('success'));
  clipboard.on('error', handle('error'));
  return () => {
    clipboard.destroy();
  };
}
