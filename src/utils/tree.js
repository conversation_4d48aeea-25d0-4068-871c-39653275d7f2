/**
 * 通过后台返回的数据构建树
 * @param list
 */
export function parseTree(list, clean = true) {
  let map, children, idx, i, j, item, id, parentIds, parentList, k;
  let minParentLength = 0;
  let rootList = [];
  const result = [];
  const listMap = {};
  for (i in list) {
    item = list[i];
    id = item.id;
    parentIds = item.parentIds;
    map = listMap;
    children = result;

    // 取出parentIds数组并去重
    parentList = Array.from(
      new Set(
        parentIds.split(',').filter(function (item) {
          return item;
        })
      )
    );

    for (j in parentList) {
      k = parentList[j];
      if (!map[k]) {
        map[k] = {
          idx: children.length,
          children: {}
        };
        children.push({ id: k });
      }
      idx = map[k].idx;
      map = map[k].children;
      if (!children[idx].children) {
        children[idx].children = [];
      }
      children = children[idx].children;
    }
    if (!map[id]) {
      map[id] = { idx: children.length, children: {} };
      children.push(Object.assign({}, item));
    } else {
      idx = map[id].idx;
      Object.assign(children[idx], item);
    }
    if (i === '0' || parentList.length < minParentLength) {
      rootList = [children[map[id].idx]];
      minParentLength = parentList.length;
    } else if (parentList.length === minParentLength) {
      rootList.push(children[map[id].idx]);
    }
  }
  if (clean) {
    return rootList;
  }
  return result[0].children;
}

/**
 * 树转化为数组，中序遍历
 * @param {树} tree
 */
export function treeToArray(tree, filter) {
  const res = [];
  const loop = (list, depth, parent) => {
    list.forEach((item) => {
      const { children } = item;
      const obj = { ...item, depth };
      const node = filter ? filter(obj) : obj;
      if (parent) {
        node.parent = parent;
      }
      res.push(node);
      if (children && children.length > 0) {
        loop(children, depth + 1, node);
      }
    });
  };
  loop(tree, 0);
  return res;
}

export function flatTree(data) {
  if (!(data && data.length)) return [];
  return data.reduce((acc, cur) => {
    acc.push(cur);
    if (cur.children && cur.children.length) {
      acc.push(...flatTree(cur.children));
    }
    return acc;
  }, []);
}

// 上移
export function listMoveUp(list, index) {
  const moveList = [...list];
  if (index !== 0) {
    moveList[index] = moveList.splice(index - 1, 1, moveList[index])[0];
  } else {
    moveList.push(moveList.shift());
  }
  return moveList;
}

// 下移
export function listMoveDown(list, index) {
  const moveList = [...list];
  if (index !== moveList.length - 1) {
    moveList[index] = moveList.splice(index + 1, 1, moveList[index])[0];
  } else {
    moveList.unshift(moveList.splice(index, 1)[0]);
  }
  return moveList;
}

// 置顶
export function listToTop(list, index) {
  const moveList = [...list];
  if (index !== 0) {
    moveList.unshift(moveList.splice(index, 1)[0]);
  }
  return moveList;
}

// 置底
export function listToBottom(list, index) {
  const moveList = [...list];
  if (index !== moveList.length - 1) {
    moveList.push(moveList.splice(index, 1)[0]);
  }
  return moveList;
}

