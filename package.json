{"name": "soyoung-zg-admin", "version": "4.21.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "dev": "npm run serve", "dev:prod": "vue-cli-service --mode production", "build": "vue-cli-service build", "build:test": "vue-cli-service build --mode test", "build:uat": "vue-cli-service build --mode uat", "lint": "vue-cli-service lint", "report": "vue-cli-service build  --report"}, "dependencies": {"@tinymce/tinymce-vue": "^3.2.4", "axios": "^0.21.1", "clipboard": "^2.0.4", "core-js": "^3.6.5", "dayjs": "^1.11.10", "decimal.js": "^10.4.3", "echarts": "^5.5.0", "element-ui": "^2.15.14", "file-saver": "^2.0.5", "html-to-image": "^1.11.11", "js-cookie": "^2.2.1", "jszip": "^3.10.1", "lodash": "^4.17.21", "lowcodelib": "1.1.377", "moment": "^2.24.0", "qrcode": "^1.3.3", "qs": "^6.5.2", "sortablejs": "^1.15.0", "tinymce": "^5.10.7", "v-viewer": "^1.6.3", "vue": "~2.6.11", "vue-clipboard2": "^0.3.1", "vue-frag": "^1.1.5", "vue-pdf-embed": "1", "vue-router": "^3.5.1", "vuedraggable": "^2.20.0", "vuex": "^3.6.2", "watermark-yjh": "^1.0.2"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.11", "@vue/cli-plugin-eslint": "~4.5.11", "@vue/cli-service": "~4.5.11", "babel-eslint": "^10.1.0", "compression-webpack-plugin": "5.0.1", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "hard-source-webpack-plugin": "^0.13.1", "less": "^3.12.2", "sass": "^1.34.0", "sass-loader": "^8.0.2", "vue-eslint-parser": "^7.6.0", "vue-template-compiler": "~2.6.11"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}